# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Install Dependencies
```bash
pnpm install
# or npm install
```

### Development Server
```bash
pnpm run dev
# or npm run dev
# Runs on http://localhost:5173
```

### Build Commands
```bash
# Production build
pnpm run build

# Type checking
pnpm run type-check

# Code quality checks
pnpm run lint
pnpm run format

# Locale management
pnpm run split-locales  # Split locale files into modules
pnpm run merge-locales  # Merge module locale files
```

### Testing
- No test framework is currently configured
- Manual testing is done through the development server
- Use `VITE_USE_MOCK=true` in development for mock data testing

## Project Architecture

### Technology Stack
- **Framework**: Vue 3 with Composition API
- **Language**: TypeScript
- **UI Library**: Element Plus
- **State Management**: Pinia
- **Routing**: Vue Router
- **Internationalization**: Vue I18n with modular locale files
- **HTTP Client**: Axios with custom interceptors
- **Build Tool**: Vite
- **Styling**: SCSS with global variables and mixins

### Key Directories

**`src/api/`** - API layer with modular organization
- `index.ts` - Axios configuration with auth and i18n integration
- `modules/` - Business domain API modules (auth, sales, inventory, etc.)

**`src/stores/`** - Pinia state management
- `auth.ts` - Authentication state with token management and permissions

**`src/router/`** - Vue Router configuration
- Comprehensive routing with authentication guards
- Role-based access control through route meta

**`src/locales/`** - Internationalization
- Modular locale structure in `modules/` directory
- Support for Chinese and English
- Dynamic locale loading with `loader.ts`

**`src/plugins/`** - Vue plugin configurations
- `i18n.ts` - Vue I18n setup with Element Plus integration
- `permission.ts` - Permission system plugin

**`src/views/`** - Page components organized by business domain
- Customer management, inventory, parts, work orders, etc.
- Nested component structure for complex features

### Core Configuration Files

**`main.ts`** - Application bootstrap
- Async initialization with i18n and auth setup
- Plugin registration order is critical

**`vite.config.ts`** - Build configuration
- Path aliases (`@` → `src`)
- SCSS preprocessing with auto-imported variables/mixins
- Development server on port 5173

### Authentication & Permissions

The application uses a comprehensive authentication system:

1. **Token-based auth** with access/refresh tokens stored in localStorage
2. **Route guards** in router that check authentication status
3. **Permission checking** through auth store methods (`hasPermission`, `hasRole`, `hasMenu`)
4. **User storage utility** for persistent auth state

### Internationalization Architecture

Sophisticated i18n setup with:
- **Modular locale files** in `src/locales/modules/`
- **Dynamic loading** of locale messages 
- **Element Plus integration** for UI component translations
- **Language switching** with menu data reloading

### API Architecture

- **Centralized Axios instance** with interceptors for auth and error handling
- **Module-based API organization** by business domain
- **Mock data support** controlled by `VITE_USE_MOCK` environment variable
- **Internationalized error messages** in response interceptors

### Styling System

- **Global SCSS variables** in `src/assets/styles/_variables.scss`
- **Mixins** in `src/assets/styles/_mixins.scss`
- **Auto-imported** in all components via Vite configuration
- **Element Plus theming** integration

## Development Guidelines

### Adding New Features

1. **API Module**: Create in `src/api/modules/` following existing patterns
2. **Types**: Define TypeScript interfaces in `src/types/`
3. **Locale Keys**: Add to both `zh.json` and `en.json` in appropriate modules
4. **Routes**: Add to `src/router/index.ts` with proper meta configuration
5. **Components**: Create in `src/views/` or `src/components/` following existing structure

### Permission Integration

Use auth store methods for permission checks:
```typescript
const authStore = useAuthStore()
const canEdit = authStore.hasPermission('system:user:edit')
const isAdmin = authStore.hasRole('admin')
```

### Internationalization

All user-facing text must use i18n with modular structure:
```vue
<!-- In templates -->
{{ $t('common.save') }}

<!-- In script -->
const { t } = useI18n()
const message = t('common.operationSuccess')
```

**Module-based i18n Structure**:
- 7 core modules: `common`, `sales`, `afterSales`, `parts`, `base`, `quotaManagement`, `menu`
- Locale files organized in `src/locales/modules/[module]/[zh|en].json`
- Use `useModuleI18n()` composable for module-specific translations

### Dictionary System

The project includes a comprehensive dictionary system for dynamic data:
```typescript
// Single dictionary usage
const { options, getNameByCode } = useDictionary('ORDER_STATUS')

// Batch dictionary usage  
const { getNameByCode, getOptions } = useBatchDictionary(['ORDER_STATUS', 'PAYMENT_STATUS'])
```

**Dictionary Features**:
- Centralized dictionary management with caching
- Support for batch loading multiple dictionary types
- Helper functions for code/name conversion
- Integration with form components (`DictionarySelect`, `DictionaryRadio`)

### Environment Variables

- `VITE_USE_MOCK` - Enable/disable mock data (controls all API modules)
- `VITE_APP_BASE_API` - Primary API base URL
- `VITE_API_BASE_URL` - Alternative API base URL

### Mock Data System

The project includes a unified mock data system:
- **Mock Configuration**: Use `MockConfig` class from `src/utils/mock-config.ts`
- **Development Mode**: Mock data is automatically used when `VITE_USE_MOCK=true`
- **Mock Data Location**: All mock data stored in `src/mock/data/` organized by module
- **API Integration**: Each API module supports both mock and real data seamlessly

### UI/UX Standards

- **Dialog forms** should use single-column layout with `label-position="top"`
- **Consistent spacing** and Element Plus design system
- **Responsive design** considerations for different screen sizes

### Component Development Standards

**Shared Components**:
- Use consistent naming: `[Feature][Action][Type]` (e.g., `WorkOrderCreateModal`)
- Place reusable components in `src/components/`
- Domain-specific components in `src/views/[domain]/components/`

**Form Standards**:
- Dialog forms use `label-position="top"` with single-column layout
- Validation messages must be internationalized
- Use dictionary components for dynamic options

## Important Notes

- **Async bootstrap** in main.ts is essential for proper i18n initialization
- **Route meta configuration** controls authentication, permissions, and menu visibility
- **Error handling** is centralized in API interceptors with i18n support
- **State persistence** is handled through UserStorage utility
- **Language switching** triggers menu data reload for dynamic content
- **Mock system** automatically switches between mock and real APIs based on environment
- **Dictionary system** provides centralized management of dynamic dropdown options
- **Module structure** follows domain-driven design with clear separation of concerns