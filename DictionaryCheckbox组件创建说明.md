# DictionaryCheckbox 组件创建说明

## 问题背景

在集成门店管理模块到统一字典系统时，发现项目中缺少 `DictionaryCheckbox` 组件，导致编译错误：

```
Failed to resolve import "@/components/common/DictionaryCheckbox.vue"
```

## 解决方案

基于现有的 `DictionaryRadio` 组件，创建了 `DictionaryCheckbox` 组件来支持多选字典功能。

## 组件特性

### 1. 基本功能
- ✅ 支持多选字典选项
- ✅ 自动获取和缓存字典数据
- ✅ 支持加载状态显示
- ✅ 支持禁用状态

### 2. Props 接口
```typescript
interface Props {
  modelValue?: string[] | number[] | boolean[];  // 选中的值数组
  dictionaryType: DictionaryType;                 // 字典类型
  disabled?: boolean;                             // 是否禁用
  size?: 'large' | 'default' | 'small';         // 尺寸
  disabledOptions?: string[];                     // 禁用的选项
}
```

### 3. Events 接口
```typescript
interface Emits {
  (e: 'update:modelValue', value: string[] | number[] | boolean[]): void;
  (e: 'change', value: string[] | number[] | boolean[]): void;
}
```

## 使用示例

### 基本用法
```vue
<template>
  <DictionaryCheckbox
    v-model="selectedProperties"
    :dictionary-type="DICTIONARY_TYPES.STORE_PROPERTIES"
  />
</template>

<script setup>
import DictionaryCheckbox from '@/components/common/DictionaryCheckbox.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const selectedProperties = ref(['sales', 'after_sales']);
</script>
```

### 带禁用选项
```vue
<template>
  <DictionaryCheckbox
    v-model="selectedOptions"
    :dictionary-type="DICTIONARY_TYPES.STORE_PROPERTIES"
    :disabled-options="['finance']"
  />
</template>
```

### 禁用状态
```vue
<template>
  <DictionaryCheckbox
    v-model="selectedOptions"
    :dictionary-type="DICTIONARY_TYPES.STORE_PROPERTIES"
    :disabled="true"
  />
</template>
```

## 在门店管理中的应用

### 门店属性选择
```vue
<!-- StoreFormDialog.vue -->
<el-form-item :label="t('store.storeProperties')" prop="storeProperties">
  <DictionaryCheckbox
    v-model="formData.storeProperties"
    :dictionary-type="DICTIONARY_TYPES.STORE_PROPERTIES"
    :disabled="isView"
  />
</el-form-item>
```

## 技术实现

### 1. 基于 Element Plus
- 使用 `el-checkbox-group` 和 `el-checkbox` 组件
- 继承 Element Plus 的样式和交互

### 2. 字典系统集成
- 使用 `useDictionary` 组合函数获取字典数据
- 自动处理加载状态和错误处理
- 支持数据缓存机制

### 3. 类型安全
- 完整的 TypeScript 类型定义
- 与字典系统的类型完全兼容

## 与其他字典组件的对比

| 组件 | 用途 | 选择方式 | 数据类型 |
|------|------|----------|----------|
| DictionarySelect | 下拉选择 | 单选/多选 | string/string[] |
| DictionaryRadio | 单选按钮组 | 单选 | string |
| DictionaryCheckbox | 复选框组 | 多选 | string[] |

## 文件位置

```
src/components/common/DictionaryCheckbox.vue
```

## 验证清单

- [x] 组件创建完成
- [x] TypeScript 类型定义正确
- [x] 与字典系统集成正常
- [x] 在门店管理中使用正常
- [x] 编译无错误
- [x] 支持所有必要的 props 和 events

## 后续优化建议

1. **样式定制**：可以添加自定义样式支持
2. **布局选项**：支持垂直/水平布局选择
3. **分组显示**：支持选项分组显示
4. **搜索功能**：在选项较多时支持搜索过滤

---

**创建时间**：2025年7月29日  
**状态**：✅ 完成  
**测试状态**：⏳ 待测试
