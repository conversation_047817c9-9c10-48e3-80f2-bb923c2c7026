# 语言切换功能说明

## 功能概述

项目现在支持前端语言切换，并且每次HTTP请求都会自动传递当前语言信息给后端。本功能遵循项目的模块化国际化标准，**切换语言时会自动重新从后端加载菜单数据**。

## 实现方式

### 1. HTTP请求语言传递

所有的HTTP请求都会自动添加语言信息：

- **请求参数**: `lang` 字段 (例如: `?lang=zh_CN` 或 `?lang=en_US`)
- **请求头**: `Accept-Language` 字段 (例如: `Accept-Language: zh_CN`)

### 2. 语言代码映射

- 中文: `zh` → `zh_CN`
- 英文: `en` → `en_US`

### 3. 自动菜单重载

**重要特性**：切换语言时会自动重新从后端获取用户信息（包含菜单数据），确保菜单显示正确的语言内容。

### 4. 模块化国际化标准

遵循项目的模块化国际化标准，使用 `useModuleI18n` 组合函数：

```typescript
// 推荐的使用方式
const { t, tc } = useModuleI18n('moduleName');
// t() - 当前模块的翻译
// tc() - 通用模块的翻译
```

### 5. 语言切换组件

新增了 `LanguageSelector` 组件，提供下拉菜单形式的语言选择，支持加载状态显示。

## 使用方法

### 1. 引入语言切换组件

```vue
<template>
  <div>
    <!-- 语言切换下拉菜单 -->
    <LanguageSelector />
  </div>
</template>

<script setup lang="ts">
import LanguageSelector from '@/components/common/LanguageSelector.vue';
</script>
```

### 2. 异步编程式语言切换

```typescript
import { switchLanguage } from '@/plugins/i18n';

try {
  // 切换到中文（会自动重载菜单）
  await switchLanguage('zh');
  console.log('语言切换完成，菜单已重载');
} catch (error) {
  console.error('语言切换失败:', error);
}

try {
  // 切换到英文（会自动重载菜单）
  await switchLanguage('en');
  console.log('语言切换完成，菜单已重载');
} catch (error) {
  console.error('语言切换失败:', error);
}
```

### 3. 获取当前语言

```typescript
import { getCurrentLanguage, getLanguageDisplayName } from '@/plugins/i18n';

const currentLang = getCurrentLanguage(); // 'zh' 或 'en'
const displayName = getLanguageDisplayName(currentLang); // '简体中文' 或 'English'
```

### 4. 模块化国际化使用

```typescript
// 在组件中使用
import { useModuleI18n } from '@/composables/useModuleI18n';

const { t, tc } = useModuleI18n('menu');
// t('home') - 获取菜单模块的翻译
// tc('confirm') - 获取通用模块的翻译
```

## 文件修改说明

### 1. HTTP请求配置 (`src/utils/http.ts`)
- 添加了语言代码映射函数
- 在请求拦截器中自动添加语言信息到请求参数和请求头

### 2. i18n插件 (`src/plugins/i18n.ts`)
- 新增**异步**语言切换工具函数
- 支持语言状态保存到localStorage
- **自动菜单重载**：切换语言时重新从后端获取用户信息
- 提供语言信息获取函数

### 3. 语言切换组件 (`src/components/common/LanguageSelector.vue`)
- 下拉菜单样式的语言选择器
- 显示当前语言状态
- 支持**异步**语言切换
- **加载状态显示**：切换时显示加载动画
- **错误处理**：切换失败时显示错误提示

### 4. 侧边栏菜单组件 (`src/components/SidebarMenuItem.vue`)
- 修改为使用模块化国际化标准
- 支持根据菜单代码(menuCode)获取翻译
- 自动响应语言切换

### 5. 主应用布局 (`src/App.vue`)
- 替换原有的语言选择下拉框为新的语言切换组件
- 使用模块化国际化标准

### 6. 登录页面 (`src/views/LoginView.vue`)
- 添加了语言切换组件
- 位于页面右上角

## 菜单国际化支持

### 1. 菜单翻译文件结构

```
src/locales/modules/menu/
├── zh.json  # 中文菜单翻译
└── en.json  # 英文菜单翻译
```

### 2. 菜单翻译使用

菜单组件会自动根据 `menuCode` 字段查找对应的翻译：

```json
// src/locales/modules/menu/zh.json
{
  "home": "首页",
  "checkinList": "到店登记",
  "salesOrder": "销售订单"
}
```

### 3. 后端菜单数据

后端返回的菜单数据应包含 `menuCode` 字段，前端会根据此字段自动获取对应语言的翻译。如果翻译不存在，会降级使用 `menuName` 字段。

### 4. 添加新菜单翻译

1. 在 `src/locales/modules/menu/zh.json` 中添加中文翻译
2. 在 `src/locales/modules/menu/en.json` 中添加英文翻译
3. 确保菜单数据中的 `menuCode` 字段与翻译键名一致

## 测试

### 1. 语言切换测试组件
创建了 `LanguageTestExample.vue` 组件用于测试语言切换功能：

```vue
<template>
  <LanguageTestExample />
</template>

<script setup lang="ts">
import LanguageTestExample from '@/components/examples/LanguageTestExample.vue';
</script>
```

### 2. 完整演示组件
创建了 `LanguageDemo.vue` 组件用于演示所有功能特性。

### 3. 功能验证
- 切换语言后，界面文本会立即更新
- **菜单数据会自动重新从后端获取**
- 菜单文本会实时响应语言切换
- 发起HTTP请求时，会自动带上当前语言信息
- 语言状态会保存到localStorage，刷新页面后保持
- **切换过程中显示加载状态**

## 后端对接

### 1. 语言信息获取

后端需要从以下两个地方获取语言信息：

1. **请求参数**: `request.getParameter("lang")`
2. **请求头**: `request.getHeader("Accept-Language")`

语言代码格式：
- 中文: `zh_CN`
- 英文: `en_US`

### 2. 菜单数据返回

**重要**：后端的菜单数据应该：
- 根据当前语言返回对应的菜单名称
- 包含 `menuCode` 字段用于前端翻译匹配
- 在语言切换时，`getUserInfo` 接口应返回新语言对应的菜单数据

## 注意事项

1. **异步操作**：语言切换现在是异步操作，需要等待完成
2. **菜单重载**：切换语言时会自动重新从后端获取菜单数据
3. **加载状态**：切换过程中会显示加载动画
4. **错误处理**：切换失败时会显示错误提示
5. **语言持久化**：语言状态会持久化保存到localStorage
6. **HTTP请求**：所有HTTP请求都会自动携带语言信息
7. **默认语言**：如果没有设置语言，默认使用中文 (`zh_CN`)
8. **模块化标准**：遵循项目的模块化国际化标准，使用 `useModuleI18n` 组合函数

## 扩展语言支持

如需添加新语言，请修改以下文件：

1. `src/utils/http.ts` - 添加语言代码映射
2. `src/plugins/i18n.ts` - 添加语言选项和显示名称
3. `src/locales/modules/` - 添加相应的语言包文件
4. 确保遵循 `docs/i18n-migration-guide.md` 中的模块化国际化标准

## 最佳实践

1. **使用模块化国际化**: 始终使用 `useModuleI18n` 而不是直接使用 `useI18n`
2. **菜单代码规范**: 确保菜单的 `menuCode` 字段与翻译键名一致
3. **翻译文件管理**: 按模块组织翻译文件，避免大文件冲突
4. **异步处理**: 使用 `await switchLanguage()` 确保语言切换完成
5. **错误处理**: 对语言切换进行适当的错误处理
6. **类型安全**: 配合 TypeScript 使用以获得更好的类型提示

## 性能优化

1. **菜单缓存**: 菜单数据在切换语言时重新获取，但会在内存中缓存
2. **请求去重**: 短时间内的重复语言切换会被自动防抖
3. **错误降级**: 如果菜单重载失败，界面翻译仍然会正常工作

## 相关文档

- [国际化模块化迁移指南](./docs/i18n-migration-guide.md)
- [Vue i18n 规范](./docs/vue-i18n-standards.md) 