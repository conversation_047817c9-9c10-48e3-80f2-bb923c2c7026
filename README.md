# DMS 前端项目
## 项目简介

该项目是一个**车辆管理系统 (DMS)** 的前端部分，旨在提供一套全面的解决方案，用于高效管理车辆库存、销售流程以及相关的车辆信息。项目采用现代化的前端技术栈，注重用户体验和代码质量。

## 技术栈

- **框架**: Vue 3 (Composition API)
- **语言**: TypeScript
- **UI 组件库**: Element Plus
- **国际化**: Vue I18n
- **路由管理**: Vue Router
- **API 请求**: 基于 Axios 封装的模块化 API
- **构建工具**: Vite

## 主要功能

- **车辆信息管理**: 实现对车辆数据的增、删、改、查操作。
- **车辆列表展示**: 以表格形式清晰展示车辆列表，支持动态加载和分页。
- **灵活的搜索与筛选**: 提供多维度搜索条件（如VIN、车型、品牌、状态），帮助用户快速定位车辆。
- **多语言支持**: 内置中英文切换功能，通过 `Vue I18n` 实现所有用户可见文本的国际化。
- **响应式布局**: 优化了表格显示，确保在不同屏幕尺寸下都能提供良好的视觉和操作体验。
- **交互式操作**: 友好的对话框用于新增和编辑车辆信息，提供操作反馈消息。

## 项目目录结构

```
dms-frontend/
├── public/                     # 公共静态资源，不会被构建工具处理
├── src/                        # 核心源代码目录
│   ├── App.vue                 # 根组件，应用程序的入口视图
│   ├── main.ts                 # 应用的入口文件，负责 Vue 应用的创建和配置
│   ├── api/                    # 存放后端 API 接口的封装和定义
│   │   ├── index.ts            # API 接口入口文件
│   │   └── modules/            # 各模块的 API 定义
│   │       └── sales.ts        # 销售模块相关 API
│   ├── assets/                 # 存放静态资源，如图片、字体、样式文件
│   │   ├── base.css            # 基础 CSS 文件
│   │   ├── logo.svg            # 项目 Logo 图片
│   │   ├── main.css            # 主 CSS 文件
│   │   └── styles/             # 全局 SASS 样式文件
│   │       ├── _mixins.scss    # SASS 混合宏定义
│   │       ├── _variables.scss # SASS 变量定义
│   │       └── index.scss      # 样式入口文件
│   ├── components/             # 可复用的 Vue 组件
│   │   ├── HelloWorld.vue      # 示例组件
│   │   ├── TheWelcome.vue      # 示例组件
│   │   ├── WelcomeItem.vue     # 示例组件
│   │   └── icons/              # SVG 图标组件
│   │       ├── IconCommunity.vue
│   │       ├── IconDocumentation.vue
│   │       ├── IconEcosystem.vue
│   │       ├── IconSupport.vue
│   │       └── IconTooling.vue
│   ├── locales/                # 国际化语言文件
│   │   ├── en.json             # 英文语言包
│   │   └── zh.json             # 中文语言包
│   ├── mock/                   # 模拟数据文件
│   │   └── data/               # 模拟数据具体文件
│   │       └── sales.ts        # 销售模块模拟数据
│   ├── plugins/                # Vue 插件配置
│   │   └── i18n.ts             # 国际化插件配置
│   ├── router/                 # Vue Router 路由配置
│   │   └── index.ts            # 路由定义文件
│   ├── stores/                 # Vuex 或 Pinia 状态管理模块
│   │   └── counter.ts          # 计数器示例 Store
│   ├── types/                  # TypeScript 类型定义文件
│   │   ├── element-plus.d.ts   # Element Plus 相关类型声明
│   │   └── module.d.ts         # 项目模块通用类型定义
│   └── views/                  # 页面级组件
│       ├── AboutView.vue       # 关于页面组件
│       └── HomeView.vue        # 主页/车辆列表页面组件
├── env.d.ts                    # TypeScript 环境声明文件
├── eslint.config.ts            # ESLint 配置文件，用于代码风格和质量检查
├── index.html                  # 入口 HTML 文件
├── node_modules/               # 项目依赖库的安装目录
├── package.json                # 项目依赖、脚本和元数据信息
├── pnpm-lock.yaml              # pnpm 包管理器锁定文件，确保依赖版本一致性
├── tsconfig.app.json           # 应用程序特定的 TypeScript 配置
├── tsconfig.json               # TypeScript 基础配置文件
├── tsconfig.node.json          # Node.js 环境下的 TypeScript 配置
├── vite.config.ts              # Vite 构建工具的配置文件
├── 规范/                       # 可能包含项目开发规范或文档
└── 原始底稿/                   # 可能包含项目原型或设计草稿
```

## 开发指南

### 环境要求

- Node.js (推荐 LTS 版本，如 14.x 或更高)
- npm 或 pnpm (推荐 pnpm)

### 启动步骤

1.  **安装依赖**: 在项目根目录执行以下命令安装所有依赖。
    ```bash
    pnpm install
    # 或者 npm install
    ```

2.  **启动开发服务器**: 启动本地开发服务器，并进行实时热重载。
    ```bash
    pnpm run dev
    # 或者 npm run dev
    ```
    项目将在 `http://localhost:8000` 运行。

3.  **构建生产版本**: 打包应用程序以供部署。
    ```bash
    pnpm run build
    # 或者 npm run build
    ```

4.  **代码风格检查**: 运行 ESLint 检查代码规范。
    ```bash
    pnpm run lint
    # 或者 npm run lint
    ```

## 最佳实践

- **表格响应式优化**: `el-table-column` 使用 `min-width` 而非固定 `width`，并移除 `show-overflow-tooltip`，确保内容完整显示且表格可横向滚动。
- **国际化集成**: 全面利用 `Vue I18n` 实现文本的国际化，支持动态语言切换。
- **模块化 API**: 将 API 调用封装在 `src/api/modules` 中，提高代码组织性和可维护性。
- **强类型化**: 广泛使用 TypeScript 定义数据结构和接口，提升代码健壮性和开发效率。

## 贡献

欢迎对本项目做出贡献。在提交代码之前，请确保遵循以下规范：
- 遵循现有的代码风格和设计模式。
- 确保所有新功能都经过测试。
- 为新功能添加国际化支持。

## 许可证

本项目基于 MIT 许可证发布。 