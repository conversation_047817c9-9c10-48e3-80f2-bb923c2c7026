// API集成功能测试脚本
// 在浏览器控制台中运行此脚本来测试API功能

console.log('🧪 开始API集成功能测试...');

// 测试函数
const tests = {
  // 1. 测试数据加载
  async testDataLoading() {
    console.log('\n📊 测试1: 数据加载功能');
    
    // 检查页面是否加载了数据
    const tableRows = document.querySelectorAll('.el-table__body-wrapper tbody tr');
    console.log(`✅ 表格数据行数: ${tableRows.length}`);
    
    if (tableRows.length > 0) {
      console.log('✅ 数据加载成功');
      return true;
    } else {
      console.log('❌ 数据加载失败');
      return false;
    }
  },

  // 2. 测试单据类型切换
  async testDocumentTypeSwitch() {
    console.log('\n🔄 测试2: 单据类型切换');
    
    const documentTypeSelect = document.querySelector('[placeholder*="请选择单据类型"]');
    if (documentTypeSelect) {
      // 模拟点击选择器
      documentTypeSelect.click();
      
      // 等待选项出现
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const options = document.querySelectorAll('.el-select-dropdown__item');
      console.log(`✅ 发现 ${options.length} 个单据类型选项`);
      
      if (options.length >= 3) {
        console.log('✅ 单据类型选择器正常');
        
        // 点击第二个选项（报损单）
        if (options[1]) {
          options[1].click();
          await new Promise(resolve => setTimeout(resolve, 1000));
          console.log('✅ 切换到报损单成功');
        }
        return true;
      }
    }
    
    console.log('❌ 单据类型切换测试失败');
    return false;
  },

  // 3. 测试缓存机制
  async testCacheInvalidation() {
    console.log('\n🗄️ 测试3: 缓存机制');
    
    // 查找刷新按钮
    const refreshButton = Array.from(document.querySelectorAll('button')).find(
      btn => btn.textContent.includes('刷新') || btn.querySelector('.el-icon')
    );
    
    if (refreshButton) {
      console.log('✅ 找到刷新按钮');
      
      // 点击刷新按钮
      const startTime = Date.now();
      refreshButton.click();
      
      // 等待加载完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const endTime = Date.now();
      const loadTime = endTime - startTime;
      
      console.log(`✅ 刷新操作完成，耗时: ${loadTime}ms`);
      
      if (loadTime < 2000) {
        console.log('✅ 刷新性能良好');
        return true;
      }
    }
    
    console.log('❌ 缓存机制测试失败');
    return false;
  },

  // 4. 测试搜索功能
  async testSearchFunction() {
    console.log('\n🔍 测试4: 搜索功能');
    
    const searchInputs = document.querySelectorAll('input[placeholder*="请输入"]');
    console.log(`✅ 发现 ${searchInputs.length} 个搜索输入框`);
    
    if (searchInputs.length > 0) {
      const firstInput = searchInputs[0];
      
      // 输入测试搜索关键词
      firstInput.value = 'TEST';
      firstInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      console.log('✅ 输入搜索关键词');
      
      // 查找搜索按钮
      const searchButton = Array.from(document.querySelectorAll('button')).find(
        btn => btn.textContent.includes('查询') || btn.textContent.includes('搜索')
      );
      
      if (searchButton) {
        searchButton.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('✅ 搜索操作执行');
        return true;
      }
    }
    
    console.log('❌ 搜索功能测试失败');
    return false;
  },

  // 5. 测试业务操作按钮
  async testBusinessOperations() {
    console.log('\n⚙️ 测试5: 业务操作功能');
    
    const operationButtons = document.querySelectorAll('.operation-buttons button');
    console.log(`✅ 发现 ${operationButtons.length} 个操作按钮`);
    
    if (operationButtons.length > 0) {
      // 查找详情按钮
      const detailButton = Array.from(operationButtons).find(
        btn => btn.textContent.includes('详情')
      );
      
      if (detailButton && !detailButton.disabled) {
        console.log('✅ 找到可用的详情按钮');
        return true;
      }
    }
    
    const actionButtons = document.querySelectorAll('button');
    const businessButtons = Array.from(actionButtons).filter(btn => 
      btn.textContent.includes('新建') || 
      btn.textContent.includes('导出') ||
      btn.textContent.includes('报损') ||
      btn.textContent.includes('物料单')
    );
    
    console.log(`✅ 发现 ${businessButtons.length} 个业务操作按钮`);
    
    if (businessButtons.length >= 4) {
      console.log('✅ 业务操作按钮齐全');
      return true;
    }
    
    console.log('❌ 业务操作功能测试失败');
    return false;
  },

  // 6. 测试Mock数据状态
  async testMockDataStatus() {
    console.log('\n🎭 测试6: Mock数据状态检查');
    
    // 检查控制台是否有Mock数据相关的日志
    const hasDataSource = console.log.toString().includes('Data Source') || 
                         localStorage.getItem('mockDataEnabled') === 'true';
    
    console.log('✅ Mock数据状态检查完成');
    
    // 检查页面是否显示了数据
    const hasTableData = document.querySelectorAll('.el-table__body-wrapper tbody tr').length > 0;
    
    if (hasTableData) {
      console.log('✅ Mock数据正常工作');
      return true;
    }
    
    console.log('❌ Mock数据状态检查失败');
    return false;
  }
};

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行所有测试...\n');
  
  const results = [];
  
  for (const [testName, testFunction] of Object.entries(tests)) {
    try {
      const result = await testFunction();
      results.push({ name: testName, passed: result });
    } catch (error) {
      console.error(`❌ 测试 ${testName} 出现错误:`, error);
      results.push({ name: testName, passed: false, error: error.message });
    }
  }
  
  // 输出测试总结
  console.log('\n📋 测试结果总结:');
  console.log('='.repeat(50));
  
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅ 通过' : '❌ 失败';
    console.log(`${status} - ${result.name}`);
    if (result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  console.log('='.repeat(50));
  console.log(`📊 测试通过率: ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！API集成功能正常');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }
  
  return results;
}

// 性能监控函数
function performanceMonitor() {
  console.log('\n📈 性能监控开始...');
  
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'navigation') {
        console.log(`📊 页面加载性能:
          - 页面加载完成: ${Math.round(entry.loadEventEnd)}ms
          - DOM内容加载: ${Math.round(entry.domContentLoadedEventEnd)}ms
          - 首次渲染: ${Math.round(entry.responseEnd)}ms`);
      }
    }
  });
  
  observer.observe({ entryTypes: ['navigation'] });
  
  // 监控API调用
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    const startTime = performance.now();
    return originalFetch.apply(this, args).then(response => {
      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);
      console.log(`🌐 API调用耗时: ${duration}ms - ${args[0]}`);
      return response;
    });
  };
}

// 如果在浏览器中运行，开始测试
if (typeof window !== 'undefined') {
  console.log('🌐 在浏览器环境中运行测试');
  
  // 等待页面完全加载
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        performanceMonitor();
        runAllTests();
      }, 2000);
    });
  } else {
    setTimeout(() => {
      performanceMonitor();
      runAllTests();
    }, 2000);
  }
} else {
  console.log('📝 测试脚本已准备好，请在浏览器控制台中运行');
}