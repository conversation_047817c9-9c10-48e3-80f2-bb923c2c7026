#!/usr/bin/env node

/**
 * 检查所有页面的下拉框使用情况
 * 识别哪些页面使用了统一的字典接口，哪些还在使用硬编码选项
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 需要检查的文件扩展名
const FILE_EXTENSIONS = ['.vue'];

// 统一字典接口的标识
const DICTIONARY_PATTERNS = [
  'useDictionary',
  'useBatchDictionary',
  'getDictionaryList',
  'getBatchDictionaryList',
  'DictionarySelect',
  'DictionaryRadio',
  'DICTIONARY_TYPES'
];

// 下拉框组件的标识
const DROPDOWN_PATTERNS = [
  'el-select',
  'el-option',
  'n-select',
  'el-radio-group'
];

// 硬编码选项的标识
const HARDCODED_PATTERNS = [
  'options\\s*=\\s*\\[',
  'Options\\s*=\\s*\\[',
  'Options\\s*=\\s*ref\\(',
  'Options\\s*=\\s*reactive\\(',
  'Options\\s*=\\s*computed\\(',
  'value="[^"]*".*label="[^"]*"',
  ':value="[^"]*".*:label="[^"]*"'
];

class DropdownChecker {
  constructor() {
    this.results = {
      usingDictionary: [],
      usingHardcoded: [],
      mixed: [],
      noDropdowns: []
    };
  }

  // 递归遍历目录
  walkDirectory(dir, callback) {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        // 跳过 node_modules 等目录
        if (!['node_modules', '.git', 'dist', 'build'].includes(file)) {
          this.walkDirectory(filePath, callback);
        }
      } else if (FILE_EXTENSIONS.some(ext => file.endsWith(ext))) {
        callback(filePath);
      }
    });
  }

  // 检查文件内容
  checkFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(process.cwd(), filePath);

      const hasDictionary = this.checkDictionaryUsage(content);
      const hasDropdowns = this.checkDropdownUsage(content);
      const hasHardcoded = this.checkHardcodedOptions(content);

      const result = {
        path: relativePath,
        hasDictionary,
        hasDropdowns,
        hasHardcoded,
        dictionaryMatches: this.getDictionaryMatches(content),
        dropdownMatches: this.getDropdownMatches(content),
        hardcodedMatches: this.getHardcodedMatches(content)
      };

      // 分类结果
      if (!hasDropdowns) {
        this.results.noDropdowns.push(result);
      } else if (hasDictionary && !hasHardcoded) {
        this.results.usingDictionary.push(result);
      } else if (!hasDictionary && hasHardcoded) {
        this.results.usingHardcoded.push(result);
      } else if (hasDictionary && hasHardcoded) {
        this.results.mixed.push(result);
      }

    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error.message);
    }
  }

  // 检查是否使用了字典接口
  checkDictionaryUsage(content) {
    return DICTIONARY_PATTERNS.some(pattern => {
      const regex = new RegExp(pattern, 'i');
      return regex.test(content);
    });
  }

  // 检查是否有下拉框组件
  checkDropdownUsage(content) {
    return DROPDOWN_PATTERNS.some(pattern => {
      const regex = new RegExp(pattern, 'i');
      return regex.test(content);
    });
  }

  // 检查是否有硬编码选项
  checkHardcodedOptions(content) {
    return HARDCODED_PATTERNS.some(pattern => {
      const regex = new RegExp(pattern, 'i');
      return regex.test(content);
    });
  }

  // 获取字典使用的匹配项
  getDictionaryMatches(content) {
    const matches = [];
    DICTIONARY_PATTERNS.forEach(pattern => {
      const regex = new RegExp(pattern, 'gi');
      const found = content.match(regex);
      if (found) {
        matches.push(...found);
      }
    });
    return [...new Set(matches)];
  }

  // 获取下拉框的匹配项
  getDropdownMatches(content) {
    const matches = [];
    DROPDOWN_PATTERNS.forEach(pattern => {
      const regex = new RegExp(pattern, 'gi');
      const found = content.match(regex);
      if (found) {
        matches.push(...found);
      }
    });
    return [...new Set(matches)];
  }

  // 获取硬编码选项的匹配项
  getHardcodedMatches(content) {
    const matches = [];
    HARDCODED_PATTERNS.forEach(pattern => {
      const regex = new RegExp(pattern, 'gi');
      const found = content.match(regex);
      if (found) {
        matches.push(...found.slice(0, 3)); // 只取前3个匹配项
      }
    });
    return [...new Set(matches)];
  }

  // 生成报告
  generateReport() {
    console.log('\n=== 下拉框使用情况检查报告 ===\n');

    console.log(`📊 统计概览:`);
    console.log(`  - 已使用统一字典接口: ${this.results.usingDictionary.length} 个文件`);
    console.log(`  - 仍使用硬编码选项: ${this.results.usingHardcoded.length} 个文件`);
    console.log(`  - 混合使用: ${this.results.mixed.length} 个文件`);
    console.log(`  - 无下拉框: ${this.results.noDropdowns.length} 个文件`);

    // 已使用字典接口的文件
    if (this.results.usingDictionary.length > 0) {
      console.log(`\n✅ 已使用统一字典接口的文件 (${this.results.usingDictionary.length}个):`);
      this.results.usingDictionary.forEach(result => {
        console.log(`  📁 ${result.path}`);
        console.log(`     字典接口: ${result.dictionaryMatches.join(', ')}`);
      });
    }

    // 仍使用硬编码的文件
    if (this.results.usingHardcoded.length > 0) {
      console.log(`\n❌ 仍使用硬编码选项的文件 (${this.results.usingHardcoded.length}个):`);
      this.results.usingHardcoded.forEach(result => {
        console.log(`  📁 ${result.path}`);
        console.log(`     下拉框组件: ${result.dropdownMatches.join(', ')}`);
        if (result.hardcodedMatches.length > 0) {
          console.log(`     硬编码示例: ${result.hardcodedMatches.slice(0, 2).join(', ')}`);
        }
      });
    }

    // 混合使用的文件
    if (this.results.mixed.length > 0) {
      console.log(`\n⚠️  混合使用的文件 (${this.results.mixed.length}个):`);
      this.results.mixed.forEach(result => {
        console.log(`  📁 ${result.path}`);
        console.log(`     字典接口: ${result.dictionaryMatches.join(', ')}`);
        console.log(`     硬编码示例: ${result.hardcodedMatches.slice(0, 2).join(', ')}`);
      });
    }

    console.log(`\n📋 需要改造的文件总数: ${this.results.usingHardcoded.length + this.results.mixed.length}`);
  }

  // 运行检查
  run() {
    const srcDir = path.join(process.cwd(), 'src/views');

    if (!fs.existsSync(srcDir)) {
      console.error('src/views 目录不存在');
      return;
    }

    console.log('🔍 开始检查下拉框使用情况...');

    this.walkDirectory(srcDir, (filePath) => {
      this.checkFile(filePath);
    });

    this.generateReport();
  }
}

// 运行检查
const checker = new DropdownChecker();
checker.run();
