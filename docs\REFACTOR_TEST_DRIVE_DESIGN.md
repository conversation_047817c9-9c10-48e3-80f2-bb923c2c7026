# 设计文档：试驾管理模块 (Test Drive Management) 重构

- **项目名称**: DMS 前端重构 - 试驾管理模块
- **日期**: 2025年7月20日
- **负责人**: gg-fe (Frontend Architect)
- **状态**: **设计完成**

## 1. 项目概述

### 1.1. 目标

本次重构旨在将现有的 `@src/views/test-drive` 模块完全迁移至新的项目规范下。目标是优化目录结构、统一组件命名、提升代码复用性，并确保 API、类型定义、路由、国际化等资源遵循统一的模块化管理标准。

### 1.2. 范围

- **页面**: `test-drive-list` 及其所有子组件。
- **相关资源**: 与试驾功能相关的 API、类型定义、路由、国际化和 Mock 数据。

## 2. 现状分析

### 2.1. 当前目录结构

当前 `test-drive` 模块的结构如下，存在页面与组件混合、目录层级不清晰的问题：

```
src/views/test-drive/
└── test-drive-list/
    ├── index.vue                 # 列表页 (路由页面)
    └── components/
        ├── AddTestDriveModal.vue   # 新增弹窗 (非路由页面)
        ├── EditTestDriveModal.vue  # 编辑弹窗 (非路由页面)
        └── TestDriveDetailModal.vue# 详情弹窗 (非路由页面)
```

### 2.2. 主要问题

1.  **目录层级不规范**: `test-drive` 和 `test-drive-list` 存在多余的层级。
2.  **命名不规范**: `index.vue` 未能清晰表达其功能。弹窗组件命名为 `*Modal.vue`，应统一为 `*View.vue` 或 `*Component.vue`。
3.  **代码重复**: `AddTestDriveModal.vue` 和 `EditTestDriveModal.vue` 中存在大量重复的表单代码，可以抽取为独立的表单组件。
4.  **资源分散**: API 和类型定义 (`@/api/modules/test-drive.ts`, `@/api/types/test-drive.d.ts`) 未按业务域（如 `sales`）进行组织。

## 3. 重构方案

### 3.1. 新目录结构

根据《页面目录结构规范》，我们将 `test-drive` 归类于 `sales`（销售）模块下，并采用新的目录结构：

```
src/views/sales/testDrive/
├── TestDriveView.vue           # 列表页 (路由页面, 原 index.vue)
└── components/                 # 非路由页面和组件统一目录
    ├── TestDriveDetailView.vue # 详情页 (原 TestDriveDetailModal.vue)
    ├── TestDriveCreateView.vue # 新建页 (原 AddTestDriveModal.vue)
    ├── TestDriveEditView.vue   # 编辑页 (原 EditTestDriveModal.vue)
    ├── TestDriveForm.vue       # [新增] 可复用的试驾表单组件
    └── TestDriveSearchForm.vue # [新增] 列表页的搜索表单组件
```

### 3.2. 关联资源迁移

所有相关资源将同步迁移到 `sales` 模块下：

-   **API**:
    -   **From**: `@/api/modules/test-drive.ts`
    -   **To**: `@/api/modules/sales/testDrive.ts`
-   **类型定义**:
    -   **From**: `@/api/types/test-drive.d.ts`
    -   **To**: `@/types/sales/testDrive.d.ts`
-   **路由**:
    -   在 `@/router/modules/sales.ts` 中定义 `TestDriveView` 的路由。
-   **国际化**:
    -   相关文本将添加到 `@/locales/modules/sales/zh.json` 和 `en.json`。
-   **Mock 数据**:
    -   创建 `@/mock/data/sales/testDrive.ts` 用于存放 Mock 数据。

### 3.3. 组件重构设计

1.  **`TestDriveView.vue` (原 `index.vue`)**:
    -   **职责**: 作为路由页面，负责展示试驾列表、分页和整体布局。
    -   **重构**:
        -   将原有的筛选表单部分拆分为独立的 `TestDriveSearchForm.vue` 组件。
        -   管理 `TestDriveCreateView`, `TestDriveEditView`, `TestDriveDetailView` 的显示/隐藏状态。
        -   处理列表数据的加载、导出等操作。

2.  **`TestDriveSearchForm.vue` (新增)**:
    -   **职责**: 封装试驾列表的搜索和重置逻辑。
    -   **来源**: 从 `index.vue` 的 `<el-form>` 部分抽取。
    -   **接口**: 通过 `emits` 向父组件 `TestDriveView` 传递搜索事件。

3.  **`TestDriveForm.vue` (新增)**:
    -   **职责**: 封装试驾的核心表单字段和验证逻辑，用于新建和编辑。
    -   **来源**: 合并 `AddTestDriveModal.vue` 和 `EditTestDriveModal.vue` 中的表单内容。
    -   **接口**: 通过 `props` 接收表单数据，通过 `emits` 触发提交事件。

4.  **`TestDriveCreateView.vue` (原 `AddTestDriveModal.vue`)**:
    -   **职责**: 作为非路由页面，处理新建试驾的完整逻辑，包括潜客搜索。
    -   **重构**:
        -   UI 从 `el-dialog` 调整为适合在页面中嵌入的模式（如 `el-drawer` 或直接渲染）。
        -   内部将使用 `TestDriveForm.vue` 来渲染核心表单。
        -   保留潜客搜索的逻辑。

5.  **`TestDriveEditView.vue` (原 `EditTestDriveModal.vue`)**:
    -   **职责**: 作为非路由页面，处理编辑试驾的逻辑。
    -   **重构**:
        -   UI 从 `el-dialog` 调整为与 `TestDriveCreateView` 一致的风格。
        -   内部使用 `TestDriveForm.vue` 渲染核心表单。
        -   通过 `props` 接收待编辑的数据，并加载完整详情。

6.  **`TestDriveDetailView.vue` (原 `TestDriveDetailModal.vue`)**:
    -   **职责**: 作为非路由页面，展示试驾单的详细信息。
    -   **重构**:
        -   UI 从 `el-dialog` 调整为与 `TestDriveCreateView` 一致的风格。
        -   数据展示逻辑保持不变。

## 4. API 与类型定义

### 4.1. API (`@/api/modules/sales/testDrive.ts`)

API 接口保持现有功能，仅调整路径。

```typescript
export const testDriveApi = {
  // 获取试驾列表
  getTestDriveList(params: GetTestDriveListRequest): Promise<ApiResponse<PageResult<TestDriveRecord>>>;
  // 获取试驾详情
  getTestDriveDetail(testDriveNo: string): Promise<ApiResponse<TestDriveRecord>>;
  // 创建试驾单
  createTestDrive(data: CreateTestDriveDto): Promise<ApiResponse<null>>;
  // 更新试驾单
  updateTestDrive(data: UpdateTestDriveDto): Promise<ApiResponse<null>>;
  // 导出试驾列表
  exportTestDriveList(params: ExportTestDriveListRequest): Promise<Blob>;
  // 搜索潜客
  searchLeads(params: SearchLeadsRequest): Promise<ApiResponse<Lead>>;
};
```

### 4.2. 类型定义 (`@/types/sales/testDrive.d.ts`)

类型定义文件将统一存放于此。

```typescript
// 试驾记录主类型
export interface TestDriveRecord {
  id: string;
  testDriveNo: string;
  customerName: string;
  customerPhone: string;
  // ... 其他潜客和试驾信息字段
}

// 创建试驾单的数据传输对象 (DTO)
export interface CreateTestDriveDto {
  prospectId: string;
  model: string;
  variant: string;
  // ... 其他必填字段
}

// 更新试驾单的数据传输对象 (DTO)
export interface UpdateTestDriveDto extends CreateTestDriveDto {
  id: string;
}

// 列表请求参数
export interface GetTestDriveListRequest extends PageRequest {
  customerName?: string;
  customerPhone?: string;
  // ... 其他筛选字段
}
```

## 5. 实施步骤与检查清单

### 5.1. 实施步骤

1.  **创建目录**: 在 `src/views/sales/` 下创建 `testDrive` 及其 `components` 子目录。
2.  **迁移并重命名**:
    -   将 `src/views/test-drive/test-drive-list/index.vue` 移动并重命名为 `src/views/sales/testDrive/TestDriveView.vue`。
    -   将 `AddTestDriveModal.vue` -> `TestDriveCreateView.vue`。
    -   将 `EditTestDriveModal.vue` -> `TestDriveEditView.vue`。
    -   将 `TestDriveDetailModal.vue` -> `TestDriveDetailView.vue`。
3.  **创建新组件**:
    -   创建 `TestDriveForm.vue`，并从 `TestDriveCreateView` 和 `TestDriveEditView` 中抽取通用表单逻辑。
    -   创建 `TestDriveSearchForm.vue`，并从 `TestDriveView` 中抽取搜索表单逻辑。
4.  **代码重构**:
    -   在 `TestDriveCreateView` 和 `TestDriveEditView` 中引入并使用 `TestDriveForm.vue`。
    -   在 `TestDriveView` 中引入并使用 `TestDriveSearchForm.vue`。
    -   更新所有组件的 `import` 路径。
5.  **资源迁移**:
    -   迁移 API 文件、类型定义文件。
    -   更新路由配置文件 `src/router/modules/sales.ts`。
    -   将国际化文本迁移至 `src/locales/modules/sales/`。
6.  **验证**:
    -   运行应用，确保试驾管理的所有功能（增、删、改、查、导出）正常工作。
    -   检查控制台有无错误。

### 5.2. 新增页面检查清单

-   **目录结构检查**:
    -   [x] 路由页面 (`TestDriveView.vue`) 直接放在功能目录下。
    -   [x] 非路由页面和组件统一放在 `components/` 目录下。
-   **命名规范检查**:
    -   [x] 路由页面文件名符合 `*View.vue` 格式。
    -   [x] 非路由页面文件名符合 `*View.vue` 格式。
    -   [x] 组件文件名符合功能命名规范。
-   **功能完整性检查**:
    -   [x] 相关的 API 接口已定义。
    -   [x] Mock 数据已准备 (若需要)。
    -   [x] 路由配置已添加。
    -   [x] 类型定义已完善。
    -   [x] 国际化文本已添加。
-   **模块一致性检查**:
    -   [x] API 模块结构与页面结构保持一致。
    -   [x] 类型定义结构与功能模块保持一致。
    -   [x] 国际化文件按模块正确组织。
    -   [x] 路由配置按模块正确组织。

---

此设计文档已准备就绪，可以作为 `cc-fe` (前端实现专家) 的开发指令。
