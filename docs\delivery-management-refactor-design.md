# 交付管理页面重构设计文档

## 1. 项目基本信息

- **重构目标文件**: `src/views/customerOrderManagement/DeliveryManagementView.vue`
- **目标模块**: `sales` (销售模块)
- **功能名称**: `delivery` (交付管理)
- **重构类型**: 页面模块化重构 + MyBatisPlus分页标准化 + 数据字典规范化

## 2. 现状分析

### 2.1 当前文件结构问题
- ❌ 页面位置不符合模块化规范：`src/views/customerOrderManagement/`
- ❌ 使用非标准分页参数：`currentPage` 和 `page` 混用，应统一为 `pageNum`
- ❌ 分页响应结构不一致：代码中同时使用 `response.records` 和 `response.list`
- ❌ 数据字典实现不规范：硬编码状态映射，未使用标准字典转义方法
- ❌ 缺少独立的API模块、类型定义和Mock数据模块化

### 2.2 MyBatisPlus分页参数分析

#### 2.2.1 当前页面的分页问题
```typescript
// ❌ 当前实现的问题
const pagination = reactive({
  currentPage: 1,     // 应为 pageNum
  pageSize: 20
})

// API调用参数不一致
const params: DeliveryOrderParams = {
  ...searchParams,
  page: pagination.currentPage,    // ❌ 应为 pageNum
  pageSize: pagination.pageSize
}

// 响应数据处理不一致
tableData.value = response.records || []  // 注释说使用list但实际用records
```

#### 2.2.2 标准MyBatisPlus规范（目标）
```typescript
// ✅ 标准MyBatisPlus分页参数
interface PageParams {
  pageNum: number;    // 当前页码，从1开始
  pageSize: number;   // 每页条数
}

// ✅ 标准MyBatisPlus响应结构
interface PageResponse<T> {
  records: T[];       // 数据列表
  total: number;      // 总条数
  pageNum: number;    // 当前页码
  pageSize: number;   // 每页条数
  pages: number;      // 总页数
}
```

### 2.3 数据字典实现对比分析

#### 2.3.1 参考页面（ProspectsView.vue）的标准实现
```vue
// ✅ 标准字典数据获取
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS
]);

// ✅ 统一的字段转义方法
const getName = (dictionaryType: DictionaryType, code: string) => {
  return getNameByCode(dictionaryType, code) || code;
};

// ✅ 表格列中的标准使用
<el-table-column :label="t('prospects.sourceChannel')" prop="sourceChannel" min-width="120">
  <template #default="{ row }">
    {{ getName(DICTIONARY_TYPES.CUSTOMER_SOURCE, row.sourceChannel) }}
  </template>
</el-table-column>
```

#### 2.3.2 当前页面的非标准实现
```vue
// ❌ 硬编码的状态映射
const getStatusTagType = (status: DeliveryStatus) => {
  const statusMap = {
    pending_delivery: 'warning',
    pending_confirm: 'primary',
    delivered: 'success'
  }
  return statusMap[status] || 'info'
}

// ❌ 硬编码的状态文本
const getStatusText = (status: DeliveryStatus) => {
  const statusTextMap = {
    pending_delivery: t('statusPending'),
    pending_confirm: t('statusConfirming'),
    delivered: t('statusCompleted')
  }
  return statusTextMap[status] || status
}
```

## 3. 重构目标结构

### 3.1 目标目录结构
```
src/
├── views/sales/delivery/
│   ├── DeliveryView.vue                # 重构后的主页面
│   └── components/                     # 组件目录
│       ├── DeliverySubmitDialog.vue
│       ├── DeliveryConfirmDialog.vue
│       ├── DeliveryDetailDialog.vue
│       └── DeliveryExportDialog.vue
├── api/modules/sales/
│   └── delivery.ts                     # API模块
├── types/sales/
│   └── delivery.d.ts                   # 类型定义
├── mock/data/sales/
│   └── delivery.ts                     # Mock数据
└── locales/modules/sales/
    ├── zh.json                         # 中文翻译（包含delivery）
    └── en.json                         # 英文翻译（包含delivery）
```

## 4. MyBatisPlus分页组件标准化实现

### 4.1 分页参数标准化

#### 4.1.1 类型定义修正
```typescript
// src/types/sales/delivery.d.ts
export interface DeliverySearchParams {
  pageNum?: number;        // ✅ 标准MyBatisPlus参数
  pageSize?: number;       // ✅ 标准MyBatisPlus参数
  deliveryNumber?: string;
  orderNumber?: string;
  customerName?: string;
  customerPhone?: string;
  vin?: string;
  model?: string;
  variant?: string;
  color?: string;
  orderStatus?: string;
  dealerStore?: string;
  salesConsultant?: string;
  deliveryStatus?: string;
  customerConfirmed?: boolean;
  confirmationType?: string;
  deliveryTimeRange?: [string, string];
  customerConfirmTimeRange?: [string, string];
}

export interface DeliveryPageResponse {
  records: DeliveryListItem[];  // ✅ 标准MyBatisPlus响应
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}
```

#### 4.1.2 分页组件配置修正
```vue
<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="pagination.pageNum"    <!-- ✅ 修正为pageNum -->
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
// 分页状态修正
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 20,
  total: 0
})

// API调用参数修正
const fetchData = async () => {
  const params: DeliverySearchParams = {
    ...searchParams,
    pageNum: pagination.pageNum,    // ✅ 修正为pageNum
    pageSize: pagination.pageSize
  }
  
  const response = await getDeliveryList(params)
  tableData.value = response.records    // ✅ 标准响应结构
  pagination.total = response.total
}

// 分页事件处理修正
const handleCurrentChange = (page: number) => {
  pagination.pageNum = page    // ✅ 修正为pageNum
  fetchData()
}
</script>
```

### 4.2 API模块标准化实现

```typescript
// src/api/modules/sales/delivery.ts
import request from '@/api';
import type { 
  DeliverySearchParams, 
  DeliveryPageResponse 
} from '@/types/sales/delivery';
import { getDeliveryListMock } from '@/mock/data/sales/delivery';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getDeliveryList = (
  params: DeliverySearchParams
): Promise<DeliveryPageResponse> => {
  if (USE_MOCK_API) {
    return getDeliveryListMock(params);
  } else {
    // 标准MyBatisPlus分页接口
    return request.post<any, DeliveryPageResponse>(
      '/sales/delivery/page', 
      params
    );
  }
};
```

## 5. 数据字典标准化实现

### 5.1 字典类型定义
```typescript
// src/constants/dictionary.ts (新增)
export const DICTIONARY_TYPES = {
  // 现有字典类型...
  ORDER_STATUS: 'ORDER_STATUS',             // 订单状态
  DELIVERY_STATUS: 'DELIVERY_STATUS',       // 交付状态
  CONFIRMATION_METHOD: 'CONFIRMATION_METHOD', // 确认方式
  BOOLEAN_TYPE: 'BOOLEAN_TYPE',             // 布尔类型
  DEALER_STORE: 'DEALER_STORE',             // 经销商门店
} as const;
```

### 5.2 标准化字典使用方式

#### 5.2.1 组合式函数引入
```typescript
// 参考ProspectsView.vue的标准实现
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.DELIVERY_STATUS,
  DICTIONARY_TYPES.CONFIRMATION_METHOD,
  DICTIONARY_TYPES.BOOLEAN_TYPE,
  DICTIONARY_TYPES.DEALER_STORE
]);
```

#### 5.2.2 字段转义函数标准化
```typescript
// 统一的字段转义函数（参考ProspectsView.vue）
const getName = (dictionaryType: DictionaryType, code: string) => {
  return getNameByCode(dictionaryType, code) || code;
};

// 获取字典选项
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS));
const deliveryStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.DELIVERY_STATUS));
const confirmationMethodOptions = computed(() => getOptions(DICTIONARY_TYPES.CONFIRMATION_METHOD));
const booleanOptions = computed(() => getOptions(DICTIONARY_TYPES.BOOLEAN_TYPE));
const dealerStoreOptions = computed(() => getOptions(DICTIONARY_TYPES.DEALER_STORE));
```

#### 5.2.3 标签样式映射标准化
```typescript
// 标准化的状态标签映射配置
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.ORDER_STATUS]: {
    'normal': 'success',
    'cancelled': 'danger',
    'pending_allocation': 'info',
    'allocating': 'warning',
    'allocated': 'primary',
    'pending_delivery': 'warning',
    'delivered': 'success'
  },
  [DICTIONARY_TYPES.DELIVERY_STATUS]: {
    'pending_delivery': 'warning',
    'pending_confirm': 'primary',
    'delivered': 'success'
  }
} as const;

// 统一的标签类型获取函数
const getStatusTagType = (status: string, dictionaryType: DictionaryType) => {
  return STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';
};
```

#### 5.2.4 表格列实现标准化
```vue
<!-- 订单状态列 -->
<el-table-column prop="orderStatus" :label="t('orderStatus')" width="100">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.orderStatus, DICTIONARY_TYPES.ORDER_STATUS)">
      {{ getName(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
    </el-tag>
  </template>
</el-table-column>

<!-- 交付状态列 -->
<el-table-column prop="deliveryStatus" :label="t('deliveryStatus')" width="100">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.deliveryStatus, DICTIONARY_TYPES.DELIVERY_STATUS)">
      {{ getName(DICTIONARY_TYPES.DELIVERY_STATUS, row.deliveryStatus) }}
    </el-tag>
  </template>
</el-table-column>

<!-- 确认方式列 -->
<el-table-column prop="confirmationType" :label="t('confirmationType')" width="100">
  <template #default="{ row }">
    {{ row.confirmationType ? getName(DICTIONARY_TYPES.CONFIRMATION_METHOD, row.confirmationType) : 'N/A' }}
  </template>
</el-table-column>
```

#### 5.2.5 搜索表单下拉选择标准化
```vue
<!-- 订单状态下拉 -->
<el-form-item :label="t('orderStatus')">
  <el-select
    v-model="searchParams.orderStatus"
    :placeholder="t('orderStatusPlaceholder')"
    clearable
    :loading="dictionaryLoading"
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in orderStatusOptions"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>

<!-- 交付状态下拉 -->
<el-form-item :label="t('deliveryStatus')">
  <el-select
    v-model="searchParams.deliveryStatus"
    :placeholder="t('deliveryStatusPlaceholder')"
    clearable
    :loading="dictionaryLoading"
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in deliveryStatusOptions"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>

<!-- 经销商门店下拉 -->
<el-form-item :label="t('dealerStore')">
  <el-select
    v-model="searchParams.dealerStore"
    :placeholder="t('dealerStorePlaceholder')"
    clearable
    :loading="dictionaryLoading"
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in dealerStoreOptions"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>
```

## 6. Mock数据实现规范

### 6.1 Mock数据结构
```typescript
// src/mock/data/sales/delivery.ts
import type {
  DeliverySearchParams,
  DeliveryPageResponse,
  DeliveryListItem
} from '@/types/sales/delivery';

// 动态生成Mock数据（25-30条）
function generateMockDeliveryData(): DeliveryListItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: DeliveryListItem[] = [];

  const orderStatuses = ['normal', 'pending_delivery', 'delivered'];
  const deliveryStatuses = ['pending_delivery', 'pending_confirm', 'delivered'];
  const confirmationTypes = ['app', 'offline'];
  const dealerStores = ['store_001', 'store_002', 'store_003'];

  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      deliveryId: `delivery_${i + 1}`,
      deliveryNumber: `DEL${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(i + 1).padStart(3, '0')}`,
      orderId: `order_${i + 1}`,
      orderNumber: `SO${String(i + 1).padStart(8, '0')}`,
      customerName: `客户${i + 1}`,
      customerPhone: `138${String(i + 1).padStart(8, '0')}`,
      vin: `VIN${String(i + 1).padStart(17, '0')}`,
      model: `Model${i % 3 + 1}`,
      variant: `Variant${i % 2 + 1}`,
      color: ['白色', '黑色', '银色'][i % 3],
      dealerStore: dealerStores[Math.floor(Math.random() * dealerStores.length)],
      salesConsultant: `顾问${i + 1}`,
      orderStatus: orderStatuses[Math.floor(Math.random() * orderStatuses.length)],
      deliveryStatus: deliveryStatuses[Math.floor(Math.random() * deliveryStatuses.length)],
      customerConfirmed: Math.random() > 0.5,
      confirmationType: confirmationTypes[Math.floor(Math.random() * confirmationTypes.length)],
      invoiceTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      deliveryTime: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000).toISOString(),
      customerConfirmTime: new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000).toISOString(),
      deliveryNotes: `交付备注${i + 1}`,
      signaturePhoto: Math.random() > 0.7 ? `https://example.com/signature${i + 1}.jpg` : null
    });
  }

  return mockData;
}

const mockData = generateMockDeliveryData();

export const getDeliveryListMock = (
  params: DeliverySearchParams
): Promise<DeliveryPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.deliveryNumber) {
        filteredData = filteredData.filter(item =>
          item.deliveryNumber.includes(params.deliveryNumber!)
        );
      }

      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNumber.includes(params.orderNumber!)
        );
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.deliveryStatus) {
        filteredData = filteredData.filter(item =>
          item.deliveryStatus === params.deliveryStatus
        );
      }

      if (params.orderStatus) {
        filteredData = filteredData.filter(item =>
          item.orderStatus === params.orderStatus
        );
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        records: filteredData.slice(start, end),
        total,
        pageNum,
        pageSize,
        pages
      });
    }, 500);
  });
};
```

## 7. 国际化文件更新

### 7.1 中文翻译文件
```json
// src/locales/modules/sales/zh.json
{
  "delivery": {
    "searchTitle": "筛选条件",
    "listTitle": "交付列表",
    "totalCount": "共 {count} 条记录",
    "deliveryNumber": "交车单号",
    "deliveryNumberPlaceholder": "请输入交车单号",
    "orderNumber": "订单编号",
    "orderNoPlaceholder": "请输入订单编号",
    "customerName": "客户姓名",
    "customerNamePlaceholder": "请输入客户姓名",
    "customerPhone": "客户电话",
    "customerMobilePlaceholder": "请输入客户电话",
    "vin": "车架号",
    "vinPlaceholder": "请输入车架号",
    "model": "车型",
    "variant": "配置",
    "color": "颜色",
    "orderStatus": "订单状态",
    "orderStatusPlaceholder": "请选择订单状态",
    "dealerStore": "经销商门店",
    "dealerStorePlaceholder": "请选择门店",
    "salesConsultant": "销售顾问",
    "salesmanPlaceholder": "请输入销售顾问",
    "deliveryStatus": "交付状态",
    "deliveryStatusPlaceholder": "请选择交付状态",
    "customerConfirmed": "客户确认",
    "customerConfirmedPlaceholder": "请选择确认状态",
    "confirmationType": "确认方式",
    "confirmationTypePlaceholder": "请选择确认方式",
    "deliveryTime": "交付时间",
    "customerConfirmTime": "客户确认时间",
    "startDate": "开始日期",
    "endDate": "结束日期",
    "customerConfirmTimeStartPlaceholder": "确认开始日期",
    "customerConfirmTimeEndPlaceholder": "确认结束日期",
    "invoiceTime": "开票时间",
    "actualDeliveryDate": "实际交付日期",
    "deliveryNotes": "交付备注",
    "signaturePhoto": "签字照片",
    "submitConfirm": "提交确认",
    "deliveryConfirm": "交车确认",
    "fetchDataFailed": "获取数据失败",
    "detailNotFound": "详情数据未找到",
    "fetchDetailFailed": "获取详情失败",
    "printFeatureNotImplemented": "打印功能待实现",
    "submitConfirmSuccess": "提交确认成功",
    "submitConfirmFailed": "提交确认失败",
    "deliveryConfirmSuccess": "交车确认成功",
    "deliveryConfirmFailed": "交车确认失败",
    "exportSuccess": "导出成功",
    "exportFailed": "导出失败"
  }
}
```

## 8. 路由配置更新

```typescript
// src/router/index.ts
{
  path: '/sales/delivery',
  name: 'Delivery',
  component: () => import('@/views/sales/delivery/DeliveryView.vue'),
  meta: {
    title: 'menu.delivery',
    requiresAuth: true,
    icon: 'Truck'
  }
}
```

## 9. 重构执行计划

### 9.1 第一阶段：基础结构重构
1. 创建目标目录结构
2. 创建类型定义文件
3. 创建Mock数据模块
4. 创建API模块

### 9.2 第二阶段：分页标准化
1. 修正分页参数命名（currentPage → pageNum, page → pageNum）
2. 统一分页响应结构处理
3. 更新API调用方式

### 9.3 第三阶段：数据字典标准化
1. 引入标准字典组合式函数
2. 实现统一的字段转义方法
3. 标准化标签样式映射
4. 更新表格列和搜索表单实现

### 9.4 第四阶段：页面迁移和测试
1. 迁移页面文件到新位置
2. 更新国际化文件
3. 更新路由配置
4. 功能测试和验证

## 10. 验证清单

### 10.1 分页功能验证
- [ ] 分页参数使用标准的 `pageNum` 和 `pageSize`
- [ ] 分页响应结构符合MyBatisPlus标准
- [ ] 分页组件正常工作（页码切换、页大小切换）
- [ ] Mock数据支持分页功能

### 10.2 数据字典验证
- [ ] 使用 `useBatchDictionary` 获取字典数据
- [ ] 字段转义使用统一的 `getName` 方法
- [ ] 标签样式映射配置标准化
- [ ] 搜索下拉选择使用字典数据
- [ ] 门店数据使用字典而非硬编码

### 10.3 功能完整性验证
- [ ] 搜索功能正常工作
- [ ] 表格数据正确显示
- [ ] 操作按钮权限控制正常
- [ ] 弹窗组件正常工作
- [ ] 国际化切换正常
- [ ] Mock数据功能完整

---

**本设计文档基于项目现有规范和最佳实践，确保重构后的页面完全符合项目标准，特别是MyBatisPlus分页规范和数据字典使用规范。**
```
