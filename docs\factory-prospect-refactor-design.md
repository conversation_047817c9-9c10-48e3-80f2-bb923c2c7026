# Factory Prospect 重构设计文档

> **重要说明**: 本设计文档已根据 `/规范/页面目录结构规范.md` 进行审查和修正，确保完全符合DMS前端项目的目录结构规范。
> 
> **模块归属说明**: 厂端潜客池管理功能归属于 `sales` 模块，因为潜客管理是销售业务流程的核心环节，包括潜客跟进、转化管理等都直接服务于销售业务。

## 1. 重构目标与范围

### 1.1 重构目标
将 `src/views/prospective-customer/factory-prospect/` 按照DMS前端重构技术规范进行模块化重构，实现：
- 符合目录结构规范的模块化组织
- 统一的MyBatisPlus分页组件标准化
- 规范的数据字典实现方式
- 标准的API请求响应处理

### 1.2 重构范围
- **源目录**: `src/views/prospective-customer/factory-prospect/`
- **目标模块**: `sales` (销售模块 - 厂端潜客池管理属于销售业务范畴)
- **功能名称**: `factoryProspect` (厂端潜客池)

## 2. 现状分析

### 2.1 当前文件结构问题
```
❌ 当前结构:
src/views/prospective-customer/factory-prospect/
├── index.vue                           # 主页面文件
└── components/
    ├── CustomerDetailModal.vue         # 客户详情模态框
    └── IntentLevelConfigModal.vue      # 意向级别配置模态框

✅ 目标结构:
src/views/sales/factoryProspect/
├── FactoryProspectView.vue             # 主页面文件（路由页面）
└── components/
    ├── CustomerDetailModal.vue         # 客户详情模态框（非路由页面）
    └── IntentLevelConfigModal.vue      # 意向级别配置模态框（非路由页面）
src/api/modules/sales/
└── factoryProspect.ts                 # API模块
src/types/sales/
└── factoryProspect.d.ts               # 类型定义
src/mock/data/sales/
└── factoryProspect.ts                 # Mock数据
```

### 2.2 MyBatisPlus分页问题分析
**当前实现问题**:
```typescript
// ❌ 分页参数不符合MyBatisPlus标准
const pagination = reactive({
  page: 1,              // ❌ 应为 pageNum
  pageSize: 20,         // ✅ 正确
  itemCount: 0          // ❌ 应为 total
});

// ❌ API调用参数不一致
const params = {
  ...filterForm,
  pageNum: pagination.page,    // ❌ 应为 pageNum: pagination.pageNum
  pageSize: pagination.pageSize
}

// ❌ 分页组件绑定不符合标准
<el-pagination
  v-model:current-page="pagination.page"      // ❌ 应绑定 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.itemCount"               // ❌ 应为 total
/>

// ✅ 响应处理部分正确
if ('result' in res && res.result) {
  const result = res.result as Record<string, any>;
  if ('records' in result) {
    tableData.value = result.records;           // ✅ 正确使用 records
    pagination.itemCount = result.total;        // ✅ 正确使用 total
  }
}
```

**目标实现**:
```typescript
// ✅ 标准MyBatisPlus分页参数
interface FactoryProspectSearchParams {
  pageNum?: number;     // MyBatisPlus标准参数
  pageSize?: number;    // MyBatisPlus标准参数
  // ... 其他搜索参数
}

// ✅ 标准分页状态
const pagination = reactive({
  pageNum: 1,           // 修正为 pageNum
  pageSize: 20,
  total: 0,             // 修正为 total
});

// ✅ 标准分页组件绑定
<el-pagination
  v-model:current-page="pagination.pageNum"
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
/>
```

### 2.3 数据字典实现问题分析
**当前实现问题**:
```typescript
// ❌ 硬编码选项数据
const storeOptions = ref<SelectOption[]>([
  { label: '全部门店', value: '' },
  { label: '北京朝阳店', value: 1 },
  { label: '上海浦东店', value: 2 },
  { label: '深圳南山店', value: 3 }
]);

const storeCountOptions = ref<SelectOption[]>([
  { label: '全部', value: '' },
  { label: '单店', value: 1 },
  { label: '多店', value: 2 }
]);

// ❌ 硬编码标签类型映射
const getLevelTagType = (level: string | undefined) => {
  switch (level) {
    case 'H': return 'success';
    case 'M': return 'warning';
    case 'L': return 'info';
    default: return 'primary';
  }
};

// ❌ 表格列直接显示原始值
<el-table-column prop="currentIntentLevel" label="潜客级别">
  <template #default="{ row }">
    <el-tag :type="getLevelTagType(row.currentIntentLevel)">
      {{ row.currentIntentLevel || '未设置' }}  <!-- ❌ 直接显示原始值 -->
    </el-tag>
  </template>
</el-table-column>
```

**目标实现** (参考ProspectsView.vue):
```typescript
// ✅ 使用标准数据字典
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS,
  DICTIONARY_TYPES.STORE_COUNT_TYPE
]);

// ✅ 标准字典转义
<el-table-column prop="currentIntentLevel" label="潜客级别">
  <template #default="{ row }">
    <el-tag :type="getLevelTagType(row.currentIntentLevel)">
      {{ getNameByCode(DICTIONARY_TYPES.INTENT_LEVEL, row.currentIntentLevel) }}
    </el-tag>
  </template>
</el-table-column>
```

### 2.4 API响应处理分析
**当前实现**:
```typescript
// ✅ 正确的响应处理模式
const res = await getFactoryProspectList(params);

if (res && typeof res === 'object') {
  if ('result' in res && res.result) {
    const result = res.result as Record<string, any>;
    if ('records' in result) {
      tableData.value = result.records;    // ✅ 正确使用 result.records
      pagination.itemCount = result.total; // ✅ 正确使用 result.total
    }
  }
}
```

**评估结果**: 当前API响应处理已符合标准，使用了`response.result`模式。

## 3. 重构技术方案

### 3.1 目录结构重构

#### 3.1.1 创建目标目录结构
```bash
# 创建模块化目录
mkdir -p src/views/sales/factoryProspect/components
mkdir -p src/api/modules/sales
mkdir -p src/types/sales
mkdir -p src/mock/data/sales
```

#### 3.1.2 文件迁移映射
```
源文件 → 目标文件:
src/views/prospective-customer/factory-prospect/index.vue
→ src/views/sales/factoryProspect/FactoryProspectView.vue

src/views/prospective-customer/factory-prospect/components/CustomerDetailModal.vue
→ src/views/sales/factoryProspect/components/CustomerDetailModal.vue

src/views/prospective-customer/factory-prospect/components/IntentLevelConfigModal.vue
→ src/views/sales/factoryProspect/components/IntentLevelConfigModal.vue

新增文件:
src/api/modules/sales/factoryProspect.ts      # API模块
src/types/sales/factoryProspect.d.ts          # 类型定义
src/mock/data/sales/factoryProspect.ts        # Mock数据
```

### 3.2 MyBatisPlus分页标准化实现

#### 3.2.1 分页参数规范修正
```typescript
// 修正分页搜索参数接口
export interface FactoryProspectSearchParams {
  pageNum?: number;        // ✅ 标准MyBatisPlus参数
  pageSize?: number;       // ✅ 标准MyBatisPlus参数
  leadId?: string;
  storeId?: string;
  storeCount?: string;
  registrationTimeStart?: string;
  registrationTimeEnd?: string;
  viewType?: 'all' | 'cross_store' | 'defeated' | 'converted';
}
```

#### 3.2.2 分页响应结构标准化
```typescript
// 标准MyBatisPlus分页响应
export interface FactoryProspectPageResponse {
  result: {
    records: FactoryProspectListItem[];    // ✅ MyBatisPlus标准响应
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}
```

#### 3.2.3 分页组件配置修正
```vue
<template>
  <div class="pagination-section mt-20">
    <el-pagination
      v-model:current-page="pagination.pageNum"    <!-- ✅ 修正为pageNum -->
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"                    <!-- ✅ 修正为total -->
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handlePageSizeChange"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup lang="ts">
// 分页状态修正
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 20,
  total: 0           // ✅ 修正为total
});

// 分页处理函数修正
const handlePageSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  fetchTableData();
};

const handlePageChange = (val: number) => {
  pagination.pageNum = val;  // ✅ 修正为pageNum
  fetchTableData();
};

// API调用参数修正
const fetchTableData = async () => {
  const params = {
    ...filterForm,
    pageNum: pagination.pageNum,     // ✅ 修正为pageNum
    pageSize: pagination.pageSize
  };
  
  const response = await getFactoryProspectList(params);
  tableData.value = response.result.records;
  pagination.total = response.result.total;
};
</script>
```

### 3.3 数据字典标准化实现

#### 3.3.1 字典类型定义
```typescript
// src/constants/dictionary.ts 中添加厂端潜客相关字典
export const DICTIONARY_TYPES = {
  // ... 现有字典类型
  STORE: '01010',                    // 门店
  INTENT_LEVEL: '01160',             // 意向级别
  PROSPECT_STATUS: '01380',          // 潜客状态
  STORE_COUNT_TYPE: '01390',         // 关联门店数类型
  VIEW_TYPE: '01400',                // 视图类型
  REGISTRATION_SOURCE: '01410'       // 注册来源
} as const;
```

#### 3.3.2 字典使用标准化
```vue
<template>
  <!-- 搜索表单字典化 -->
  <el-form :model="filterForm" label-position="top">
    <el-row :gutter="24">
      <el-col :span="6">
        <el-form-item label="门店">
          <el-select v-model="filterForm.storeId" clearable>
            <el-option
              v-for="option in storeOptions"
              :key="option.code"
              :label="option.name"
              :value="option.code"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="6">
        <el-form-item label="关联门店数">
          <el-select v-model="filterForm.storeCount" clearable>
            <el-option
              v-for="option in storeCountOptions"
              :key="option.code"
              :label="option.name"
              :value="option.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <!-- 视图类型字典化 -->
  <el-radio-group v-model="filterForm.viewType" @change="changeViewType">
    <el-radio-button
      v-for="option in viewTypeOptions"
      :key="option.code"
      :label="option.code"
    >
      {{ option.name }}
    </el-radio-button>
  </el-radio-group>

  <!-- 表格列字典化 -->
  <el-table :data="tableData">
    <el-table-column prop="currentIntentLevel" label="潜客级别">
      <template #default="{ row }">
        <el-tag :type="getLevelTagType(row.currentIntentLevel)">
          {{ getNameByCode(DICTIONARY_TYPES.INTENT_LEVEL, row.currentIntentLevel) }}
        </el-tag>
      </template>
    </el-table-column>

    <el-table-column prop="prospectStatus" label="潜客状态">
      <template #default="{ row }">
        <el-tag :type="getStatusTagType(row.prospectStatus)">
          {{ getNameByCode(DICTIONARY_TYPES.PROSPECT_STATUS, row.prospectStatus) }}
        </el-tag>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 批量获取字典数据
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS,
  DICTIONARY_TYPES.STORE_COUNT_TYPE,
  DICTIONARY_TYPES.VIEW_TYPE,
  DICTIONARY_TYPES.REGISTRATION_SOURCE
]);

// 计算属性获取字典选项
const storeOptions = computed(() => getOptions(DICTIONARY_TYPES.STORE));
const intentLevelOptions = computed(() => getOptions(DICTIONARY_TYPES.INTENT_LEVEL));
const prospectStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PROSPECT_STATUS));
const storeCountOptions = computed(() => getOptions(DICTIONARY_TYPES.STORE_COUNT_TYPE));
const viewTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.VIEW_TYPE));

// 标签类型映射函数
const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    '01160001': 'danger',   // H级
    '01160002': 'warning',  // A级
    '01160003': 'success',  // B级
    '01160004': 'info'      // C级
  };
  return typeMap[level] || 'info';
};

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01380001': 'info',     // 新建
    '01380002': 'warning',  // 跟进中
    '01380003': 'success',  // 已成交
    '01380004': 'danger'    // 无意向
  };
  return typeMap[status] || 'info';
};
</script>
```

### 3.4 API模块重构

#### 3.4.1 API模块实现
```typescript
// src/api/modules/sales/factoryProspect.ts
import request from '@/api';
import type {
  FactoryProspectSearchParams,
  FactoryProspectPageResponse,
  FactoryProspectStatistics,
  FactoryProspectDetail
} from '@/types/sales/factoryProspect';
import {
  getFactoryProspectList as getMockFactoryProspectList,
  getFactoryProspectStatistics as getMockFactoryProspectStatistics,
  getFactoryProspectDetail as getMockFactoryProspectDetail
} from '@/mock/data/sales/factoryProspect';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getFactoryProspectList = (params: FactoryProspectSearchParams): Promise<FactoryProspectPageResponse> => {
  if (USE_MOCK_API) {
    return getMockFactoryProspectList(params);
  } else {
    return request.post<any, FactoryProspectPageResponse>('/sales/factory-prospect/list', params);
  }
};

export const getFactoryProspectStatistics = (params: Partial<FactoryProspectSearchParams>): Promise<{ result: FactoryProspectStatistics }> => {
  if (USE_MOCK_API) {
    return getMockFactoryProspectStatistics(params);
  } else {
    return request.post<any, { result: FactoryProspectStatistics }>('/sales/factory-prospect/statistics', params);
  }
};

export const getFactoryProspectDetail = (globalCustomerId: string): Promise<{ result: FactoryProspectDetail }> => {
  if (USE_MOCK_API) {
    return getMockFactoryProspectDetail(globalCustomerId);
  } else {
    return request.get<any, { result: FactoryProspectDetail }>(`/sales/factory-prospect/detail/${globalCustomerId}`);
  }
};

export const exportFactoryProspectData = (params: FactoryProspectSearchParams): Promise<Blob> => {
  return request.post<any, Blob>('/sales/factory-prospect/export', params, { responseType: 'blob' });
};
```

### 3.5 国际化文件结构

#### 3.5.1 国际化文件结构
```json
// src/locales/modules/sales/zh.json
{
  "factoryProspect": {
    "title": "厂端潜客池管理",
    "statistics": {
      "totalLeadCount": "总线索数",
      "hLevelProspectCount": "H级潜客",
      "monthlyConversionProspectCount": "本月转化",
      "crossStoreCustomerCount": "跨门店潜客",
      "vsLastMonth": "较上月",
      "conversionRate": "转化率",
      "ratio": "占比"
    },
    "search": {
      "leadId": "潜客ID",
      "store": "门店",
      "storeCount": "关联门店数",
      "registrationTime": "注册时间",
      "search": "查询",
      "reset": "重置",
      "export": "导出数据"
    },
    "viewType": {
      "all": "全部潜客",
      "crossStore": "跨门店潜客",
      "defeated": "战败潜客",
      "converted": "已转化"
    },
    "table": {
      "index": "序号",
      "leadId": "潜客ID",
      "customerName": "潜客姓名",
      "phoneNumber": "手机号",
      "associatedStoreCount": "关联门店数",
      "currentIntentLevel": "潜客级别",
      "prospectStatus": "潜客状态",
      "actions": "操作",
      "detail": "详情"
    }
  }
}
```

#### 3.5.2 国际化引用更新
```typescript
// 更新国际化引用
const { t, tc } = useModuleI18n('sales.factoryProspect');
```

### 3.6 路由配置更新

```typescript
// src/router/modules/sales.ts
{
  path: '/sales/factory-prospect',
  name: 'SalesFactoryProspect',
  component: () => import('@/views/sales/factoryProspect/FactoryProspectView.vue'),
  meta: {
    title: 'menu.salesFactoryProspect',
    requiresAuth: true,
    icon: 'User'
  }
}
```

## 4. 实施计划

### 4.1 重构步骤

#### 4.1.1 第一阶段：目录结构迁移
1. 创建目标目录结构
2. 迁移主页面文件和组件
3. 更新路由配置
4. 更新组件引用路径

#### 4.1.2 第二阶段：MyBatisPlus分页标准化
1. 修正分页参数接口定义
2. 更新分页状态管理
3. 修正分页组件绑定
4. 测试分页功能

#### 4.1.3 第三阶段：数据字典标准化
1. 定义字典类型常量
2. 实现字典批量获取
3. 更新搜索表单字典化
4. 更新表格列字典化
5. 实现标签类型映射

#### 4.1.4 第四阶段：API模块重构
1. 创建API模块文件
2. 实现Mock数据
3. 更新API调用
4. 测试API功能

### 4.2 验收标准

#### 4.2.1 目录结构验证
- [ ] 页面文件移动到 `src/views/sales/factoryProspect/FactoryProspectView.vue`
- [ ] API模块创建在 `src/api/modules/sales/factoryProspect.ts`
- [ ] Mock数据创建在 `src/mock/data/sales/factoryProspect.ts`
- [ ] 类型定义创建在 `src/types/sales/factoryProspect.d.ts`
- [ ] 国际化文件更新在 `src/locales/modules/sales/`

#### 4.2.2 MyBatisPlus分页验证
- [ ] 分页参数使用 `pageNum` 和 `pageSize`
- [ ] 分页组件绑定 `pagination.pageNum`
- [ ] API响应使用 `response.result.records` 和 `response.result.total`
- [ ] 分页功能正常工作

#### 4.2.3 数据字典验证
- [ ] 所有下拉选项使用字典数据
- [ ] 表格列显示使用字典转义
- [ ] 标签类型映射正确
- [ ] 字典数据加载正常

#### 4.2.4 API响应处理验证
- [ ] 使用 `response.result` 获取数据
- [ ] 错误处理符合规范
- [ ] Mock数据和真实API切换正常

## 5. 类型定义文件

### 5.1 类型定义文件
```typescript
// src/types/sales/factoryProspect.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 厂端潜客搜索参数
export interface FactoryProspectSearchParams extends PageParams {
  leadId?: string;
  storeId?: string;
  storeCount?: string;
  registrationTimeStart?: string;
  registrationTimeEnd?: string;
  viewType?: 'all' | 'cross_store' | 'defeated' | 'converted';
}

// 厂端潜客列表项
export interface FactoryProspectListItem {
  globalCustomerId: string;
  leadId: string;
  customerName: string;
  phoneNumber: string;
  associatedStoreCount: number;
  currentIntentLevel: string;
  prospectStatus: string;
  registrationTime: string;
  lastFollowUpTime?: string;
  createTime: string;
  updateTime: string;
}

// 厂端潜客统计
export interface FactoryProspectStatistics {
  totalLeadCount: number;
  hLevelProspectCount: number;
  monthlyConversionProspectCount: number;
  crossStoreCustomerCount: number;
  totalLeadCountVsLastMonth: number;
  hLevelProspectCountVsLastMonth: number;
  monthlyConversionRate: string;
  crossStoreCustomerRatio: string;
}

// 厂端潜客详情
export interface FactoryProspectDetail extends FactoryProspectListItem {
  customerInfo: {
    name: string;
    phone: string;
    email?: string;
    idType: string;
    idNumber: string;
    registerTime: string;
    registerSource: string;
    currentStatus: string;
  };
  storeAssociations: {
    associatedStoreCount: number;
    storeRecords: StoreAssociationRecord[];
  };
  followUpRecords: FollowUpRecord[];
  testDriveRecords: TestDriveRecord[];
  defeatRecords: DefeatRecord[];
  changeHistory: ChangeHistoryRecord[];
}

// 门店关联记录
export interface StoreAssociationRecord {
  storeId: string;
  storeName: string;
  leadAssociationTime: string;
  associationReason: string;
  currentSalesAdvisor: string;
  currentIntentLevel: string;
  lastFollowUpTime: string;
}

// 跟进记录
export interface FollowUpRecord {
  id: string;
  storeName: string;
  salesAdvisor: string;
  followUpTime: string;
  followUpMethod: string;
  followUpContent: string;
  intentLevel: string;
  nextFollowUpTime: string;
}

// 试驾记录
export interface TestDriveRecord {
  id: string;
  storeName: string;
  salesAdvisor: string;
  testDriveTime: string;
  vehicleModel: string;
  duration: number;
  customerFeedback: string;
}

// 战败记录
export interface DefeatRecord {
  id: string;
  storeName: string;
  salesAdvisor: string;
  defeatTime: string;
  defeatReason: string;
  competingBrand: string;
  competingModel: string;
  approvalStatus: string;
}

// 变更历史记录
export interface ChangeHistoryRecord {
  id: string;
  changedTime: string;
  changedBy: string;
  changedField: string;
  oldValue: string;
  newValue: string;
  changeReason: string;
}

// 厂端潜客分页响应
export interface FactoryProspectPageResponse {
  result: PageResponse<FactoryProspectListItem>;
}
```

### 5.2 Mock数据实现
```typescript
// src/mock/data/sales/factoryProspect.ts
import type {
  FactoryProspectSearchParams,
  FactoryProspectPageResponse,
  FactoryProspectStatistics,
  FactoryProspectDetail
} from '@/types/sales/factoryProspect';

// Mock数据生成
const generateMockFactoryProspects = (count: number) => {
  const prospects = [];
  const stores = ['01010001', '01010002', '01010003', '01010004'];
  const intentLevels = ['01160001', '01160002', '01160003', '01160004'];
  const statuses = ['01380001', '01380002', '01380003', '01380004'];

  for (let i = 1; i <= count; i++) {
    prospects.push({
      globalCustomerId: `GC${String(i).padStart(6, '0')}`,
      leadId: `L${String(i).padStart(8, '0')}`,
      customerName: `客户${i}`,
      phoneNumber: `+60${Math.floor(Math.random() * 1000000000)}`,
      associatedStoreCount: Math.floor(Math.random() * 3) + 1,
      currentIntentLevel: intentLevels[Math.floor(Math.random() * intentLevels.length)],
      prospectStatus: statuses[Math.floor(Math.random() * statuses.length)],
      registrationTime: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
      lastFollowUpTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      createTime: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
      updateTime: new Date().toISOString()
    });
  }
  return prospects;
};

const mockProspects = generateMockFactoryProspects(100);

export const getFactoryProspectList = (params: FactoryProspectSearchParams): Promise<FactoryProspectPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockProspects];

      // 搜索过滤
      if (params.leadId) {
        filteredData = filteredData.filter(item =>
          item.leadId.toLowerCase().includes(params.leadId!.toLowerCase())
        );
      }
      if (params.storeId) {
        filteredData = filteredData.filter(item =>
          item.associatedStoreCount > 0 // 简化处理，实际应该根据门店关联记录过滤
        );
      }
      if (params.viewType && params.viewType !== 'all') {
        switch (params.viewType) {
          case 'cross_store':
            filteredData = filteredData.filter(item => item.associatedStoreCount > 1);
            break;
          case 'defeated':
            filteredData = filteredData.filter(item => item.prospectStatus === '01380004');
            break;
          case 'converted':
            filteredData = filteredData.filter(item => item.prospectStatus === '01380003');
            break;
        }
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        }
      });
    }, 500);
  });
};

export const getFactoryProspectStatistics = (params: Partial<FactoryProspectSearchParams>): Promise<{ result: FactoryProspectStatistics }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        result: {
          totalLeadCount: mockProspects.length,
          hLevelProspectCount: mockProspects.filter(p => p.currentIntentLevel === '01160001').length,
          monthlyConversionProspectCount: mockProspects.filter(p => p.prospectStatus === '01380003').length,
          crossStoreCustomerCount: mockProspects.filter(p => p.associatedStoreCount > 1).length,
          totalLeadCountVsLastMonth: Math.floor(Math.random() * 20) - 10,
          hLevelProspectCountVsLastMonth: Math.floor(Math.random() * 10) - 5,
          monthlyConversionRate: '15.6%',
          crossStoreCustomerRatio: '23.4%'
        }
      });
    }, 300);
  });
};

export const getFactoryProspectDetail = (globalCustomerId: string): Promise<{ result: FactoryProspectDetail }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const prospect = mockProspects.find(item => item.globalCustomerId === globalCustomerId);
      if (prospect) {
        resolve({
          result: {
            ...prospect,
            customerInfo: {
              name: prospect.customerName,
              phone: prospect.phoneNumber,
              email: `${prospect.customerName.toLowerCase()}@example.com`,
              idType: 'IC',
              idNumber: '123456-78-9012',
              registerTime: prospect.registrationTime,
              registerSource: '官网注册',
              currentStatus: prospect.prospectStatus
            },
            storeAssociations: {
              associatedStoreCount: prospect.associatedStoreCount,
              storeRecords: []
            },
            followUpRecords: [],
            testDriveRecords: [],
            defeatRecords: [],
            changeHistory: []
          } as FactoryProspectDetail
        });
      }
    }, 300);
  });
};
```

## 6. 规范符合性验证

### 6.1 页面目录结构规范符合性

#### 6.1.1 目录结构规范 ✅
- [x] **路由页面位置**: `src/views/sales/factoryProspect/FactoryProspectView.vue` - 符合 `src/views/模块名/功能名/功能名View.vue` 规范
- [x] **非路由页面位置**: `src/views/sales/factoryProspect/components/` - 符合非路由页面统一放在 `components/` 目录的规范
- [x] **API模块位置**: `src/api/modules/sales/factoryProspect.ts` - 符合模块化API结构
- [x] **类型定义位置**: `src/types/sales/factoryProspect.d.ts` - 符合模块化类型结构
- [x] **Mock数据位置**: `src/mock/data/sales/factoryProspect.ts` - 符合模块化Mock结构
- [x] **国际化位置**: `src/locales/modules/sales/` - 符合模块化国际化结构

#### 6.1.2 命名规范 ✅
- [x] **路由页面命名**: `FactoryProspectView.vue` - 符合 `功能名View.vue` 格式
- [x] **模块名称**: `sales` - 使用标准模块名称
- [x] **功能名称**: `factoryProspect` - 使用小驼峰命名法

#### 6.1.3 MyBatisPlus分页规范 ✅
- [x] **分页参数**: 使用 `pageNum` 和 `pageSize` 标准参数
- [x] **分页响应**: 使用 `records`、`total`、`pageNum`、`pageSize`、`pages` 标准结构
- [x] **分页组件**: 绑定 `pagination.pageNum` 而非 `pagination.page`

#### 6.1.4 数据字典规范 ✅
- [x] **字典使用**: 使用 `useBatchDictionary` 批量获取字典
- [x] **字典转义**: 使用 `getNameByCode` 进行字典值转义
- [x] **字典常量**: 使用 `DICTIONARY_TYPES` 常量定义字典类型

#### 6.1.5 API响应处理规范 ✅
- [x] **响应结构**: 使用 `response.result` 获取业务数据
- [x] **错误处理**: 通过API拦截器统一处理错误
- [x] **Mock切换**: 支持Mock数据和真实API的切换

### 6.2 规范遵循要点
1. **功能内聚**: 厂端潜客池相关的所有文件统一放在 `sales/factoryProspect/` 目录下
2. **清晰分类**: 路由页面直接放在功能目录下，组件放在 `components/` 子目录
3. **模块化组织**: API、Mock、类型定义、国际化文件都按相同的模块结构组织
4. **命名一致性**: 所有文件和目录命名都遵循统一的命名规范
5. **标准模块使用**: 使用项目标准的 `sales` 模块，而非自定义模块名
6. **分页标准化**: 严格遵循MyBatisPlus分页参数和响应结构
7. **字典标准化**: 统一使用数据字典进行选项展示和值转义
8. **API标准化**: 统一使用 `response.result` 模式处理API响应

---

**cc-fe**: 我已完成 `factory-prospect` 模块的重构设计文档。

**设计要点**:
- [x] 符合页面目录结构规范的模块化重构方案
- [x] MyBatisPlus分页组件标准化实现（pageNum/pageSize参数，修正当前的page/itemCount问题）
- [x] 参考ProspectsView.vue的数据字典实现方式（批量字典获取、标准转义）
- [x] API请求响应的通用处理方式（response.result模式）
- [x] 完整的类型定义和Mock数据实现
- [x] 详细的实施计划和验收标准

文档已提交，请审查。
