# 国际化模块化迁移指南

## 概述

本项目已将国际化文件从单一的大文件拆分为模块化的小文件，解决了多人协作时的文件冲突问题。

## 新架构的优势

1. **解决冲突**: 每个模块有独立的国际化文件，避免多人同时编辑同一文件
2. **按需加载**: 可以根据需要只加载特定模块的语言包
3. **维护性好**: 模块化组织，便于维护和查找
4. **类型安全**: 配合 TypeScript 可以提供更好的类型提示
5. **向后兼容**: 现有代码迁移成本低

## 目录结构

```
src/locales/
├── modules/
│   ├── common/           # 通用模块 (按钮、操作等)
│   │   ├── zh.json
│   │   └── en.json
│   ├── sales/           # 销售模块
│   │   ├── zh.json
│   │   └── en.json
│   ├── order/           # 订单模块
│   │   ├── zh.json
│   │   └── en.json
│   └── ...              # 其他业务模块
├── loader.ts            # 模块加载器
├── backup/              # 原文件备份
│   ├── zh-2023-12-01T10-30-00.json
│   └── en-2023-12-01T10-30-00.json
├── zh.json              # 原文件（保留作为备份）
└── en.json              # 原文件（保留作为备份）
```

## 使用方法

### 1. 在组件中使用模块化国际化

#### 旧方式
```vue
<template>
  <div>
    <h1>{{ $t('sales.vehicleList') }}</h1>
    <el-button>{{ $t('common.save') }}</el-button>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
</script>
```

#### 新方式
```vue
<template>
  <div>
    <h1>{{ t('vehicleList') }}</h1>
    <el-button>{{ tc('save') }}</el-button>
  </div>
</template>

<script setup>
import { useModuleI18n } from '@/composables/useModuleI18n'

// 使用销售模块的国际化
const { t, tc } = useModuleI18n('sales')
// t() 用于访问当前模块的翻译
// tc() 用于访问通用模块的翻译
</script>
```

### 2. 使用多模块国际化

```vue
<script setup>
import { useMultiModuleI18n } from '@/composables/useModuleI18n'

// 同时使用多个模块
const { sales, order, tc } = useMultiModuleI18n(['sales', 'order'])

// 使用方式
const title = sales('vehicleList')      // 销售模块
const orderNo = order('orderNumber')    // 订单模块
const saveText = tc('save')             // 通用模块
</script>
```

### 3. API 函数

#### `useModuleI18n(moduleName: string)`

返回对象包含：
- `t(key, params?)`: 当前模块的翻译函数
- `tc(key, params?)`: 通用模块的翻译函数  
- `tr(key, params?)`: 原始翻译函数
- `locale`: 当前语言
- 其他 i18n 功能

#### `useMultiModuleI18n(moduleNames: string[])`

返回对象包含：
- 每个模块名对应的翻译函数
- `tc(key, params?)`: 通用模块的翻译函数
- `locale`: 当前语言
- 其他 i18n 功能

## 迁移步骤

### 1. 自动迁移（推荐）

运行自动迁移脚本：
```bash
npm run split-locales
```

这会：
- 自动拆分现有国际化文件到各个模块
- 备份原始文件
- 生成模块索引
- 显示统计信息

### 2. 手动迁移现有组件

#### 步骤1: 导入新的组合式API
```javascript
// 替换
import { useI18n } from 'vue-i18n'

// 为
import { useModuleI18n } from '@/composables/useModuleI18n'
```

#### 步骤2: 使用模块化翻译函数
```javascript
// 替换
const { t } = useI18n()

// 为
const { t, tc } = useModuleI18n('你的模块名')
```

#### 步骤3: 更新翻译键
```javascript
// 替换
t('sales.vehicleList')
t('common.save')

// 为
t('vehicleList')    // 当前模块
tc('save')          // 通用模块
```

## 添加新模块

### 1. 创建模块文件

```bash
mkdir src/locales/modules/newModule
touch src/locales/modules/newModule/zh.json
touch src/locales/modules/newModule/en.json
```

### 2. 添加翻译内容

`src/locales/modules/newModule/zh.json`:
```json
{
  "title": "新模块标题",
  "description": "新模块描述"
}
```

`src/locales/modules/newModule/en.json`:
```json
{
  "title": "New Module Title", 
  "description": "New Module Description"
}
```

### 3. 更新模块索引

编辑 `src/locales/modules/index.ts`，添加新模块：
```typescript
export const moduleNames = [
  // ... 现有模块
  'newModule'
] as const
```

### 4. 在组件中使用

```vue
<script setup>
import { useModuleI18n } from '@/composables/useModuleI18n'
const { t, tc } = useModuleI18n('newModule')
</script>
```

## 最佳实践

### 1. 模块命名

- 使用 camelCase 命名：`salesOrder`, `partManagement`
- 名称应该清晰表达模块功能
- 避免过长的名称

### 2. 翻译键命名

- 使用有意义的键名：`vehicleList` 而不是 `list1`
- 保持键名简洁但描述性强
- 使用 camelCase

### 3. 模块划分原则

- 按业务功能划分：sales, order, parts 等
- 将通用内容放在 common 模块
- 避免模块间的循环依赖

### 4. 团队协作

- 每个开发人员负责自己模块的国际化文件
- 通用翻译的修改需要团队讨论
- 新增模块需要更新文档

## 常见问题

### Q: 如何访问其他模块的翻译？
A: 使用 `useMultiModuleI18n()` 或 `tr()` 函数：
```javascript
const { sales, order } = useMultiModuleI18n(['sales', 'order'])
// 或
const { tr } = useModuleI18n('currentModule')
const otherText = tr('sales.vehicleList')
```

### Q: 如何在非组件中使用？
A: 使用 `getModuleTranslator()` 函数：
```javascript
import { getModuleTranslator } from '@/composables/useModuleI18n'
import i18n from '@/plugins/i18n'

const t = getModuleTranslator('sales', i18n)
const text = t('vehicleList')
```

### Q: 模块文件丢失怎么办？
A: 系统会自动处理丢失的模块文件，返回空对象并在控制台显示警告。

### Q: 如何回退到旧版本？
A: 
1. 恢复备份的原始文件
2. 回退 `src/plugins/i18n.ts` 的修改
3. 回退 `src/main.ts` 的修改

## 脚本命令

```bash
# 拆分国际化文件为模块
npm run split-locales

# 合并模块文件回原始格式（如果需要）
npm run merge-locales
```

## 注意事项

1. **向下兼容**: 旧的 `$t()` 方法仍然可用，但建议逐步迁移
2. **性能**: 模块化加载可能会有轻微的性能开销，但在实际使用中影响很小
3. **类型安全**: 建议配合 TypeScript 使用以获得更好的开发体验
4. **备份**: 原始文件已自动备份，如有问题可以随时回退

## 技术支持

如有问题，请：
1. 查看本文档的常见问题部分
2. 查看示例组件 `src/components/I18nExample.vue`
3. 联系技术负责人 