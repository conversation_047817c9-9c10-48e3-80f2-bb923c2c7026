 # Vue.js 项目国际化处理规则文档

## 概述

本文档定义了Vue.js项目中使用模块化国际化的标准流程和规则，确保所有页面和组件的国际化处理保持一致性。

## 1. 基础架构

### 1.1 国际化Hook
使用 `useModuleI18n` 替代传统的 `useI18n`：

```typescript
// 错误方式
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

// 正确方式
import { useModuleI18n } from '@/composables/useModuleI18n'
const { t } = useModuleI18n('moduleName')        // 模块翻译
const { t: tc } = useModuleI18n('common')        // 通用翻译
```

### 1.2 翻译文件结构
```
src/locales/modules/
├── common/
│   ├── zh.json
│   └── en.json
├── [moduleName]/
│   ├── zh.json
│   └── en.json
```

## 2. 翻译键命名规范

### 2.1 基础字段
```json
{
  "title": "页面标题",
  "orderNumber": "订单编号",
  "buyerName": "购车人名称",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

### 2.2 表单相关
```json
{
  "enterOrderNumber": "请输入订单编号",
  "selectOrderStatus": "请选择订单状态",
  "selectDateRange": "选择日期范围",
  "searchPlaceholder": "请输入搜索关键词"
}
```

### 2.3 状态相关
```json
{
  "orderStatusSubmitted": "已提交",
  "orderStatusConfirmed": "已确认",
  "paymentStatusPending": "待支付",
  "paymentStatusPaid": "已支付"
}
```

### 2.4 操作相关
```json
{
  "addRecord": "添加记录",
  "editRecord": "编辑记录",
  "deleteRecord": "删除记录",
  "viewDetail": "查看详情"
}
```

### 2.5 验证消息
```json
{
  "fieldRequired": "此字段为必填项",
  "amountMustBePositive": "金额必须大于0",
  "remarkTooLong": "备注不能超过200个字符"
}
```

### 2.6 成功/错误消息
```json
{
  "addSuccess": "添加成功",
  "addFailed": "添加失败",
  "deleteSuccess": "删除成功",
  "deleteFailed": "删除失败",
  "confirmDelete": "确认删除此记录？"
}
```

## 3. 代码实现规范

### 3.1 Script Setup 部分

```vue
<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'

// 模块翻译和通用翻译
const { t } = useModuleI18n('payment')
const { t: tc } = useModuleI18n('common')

// 错误处理中使用通用翻译
try {
  // API调用
} catch (error: unknown) {
  ElMessage.error(tc('loadFailed'))
}

// 状态文本映射
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': t('orderStatusSubmitted'),
    '已确认': t('orderStatusConfirmed')
  }
  return statusMap[status] || status
}

// 表单验证规则
const formRules = {
  orderNumber: [
    { required: true, message: t('orderNumberRequired'), trigger: 'blur' }
  ],
  amount: [
    { required: true, message: t('amountRequired'), trigger: 'blur' },
    { type: 'number', min: 0.01, message: t('amountMustBePositive'), trigger: 'blur' }
  ]
}
</script>
```

### 3.2 Template 部分

```vue
<template>
  <!-- 页面标题 -->
  <h1 class="page-title">{{ t('title') }}</h1>
  
  <!-- 表单字段 -->
  <el-form-item :label="t('orderNumber')">
    <el-input 
      v-model="form.orderNumber" 
      :placeholder="t('enterOrderNumber')" 
    />
  </el-form-item>
  
  <!-- 下拉选择 -->
  <el-select :placeholder="t('selectOrderStatus')">
    <el-option :label="t('orderStatusSubmitted')" value="已提交" />
    <el-option :label="t('orderStatusConfirmed')" value="已确认" />
  </el-select>
  
  <!-- 按钮 -->
  <el-button type="primary" @click="handleSearch">
    {{ tc('search') }}
  </el-button>
  <el-button @click="handleReset">{{ tc('reset') }}</el-button>
  
  <!-- 表格列 -->
  <el-table-column :label="tc('index')" type="index" />
  <el-table-column :label="t('orderNumber')" prop="orderNumber" />
  <el-table-column :label="tc('operations')">
    <template #default="scope">
      <el-button @click="handleEdit(scope.row)">{{ tc('edit') }}</el-button>
      <el-button @click="handleDelete(scope.row)">{{ tc('delete') }}</el-button>
    </template>
  </el-table-column>
  
  <!-- 状态标签 -->
  <el-tag>{{ getOrderStatusText(row.status) }}</el-tag>
</template>
```

## 4. 翻译文件管理规范

### 4.1 添加新翻译键的原则
1. **不允许删除现有键** - 其他文件可能正在使用
2. **只能新增或修改值** - 保持向后兼容性
3. **中英文同步更新** - 确保两种语言的键完全一致

### 4.2 通用翻译键 (common模块)
```json
{
  "search": "搜索",
  "reset": "重置",
  "confirm": "确认",
  "cancel": "取消",
  "save": "保存",
  "edit": "编辑",
  "delete": "删除",
  "add": "添加",
  "view": "查看",
  "close": "关闭",
  "yes": "是",
  "no": "否",
  "index": "序号",
  "operations": "操作",
  "remark": "备注",
  "startDate": "开始日期",
  "endDate": "结束日期",
  "loadFailed": "加载失败",
  "saveFailed": "保存失败",
  "copied": "已复制",
  "copyFailed": "复制失败"
}
```

## 5. Cursor AI 处理指令模板

当需要对页面进行国际化处理时，请使用以下指令：

```
请对 [文件路径] 进行国际化处理，使用 useModuleI18n 替代 useI18n：

1. 将 useI18n 替换为 useModuleI18n('[模块名]')
2. 添加通用翻译：const { t: tc } = useModuleI18n('common')
3. 更新所有模板中的翻译调用，移除模块前缀
4. 更新脚本中的翻译调用
5. 不允许删除 en.json、zh.json 的 key，只能新增或修改值
6. 确保中英文翻译文件同步更新

翻译键命名规范：
- 基础字段：orderNumber, buyerName, createTime
- 表单提示：enterXxx, selectXxx, xxxRequired
- 状态：xxxStatusPending, xxxStatusConfirmed
- 操作：addXxx, editXxx, deleteXxx
- 消息：xxxSuccess, xxxFailed, confirmXxx
```

## 6. 文件处理检查清单

### 6.1 新建页面时
- [ ] 创建对应的翻译文件 (`zh.json`, `en.json`)
- [ ] 使用 `useModuleI18n` 导入翻译函数
- [ ] 模板中所有硬编码文本替换为翻译调用
- [ ] 表单验证消息国际化
- [ ] 错误处理消息国际化

### 6.2 修改现有页面时
- [ ] 检查是否已使用 `useModuleI18n`
- [ ] 新增翻译键到对应的翻译文件
- [ ] 确保中英文翻译键同步
- [ ] 不删除现有翻译键
- [ ] 测试语言切换功能

## 7. 常见模式示例

### 7.1 对话框国际化
```vue
<template>
  <el-dialog :title="t('dialogTitle')" v-model="visible">
    <el-form :model="form" :rules="rules">
      <el-form-item :label="t('fieldLabel')" prop="fieldName">
        <el-input v-model="form.fieldName" :placeholder="t('fieldPlaceholder')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">{{ tc('cancel') }}</el-button>
      <el-button type="primary" @click="handleSubmit">{{ tc('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>
```

### 7.2 确认对话框国际化
```typescript
const handleDelete = async (record: any) => {
  try {
    await ElMessageBox.confirm(
      t('confirmDeleteMessage'),
      tc('confirm'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    )
    ElMessage.success(t('deleteSuccess'))
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(t('deleteFailed'))
    }
  }
}
```

### 7.3 表单验证国际化
```typescript
const rules = {
  orderNumber: [
    { required: true, message: t('orderNumberRequired'), trigger: 'blur' }
  ],
  amount: [
    { required: true, message: t('amountRequired'), trigger: 'blur' },
    { type: 'number', min: 0.01, message: t('amountMustBePositive'), trigger: 'blur' }
  ]
}
```

---

本文档应作为项目国际化的标准参考，所有开发人员和 AI 助手都应严格遵循这些规范。