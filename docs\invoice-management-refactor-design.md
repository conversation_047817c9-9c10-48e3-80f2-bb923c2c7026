# 发票管理页面重构设计文档

## 1. 重构概述

### 1.1 重构目标
将 `InvoiceManagementView.vue` 从 `src/views/customerOrderManagement/` 重构到符合项目规范的模块化结构，实现：
- 符合页面目录结构规范的模块化组织
- 标准化MyBatisPlus分页组件使用
- 规范化数据字典实现
- 统一API响应处理方式

### 1.2 当前问题分析

#### 1.2.1 目录结构问题
```
❌ 当前结构:
src/views/customerOrderManagement/InvoiceManagementView.vue

✅ 目标结构:
src/views/sales/invoiceManagement/
├── InvoiceManagementView.vue             # 主页面文件（路由页面）
└── components/
    ├── InvoiceDetailDialog.vue           # 发票详情对话框（非路由页面）
    ├── EmailConfirmDialog.vue            # 邮件确认对话框
    ├── ExportConfigDialog.vue            # 导出配置对话框
    ├── OperationLogDialog.vue            # 操作日志对话框
    ├── InvoiceDetailSection.vue          # 详情区块组件
    └── InvoiceReceiptTable.vue           # 收据明细表格
src/api/modules/sales/
└── invoiceManagement.ts                 # API模块
src/types/sales/
└── invoiceManagement.d.ts               # 类型定义
src/mock/data/sales/
└── invoiceManagement.ts                 # Mock数据
```

#### 1.2.2 MyBatisPlus分页问题分析
**当前实现分析**:
```typescript
// ✅ 分页参数已符合MyBatisPlus标准
const pagination = reactive({
  pageNum: 1,           // ✅ 正确使用 pageNum
  pageSize: 20,         // ✅ 正确
  total: 0              // ✅ 正确
});

// ✅ 分页组件绑定正确
<el-pagination
  v-model:current-page="pagination.pageNum"    // ✅ 正确绑定 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
/>

// ✅ API调用参数正确
const params = {
  ...searchParams,
  pageNum: pagination.pageNum,      // ✅ 正确使用 pageNum
  pageSize: pagination.pageSize
};

// ❌ 响应处理需要标准化
const response: PaginationResponse<Invoice> = await getInvoiceList(params);
tableData.value = response.records;        // ❌ 应为 response.result.records
pagination.total = response.total;         // ❌ 应为 response.result.total
```

**目标实现**:
```typescript
// ✅ 标准MyBatisPlus分页响应处理
const response = await getInvoiceList(params);
if (response.code === '200' || response.code === 200) {
  tableData.value = response.result.records || [];    // ✅ 标准响应结构
  pagination.total = response.result.total || 0;      // ✅ 标准响应结构
  pagination.pageNum = response.result.pageNum || pagination.pageNum;
} else {
  throw new Error(response.message || '获取数据失败');
}
```

#### 1.2.3 数据字典问题分析
**当前实现问题**:
```typescript
// ❌ 只使用了单个字典，缺少批量字典使用
const {
  options: salesTypeOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.SALES_TYPE);

// ❌ 缺少其他字典类型的使用
// 如：支付方式、发票状态、门店、销售顾问等

// ❌ 表格中缺少字典转义显示
<el-table-column :label="t('paymentMethod')" prop="paymentMethod" min-width="90" />
// 应该使用字典转义显示
```

**目标实现**:
```typescript
// ✅ 使用批量字典获取
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.SALES_TYPE,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.INVOICE_STATUS,
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.SALES_CONSULTANT,
  DICTIONARY_TYPES.FINANCE_COMPANY
]);

// ✅ 计算属性获取选项
const salesTypeOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.SALES_TYPE)
);
const paymentMethodOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.PAYMENT_METHOD)
);
const storeOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.STORE)
);

// ✅ 标准字典转义
const formatPaymentMethod = (method: string) => 
  getNameByCode(DICTIONARY_TYPES.PAYMENT_METHOD, method) || method;

const formatSalesType = (type: string) => 
  getNameByCode(DICTIONARY_TYPES.SALES_TYPE, type) || type;

// ✅ 表格中使用字典转义
<el-table-column :label="t('paymentMethod')" prop="paymentMethod" min-width="90">
  <template #default="{ row }">
    {{ formatPaymentMethod(row.paymentMethod) }}
  </template>
</el-table-column>
```

#### 1.2.4 API响应处理问题分析
**当前实现问题**:
```typescript
// ❌ 响应处理不符合标准
const response: PaginationResponse<Invoice> = await getInvoiceList(params);
tableData.value = response.records;        // ❌ 直接使用 records
pagination.total = response.total;         // ❌ 直接使用 total

// ❌ 缺少标准错误处理
// ❌ 缺少响应码判断
```

**目标实现**:
```typescript
// ✅ 标准API响应处理模式
try {
  const response = await getInvoiceList(params);
  
  // 标准响应结构: { code, message, result: { records, total, pageNum, pageSize, pages } }
  if (response.code === '200' || response.code === 200) {
    tableData.value = response.result.records || [];
    pagination.total = response.result.total || 0;
    pagination.pageNum = response.result.pageNum || pagination.pageNum;
  } else {
    throw new Error(response.message || '获取数据失败');
  }
} catch (error) {
  console.error('获取发票列表失败:', error);
  ElMessage.error(tc('loadFailed'));
  tableData.value = [];
  pagination.total = 0;
}
```

## 2. 重构实施方案

### 2.1 目录结构重构

#### 2.1.1 创建新的目录结构
```bash
# 创建页面目录
mkdir -p src/views/sales/invoiceManagement/components

# 创建API模块目录
mkdir -p src/api/modules/sales

# 创建类型定义目录
mkdir -p src/types/sales

# 创建Mock数据目录
mkdir -p src/mock/data/sales
```

#### 2.1.2 文件迁移计划
1. **主页面文件**: `InvoiceManagementView.vue` → `src/views/sales/invoiceManagement/InvoiceManagementView.vue`
2. **弹窗组件拆分**: 将四个弹窗拆分为独立组件放入 `components/` 目录
3. **API模块**: 重构 `src/api/modules/invoice.ts` → `src/api/modules/sales/invoiceManagement.ts`
4. **类型定义**: 重构 `src/types/invoice.d.ts` → `src/types/sales/invoiceManagement.d.ts`
5. **Mock数据**: 创建 `src/mock/data/sales/invoiceManagement.ts`

### 2.2 MyBatisPlus分页标准化

#### 2.2.1 分页参数标准化
```typescript
// src/types/sales/invoiceManagement.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 发票搜索参数（继承分页参数）
export interface InvoiceSearchParams extends PageParams {
  invoiceNumber?: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  orderNumber?: string;
  vin?: string;
  salesType?: string;
  salesStore?: string;
  salesConsultant?: string;
  invoiceDateStart?: string;
  invoiceDateEnd?: string;
}
```

#### 2.2.2 分页组件标准化
```vue
<!-- 标准MyBatisPlus分页组件 -->
<div class="pagination-container">
  <el-pagination
    v-model:current-page="pagination.pageNum"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 50, 100]"
    :total="pagination.total"
    layout="total, sizes, prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</div>
```

#### 2.2.3 分页处理函数标准化
```typescript
// 分页处理（保持MyBatisPlus标准）
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // ✅ 使用pageNum
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // ✅ 使用pageNum
  loadData();
};

// API调用参数构建
const buildSearchParams = (): InvoiceSearchParams => {
  const params: InvoiceSearchParams = {
    pageNum: pagination.pageNum,      // ✅ 使用标准参数名
    pageSize: pagination.pageSize,
    ...searchParams
  };

  // 处理时间范围
  if (dateRange.value) {
    params.invoiceDateStart = dateRange.value[0];
    params.invoiceDateEnd = dateRange.value[1];
  }

  return filterEmptyParams(params);
};
```

### 2.3 数据字典标准化

#### 2.3.1 字典类型定义
```typescript
// src/constants/dictionary.ts 中添加
export const DICTIONARY_TYPES = {
  // ... 现有字典类型
  SALES_TYPE: '0118',                     // 销售类型
  PAYMENT_METHOD: '0142',                 // 付款方式
  INVOICE_STATUS: '0148',                 // 发票状态
  STORE: '0129',                          // 门店
  SALES_CONSULTANT: '0130',               // 销售顾问
  FINANCE_COMPANY: '0149',                // 金融公司
  OPERATION_TYPE: '0150',                 // 操作类型
} as const;
```

#### 2.3.2 字典使用标准化
```typescript
// 使用批量字典获取
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.SALES_TYPE,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.INVOICE_STATUS,
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.SALES_CONSULTANT,
  DICTIONARY_TYPES.FINANCE_COMPANY,
  DICTIONARY_TYPES.OPERATION_TYPE
]);

// 计算属性获取选项
const salesTypeOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.SALES_TYPE)
);
const paymentMethodOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.PAYMENT_METHOD)
);
const storeOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.STORE)
);
const salesConsultantOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.SALES_CONSULTANT)
);

// 标准转义函数
const formatSalesType = (type: string) => 
  getNameByCode(DICTIONARY_TYPES.SALES_TYPE, type) || type;

const formatPaymentMethod = (method: string) => 
  getNameByCode(DICTIONARY_TYPES.PAYMENT_METHOD, method) || method;

const formatOperationType = (type: string) => 
  getNameByCode(DICTIONARY_TYPES.OPERATION_TYPE, type) || type;
```

#### 2.3.3 门店和销售顾问级联处理
```typescript
// 门店变化处理（保持现有逻辑）
const handleStoreChange = (storeCode: string) => {
  searchParams.salesConsultant = '';
  
  // 根据门店筛选销售顾问
  const filteredConsultants = computed(() => {
    if (!storeCode) return salesConsultantOptions.value;
    
    return salesConsultantOptions.value.filter(consultant => 
      consultant.storeCode === storeCode
    );
  });
  
  consultantOptions.value = filteredConsultants.value;
};
```

### 2.4 API响应处理标准化

#### 2.4.1 API函数标准化
```typescript
// src/api/modules/sales/invoiceManagement.ts

import request from '@/api';
import type { 
  InvoiceSearchParams, 
  InvoicePageResponse,
  InvoiceDetail,
  InvoiceOperationLog,
  ApiResponse
} from '@/types/sales/invoiceManagement';

export const getInvoiceList = (
  params: InvoiceSearchParams
): Promise<ApiResponse<InvoicePageResponse>> => {
  if (USE_MOCK_API) {
    return getInvoiceListMock(params);
  } else {
    return request.get<any, ApiResponse<InvoicePageResponse>>(
      '/sales/invoices/list', 
      { params }
    );
  }
};

export const getInvoiceDetail = (
  id: string
): Promise<ApiResponse<InvoiceDetail>> => {
  if (USE_MOCK_API) {
    return getInvoiceDetailMock(id);
  } else {
    return request.get<any, ApiResponse<InvoiceDetail>>(
      `/sales/invoices/detail/${id}`
    );
  }
};
```

#### 2.4.2 响应处理标准化
```typescript
// 标准API调用和响应处理
const loadData = async () => {
  try {
    loading.value = true;
    
    const params = buildSearchParams();
    const response = await getInvoiceList(params);
    
    // 标准响应处理
    if (response.code === '200' || response.code === 200) {
      tableData.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pageNum = response.result.pageNum || pagination.pageNum;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取发票列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};
```

## 3. Mock数据设计

### 3.1 Mock数据结构
```typescript
// src/mock/data/sales/invoiceManagement.ts

import type { 
  InvoiceSearchParams, 
  InvoicePageResponse,
  InvoiceItem 
} from '@/types/sales/invoiceManagement';

// 动态生成模拟数据（30-35条，便于测试分页）
function generateMockData(): InvoiceItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 30;
  const mockData: InvoiceItem[] = [];
  
  const salesTypes = ['retail', 'fleet', 'government'];
  const paymentMethods = ['cash', 'loan', 'lease'];
  const invoiceStatuses = ['draft', 'issued', 'sent', 'paid'];
  const vehicleModels = ['MYVI 1.5L', 'ALZA 1.5L', 'AXIA 1.0L', 'BEZZA 1.3L'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];
  const stores = ['KL001', 'PJ002', 'SB003', 'JB004'];
  const consultants = ['张三', '李四', '王五', '赵六'];
  
  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      id: `INV${String(i + 1).padStart(6, '0')}`,
      invoiceNumber: `522102-${String(Math.floor(Math.random() * 900000) + 100000)}`,
      invoiceDate: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      orderNumber: `ORD${String(i + 1).padStart(6, '0')}`,
      customerName: `客户${i + 1}`,
      customerPhone: `1${String(Math.floor(Math.random() * 900000000) + 100000000)}`,
      customerEmail: `customer${i + 1}@example.com`,
      customerAddress: `地址${i + 1}号`,
      vin: `WVWZZZ1JZ3W${String(Math.floor(Math.random() * 900000) + 100000)}`,
      model: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      variant: 'Standard',
      color: colors[Math.floor(Math.random() * colors.length)],
      salesStore: stores[Math.floor(Math.random() * stores.length)],
      salesConsultant: consultants[Math.floor(Math.random() * consultants.length)],
      paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
      financeCompany: Math.random() > 0.5 ? `金融公司${Math.floor(Math.random() * 5) + 1}` : '',
      loanAmount: Math.random() > 0.5 ? Math.floor(Math.random() * 50000) + 10000 : 0,
      invoiceAmount: Math.floor(Math.random() * 80000) + 20000,
      createdTime: new Date(Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000).toISOString(),
      status: invoiceStatuses[Math.floor(Math.random() * invoiceStatuses.length)]
    });
  }
  
  return mockData;
}

const mockData = generateMockData();

export const getInvoiceListMock = (
  params: InvoiceSearchParams
): Promise<ApiResponse<InvoicePageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];
      
      if (params.invoiceNumber) {
        filteredData = filteredData.filter(item =>
          item.invoiceNumber.toLowerCase().includes(params.invoiceNumber!.toLowerCase())
        );
      }
      
      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.includes(params.customerName!)
        );
      }
      
      if (params.salesType) {
        filteredData = filteredData.filter(item =>
          item.salesType === params.salesType
        );
      }
      
      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        code: '200',
        message: '获取成功',
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        },
        timestamp: Date.now()
      });
    }, 500);
  });
};
```

## 4. 类型定义文件

### 4.1 完整类型定义
```typescript
// src/types/sales/invoiceManagement.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 发票基本信息接口
export interface InvoiceItem {
  id: string;
  invoiceNumber: string;        // 发票编号 522102-XXXXXXX
  invoiceDate: string;          // 开票日期
  orderNumber: string;          // 订单编号
  customerName: string;         // 购车人姓名
  customerPhone: string;        // 购车人手机号
  customerEmail: string;        // 购车人邮箱
  customerAddress: string;      // 购车人地址
  customerState?: string;       // 州
  customerCity?: string;        // 城市
  customerPostcode?: string;    // 邮编
  vin: string;                  // VIN码
  model: string;                // 车型
  variant: string;              // 变型
  color: string;                // 颜色
  salesStore: string;           // 销售门店
  salesConsultant: string;      // 销售顾问
  paymentMethod: string;        // 付款方式 (现金/贷款)
  financeCompany?: string;      // 金融公司
  loanAmount: number;           // 贷款金额
  invoiceAmount: number;        // 发票总额
  createdTime: string;          // 创建时间
  status: string;               // 发票状态
  salesType?: string;           // 销售类型
}

// 发票详细信息接口
export interface InvoiceDetail extends InvoiceItem {
  // 发票基本信息
  invoiceCompany: string;       // 开票公司名称
  companyAddress: string;       // 公司地址
  gstNumber: string;            // GST编号
  sstNumber: string;            // SST编号
  contactPhone: string;         // 联系电话
  contactEmail: string;         // 联系邮箱

  // 客户详细信息
  deliveryNumber: string;       // 交车单编号
  salesConsultantId: string;    // 销售顾问ID

  // 车辆详细信息
  tinNumber: string;            // TIN编号
  modelCode: string;            // 车型代码
  modelDescription: string;     // 车型描述
  engineNumber: string;         // 发动机号
  chassisNumber: string;        // 底盘号
  engineDisplacement: string;   // 发动机排量
  vehicleRegistrationDate: string; // 车辆登记日期
  creator: string;              // 创建人
  updater: string;              // 更新人
  updateTime: string;           // 更新时间

  // 金融信息详情
  financeType: string;          // 金融方式
  loanPeriod: string;           // 贷款期限

  // 保险信息详情
  insuranceCompany: string;     // 保险公司
  agentCode: string;            // 代理编码
  policyNumber: string;         // 保单号
  issueDate: string;            // 出单日期
  insuranceAmount: number;      // 保险费用

  // 价格结构明细
  vehicleSalesPrice: number;    // 车辆销售价
  licensePlateFee: number;      // 车牌费用
  accessories: AccessoryItem[]; // 选配件明细
  accessoryAmount: number;      // 选配件总金额
  otrFees: OTRFeeItem[];       // OTR费用明细
  otrAmount: number;           // OTR费用总金额
  adjustmentAmount: number;     // 调整金额
  invoiceNetValue: number;      // 发票净值

  // 收据明细信息
  receipts: ReceiptItem[];      // 收据明细
}

// 选配件明细
export interface AccessoryItem {
  specification: string;       // 规格分类
  accessoryName: string;       // 配件名称
  unitPrice: number;           // 单价
  quantity: number;            // 数量
  amount: number;              // 总价
}

// OTR费用明细
export interface OTRFeeItem {
  feeCode: string;             // 费用编码
  feeType: string;             // 费用类型
  taxAmount: number;           // 费用金额
  effectiveDate: string;       // 生效日期
  expiryDate: string;          // 失效日期
}

// 收据明细
export interface ReceiptItem {
  receiptNumber: string;       // 收据编号
  receiptType: string;         // 业务类型
  receiptNo: string;           // 流水号
  paymentChannel: string;      // 渠道
  paidAmount: number;          // 金额
  collectionType: string;      // 收款类型
  arrivalTime: string;         // 到账时间
  remarks: string;             // 备注
}

// 发票搜索参数
export interface InvoiceSearchParams extends PageParams {
  invoiceNumber?: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  orderNumber?: string;
  vin?: string;
  salesType?: string;
  salesStore?: string;
  salesConsultant?: string;
  invoiceDateStart?: string;
  invoiceDateEnd?: string;
}

// 发票分页响应
export interface InvoicePageResponse extends PageResponse<InvoiceItem> {}

// 操作日志接口
export interface InvoiceOperationLog {
  id: string;
  operationType: string;        // 操作类型
  operator: string;             // 操作人
  operationTime: string;        // 操作时间
  operationDescription: string; // 操作描述
  operationResult: string;      // 操作结果
  errorMessage?: string;        // 错误信息
}

// 导出配置
export interface ExportConfig {
  format: 'excel' | 'pdf' | 'csv';
  scope: 'current' | 'all' | 'filtered';
  searchParams?: InvoiceSearchParams;
}

// API响应类型
export interface ApiResponse<T> {
  code: string | number;
  message: string;
  result: T;
  timestamp: number;
}

// 门店选项
export interface StoreOption {
  code: string;
  dealerName: string;
}

// 销售顾问选项
export interface ConsultantOption {
  code: string;
  name: string;
  storeCode?: string;
}
```

## 5. 组件拆分设计

### 5.1 发票详情弹窗组件
```vue
<!-- src/views/sales/invoiceManagement/components/InvoiceDetailDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    :title="t('invoiceDetail')"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <p>{{ tc('loading') }}</p>
    </div>

    <div v-else-if="detailData" class="invoice-detail">
      <!-- 基本信息 -->
      <InvoiceDetailSection
        :title="t('basicInfo')"
        icon="el-icon-document"
      >
        <InfoGrid :data="basicInfoData" />
      </InvoiceDetailSection>

      <!-- 客户信息 -->
      <InvoiceDetailSection
        :title="t('customerInfo')"
        icon="el-icon-user"
      >
        <InfoGrid :data="customerInfoData" />
      </InvoiceDetailSection>

      <!-- 车辆信息 -->
      <InvoiceDetailSection
        :title="t('vehicleInfo')"
        icon="el-icon-truck"
      >
        <InfoGrid :data="vehicleInfoData" />
      </InvoiceDetailSection>

      <!-- 金融信息 -->
      <InvoiceDetailSection
        :title="t('financeInfo')"
        icon="el-icon-money"
      >
        <InfoGrid :data="financeInfoData" />
      </InvoiceDetailSection>

      <!-- 保险信息 -->
      <InvoiceDetailSection
        :title="t('insuranceInfo')"
        icon="el-icon-shield"
      >
        <InfoGrid :data="insuranceInfoData" />
      </InvoiceDetailSection>

      <!-- 价格结构明细 -->
      <InvoiceDetailSection
        :title="t('priceStructureDetails')"
        icon="el-icon-price-tag"
      >
        <!-- 选配件明细 -->
        <div class="accessories-section">
          <h4>{{ t('optionalAccessories') }}</h4>
          <el-table :data="detailData.accessories || []" border>
            <el-table-column :label="t('category')" prop="specification" min-width="100" />
            <el-table-column :label="t('accessoryName')" prop="accessoryName" min-width="150" />
            <el-table-column :label="t('unitPrice')" prop="unitPrice" min-width="100" align="right">
              <template #default="{ row }">RM {{ formatAmount(row.unitPrice) }}</template>
            </el-table-column>
            <el-table-column :label="t('quantity')" prop="quantity" min-width="80" align="center" />
            <el-table-column :label="t('totalPrice')" prop="amount" min-width="120" align="right">
              <template #default="{ row }">RM {{ formatAmount(row.amount) }}</template>
            </el-table-column>
          </el-table>
        </div>

        <!-- OTR费用明细 -->
        <div class="otr-fees-section">
          <h4>{{ t('otrRegistrationFees') }}</h4>
          <el-table :data="detailData.otrFees || []" border>
            <el-table-column :label="t('billNumber')" prop="feeCode" min-width="120" />
            <el-table-column :label="t('feeItem')" prop="feeType" min-width="150" />
            <el-table-column :label="t('feePrice')" prop="taxAmount" min-width="100" align="right">
              <template #default="{ row }">RM {{ formatAmount(row.taxAmount) }}</template>
            </el-table-column>
            <el-table-column :label="t('effectiveDate')" prop="effectiveDate" min-width="120" />
            <el-table-column :label="t('expiryDate')" prop="expiryDate" min-width="120" />
          </el-table>
        </div>

        <!-- 价格汇总 -->
        <div class="price-summary">
          <div class="summary-item">
            <label>{{ t('totalAccessoryAmount') }}:</label>
            <span>RM {{ formatAmount(detailData.accessoryAmount) }}</span>
          </div>
          <div class="summary-item">
            <label>{{ t('totalOtrFeeAmount') }}:</label>
            <span>RM {{ formatAmount(detailData.otrAmount) }}</span>
          </div>
          <div class="summary-item">
            <label>{{ t('insurancePremium') }}:</label>
            <span>RM {{ formatAmount(detailData.insuranceAmount) }}</span>
          </div>
          <div class="summary-item total">
            <label>{{ t('invoiceNetValue') }}:</label>
            <span>RM {{ formatAmount(detailData.invoiceNetValue) }}</span>
          </div>
        </div>
      </InvoiceDetailSection>

      <!-- 收据明细 -->
      <InvoiceDetailSection
        :title="t('receiptDetails')"
        icon="el-icon-tickets"
      >
        <InvoiceReceiptTable :receipts="detailData.receipts || []" />
      </InvoiceDetailSection>
    </div>

    <template #footer>
      <el-button @click="handleClose">{{ tc('close') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getInvoiceDetail } from '@/api/modules/sales/invoiceManagement';
import type { InvoiceDetail } from '@/types/sales/invoiceManagement';
import InvoiceDetailSection from './InvoiceDetailSection.vue';
import InfoGrid from './InfoGrid.vue';
import InvoiceReceiptTable from './InvoiceReceiptTable.vue';

interface Props {
  visible: boolean;
  invoiceId?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

const loading = ref(false);
const detailData = ref<InvoiceDetail | null>(null);

// 计算属性
const basicInfoData = computed(() => {
  if (!detailData.value) return [];
  return [
    { label: t('invoiceNumber'), value: detailData.value.invoiceNumber },
    { label: t('invoiceDate'), value: detailData.value.invoiceDate },
    { label: t('companyInfo'), value: detailData.value.invoiceCompany },
    { label: t('gstNumber'), value: detailData.value.gstNumber },
    { label: t('sstNumber'), value: detailData.value.sstNumber },
    { label: t('contactPhone'), value: detailData.value.contactPhone },
    { label: t('contactEmail'), value: detailData.value.contactEmail },
    { label: t('paymentMethod'), value: detailData.value.paymentMethod }
  ];
});

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.invoiceId) {
    loadDetailData();
  } else {
    detailData.value = null;
  }
});

// 加载详情数据
const loadDetailData = async () => {
  if (!props.invoiceId) return;

  try {
    loading.value = true;
    const response = await getInvoiceDetail(props.invoiceId);

    if (response.code === '200' || response.code === 200) {
      detailData.value = response.result;
    } else {
      throw new Error(response.message || '获取详情失败');
    }
  } catch (error) {
    console.error('获取发票详情失败:', error);
    ElMessage.error(tc('loadFailed'));
    handleClose();
  } finally {
    loading.value = false;
  }
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
};

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};
</script>
```

### 5.2 邮件确认弹窗组件
```vue
<!-- src/views/sales/invoiceManagement/components/EmailConfirmDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    :title="t('emailConfirm')"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="invoiceData" class="email-confirm">
      <div class="confirm-info">
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="confirm-item">
              <label>{{ t('invoiceNumber') }}:</label>
              <span>{{ invoiceData.invoiceNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="confirm-item">
              <label>{{ t('orderNumber') }}:</label>
              <span>{{ invoiceData.orderNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="confirm-item">
              <label>{{ t('customerName') }}:</label>
              <span>{{ invoiceData.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="confirm-item">
              <label>{{ t('customerEmail') }}:</label>
              <span>{{ invoiceData.customerEmail }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="confirm-item">
              <label>{{ t('invoiceAmount') }}:</label>
              <span class="amount">RM {{ formatAmount(invoiceData.invoiceAmount) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="confirm-message">
        <el-icon><InfoFilled /></el-icon>
        {{ t('confirmSendEmail') }}
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          {{ tc('confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InvoiceItem } from '@/types/sales/invoiceManagement';

interface Props {
  visible: boolean;
  invoiceData?: InvoiceItem | null;
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = () => {
  emit('confirm');
};

const formatAmount = (amount: number): string => {
  return amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};
</script>
```

### 5.3 导出配置弹窗组件
```vue
<!-- src/views/sales/invoiceManagement/components/ExportConfigDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    :title="t('exportConfig')"
    width="450px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form :model="exportForm" label-position="top">
      <el-form-item :label="t('exportFormat')">
        <el-radio-group v-model="exportForm.format">
          <el-radio label="excel">Excel</el-radio>
          <el-radio label="pdf">PDF</el-radio>
          <el-radio label="csv">CSV</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="t('exportScope')">
        <el-radio-group v-model="exportForm.scope">
          <el-radio label="current">{{ t('currentPage') }}</el-radio>
          <el-radio label="all">{{ t('allData') }}</el-radio>
          <el-radio label="filtered">{{ t('filteredData') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          {{ tc('confirmExport') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { ExportConfig } from '@/types/sales/invoiceManagement';

interface Props {
  visible: boolean;
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', config: ExportConfig): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

const exportForm = reactive<ExportConfig>({
  format: 'excel',
  scope: 'current'
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = () => {
  emit('confirm', { ...exportForm });
};
</script>
```

## 6. 路由配置更新

### 6.1 路由路径调整
```typescript
// src/router/modules/sales.ts

{
  path: '/sales/invoice-management',
  name: 'InvoiceManagement',
  component: () => import('@/views/sales/invoiceManagement/InvoiceManagementView.vue'),
  meta: {
    title: 'menu.invoiceManagement',
    requiresAuth: true,
    icon: 'Receipt',
    roles: ['invoice_manager', 'sales_manager', 'finance_manager']
  }
}
```

### 6.2 菜单配置更新
```json
// src/locales/modules/common/zh.json
{
  "menu": {
    "invoiceManagement": "发票管理"
  }
}
```

## 7. 完整实施步骤

### 7.1 第一阶段：基础结构创建

#### 步骤1：创建目录结构
```bash
# 创建页面目录
mkdir -p src/views/sales/invoiceManagement/components

# 创建API模块目录
mkdir -p src/api/modules/sales

# 创建类型定义目录
mkdir -p src/types/sales

# 创建Mock数据目录
mkdir -p src/mock/data/sales
```

#### 步骤2：创建类型定义文件
```typescript
// 创建 src/types/sales/invoiceManagement.d.ts
// 内容参考第4节的完整类型定义
```

#### 步骤3：创建Mock数据文件
```typescript
// 创建 src/mock/data/sales/invoiceManagement.ts
// 内容参考第3节的Mock数据实现
```

#### 步骤4：创建API模块文件
```typescript
// 创建 src/api/modules/sales/invoiceManagement.ts
import request from '@/api';
import type {
  InvoiceSearchParams,
  InvoicePageResponse,
  InvoiceDetail,
  InvoiceOperationLog,
  ApiResponse,
  ExportConfig
} from '@/types/sales/invoiceManagement';
import {
  getInvoiceListMock,
  getInvoiceDetailMock,
  printInvoiceMock,
  batchPrintInvoicesMock,
  sendInvoiceEmailMock,
  exportInvoiceDataMock,
  getInvoiceOperationLogsMock
} from '@/mock/data/sales/invoiceManagement';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getInvoiceList = (
  params: InvoiceSearchParams
): Promise<ApiResponse<InvoicePageResponse>> => {
  if (USE_MOCK_API) {
    return getInvoiceListMock(params);
  } else {
    return request.get<any, ApiResponse<InvoicePageResponse>>(
      '/sales/invoices/list',
      { params }
    );
  }
};

export const getInvoiceDetail = (
  id: string
): Promise<ApiResponse<InvoiceDetail>> => {
  if (USE_MOCK_API) {
    return getInvoiceDetailMock(id);
  } else {
    return request.get<any, ApiResponse<InvoiceDetail>>(
      `/sales/invoices/detail/${id}`
    );
  }
};

export const printInvoice = (
  id: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return printInvoiceMock(id);
  } else {
    return request.post<any, ApiResponse<boolean>>(
      `/sales/invoices/print/${id}`
    );
  }
};

export const batchPrintInvoices = (
  ids: string[]
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return batchPrintInvoicesMock(ids);
  } else {
    return request.post<any, ApiResponse<boolean>>(
      '/sales/invoices/batch-print',
      { ids }
    );
  }
};

export const sendInvoiceEmail = (
  id: string,
  email: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return sendInvoiceEmailMock(id, email);
  } else {
    return request.post<any, ApiResponse<boolean>>(
      `/sales/invoices/send-email/${id}`,
      { email }
    );
  }
};

export const exportInvoiceData = (
  config: ExportConfig
): Promise<ApiResponse<string>> => {
  if (USE_MOCK_API) {
    return exportInvoiceDataMock(config);
  } else {
    return request.post<any, ApiResponse<string>>(
      '/sales/invoices/export',
      config
    );
  }
};

export const getInvoiceOperationLogs = (
  id: string
): Promise<ApiResponse<InvoiceOperationLog[]>> => {
  if (USE_MOCK_API) {
    return getInvoiceOperationLogsMock(id);
  } else {
    return request.get<any, ApiResponse<InvoiceOperationLog[]>>(
      `/sales/invoices/operation-logs/${id}`
    );
  }
};
```

### 7.2 第二阶段：主页面重构

#### 步骤5：重构主页面文件
```vue
<!-- src/views/sales/invoiceManagement/InvoiceManagementView.vue -->
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <!-- 第一行筛选字段 -->
          <el-col :span="6">
            <el-form-item :label="t('invoiceNumber')">
              <el-input
                v-model="searchParams.invoiceNumber"
                :placeholder="t('invoiceNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('customerNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerPhone')">
              <el-input
                v-model="searchParams.customerPhone"
                :placeholder="t('customerPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerEmail')">
              <el-input
                v-model="searchParams.customerEmail"
                :placeholder="t('customerEmailPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行筛选字段 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vin')">
              <el-input
                v-model="searchParams.vin"
                :placeholder="t('vinPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesType')">
              <el-select
                v-model="searchParams.salesType"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in salesTypeOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesStore')">
              <el-select
                v-model="searchParams.salesStore"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
                @change="handleStoreChange"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in storeOptions"
                  :key="option.code"
                  :label="option.dealerName"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行筛选字段 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('salesConsultant')">
              <el-select
                v-model="searchParams.salesConsultant"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in consultantOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('invoiceDate')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 预留空间 -->
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="resetSearch">
                {{ tc('reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能操作区域 -->
    <el-card class="mb-20 operation-card">
      <div class="operation-buttons">
        <el-button :icon="Download" @click="showExportDialog">
          {{ t('export') }}
        </el-button>
        <el-button
          :icon="Printer"
          @click="handleBatchPrint"
          :disabled="selectedRows.length === 0"
        >
          {{ t('batchPrint') }}
        </el-button>
      </div>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <div class="table-container">
        <el-table
          ref="tableRef"
          :data="tableData"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" fixed="left" />
          <el-table-column :label="tc('index')" type="index" width="70" />
          <el-table-column :label="t('invoiceNumber')" prop="invoiceNumber" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('invoiceDate')" prop="invoiceDate" min-width="110" />
          <el-table-column :label="t('orderNumber')" prop="orderNumber" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('customerName')" prop="customerName" min-width="110" show-overflow-tooltip />
          <el-table-column :label="t('customerPhone')" prop="customerPhone" min-width="130" />
          <el-table-column :label="t('customerEmail')" prop="customerEmail" min-width="160" show-overflow-tooltip />
          <el-table-column :label="t('vin')" prop="vin" min-width="160" show-overflow-tooltip />
          <el-table-column :label="t('model')" prop="model" min-width="90" />
          <el-table-column :label="t('variant')" prop="variant" min-width="140" show-overflow-tooltip />
          <el-table-column :label="t('color')" prop="color" min-width="90" />
          <el-table-column :label="t('salesStore')" prop="salesStore" min-width="110" show-overflow-tooltip>
            <template #default="{ row }">
              {{ formatSalesStore(row.salesStore) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('salesConsultant')" prop="salesConsultant" min-width="110" show-overflow-tooltip>
            <template #default="{ row }">
              {{ formatSalesConsultant(row.salesConsultant) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('paymentMethod')" prop="paymentMethod" min-width="90">
            <template #default="{ row }">
              {{ formatPaymentMethod(row.paymentMethod) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('financeCompany')" prop="financeCompany" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('loanAmount')" prop="loanAmount" min-width="110" align="right">
            <template #default="{ row }">
              <span v-if="row.loanAmount > 0">RM {{ formatAmount(row.loanAmount) }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('invoiceAmount')" prop="invoiceAmount" min-width="110" align="right">
            <template #default="{ row }">
              RM {{ formatAmount(row.invoiceAmount) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('createdTime')" prop="createdTime" min-width="150" />

          <!-- 操作列 -->
          <el-table-column :label="tc('operations')" min-width="280" fixed="right" class-name="operations-column">
            <template #default="{ row }">
              <div class="operation-buttons">
                <el-button type="success" :icon="View" link @click="showDetail(row)" size="small">
                  {{ tc('detail') }}
                </el-button>
                <el-button type="primary" :icon="Printer" link @click="handlePrint(row)" size="small">
                  {{ t('print') }}
                </el-button>
                <el-button type="primary" :icon="Message" link @click="showEmailDialog(row)" size="small">
                  {{ t('email') }}
                </el-button>
                <el-button type="info" :icon="Clock" link @click="showLogDialog(row)" size="small">
                  {{ t('log') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 弹窗组件 -->
    <InvoiceDetailDialog
      v-model:visible="detailDialogVisible"
      :invoice-id="selectedInvoice?.id"
    />

    <EmailConfirmDialog
      v-model:visible="emailDialogVisible"
      :invoice-data="selectedInvoice"
      :loading="emailSending"
      @confirm="handleSendEmail"
    />

    <ExportConfigDialog
      v-model:visible="exportDialogVisible"
      :loading="exporting"
      @confirm="handleExport"
    />

    <OperationLogDialog
      v-model:visible="logDialogVisible"
      :invoice-id="selectedInvoice?.id"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { ElMessage } from 'element-plus';
import { Search, Download, Printer, View, Message, Clock } from '@element-plus/icons-vue';
import type {
  InvoiceSearchParams,
  InvoiceItem,
  ExportConfig
} from '@/types/sales/invoiceManagement';
import {
  getInvoiceList,
  printInvoice,
  batchPrintInvoices,
  sendInvoiceEmail,
  exportInvoiceData
} from '@/api/modules/sales/invoiceManagement';
import InvoiceDetailDialog from './components/InvoiceDetailDialog.vue';
import EmailConfirmDialog from './components/EmailConfirmDialog.vue';
import ExportConfigDialog from './components/ExportConfigDialog.vue';
import OperationLogDialog from './components/OperationLogDialog.vue';

// 使用国际化
const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

// 使用字典数据
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.SALES_TYPE,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.SALES_CONSULTANT
]);

// 计算属性获取字典选项
const salesTypeOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.SALES_TYPE)
);
const storeOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.STORE)
);
const allConsultantOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.SALES_CONSULTANT)
);

// 数据定义
const loading = ref(false);
const tableData = ref<InvoiceItem[]>([]);
const selectedRows = ref<InvoiceItem[]>([]);
const selectedInvoice = ref<InvoiceItem | null>(null);

// 搜索参数（使用MyBatisPlus标准）
const searchParams = reactive<InvoiceSearchParams>({
  invoiceNumber: '',
  customerName: '',
  customerPhone: '',
  customerEmail: '',
  orderNumber: '',
  vin: '',
  salesType: '',
  salesStore: '',
  salesConsultant: ''
});

// 日期范围
const dateRange = ref<[string, string] | null>(null);

// 分页参数（使用MyBatisPlus标准）
const pagination = reactive({
  pageNum: 1,      // ✅ 使用pageNum
  pageSize: 20,
  total: 0
});

// 弹窗状态
const detailDialogVisible = ref(false);
const emailDialogVisible = ref(false);
const exportDialogVisible = ref(false);
const logDialogVisible = ref(false);

// 其他状态
const emailSending = ref(false);
const exporting = ref(false);

// 销售顾问选项（根据门店筛选）
const consultantOptions = ref(allConsultantOptions.value);

// 字典转义函数
const formatSalesType = (type: string) =>
  getNameByCode(DICTIONARY_TYPES.SALES_TYPE, type) || type;

const formatPaymentMethod = (method: string) =>
  getNameByCode(DICTIONARY_TYPES.PAYMENT_METHOD, method) || method;

const formatSalesStore = (store: string) =>
  getNameByCode(DICTIONARY_TYPES.STORE, store) || store;

const formatSalesConsultant = (consultant: string) =>
  getNameByCode(DICTIONARY_TYPES.SALES_CONSULTANT, consultant) || consultant;

// 页面初始化
onMounted(() => {
  loadData();
});

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.invoiceDateStart = newVal[0];
    searchParams.invoiceDateEnd = newVal[1];
  } else {
    searchParams.invoiceDateStart = '';
    searchParams.invoiceDateEnd = '';
  }
});

// 构建搜索参数
const buildSearchParams = (): InvoiceSearchParams => {
  const params: InvoiceSearchParams = {
    pageNum: pagination.pageNum,      // ✅ 使用标准参数名
    pageSize: pagination.pageSize,
    ...searchParams
  };

  // 处理时间范围
  if (dateRange.value) {
    params.invoiceDateStart = dateRange.value[0];
    params.invoiceDateEnd = dateRange.value[1];
  }

  return filterEmptyParams(params);
};

// 过滤空值参数
const filterEmptyParams = (params: InvoiceSearchParams): InvoiceSearchParams => {
  const filteredParams: InvoiceSearchParams = {
    pageNum: params.pageNum,
    pageSize: params.pageSize
  };

  // 只保留非空值
  Object.keys(params).forEach(key => {
    const value = params[key as keyof InvoiceSearchParams];
    if (value && String(value).trim() !== '' && key !== 'pageNum' && key !== 'pageSize') {
      (filteredParams as any)[key] = value;
    }
  });

  return filteredParams;
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;

    const params = buildSearchParams();
    const response = await getInvoiceList(params);

    // 标准API响应处理
    if (response.code === '200' || response.code === 200) {
      tableData.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pageNum = response.result.pageNum || pagination.pageNum;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取发票列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    invoiceNumber: '',
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    orderNumber: '',
    vin: '',
    salesType: '',
    salesStore: '',
    salesConsultant: ''
  });
  dateRange.value = null;
  consultantOptions.value = allConsultantOptions.value;
  pagination.pageNum = 1;
  loadData();
};

// 分页处理（保持MyBatisPlus标准）
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // ✅ 使用pageNum
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // ✅ 使用pageNum
  loadData();
};

// 门店变化处理
const handleStoreChange = (storeCode: string) => {
  searchParams.salesConsultant = '';

  if (storeCode) {
    // 根据门店筛选销售顾问
    consultantOptions.value = allConsultantOptions.value.filter(consultant =>
      consultant.storeCode === storeCode
    );
  } else {
    consultantOptions.value = allConsultantOptions.value;
  }
};

// 选择变化
const handleSelectionChange = (selection: InvoiceItem[]) => {
  selectedRows.value = selection;
};

// 显示详情
const showDetail = (row: InvoiceItem) => {
  selectedInvoice.value = row;
  detailDialogVisible.value = true;
};

// 打印发票
const handlePrint = async (row: InvoiceItem) => {
  try {
    const response = await printInvoice(row.id);

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('printSuccess'));
    } else {
      throw new Error(response.message || t('printFailed'));
    }
  } catch (error) {
    console.error('打印失败:', error);
    ElMessage.error(t('printFailed'));
  }
};

// 批量打印
const handleBatchPrint = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning(t('pleaseSelectRecords'));
    return;
  }

  try {
    const ids = selectedRows.value.map(row => row.id);
    const response = await batchPrintInvoices(ids);

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('batchPrintSuccess'));
    } else {
      throw new Error(response.message || t('batchPrintFailed'));
    }
  } catch (error) {
    console.error('批量打印失败:', error);
    ElMessage.error(t('batchPrintFailed'));
  }
};

// 显示邮件弹窗
const showEmailDialog = (row: InvoiceItem) => {
  selectedInvoice.value = row;
  emailDialogVisible.value = true;
};

// 发送邮件
const handleSendEmail = async () => {
  if (!selectedInvoice.value) return;

  try {
    emailSending.value = true;
    const response = await sendInvoiceEmail(
      selectedInvoice.value.id,
      selectedInvoice.value.customerEmail
    );

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('emailSentSuccess'));
      emailDialogVisible.value = false;
    } else {
      throw new Error(response.message || t('emailSentFailed'));
    }
  } catch (error) {
    console.error('邮件发送失败:', error);
    ElMessage.error(t('emailSentFailed'));
  } finally {
    emailSending.value = false;
  }
};

// 显示导出弹窗
const showExportDialog = () => {
  exportDialogVisible.value = true;
};

// 导出数据
const handleExport = async (config: ExportConfig) => {
  try {
    exporting.value = true;

    if (config.scope === 'filtered') {
      config.searchParams = buildSearchParams();
    }

    const response = await exportInvoiceData(config);

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('exportSuccess'));
      exportDialogVisible.value = false;
    } else {
      throw new Error(response.message || t('exportFailed'));
    }
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('exportFailed'));
  } finally {
    exporting.value = false;
  }
};

// 显示日志弹窗
const showLogDialog = (row: InvoiceItem) => {
  selectedInvoice.value = row;
  logDialogVisible.value = true;
};

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};
</script>
```

### 7.3 第三阶段：验证清单

#### 步骤6：功能测试
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作（使用pageNum参数）
- [ ] 字典数据正常显示和转义
- [ ] 门店和销售顾问级联筛选正常
- [ ] 打印和批量打印功能正常
- [ ] 邮件发送功能正常
- [ ] 导出功能正常
- [ ] 详情弹窗正常显示
- [ ] 操作日志弹窗正常
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

#### 步骤7：代码质量检查
- [ ] TypeScript类型安全，无编译错误
- [ ] ESLint检查通过
- [ ] 代码格式化正确
- [ ] 组件拆分合理
- [ ] API响应处理符合规范

#### 步骤8：性能优化验证
- [ ] 字典数据缓存正常
- [ ] 分页加载性能良好
- [ ] 组件懒加载正常
- [ ] Mock数据切换正常

---

**本设计文档为发票管理页面重构提供完整的技术方案和实施指导，确保重构后的代码符合项目规范并具备良好的可维护性。**
