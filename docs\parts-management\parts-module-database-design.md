# 零件管理模块数据库设计

## 1. 数据库表结构

### 1.1 零件主数据表 (parts)
零件基础信息表，存储从ERP同步的零件主数据。

```sql
CREATE TABLE `parts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `part_code` varchar(50) NOT NULL COMMENT '零件编号',
  `part_name` varchar(200) NOT NULL COMMENT '零件名称',
  `brand` varchar(100) DEFAULT NULL COMMENT '品牌',
  `category` varchar(100) DEFAULT NULL COMMENT '类别',
  `unit` varchar(20) NOT NULL COMMENT '计量单位',
  `specification` varchar(200) DEFAULT NULL COMMENT '规格型号',
  `barcode` varchar(50) DEFAULT NULL COMMENT '条形码',
  `retail_price` decimal(10,2) DEFAULT NULL COMMENT '建议零售价',
  `purchase_price` decimal(10,2) DEFAULT NULL COMMENT '进货参考价',
  `minimum_price` decimal(10,2) DEFAULT NULL COMMENT '最低销售价',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-停用',
  `sync_status` varchar(20) DEFAULT NULL COMMENT '同步状态：SUCCESS-同步成功，FAILED-同步失败',
  `source_system` varchar(50) DEFAULT NULL COMMENT '来源系统',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_part_code` (`part_code`),
  KEY `idx_part_name` (`part_name`),
  KEY `idx_brand` (`brand`),
  KEY `idx_category` (`category`),
  KEY `idx_barcode` (`barcode`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='零件主数据表';
```

### 1.2 库存表 (inventory)
存储各门店的零件库存信息。

```sql
CREATE TABLE `inventory` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_id` bigint(20) NOT NULL COMMENT '门店ID',
  `part_id` bigint(20) NOT NULL COMMENT '零件ID',
  `warehouse_id` bigint(20) DEFAULT NULL COMMENT '仓库ID',
  `current_stock` int(11) NOT NULL DEFAULT '0' COMMENT '当前库存',
  `available_stock` int(11) NOT NULL DEFAULT '0' COMMENT '可用库存',
  `occupied_stock` int(11) NOT NULL DEFAULT '0' COMMENT '占用库存',
  `damaged_stock` int(11) NOT NULL DEFAULT '0' COMMENT '损坏库存',
  `safety_stock` int(11) DEFAULT NULL COMMENT '安全库存',
  `maximum_stock` int(11) DEFAULT NULL COMMENT '最大库存',
  `location` varchar(100) DEFAULT NULL COMMENT '库位',
  `shelf_number` varchar(50) DEFAULT NULL COMMENT '货架号',
  `stock_status` varchar(20) NOT NULL DEFAULT 'NORMAL' COMMENT '库存状态：NORMAL-正常，WARNING-预警，SHORTAGE-缺货',
  `last_check_time` datetime DEFAULT NULL COMMENT '最后盘点时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_part_warehouse` (`store_id`,`part_id`,`warehouse_id`),
  KEY `idx_part_id` (`part_id`),
  KEY `idx_stock_status` (`stock_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存表';
```

### 1.3 补货单表 (replenishment_orders)
存储补货申请单信息。

```sql
CREATE TABLE `replenishment_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(50) NOT NULL COMMENT '补货单号',
  `order_date` date NOT NULL COMMENT '申请日期',
  `store_id` bigint(20) NOT NULL COMMENT '门店ID',
  `applicant` varchar(50) NOT NULL COMMENT '申请人',
  `approval_status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '审批状态：DRAFT-草稿，PENDING-待审批，APPROVED-已审批，REJECTED-已拒绝',
  `execution_status` varchar(20) DEFAULT NULL COMMENT '执行状态：PENDING-待收货，PARTIAL-部分收货，COMPLETED-已完成',
  `total_amount` decimal(10,2) DEFAULT NULL COMMENT '总金额',
  `total_quantity` int(11) DEFAULT NULL COMMENT '总数量',
  `expected_delivery_date` date DEFAULT NULL COMMENT '预计到货日期',
  `approver` varchar(50) DEFAULT NULL COMMENT '审批人',
  `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approval_remark` varchar(500) DEFAULT NULL COMMENT '审批备注',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_approval_status` (`approval_status`),
  KEY `idx_execution_status` (`execution_status`),
  KEY `idx_order_date` (`order_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='补货单表';
```

### 1.4 补货单明细表 (replenishment_order_details)
存储补货单的零件明细信息。

```sql
CREATE TABLE `replenishment_order_details` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) NOT NULL COMMENT '补货单ID',
  `part_id` bigint(20) NOT NULL COMMENT '零件ID',
  `request_quantity` int(11) NOT NULL COMMENT '申请数量',
  `approved_quantity` int(11) DEFAULT NULL COMMENT '批准数量',
  `received_quantity` int(11) DEFAULT '0' COMMENT '已收数量',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT '小计',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_part_id` (`part_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='补货单明细表';
```

### 1.5 收货单表 (receiving_orders)
存储收货单信息。

```sql
CREATE TABLE `receiving_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `receiving_no` varchar(50) NOT NULL COMMENT '收货单号',
  `replenishment_order_id` bigint(20) NOT NULL COMMENT '关联补货单ID',
  `receiving_date` date NOT NULL COMMENT '收货日期',
  `receiver` varchar(50) NOT NULL COMMENT '收货人',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING-待收货，PARTIAL-部分收货，COMPLETED-已完成',
  `total_received_quantity` int(11) DEFAULT '0' COMMENT '总收货数量',
  `total_damaged_quantity` int(11) DEFAULT '0' COMMENT '总报损数量',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_receiving_no` (`receiving_no`),
  KEY `idx_replenishment_order_id` (`replenishment_order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_receiving_date` (`receiving_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货单表';
```

### 1.6 收货单明细表 (receiving_order_details)
存储收货单的零件明细信息。

```sql
CREATE TABLE `receiving_order_details` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `receiving_order_id` bigint(20) NOT NULL COMMENT '收货单ID',
  `part_id` bigint(20) NOT NULL COMMENT '零件ID',
  `expected_quantity` int(11) NOT NULL COMMENT '应收数量',
  `received_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '实收数量',
  `damaged_quantity` int(11) DEFAULT '0' COMMENT '报损数量',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_receiving_order_id` (`receiving_order_id`),
  KEY `idx_part_id` (`part_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货单明细表';
```

### 1.7 报损单表 (damage_orders)
存储报损申请单信息。

```sql
CREATE TABLE `damage_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `damage_no` varchar(50) NOT NULL COMMENT '报损单号',
  `damage_date` date NOT NULL COMMENT '报损日期',
  `store_id` bigint(20) NOT NULL COMMENT '门店ID',
  `reporter` varchar(50) NOT NULL COMMENT '报告人',
  `damage_reason` varchar(500) NOT NULL COMMENT '报损原因',
  `approval_status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '审批状态：DRAFT-草稿，PENDING-待审批，APPROVED-已审批，REJECTED-已拒绝',
  `approver` varchar(50) DEFAULT NULL COMMENT '审批人',
  `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_damage_no` (`damage_no`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_approval_status` (`approval_status`),
  KEY `idx_damage_date` (`damage_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报损单表';
```

### 1.8 报损单明细表 (damage_order_details)
存储报损单的零件明细信息。

```sql
CREATE TABLE `damage_order_details` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `damage_order_id` bigint(20) NOT NULL COMMENT '报损单ID',
  `part_id` bigint(20) NOT NULL COMMENT '零件ID',
  `damage_quantity` int(11) NOT NULL COMMENT '报损数量',
  `damage_type` varchar(50) DEFAULT NULL COMMENT '损坏类型',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `total_loss` decimal(10,2) DEFAULT NULL COMMENT '损失金额',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_damage_order_id` (`damage_order_id`),
  KEY `idx_part_id` (`part_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报损单明细表';
```

### 1.9 拣货单表 (picking_orders)
存储拣货单信息。

```sql
CREATE TABLE `picking_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `picking_no` varchar(50) NOT NULL COMMENT '拣货单号',
  `source_type` varchar(30) NOT NULL COMMENT '来源类型：WORK_ORDER-工单，SALES_ORDER-销售单',
  `source_id` bigint(20) NOT NULL COMMENT '来源单据ID',
  `source_no` varchar(50) DEFAULT NULL COMMENT '来源单据号',
  `store_id` bigint(20) NOT NULL COMMENT '门店ID',
  `picker` varchar(50) DEFAULT NULL COMMENT '拣货人',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING-待拣货，PICKING-拣货中，COMPLETED-已完成，RETURNED-已退拣',
  `picking_time` datetime DEFAULT NULL COMMENT '拣货时间',
  `is_printed` tinyint(1) DEFAULT '0' COMMENT '是否已打印：0-否，1-是',
  `print_time` datetime DEFAULT NULL COMMENT '打印时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_picking_no` (`picking_no`),
  KEY `idx_source` (`source_type`,`source_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拣货单表';
```

### 1.10 拣货单明细表 (picking_order_details)
存储拣货单的零件明细信息。

```sql
CREATE TABLE `picking_order_details` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `picking_order_id` bigint(20) NOT NULL COMMENT '拣货单ID',
  `part_id` bigint(20) NOT NULL COMMENT '零件ID',
  `required_quantity` int(11) NOT NULL COMMENT '需求数量',
  `picked_quantity` int(11) DEFAULT '0' COMMENT '已拣数量',
  `location` varchar(100) DEFAULT NULL COMMENT '库位',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_picking_order_id` (`picking_order_id`),
  KEY `idx_part_id` (`part_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拣货单明细表';
```

## 2. 索引设计说明

### 2.1 主键索引
- 所有表都使用自增ID作为主键，保证唯一性和查询效率

### 2.2 唯一索引
- 单号字段（order_no, receiving_no等）建立唯一索引，防止重复
- 库存表的门店+零件+仓库组合建立唯一索引

### 2.3 普通索引
- 外键关联字段建立索引，提高JOIN查询效率
- 状态字段建立索引，支持按状态筛选
- 日期字段建立索引，支持时间范围查询
- 常用查询条件字段建立索引

## 3. 表关系说明

### 3.1 一对多关系
- parts (1) → inventory (n)：一个零件在多个门店有库存
- replenishment_orders (1) → replenishment_order_details (n)：一个补货单包含多个零件
- receiving_orders (1) → receiving_order_details (n)：一个收货单包含多个零件
- damage_orders (1) → damage_order_details (n)：一个报损单包含多个零件
- picking_orders (1) → picking_order_details (n)：一个拣货单包含多个零件

### 3.2 关联关系
- inventory.part_id → parts.id：库存关联零件
- replenishment_order_details.part_id → parts.id：补货明细关联零件
- receiving_orders.replenishment_order_id → replenishment_orders.id：收货单关联补货单

## 4. 数据完整性约束

### 4.1 外键约束
建议在应用层控制外键关系，避免数据库层面的外键约束影响性能

### 4.2 字段约束
- NOT NULL约束：核心业务字段不允许为空
- DEFAULT值：状态字段设置合理的默认值
- CHECK约束：金额、数量等字段应大于等于0

## 5. 性能优化建议

### 5.1 分表策略
- 当单表数据量超过500万时，考虑按时间或门店进行分表
- 历史数据定期归档，保持在线表的查询性能

### 5.2 读写分离
- 主库负责写操作，从库负责查询操作
- 报表类查询走从库，减轻主库压力

### 5.3 缓存策略
- 零件主数据变化不频繁，适合缓存
- 库存数据需要实时性，谨慎使用缓存