# 采购管理页面接口文档 (v2.0)

**说明:** 此接口文档基于“经销商-主机厂”协同采购模式设计。

---

## A. 经销商端接口

### 1. 采购订单列表页 (经销商视图)

#### 1.1 获取订单页统计指标

**接口地址:** `/api/purchase/dealer/dashboard`
**请求方式:** `POST`
**接口描述:** 获取经销商采购订单页顶部的四个核心统计指标。

**请求参数:**
```json
{
  "dealerId": 2001
}
```

**响应结果:**
```json
{
  "code": 200,
  "data": {
    "pendingApprovalCount": 2,
    "inTransitCount": 5,
    "pendingReceiptCount": 3,
    "currentMonthTotalAmount": 128000.00
  }
}
```

#### 1.2 获取采购订单列表

**接口地址:** `/api/purchase/dealer/list`
**请求方式:** `POST`
**接口描述:** 分页查询经销商自己的采购订单列表。

**请求参数:**
```json
{
  "dealerId": 2001,
  "page": 1,
  "size": 20,
  "query": "PO20250728",
  "status": ["IN_TRANSIT", "PARTIALLY_RECEIVED"]
}
```

**响应结果:**
```json
{
  "code": 200,
  "data": [
    {
      "orderId": 101,
      "orderNo": "PO20250728001",
      "totalAmount": 3150.00,
      "status": "SHIPPED_ALL",
      "creationDate": "2025-07-27",
      "shippingDate": "2025-07-28"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalCount": 31,
    "totalPages": 2
  }
}
```

### 2. 新建/编辑采购订单页 (经销商视图)

#### 2.1 创建/更新采购订单

**接口地址:** `/api/purchase/dealer/save`
**请求方式:** `POST`
**接口描述:** 保存（草稿）或更新一个采购订单。

**请求参数:**
```json
{
  "orderId": null, // 新建时为null，编辑时传ID
  "dealerId": 2001,
  "warehouseId": 3001,
  "remarks": "部分配件急用，请优先处理",
  "items": [
    {"partId": "P10001", "partType": "ORIGINAL", "quantity": 100},
    {"partId": "P10002", "partType": "ORIGINAL", "quantity": 50},
    {"partId": "NP2001", "partType": "NON_ORIGINAL", "quantity": 20}
  ]
}
```

#### 2.2 提交采购订单至主机厂审核

**接口地址:** `/api/purchase/dealer/submit`
**请求方式:** `POST`
**接口描述:** 将一个“草稿”状态的采购订单提交给主机厂进行审核。如果订单中同时包含原厂和非原厂零件，后端会自动将其拆分为两个独立的采购订单。

**请求参数:**
```json
{
  "orderId": 100 // 草稿订单ID
}
```

**响应结果:**
```json
{
  "code": 200,
  "message": "提交成功，已生成2个新订单",
  "data": [
    {
      "orderId": 101,
      "orderNo": "PO20250728001",
      "partType": "ORIGINAL"
    },
    {
      "orderId": 102,
      "orderNo": "PO20250728002",
      "partType": "NON_ORIGINAL"
    }
  ]
}
```

#### 2.3 取消采购订单

**接口地址:** `/api/purchase/dealer/cancel`
**请求方式:** `POST`
**接口描述:** 取消一个尚未被主机厂完全发货的采购订单。

**请求参数:**
```json
{
  "orderId": 101
}
```

### 3. 采购收货页 (经销商视图)

#### 3.1 获取待收货信息

**接口地址:** `/api/purchase/dealer/receipt-info`
**请求方式:** `POST`
**接口描述:** 根据采购订单ID，获取所有关联的、待收货的发货单信息。

**请求参数:**
```json
{
  "orderId": 101
}
```

**响应结果:**
```json
{
  "code": 200,
  "data": {
    "orderNo": "PO20250728001",
    "shipments": [
      {
        "shipmentId": 501,
        "shipmentNo": "SH20250728005",
        "shippingDate": "2025-07-28",
        "carrier": "顺丰速运",
        "trackingNumber": "SF1234567890",
        "items": [
          {"partId": "P10003", "partName": "刹车片前", "orderedQuantity": 20, "shippedQuantity": 10},
          {"partId": "P10008", "partName": "雨刮片", "orderedQuantity": 30, "shippedQuantity": 30}
        ]
      }
    ]
  }
}
```

#### 3.2 确认入库

**接口地址:** `/api/purchase/dealer/receive`
**请求方式:** `POST`
**接口描述:** 经销商确认收货，将配件登记入库。

**请求参数:**
```json
{
  "shipmentId": 501,
  "receiptDate": "2025-07-29",
  "handler": "张三",
  "items": [
    {"partId": "P10003", "receivedQuantity": 10, "locationId": "A01-03"},
    {"partId": "P10008", "receivedQuantity": 30, "locationId": "B02-11"}
  ]
}
```

---

## B. 主机厂端接口

### 1. 采购审批列表页 (主机厂视图)

#### 1.1 获取审批页统计指标

**接口地址:** `/api/purchase/oem/dashboard`
**请求方式:** `GET`
**接口描述:** 获取主机厂审批页顶部的核心统计指标。

**响应结果:**
```json
{
  "code": 200,
  "data": {
    "pendingApprovalCount": 15,
    "approvedTodayCount": 32,
    "pendingShipmentCount": 8,
    "last7DaysTotalAmount": 1250000.00
  }
}
```

#### 1.2 获取待处理订单列表

**接口地址:** `/api/purchase/oem/list`
**请求方式:** `POST`
**接口描述:** 分页查询所有经销商提交的、待处理的采购订单。

**请求参数:**
```json
{
  "page": 1,
  "size": 20,
  "query": "A经销商",
  "status": ["PENDING_APPROVAL"]
}
```

**响应结果:**
```json
{
  "code": 200,
  "data": [
    {
      "orderId": 102,
      "orderNo": "PO20250728002",
      "dealerName": "A经销商-北京总店",
      "totalAmount": 8200.00,
      "status": "PENDING_APPROVAL",
      "creationDate": "2025-07-28"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalCount": 65,
    "totalPages": 4
  }
}
```

### 2. 采购审批/发货处理页 (主机厂视图)

#### 2.1 获取订单详情以供处理

**接口地址:** `/api/purchase/oem/detail`
**请求方式:** `POST`
**接口描述:** 获取单个采购订单的完整详情，包含厂区库存信息。

**请求参数:**
```json
{
  "orderId": 102
}
```

**响应结果:**
```json
{
  "code": 200,
  "data": {
    "orderInfo": { ... },
    "items": [
      {"partId": "P10001", "partName": "火花塞", "requestedQuantity": 100, "factoryStock": 5000},
      {"partId": "P10002", "partName": "机油滤清器", "requestedQuantity": 50, "factoryStock": 20}
    ]
  }
}
```

#### 2.2 审核订单

**接口地址:** `/api/purchase/oem/approve`
**请求方式:** `POST`
**接口描述:** 主机厂专员审核采购订单（通过或驳回）。

**请求参数:**
```json
{
  "orderId": 102,
  "isApproved": true,
  "remarks": "同意采购",
  "auditorId": 9001
}
```

#### 2.3 执行发货

**接口地址:** `/api/purchase/oem/ship`
**请求方式:** `POST`
**接口描述:** 对已审核通过的订单执行发货操作。

**请求参数:**
```json
{
  "orderId": 102,
  "carrier": "顺丰速运",
  "trackingNumber": "SF9876543210",
  "remarks": "缺货配件(P10002)预计下周到货，届时将补发",
  "items": [
    {"partId": "P10001", "shippedQuantity": 100},
    {"partId": "P10002", "shippedQuantity": 20}
  ]
}
```
