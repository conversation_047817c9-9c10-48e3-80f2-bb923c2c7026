# 采购管理-业务流程 (v2.0)

**说明:** 此版本已根据“经销商-主机厂”协同采购模式进行更新。

---

## 1. 概述

本流程文档描述了从 **经销商门店** 发起采购申请，到 **主机厂** 审核、发货，最终由经销商确认收货的完整闭环业务流程。旨在明确两个不同组织实体间的操作步骤、状态流转和信息同步机制。

## 2. 角色

*   **经销商 (Dealer):** 配件的需求方，负责发起采购、跟踪物流、确认收货。
*   **主机厂 (OEM):** 配件的供应方，负责审核订单、执行发货、处理缺货情况。

## 3. 流程图

```mermaid
graph TD
    subgraph 经销商端
        A[开始] --> B(创建采购单);
        B --> C{保存草稿};
        C --> D(编辑采购单);
        D --> E[提交审核];
        B --> E;
        E -- 包含多种类型配件 --> E_Split(拆分为多个订单);
        E_Split --> F_OEM[原厂订单待审核];
        E_Split --> F_3rd[非原厂订单待审核];
        E -- 单一类型配件 --> F[待厂端审核];
        F --> G;
        F_OEM --> G;
        J[待发货] --> K(跟踪订单状态);
        L[部分/全部发货] --> M(查看物流信息);
        M --> N(确认收货);
        N -- 部分收货 --> O[部分入库];
        N -- 全部收货 --> P[全部入库];
        O --> K;
        P --> Q[完成];
    end

    subgraph 主机厂端
        F --> G{审核采购单};
        G -- 驳回 --> D;
        G -- 通过 --> J;
        J --> H{库存检查};
        H -- 库存不足 --> I(部分发货);
        H -- 库存充足 --> S(全部发货);
        I --> L;
        S --> L;
    end

    linkStyle 6 stroke:#ff9999,stroke-width:2px,color:red;
    linkStyle 10 stroke:#ff9999,stroke-width:2px,color:red;
    linkStyle 11 stroke:#ff9999,stroke-width:2px,color:red;
```

## 4. 流程步骤详解

| 步骤 | 操作方 | 操作描述 | 前置状态 | 后置状态 | 产出物 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 1 | **经销商** | 在DMS系统中创建采购订单，填写所需配件和数量。 | - | 草稿 | 采购订单（草稿） |
| 2 | **经销商** | 确认订单信息无误后，提交审核。如果订单同时包含原厂和非原厂零件，系统会自动将其拆分为两个独立的订单分别提交。 | 草稿 | 待厂端审核 | 采购订单（待厂端审核） |
| 3 | **主机厂/第三方** | 零件专员审核经销商提交的采购订单。原厂和非原厂订单进入各自的审批流程。 | 待厂端审核 | 已驳回 / 待发货 | 审核意见 |
| 4 | **主机厂** | 审核通过后，根据厂内库存情况，安排发货。可能一次性全部发货，也可能因缺货而部分发货。 | 待发货 | 部分发货 / 全部发货 | 发货单、物流信息 |
| 5 | **经销商** | 订单被厂端发货后，经销商可查看物流信息，跟踪货物位置。 | 部分发货 / 全部发货 | - | - |
| 6 | **经销商** | 收到厂端发来的货物后，在系统中进行清点和入库操作。 | 部分发货 / 全部发货 | 部分入库 / 全部入库 | 入库记录 |
| 7 | **系统** | 配件入库后，系统自动增加经销商对应配件的库存数量。 | - | - | 更新后的库存 |

## 5. 关键业务规则

- **订单拆分:** 经销商提交包含原厂和非原厂两种类型零件的采购单时，系统会自动生成两个独立的订单，分别进入不同的审批和发货流程。
- **状态同步:** 采购订单的状态在经销商和主机厂之间必须实时同步，确保双方信息一致。
- **部分发货/收货:** 流程必须完整支持一个采购订单分多次发货、多次收货的场景。
- **数据隔离:** 经销商只能看到自己的采购订单。主机厂可以查看所有经销商的订单。
- **权限分离:** 经销商员工不能进行审核、发货操作；主机厂员工不能创建、修改、删除经销商的采购订单。