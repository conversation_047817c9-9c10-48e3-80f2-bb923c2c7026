# 采购管理-实体信息及实体关系 (v2.0)

**说明:** 此版本已根据“经销商-主机厂”协同采购模式进行更新。

---

## 1. 核心实体

### 1.1. 采购订单 (PurchaseOrder) - 经销商创建

| 字段名 | 数据类型 | 中文名称 | 说明 | 关联关系 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `String` | 订单ID | 主键 | |
| `orderNo` | `String` | 订单号 | 业务唯一标识 | |
| `dealerId` | `String` | 经销商ID | | `Dealer.id` |
| `supplierId` | `String` | 供应商ID | 固定为主机厂 | `OEM.id` |
| `status` | `Enum` | 订单状态 | **草稿**, **待厂端审核**, **已驳回**, **待发货**, **部分发货**, **全部发货**, **部分入库**, **全部入库**, **已作废** <br> **说明:** 草稿状态的订单可包含混合类型的零件，提交后会根据零件类型拆分为独立的订单。 | |
| `totalAmount` | `Number` | 订单总金额 | | |
| `remarks` | `String` | 备注 | | |
| `creatorId` | `String` | 创建人ID | 经销商员工 | `User.id` |
| `createdAt` | `DateTime` | 创建时间 | | |
| `auditorId` | `String` | 审核人ID | 主机厂员工 | `User.id` |
| `auditedAt` | `DateTime` | 审核时间 | | |

### 1.2. 采购订单明细 (PurchaseOrderItem)

| 字段名 | 数据类型 | 中文名称 | 说明 | 关联关系 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `String` | 明细ID | 主键 | |
| `purchaseOrderId` | `String` | 采购订单ID | | `PurchaseOrder.id` |
| `partId` | `String` | 配件ID | | `Part.id` |
| `partType` | `Enum` | 零件类型 | **ORIGINAL** (原厂), **NON_ORIGINAL** (非原厂) | |
| `purchasePrice` | `Number` | 采购单价 | | |
| `orderQuantity` | `Number` | 订购数量 | | |
| `shippedQuantity` | `Number` | 已发货数量 | 由发货单更新 | |
| `receivedQuantity`| `Number` | 已入库数量 | 由入库操作更新 | |
| `amount` | `Number` | 金额 | | |

### 1.3. 发货单 (ShipmentOrder) - 主机厂创建

| 字段名 | 数据类型 | 中文名称 | 说明 | 关联关系 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `String` | 发货单ID | 主键 | |
| `shipmentNo` | `String` | 发货单号 | | |
| `purchaseOrderId` | `String` | 关联的采购订单ID | | `PurchaseOrder.id` |
| `shippingDate` | `Date` | 发货日期 | | |
| `carrier` | `String` | 承运商 | | |
| `trackingNumber` | `String` | 物流单号 | | |
| `remarks` | `String` | 发货备注 | | |
| `creatorId` | `String` | 发货人ID | 主机厂员工 | `User.id` |
| `createdAt` | `DateTime` | 发货时间 | | |

### 1.4. 发货单明细 (ShipmentOrderItem)

| 字段名 | 数据类型 | 中文名称 | 说明 | 关联关系 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `String` | 发货明细ID | 主键 | |
| `shipmentOrderId` | `String` | 发货单ID | | `ShipmentOrder.id` |
| `purchaseOrderItemId` | `String` | 采购订单明细ID | | `PurchaseOrderItem.id` |
| `partId` | `String` | 配件ID | | `Part.id` |
| `shippedQuantity` | `Number` | 本次发货数量 | | |

---

## 2. 实体关系 (ERD)

```mermaid
erDiagram
    DEALER ||--|{ PURCHASE_ORDER : "创建"
    OEM ||--|{ PURCHASE_ORDER : "审核"
    PURCHASE_ORDER ||--o{ PURCHASE_ORDER_ITEM : "包含"
    PURCHASE_ORDER ||--o{ SHIPMENT_ORDER : "产生"

    SHIPMENT_ORDER ||--o{ SHIPMENT_ORDER_ITEM : "包含"
    PURCHASE_ORDER_ITEM ||--o{ SHIPMENT_ORDER_ITEM : "关联"

    DEALER {
        String id PK
        String name
    }

    OEM {
        String id PK
        String name
    }

    PURCHASE_ORDER {
        String id PK
        String orderNo
        String dealerId FK
        String supplierId FK
        Enum status
        DateTime createdAt
    }

    PURCHASE_ORDER_ITEM {
        String id PK
        String purchaseOrderId FK
        String partId FK
        Enum partType
        Number orderQuantity
        Number shippedQuantity
        Number receivedQuantity
    }

    SHIPMENT_ORDER {
        String id PK
        String shipmentNo
        String purchaseOrderId FK
        Date shippingDate
        String trackingNumber
        DateTime createdAt
    }

    SHIPMENT_ORDER_ITEM {
        String id PK
        String shipmentOrderId FK
        String purchaseOrderItemId FK
        Number shippedQuantity
    }
```