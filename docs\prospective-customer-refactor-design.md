# 潜在客户销售前景页面重构设计文档

## 1. 重构背景与目标

### 1.1 现有问题分析

**当前目录结构问题：**
- ❌ 页面位置：`src/views/prospective-customer/sales-prospect/index.vue`
- ❌ 非路由页面和组件混合放置在 `components/` 目录
- ❌ 缺少专门的 API 模块（依赖现有但不规范的 API）
- ❌ 类型定义分散在全局 `types/prospective-customer.d.ts`
- ❌ 国际化使用不规范的模块引用 `useModuleI18n('sales')`

**重构目标：**
- ✅ 符合页面目录结构规范
- ✅ 建立标准的模块化组织
- ✅ 规范化国际化处理
- ✅ 优化代码架构和可维护性

### 1.2 重构依据

参考 **零件档案页面** (`src/views/parts/archives/ArchivesView.vue`) 的成功重构案例：
- 模块化目录结构
- 标准化 API 调用
- 统一的国际化处理
- 完整的类型定义

## 2. 重构目标结构设计

### 2.1 目录结构重构

**重构前：**
```
src/views/prospective-customer/sales-prospect/
├── index.vue                         # 主页面
└── components/                       # 组件目录
    ├── AddProspectModal.vue
    ├── AssignAdvisorModal.vue
    ├── DefeatModal.vue
    ├── FollowUpModal.vue
    └── ProspectDetailModal.vue
```

**重构后：**
```
src/
├── views/sales/prospects/                    # 按规范调整到销售模块
│   ├── ProspectsView.vue                    # 主列表页面（路由页面）
│   └── components/                          # 非路由页面和组件统一目录
│       ├── ProspectAddView.vue              # 新增潜客页面（非路由）
│       ├── ProspectDetailView.vue           # 潜客详情页面（非路由）
│       ├── ProspectFollowUpView.vue         # 跟进记录页面（非路由）
│       ├── ProspectDefeatView.vue           # 无意向标记页面（非路由）
│       ├── ProspectAssignView.vue           # 分配顾问页面（非路由）
│       ├── ProspectForm.vue                 # 潜客表单组件
│       ├── ProspectSearchForm.vue           # 搜索表单组件
│       └── ProspectTable.vue                # 数据表格组件
├── api/modules/sales/
│   └── prospects.ts                         # 潜客管理 API
├── types/sales/
│   └── prospects.d.ts                       # 潜客管理类型定义
├── mock/data/sales/
│   └── prospects.ts                         # 潜客管理 Mock 数据
└── locales/modules/sales/
    ├── zh.json                              # 中文翻译（包含 prospects 模块）
    └── en.json                              # 英文翻译（包含 prospects 模块）
```

### 2.2 模块重新分类说明

**从 `prospective-customer` 迁移到 `sales`：**
- 潜在客户销售前景属于销售业务模块
- 与销售相关的其他功能保持一致性
- 遵循业务域划分原则

**页面类型重新定义：**
- **ProspectsView.vue**：潜客列表页（路由页面）
- **ProspectAddView.vue**：新增潜客页（非路由，取代 AddProspectModal）
- **ProspectDetailView.vue**：潜客详情页（非路由，取代 ProspectDetailModal）
- **ProspectFollowUpView.vue**：跟进记录页（非路由，取代 FollowUpModal）
- **ProspectDefeatView.vue**：无意向标记页（非路由，取代 DefeatModal）
- **ProspectAssignView.vue**：分配顾问页（非路由，取代 AssignAdvisorModal）

## 3. 技术实现方案

### 3.1 类型定义重构

**新建文件：** `src/types/sales/prospects.d.ts`

```typescript
// 潜客基本信息接口
export interface ProspectItem {
  id: string;
  name: string;
  phoneNumber: string;
  sourceChannel: string;
  currentIntentLevel: string;
  prospectStatus: string;
  intentModel?: string;
  intentVariant?: string;
  intentColor?: string;
  currentSalesAdvisorId?: string;
  currentSalesAdvisorName?: string;
  leadAssociationTime?: string;
  lastFollowUpTime?: string;
  nextFollowUpTime?: string;
}

// 搜索参数接口
export interface ProspectSearchParams {
  prospectId?: string;
  customerName?: string;
  customerPhone?: string;
  sourceChannel?: string;
  customerLevel?: string;
  customerStatus?: string;
  isToday?: boolean;
  page?: number;
  pageSize?: number;
}

// 分页响应接口
export interface ProspectPageResponse {
  list: ProspectItem[];
  total: number;
}

// 新增潜客请求参数
export interface CreateProspectRequest {
  name: string;
  phoneNumber: string;
  sourceChannel: string;
  intentLevel: string;
  salesAdvisorId?: string;
  // 其他必要字段
}

// 跟进记录接口
export interface FollowUpRecord {
  id: string;
  prospectId: string;
  followUpType: string;
  followUpTime: string;
  nextFollowUpTime?: string;
  followUpContent: string;
  followUpResult: string;
}

// 分配顾问请求参数
export interface AssignAdvisorRequest {
  prospectId: string;
  newAdvisorId: string;
  reason?: string;
}
```

### 3.2 API 模块重构

**新建文件：** `src/api/modules/sales/prospects.ts`

```typescript
import request from '@/api';
import type { 
  ProspectSearchParams, 
  ProspectPageResponse, 
  ProspectItem,
  CreateProspectRequest,
  FollowUpRecord,
  AssignAdvisorRequest
} from '@/types/sales/prospects';
import { 
  getProspectList, 
  createProspect, 
  getProspectDetail,
  addFollowUpRecord,
  assignAdvisor,
  markNoIntention
} from '@/mock/data/sales/prospects';
import { USE_MOCK_API } from '@/utils/mock-config';

// 获取潜客列表
export const getProspects = (params: ProspectSearchParams): Promise<ProspectPageResponse> => {
  if (USE_MOCK_API) {
    return getProspectList(params);
  } else {
    return request.get<any, ProspectPageResponse>('/sales/prospects/list', { params });
  }
};

// 获取潜客详情
export const getProspectById = (id: string): Promise<ProspectItem> => {
  if (USE_MOCK_API) {
    return getProspectDetail(id);
  } else {
    return request.get<any, ProspectItem>(`/sales/prospects/${id}`);
  }
};

// 新增潜客
export const createNewProspect = (data: CreateProspectRequest): Promise<ProspectItem> => {
  if (USE_MOCK_API) {
    return createProspect(data);
  } else {
    return request.post<any, ProspectItem>('/sales/prospects', data);
  }
};

// 添加跟进记录
export const addFollowUp = (data: FollowUpRecord): Promise<FollowUpRecord> => {
  if (USE_MOCK_API) {
    return addFollowUpRecord(data);
  } else {
    return request.post<any, FollowUpRecord>('/sales/prospects/follow-up', data);
  }
};

// 分配顾问
export const assignAdvisorToProspect = (data: AssignAdvisorRequest): Promise<boolean> => {
  if (USE_MOCK_API) {
    return assignAdvisor(data);
  } else {
    return request.put<any, boolean>('/sales/prospects/assign-advisor', data);
  }
};

// 标记无意向
export const markProspectNoIntention = (prospectId: string, reason: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return markNoIntention(prospectId, reason);
  } else {
    return request.put<any, boolean>(`/sales/prospects/${prospectId}/no-intention`, { reason });
  }
};
```

### 3.3 Mock 数据重构

**新建文件：** `src/mock/data/sales/prospects.ts`

```typescript
import type { 
  ProspectSearchParams, 
  ProspectPageResponse, 
  ProspectItem,
  CreateProspectRequest,
  FollowUpRecord,
  AssignAdvisorRequest
} from '@/types/sales/prospects';

// 动态生成模拟潜客数据
function generateMockProspects(): ProspectItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25; // 25-30条数据
  const mockData: ProspectItem[] = [];
  
  const sourceChannels = ['01050001', '01050002', '01050003', '01050004'];
  const intentLevels = ['01160001', '01160002', '01160003', '01160004'];
  const statuses = ['01380001', '01380002', '01380003', '01380004'];
  const models = ['Myvi', 'Axia', 'Bezza', 'Aruz', 'Alza'];
  const variants = ['1.3L Standard', '1.3L Premium', '1.0L Entry', '1.5L Advance'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];
  
  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      id: `PROSPECT${String(i + 1).padStart(6, '0')}`,
      name: `客户${i + 1}`,
      phoneNumber: `1${String(Math.floor(Math.random() * 900000000) + 100000000)}`,
      sourceChannel: sourceChannels[Math.floor(Math.random() * sourceChannels.length)],
      currentIntentLevel: intentLevels[Math.floor(Math.random() * intentLevels.length)],
      prospectStatus: statuses[Math.floor(Math.random() * statuses.length)],
      intentModel: models[Math.floor(Math.random() * models.length)],
      intentVariant: variants[Math.floor(Math.random() * variants.length)],
      intentColor: colors[Math.floor(Math.random() * colors.length)],
      currentSalesAdvisorId: `SA${String(Math.floor(Math.random() * 50) + 1).padStart(3, '0')}`,
      currentSalesAdvisorName: `顾问${Math.floor(Math.random() * 50) + 1}`,
      leadAssociationTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      lastFollowUpTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      nextFollowUpTime: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  
  return mockData;
}

const mockProspects = generateMockProspects();

// 获取潜客列表
export const getProspectList = (params: ProspectSearchParams): Promise<ProspectPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockProspects];
      
      // 搜索过滤
      if (params.prospectId) {
        filteredData = filteredData.filter(item => 
          item.id.toLowerCase().includes(params.prospectId!.toLowerCase())
        );
      }
      
      if (params.customerName) {
        filteredData = filteredData.filter(item => 
          item.name.includes(params.customerName!)
        );
      }
      
      if (params.customerPhone) {
        filteredData = filteredData.filter(item => 
          item.phoneNumber.includes(params.customerPhone!)
        );
      }
      
      if (params.sourceChannel) {
        filteredData = filteredData.filter(item => 
          item.sourceChannel === params.sourceChannel
        );
      }
      
      if (params.customerLevel) {
        filteredData = filteredData.filter(item => 
          item.currentIntentLevel === params.customerLevel
        );
      }
      
      if (params.customerStatus) {
        filteredData = filteredData.filter(item => 
          item.prospectStatus === params.customerStatus
        );
      }
      
      // 今日筛选
      if (params.isToday) {
        const today = new Date().toDateString();
        filteredData = filteredData.filter(item => 
          new Date(item.leadAssociationTime!).toDateString() === today
        );
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length
      });
    }, 500);
  });
};

// 获取潜客详情
export const getProspectDetail = (id: string): Promise<ProspectItem> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const prospect = mockProspects.find(item => item.id === id);
      if (prospect) {
        resolve(prospect);
      } else {
        reject(new Error('潜客不存在'));
      }
    }, 300);
  });
};

// 新增潜客
export const createProspect = (data: CreateProspectRequest): Promise<ProspectItem> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newProspect: ProspectItem = {
        id: `PROSPECT${String(mockProspects.length + 1).padStart(6, '0')}`,
        name: data.name,
        phoneNumber: data.phoneNumber,
        sourceChannel: data.sourceChannel,
        currentIntentLevel: data.intentLevel,
        prospectStatus: '01380001', // 新建状态
        currentSalesAdvisorId: data.salesAdvisorId,
        leadAssociationTime: new Date().toISOString()
      };
      
      mockProspects.push(newProspect);
      resolve(newProspect);
    }, 500);
  });
};

// 添加跟进记录
export const addFollowUpRecord = (data: FollowUpRecord): Promise<FollowUpRecord> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 更新潜客的跟进时间
      const prospect = mockProspects.find(p => p.id === data.prospectId);
      if (prospect) {
        prospect.lastFollowUpTime = data.followUpTime;
        prospect.nextFollowUpTime = data.nextFollowUpTime;
      }
      
      resolve(data);
    }, 300);
  });
};

// 分配顾问
export const assignAdvisor = (data: AssignAdvisorRequest): Promise<boolean> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const prospect = mockProspects.find(p => p.id === data.prospectId);
      if (prospect) {
        prospect.currentSalesAdvisorId = data.newAdvisorId;
        prospect.currentSalesAdvisorName = `顾问${data.newAdvisorId}`;
      }
      
      resolve(true);
    }, 300);
  });
};

// 标记无意向
export const markNoIntention = (prospectId: string, reason: string): Promise<boolean> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const prospect = mockProspects.find(p => p.id === prospectId);
      if (prospect) {
        prospect.prospectStatus = '01380004'; // 无意向状态
      }
      
      resolve(true);
    }, 300);
  });
};
```

### 3.4 国际化重构

**更新文件：** `src/locales/modules/sales/zh.json`

在现有销售模块国际化文件中添加 prospects 模块：

```json
{
  "prospects": {
    "title": "潜客管理",
    "salesProspectManagement": "销售前景管理",
    "prospectId": "潜客编号",
    "prospectName": "潜客姓名",
    "prospectPhone": "联系电话",
    "sourceChannel": "来源渠道",
    "prospectLevel": "意向等级",
    "prospectStatus": "潜客状态",
    "intentModel": "意向车型",
    "intentVariant": "意向车款",
    "intentColor": "意向颜色",
    "salesAdvisorId": "销售顾问编号",
    "salesAdvisorName": "销售顾问",
    "prospectCreationTime": "潜客创建时间",
    "lastFollowUpTime": "最后跟进时间",
    "nextFollowUpTime": "下次跟进时间",
    "markNoIntention": "标记无意向",
    "changeAdvisor": "变更顾问",
    "addProspectSuccess": "新增潜客成功",
    "followUpRecordAddSuccess": "跟进记录添加成功",
    "markNoIntentionApplySuccess": "无意向标记申请成功",
    "changeAdvisorSuccess": "顾问变更成功",
    "inputProspectId": "请输入潜客编号",
    "inputProspectName": "请输入潜客姓名",
    "inputPhoneNumber": "请输入联系电话",
    "dictionary": {
      "customerSource": {
        "01050001": "线上渠道",
        "01050002": "展厅来访",
        "01050003": "电话咨询",
        "01050004": "朋友推荐"
      },
      "intentLevel": {
        "01160001": "H级",
        "01160002": "A级",
        "01160003": "B级",
        "01160004": "C级"
      },
      "prospectStatus": {
        "01380001": "新建",
        "01380002": "跟进中",
        "01380003": "已成交",
        "01380004": "无意向"
      }
    }
  }
}
```

**更新文件：** `src/locales/modules/sales/en.json`

```json
{
  "prospects": {
    "title": "Prospect Management",
    "salesProspectManagement": "Sales Prospect Management",
    "prospectId": "Prospect ID",
    "prospectName": "Prospect Name",
    "prospectPhone": "Contact Phone",
    "sourceChannel": "Source Channel",
    "prospectLevel": "Intent Level",
    "prospectStatus": "Prospect Status",
    "intentModel": "Intent Model",
    "intentVariant": "Intent Variant",
    "intentColor": "Intent Color",
    "salesAdvisorId": "Sales Advisor ID",
    "salesAdvisorName": "Sales Advisor",
    "prospectCreationTime": "Prospect Creation Time",
    "lastFollowUpTime": "Last Follow-up Time",
    "nextFollowUpTime": "Next Follow-up Time",
    "markNoIntention": "Mark No Intention",
    "changeAdvisor": "Change Advisor",
    "addProspectSuccess": "Prospect added successfully",
    "followUpRecordAddSuccess": "Follow-up record added successfully",
    "markNoIntentionApplySuccess": "No intention mark applied successfully",
    "changeAdvisorSuccess": "Advisor changed successfully",
    "inputProspectId": "Please enter prospect ID",
    "inputProspectName": "Please enter prospect name",
    "inputPhoneNumber": "Please enter phone number",
    "dictionary": {
      "customerSource": {
        "01050001": "Online Channel",
        "01050002": "Showroom Visit",
        "01050003": "Phone Inquiry",
        "01050004": "Friend Referral"
      },
      "intentLevel": {
        "01160001": "H Level",
        "01160002": "A Level",
        "01160003": "B Level",
        "01160004": "C Level"
      },
      "prospectStatus": {
        "01380001": "New",
        "01380002": "Following Up",
        "01380003": "Closed",
        "01380004": "No Intention"
      }
    }
  }
}
```

### 3.5 主页面重构

**新建文件：** `src/views/sales/prospects/ProspectsView.vue`

```vue
<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('salesProspectManagement') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('prospectId')">
              <el-input
                v-model="searchParams.prospectId"
                :placeholder="t('inputProspectId')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospectName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('inputProspectName')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospectPhone')">
              <el-input
                v-model="searchParams.customerPhone"
                :placeholder="t('inputPhoneNumber')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('sourceChannel')">
              <el-select
                v-model="searchParams.sourceChannel"
                :placeholder="tc('all')"
                clearable
              >
                <el-option
                  v-for="option in sourceChannelOptions"
                  :key="option.code"
                  :label="t(`dictionary.customerSource.${option.code}`)"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item class="buttons-col">
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20">
      <div class="action-section">
        <el-space>
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            {{ tc('add') }}
          </el-button>
          <el-button @click="handleExport" :icon="Download">
            {{ tc('export') }}
          </el-button>
        </el-space>
      </div>
    </el-card>

    <!-- 数据展示区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="index" :label="tc('serialNumber')" width="60" />
        <el-table-column :label="t('prospectId')" prop="id" min-width="120" />
        <el-table-column :label="t('prospectName')" prop="name" min-width="120" />
        <el-table-column :label="t('prospectPhone')" prop="phoneNumber" min-width="130" />
        <el-table-column :label="t('sourceChannel')" prop="sourceChannel" min-width="120">
          <template #default="{ row }">
            {{ t(`dictionary.customerSource.${row.sourceChannel}`) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('prospectLevel')" prop="currentIntentLevel" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.currentIntentLevel)">
              {{ t(`dictionary.intentLevel.${row.currentIntentLevel}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('prospectStatus')" prop="prospectStatus" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.prospectStatus)">
              {{ t(`dictionary.prospectStatus.${row.prospectStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="tc('actions')" width="200" fixed="right">
          <template #default="{ row }">
            <el-space>
              <el-button type="primary" link @click="handleFollowUp(row)">
                {{ tc('followUp') }}
              </el-button>
              <el-button type="info" link @click="handleViewDetail(row)">
                {{ tc('details') }}
              </el-button>
              <el-button type="warning" link @click="handleMarkNoIntention(row)">
                {{ t('markNoIntention') }}
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search, Plus, Download } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getProspects } from '@/api/modules/sales/prospects';
import type { ProspectItem, ProspectSearchParams } from '@/types/sales/prospects';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 使用规范的国际化引用
const { t, tc } = useModuleI18n('sales.prospects');

// 搜索参数
const searchParams = reactive<ProspectSearchParams>({
  prospectId: '',
  customerName: '',
  customerPhone: '',
  sourceChannel: '',
  customerLevel: '',
  customerStatus: ''
});

// 加载状态
const loading = ref(false);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

const tableData = ref<ProspectItem[]>([]);

// 字典数据
const { getOptions } = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS
]);

const sourceChannelOptions = computed(() => getOptions(DICTIONARY_TYPES.CUSTOMER_SOURCE));

// 获取标签类型
const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    '01160001': 'danger',  // H级
    '01160002': 'warning', // A级
    '01160003': 'success', // B级
    '01160004': 'info'     // C级
  };
  return typeMap[level] || 'info';
};

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01380001': 'info',    // 新建
    '01380002': 'warning', // 跟进中
    '01380003': 'success', // 已成交
    '01380004': 'danger'   // 无意向
  };
  return typeMap[status] || 'info';
};

// 搜索方法
const handleSearch = async () => {
  loading.value = true;
  try {
    const response = await getProspects({
      ...searchParams,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    });
    
    tableData.value = response.list;
    pagination.total = response.total;
    
    ElMessage.success(tc('operationSuccessful'));
  } catch (error) {
    console.error('搜索失败:', error);
    ElMessage.error(tc('searchFailed'));
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    prospectId: '',
    customerName: '',
    customerPhone: '',
    sourceChannel: '',
    customerLevel: '',
    customerStatus: ''
  });
  pagination.currentPage = 1;
  handleSearch();
};

// 操作方法
const handleAdd = () => {
  // 跳转到新增页面或打开新增弹窗
  ElMessage.info('新增功能开发中');
};

const handleExport = () => {
  ElMessage.info(tc('exportingFeatureDeveloping'));
};

const handleFollowUp = (row: ProspectItem) => {
  // 跳转到跟进页面
  ElMessage.info('跟进功能开发中');
};

const handleViewDetail = (row: ProspectItem) => {
  // 跳转到详情页面
  ElMessage.info('详情功能开发中');
};

const handleMarkNoIntention = (row: ProspectItem) => {
  // 跳转到无意向标记页面
  ElMessage.info('标记无意向功能开发中');
};

// 分页方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  handleSearch();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  handleSearch();
};

// 页面加载时执行一次搜索
onMounted(() => {
  handleSearch();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

.buttons-col {
  text-align: right;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
```

### 3.6 路由配置更新

**更新文件：** `src/router/modules/sales.ts`（或相应的路由文件）

```typescript
{
  path: '/sales/prospects',
  name: 'Prospects',
  component: () => import('@/views/sales/prospects/ProspectsView.vue'),
  meta: {
    title: 'menu.prospects',
    requiresAuth: true,
    icon: 'User'
  }
}
```

## 4. 重构实施计划

### 4.1 分阶段实施

**阶段一：基础结构搭建**
- [ ] 创建新的模块化目录结构
- [ ] 建立类型定义文件
- [ ] 创建API接口模块
- [ ] 建立Mock数据

**阶段二：国际化重构**
- [ ] 更新销售模块国际化文件
- [ ] 调整国际化引用方式
- [ ] 验证国际化功能

**阶段三：页面重构**
- [ ] 重构主列表页面
- [ ] 更新路由配置
- [ ] 测试基本功能

**阶段四：组件迁移**
- [ ] 重构各个子组件为页面
- [ ] 优化用户体验
- [ ] 完善功能测试

### 4.2 重构验证清单

**目录结构验证：**
- [ ] 页面移动到 `src/views/sales/prospects/ProspectsView.vue`
- [ ] API模块创建在 `src/api/modules/sales/prospects.ts`
- [ ] Mock数据创建在 `src/mock/data/sales/prospects.ts`
- [ ] 类型定义创建在 `src/types/sales/prospects.d.ts`
- [ ] 国际化更新在 `src/locales/modules/sales/`

**功能验证：**
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 国际化切换正常
- [ ] 字典数据正确显示

**代码质量验证：**
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持搜索分页
- [ ] API调用标准化
- [ ] 组件复用和抽象合理

## 5. 预期效果

### 5.1 架构优化效果

**模块化程度提升：**
- 清晰的业务域划分（sales 模块）
- 标准化的文件组织结构
- 统一的API接口规范

**可维护性提升：**
- 独立的类型定义和Mock数据
- 规范化的国际化处理
- 清晰的组件分层

**开发效率提升：**
- 标准化的开发流程
- 可复用的组件和工具函数
- 完善的类型提示和检查

### 5.2 用户体验改善

**页面性能优化：**
- 优化的数据加载机制
- 改进的分页和搜索体验
- 更好的加载状态提示

**界面一致性：**
- 统一的设计规范
- 标准化的交互模式
- 一致的国际化体验

---

**本设计文档为潜在客户销售前景页面重构提供完整的技术方案和实施指导，确保重构过程规范化、标准化，提升代码质量和可维护性。**