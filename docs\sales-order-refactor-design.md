# DMS前端销售订单模块重构设计文档

## 1. 项目背景与目标

### 1.1 当前问题
现有销售订单相关页面存在以下架构问题：
- **页面分散**：三个核心页面分布在不同目录层级
  - `src/views/SalesOrderView.vue`（订单列表）
  - `src/views/OrderDetailView.vue`（订单详情）
  - `src/views/customerOrderManagement/SalesOrderEditView.vue`（订单编辑）
- **模块耦合**：API、类型定义、国际化文件未按业务模块组织
- **命名不一致**：路由命名与文件结构不匹配

### 1.2 重构目标
基于《页面重构技术规范.md》，实现销售订单模块的完全模块化重构，建立清晰的领域边界和可维护的架构。

## 2. 模块归属与功能划分

### 2.1 模块确定
**归属模块**：`sales`（销售模块）
**功能域**：`order`（订单管理）

### 2.2 功能映射
| 原文件路径 | 重构后功能 | 路由路径 | 说明 |
|-----------|-----------|----------|------|
| `SalesOrderView.vue` | 订单列表 | `/sales/orders` | 销售订单查询与管理 |
| `OrderDetailView.vue` | 订单详情 | `/sales/orders/:orderNo/detail` | 订单详细信息展示 |
| `SalesOrderEditView.vue` | 订单编辑 | `/sales/orders/:orderNo/edit` | 订单信息修改 |

## 3. 重构后目录结构

### 3.1 完整目录树
```
src/
├── views/
│   └── sales/
│       └── orders/
│           ├── OrdersView.vue              # 订单列表页面
│           ├── OrderDetailView.vue         # 订单详情页面
│           ├── OrderEditView.vue           # 订单编辑页面
│           └── components/                 # 订单相关组件
│               ├── OrderSearchForm.vue     # 订单搜索表单
│               ├── OrderStatusTag.vue      # 订单状态标签
│               ├── OrderBasicInfo.vue      # 订单基本信息卡片
│               └── OrderPaymentInfo.vue    # 订单支付信息卡片
├── api/
│   └── modules/
│       └── sales/
│           └── orders.ts                  # 销售订单API模块
├── types/
│   └── sales/
│       └── orders.d.ts                    # 销售订单类型定义
├── mock/
│   └── data/
│       └── sales/
│           └── orders.ts                  # 销售订单Mock数据
└── locales/
    └── modules/
        └── sales/
            ├── zh.json                    # 销售模块中文翻译
            └── en.json                    # 销售模块英文翻译
```

## 4. MyBatisPlus分页组件分析与实现

### 4.1 MyBatisPlus分页参数规范
基于后端MyBatisPlus分页组件，前端需要遵循以下分页参数规范：

#### 4.1.1 请求参数结构
```typescript
// 标准MyBatisPlus分页参数
interface PageParams {
  pageNum: number;    // 当前页码，从1开始
  pageSize: number;   // 每页条数
  total?: number;     // 总条数（响应返回）
}

// 销售订单搜索参数（继承分页参数）
interface SalesOrderSearchParams extends PageParams {
  orderNumber?: string;
  buyerName?: string;
  buyerPhone?: string;
  // ... 其他搜索条件
}
```

#### 4.1.2 响应数据结构
```typescript
// MyBatisPlus标准分页响应
interface PageResponse<T> {
  records: T[];       // 数据列表
  total: number;      // 总条数
  pageNum: number;    // 当前页码
  pageSize: number;   // 每页条数
  pages: number;      // 总页数
}

// 销售订单列表响应
interface SalesOrderListResponse extends PageResponse<SalesOrderListItem> {}
```

### 4.2 前端分页实现方案

#### 4.2.1 分页组件配置
```vue
<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="pagination.pageNum"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
});

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1; // 重置到第一页
  fetchData();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  fetchData();
};

const fetchData = async () => {
  loading.value = true;
  try {
    const response = await getSalesOrdersList({
      ...searchParams,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    });
    
    tableData.value = response.records;
    pagination.total = response.total;
  } catch (error) {
    console.error('获取数据失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>
```

#### 4.2.2 API接口适配
```typescript
// src/api/modules/sales/orders.ts
import request from '@/api';
import type { SalesOrderSearchParams, SalesOrderListResponse } from '@/types/sales/orders';

export const getSalesOrdersList = (params: SalesOrderSearchParams): Promise<SalesOrderListResponse> => {
  if (USE_MOCK_API) {
    return getMockOrdersList(params);
  }
  return request.get<any, SalesOrderListResponse>('/sales/orders/page', { params });
};
```

## 5. 数据字典实现规范

### 5.1 字典使用规范（基于ProspectsView实现）

#### 5.1.1 字典类型定义
```typescript
// src/constants/dictionary.ts
export const DICTIONARY_TYPES = {
  // 订单相关字典
  ORDER_STATUS: 'order_status',           // 订单状态
  APPROVAL_STATUS: 'approval_status',     // 审批状态
  PAYMENT_STATUS: 'payment_status',       // 支付状态
  BUYER_TYPE: 'buyer_type',              // 购车人类型
  PAYMENT_METHOD: 'payment_method',       // 支付方式
  INSURANCE_STATUS: 'insurance_status',   // 保险状态
  JPJ_REGISTRATION_STATUS: 'jpj_registration_status' // JPJ注册状态
} as const;
```

#### 5.1.2 字典展示实现
```typescript
// 使用useBatchDictionary组合式函数
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.BUYER_TYPE,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.INSURANCE_STATUS
]);

// 获取选项列表
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS));

// 获取字典名称
const formatOrderStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;
};
```

### 5.2 表格中字典字段展示

#### 5.2.1 标准展示方式
```vue
<el-table-column :label="t('orders.orderStatus')" prop="orderStatus" min-width="120">
  <template #default="{ row }">
    <el-tag :type="getOrderStatusType(row.orderStatus)">
      {{ getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
    </el-tag>
  </template>
</el-table-column>

<el-table-column :label="t('orders.buyerType')" prop="buyerType" min-width="100">
  <template #default="{ row }">
    {{ getNameByCode(DICTIONARY_TYPES.BUYER_TYPE, row.buyerType) }}
  </template>
</el-table-column>
```

#### 5.2.2 标签样式映射
```typescript
// 状态标签样式映射
const getOrderStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'submitted': 'info',
    'confirmed': 'success',
    'pending_delivery': 'warning',
    'completed': 'success',
    'cancel_pending': 'warning',
    'cancel_approved': 'danger',
    'canceled': 'danger'
  };
  return typeMap[status] || 'info';
};

const getPaymentStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'fully_paid': 'success',
    'pending_deposit': 'warning',
    'pending_final_payment': 'warning',
    'refund_completed': 'danger'
  };
  return typeMap[status] || 'info';
};
```

### 5.3 搜索表单字典实现

#### 5.3.1 下拉选择框
```vue
<el-form-item :label="t('orders.orderStatus')">
  <el-select
    v-model="searchParams.orderStatus"
    :placeholder="tc('all')"
    clearable
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in orderStatusOptions"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>

<el-form-item :label="t('orders.buyerType')">
  <el-select
    v-model="searchParams.buyerType"
    :placeholder="tc('all')"
    clearable
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in buyerTypeOptions"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>
```

### 5.4 字典国际化配置

#### 5.4.1 销售模块国际化
```json
// src/locales/modules/sales/zh.json
{
  "orders": {
    "list": {
      "title": "销售订单列表",
      "columns": {
        "orderNumber": "订单编号",
        "buyerName": "购车人姓名",
        "buyerPhone": "购车人电话",
        "buyerType": "购车人类型",
        "orderStatus": "订单状态",
        "approvalStatus": "审批状态",
        "paymentStatus": "支付状态",
        "insuranceStatus": "保险状态"
      }
    },
    "status": {
      "submitted": "已提交",
      "confirmed": "已确认",
      "pending_delivery": "待交付",
      "completed": "已完成",
      "canceled": "已取消"
    },
    "buyerType": {
      "individual": "个人",
      "company": "公司"
    }
  }
}
```

## 6. 技术实现方案

### 6.1 API模块重构
```typescript
// src/api/modules/sales/orders.ts
import request from '@/api';
import type {
  SalesOrderListParams,
  SalesOrderListResponse,
  SalesOrderDetail,
  SaveOrderRequest
} from '@/types/sales/orders';
import { getOrdersList as getMockOrdersList } from '@/mock/data/sales/orders';
import { USE_MOCK_API } from '@/utils/mock-config';

// 订单列表
export const getSalesOrdersList = (params: SalesOrderListParams): Promise<SalesOrderListResponse> => {
  if (USE_MOCK_API) {
    return getMockOrdersList(params);
  }
  return request.get<any, SalesOrderListResponse>('/sales/orders/page', { params });
};

// 订单详情
export const getSalesOrderDetail = (orderNo: string): Promise<SalesOrderDetail> => {
  if (USE_MOCK_API) {
    return Promise.resolve(generateMockOrderDetail(orderNo));
  }
  return request.get<any, SalesOrderDetail>(`/sales/orders/${orderNo}`);
};

// 保存订单
export const saveSalesOrder = (data: SaveOrderRequest): Promise<any> => {
  if (USE_MOCK_API) {
    return Promise.resolve({ message: '保存成功' });
  }
  return request.put<any, any>(`/sales/orders/${data.orderNo}`, data);
};
```

### 6.2 类型定义重构
```typescript
// src/types/sales/orders.d.ts
export interface SalesOrderListParams {
  pageNum?: number;
  pageSize?: number;
  orderNumber?: string;
  buyerName?: string;
  buyerPhone?: string;
  buyerType?: string;
  orderStatus?: string;
  approvalStatus?: string;
  paymentStatus?: string;
  insuranceStatus?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
}

export interface SalesOrderListItem {
  id: string;
  orderNo: string;
  createTime: string;
  customerName: string;
  customerPhone: string;
  buyerName: string;
  buyerPhone: string;
  buyerType: string;
  model: string;
  variant: string;
  color: string;
  vin: string;
  paymentMethod: string;
  orderStatus: string;
  approvalStatus: string;
  paymentStatus: string;
  insuranceStatus: string;
  jpjRegistrationStatus: string;
  totalAmount: number;
}

export interface SalesOrderListResponse {
  records: SalesOrderListItem[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

export interface SalesOrderDetail {
  id: string;
  orderNo: string;
  createTime: string;
  customerName: string;
  customerPhone: string;
  buyerName: string;
  buyerPhone: string;
  buyerType: string;
  model: string;
  variant: string;
  color: string;
  vin: string;
  // ... 其他详情字段
}

export interface SaveOrderRequest {
  orderNo: string;
  color?: string;
  selectedRights?: string[];
  paymentMethod?: string;
  loanAmount?: number;
  loanTerm?: number;
  loanApprovalStatus?: string;
  insuranceNotes?: string;
}
```

### 6.3 Mock数据重构
```typescript
// src/mock/data/sales/orders.ts
import type { SalesOrderListParams, SalesOrderListResponse } from '@/types/sales/orders';

// 动态生成订单数据
function generateMockOrders(count: number) {
  const orders = [];
  const statuses = ['submitted', 'confirmed', 'pending_delivery', 'completed', 'canceled'];
  const buyerTypes = ['individual', 'company'];
  
  for (let i = 0; i < count; i++) {
    orders.push({
      id: `order_${i + 1}`,
      orderNo: `SO${String(i + 1).padStart(8, '0')}`,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      customerName: `客户${i + 1}`,
      customerPhone: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
      buyerName: `购车人${i + 1}`,
      buyerPhone: `139${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
      buyerType: buyerTypes[Math.floor(Math.random() * buyerTypes.length)],
      model: ['Myvi', 'Ativa', 'Alza'][Math.floor(Math.random() * 3)],
      variant: ['1.3L Standard', '1.5L Premium', '1.5L Advance'][Math.floor(Math.random() * 3)],
      color: ['白色', '黑色', '银色', '红色'][Math.floor(Math.random() * 4)],
      vin: `VIN${String(Math.floor(Math.random() * *********00000000)).padStart(17, '0')}`,
      paymentMethod: ['full_payment', 'installment'][Math.floor(Math.random() * 2)],
      orderStatus: statuses[Math.floor(Math.random() * statuses.length)],
      approvalStatus: ['pending_approval', 'approved', 'rejected'][Math.floor(Math.random() * 3)],
      paymentStatus: ['pending_deposit', 'fully_paid', 'refund_completed'][Math.floor(Math.random() * 3)],
      insuranceStatus: ['not_insured', 'pending', 'insured'][Math.floor(Math.random() * 3)],
      jpjRegistrationStatus: ['pending_registration', 'registering', 'registered', 'registration_failed'][Math.floor(Math.random() * 4)],
      totalAmount: Math.floor(Math.random() * 100000) + 50000
    });
  }
  return orders;
}

export const getOrdersList = (params: SalesOrderListParams): Promise<SalesOrderListResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const allOrders = generateMockOrders(28); // 生成28条数据用于分页测试
      
      // 搜索过滤逻辑
      let filteredOrders = [...allOrders];
      
      if (params.orderNumber) {
        filteredOrders = filteredOrders.filter(order => 
          order.orderNo.includes(params.orderNumber)
        );
      }
      
      if (params.buyerName) {
        filteredOrders = filteredOrders.filter(order => 
          order.buyerName.includes(params.buyerName)
        );
      }
      
      if (params.orderStatus) {
        filteredOrders = filteredOrders.filter(order => 
          order.orderStatus === params.orderStatus
        );
      }
      
      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 10;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        records: filteredOrders.slice(start, end),
        total: filteredOrders.length,
        pageNum,
        pageSize,
        pages: Math.ceil(filteredOrders.length / pageSize)
      });
    }, 500);
  });
};
```

### 6.4 路由配置更新
```typescript
// src/router/index.ts
{
  path: '/sales/orders',
  name: 'sales-orders',
  component: () => import('@/views/sales/orders/OrdersView.vue'),
  meta: {
    title: 'menu.salesOrders',
    requiresAuth: true,
    icon: 'List'
  }
},
{
  path: '/sales/orders/:orderNo/detail',
  name: 'sales-order-detail',
  component: () => import('@/views/sales/orders/OrderDetailView.vue'),
  meta: {
    title: 'menu.salesOrderDetail',
    requiresAuth: true,
    hideInMenu: true
  }
},
{
  path: '/sales/orders/:orderNo/edit',
  name: 'sales-order-edit',
  component: () => import('@/views/sales/orders/OrderEditView.vue'),
  meta: {
    title: 'menu.salesOrderEdit',
    requiresAuth: true,
    hideInMenu: true
  }
}
```

### 6.5 国际化整合
```json
// src/locales/modules/sales/zh.json
{
  "orders": {
    "title": "销售订单",
    "list": {
      "title": "销售订单列表",
      "search": {
        "buyerName": "购车人姓名",
        "orderNumber": "订单编号",
        "orderStatus": "订单状态",
        "buyerType": "购车人类型"
      }
    },
    "detail": {
      "title": "订单详情",
      "customerInfo": "客户信息"
    },
    "edit": {
      "title": "订单编辑",
      "saveSuccess": "保存成功"
    }
  }
}
```

## 7. 重构执行计划

### 7.1 执行顺序
1. **阶段一**：创建目录结构（10分钟）
2. **阶段二**：迁移类型定义（15分钟）
3. **阶段三**：迁移API模块（15分钟）
4. **阶段四**：迁移Mock数据（15分钟）
5. **阶段五**：迁移页面文件（30分钟）
6. **阶段六**：更新路由配置（10分钟）
7. **阶段七**：验证功能完整性（20分钟）

### 7.2 验证清单
- [ ] 所有页面可正常访问
- [ ] 订单列表搜索、分页功能正常
- [ ] 订单详情页面数据展示正确
- [ ] 订单编辑功能正常
- [ ] Mock数据动态生成（28条）
- [ ] 国际化正常工作
- [ ] 控制台无错误

## 8. 风险与兼容性

### 8.1 兼容性处理
- **旧路由重定向**：添加路由重定向规则
- **API兼容**：保持原有API接口不变，仅更新内部组织方式
- **数据格式**：保持原有数据格式，确保前端无感知迁移

### 8.2 回滚方案
- 保留原文件备份，如有问题可快速回滚
- 分阶段提交，每个阶段可独立回滚

## 9. 后续优化建议

### 9.1 组件化分解
- 将订单详情页面分解为多个可复用组件
- 提取公共的订单状态展示组件
- 创建订单操作按钮组组件

### 9.2 性能优化
- 实现订单列表虚拟滚动
- 添加订单详情页面缓存
- 优化大表单的性能

---

**文档版本**：1.0  
**最后更新**：2025-07-22  
**作者**：前端实现专家（cc-fe）