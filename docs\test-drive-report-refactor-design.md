# TestDriveReport 重构设计文档

> **重要说明**: 本设计文档已根据 `/规范/页面目录结构规范.md` 进行审查和修正，确保完全符合DMS前端项目的目录结构规范。
>
> **模块归属说明**: 试驾报表功能归属于 `sales` 模块，因为试驾是销售流程中的重要环节，试驾数据统计和报表分析直接服务于销售业务管理和决策。

## 1. 重构目标与范围

### 1.1 重构目标
将 `src/views/factory-report/test-drive-report` 按照DMS前端重构技术规范进行模块化重构，实现：
- 符合目录结构规范的模块化组织
- 统一的MyBatisPlus分页组件标准化
- 规范的数据字典实现方式
- 标准的API请求响应处理

### 1.2 重构范围
- **源目录**: `src/views/factory-report/test-drive-report/`
- **目标模块**: `sales` (销售模块 - 试驾报表属于销售业务范畴)
- **功能名称**: `testDriveReport` (试驾报表)

## 2. 现状分析

### 2.1 当前文件结构问题
```
❌ 当前结构:
src/views/factory-report/test-drive-report/
├── index.vue                           # 主页面文件
├── types.ts                           # 类型定义
└── components/
    └── TestDriveDetailModal.vue       # 详情模态框

✅ 目标结构:
src/views/sales/testDriveReport/
├── TestDriveReportView.vue            # 主页面文件（路由页面）
└── components/
    └── TestDriveDetailModal.vue       # 详情模态框（非路由页面）
src/api/modules/sales/
└── testDriveReport.ts                 # API模块
src/types/sales/
└── testDriveReport.d.ts               # 类型定义
src/mock/data/sales/
└── testDriveReport.ts                 # Mock数据
```

### 2.2 MyBatisPlus分页问题分析
**当前实现问题**:
```typescript
// ❌ 分页参数不符合MyBatisPlus标准
interface QueryParams {
  pageNum?: number;     // ✅ 参数名正确
  pageSize?: number;    // ✅ 参数名正确
  // ... 其他参数
}

// ❌ 分页状态不符合标准
const pagination = reactive({
  page: 1,              // ❌ 应为 pageNum
  pageSize: 20,         // ✅ 正确
  itemCount: 0,         // ❌ 应为 total
  pageSizes: [10, 20, 50, 100]
});

// ❌ 分页组件绑定不符合标准
<el-pagination
  v-model:current-page="pagination.page"      // ❌ 应绑定 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.itemCount"               // ❌ 应为 total
/>

// ✅ 响应处理正确
const result = res.result as any;
if (result && result.records) {
  tableData.value = result.records;           // ✅ 正确使用 records
  pagination.itemCount = result.total;        // ✅ 正确使用 total
}
```

**目标实现**:
```typescript
// ✅ 标准MyBatisPlus分页参数
interface TestDriveSearchParams {
  pageNum?: number;     // MyBatisPlus标准参数
  pageSize?: number;    // MyBatisPlus标准参数
  // ... 其他搜索参数
}

// ✅ 标准分页状态
const pagination = reactive({
  pageNum: 1,           // 修正为 pageNum
  pageSize: 20,
  total: 0,             // 修正为 total
});

// ✅ 标准分页组件绑定
<el-pagination
  v-model:current-page="pagination.pageNum"
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
/>
```

### 2.3 数据字典实现问题分析
**当前实现问题**:
```typescript
// ❌ 硬编码选项数据
const storeOptions = ref<{label: string, value: number}[]>([
  { label: '北京朝阳门店', value: 1001 },
  { label: '北京海淀门店', value: 1002 },
  // ...
]);

const modelOptions = ref<{label: string, value: string}[]>([
  { label: 'ModelX', value: 'ModelX' },
  { label: 'ModelY', value: 'ModelY' },
]);

// ❌ 缺少数据字典转义
<el-table-column prop="storeName" label="门店" width="120" />
<el-table-column prop="model" label="试驾车型 (Model)" width="180" />
```

**目标实现** (参考ProspectsView.vue):
```typescript
// ✅ 使用标准数据字典
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.TEST_DRIVE_STATUS
]);

// ✅ 标准字典转义
<el-table-column prop="storeId" label="门店" width="120">
  <template #default="{ row }">
    {{ getNameByCode(DICTIONARY_TYPES.STORE, row.storeId) }}
  </template>
</el-table-column>
```

### 2.4 API响应处理分析
**当前实现**:
```typescript
// ✅ 正确的响应处理
const res = await getTestDriveReportList(params);
const result = res.result as any;           // ✅ 正确使用 result
if (result && result.records) {
  tableData.value = result.records;         // ✅ 正确使用 records
  pagination.itemCount = result.total;      // ✅ 正确使用 total
}
```

**评估结果**: 当前API响应处理已符合标准，使用了`response.result`模式。

### 2.5 国际化问题分析
**当前实现问题**:
```typescript
// ❌ 硬编码中文文本
<h1 class="page-title">厂端试驾报表</h1>
<div class="stat-title">本月试驾次数</div>
<el-form-item label="门店">
<el-button @click="handleReset">重置</el-button>
```

**目标实现**:
```typescript
// ✅ 使用国际化
const { t, tc } = useModuleI18n('factoryReport.testDrive');

<h1 class="page-title">{{ t('title') }}</h1>
<div class="stat-title">{{ t('stats.monthlyTestDrives') }}</div>
<el-form-item :label="t('store')">
<el-button @click="handleReset">{{ tc('reset') }}</el-button>
```

## 3. 重构技术方案

### 3.1 目录结构重构

#### 3.1.1 创建目标目录结构
```bash
# 创建模块化目录
mkdir -p src/views/sales/testDriveReport/components
mkdir -p src/api/modules/sales
mkdir -p src/types/sales
mkdir -p src/mock/data/sales
```

#### 3.1.2 文件迁移映射
```
源文件 → 目标文件:
src/views/factory-report/test-drive-report/index.vue
→ src/views/sales/testDriveReport/TestDriveReportView.vue

src/views/factory-report/test-drive-report/types.ts
→ src/types/sales/testDriveReport.d.ts

src/views/factory-report/test-drive-report/components/TestDriveDetailModal.vue
→ src/views/sales/testDriveReport/components/TestDriveDetailModal.vue

新增文件:
src/api/modules/sales/testDriveReport.ts           # API模块
src/mock/data/sales/testDriveReport.ts             # Mock数据
```

### 3.2 MyBatisPlus分页标准化实现

#### 3.2.1 分页参数规范修正
```typescript
// 修正分页搜索参数接口
export interface TestDriveSearchParams {
  pageNum?: number;        // ✅ 保持MyBatisPlus标准参数
  pageSize?: number;       // ✅ 保持MyBatisPlus标准参数
  model?: string;
  variant?: number;
  storeIds?: number[];
  startTimeBegin?: string;
  startTimeEnd?: string;
}
```

#### 3.2.2 分页响应结构标准化
```typescript
// 标准MyBatisPlus分页响应
export interface TestDrivePageResponse {
  result: {
    records: TestDriveItem[];    // ✅ MyBatisPlus标准响应
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}
```

#### 3.2.3 分页组件配置修正
```vue
<template>
  <div class="pagination-section mt-20">
    <el-pagination
      v-model:current-page="pagination.pageNum"    <!-- ✅ 修正为pageNum -->
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"                    <!-- ✅ 修正为total -->
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
// 分页状态修正
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 20,
  total: 0           // ✅ 修正为total
});

// 分页处理函数修正
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadTableData();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;  // ✅ 修正为pageNum
  loadTableData();
};

// API调用参数修正
const buildQueryParams = () => {
  const params: TestDriveSearchParams = {
    pageNum: pagination.pageNum,     // ✅ 修正为pageNum
    pageSize: pagination.pageSize,
    // ... 其他参数
  };
  return params;
};

// 响应处理修正
const loadTableData = async () => {
  try {
    const response = await getTestDriveList(params);
    tableData.value = response.result.records;
    pagination.total = response.result.total;    // ✅ 修正为total
  } catch (error) {
    // 错误处理
  }
};
</script>
```

### 3.3 数据字典标准化实现

#### 3.3.1 字典类型定义
```typescript
// src/constants/dictionary.ts (新增)
export const DICTIONARY_TYPES = {
  // 试驾报表相关字典
  STORE: '01300001',                    // 门店
  VEHICLE_MODEL: '01300002',            // 车型
  VEHICLE_VARIANT: '01300003',          // 配置
  CUSTOMER_SOURCE: '01300004',          // 客户来源
  TEST_DRIVE_STATUS: '01300005',        // 试驾状态
  ID_TYPE: '01300006',                  // 证件类型
} as const;
```

#### 3.3.2 字典使用标准化
```typescript
// 引入标准字典组合式函数
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 批量获取字典数据
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.ID_TYPE
]);

// 标准字典转义函数
const getTestDriveDisplay = (type: string, code: string) => {
  return getNameByCode(type, code) || code;
};
```

#### 3.3.3 表格字段标准化实现
```vue
<!-- 门店列 -->
<el-table-column prop="storeId" label="门店" width="120">
  <template #default="{ row }">
    {{ getNameByCode(DICTIONARY_TYPES.STORE, row.storeId) }}
  </template>
</el-table-column>

<!-- 车型列 -->
<el-table-column prop="modelId" label="试驾车型" width="180">
  <template #default="{ row }">
    {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, row.modelId) }}
  </template>
</el-table-column>

<!-- 客户来源列 -->
<el-table-column prop="sourceChannel" label="客户来源" width="120">
  <template #default="{ row }">
    {{ getNameByCode(DICTIONARY_TYPES.CUSTOMER_SOURCE, row.sourceChannel) }}
  </template>
</el-table-column>
```

#### 3.3.4 搜索表单标准化实现
```vue
<!-- 门店下拉选择 -->
<el-form-item :label="t('store')">
  <el-select
    v-model="filterForm.storeIds"
    :placeholder="tc('all')"
    clearable
    multiple
  >
    <el-option
      v-for="option in getOptions(DICTIONARY_TYPES.STORE)"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>

<!-- 车型下拉选择 -->
<el-form-item :label="t('model')">
  <el-select
    v-model="filterForm.model"
    :placeholder="tc('all')"
    clearable
    @change="handleModelChange"
  >
    <el-option
      v-for="option in getOptions(DICTIONARY_TYPES.VEHICLE_MODEL)"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>
```

### 3.4 API请求响应处理标准化

#### 3.4.1 API模块重构
```typescript
// src/api/modules/sales/testDriveReport.ts
import request from '@/api';
import type { TestDriveSearchParams, TestDrivePageResponse, TestDriveStats } from '@/types/sales/testDriveReport';
import { getTestDriveList, getTestDriveStats } from '@/mock/data/sales/testDriveReport';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getTestDriveReportList = (params: TestDriveSearchParams): Promise<TestDrivePageResponse> => {
  if (USE_MOCK_API) {
    return getTestDriveList(params);
  } else {
    return request.post<any, TestDrivePageResponse>('/sales/test-drive-report/list', params);
  }
};

export const getTestDriveReportStats = (): Promise<{ result: TestDriveStats }> => {
  if (USE_MOCK_API) {
    return getTestDriveStats();
  } else {
    return request.get<any, { result: TestDriveStats }>('/sales/test-drive-report/statistics');
  }
};

export const getTestDriveDetail = (testDriveNo: string): Promise<{ result: TestDriveItem }> => {
  if (USE_MOCK_API) {
    return getTestDriveDetailMock(testDriveNo);
  } else {
    return request.get<any, { result: TestDriveItem }>(`/sales/test-drive/detail/${testDriveNo}`);
  }
};

export const exportTestDriveReport = (params: TestDriveSearchParams): Promise<Blob> => {
  return request.post<any, Blob>('/sales/test-drive-report/export', params, { responseType: 'blob' });
};
```

#### 3.4.2 响应处理标准化 (已符合标准)
```typescript
// ✅ 当前实现已正确，保持标准响应处理
const loadTableData = async () => {
  loading.value = true;
  try {
    const response = await getTestDriveReportList(params);

    // 标准响应处理：使用response.result获取数据
    tableData.value = response.result.records;
    pagination.total = response.result.total;

  } catch (error) {
    console.error('获取试驾报表失败:', error);
    ElMessage.error(tc('fetchFailed'));
  } finally {
    loading.value = false;
  }
};
```

### 3.5 国际化文件更新

#### 3.5.1 国际化文件结构
```json
// src/locales/modules/sales/zh.json
{
  "testDriveReport": {
    "title": "试驾报表",
    "stats": {
      "monthlyTestDrives": "本月试驾次数",
      "todayTestDrives": "今日试驾次数",
      "topStore": "试驾次数最多的门店",
      "topModel": "试驾量最多的车型"
    },
    "filter": {
      "store": "门店",
      "model": "试驾车型",
      "variant": "试驾配置",
      "dateRange": "试驾时间"
    },
    "table": {
      "testDriveNo": "试驾单号",
      "storeName": "门店",
      "createTime": "试驾单录入时间",
      "customerName": "试驾人",
      "customerPhone": "试驾人手机号",
      "model": "试驾车型",
      "variant": "试驾配置",
      "mileage": "里程数",
      "startTime": "试驾开始时间",
      "endTime": "试驾结束时间",
      "consultantName": "销售顾问"
    },
    "detail": {
      "title": "试驾单详情",
      "customerInfo": "潜客信息",
      "testDriveInfo": "试驾信息",
      "sourceChannel": "潜客来源",
      "customerName": "潜客名称",
      "customerPhone": "潜客手机号",
      "idType": "身份证件类别",
      "idNumber": "潜客证件号",
      "email": "潜客邮箱",
      "driverName": "试驾人",
      "driverPhone": "试驾人手机号",
      "driverIdType": "试驾人证件类型",
      "driverIdNumber": "试驾人证件号",
      "startMileage": "开始里程数",
      "endMileage": "结束里程数",
      "feedback": "试驾反馈"
    }
  }
}
```

#### 3.5.2 国际化引用更新
```typescript
// 更新国际化引用
const { t, tc } = useModuleI18n('sales.testDriveReport');
```

### 3.6 路由配置更新

```typescript
// src/router/modules/sales.ts
{
  path: '/sales/test-drive-report',
  name: 'SalesTestDriveReport',
  component: () => import('@/views/sales/testDriveReport/TestDriveReportView.vue'),
  meta: {
    title: 'menu.salesTestDriveReport',
    requiresAuth: true,
    icon: 'Document'
  }
}
```

## 4. 重构实施计划

### 4.1 重构步骤
1. **创建目录结构** - 建立模块化目录
2. **类型定义重构** - 提取并标准化类型定义
3. **Mock数据重构** - 创建模块化Mock数据
4. **API模块重构** - 创建标准化API模块
5. **MyBatisPlus分页修正** - 修正分页参数和组件绑定
6. **数据字典集成** - 替换硬编码为标准字典
7. **国际化集成** - 替换硬编码文本为国际化
8. **页面文件重构** - 重构主页面文件
9. **路由配置更新** - 更新路由配置

### 4.2 重构检查清单

#### 4.2.1 目录结构验证
- [ ] 页面文件移动到 `src/views/sales/testDriveReport/TestDriveReportView.vue`
- [ ] API模块创建在 `src/api/modules/sales/testDriveReport.ts`
- [ ] Mock数据创建在 `src/mock/data/sales/testDriveReport.ts`
- [ ] 类型定义创建在 `src/types/sales/testDriveReport.d.ts`
- [ ] 国际化文件更新在 `src/locales/modules/sales/`

#### 4.2.2 MyBatisPlus分页验证
- [ ] 分页状态修正为 `pageNum` 和 `total`
- [ ] 分页组件绑定修正为标准参数
- [ ] API调用使用标准 `pageNum` 参数
- [x] 响应数据使用 `response.result.records` (已符合)

#### 4.2.3 数据字典验证
- [ ] 使用 `useBatchDictionary` 获取字典数据
- [ ] 使用 `getNameByCode` 进行字段转义
- [ ] 使用 `getOptions` 获取下拉选项
- [ ] 移除硬编码选项数据
- [ ] 统一字典转义显示

#### 4.2.4 国际化验证
- [ ] 移除硬编码中文文本
- [ ] 使用 `useModuleI18n` 获取翻译函数
- [ ] 统一使用 `t()` 和 `tc()` 进行翻译
- [ ] 创建完整的国际化文件

#### 4.2.5 API响应处理验证
- [x] 使用标准 `response.result` 获取数据 (已符合)
- [x] 统一错误处理机制 (已符合)
- [x] 正确的响应码处理 (已符合)

#### 4.2.6 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 字典转义正常显示
- [ ] 国际化切换正常
- [ ] 统计数据正常显示
- [ ] 导出功能正常工作
- [ ] 详情模态框正常显示
- [ ] 控制台无错误信息

## 5. 风险评估与注意事项

### 5.1 低风险项
- **API响应处理**: 当前实现已符合标准，风险极低
- **类型定义**: 已有基础类型定义，迁移风险低

### 5.2 中等风险项
- **MyBatisPlus分页修正**: 需要修改分页状态和组件绑定
- **数据字典集成**: 需要替换大量硬编码选项数据
- **国际化集成**: 需要替换所有硬编码中文文本

### 5.3 高风险项
- **级联选择逻辑**: 车型-配置的级联选择需要重新实现
- **统计数据处理**: 需要确保统计API的正确调用

### 5.4 注意事项
1. **保持功能不变**: 重构过程中不改变任何业务逻辑
2. **渐进式重构**: 按步骤逐一验证，确保每步正常
3. **充分测试**: 重点测试分页、字典转义和级联选择功能
4. **备份原文件**: 重构前备份原始文件
5. **数据脱敏**: 确保详情模态框的数据脱敏功能正常

## 6. 详细技术实现

### 6.1 类型定义文件
```typescript
// src/types/sales/testDriveReport.d.ts
export interface TestDriveItem {
  id: string;
  testDriveNo: string;
  customerName: string;
  customerPhone: string;
  model: string;
  variant: string;
  startTime: string;
  endTime: string;
  mileage: number;
  consultantName: string;
  storeName: string;
  createTime: string;

  // 详情字段
  customerId?: number;
  customerIdType?: string;
  customerIdNumber?: string;
  driverName?: string;
  driverPhone?: string;
  driverIdType?: string;
  driverIdNumber?: string;
  source?: string;
  email?: string;
  driverLicenseNumber?: string;
  startMileage?: number;
  endMileage?: number;
  feedback?: string;
  consultantId?: number;
  storeId?: number;
  storeRegion?: string;
  updateTime?: string;
}

export interface TestDriveSearchParams {
  pageNum?: number;
  pageSize?: number;
  model?: string;
  variant?: number;
  storeIds?: number[];
  startTimeBegin?: string;
  startTimeEnd?: string;
}

export interface TestDrivePageResponse {
  result: {
    records: TestDriveItem[];
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}

export interface TestDriveStats {
  monthlyCount: number;
  dailyCount: number;
  topStore: string;
  topModel: string;
}
```

### 6.2 Mock数据实现
```typescript
// src/mock/data/sales/testDriveReport.ts
import type { TestDriveSearchParams, TestDrivePageResponse, TestDriveStats } from '@/types/sales/testDriveReport';

// 动态生成模拟数据
function generateMockData() {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData = [];

  const storeOptions = ['01300001001', '01300001002', '01300001003'];
  const modelOptions = ['01300002001', '01300002002'];
  const sourceOptions = ['01300004001', '01300004002', '01300004003'];

  for (let i = 0; i < dataCount; i++) {
    const startTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const endTime = new Date(startTime.getTime() + Math.random() * 2 * 60 * 60 * 1000);

    mockData.push({
      id: `TD${String(i + 1).padStart(6, '0')}`,
      testDriveNo: `TD${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
      customerName: `客户${i + 1}`,
      customerPhone: `138${String(Math.floor(Math.random() * 99999999)).padStart(8, '0')}`,
      model: modelOptions[Math.floor(Math.random() * modelOptions.length)],
      variant: `配置${Math.floor(Math.random() * 3) + 1}`,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      mileage: Math.floor(Math.random() * 50) + 10,
      consultantName: `顾问${Math.floor(Math.random() * 10) + 1}`,
      storeName: storeOptions[Math.floor(Math.random() * storeOptions.length)],
      createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      storeId: storeOptions[Math.floor(Math.random() * storeOptions.length)],
      sourceChannel: sourceOptions[Math.floor(Math.random() * sourceOptions.length)],
      email: `customer${i + 1}@example.com`,
      feedback: `试驾体验${Math.floor(Math.random() * 5) + 1}分`,
      startMileage: Math.floor(Math.random() * 1000) + 5000,
      endMileage: Math.floor(Math.random() * 1000) + 5050
    });
  }

  return mockData;
}

const mockData = generateMockData();

export const getTestDriveList = (params: TestDriveSearchParams): Promise<TestDrivePageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.model) {
        filteredData = filteredData.filter(item => item.model === params.model);
      }

      if (params.storeIds && params.storeIds.length > 0) {
        filteredData = filteredData.filter(item =>
          params.storeIds!.includes(item.storeId)
        );
      }

      if (params.startTimeBegin && params.startTimeEnd) {
        filteredData = filteredData.filter(item => {
          const itemTime = new Date(item.startTime);
          return itemTime >= new Date(params.startTimeBegin!) &&
                 itemTime <= new Date(params.startTimeEnd!);
        });
      }

      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        }
      });
    }, 500);
  });
};

export const getTestDriveStats = (): Promise<{ result: TestDriveStats }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        result: {
          monthlyCount: Math.floor(Math.random() * 500) + 100,
          dailyCount: Math.floor(Math.random() * 20) + 5,
          topStore: '北京朝阳门店',
          topModel: 'ModelX'
        }
      });
    }, 300);
  });
};
```

## 7. 页面目录结构规范符合性检查

### 7.1 规范符合性验证
根据 `/规范/页面目录结构规范.md` 进行的符合性检查：

#### 7.1.1 目录结构规范 ✅
- [x] **路由页面位置**: `src/views/sales/testDriveReport/TestDriveReportView.vue` - 符合 `src/views/模块名/功能名/功能名View.vue` 规范
- [x] **非路由页面位置**: `src/views/sales/testDriveReport/components/TestDriveDetailModal.vue` - 符合非路由页面统一放在 `components/` 目录的规范
- [x] **API模块位置**: `src/api/modules/sales/testDriveReport.ts` - 符合模块化API结构
- [x] **类型定义位置**: `src/types/sales/testDriveReport.d.ts` - 符合模块化类型结构
- [x] **Mock数据位置**: `src/mock/data/sales/testDriveReport.ts` - 符合模块化Mock结构
- [x] **国际化位置**: `src/locales/modules/sales/` - 符合模块化国际化结构

#### 7.1.2 命名规范 ✅
- [x] **路由页面命名**: `TestDriveReportView.vue` - 符合 `功能名View.vue` 格式
- [x] **非路由页面命名**: `TestDriveDetailModal.vue` - 符合组件命名规范
- [x] **模块名称**: `sales` - 使用标准模块名称
- [x] **功能名称**: `testDriveReport` - 使用小驼峰命名法

#### 7.1.3 页面类型分类 ✅
- [x] **路由页面**: `TestDriveView.vue` 作为试驾报表列表页，直接放在功能目录下
- [x] **非路由页面**: `TestDriveDetailModal.vue` 作为详情模态框，放在 `components/` 目录下
- [x] **组件分类**: 明确区分了路由页面和非路由页面的存放位置

#### 7.1.4 模块一致性 ✅
- [x] **API模块结构**: 与页面结构保持一致
- [x] **Mock数据结构**: 与API模块结构保持一致
- [x] **类型定义结构**: 与功能模块保持一致
- [x] **国际化文件**: 按模块正确组织
- [x] **路由配置**: 按模块正确组织

### 7.2 规范遵循要点
1. **功能内聚**: 试驾报表相关的所有文件统一放在 `sales/testDriveReport/` 目录下
2. **清晰分类**: 路由页面直接放在功能目录下，非路由页面和组件统一放在 `components/` 子目录
3. **模块化组织**: API、Mock、类型定义、国际化文件都按相同的模块结构组织
4. **命名一致性**: 所有文件和目录命名都遵循统一的命名规范
5. **页面类型区分**: 明确区分路由页面和非路由页面的存放规则
6. **标准模块使用**: 使用项目标准的 `sales` 模块，而非自定义模块名

---

**本设计文档基于DMS前端重构技术规范制定，已完全符合页面目录结构规范，为TestDriveReport的标准化重构提供详细指导。重构完成后将实现完全的模块化组织和规范化实现。**
