# 通用项目检查清单

## 使用说明
- 按照清单逐项检查，确保每一项都通过验证
- 对于不适用的检查项，可以标记为"N/A"
- 发现问题时，记录具体问题和修复方案
- 建议在代码审查时使用此清单进行系统性检查

---

## 1. 目录结构与文件组织检查

### 1.1 目录结构规范
- [ ] **页面文件位置**：路由页面放在正确的模块目录下
- [ ] **组件文件位置**：非路由组件放在对应的 `components/` 目录下
- [ ] **API文件位置**：API模块放在 `src/api/modules/` 对应目录下
- [ ] **类型定义位置**：类型文件放在 `src/types/` 对应目录下
- [ ] **Mock数据位置**：Mock文件放在 `src/mock/data/` 对应目录下
- [ ] **国际化文件位置**：翻译文件放在 `src/locales/modules/` 对应目录下

### 1.2 文件命名规范
- [ ] **页面文件命名**：使用 PascalCase + View 后缀（如：`UserManagementView.vue`）
- [ ] **组件文件命名**：使用 PascalCase（如：`UserDetailDialog.vue`）
- [ ] **API文件命名**：使用 camelCase（如：`userManagement.ts`）
- [ ] **类型文件命名**：使用 camelCase + .d.ts 后缀（如：`userManagement.d.ts`）

### 1.3 旧文件清理
- [ ] **旧目录删除**：确认旧的目录结构已删除或重命名
- [ ] **代码迁移完整**：相关代码已完整迁移到新位置
- [ ] **引用路径更新**：所有import路径已更新为新路径

---

## 2. MyBatisPlus分页标准检查

### 2.1 分页参数规范
```typescript
// ✅ 正确的分页参数
const pagination = reactive({
  pageNum: 1,      // 必须使用 pageNum，不是 page
  pageSize: 20,    // 页大小
  total: 0         // 总数
});
```
- [ ] **分页状态定义**：使用 `pageNum` 而不是 `page`
- [ ] **分页参数传递**：API调用时传递 `pageNum` 和 `pageSize`

### 2.2 分页组件绑定
```vue
<!-- ✅ 正确的分页组件绑定 -->
<el-pagination
  v-model:current-page="pagination.pageNum"
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
  layout="total, sizes, prev, pager, next, jumper"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
/>
```
- [ ] **组件绑定**：`v-model:current-page` 绑定到 `pagination.pageNum`
- [ ] **布局配置**：包含必要的分页元素
- [ ] **事件处理**：正确绑定分页事件处理函数

### 2.3 分页处理函数
```typescript
// ✅ 标准分页处理函数
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // 重置到第一页，使用 pageNum
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // 使用 pageNum
  loadData();
};
```
- [ ] **页大小变化处理**：重置到第一页并重新加载数据
- [ ] **页码变化处理**：更新页码并重新加载数据
- [ ] **参数一致性**：处理函数中使用 `pageNum`

---

## 3. 数据字典标准检查

### 3.1 字典导入规范
```typescript
// ✅ 使用批量字典导入
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.USER_STATUS,
  DICTIONARY_TYPES.USER_ROLE,
  // ... 其他字典类型
]);
```
- [ ] **批量导入**：使用 `useBatchDictionary` 而不是单个字典
- [ ] **字典类型**：从 `DICTIONARY_TYPES` 常量导入
- [ ] **加载状态**：获取字典加载状态

### 3.2 字典使用规范
```typescript
// ✅ 计算属性获取字典选项
const userStatusOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.USER_STATUS)
);

// ✅ 字典转义函数
const formatUserStatus = (status: string) => 
  getNameByCode(DICTIONARY_TYPES.USER_STATUS, status) || status;
```
- [ ] **计算属性**：使用计算属性获取字典选项
- [ ] **转义函数**：创建标准的字典转义函数
- [ ] **默认值处理**：转义失败时返回原值

### 3.3 模板中字典使用
```vue
<!-- ✅ 下拉选项使用字典 -->
<el-select v-model="searchParams.status" :loading="dictionaryLoading">
  <el-option :label="tc('all')" value="" />
  <el-option
    v-for="option in userStatusOptions"
    :key="option.code"
    :label="option.name"
    :value="option.code"
  />
</el-select>

<!-- ✅ 表格列使用字典转义 -->
<el-table-column prop="status" :label="t('status')">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.status)">
      {{ formatUserStatus(row.status) }}
    </el-tag>
  </template>
</el-table-column>
```
- [ ] **下拉选项**：使用字典数据填充选项
- [ ] **表格显示**：表格列使用字典转义显示
- [ ] **标签样式**：状态标签使用对应的样式类型
- [ ] **加载状态**：下拉框显示字典加载状态

---

## 4. API响应处理标准检查

### 4.1 API调用规范
```typescript
// ✅ 标准API调用和响应处理
const loadData = async () => {
  try {
    loading.value = true;
    
    const params = buildSearchParams();
    const response = await getDataList(params);
    
    // ✅ 必须检查响应码
    if (response.code === '200' || response.code === 200) {
      // ✅ 必须使用 response.result.records
      tableData.value = response.result.records || [];
      // ✅ 必须使用 response.result.total
      pagination.total = response.result.total || 0;
      pagination.pageNum = response.result.pageNum || pagination.pageNum;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};
```
- [ ] **响应码检查**：检查 `response.code` 是否为成功状态
- [ ] **数据获取**：使用 `response.result.records` 获取列表数据
- [ ] **分页信息**：使用 `response.result.total` 获取总数
- [ ] **错误处理**：有完整的错误处理和用户提示
- [ ] **加载状态**：正确管理loading状态

### 4.2 API函数定义规范
```typescript
// ✅ API函数返回类型规范
export const getDataList = (
  params: SearchParams
): Promise<ApiResponse<PageResponse<DataItem>>> => {
  if (USE_MOCK_API) {
    return getDataListMock(params);
  } else {
    return request.get<any, ApiResponse<PageResponse<DataItem>>>(
      '/api/data/list', 
      { params }
    );
  }
};
```
- [ ] **返回类型**：明确定义API函数的返回类型
- [ ] **Mock切换**：支持Mock数据和真实API的切换
- [ ] **泛型使用**：正确使用泛型定义响应数据类型

---

## 5. 类型定义标准检查

### 5.1 基础类型定义
```typescript
// ✅ MyBatisPlus分页类型
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// ✅ API响应类型
export interface ApiResponse<T> {
  code: string | number;
  message: string;
  result: T;
  timestamp: number;
}
```
- [ ] **分页类型**：定义标准的MyBatisPlus分页类型
- [ ] **响应类型**：定义统一的API响应类型
- [ ] **泛型支持**：使用泛型支持不同数据类型

### 5.2 搜索参数类型
```typescript
// ✅ 搜索参数继承分页参数
export interface DataSearchParams extends PageParams {
  keyword?: string;
  status?: string;
  dateStart?: string;
  dateEnd?: string;
  // ... 其他搜索字段
}
```
- [ ] **继承关系**：搜索参数继承分页参数
- [ ] **可选字段**：搜索字段定义为可选
- [ ] **字段命名**：字段命名符合业务含义

---

## 6. 国际化标准检查

### 6.1 国际化导入规范
```typescript
// ✅ 使用模块化国际化
const { t } = useModuleI18n('moduleName.subModule');
const { t: tc } = useModuleI18n('common');
```
- [ ] **模块化导入**：使用模块化的国际化导入
- [ ] **通用翻译**：单独导入通用翻译函数
- [ ] **命名规范**：使用 `t` 和 `tc` 区分模块和通用翻译

### 6.2 国际化文件结构
```json
// ✅ 模块化国际化文件结构
{
  "subModule": {
    "title": "页面标题",
    "field1": "字段1",
    "field2": "字段2",
    "action1": "操作1",
    "message1": "提示信息1"
  }
}
```
- [ ] **文件结构**：按模块组织翻译文件
- [ ] **键名规范**：使用有意义的键名
- [ ] **完整性**：所有显示文本都有对应翻译

---

## 7. 组件规范检查

### 7.1 Vue组件结构
```vue
<template>
  <!-- ✅ 模板结构清晰，使用语义化标签 -->
</template>

<script setup lang="ts">
// ✅ 导入顺序：Vue相关 -> 第三方库 -> 项目内部
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';

// ✅ Props和Emits类型定义
interface Props {
  visible: boolean;
  data?: DataType;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: DataType): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
</script>

<style scoped lang="scss">
// ✅ 使用scoped样式，避免样式污染
</style>
```
- [ ] **导入顺序**：按照Vue -> 第三方 -> 项目内部的顺序导入
- [ ] **类型定义**：Props和Emits有明确的类型定义
- [ ] **样式作用域**：使用scoped样式避免污染
- [ ] **模板语义化**：使用语义化的HTML标签

### 7.2 组件命名和导出
- [ ] **文件名一致**：组件文件名与组件名一致
- [ ] **命名规范**：使用PascalCase命名组件
- [ ] **导出方式**：使用标准的Vue 3 Composition API导出

---

## 8. Mock数据标准检查

### 8.1 Mock分页处理
```typescript
// ✅ 标准Mock分页处理
export const getDataListMock = (
  params: SearchParams
): Promise<ApiResponse<PageResponse<DataItem>>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        code: '200',
        message: '获取成功',
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        },
        timestamp: Date.now()
      });
    }, 500);
  });
};
```
- [ ] **分页参数**：使用 `pageNum` 和 `pageSize` 参数
- [ ] **分页计算**：正确计算分页的start和end
- [ ] **响应结构**：返回标准的API响应结构
- [ ] **搜索过滤**：支持搜索条件过滤
- [ ] **延迟模拟**：使用setTimeout模拟网络延迟

### 8.2 Mock数据质量
- [ ] **数据量充足**：Mock数据量足够测试分页功能（建议25-30条）
- [ ] **数据多样性**：包含各种状态和类型的测试数据
- [ ] **边界情况**：包含边界值和特殊情况的测试数据

---

## 9. 路由配置检查

### 9.1 路由定义规范
```typescript
// ✅ 路由配置规范
{
  path: '/module/sub-module',
  name: 'SubModule',
  component: () => import('@/views/module/subModule/SubModuleView.vue'),
  meta: {
    title: 'menu.subModule',
    requiresAuth: true,
    icon: 'IconName',
    roles: ['role1', 'role2']
  }
}
```
- [ ] **路径规范**：使用kebab-case命名路径
- [ ] **组件懒加载**：使用动态import实现懒加载
- [ ] **元信息完整**：包含title、权限、图标等元信息
- [ ] **权限配置**：正确配置页面访问权限

### 9.2 路由文件组织
- [ ] **模块化组织**：按业务模块组织路由文件
- [ ] **路由导入**：在主路由文件中正确导入模块路由
- [ ] **路由命名**：路由名称与组件名称保持一致

---

## 10. 代码质量检查

### 10.1 TypeScript检查
- [ ] **类型安全**：所有变量、函数参数、返回值都有明确类型
- [ ] **接口定义**：所有数据结构都有对应的接口定义
- [ ] **泛型使用**：合理使用泛型提高代码复用性
- [ ] **类型导入**：正确导入和使用类型定义
- [ ] **编译通过**：TypeScript编译无错误

### 10.2 代码规范检查
- [ ] **命名规范**：变量、函数、类名符合命名约定
- [ ] **代码格式**：通过ESLint和Prettier检查
- [ ] **注释完整**：复杂逻辑有适当注释
- [ ] **函数职责**：函数职责单一，长度合理
- [ ] **魔法数字**：避免硬编码的魔法数字

### 10.3 性能检查
- [ ] **计算属性**：合理使用computed缓存计算结果
- [ ] **监听器**：watch使用得当，避免不必要的监听
- [ ] **组件懒加载**：路由组件使用懒加载
- [ ] **防抖节流**：搜索等操作使用防抖处理
- [ ] **内存泄漏**：及时清理定时器和事件监听器

---

## 11. 功能测试检查

### 11.1 基础功能
- [ ] **页面加载**：页面正常加载，无控制台错误
- [ ] **数据展示**：表格数据正常显示
- [ ] **搜索功能**：搜索条件生效
- [ ] **分页功能**：分页切换正常
- [ ] **重置功能**：重置搜索条件正常

### 11.2 交互功能
- [ ] **弹窗操作**：弹窗正常打开和关闭
- [ ] **表单验证**：表单验证规则生效
- [ ] **数据提交**：数据提交和更新正常
- [ ] **错误处理**：错误提示友好
- [ ] **权限控制**：按钮权限控制正确

### 11.3 用户体验
- [ ] **加载状态**：有适当的loading状态
- [ ] **操作反馈**：操作有明确的成功/失败提示
- [ ] **数据为空**：空数据状态处理得当
- [ ] **响应速度**：页面响应速度合理
- [ ] **操作流畅**：用户操作流畅自然

---

## 12. 兼容性和响应式检查

### 12.1 浏览器兼容性
- [ ] **Chrome**：主流Chrome版本正常
- [ ] **Firefox**：主流Firefox版本正常
- [ ] **Safari**：Safari浏览器正常（如需要）
- [ ] **Edge**：Edge浏览器正常（如需要）

### 12.2 响应式设计
- [ ] **桌面端**：1920x1080及以上分辨率正常
- [ ] **笔记本**：1366x768分辨率正常
- [ ] **平板端**：768px宽度正常（如需要）
- [ ] **手机端**：375px宽度正常（如需要）

---

## 13. 安全性检查

### 13.1 数据安全
- [ ] **敏感信息**：手机号、身份证等敏感信息脱敏显示
- [ ] **XSS防护**：用户输入内容经过转义
- [ ] **权限验证**：API调用有权限验证
- [ ] **数据验证**：前端数据验证完整

### 13.2 代码安全
- [ ] **依赖安全**：第三方依赖无已知安全漏洞
- [ ] **配置安全**：敏感配置信息不暴露在前端
- [ ] **调试信息**：生产环境不包含调试信息

---

## 14. 文档和注释检查

### 14.1 代码注释
- [ ] **复杂逻辑**：复杂业务逻辑有注释说明
- [ ] **API接口**：API函数有参数和返回值说明
- [ ] **组件说明**：组件有用途和使用方法说明
- [ ] **类型注释**：复杂类型有注释说明

### 14.2 项目文档
- [ ] **README文档**：项目说明、安装和运行指南
- [ ] **开发规范**：开发规范和约定文档
- [ ] **API文档**：API接口文档完整
- [ ] **部署文档**：部署相关说明文档

---

## 15. 最终验证

### 15.1 设计文档对比
- [ ] **需求实现**：所有设计文档要求都已实现
- [ ] **接口规范**：API接口完全符合设计
- [ ] **数据结构**：数据结构完全符合设计
- [ ] **交互流程**：用户交互流程符合设计

### 15.2 验收标准
- [ ] **功能完整**：所有功能点都已实现
- [ ] **质量达标**：代码质量符合团队标准
- [ ] **性能合格**：页面性能满足要求
- [ ] **用户体验**：用户体验良好

---

## 检查结果记录

### 问题记录
| 检查项 | 问题描述 | 严重程度 | 修复方案 | 负责人 | 状态 |
|--------|----------|----------|----------|--------|------|
|        |          |          |          |        |      |

### 验收结论
- [ ] **通过验收**：所有检查项都已通过
- [ ] **条件通过**：存在轻微问题但不影响功能
- [ ] **不通过**：存在严重问题需要修复

### 签字确认
- **开发人员**：_________________ 日期：_________
- **测试人员**：_________________ 日期：_________
- **项目经理**：_________________ 日期：_________

---

**注意事项**：
1. 此清单适用于基于Vue 3 + TypeScript + Element Plus的前端项目
2. 根据具体项目需求，可以调整或增减检查项
3. 建议在每个开发阶段都使用此清单进行检查
4. 发现问题时应及时记录并跟踪修复进度
