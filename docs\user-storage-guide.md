You've saved $81 on API model usage this month with Pro. Switch to Auto for more requests or set a Spend Limit to continue with Sonnet. Your usage limits will reset when your monthly cycle ends on 7/21/2025.# 用户信息本地存储功能说明

## 概述

本功能实现了将用户信息存储到本地localStorage，就像token一样持久化保存。用户登录后，用户信息会被自动保存到本地，页面刷新后可以立即恢复用户状态，无需重新请求用户信息。

## 功能特性

### 1. 自动存储
- 用户登录成功后，用户信息自动保存到localStorage
- 包括用户基本信息、角色、权限等
- 与token和refreshToken一起管理

### 2. 自动恢复
- 页面刷新时自动从localStorage恢复用户信息
- 无需重新调用API获取用户信息
- 提升用户体验，减少网络请求

### 3. 统一管理
- 使用`UserStorage`工具类统一管理所有用户相关数据
- 提供完整的CRUD操作
- 包含错误处理和日志记录

## 核心组件

### UserStorage工具类 (`src/utils/user-storage.ts`)

```typescript
// 保存用户信息
UserStorage.setUserInfo(userInfo)

// 获取用户信息
const userInfo = UserStorage.getUserInfo()

// 保存Token
UserStorage.setToken(token)

// 获取Token
const token = UserStorage.getToken()

// 清除所有数据
UserStorage.clearAll()

// 检查状态
const hasUser = UserStorage.hasUserInfo()
const hasToken = UserStorage.hasToken()

// 获取摘要信息（用于调试）
const summary = UserStorage.getStorageSummary()
```

### 认证状态管理 (`src/stores/auth.ts`)

```typescript
// 使用认证状态管理
const authStore = useAuthStore()

// 用户信息
console.log(authStore.userInfo)

// 是否已登录
console.log(authStore.isLoggedIn)

// 用户角色
console.log(authStore.userRoles)

// 用户权限
console.log(authStore.userPermissions)
```

## 存储的数据结构

### 用户信息 (userInfo)
```json
{
  "id": "1",
  "username": "admin",
  "realName": "管理员",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "avatar": "",
  "roles": ["admin"],
  "permissions": ["*:*:*"]
}
```

### Token (token)
```
mock_token_1_1703123456789
```

### Refresh Token (refreshToken)
```
refresh_mock_token_1_1703123456789
```

## 使用流程

### 1. 登录流程
```typescript
// 用户登录
const response = await authStore.login(loginData)

// 自动保存到localStorage
// - token保存到localStorage['token']
// - refreshToken保存到localStorage['refreshToken']
// - userInfo保存到localStorage['userInfo']
```

### 2. 页面刷新恢复
```typescript
// 应用启动时自动调用
await authStore.initAuth()

// 自动从localStorage恢复用户信息
// 如果有token但没有用户信息，会调用API获取
```

### 3. 退出登录
```typescript
// 用户退出
await authStore.logout()

// 自动清除localStorage中的所有用户数据
```

## 测试页面

访问 `/user-info-test` 页面可以查看和测试用户信息存储功能：

- 显示当前用户信息
- 显示localStorage中的原始数据
- 提供刷新、清除等操作按钮
- 显示存储状态摘要

## 安全考虑

1. **数据加密**: 生产环境建议对敏感数据进行加密存储
2. **过期时间**: 可以添加用户信息的过期时间检查
3. **数据验证**: 从localStorage恢复数据时进行格式验证
4. **自动清理**: 定期清理过期的用户数据

## 扩展功能

### 1. 添加过期时间
```typescript
// 在UserInfo接口中添加过期时间
interface UserInfo {
  // ... 其他字段
  expiresAt?: number
}

// 检查是否过期
const isExpired = (userInfo: UserInfo) => {
  return userInfo.expiresAt && Date.now() > userInfo.expiresAt
}
```

### 2. 数据加密
```typescript
// 使用简单的base64编码（生产环境建议使用更安全的加密）
const encrypt = (data: string) => btoa(data)
const decrypt = (data: string) => atob(data)
```

### 3. 自动同步
```typescript
// 定期同步用户信息
setInterval(async () => {
  if (authStore.isLoggedIn) {
    await authStore.getUserInfo()
  }
}, 5 * 60 * 1000) // 每5分钟同步一次
```

## 注意事项

1. **存储限制**: localStorage有存储大小限制（通常5-10MB）
2. **隐私模式**: 隐私模式下localStorage可能不可用
3. **跨域限制**: localStorage受同源策略限制
4. **数据一致性**: 需要确保localStorage中的数据与服务器数据一致

## 故障排除

### 常见问题

1. **用户信息丢失**
   - 检查localStorage是否被清除
   - 检查浏览器隐私设置
   - 查看控制台错误信息

2. **数据格式错误**
   - 清除localStorage重新登录
   - 检查UserInfo接口定义
   - 查看数据解析错误

3. **权限问题**
   - 确认用户角色和权限正确
   - 检查API返回的权限数据
   - 重新获取用户信息

### 调试方法

```typescript
// 查看存储状态
console.log(UserStorage.getStorageSummary())

// 查看原始数据
console.log(localStorage.getItem('userInfo'))

// 检查用户信息
console.log(authStore.userInfo)
``` 