# VehicleModelMasterView.vue 重构设计文档

## 1. 项目概述

### 1.1 重构目标
将 `src/views/sales/VehicleModelMasterView.vue` 从当前的非规范化实现重构为符合DMS前端项目规范的标准化实现。

### 1.2 重构范围
- **目标文件**: `/Users/<USER>/Git/perodua/auto/new-frontend/dms_frontend/src/views/sales/VehicleModelMasterView.vue`
- **参考实现**: `/Users/<USER>/Git/perodua/auto/new-frontend/dms_frontend/src/views/sales/prospects/ProspectsView.vue`
- **重构模块**: 销售模块 - 车型主数据管理

## 2. 当前实现问题分析

### 2.1 MyBatisPlus分页组件问题

**当前实现问题**:
```typescript
// ❌ 分页参数不符合MyBatisPlus标准
const pagination = reactive({
  currentPage: 1,        // ❌ 应为 pageNum
  pageSize: 20,          // ✅ 正确
  total: 0               // ✅ 正确
});

// ❌ API调用参数不一致
const response = await getVehicleModelList({
  ...searchParams,
  page: pagination.currentPage,    // ❌ 应为 pageNum: pagination.pageNum
  pageSize: pagination.pageSize
})

// ❌ 分页组件绑定不符合标准
<el-pagination
  v-model:current-page="pagination.currentPage"    // ❌ 应绑定 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
/>
```

### 2.2 数据字典实现问题
**当前实现问题**:
```typescript
// ❌ 硬编码选项数据
const modelOptions = ref(['MYVI', 'ALZA', 'AXIA', 'BEZZA'])
const colourOptions = ref(['GRANITE GREY', 'ELECTRIC BLUE', 'GLITTERING SILVER', 'IVORY WHITE'])

// ❌ 缺少数据字典转义
<el-table-column :label="t('vehicleModel.model')" prop="model" min-width="120" />
// 应该使用字典转义显示
```

### 2.3 API响应处理问题
**当前实现问题**:
```typescript
// ❌ 响应数据处理不符合标准
const response = await getVehicleModelList({...})
tableData.value = response.data        // ❌ 应为 response.result.records
pagination.total = response.total      // ❌ 应为 response.result.total
```

## 3. 标准化重构方案

### 3.1 MyBatisPlus分页标准化

#### 3.1.1 类型定义修正
```typescript
// src/types/sales/vehicleModel.d.ts
export interface VehicleModelSearchParams {
  pageNum?: number;        // ✅ 标准MyBatisPlus参数
  pageSize?: number;       // ✅ 标准MyBatisPlus参数
  model?: string;
  variantName?: string;
  colourName?: string;
  fmrid?: string;
}

export interface VehicleModelPageResponse {
  records: VehicleModelMasterItem[];  // ✅ 标准MyBatisPlus响应
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}
```

#### 3.1.2 分页组件配置修正
```vue
<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="pagination.pageNum"    <!-- ✅ 修正为pageNum -->
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
// 分页状态修正
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 20,
  total: 0
})

// 分页处理函数修正
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  fetchData();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;  // ✅ 修正为pageNum
  fetchData();
};
</script>
```

### 3.2 数据字典标准化实现

#### 3.2.1 字典类型定义
```typescript
// src/constants/dictionary.ts 中添加
export const DICTIONARY_TYPES = {
  // 现有字典类型...
  VEHICLE_MODEL: 'VEHICLE_MODEL',           // 车型
  VEHICLE_VARIANT: 'VEHICLE_VARIANT',       // 车型版本
  VEHICLE_COLOR: 'VEHICLE_COLOR',           // 车辆颜色
  SYNC_STATUS: 'SYNC_STATUS'                // 同步状态
} as const;
```

#### 3.2.2 字典数据获取
```typescript
// 使用标准字典组合式函数
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.VEHICLE_VARIANT,
  DICTIONARY_TYPES.VEHICLE_COLOR,
  DICTIONARY_TYPES.SYNC_STATUS
]);

// 计算属性获取选项
const modelOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_MODEL));
const variantOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_VARIANT));
const colourOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_COLOR));
```

#### 3.2.3 表格字段转义
```vue
<!-- 标准字典转义实现 -->
<el-table-column :label="t('vehicleModel.model')" prop="model" min-width="120">
  <template #default="{ row }">
    {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, row.model) || row.model }}
  </template>
</el-table-column>

<el-table-column :label="t('vehicleModel.colourName')" prop="colourName" min-width="150">
  <template #default="{ row }">
    {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_COLOR, row.colourName) || row.colourName }}
  </template>
</el-table-column>
```

### 3.3 API响应处理标准化

#### 3.3.1 API函数修正
```typescript
// src/api/modules/sales/vehicleModel.ts
export const getVehicleModelList = async (params: VehicleModelSearchParams): Promise<VehicleModelPageResponse> => {
  if (USE_MOCK_API) {
    return getMockVehicleModelList(params);
  } else {
    const response = await request.post<any, ApiResponse<VehicleModelPageResponse>>('/vehicle-models/list', params);
    return response.result;  // ✅ 标准响应处理
  }
};
```

#### 3.3.2 组件中API调用修正
```typescript
// 获取数据函数修正
const fetchData = async () => {
  loading.value = true;
  try {
    const response = await getVehicleModelList({
      ...searchParams,
      pageNum: pagination.pageNum,    // ✅ 修正为pageNum
      pageSize: pagination.pageSize
    });
    
    tableData.value = response.records;  // ✅ 标准响应处理
    pagination.total = response.total;   // ✅ 标准响应处理
  } catch (error) {
    console.error('获取车型数据失败:', error);
    ElMessage.error(tc('fetchFailed'));
  } finally {
    loading.value = false;
  }
};
```

## 4. 目录结构重构

### 4.1 重构前结构
```
src/views/sales/VehicleModelMasterView.vue    # 页面文件
src/views/sales/components/SyncLogDialog.vue  # 同步日志弹窗
src/api/modules/vehicleModel.ts               # API模块
src/types/module.d.ts                         # 类型定义（混合）
```

### 4.2 重构后结构
```
src/
├── views/sales/vehicleModel/
│   ├── VehicleModelView.vue                  # 重构后的页面
│   └── components/
│       └── SyncLogDialog.vue                 # 同步日志弹窗
├── api/modules/sales/
│   └── vehicleModel.ts                       # API模块
├── types/sales/
│   └── vehicleModel.d.ts                     # 类型定义
├── mock/data/sales/
│   └── vehicleModel.ts                       # Mock数据
└── locales/modules/sales/
    ├── zh.json                               # 中文翻译（包含vehicleModel）
    └── en.json                               # 英文翻译（包含vehicleModel）
```

## 5. 详细实现计划

### 5.1 第一阶段：目录结构创建
```bash
# 创建目录结构
mkdir -p src/views/sales/vehicleModel/components
mkdir -p src/types/sales
mkdir -p src/mock/data/sales
```

### 5.2 第二阶段：类型定义创建
```typescript
// src/types/sales/vehicleModel.d.ts
export interface VehicleModelMasterItem {
  id: string;
  model: string;
  variantName: string;
  variantCode: string;
  colourName: string;
  colourCode: string;
  fmrid: string;
  createTime: string;
  updateTime: string;
}

export interface VehicleModelSearchParams {
  pageNum?: number;
  pageSize?: number;
  model?: string;
  variantName?: string;
  colourName?: string;
  fmrid?: string;
}

export interface VehicleModelPageResponse {
  records: VehicleModelMasterItem[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

export interface SyncLogItem {
  id: string;
  syncTime: string;
  status: 'success' | 'failed';
  recordCount: number;
  errorMessage?: string;
}
```

### 5.3 第三阶段：Mock数据重构
```typescript
// src/mock/data/sales/vehicleModel.ts
import type { VehicleModelSearchParams, VehicleModelPageResponse } from '@/types/sales/vehicleModel';

// 动态生成模拟数据（25-30条）
function generateMockData() {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData = [];

  const models = ['MYVI', 'ALZA', 'AXIA', 'BEZZA'];
  const variants = {
    'MYVI': ['1.5L H CVT', '1.3L G MT', '1.3L G CVT'],
    'ALZA': ['1.5L AV CVT', '1.5L H CVT'],
    'AXIA': ['1.0L G MT', '1.0L G CVT'],
    'BEZZA': ['1.3L X CVT', '1.0L G MT']
  };
  const colors = ['GRANITE GREY', 'ELECTRIC BLUE', 'GLITTERING SILVER', 'IVORY WHITE'];

  for (let i = 0; i < dataCount; i++) {
    const model = models[Math.floor(Math.random() * models.length)];
    const variantList = variants[model];
    const variant = variantList[Math.floor(Math.random() * variantList.length)];
    const color = colors[Math.floor(Math.random() * colors.length)];

    mockData.push({
      id: `${i + 1}`,
      model,
      variantName: variant,
      variantCode: `BD${Math.floor(Math.random() * 9)}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
      colourName: color,
      colourCode: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(Math.random() * 99)}(M)`,
      fmrid: `C${6500 + i}`,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
    });
  }

  return mockData;
}

const mockData = generateMockData();

export const getMockVehicleModelList = (params: VehicleModelSearchParams): Promise<VehicleModelPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.model) {
        filteredData = filteredData.filter(item =>
          item.model.toLowerCase().includes(params.model!.toLowerCase())
        );
      }

      if (params.variantName) {
        filteredData = filteredData.filter(item =>
          item.variantName.toLowerCase().includes(params.variantName!.toLowerCase())
        );
      }

      if (params.colourName) {
        filteredData = filteredData.filter(item =>
          item.colourName.toLowerCase().includes(params.colourName!.toLowerCase())
        );
      }

      if (params.fmrid) {
        filteredData = filteredData.filter(item =>
          item.fmrid.toLowerCase().includes(params.fmrid!.toLowerCase())
        );
      }

      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        records: filteredData.slice(start, end),
        total: filteredData.length,
        pageNum,
        pageSize,
        pages: Math.ceil(filteredData.length / pageSize)
      });
    }, 500);
  });
};
```

### 5.4 第四阶段：API模块重构
```typescript
// src/api/modules/sales/vehicleModel.ts
import request from '@/api';
import type { VehicleModelSearchParams, VehicleModelPageResponse } from '@/types/sales/vehicleModel';
import { getMockVehicleModelList } from '@/mock/data/sales/vehicleModel';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getVehicleModelList = async (params: VehicleModelSearchParams): Promise<VehicleModelPageResponse> => {
  if (USE_MOCK_API) {
    return getMockVehicleModelList(params);
  } else {
    const response = await request.post<any, ApiResponse<VehicleModelPageResponse>>('/vehicle-models/list', params);
    return response.result;
  }
};

export const syncVehicleModelData = async (): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true, message: '数据同步成功' });
      }, 2000);
    });
  } else {
    const response = await request.post<any, ApiResponse<{ success: boolean; message: string }>>('/vehicle-models/sync');
    return response.result;
  }
};
```

### 5.5 第五阶段：页面组件重构
```vue
<!-- src/views/sales/vehicleModel/VehicleModelView.vue -->
<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('vehicleModel.title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vehicleModel.model')">
              <el-select
                v-model="searchParams.model"
                :placeholder="tc('all')"
                clearable
                @change="handleModelChange"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in modelOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleModel.variantName')">
              <el-select
                v-model="searchParams.variantName"
                :placeholder="tc('all')"
                clearable
                :disabled="!searchParams.model"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in variantOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleModel.colourName')">
              <el-select
                v-model="searchParams.colourName"
                :placeholder="tc('all')"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in colourOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleModel.fmrid')">
              <el-input
                v-model="searchParams.fmrid"
                :placeholder="t('vehicleModel.fmridPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="buttons-col">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ tc('search') }}
            </el-button>
            <el-button @click="resetSearch">
              {{ tc('reset') }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20">
        <el-col :span="24" class="buttons-col">
          <el-button type="primary" :icon="Refresh" @click="handleSync" :loading="syncLoading">
            {{ t('vehicleModel.syncData') }}
          </el-button>
          <el-button :icon="Document" @click="handleSyncLog">
            {{ t('vehicleModel.syncLog') }}
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            {{ t('vehicleModel.exportData') }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" stripe>
        <el-table-column type="index" :label="tc('serialNumber')" width="80" />
        <el-table-column :label="t('vehicleModel.model')" prop="model" min-width="120">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, row.model) || row.model }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleModel.variantName')" prop="variantName" min-width="150">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_VARIANT, row.variantName) || row.variantName }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleModel.variantCode')" prop="variantCode" min-width="120" />
        <el-table-column :label="t('vehicleModel.colourName')" prop="colourName" min-width="150">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_COLOR, row.colourName) || row.colourName }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleModel.colourCode')" prop="colourCode" min-width="120" />
        <el-table-column :label="t('vehicleModel.fmrid')" prop="fmrid" min-width="100" />
        <el-table-column :label="t('vehicleModel.createTime')" prop="createTime" min-width="160">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleModel.updateTime')" prop="updateTime" min-width="160">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 同步日志弹窗 -->
    <SyncLogDialog ref="syncLogDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search, Refresh, Document, Download } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getVehicleModelList, syncVehicleModelData } from '@/api/modules/sales/vehicleModel';
import type { VehicleModelMasterItem, VehicleModelSearchParams } from '@/types/sales/vehicleModel';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { formatDate } from '@/utils/date-filter';
import SyncLogDialog from './components/SyncLogDialog.vue';

// 使用规范的国际化引用
const { t, tc } = useModuleI18n('sales');

// 搜索参数
const searchParams = reactive<VehicleModelSearchParams>({
  model: '',
  variantName: '',
  colourName: '',
  fmrid: ''
});

// 表格数据
const tableData = ref<VehicleModelMasterItem[]>([]);
const loading = ref(false);
const syncLoading = ref(false);

// 分页（修正为MyBatisPlus标准）
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 20,
  total: 0
});

// 字典数据
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.VEHICLE_VARIANT,
  DICTIONARY_TYPES.VEHICLE_COLOR
]);

// 下拉选项
const modelOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_MODEL));
const variantOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_VARIANT));
const colourOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_COLOR));

// 弹窗引用
const syncLogDialogRef = ref();

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const response = await getVehicleModelList({
      ...searchParams,
      pageNum: pagination.pageNum,    // ✅ 修正为pageNum
      pageSize: pagination.pageSize
    });

    tableData.value = response.records;  // ✅ 标准响应处理
    pagination.total = response.total;   // ✅ 标准响应处理

    ElMessage.success(tc('operationSuccessful'));
  } catch (error) {
    console.error('获取车型数据失败:', error);
    ElMessage.error(tc('fetchFailed'));
  } finally {
    loading.value = false;
  }
};

// 车型变化时更新版本选项
const handleModelChange = () => {
  searchParams.variantName = '';
  // 根据选择的车型过滤版本选项
  // 这里可以根据实际业务逻辑实现联动
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    model: '',
    variantName: '',
    colourName: '',
    fmrid: ''
  });
  pagination.pageNum = 1;
  fetchData();
};

// 同步数据
const handleSync = async () => {
  syncLoading.value = true;
  try {
    const result = await syncVehicleModelData();
    if (result.success) {
      ElMessage.success(t('vehicleModel.syncSuccess'));
      fetchData();
    }
  } catch (error) {
    console.error('同步失败:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    syncLoading.value = false;
  }
};

// 查看同步日志
const handleSyncLog = () => {
  syncLogDialogRef.value?.open();
};

// 导出数据
const handleExport = () => {
  ElMessage.success(t('vehicleModel.exportSuccess'));
  console.log('导出数据参数:', searchParams);
};

// 分页处理（修正为MyBatisPlus标准）
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  fetchData();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;  // ✅ 修正为pageNum
  fetchData();
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.search-card,
.operation-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-right: 20px;
    margin-bottom: 15px;
    &:last-child {
      margin-right: 0;
    }
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.table-card {
  margin-bottom: 20px;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
```

### 5.6 第六阶段：国际化文件更新
```json
// src/locales/modules/sales/zh.json 中添加
{
  "vehicleModel": {
    "title": "车型主数据管理",
    "model": "车型",
    "variantName": "版本名称",
    "variantCode": "版本代码",
    "colourName": "颜色名称",
    "colourCode": "颜色代码",
    "fmrid": "FMRID",
    "createTime": "创建时间",
    "updateTime": "更新时间",
    "syncData": "同步数据",
    "syncLog": "同步日志",
    "exportData": "导出数据",
    "syncSuccess": "数据同步成功",
    "exportSuccess": "数据导出成功",
    "fmridPlaceholder": "请输入FMRID"
  }
}
```

### 5.7 第七阶段：路由配置更新
```typescript
// src/router/modules/sales.ts 中更新
{
  path: '/sales/vehicle-model',
  name: 'VehicleModel',
  component: () => import('@/views/sales/vehicleModel/VehicleModelView.vue'),
  meta: {
    title: 'menu.vehicleModel',
    requiresAuth: true,
    icon: 'Car'
  }
}
```

## 6. 重构验证清单

### 6.1 MyBatisPlus分页验证
- [ ] 分页参数使用 `pageNum` 和 `pageSize`
- [ ] 分页组件绑定 `pagination.pageNum`
- [ ] API响应使用 `response.result.records` 和 `response.result.total`
- [ ] 分页功能正常工作

### 6.2 数据字典验证
- [ ] 所有下拉选项使用字典数据
- [ ] 表格列显示使用字典转义
- [ ] 字典数据加载正常
- [ ] 联动选择功能正常

### 6.3 API响应处理验证
- [ ] 使用 `response.result` 获取数据
- [ ] 错误处理符合规范
- [ ] Mock数据和真实API切换正常

### 6.4 目录结构验证
- [ ] 页面文件移动到 `src/views/sales/vehicleModel/VehicleModelView.vue`
- [ ] API模块移动到 `src/api/modules/sales/vehicleModel.ts`
- [ ] 类型定义创建在 `src/types/sales/vehicleModel.d.ts`
- [ ] Mock数据创建在 `src/mock/data/sales/vehicleModel.ts`
- [ ] 国际化文件更新在 `src/locales/modules/sales/`

### 6.5 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 同步功能正常工作
- [ ] 导出功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

## 7. 关键技术要点总结

### 7.1 MyBatisPlus分页标准
- **参数**: 使用 `pageNum` 和 `pageSize`
- **响应**: 使用 `records`、`total`、`pageNum`、`pageSize`、`pages`
- **组件绑定**: `v-model:current-page="pagination.pageNum"`

### 7.2 数据字典标准
- **获取**: 使用 `useBatchDictionary` 组合式函数
- **转义**: 使用 `getNameByCode` 进行字段转义
- **选项**: 使用 `getOptions` 获取下拉选项

### 7.3 API响应处理标准
- **成功响应**: 使用 `response.result` 获取业务数据
- **错误处理**: 统一使用 `ElMessage.error(tc('operationFailed'))`
- **Mock切换**: 通过环境变量控制Mock和真实API

---

**本设计文档基于项目实际需求和技术规范制定，确保重构后的代码符合DMS前端项目的标准化要求。**
```
