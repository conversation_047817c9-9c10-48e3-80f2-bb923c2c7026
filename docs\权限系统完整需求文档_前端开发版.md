# DMS系统权限管理完整需求文档 - 前端开发版

## 1. 文档概述

### 1.1 文档目的
本文档是DMS系统权限管理模块的完整需求文档，专门用于指导前端页面开发。整合了所有权限相关功能的详细需求，包括页面布局、交互细节、弹窗设计等前端开发必需的信息。

### 1.2 系统架构概述
系统采用**基于组织上下文的RBAC模型**，核心特点：
- 同一用户在不同门店可拥有不同角色
- 区分厂端和店端权限体系
- 支持细粒度的数据权限控制

### 1.3 核心实体关系
```
门店(组织) -> 部门 -> 用户
用户 -> 直接权限(组织+角色)
角色 -> 菜单权限 + 数据权限
```

## 2. 功能模块详细需求

### 2.1 门店管理

#### 2.1.1 门店列表页
**页面布局：**
- **搜索区域**：
  - 门店名称（输入框）
  - 状态（下拉选择：全部/开启/关闭）
  - 按钮：搜索、重置、新增
- **操作区域**：
  - 展开所有、折叠所有按钮
- **表格区域**：
  - 列：门店名称、负责人、排序、状态、创建时间、操作
  - 树形结构展示门店层级关系
  - 操作列：修改、删除

**交互逻辑：**
- 点击"搜索"：根据条件筛选门店
- 点击"重置"：清空搜索条件并刷新列表
- 点击"新增"：弹出新增门店弹窗
- 点击"修改"：弹出编辑门店弹窗
- 点击"删除"：弹出确认删除弹窗

#### 2.1.2 新增门店弹窗
**弹窗标题：** 新增门店
**弹窗尺寸：** 中等尺寸（600px宽）
**表单字段：**
- 上级门店（层级选择器，可为空）
- 门店名称（必填，输入框）
- 门店编码（必填，输入框）
- 显示排序（必填，数字输入框）
- 负责人（下拉选择，可为空）
- 联系电话（输入框）
- 邮箱（输入框）
- 状态（必填，单选：开启/关闭）
- 备注（文本域）

**按钮：** 确定、取消

#### 2.1.3 编辑门店弹窗
**弹窗标题：** 编辑门店
**表单字段：** 与新增相同，但预填充现有数据
**按钮：** 确定、取消

#### 2.1.4 删除确认弹窗
**弹窗标题：** 系统提示
**内容：** "是否删除所选中数据？"
**按钮：** 取消、确定

### 2.2 部门管理

#### 2.2.1 部门列表页
**页面布局：**
- **搜索区域**：
  - 所属门店（下拉选择）
  - 部门名称（输入框）
  - 状态（下拉选择：全部/开启/关闭）
  - 按钮：搜索、重置、新增
- **操作区域**：
  - 展开所有、折叠所有按钮
- **表格区域**：
  - 列：部门名称、所属门店、负责人、排序、状态、创建时间、操作
  - 树形结构展示部门层级关系
  - 操作列：修改、删除

#### 2.2.2 新增部门弹窗
**弹窗标题：** 新增部门
**表单字段：**
- 所属门店（必填，下拉选择）
- 上级部门（必填，层级选择器，根据所属门店动态加载）
- 部门名称（必填，输入框）
- 显示排序（必填，数字输入框）
- 负责人（下拉选择）
- 联系电话（输入框）
- 邮箱（输入框）
- 状态（必填，单选：开启/关闭）
- 备注（文本域）

**交互逻辑：**
- 选择"所属门店"后，"上级部门"选择器自动加载该门店下的部门树

#### 2.2.3 编辑部门弹窗
**弹窗标题：** 编辑部门
**表单字段：** 与新增相同，但"所属门店"字段只读不可编辑
**按钮：** 确定、取消

### 2.3 菜单管理

#### 2.3.1 菜单列表页
**页面布局：**
- **搜索区域**：
  - 菜单名称（输入框）
  - 状态（下拉选择：全部/开启/关闭）
  - 按钮：搜索、重置、新增
- **操作区域**：
  - 展开所有、折叠所有、刷新菜单缓存按钮
- **表格区域**：
  - 列：菜单名称、图标、排序、权限标识、组件路径、组件名称、状态、操作
  - 树形结构展示菜单层级关系
  - 操作列：修改、新增、删除

#### 2.3.2 新增菜单弹窗
**弹窗标题：** 新增菜单
**表单字段：**
- 上级菜单（层级选择器）
- 菜单名称（必填，输入框）
- 菜单类型（必填，单选：目录/菜单/按钮）
- 显示排序（必填，数字输入框）
- 菜单状态（必填，单选：开启/关闭）

**动态字段（根据菜单类型显示）：**
- **类型为"目录"时**：
  - 菜单图标（图标选择器）
  - 路由地址（必填，输入框）
- **类型为"菜单"时**：
  - 菜单图标（图标选择器）
  - 路由地址（必填，输入框）
  - 组件地址（输入框，如：system/user/index）
  - 组件名字（输入框，如：SystemUser）
  - 权限标识（输入框，如：system:user:list）
- **类型为"按钮"时**：
  - 权限标识（必填，输入框）

### 2.4 角色管理

#### 2.4.1 角色列表页
**页面布局：**
- **搜索区域**：
  - 角色名称（输入框）
  - 角色标识（输入框）
  - 状态（下拉选择：全部/开启/关闭）
  - 创建时间（日期范围选择器）
  - 按钮：搜索、重置、新增
- **操作区域**：
  - 导出按钮
- **表格区域**：
  - 列：角色编号、角色名称、角色标识、上级角色、显示顺序、备注、状态、创建时间、操作
  - 操作列：编辑、菜单权限、数据权限、删除

#### 2.4.2 新增角色弹窗
**弹窗标题：** 新增角色
**表单字段：**
- 角色名称（必填，输入框）
- 角色标识（必填，输入框）
- 上级角色（非必填，下拉选择）
- 显示顺序（必填，数字输入框）
- 状态（必填，单选：开启/关闭）
- 备注（文本域）

**交互逻辑：**
- "上级角色"下拉列表需排除当前角色及其子孙角色，防止循环继承

#### 2.4.3 菜单权限配置弹窗
**弹窗标题：** 菜单权限
**弹窗尺寸：** 大尺寸（800px宽）
**页面布局：**
- **角色信息区域**：
  - 角色名称（只读）
  - 角色标识（只读）
- **权限树区域**：
  - 菜单权限树（树形结构，每个节点带复选框）
  - 操作按钮：全选/全不选、全部展开/折叠
- **按钮区域**：确定、取消

**交互逻辑：**
- 如果角色有父角色，父角色的权限默认勾选且禁用
- 支持父子节点联动选择
- 支持搜索菜单功能

#### 2.4.4 数据权限配置弹窗
**弹窗标题：** 数据权限
**弹窗尺寸：** 大尺寸（800px宽）
**页面布局：**
- **角色信息区域**：
  - 角色名称（只读）
  - 角色标识（只读）
- **权限配置区域**：
  - 权限范围（下拉选择：全部数据权限/自定义部门权限/本部门数据权限/本部门及以下数据权限/仅本人数据权限）
  - 部门范围树（当选择"自定义部门权限"时显示）
    - 部门树形结构，每个节点带复选框
    - 操作按钮：全选/全不选、全部展开/折叠、父子联动开关
- **按钮区域**：确定、取消

**交互逻辑：**
- 选择不同权限范围时，页面内容动态变化
- 继承自父角色的数据权限默认勾选且禁用


**弹窗标题：** 权限配置 
**弹窗尺寸：** 大尺寸（900px宽）
**页面布局：**
- **已分配权限列表**：
  - 表格显示已分配的"门店-角色"对
  - 列：门店名称、角色名称、操作（删除）
- **操作区域**：
  - 新增权限按钮
- **按钮区域**：确定、取消

#### 2.5.4 新增权限弹窗
**弹窗标题：** 新增权限
**表单字段：**
- 选择门店（必填，多选下拉/穿梭框）
- 选择角色（必填，多选下拉/穿梭框）
- 按钮：确定、取消

**交互逻辑：**
- 确定后将门店和角色的笛卡尔积添加到权限列表

#### 2.5.5 成员管理弹窗
**弹窗标题：** 成员管理 - [用户组名称]
**弹窗尺寸：** 大尺寸（900px宽）
**页面布局：**
- **用户穿梭框**：
  - 左侧：待选用户列表
  - 右侧：已选成员列表
  - 支持搜索功能（用户名称、手机号）
- **按钮区域**：确定、取消

### 2.6 用户管理

#### 2.6.1 用户列表页
**页面布局：**
- **搜索区域**：
  - 部门名称（层级选择器）
  - 用户名称（输入框）
  - 手机号码（输入框）
  - 状态（下拉选择：全部/开启/关闭）
  - 创建时间（日期范围选择器）
  - 按钮：搜索、重置、新增
- **操作区域**：
  - 导入、导出按钮
- **表格区域**：
  - 列：用户编号、用户名称、用户昵称、部门、手机号码、状态、创建时间、操作
  - 操作列：修改、更多（下拉菜单：删除、重置密码）

#### 2.6.2 新增用户弹窗
**弹窗标题：** 新增用户
**弹窗尺寸：** 大尺寸（800px宽）
**页面布局：** 使用Tab页签分离基本信息和权限配置

**基本信息Tab：**
- 用户昵称（必填，输入框）
- 归属部门（必填，层级选择器，显示"门店->部门"结构）
- 手机号码（输入框）
- 邮箱（输入框）
- 用户名称（必填，输入框）
- 用户密码（必填，密码输入框）
- 用户性别（下拉选择：男/女/未知）
- 状态（必填，单选：开启/关闭）
- 备注（文本域）

**权限配置Tab：**

- **直接分配角色区域**：
  - 已分配角色列表（门店-角色对）
  - 新增角色按钮
- **有效权限总览区域**：
  - 只读权限树，展示用户最终有效权限

#### 2.6.3 编辑用户弹窗
**弹窗标题：** 编辑用户
**表单字段：** 与新增相同，但不显示"用户密码"字段
**按钮：** 确定、取消

#### 2.6.4 重置密码弹窗
**弹窗标题：** 重置密码
**内容：** "请输入'[用户昵称]'的新密码"
**表单字段：**
- 新密码（必填，密码输入框）
- 确认密码（必填，密码输入框）
**按钮：** 取消、确定

#### 2.6.5 用户组选择弹窗
**弹窗标题：** 选择用户组
**页面布局：**
- 用户组列表（多选列表）
- 搜索框（支持用户组名称搜索）
**按钮：** 确定、取消

#### 2.6.6 分配角色弹窗
**弹窗标题：** 分配角色
**表单字段：**
- 选择门店（必填，下拉选择）
- 选择角色（必填，下拉选择）
**按钮：** 确定、取消

## 3. 通用组件规范

### 3.1 树形选择器
**功能要求：**
- 支持搜索功能
- 支持展开/折叠
- 支持父子联动
- 支持单选/多选模式
- 显示层级关系

### 3.2 穿梭框
**功能要求：**
- 左右两侧列表
- 支持搜索功能
- 支持全选/全不选
- 支持批量操作
- 显示已选/未选数量

### 3.3 权限树
**功能要求：**
- 树形结构展示
- 复选框选择
- 支持搜索过滤
- 支持展开/折叠
- 支持父子联动
- 支持全选/全不选
- 继承权限的禁用显示

### 3.4 确认弹窗
**标准样式：**
- 标题：系统提示
- 内容：确认信息
- 按钮：取消（左）、确定（右）
- 危险操作用红色确定按钮

## 4. 页面交互规范

### 4.1 列表页交互
- 搜索后保持搜索条件
- 支持列表排序
- 支持分页（每页10/20/50条）
- 操作按钮统一放在操作列
- 危险操作需要二次确认

### 4.2 弹窗交互
- 弹窗打开时自动聚焦第一个输入框
- 必填字段用红色星号标识
- 表单验证实时反馈
- 提交成功后自动关闭弹窗并刷新列表

### 4.3 响应式设计
- 支持1920px、1440px、1200px等主流分辨率
- 弹窗在小屏幕上适配
- 表格在小屏幕上支持横向滚动

## 5. 权限控制规范

### 5.1 页面权限
- 厂端用户：可访问所有管理页面
- 店端用户：只能访问指定的管理页面
- 根据用户类型显示不同的菜单

### 5.2 操作权限
- 新增、编辑、删除等操作需要对应权限
- 权限不足时隐藏相关按钮
- 显示权限相关的提示信息

### 5.3 数据权限
- 根据用户的数据权限范围过滤显示数据
- 部门管理按门店过滤
- 用户管理按部门过滤

## 6. 错误处理规范

### 6.1 表单验证
- 必填字段验证
- 格式验证（邮箱、手机号等）
- 长度验证
- 唯一性验证

### 6.2 网络错误
- 请求超时提示
- 网络异常提示
- 服务器错误提示

### 6.3 业务错误
- 权限不足提示
- 数据冲突提示
- 操作失败提示

## 7. 性能要求

### 7.1 加载速度
- 列表页面加载时间 < 2秒
- 弹窗打开时间 < 1秒
- 搜索响应时间 < 1秒

### 7.2 数据处理
- 大数据量列表分页加载
- 树形数据懒加载
- 搜索防抖处理

这份文档包含了权限系统所有模块的详细前端开发需求，可以直接用于AI生成前端页面。 