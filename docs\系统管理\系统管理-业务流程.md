# 系统管理-业务流程

## 业务流程概述

系统管理是DMS系统的基础管理模块，涵盖用户管理、门店管理、部门管理、角色管理、菜单管理等核心业务流程。该模块为系统提供组织架构、权限控制、用户管理等基础服务，是整个系统正常运行的基石。

## 主要业务流程图

```mermaid
graph TD
    A["系统管理员登录"] --> B["进入系统管理模块"]
    B --> C{"选择管理功能"}
    
    C -->|用户管理| D["用户管理流程"]
    C -->|门店管理| E["门店管理流程"]
    C -->|部门管理| F["部门管理流程"]
    C -->|角色管理| G["角色管理流程"]
    C -->|菜单管理| H["菜单管理流程"]
    
    D --> D1["查看用户列表"]
    D1 --> D2{"用户操作选择"}
    D2 -->|新增用户| D3["用户创建流程"]
    D2 -->|编辑用户| D4["用户编辑流程"]
    D2 -->|分配角色| D5["角色分配流程"]
    D2 -->|状态管理| D6["用户状态管理"]
    
    D3 --> D7["填写用户信息"]
    D7 --> D8["验证数据"]
    D8 --> D9["保存用户"]
    D9 --> D10["分配初始角色"]
    D10 --> D1
    
    D5 --> D11["选择门店"]
    D11 --> D12["选择部门"]
    D12 --> D13["选择角色"]
    D13 --> D14["设置职位"]
    D14 --> D15["确认分配"]
    D15 --> D1
    
    E --> E1["查看门店列表"]
    E1 --> E2{"门店操作选择"}
    E2 -->|新增门店| E3["门店创建流程"]
    E2 -->|编辑门店| E4["门店编辑流程"]
    E2 -->|状态管理| E5["门店状态管理"]
    
    E3 --> E6["选择父门店"]
    E6 --> E7["填写门店信息"]
    E7 --> E8["设置门店属性"]
    E8 --> E9["保存门店"]
    E9 --> E1
    
    G --> G1["查看角色列表"]
    G1 --> G2{"角色操作选择"}
    G2 -->|新增角色| G3["角色创建流程"]
    G2 -->|编辑角色| G4["角色编辑流程"]
    G2 -->|配置权限| G5["权限配置流程"]
    
    G5 --> G6{"权限类型选择"}
    G6 -->|菜单权限| G7["菜单权限配置"]
    G6 -->|数据权限| G8["数据权限配置"]
    
    G7 --> G9["显示菜单树"]
    G9 --> G10["选择菜单权限"]
    G10 --> G11["保存菜单权限"]
    G11 --> G1
    
    G8 --> G12["选择权限范围"]
    G12 --> G13{"是否自定义权限?"}
    G13 -->|是| G14["选择部门"]
    G13 -->|否| G15["保存权限范围"]
    G14 --> G15
    G15 --> G1
    
    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#f3e5f5
    style F fill:#f3e5f5
    style G fill:#f3e5f5
    style H fill:#f3e5f5
```

## 详细业务流程说明

### 1. 用户管理流程

#### 1.1 用户创建流程
- **流程描述**：系统管理员创建新用户账号的完整流程
- **触发条件**：新员工入职或需要新增系统用户
- **流程步骤**：
  1. 点击"新增用户"按钮
  2. 填写用户基本信息（用户名、姓名、联系方式）
  3. 选择用户类型（Factory/Store）
  4. 设置用户状态和入职日期
  5. 验证数据完整性和唯一性
  6. 保存用户信息
  7. 为用户分配初始角色
- **验证规则**：
  - 用户名全局唯一性检查
  - 邮箱格式验证
  - 手机号格式验证
  - 必填字段非空验证

#### 1.2 用户角色分配流程
- **流程描述**：为用户分配门店角色和权限的流程
- **触发条件**：新用户创建或用户职责变更
- **流程步骤**：
  1. 选择目标用户，点击"分配角色"
  2. 添加门店角色分配记录
  3. 选择所属门店（动态加载门店列表）
  4. 选择所属部门（根据门店动态加载）
  5. 选择角色（根据门店动态加载角色列表）
  6. 设置职位信息
  7. 设置是否为主角色
  8. 保存角色分配
  9. 系统自动更新用户权限
- **业务规则**：
  - Factory用户最多只能分配一个门店角色
  - 每个用户必须有且仅有一个主角色
  - 角色分配立即生效

#### 1.3 用户状态管理流程
- **流程描述**：管理用户账号启用/禁用状态
- **触发条件**：员工离职、权限调整或安全需要
- **流程步骤**：
  1. 在用户列表中找到目标用户
  2. 点击状态切换开关
  3. 系统提示状态变更确认
  4. 确认后更新用户状态
  5. 状态变更立即生效
  6. 禁用用户无法登录系统

### 2. 门店管理流程

#### 2.1 门店创建流程
- **流程描述**：创建新门店的完整流程
- **触发条件**：新门店开业或组织架构调整
- **流程步骤**：
  1. 点击"新增门店"按钮
  2. 选择父门店（建立层级关系）
  3. 填写门店基本信息（编码、名称、类型）
  4. 设置门店属性（销售/售后）
  5. 填写联系信息和地址信息
  6. 设置门店状态
  7. 验证门店编码唯一性
  8. 保存门店信息
  9. 更新门店层级结构
- **验证规则**：
  - 门店编码全局唯一性
  - 父门店存在性验证
  - 必填字段验证

#### 2.2 门店层级管理流程
- **流程描述**：管理门店之间的层级关系
- **触发条件**：组织架构调整或门店重组
- **流程步骤**：
  1. 查看门店树形结构
  2. 识别需要调整的门店关系
  3. 编辑门店信息，修改父门店
  4. 验证层级关系合理性
  5. 保存层级关系变更
  6. 系统自动更新相关权限
- **业务规则**：
  - 总部门店作为根节点
  - 不允许循环层级关系
  - 层级变更影响权限继承

### 3. 部门管理流程

#### 3.1 部门创建流程
- **流程描述**：创建新部门的完整流程
- **触发条件**：组织架构调整或业务发展需要
- **流程步骤**：
  1. 点击"新增部门"按钮
  2. 选择父部门（建立层级关系）
  3. 填写部门基本信息（名称、编码、类型）
  4. 设置部门负责人
  5. 填写部门描述
  6. 设置部门状态
  7. 验证部门编码唯一性
  8. 保存部门信息
  9. 更新部门树形结构
- **验证规则**：
  - 部门编码门店内唯一性
  - 父部门存在性验证
  - 必填字段验证

#### 3.2 部门层级管理流程
- **流程描述**：管理部门之间的层级关系
- **触发条件**：组织架构调整或部门重组
- **流程步骤**：
  1. 查看部门树形结构
  2. 识别需要调整的部门关系
  3. 编辑部门信息，修改父部门
  4. 验证层级关系合理性
  5. 保存层级关系变更
  6. 系统自动更新相关数据权限
- **业务规则**：
  - 不允许循环层级关系
  - 层级变更影响数据权限范围
  - 删除部门需检查子部门和关联用户

### 4. 角色管理流程

#### 4.1 角色创建流程
- **流程描述**：创建新角色的完整流程
- **触发条件**：新业务需求或权限细化需要
- **流程步骤**：
  1. 点击"新增角色"按钮
  2. 填写角色基本信息（名称、编码、描述）
  3. 选择角色来源（Factory/Store）
  4. 设置所属门店（Store角色必填）
  5. 设置数据权限范围
  6. 配置自定义部门权限（如适用）
  7. 设置角色状态
  8. 验证角色编码唯一性
  9. 保存角色信息
- **验证规则**：
  - 角色编码唯一性（按来源和门店）
  - Store角色必须指定所属门店
  - Factory角色只有Factory用户可创建

#### 4.2 菜单权限配置流程
- **流程描述**：为角色配置菜单访问权限
- **触发条件**：角色权限调整或新功能上线
- **流程步骤**：
  1. 选择目标角色，点击"配置菜单权限"
  2. 显示菜单权限配置界面
  3. 加载完整菜单树结构
  4. 显示当前角色已有权限
  5. 选择/取消菜单权限
  6. 支持批量操作（全选/取消）
  7. 预览权限配置效果
  8. 保存菜单权限配置
  9. 权限立即生效，通知相关用户
- **业务规则**：
  - 父菜单权限包含子菜单权限
  - 按钮权限需要对应菜单权限
  - 权限变更立即生效

#### 4.3 数据权限配置流程
- **流程描述**：为角色配置数据访问权限范围
- **触发条件**：数据安全需求或业务权限调整
- **流程步骤**：
  1. 选择目标角色，点击"配置数据权限"
  2. 显示数据权限配置界面
  3. 选择数据权限范围类型
  4. 如选择自定义权限，显示部门树
  5. 选择可访问的部门
  6. 预览数据权限范围
  7. 保存数据权限配置
  8. 权限立即生效
- **权限范围说明**：
  - 全部数据：可访问所有数据（超级管理员）
  - 自定义：可访问指定部门数据
  - 本部门：只能访问本部门数据
  - 本部门及下级：可访问本部门及下级部门数据
  - 仅个人：只能访问个人创建的数据

### 5. 菜单管理流程

#### 5.1 菜单创建流程
- **流程描述**：创建新菜单项的完整流程
- **触发条件**：新功能开发或菜单结构调整
- **流程步骤**：
  1. 点击"新增菜单"按钮
  2. 选择菜单类型（目录/菜单/按钮）
  3. 选择父菜单（建立层级关系）
  4. 设置菜单归属（Factory/Dealer）
  5. 填写菜单基本信息（名称、编码）
  6. 配置菜单路径和组件（如适用）
  7. 设置菜单图标
  8. 设置权限标识
  9. 配置菜单属性（显示/缓存等）
  10. 保存菜单信息
  11. 更新菜单树结构
- **验证规则**：
  - 菜单编码全局唯一性
  - 权限标识格式验证
  - 必填字段验证

#### 5.2 菜单权限标识管理流程
- **流程描述**：管理菜单的权限标识符
- **触发条件**：权限控制需求或安全要求
- **流程步骤**：
  1. 查看菜单列表，识别权限标识
  2. 编辑菜单，修改权限标识
  3. 验证权限标识格式和唯一性
  4. 保存权限标识变更
  5. 同步更新相关角色权限
  6. 通知权限变更影响的用户
- **标识格式规范**：
  - 格式：模块:功能:操作
  - 示例：system:user:add
  - 层级化权限控制

### 6. 权限验证流程

#### 6.1 用户登录权限验证
- **流程描述**：用户登录时的权限验证流程
- **流程步骤**：
  1. 用户输入用户名和密码
  2. 验证用户身份信息
  3. 检查用户状态（是否禁用）
  4. 加载用户角色信息
  5. 计算用户有效权限（角色权限合并）
  6. 生成用户权限缓存
  7. 生成访问令牌
  8. 返回用户信息和权限

#### 6.2 功能访问权限验证
- **流程描述**：用户访问功能时的权限验证
- **流程步骤**：
  1. 用户请求访问特定功能
  2. 获取功能的权限标识
  3. 检查用户是否具有相应权限
  4. 验证用户状态和角色状态
  5. 允许/拒绝访问请求
  6. 记录访问日志

#### 6.3 数据访问权限验证
- **流程描述**：用户访问数据时的权限验证
- **流程步骤**：
  1. 用户请求访问数据
  2. 获取用户的数据权限范围
  3. 根据权限范围过滤数据
  4. 返回用户有权访问的数据
  5. 记录数据访问日志

### 7. 异常处理流程

#### 7.1 权限冲突处理
- **处理场景**：用户权限配置冲突或权限验证失败
- **处理步骤**：
  1. 检测权限冲突
  2. 记录冲突详情
  3. 按优先级规则解决冲突
  4. 更新权限配置
  5. 通知相关管理员

#### 7.2 数据完整性处理
- **处理场景**：删除操作可能影响数据完整性
- **处理步骤**：
  1. 检查数据关联关系
  2. 提示关联数据影响
  3. 要求用户确认或提供解决方案
  4. 执行安全删除或拒绝操作
  5. 记录操作日志

### 8. 业务规则和约束

#### 8.1 用户管理规则
- Factory用户只能分配一个门店角色
- 每个用户必须有且仅有一个主角色
- 用户名全局唯一，不可重复
- 禁用用户无法登录系统

#### 8.2 组织架构规则
- 总部门店作为根节点，有且仅有一个
- 门店和部门支持多级层级结构
- 不允许循环层级关系
- 删除需检查子节点和关联关系

#### 8.3 权限控制规则
- 权限通过角色分配给用户
- 用户有效权限是所有角色权限的合并
- 权限变更立即生效
- 数据权限严格按范围过滤

#### 8.4 多租户隔离规则
- Factory用户可访问所有门店数据
- Store用户只能访问所属门店数据
- 角色权限不能跨越租户边界
- 严格的数据访问控制

---