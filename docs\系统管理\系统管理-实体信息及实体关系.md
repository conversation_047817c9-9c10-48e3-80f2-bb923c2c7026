 # 系统管理-实体信息及实体关系

## 实体关系概述

系统管理模块涉及用户、门店、部门、角色、菜单等多个核心实体，以及它们之间的复杂关联关系。这些实体构成了完整的权限管理和组织架构体系，是整个DMS系统的基础。

## 核心实体定义

### 1. 用户实体 (User)

**实体描述**：系统用户账号的核心信息载体，是权限管理和身份认证的基础实体。

**核心属性**：
- **主键字段**：
  - `user_id` (varchar): 用户内部ID，主键
  - `username` (varchar): 用户名，业务主键，全局唯一

- **基础信息字段**：
  - `full_name` (varchar): 用户真实姓名
  - `phone` (varchar): 手机号码
  - `email` (varchar): 邮箱地址
  - `user_type` (enum): 用户类型（Factory/Store）
  - `entry_date` (date): 入职日期
  - `user_status` (enum): 用户状态（Normal/Disabled）
  - `remark` (text): 备注信息

- **系统字段**：
  - `create_time` (datetime): 创建时间
  - `update_time` (datetime): 更新时间
  - `creator_id` (varchar): 创建人ID
  - `updater_id` (varchar): 更新人ID

### 2. 门店实体 (Store)

**实体描述**：经销商门店信息，支持层级结构，是多租户架构的基础。

**核心属性**：
- **主键字段**：
  - `store_id` (varchar): 门店内部ID，主键
  - `store_code` (varchar): 门店编码，业务主键，全局唯一

- **层级关系字段**：
  - `parent_id` (varchar): 父门店ID，外键

- **基础信息字段**：
  - `store_name` (varchar): 门店名称
  - `store_type` (enum): 门店类型（Headquarter/Main/Branch/Warehouse）
  - `store_properties` (json): 门店属性数组（Sales/After_Sales）
  - `store_status` (enum): 门店状态（Active/Inactive）

- **联系信息字段**：
  - `manager` (varchar): 门店经理
  - `contact_person` (varchar): 联系人
  - `contact_phone` (varchar): 联系电话

- **地址信息字段**：
  - `province` (varchar): 省份
  - `city` (varchar): 城市
  - `district` (varchar): 区县
  - `detail_address` (varchar): 详细地址

- **系统字段**：
  - `create_time` (datetime): 创建时间
  - `update_time` (datetime): 更新时间
  - `creator_id` (varchar): 创建人ID
  - `updater_id` (varchar): 更新人ID
  - `remark` (text): 备注信息

### 3. 部门实体 (Department)

**实体描述**：组织内的职能单位，支持树形层级结构，是数据权限控制的基础。

**核心属性**：
- **主键字段**：
  - `department_id` (varchar): 部门内部ID，主键
  - `department_code` (varchar): 部门编码，门店内唯一

- **层级关系字段**：
  - `parent_id` (varchar): 父部门ID，外键

- **基础信息字段**：
  - `department_name` (varchar): 部门名称
  - `department_type` (enum): 部门类型（Business/Support/Management）
  - `department_status` (enum): 部门状态（Normal/Disabled）
  - `department_head` (varchar): 部门负责人
  - `description` (text): 部门描述
  - `sort_order` (int): 排序序号

- **系统字段**：
  - `create_time` (datetime): 创建时间
  - `update_time` (datetime): 更新时间
  - `creator_id` (varchar): 创建人ID
  - `updater_id` (varchar): 更新人ID

### 4. 角色实体 (Role)

**实体描述**：权限的集合体，是RBAC权限模型的核心实体。

**核心属性**：
- **主键字段**：
  - `role_id` (varchar): 角色内部ID，主键
  - `role_code` (varchar): 角色编码，按来源和门店唯一

- **基础信息字段**：
  - `role_name` (varchar): 角色名称
  - `role_source` (enum): 角色来源（Factory/Store）
  - `store_id` (varchar): 所属门店ID（Store角色必填）
  - `role_status` (enum): 角色状态（Normal/Disabled）
  - `description` (text): 角色描述

- **数据权限字段**：
  - `role_scope` (enum): 数据权限范围（All/Custom/Department/DepartmentAndBelow/OnlyPersonal）
  - `dept_ids` (json): 自定义部门ID列表（自定义权限时使用）

- **系统字段**：
  - `create_time` (datetime): 创建时间
  - `update_time` (datetime): 更新时间
  - `creator_id` (varchar): 创建人ID
  - `updater_id` (varchar): 更新人ID

### 5. 菜单实体 (Menu)

**实体描述**：系统功能的组织结构，支持树形层级，是菜单权限控制的基础。

**核心属性**：
- **主键字段**：
  - `menu_id` (varchar): 菜单内部ID，主键
  - `menu_code` (varchar): 菜单编码，业务主键，全局唯一

- **层级关系字段**：
  - `parent_id` (varchar): 父菜单ID，外键

- **基础信息字段**：
  - `menu_name` (varchar): 菜单名称
  - `menu_type` (enum): 菜单类型（Directory/Menu/Button）
  - `menu_side` (enum): 菜单归属（Factory/Dealer）
  - `menu_status` (enum): 菜单状态（Normal/Disabled）

- **路径信息字段**：
  - `menu_icon` (varchar): 菜单图标
  - `menu_path` (varchar): 路由路径
  - `component` (varchar): 组件路径
  - `permission` (varchar): 权限标识

- **配置字段**：
  - `sort_order` (int): 排序序号
  - `is_visible` (boolean): 是否显示
  - `is_cache` (boolean): 是否缓存

- **系统字段**：
  - `create_time` (datetime): 创建时间
  - `update_time` (datetime): 更新时间
  - `creator_id` (varchar): 创建人ID
  - `updater_id` (varchar): 更新人ID

### 6. 用户门店角色关联实体 (UserStoreRole)

**实体描述**：用户在不同门店的角色分配关系，支持一个用户在多个门店担任不同角色。

**核心属性**：
- **主键字段**：
  - `user_store_role_id` (varchar): 关联记录内部ID，主键

- **关联字段**：
  - `user_id` (varchar): 用户ID，外键
  - `store_id` (varchar): 门店ID，外键
  - `department_id` (varchar): 部门ID，外键
  - `role_id` (varchar): 角色ID，外键

- **业务字段**：
  - `position` (varchar): 职位名称
  - `is_primary` (boolean): 是否主角色

- **系统字段**：
  - `create_time` (datetime): 创建时间
  - `creator_id` (varchar): 创建人ID

### 7. 角色菜单权限关联实体 (RoleMenu)

**实体描述**：角色与菜单权限的关联关系，是菜单权限控制的基础。

**核心属性**：
- **主键字段**：
  - `role_menu_id` (varchar): 关联记录内部ID，主键

- **关联字段**：
  - `role_id` (varchar): 角色ID，外键
  - `menu_id` (varchar): 菜单ID，外键

- **系统字段**：
  - `create_time` (datetime): 创建时间
  - `creator_id` (varchar): 创建人ID

## 实体关系图

```mermaid
erDiagram
    User ||--o{ UserStoreRole : "用户可分配多个门店角色"
    Store ||--o{ UserStoreRole : "门店可有多个用户角色"
    Department ||--o{ UserStoreRole : "部门可有多个用户"
    Role ||--o{ UserStoreRole : "角色可分配给多个用户"
    
    Store ||--o{ Store : "门店层级关系"
    Department ||--o{ Department : "部门层级关系"
    Menu ||--o{ Menu : "菜单层级关系"
    
    Role ||--o{ RoleMenu : "角色可有多个菜单权限"
    Menu ||--o{ RoleMenu : "菜单可分配给多个角色"
    
    Store ||--o{ Role : "门店可有多个角色"
    
    User {
        varchar user_id PK
        varchar username UK
        varchar full_name
        varchar phone
        varchar email
        enum user_type
        date entry_date
        enum user_status
        text remark
        datetime create_time
        datetime update_time
        varchar creator_id
        varchar updater_id
    }
    
    Store {
        varchar store_id PK
        varchar store_code UK
        varchar parent_id FK
        varchar store_name
        enum store_type
        json store_properties
        enum store_status
        varchar manager
        varchar contact_person
        varchar contact_phone
        varchar province
        varchar city
        varchar district
        varchar detail_address
        text remark
        datetime create_time
        datetime update_time
        varchar creator_id
        varchar updater_id
    }
    
    Department {
        varchar department_id PK
        varchar department_code
        varchar parent_id FK
        varchar department_name
        enum department_type
        enum department_status
        varchar department_head
        text description
        int sort_order
        datetime create_time
        datetime update_time
        varchar creator_id
        varchar updater_id
    }
    
    Role {
        varchar role_id PK
        varchar role_code
        varchar role_name
        enum role_source
        varchar store_id FK
        enum role_scope
        json dept_ids
        enum role_status
        text description
        datetime create_time
        datetime update_time
        varchar creator_id
        varchar updater_id
    }
    
    Menu {
        varchar menu_id PK
        varchar menu_code UK
        varchar parent_id FK
        varchar menu_name
        enum menu_type
        enum menu_side
        varchar menu_icon
        varchar menu_path
        varchar component
        varchar permission
        int sort_order
        boolean is_visible
        boolean is_cache
        enum menu_status
        datetime create_time
        datetime update_time
        varchar creator_id
        varchar updater_id
    }
    
    UserStoreRole {
        varchar user_store_role_id PK
        varchar user_id FK
        varchar store_id FK
        varchar department_id FK
        varchar role_id FK
        varchar position
        boolean is_primary
        datetime create_time
        varchar creator_id
    }
    
    RoleMenu {
        varchar role_menu_id PK
        varchar role_id FK
        varchar menu_id FK
        datetime create_time
        varchar creator_id
    }
```

## 详细关系说明

### 1. 用户与角色关系 (User ↔ Role)
- **关系类型**：多对多关系（通过UserStoreRole中间表）
- **关联字段**：User.user_id ↔ UserStoreRole.user_id ↔ Role.role_id
- **业务含义**：一个用户可以在多个门店担任不同角色，一个角色可以分配给多个用户
- **完整性约束**：
  - 每个用户必须至少有一个角色
  - 每个用户有且仅有一个主角色（is_primary=true）
  - Factory用户最多只能分配一个门店角色
  - 删除用户时需要清理所有角色关联

### 2. 门店层级关系 (Store ↔ Store)
- **关系类型**：自关联的一对多关系
- **关联字段**：Store.parent_id ↔ Store.store_id
- **业务含义**：门店支持多级层级结构，形成树形组织架构
- **完整性约束**：
  - 总部门店作为根节点，parent_id为null
  - 不允许循环引用
  - 删除门店时需要处理子门店
  - 门店层级影响权限继承

### 3. 部门层级关系 (Department ↔ Department)
- **关系类型**：自关联的一对多关系
- **关联字段**：Department.parent_id ↔ Department.department_id
- **业务含义**：部门支持多级层级结构，形成树形组织架构
- **完整性约束**：
  - 支持多级部门嵌套
  - 不允许循环引用
  - 删除部门时需要检查子部门和关联用户
  - 部门层级影响数据权限范围

### 4. 菜单层级关系 (Menu ↔ Menu)
- **关系类型**：自关联的一对多关系
- **关联字段**：Menu.parent_id ↔ Menu.menu_id
- **业务含义**：菜单支持多级层级结构，形成树形功能导航
- **完整性约束**：
  - 支持目录、菜单、按钮三级结构
  - 不允许循环引用
  - 删除菜单时需要处理子菜单
  - 菜单层级影响权限继承

### 5. 角色与菜单权限关系 (Role ↔ Menu)
- **关系类型**：多对多关系（通过RoleMenu中间表）
- **关联字段**：Role.role_id ↔ RoleMenu.role_id ↔ Menu.menu_id
- **业务含义**：角色可以拥有多个菜单权限，菜单可以分配给多个角色
- **完整性约束**：
  - 角色权限变更立即生效
  - 父菜单权限包含子菜单权限
  - 删除角色时清理权限关联
  - 删除菜单时清理权限关联

### 6. 角色与门店关系 (Role ↔ Store)
- **关系类型**：多对一关系
- **关联字段**：Role.store_id ↔ Store.store_id
- **业务含义**：Store类型角色必须归属于特定门店，Factory角色不归属门店
- **完整性约束**：
  - Store角色必须指定所属门店
  - Factory角色的store_id为null
  - 删除门店时需要处理相关角色
  - 门店状态变更影响角色可用性

### 7. 用户门店部门关系 (UserStoreRole ↔ Store/Department)
- **关系类型**：多对一关系
- **关联字段**：
  - UserStoreRole.store_id ↔ Store.store_id
  - UserStoreRole.department_id ↔ Department.department_id
- **业务含义**：用户在特定门店的特定部门担任角色
- **完整性约束**：
  - 部门必须属于对应门店
  - 用户可以在多个门店担任角色
  - 删除门店或部门时需要处理用户关联

## 数据流分析

### 1. 用户权限计算流程
```
用户登录 → 查询用户角色 → 合并角色权限 → 生成用户权限集合 → 缓存权限信息
```

### 2. 数据权限过滤流程
```
用户请求数据 → 获取用户数据权限范围 → 根据权限范围过滤数据 → 返回可访问数据
```

### 3. 组织架构变更流程
```
修改组织关系 → 重新计算权限继承 → 更新相关用户权限 → 清理权限缓存 → 通知权限变更
```

## 业务约束和规则

### 1. 数据完整性约束
- **主键约束**：所有实体必须有唯一主键
- **外键约束**：关联关系必须保证数据一致性
- **唯一性约束**：用户名、门店编码、菜单编码等必须唯一
- **非空约束**：关键字段不允许为空

### 2. 业务逻辑约束
- **用户类型约束**：Factory用户最多只能有一个门店角色
- **主角色约束**：每个用户有且仅有一个主角色
- **层级约束**：不允许循环层级关系
- **权限约束**：角色权限不能超越其来源范围

### 3. 多租户隔离约束
- **数据隔离**：Store用户只能访问所属门店数据
- **权限隔离**：Store角色不能跨门店使用
- **功能隔离**：按用户类型控制功能访问

### 4. 状态管理约束
- **级联状态**：上级实体状态变更影响下级实体
- **权限状态**：禁用状态的实体相关权限失效
- **删除约束**：存在关联关系时不允许删除

## 扩展性设计

### 1. 字段扩展
- 预留扩展字段用于未来业务需求
- 使用JSON字段存储灵活配置信息
- 支持自定义属性配置

### 2. 关系扩展
- 支持更复杂的组织架构关系
- 预留中间表支持多对多关系扩展
- 支持权限模型的扩展

### 3. 性能优化
- 权限信息缓存机制
- 树形结构索引优化
- 大数据量分页查询优化

---
**维护说明**：实体关系设计应随业务发展持续优化，确保数据模型的准确性和扩展性。