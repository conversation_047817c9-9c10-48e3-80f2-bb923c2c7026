 # 系统管理-术语定义

## 术语统一说明
本文档对系统管理业务中涉及的核心术语进行统一定义，确保业务理解的一致性。

## 核心业务术语

### 1. 用户相关术语

**用户 (User)**
- 定义：系统中的登录账号，具有唯一身份标识的个体
- 特性：拥有用户名、密码、基本信息和权限
- 用途：身份认证、权限控制、操作审计

**用户名 (Username)**
- 定义：用户的唯一登录标识符
- 格式：字母、数字、下划线组合，3-50个字符
- 特性：全局唯一性，不可重复
- 用途：用户登录、身份识别

**用户类型 (User Type)**
- 定义：用户的业务分类
- 枚举值：Factory（总部用户）、Store（门店用户）
- 作用：权限控制、数据访问范围限定

**用户状态 (User Status)**
- 定义：用户账号的当前状态
- 枚举值：Normal（正常）、Disabled（禁用）
- 作用：控制用户登录和系统访问权限

**主角色 (Primary Role)**
- 定义：用户的主要角色身份
- 特性：每个用户有且仅有一个主角色
- 用途：确定用户的主要工作职责和权限

### 2. 门店相关术语

**门店 (Store)**
- 定义：经销商的营业网点或办公场所
- 特性：具有层级结构、地理位置、业务属性
- 用途：组织架构管理、权限控制、业务分组

**门店编码 (Store Code)**
- 定义：门店的唯一标识编码
- 格式：字母数字组合，通常包含地区标识
- 特性：全局唯一性，不可重复
- 用途：门店识别、数据关联

**门店类型 (Store Type)**
- 定义：门店的业务类型分类
- 枚举值：Headquarter（总部）、Main（主店）、Branch（分店）、Warehouse（仓库）
- 作用：权限控制、业务流程区分

**门店属性 (Store Properties)**
- 定义：门店的业务属性配置
- 枚举值：Sales（销售）、After_Sales（售后）
- 特性：可以同时具备多种属性
- 用途：业务功能开放控制

**门店状态 (Store Status)**
- 定义：门店的运营状态
- 枚举值：Active（正常）、Inactive（停用）
- 作用：控制门店业务功能可用性

### 3. 部门相关术语

**部门 (Department)**
- 定义：组织内的职能单位或工作组
- 特性：具有层级结构、职责分工、人员归属
- 用途：组织管理、权限控制、数据权限

**部门编码 (Department Code)**
- 定义：部门的唯一标识编码
- 格式：字母数字组合，通常反映部门性质
- 特性：门店内唯一性
- 用途：部门识别、数据关联

**部门类型 (Department Type)**
- 定义：部门的职能类型分类
- 枚举值：Business（业务部门）、Support（支持部门）、Management（管理部门）
- 作用：权限配置、业务流程区分

**部门状态 (Department Status)**
- 定义：部门的当前状态
- 枚举值：Normal（正常）、Disabled（禁用）
- 作用：控制部门功能可用性

**部门负责人 (Department Head)**
- 定义：部门的主要负责人
- 特性：具有部门管理权限
- 用途：部门管理、权限控制

### 4. 角色相关术语

**角色 (Role)**
- 定义：一组权限的集合，代表特定的业务职责
- 特性：可分配给用户，用户可拥有多个角色
- 用途：权限管理、职责划分

**角色编码 (Role Code)**
- 定义：角色的唯一标识编码
- 格式：字母数字组合，通常反映角色性质
- 特性：门店内唯一性（Store角色）或全局唯一性（Factory角色）
- 用途：角色识别、权限配置

**角色来源 (Role Source)**
- 定义：角色的创建来源和适用范围
- 枚举值：Factory（总部角色）、Store（门店角色）
- 作用：权限范围控制、角色管理

**角色权限范围 (Role Scope)**
- 定义：角色的数据访问权限范围
- 枚举值：
  - All（全部数据）
  - Custom（自定义部门）
  - Department（本部门）
  - DepartmentAndBelow（本部门及下级）
  - OnlyPersonal（仅个人）
- 作用：数据权限控制

**角色状态 (Role Status)**
- 定义：角色的当前状态
- 枚举值：Normal（正常）、Disabled（禁用）
- 作用：控制角色可用性

### 5. 菜单相关术语

**菜单 (Menu)**
- 定义：系统功能的组织结构和导航体系
- 特性：具有层级结构、权限控制、访问路径
- 用途：功能导航、权限控制、系统架构

**菜单编码 (Menu Code)**
- 定义：菜单的唯一标识编码
- 格式：字母数字组合，通常反映功能模块
- 特性：全局唯一性
- 用途：菜单识别、权限配置

**菜单类型 (Menu Type)**
- 定义：菜单的功能类型分类
- 枚举值：Directory（目录）、Menu（菜单）、Button（按钮）
- 作用：权限控制粒度、界面渲染

**菜单归属 (Menu Side)**
- 定义：菜单的使用端归属
- 枚举值：Factory（总部端）、Dealer（经销商端）
- 作用：权限控制、界面区分

**权限标识 (Permission)**
- 定义：菜单或功能的权限标识符
- 格式：模块:功能:操作（如：system:user:add）
- 用途：权限验证、功能控制

**菜单状态 (Menu Status)**
- 定义：菜单的当前状态
- 枚举值：Normal（正常）、Disabled（禁用）
- 作用：控制菜单可用性

### 6. 权限相关术语

**权限 (Permission)**
- 定义：执行特定操作或访问特定资源的授权
- 特性：可分配给角色，通过角色传递给用户
- 用途：访问控制、功能限制

**菜单权限 (Menu Permission)**
- 定义：访问特定菜单或功能的权限
- 特性：基于菜单权限标识进行控制
- 用途：功能访问控制

**数据权限 (Data Permission)**
- 定义：访问特定数据范围的权限
- 特性：基于角色权限范围进行控制
- 用途：数据访问控制

**RBAC (Role-Based Access Control)**
- 定义：基于角色的访问控制模型
- 特性：用户-角色-权限三层结构
- 用途：权限管理架构

### 7. 组织架构术语

**组织架构 (Organization Structure)**
- 定义：门店和部门的层级关系结构
- 特性：树形结构、层级关系、权限继承
- 用途：权限控制、业务管理

**层级关系 (Hierarchy Relationship)**
- 定义：上下级组织单位之间的关系
- 特性：父子关系、层级传递
- 用途：权限继承、数据范围控制

**总部 (Headquarter)**
- 定义：组织架构的最高层级单位
- 特性：唯一性、全局管理权限
- 用途：全局权限控制、系统管理

### 8. 系统管理术语

**系统管理员 (System Administrator)**
- 定义：具有系统管理权限的用户角色
- 权限：用户管理、角色管理、权限配置
- 用途：系统维护、权限管理

**超级管理员 (Super Administrator)**
- 定义：具有最高系统权限的用户角色
- 权限：全系统管理权限，包括Factory级别权限
- 用途：系统总体管理、安全控制

**多租户 (Multi-Tenant)**
- 定义：支持多个独立业务实体共享系统资源
- 特性：数据隔离、权限隔离、资源共享
- 用途：SaaS架构、数据安全

### 9. 业务流程术语

**用户入职 (User Onboarding)**
- 定义：新员工加入系统的完整流程
- 包含：账号创建、信息录入、角色分配、权限配置
- 用途：新用户系统准入管理

**角色分配 (Role Assignment)**
- 定义：为用户分配角色和权限的过程
- 特性：可分配多个角色、支持跨门店分配
- 用途：权限配置、职责划分

**权限配置 (Permission Configuration)**
- 定义：为角色配置具体权限的过程
- 包含：菜单权限配置、数据权限配置
- 用途：权限管理、功能控制

### 10. 数据权限术语

**全部数据权限 (All Data Permission)**
- 定义：可访问系统中所有数据的权限
- 适用：超级管理员、系统管理员
- 用途：系统管理、数据维护

**自定义数据权限 (Custom Data Permission)**
- 定义：可访问指定部门数据的权限
- 特性：灵活配置、精确控制
- 用途：跨部门业务、特殊权限需求

**本部门数据权限 (Department Data Permission)**
- 定义：只能访问本部门数据的权限
- 特性：部门数据隔离
- 用途：部门级权限控制

**部门及下级数据权限 (Department And Below Permission)**
- 定义：可访问本部门及下级部门数据的权限
- 特性：层级权限继承
- 用途：管理层权限控制

**仅个人数据权限 (Personal Data Only Permission)**
- 定义：只能访问个人创建数据的权限
- 特性：最小权限原则
- 用途：一般员工权限控制

## 术语使用规范

1. **统一性原则**：在所有文档和系统中，必须使用本文档定义的标准术语
2. **精确性原则**：避免使用模糊或歧义的表述，严格按照定义使用
3. **一致性原则**：同一概念在不同场景下使用相同术语
4. **完整性原则**：新增术语时需要完整定义其含义、用途和规则

## 术语关系图

```
系统管理 (System Management)
├── 用户管理 (User Management)
│   ├── 用户 (User)
│   ├── 用户类型 (User Type)
│   ├── 用户状态 (User Status)
│   └── 主角色 (Primary Role)
├── 门店管理 (Store Management)
│   ├── 门店 (Store)
│   ├── 门店类型 (Store Type)
│   ├── 门店属性 (Store Properties)
│   └── 门店状态 (Store Status)
├── 部门管理 (Department Management)
│   ├── 部门 (Department)
│   ├── 部门类型 (Department Type)
│   ├── 部门状态 (Department Status)
│   └── 部门负责人 (Department Head)
├── 角色管理 (Role Management)
│   ├── 角色 (Role)
│   ├── 角色来源 (Role Source)
│   ├── 角色权限范围 (Role Scope)
│   └── 角色状态 (Role Status)
├── 菜单管理 (Menu Management)
│   ├── 菜单 (Menu)
│   ├── 菜单类型 (Menu Type)
│   ├── 菜单归属 (Menu Side)
│   ├── 权限标识 (Permission)
│   └── 菜单状态 (Menu Status)
└── 权限控制 (Permission Control)
    ├── 菜单权限 (Menu Permission)
    ├── 数据权限 (Data Permission)
    ├── RBAC模型 (RBAC Model)
    └── 多租户隔离 (Multi-Tenant Isolation)
```

---
**维护说明**：本术语表应随业务发展持续更新，确保术语定义的准确性和完整性。