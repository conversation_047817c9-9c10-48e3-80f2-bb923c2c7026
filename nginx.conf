server {
    listen       80;
    server_name  localhost;

    #access_log  /var/log/nginx/host.access.log  main;
    
    # 开启gzip
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        # 关键配置：尝试查找文件，如果不存在则返回index.html
        try_files $uri $uri/ /index.html;
        
        # 确保正确的MIME类型
        location ~* \.json$ {
            add_header Content-Type application/json;
            expires 1h;
        }
    }

    # 静态资源缓存设置 - 添加了JSON文件支持
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|json|woff|woff2|ttf|eot|svg)$ {
        root /usr/share/nginx/html;
        expires 1d;
        add_header Cache-Control "public, immutable";
        
        # 确保JSON文件正确的MIME类型
        location ~* \.json$ {
            add_header Content-Type application/json;
        }
    }

    # 特别处理assets目录下的文件
    location ^~ /assets/ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 确保JSON文件正确的MIME类型
        location ~* \.json$ {
            add_header Content-Type application/json;
        }
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}