{"name": "dms-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "split-locales": "node scripts/split-locales.cjs", "merge-locales": "node scripts/merge-locales.cjs"}, "dependencies": {"axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.1", "pinia": "^3.0.1", "signature_pad": "^5.0.9", "vue": "^3.5.13", "vue-i18n": "^10.0.4", "vue-router": "^4.5.0"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@types/signature_pad": "^2.3.6", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "sass": "^1.89.1", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}