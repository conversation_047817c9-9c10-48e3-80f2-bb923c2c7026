import request from '@/api';
import { mockAppointmentList, mockServiceAdvisors, mockTechnicians } from '@/mock/data/afterSales';
import type {
  AppointmentListItem,
  AppointmentListParams,
  AppointmentDetail,
  AppointmentStatistics,
  PaginationResponse,
  QualityInspectionParams,
  QualityInspectionResponse,
  ServiceAdvisor,
  Technician
} from '@/types/module.d.ts';
import { ElMessage } from 'element-plus';

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('AfterSales Module')

/**
 * @description 获取预约列表
 * @param params 查询参数
 * @returns Promise<PaginationResponse<AppointmentListItem>>
 */
export const getAppointmentList = (params: AppointmentListParams): Promise<PaginationResponse<AppointmentListItem>> => {
  console.log(USE_MOCK_API)
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page = 1, pageSize = 10, ...filter } = params;

        // 模拟数据过滤逻辑
        const filteredList = mockAppointmentList.filter(item => {
          // 支持多字段搜索
          const matchesAppointmentId = !filter.appointmentId || item.id.toLowerCase().includes(filter.appointmentId.toLowerCase());
          const matchesLicensePlate = !filter.licensePlate || item.licensePlate.toLowerCase().includes(filter.licensePlate.toLowerCase());
          const matchesReservationPhone = !filter.reservationPhone || item.reservationContactPhone.includes(filter.reservationPhone);
          const matchesServicePhone = !filter.servicePhone || item.serviceContactPhone.includes(filter.servicePhone);

          const matchesStatus = !filter.status || item.status === filter.status;
          const matchesServiceType = !filter.serviceType || item.serviceType === filter.serviceType;
          const matchesServiceAdvisor = !filter.serviceAdvisorId || item.serviceAdvisor?.id === filter.serviceAdvisorId;
          const matchesTechnician = !filter.technicianId || item.technician?.id === filter.technicianId;

          let matchesDateRange = true;
          if (filter.dateRange && filter.dateRange.length === 2) {
            const appointmentDate = new Date(item.appointmentTime).toDateString();
            const startDate = new Date(filter.dateRange[0]).toDateString();
            const endDate = new Date(filter.dateRange[1]).toDateString();
            matchesDateRange = appointmentDate >= startDate && appointmentDate <= endDate;
          }

          return matchesAppointmentId && matchesLicensePlate && matchesReservationPhone &&
                 matchesServicePhone && matchesStatus && matchesServiceType &&
                 matchesServiceAdvisor && matchesTechnician && matchesDateRange;
        });

        // 模拟分页逻辑
        const total = filteredList.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const list = filteredList.slice(start, end);

        resolve({
          list,
          total,
          page,
          pageSize
        });
      }, 500);
    });
  } else {
    return request.get<any, PaginationResponse<AppointmentListItem>>('/after-sales/appointments', { params });
  }
};

/**
 * @description 获取预约统计数据
 * @param params 查询参数
 * @returns Promise<AppointmentStatistics>
 */
export const getAppointmentStatistics = (params: AppointmentListParams): Promise<AppointmentStatistics> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 基于当前筛选条件计算统计数据
        const filteredList = mockAppointmentList.filter(item => {
          // 支持多字段搜索
          const matchesAppointmentId = !params.appointmentId || item.id.toLowerCase().includes(params.appointmentId.toLowerCase());
          const matchesLicensePlate = !params.licensePlate || item.licensePlate.toLowerCase().includes(params.licensePlate.toLowerCase());
          const matchesReservationPhone = !params.reservationPhone || item.reservationContactPhone.includes(params.reservationPhone);
          const matchesServicePhone = !params.servicePhone || item.serviceContactPhone.includes(params.servicePhone);

          const matchesStatus = !params.status || item.status === params.status;
          const matchesServiceType = !params.serviceType || item.serviceType === params.serviceType;
          const matchesServiceAdvisor = !params.serviceAdvisorId || item.serviceAdvisor?.id === params.serviceAdvisorId;
          const matchesTechnician = !params.technicianId || item.technician?.id === params.technicianId;

          let matchesDateRange = true;
          if (params.dateRange && params.dateRange.length === 2) {
            const appointmentDate = new Date(item.appointmentTime).toDateString();
            const startDate = new Date(params.dateRange[0]).toDateString();
            const endDate = new Date(params.dateRange[1]).toDateString();
            matchesDateRange = appointmentDate >= startDate && appointmentDate <= endDate;
          }

          return matchesAppointmentId && matchesLicensePlate && matchesReservationPhone &&
                 matchesServicePhone && matchesStatus && matchesServiceType &&
                 matchesServiceAdvisor && matchesTechnician && matchesDateRange;
        });

        const statistics: AppointmentStatistics = {
          total: filteredList.length,
          notArrivedCount: filteredList.filter(item => item.status === 'not_arrived').length,
          arrivedCount: filteredList.filter(item => item.status === 'arrived').length,
          cancelledCount: filteredList.filter(item => item.status === 'cancelled').length,
          noShowCount: filteredList.filter(item => item.status === 'no_show').length,
          maintenanceCount: filteredList.filter(item => item.serviceType === 'maintenance').length,
          repairCount: filteredList.filter(item => item.serviceType === 'repair').length,
          arrivalRate: filteredList.length > 0 ?
            Math.round((filteredList.filter(item => item.status === 'arrived').length / filteredList.length) * 100) : 0
        };

        resolve(statistics);
      }, 300);
    });
  } else {
    return request.get<any, AppointmentStatistics>('/after-sales/appointments/statistics', { params });
  }
};

/**
 * @description 获取预约详情
 * @param id 预约单号
 * @returns Promise<AppointmentDetail>
 */
export const getAppointmentDetail = (id: string): Promise<AppointmentDetail> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const appointment = mockAppointmentList.find(item => item.id === id);
        if (appointment) {
          // 模拟详情数据，补充额外字段
          const detail: AppointmentDetail = {
            ...appointment,
            reservationContactName: appointment.customerName,
            reservationContactPhone: appointment.customerPhone,
            serviceContactName: appointment.customerName,
            serviceContactPhone: appointment.customerPhone,
            store: {
              id: 'store_001',
              name: '北京朝阳店',
              address: '北京市朝阳区工体北路甲6号'
            },
            paymentStatus: 'paid',
            paymentAmount: appointment.maintenancePackage?.price || 0,
            paymentOrderNumber: 'PAY_' + Date.now(),
            // 操作历史已移除
          };
          resolve(detail);
        } else {
          reject(new Error('预约单不存在'));
        }
      }, 300);
    });
  } else {
    return request.get<any, AppointmentDetail>(`/after-sales/appointments/${id}`);
  }
};

/**
 * @description 创建质检单
 * @param params 质检单创建参数
 * @returns Promise<QualityInspectionResponse>
 */
export const createQualityInspection = (params: QualityInspectionParams): Promise<QualityInspectionResponse> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const inspectionId = 'QC_' + Date.now();

        // 更新预约状态为"检测中"
        const appointment = mockAppointmentList.find(item => item.id === params.appointmentId);
        if (appointment) {
          appointment.status = 'arrived'; // 模拟状态更新
          appointment.updatedAt = new Date().toISOString();
        }

        ElMessage.success('质检单创建成功');
        resolve({
          inspectionId,
          success: true,
          message: '质检单创建成功'
        });
      }, 500);
    });
  } else {
    return request.post<any, QualityInspectionResponse>('/after-sales/quality-inspections', params);
  }
};

/**
 * @description 更新预约状态
 * @param id 预约单号
 * @param status 新状态
 * @returns Promise<boolean>
 */
export const updateAppointmentStatus = (id: string, status: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const appointment = mockAppointmentList.find(item => item.id === id);
        if (appointment) {
          appointment.status = status as any;
          appointment.updatedAt = new Date().toISOString();
          ElMessage.success('操作成功！');
          resolve(true);
        } else {
          ElMessage.error('操作失败！');
          reject(new Error('预约单不存在'));
        }
      }, 300);
    });
  } else {
    return request.put<any, boolean>(`/after-sales/appointments/${id}/status`, { status });
  }
};

/**
 * @description 取消预约
 * @param id 预约单号
 * @param reason 取消原因
 * @returns Promise<boolean>
 */
export const cancelAppointment = (id: string, reason?: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const appointment = mockAppointmentList.find(item => item.id === id);
        if (appointment) {
          appointment.status = 'cancelled';
          appointment.updatedAt = new Date().toISOString();
          ElMessage.success('操作成功！');
          resolve(true);
        } else {
          ElMessage.error('操作失败！');
          reject(new Error('预约单不存在'));
        }
      }, 300);
    });
  } else {
    return request.put<any, boolean>(`/after-sales/appointments/${id}/cancel`, { reason });
  }
};
