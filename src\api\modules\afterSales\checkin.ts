// src/api/modules/afterSales/checkin.ts

import request from '@/api';
import type { 
  CheckinListParams, 
  CheckinPageResponse, 
  VehicleInfo,
  CheckinFormData 
} from '@/types/afterSales/checkin.d.ts';
import { 
  getCheckinList as getMock<PERSON>heckinList,
  queryVehicleInfo as queryMockVehicleInfo,
  addCheckinRecord as addMockCheckinRecord,
  updateCheckinRecord as updateMockCheckinRecord,
  deleteCheckinRecord as deleteMockCheckinRecord,
  createRelatedRepairOrder as createMockRelatedRepairOrder
} from '@/mock/data/afterSales/checkin';
import { USE_MOCK_API } from '@/utils/mock-config';

const USE_MOCK_API_TEMP = true;

export const getCheckinList = (params: CheckinListParams): Promise<CheckinPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockCheckinList(params);
  }
  return request.get<any, CheckinPageResponse>('/after-sales/checkin/list', { params });
};

export const queryVehicleInfo = (licensePlate: string): Promise<VehicleInfo | null> => {
  if (USE_MOCK_API_TEMP) {
    return queryMockVehicleInfo(licensePlate);
  }
  return request.get<any, VehicleInfo | null>(`/after-sales/checkin/vehicle-info/${licensePlate}`);
};

export const addCheckinRecord = (data: CheckinFormData): Promise<{ success: boolean; checkinId: string }> => {
  if (USE_MOCK_API_TEMP) {
    return addMockCheckinRecord(data);
  }
  return request.post<any, { success: boolean; checkinId: string }>('/after-sales/checkin', data);
};

export const updateCheckinRecord = (checkinId: string, data: Partial<CheckinFormData>): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return updateMockCheckinRecord(checkinId, data);
  }
  return request.put<any, { success: boolean }>(`/after-sales/checkin/${checkinId}`, data);
};

export const deleteCheckinRecord = (checkinId: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return deleteMockCheckinRecord(checkinId);
  }
  return request.delete<any, { success: boolean }>(`/after-sales/checkin/${checkinId}`);
};

export const createRelatedRepairOrder = (checkinId: string): Promise<{ success: boolean; repairOrderId: string }> => {
  if (USE_MOCK_API_TEMP) {
    return createMockRelatedRepairOrder(checkinId);
  }
  return request.post<any, { success: boolean; repairOrderId: string }>(`/after-sales/checkin/${checkinId}/repair-order`);
};
