// src/api/modules/afterSales/workAssignment.ts

import request from '@/api';
import type { 
  WorkOrderListParams, 
  WorkOrderPageResponse, 
  TechnicianInfo,
  AssignWorkOrderRequest,
  ReassignWorkOrderRequest,
  AssignmentResult,
  TechnicianWorkload,
  WorkAssignmentStatistics
} from '@/types/afterSales/workAssignment.d.ts';
import { 
  getWorkOrderList as getMockWorkOrderList,
  getTechnicianList as getMockTechnicianList,
  assignWorkOrder as assignMockWorkOrder,
  reassignWorkOrder as reassignMockWorkOrder,
  getTechnicianWorkload as getMockTechnicianWorkload,
  getAssignmentStatistics as getMockAssignmentStatistics
} from '@/mock/data/afterSales/workAssignment';

const USE_MOCK_API_TEMP = true;

export const getWorkOrderList = (params: WorkOrderListParams): Promise<WorkOrderPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkOrderList(params);
  }
  return request.get<any, WorkOrderPageResponse>('/after-sales/work-assignment/orders', { params });
};

export const getTechnicianList = (): Promise<TechnicianInfo[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianList();
  }
  return request.get<any, TechnicianInfo[]>('/after-sales/work-assignment/technicians');
};

export const assignWorkOrder = (data: AssignWorkOrderRequest): Promise<AssignmentResult> => {
  if (USE_MOCK_API_TEMP) {
    return assignMockWorkOrder(data);
  }
  return request.post<any, AssignmentResult>('/after-sales/work-assignment/assign', data);
};

export const reassignWorkOrder = (data: ReassignWorkOrderRequest): Promise<AssignmentResult> => {
  if (USE_MOCK_API_TEMP) {
    return reassignMockWorkOrder(data);
  }
  return request.post<any, AssignmentResult>('/after-sales/work-assignment/reassign', data);
};

export const getTechnicianWorkload = (): Promise<TechnicianWorkload[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianWorkload();
  }
  return request.get<any, TechnicianWorkload[]>('/after-sales/work-assignment/workload');
};

export const getAssignmentStatistics = (): Promise<WorkAssignmentStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockAssignmentStatistics();
  }
  return request.get<any, WorkAssignmentStatistics>('/after-sales/work-assignment/statistics');
};
