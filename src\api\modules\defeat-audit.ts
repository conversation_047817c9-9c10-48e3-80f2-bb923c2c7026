// 战败审核API模块
import request from '@/api'
import type {
  PaginationResponse,
  DefeatApplication,
  GetDefeatApplicationListRequest,
  AuditDefeatApplicationRequest,
  ApiResponse
} from '@/api/types/defeat-audit'
import { AuditResult } from '@/api/types/defeat-audit'

// 获取环境变量，用于判断是否使用本地 Mock 数据
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('DefeatAudit Module')

/**
 * 获取战败申请列表
 */
export const getDefeatApplicationList = async (params: GetDefeatApplicationListRequest): Promise<ApiResponse<PaginationResponse<DefeatApplication>>> => {
  if (USE_MOCK_API) {
    // Mock 数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData: PaginationResponse<DefeatApplication> = {
          records: [
            {
              id: 1,
              applicationNo: 'DEF-20240615-001',
              applicantName: '张三',
              prospectId: 1001,
              prospectName: '李四',
              prospectPhone: '13800138001',
              defeatReason: '价格因素',
              defeatDescription: '客户认为价格过高，已购买竞品',
              applicationTime: '2024-06-15 10:30:00',
              auditResult: AuditResult.PENDING,
              attachments: []
            }
          ],
          total: 1,
          pageSize: params.pageSize
        }
        resolve({
          code: '200',
          message: '操作成功',
          result: mockData
        })
      }, 500)
    })
  } else {
    // 过滤掉空值参数
    const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {
      // 如果值不为空字符串、null或undefined，则添加到参数对象中
      if (value !== '' && value !== null && value !== undefined) {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, unknown>)

    // 发送请求
    return await request.post('/defeat-audit/applications', filteredParams)
  }
}

/**
 * 审核战败申请
 */
export const auditDefeatApplication = async (data: AuditDefeatApplicationRequest): Promise<ApiResponse<null>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock: 战败申请审核成功', data)
        resolve({
          code: '200',
          message: '操作成功',
          result: null
        })
      }, 300)
    })
  } else {
    return await request.post('/prospects/store/defeat/audit', data)
  }
}

/**
 * 导出战败申请列表
 */
export const exportDefeatApplications = async (params: Record<string, unknown>): Promise<Blob> => {
  if (USE_MOCK_API) {
    // 创建一个简单的Excel文件作为Mock
    const mockExcel = new Blob(['Mock Excel Content'], { type: 'application/vnd.ms-excel' })
    return Promise.resolve(mockExcel)
  } else {
    const response = await request.post('/prospects/defeat-audit/export', params, {
      responseType: 'blob'
    })
    return response as unknown as Blob
  }
}

// 导出所有API方法
export const defeatAuditApi = {
  getDefeatApplicationList,
  auditDefeatApplication,
  exportDefeatApplications
}
