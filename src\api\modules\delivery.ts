import request from '@/api'; // 导入 Axios 封装实例
import { mockDeliveryList } from '@/mock/data/delivery'; // 导入模拟数据
import type {
  DeliveryOrderItem,
  DeliveryOrderParams,
  SubmitConfirmRequest,
  DeliveryConfirmRequest,
  PaginationResponse,
  ExportSettings
} from '@/types/module'

// 获取环境变量，用于判断是否使用本地 Mock 数据
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Delivery Module')

/**
 * @description 获取交车单列表
 * @param params 查询参数 (DeliveryOrderParams)
 * @returns Promise<PaginationResponse<DeliveryOrderItem>> 交车单列表和总数
 */
export const getDeliveryOrderList = async (params: DeliveryOrderParams): Promise<PaginationResponse<DeliveryOrderItem>> => {
  if (USE_MOCK_API) {
    // 在开发模式下使用本地 Mock 数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page = 1, pageSize = 20, ...filter } = params;

        // 模拟数据过滤
        const filteredList = mockDeliveryList.filter((item: DeliveryOrderItem) => {
          return (!filter.deliveryNumber || item.deliveryNumber.includes(filter.deliveryNumber)) &&
                 (!filter.orderNumber || item.orderNumber.includes(filter.orderNumber)) &&
                 (!filter.customerName || item.customerName.includes(filter.customerName)) &&
                 (!filter.deliveryStatus || item.deliveryStatus === filter.deliveryStatus);
        });

        const total = filteredList.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const list = filteredList.slice(start, end);

        resolve({ list, total, page, pageSize });
      }, 500);
    });
  } else {
    // 连接真实后端 API
    const response = await request.get<PaginationResponse<DeliveryOrderItem>>('/delivery/orders', { params });
    return response.result;
  }
};

/**
 * @description 提交确认操作
 * @param data 提交确认请求参数
 * @returns Promise<boolean> 操作成功与否
 */
export const submitConfirm = async (data: SubmitConfirmRequest): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = mockDeliveryList.find((item: DeliveryOrderItem) => item.deliveryNumber === data.deliveryNumber);
        if (order && order.deliveryStatus === 'pending_delivery') {
          order.deliveryStatus = 'pending_confirm';
          order.updateTime = new Date().toISOString();
        }
        resolve(true);
      }, 300);
    });
  } else {
    const response = await request.put<boolean>(`/delivery/orders/${data.deliveryNumber}/submit-confirm`);
    return response.result;
  }
};

/**
 * @description 交车确认操作
 * @param data 交车确认请求参数
 * @returns Promise<boolean> 操作成功与否
 */
export const deliveryConfirm = async (data: DeliveryConfirmRequest): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = mockDeliveryList.find((item: DeliveryOrderItem) => item.deliveryNumber === data.deliveryNumber);
        if (order && order.deliveryStatus === 'pending_confirm') {
          order.deliveryStatus = 'delivered';
          order.deliveryTime = data.deliveryTime;
          order.deliveryNotes = data.deliveryNotes;
          order.customerConfirmed = true;
          order.customerConfirmTime = new Date().toISOString();
          order.updateTime = new Date().toISOString();
        }
        resolve(true);
      }, 300);
    });
  } else {
    const response = await request.put<boolean>('/delivery/confirm', data);
    return response.result;
  }
};

/**
 * @description 获取交车单详情
 * @param deliveryNumber 交车单号
 * @returns Promise<DeliveryOrderItem | null> 交车单详情或 null
 */
export const getDeliveryOrderDetail = async (deliveryNumber: string): Promise<DeliveryOrderItem | null> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = mockDeliveryList.find((item: DeliveryOrderItem) => item.deliveryNumber === deliveryNumber);
        resolve(order || null);
      }, 200);
    });
  } else {
    const response = await request.get<DeliveryOrderItem>(`/delivery/orders/${deliveryNumber}`);
    return response.result;
  }
};

/**
 * @description 导出交车数据
 * @param settings 导出设置
 * @param params 筛选参数
 * @returns Promise<Blob> 导出文件
 */
export const exportDeliveryData = async (settings: ExportSettings, params: DeliveryOrderParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟导出文件
        const csvContent = "交车单号,订单编号,购车人,手机号,VIN,车型,状态\n" +
          mockDeliveryList.map((item: DeliveryOrderItem) =>
            `${item.deliveryNumber},${item.orderNumber},${item.customerName},${item.customerPhone},${item.vin},${item.model},${item.deliveryStatus}`
          ).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        resolve(blob);
      }, 1000);
    });
  } else {
    const response = await request.post<Blob>('/delivery/export', { settings, params }, {
      responseType: 'blob'
    });
    return response.result;
  }
};

/**
 * @description 上传客户签字照片
 * @param file 照片文件
 * @param deliveryNumber 交车单号
 * @param deliveryTime
 * @returns Promise<string> 上传后的文件路径
 */
export const uploadSignaturePhoto = async (file: File, deliveryNumber: string, deliveryTime: string): Promise<string> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 验证文件格式和大小
        const allowedTypes = ['image/jpeg', 'image/png'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        if (!allowedTypes.includes(file.type)) {
          reject(new Error('仅支持 JPG 和 PNG 格式'));
          return;
        }

        if (file.size > maxSize) {
          reject(new Error('文件大小不能超过 5MB'));
          return;
        }

        // 模拟上传成功
        const filePath = `/uploads/signatures/${deliveryNumber}_${Date.now()}.${file.type.split('/')[1]}`;
        resolve(filePath);
      }, 1000);
    });
  } else {
    const formData = new FormData();
    formData.append('deliveryTime', deliveryTime);
    formData.append('signaturePhoto', "23233");
    formData.append('deliveryNumber', deliveryNumber);

    const response = await request.put<{ filePath: string }>('delivery/orders/'+deliveryNumber+'/delivery-confirm', formData, {

    });
    return response.result.filePath;
  }
};
