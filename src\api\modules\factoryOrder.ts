import request from '@/api/index'
import type {
  OrderStatistics,
  FactoryOrderListItem,
  FactoryOrderListParams,
  FactoryOrderDetail,
  FactoryOrderExportRequest,
  PaginationResponse
} from '@/types/module'
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('FactoryOrder Module')

// Mock数据
const mockOrderStatistics: OrderStatistics = {
  monthlyOrderCount: 2156,
  dailyOrderCount: 72,
  monthlyGrowthRate: 12.5,
  dailyGrowthRate: 8.3,
  topDealers: [
    { dealerName: '吉隆坡中央店', orderCount: 168 },
    { dealerName: '槟城旗舰店', orderCount: 145 },
    { dealerName: '新山商业中心店', orderCount: 132 },
    { dealerName: '怡保市中心店', orderCount: 118 },
    { dealerName: '马六甲历史城店', orderCount: 95 }
  ],
  topVehicles: [
    { model: 'Model A', variant: 'Variant 1', salesCount: 342 },
    { model: 'Model B', variant: 'Variant 2', salesCount: 298 },
    { model: 'Model C', variant: 'Variant 1', salesCount: 256 },
    { model: 'Model A', variant: 'Variant 3', salesCount: 234 },
    { model: 'Model D', variant: 'Variant 1', salesCount: 198 }
  ],
  pendingDeliveryCount: 234,
  lastUpdateTime: '2024-03-15T10:30:00.000Z'
}

const mockOrderList: FactoryOrderListItem[] = [
  {
    id: '1',
    orderNumber: 'ORD202403150001',
    dealerName: '吉隆坡中央店',
    creationTime: '2024-03-15T09:30:00.000Z',
    ordererName: 'A***',
    ordererPhone: '+6012****6789',
    buyerName: 'L***',
    buyerPhone: '+6013****5432',
    buyerCategory: '个人用户',
    model: 'Model A',
    variant: 'Variant 1',
    color: '珍珠白',
    vin: 'VIN1****7890',
    paymentMethod: '贷款',
    loanApprovalStatus: '已通过',
    orderStatus: '已确认',
    orderApprovalStatus: '已审批',
    paymentStatus: '已付定金',
    insuranceStatus: '已投保',
    jpjRegistrationStatus: '待注册'
  },
  {
    id: '2',
    orderNumber: 'ORD202403150002',
    dealerName: '槟城旗舰店',
    creationTime: '2024-03-15T08:15:00.000Z',
    ordererName: 'C***',
    ordererPhone: '+6014****3456',
    buyerName: 'C***',
    buyerPhone: '+6014****3456',
    buyerCategory: '企业用户',
    model: 'Model B',
    variant: 'Variant 2',
    color: '典雅黑',
    vin: 'VIN2****3456',
    paymentMethod: '全款',
    loanApprovalStatus: '无需审批',
    orderStatus: '生产中',
    orderApprovalStatus: '已审批',
    paymentStatus: '已全款',
    insuranceStatus: '已投保',
    jpjRegistrationStatus: '已注册'
  }
]

const mockOrderDetail: FactoryOrderDetail = {
  id: '1',
  orderNumber: 'ORD202403150001',
  creationTime: '2024-03-15T09:30:00.000Z',
  ordererName: 'A***',
  ordererPhone: '+6012****6789',
  buyerName: 'L***',
  buyerPhone: '+6013****5432',
  buyerIdType: 'IC身份证',
  buyerIdNumber: '850123****5678',
  buyerEmail: 'l***@example.com',
  buyerAddress: '***雪兰莪州八打灵再也',
  buyerState: '雪兰莪州',
  buyerCity: '八打灵再也',
  buyerPostcode: '47300',
  dealerRegion: '中央区域',
  dealerCity: '吉隆坡',
  dealerName: '吉隆坡中央店',
  salesConsultant: '张销售',
  model: 'Model A',
  variant: 'Variant 1',
  color: '珍珠白',
  salesSubtotal: 65000.00,
  numberPlatesFee: 500.00,
  accessories: [
    { category: '外观配件', name: '运动保险杠', unitPrice: 1200.00, quantity: 1, totalPrice: 1200.00 }
  ],
  accessoriesTotalAmount: 1200.00,
  invoiceType: '个人',
  invoiceName: 'L***',
  invoicePhone: '+6013****5432',
  invoiceAddress: '***雪兰莪州八打灵再也',
  benefits: [
    { benefitCode: 'WARRANTY001', benefitName: '延长保修', benefitMode: '优惠购买', discountPrice: 500.00, effectiveDate: '2024-03-15', expirationDate: '2027-03-15' }
  ],
  benefitsTotalAmount: 500.00,
  paymentMethod: '贷款',
  loanApprovalStatus: '已通过',
  depositAmount: 10000.00,
  loanAmount: 50000.00,
  balanceAmount: 11000.00,
  insurances: [
    { policyNumber: 'POL2024001', insuranceType: '车辆损失险', insuranceCompany: '太平洋保险', effectiveDate: '2024-03-15', expirationDate: '2025-03-15', insurancePrice: 1200.00 }
  ],
  insurancesTotalAmount: 1200.00,
  insuranceNotes: '保险已生效',
  otrFees: [
    { ticketNumber: 'ROAD2024001', feeItem: '路税', feePrice: 300.00, effectiveDate: '2024-03-15', expirationDate: '2025-03-15' }
  ],
  otrFeesTotalAmount: 300.00,
  changeRecords: [
    { id: '1', originalContent: '车辆颜色：星河蓝', changedContent: '车辆颜色：珍珠白', operator: '张销售', operationTime: '2024-03-14T15:30:00.000Z' }
  ],
  vehicleInvoicePrice: 71000.00,
  remainingReceivable: 11000.00
}

// 获取订单统计概览数据
export const getOrderStatistics = (): Promise<OrderStatistics> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockOrderStatistics)
      }, 800)
    })
  } else {
    return request.get<any, OrderStatistics>('/factory/orders/stats')
  }
}

// 获取厂端订单列表
export const getFactoryOrderList = (params: FactoryOrderListParams): Promise<PaginationResponse<FactoryOrderListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { pageNum = 1, pageSize = 10, ...filters } = params
        let filteredData = [...mockOrderList]

        // 模拟筛选逻辑
        if (filters.dealerName) {
          filteredData = filteredData.filter(item =>
            item.dealerName.includes(filters.dealerName!)
          )
        }
        if (filters.model) {
          filteredData = filteredData.filter(item =>
            item.model.includes(filters.model!)
          )
        }
        if (filters.variant) {
          filteredData = filteredData.filter(item =>
            item.variant.includes(filters.variant!)
          )
        }
        if (filters.orderStatus) {
          filteredData = filteredData.filter(item =>
            item.orderStatus === filters.orderStatus
          )
        }
        if (filters.paymentStatus) {
          filteredData = filteredData.filter(item =>
            item.paymentStatus === filters.paymentStatus
          )
        }
        if (filters.orderNumber) {
          filteredData = filteredData.filter(item =>
            item.orderNumber.includes(filters.orderNumber!)
          )
        }
        if (filters.orderDateStart && filters.orderDateEnd) {
          filteredData = filteredData.filter(item => {
            const itemDate = new Date(item.creationTime)
            const startDate = new Date(filters.orderDateStart!)
            const endDate = new Date(filters.orderDateEnd!)
            return itemDate >= startDate && itemDate <= endDate
          })
        }

        const total = filteredData.length
        const startIndex = (pageNum - 1) * pageSize
        const endIndex = startIndex + pageSize
        const list = filteredData.slice(startIndex, endIndex)

        resolve({
          list,
          total,
          page: pageNum,
          pageSize
        })
      }, 1000)
    })
  } else {
    // 过滤空参数
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([, value]) => value !== '' && value !== null && value !== undefined)
    )
    return request.post<any, PaginationResponse<FactoryOrderListItem>>('/factory/orders/list', Object.keys(filteredParams).length > 0 ? filteredParams : {})
  }
}

// 获取厂端订单详情
export const getFactoryOrderDetail = (orderNumber: string): Promise<FactoryOrderDetail> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const detail = mockOrderDetail.orderNumber === orderNumber ? mockOrderDetail : null
        if (detail) {
          resolve(detail)
        } else {
          reject(new Error('订单不存在'))
        }
      }, 800)
    })
  } else {
    return request.get<any, { result: FactoryOrderDetail }>(`/factory/orders/${orderNumber}`).then(response => response.result)
  }
}

// 导出厂端订单数据
export const exportFactoryOrderData = (params: FactoryOrderExportRequest): Promise<{ downloadUrl: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟导出成功，返回下载链接
        const timestamp = new Date().getTime()
        resolve({
          downloadUrl: `/api/downloads/factory-orders-${timestamp}.xlsx`
        })
      }, 2000)
    })
  } else {
    return request.post<any, { downloadUrl: string }>('/factory-order/export', params)
  }
}

// 刷新统计数据
export const refreshStatistics = (): Promise<OrderStatistics> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟数据更新
        const updatedStats = {
          ...mockOrderStatistics,
          lastUpdateTime: new Date().toISOString()
        }
        resolve(updatedStats)
      }, 1500)
    })
  } else {
    return request.post<any, OrderStatistics>('/factory-order/refresh-statistics')
  }
}
