import request from '../index'
import type { PaginationResponse } from '@/types/module'

export interface InventoryListItem {
  id: number
  partName: string
  partNumber: string
  supplierName: string
  totalInventory: number
  availableInventory: number
  lockedInventory: number
  defectiveCount: number
  pendingCount: number
  safetyStock: number
  inventoryStatus: 'normal' | 'belowSafety'
}

export interface InventoryListParams {
  partName?: string
  partNumber?: string
  supplierName?: string
  inventoryStatus?: string
  page: number
  pageSize: number
}

export interface InventoryUpdateItem {
  partName: string
  partNumber: string
  supplierName: string
  receiptQuantity: number
  damagedQuantity?: number
  receiptTime: string
  receiptOrderNumber: string
}

// 获取库存列表
export const getInventoryList = (params: InventoryListParams): Promise<PaginationResponse<InventoryListItem>> => {
  return request.get<any, PaginationResponse<InventoryListItem>>('/inventory/list', { params })
}

// 更新库存（收货时调用）
export const updateInventoryFromReceipt = (items: InventoryUpdateItem[]): Promise<boolean> => {
  return request.post<any, boolean>('/inventory/update-from-receipt', items)
}
