import request from '@/api';
import type { PartArchiveSearchParams, PartArchivePageResponse } from '@/types/parts/archives';
import { getPartArchiveList } from '@/mock/data/parts/archives';
import {MockConfig } from '@/utils/mock-config';

// 初始化时打印Mock状态
MockConfig.logStatus('Part Archives Module');
const USE_MOCK_API = true;
/**
 * 获取零件档案列表
 * @param params 搜索参数
 */
export const getPartArchives = (params: PartArchiveSearchParams): Promise<PartArchivePageResponse> => {
  if (USE_MOCK_API) {
    return getPartArchiveList(params);
  } else {
    return request.get<any, PartArchivePageResponse>('/parts/archives/list', { params });
  }
};