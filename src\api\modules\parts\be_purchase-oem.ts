
// 基础API
import request from '@/api';
// 类型定义
import type {
  OemPurchaseOrder,
  OemOrderDetail,
  OemDashboardStats,
  ApprovalRequest,
  ShipmentRequest,
  BatchApprovalRequest,
  BatchShipmentRequest,
  CarrierInfo,
  DealerInfo,
  InventoryCheckResult,
  OrderStatistics,
  PopularPart,
  DealerAnalysis,
  OemOrderListQuery
} from '@/types/parts/purchase-oem';
import type { PaginationResult, ApiResult } from '@/types/common';
// Mock数据
import { mockOemPurchaseData } from '@/mock/data/parts/purchase-oem';
// 工具配置

const USE_MOCK_API = true;
/**
 * 获取主机厂端采购审批仪表盘统计数据
 * @returns {Promise<OemDashboardStats>} 仪表盘统计数据
 */
export const getDashboardStats = async (): Promise<OemDashboardStats> => {
  if (USE_MOCK_API) {
    return Promise.resolve(mockOemPurchaseData.dashboardStats);
  }
  
  const response = await request.get<ApiResult<OemDashboardStats>>('/api/purchase-oem/dashboard');
  return response.result;
};

/**
 * 获取主机厂端待处理订单列表
 * @param {OemOrderListQuery} params 查询参数
 * @returns {Promise<PaginationResult<OemPurchaseOrder>>} 分页订单列表
 */
export const getOemOrderList = async (params: OemOrderListQuery): Promise<PaginationResult<OemPurchaseOrder>> => {
  if (USE_MOCK_API) {
    const { page = 1, size = 10, status, dealerName, orderNo } = params;
    let filteredOrders = mockOemPurchaseData.orders;
    
    if (status) {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }
    if (dealerName) {
      filteredOrders = filteredOrders.filter(order => order.dealerName.includes(dealerName));
    }
    if (orderNo) {
      filteredOrders = filteredOrders.filter(order => order.orderNo.includes(orderNo));
    }
    
    const start = (page - 1) * size;
    const end = start + size;
    
    return Promise.resolve({
      list: filteredOrders.slice(start, end),
      total: filteredOrders.length,
      page,
      size
    });
  }
  
  const response = await request.get<ApiResult<PaginationResult<OemPurchaseOrder>>>('/api/purchase-oem/orders', { params });
  return response.result;
};

/**
 * 获取订单详情含库存信息
 * @param {string} id 订单ID
 * @returns {Promise<OemOrderDetail>} 订单详情
 */
export const getOemOrderDetail = async (id: string): Promise<OemOrderDetail> => {
  if (USE_MOCK_API) {
    const order = mockOemPurchaseData.orders.find(o => o.id === id);
    if (!order) {
      throw new Error('Order not found');
    }
    
    // 构造详情数据
    const detail: OemOrderDetail = {
      order,
      approvalHistory: [
        {
          id: '1',
          orderId: id,
          action: 'created',
          operator: '系统',
          operatedAt: order.createTime,
          remark: '订单创建'
        }
      ]
    };
    
    return Promise.resolve(detail);
  }
  
  const response = await request.get<ApiResult<OemOrderDetail>>(`/api/purchase-oem/orders/${id}`);
  return response.result;
};

/**
 * 审核订单通过或拒绝
 * @param {ApprovalRequest} data 审批请求数据
 * @returns {Promise<void>} 审批结果
 */
export const approveOemOrder = async (data: ApprovalRequest): Promise<void> => {
  if (USE_MOCK_API) {
    return Promise.resolve();
  }
  
  const response = await request.post<ApiResult<void>>('/api/purchase-oem/approve', data);
  return response.result;
};

/**
 * 执行发货操作
 * @param {ShipmentRequest} data 发货请求数据
 * @returns {Promise<{ shipmentId: string; shipmentNo: string }>} 发货结果
 */
export const shipOemOrder = async (data: ShipmentRequest): Promise<{ shipmentId: string; shipmentNo: string }> => {
  if (USE_MOCK_API) {
    return Promise.resolve({
      shipmentId: Date.now().toString(),
      shipmentNo: `SH${Date.now()}`
    });
  }
  
  const response = await request.post<ApiResult<{ shipmentId: string; shipmentNo: string }>>('/api/purchase-oem/ship', data);
  return response.result;
};

/**
 * 批量审核订单
 * @param {BatchApprovalRequest} data 批量审批请求数据
 * @returns {Promise<{ successCount: number; failureCount: number; failures: Array<{ orderId: string; reason: string }> }>} 批量审批结果
 */
export const batchApproveOrders = async (data: BatchApprovalRequest): Promise<{
  successCount: number;
  failureCount: number;
  failures: Array<{ orderId: string; reason: string }>;
}> => {
  if (USE_MOCK_API) {
    return Promise.resolve({
      successCount: data.orderIds.length - 1,
      failureCount: 1,
      failures: [
        {
          orderId: data.orderIds[0],
          reason: '库存不足'
        }
      ]
    });
  }
  
  const response = await request.post<ApiResult<{
    successCount: number;
    failureCount: number;
    failures: Array<{ orderId: string; reason: string }>;
  }>>('/api/purchase-oem/batch-approve', data);
  return response.result;
};

/**
 * 批量发货操作
 * @param {BatchShipmentRequest} data 批量发货请求数据
 * @returns {Promise<{ successCount: number; failureCount: number; failures: Array<{ orderId: string; reason: string }> }>} 批量发货结果
 */
export const batchShipOrders = async (data: BatchShipmentRequest): Promise<{
  successCount: number;
  failureCount: number;
  failures: Array<{ orderId: string; reason: string }>;
}> => {
  if (USE_MOCK_API) {
    return Promise.resolve({
      successCount: data.orderIds.length,
      failureCount: 0,
      failures: []
    });
  }
  
  const response = await request.post<ApiResult<{
    successCount: number;
    failureCount: number;
    failures: Array<{ orderId: string; reason: string }>;
  }>>('/api/purchase-oem/batch-ship', data);
  return response.result;
};

/**
 * 检查配件库存状态
 * @param {string[]} partIds 配件ID列表
 * @returns {Promise<InventoryCheckResult[]>} 库存检查结果
 */
export const checkInventory = async (partIds: string[]): Promise<InventoryCheckResult[]> => {
  if (USE_MOCK_API) {
    return Promise.resolve(
      partIds.map(partId => ({
        partId,
        partCode: `PC${partId}`,
        partName: `配件${partId}`,
        factoryStock: Math.floor(Math.random() * 1000),
        availableStock: Math.floor(Math.random() * 800),
        stockStatus: Math.random() > 0.3 ? 'sufficient' : 'insufficient'
      }))
    );
  }
  
  const response = await request.post<ApiResult<InventoryCheckResult[]>>('/api/purchase-oem/check-inventory', { partIds });
  return response.result;
};

/**
 * 获取承运商列表
 * @returns {Promise<CarrierInfo[]>} 承运商列表
 */
export const getCarriers = async (): Promise<CarrierInfo[]> => {
  if (USE_MOCK_API) {
    return Promise.resolve([
      { id: '1', name: '顺丰速递', code: 'SF', contactPhone: '************' },
      { id: '2', name: '中通快递', code: 'ZTO', contactPhone: '************' },
      { id: '3', name: '圆通速递', code: 'YTO', contactPhone: '************' }
    ]);
  }
  
  const response = await request.get<ApiResult<CarrierInfo[]>>('/api/purchase-oem/carriers');
  return response.result;
};

/**
 * 获取经销商列表
 * @param {object} params 查询参数
 * @param {string} params.query 查询关键词
 * @param {string} params.regionCode 区域代码
 * @param {string} params.status 状态
 * @returns {Promise<DealerInfo[]>} 经销商列表
 */
export const getDealers = async (params?: {
  query?: string;
  regionCode?: string;
  status?: 'ACTIVE' | 'INACTIVE';
}): Promise<DealerInfo[]> => {
  if (USE_MOCK_API) {
    return Promise.resolve(mockOemPurchaseData.dealers);
  }
  
  const response = await request.get<ApiResult<DealerInfo[]>>('/api/purchase-oem/dealers', { params });
  return response.result;
};

/**
 * 获取订单统计数据
 * @param {object} params 统计参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.dealerId 经销商ID
 * @param {string} params.groupBy 分组方式
 * @returns {Promise<OrderStatistics>} 订单统计数据
 */
export const getOrderStatistics = async (params: {
  startDate: string;
  endDate: string;
  dealerId?: string;
  groupBy: 'day' | 'week' | 'month';
}): Promise<OrderStatistics> => {
  if (USE_MOCK_API) {
    return Promise.resolve(mockOemPurchaseData.statistics);
  }
  
  const response = await request.get<ApiResult<OrderStatistics>>('/api/purchase-oem/statistics', { params });
  return response.result;
};

/**
 * 获取热门配件分析
 * @param {object} params 分析参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {number} params.limit 限制数量
 * @returns {Promise<PopularPart[]>} 热门配件列表
 */
export const getPopularParts = async (params: {
  startDate: string;
  endDate: string;
  limit?: number;
}): Promise<PopularPart[]> => {
  if (USE_MOCK_API) {
    return Promise.resolve(mockOemPurchaseData.hotParts);
  }
  
  const response = await request.get<ApiResult<PopularPart[]>>('/api/purchase-oem/popular-parts', { params });
  return response.result;
};

/**
 * 获取经销商采购分析
 * @param {object} params 分析参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {number} params.limit 限制数量
 * @returns {Promise<DealerAnalysis[]>} 经销商分析数据
 */
export const getDealerAnalysis = async (params: {
  startDate: string;
  endDate: string;
  limit?: number;
}): Promise<DealerAnalysis[]> => {
  if (USE_MOCK_API) {
    return Promise.resolve([
      {
        dealerId: '1',
        dealerName: '北京经销商',
        totalOrders: 120,
        totalAmount: 1500000,
        averageAmount: 12500,
        approvalRate: 0.95
      },
      {
        dealerId: '2',
        dealerName: '上海经销商',
        totalOrders: 98,
        totalAmount: 1280000,
        averageAmount: 13061,
        approvalRate: 0.92
      }
    ]);
  }
  
  const response = await request.get<ApiResult<DealerAnalysis[]>>('/api/purchase-oem/dealer-analysis', { params });
  return response.result;
};

/**
 * 导出订单数据
 * @param {OemOrderListQuery} params 查询参数
 * @returns {Promise<Blob>} 导出文件数据
 */
export const exportOemOrders = async (params: OemOrderListQuery): Promise<Blob> => {
  if (USE_MOCK_API) {
    const data = 'Order No,Dealer,Status,Amount\nPO001,北京经销商,Pending,50000\nPO002,上海经销商,Approved,75000';
    return Promise.resolve(new Blob([data], { type: 'text/csv' }));
  }
  
  const response = await request.get<Blob>('/api/purchase-oem/export', { 
    params,
    responseType: 'blob'
  });
  return response.data;
};

/**
 * 导出统计报表
 * @param {object} params 导出参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.type 报表类型
 * @param {string} params.format 导出格式
 * @returns {Promise<Blob>} 导出文件数据
 */
export const exportStatistics = async (params: {
  startDate: string;
  endDate: string;
  type: 'orders' | 'parts' | 'dealers';
  format: 'excel' | 'pdf';
}): Promise<Blob> => {
  if (USE_MOCK_API) {
    const data = 'Statistics Report\nType,Count,Amount\nOrders,150,2000000\nParts,500,1800000';
    return Promise.resolve(new Blob([data], { type: 'text/csv' }));
  }
  
  const response = await request.get<Blob>('/api/purchase-oem/export-statistics', { 
    params,
    responseType: 'blob'
  });
  return response.data;
};

/**
 * 获取物流跟踪信息
 * @param {string} trackingNumber 运单号
 * @returns {Promise<object>} 物流跟踪信息
 */
export const getTrackingInfo = async (trackingNumber: string): Promise<{
  trackingNumber: string;
  carrier: string;
  status: string;
  currentLocation: string;
  estimatedDelivery: string;
  trackingHistory: Array<{
    timestamp: string;
    location: string;
    status: string;
    description: string;
  }>;
}> => {
  if (USE_MOCK_API) {
    return Promise.resolve({
      trackingNumber,
      carrier: '顺丰速递',
      status: '运输中',
      currentLocation: '北京分拣中心',
      estimatedDelivery: '2024-01-15 18:00:00',
      trackingHistory: [
        {
          timestamp: '2024-01-13 10:00:00',
          location: '上海发货仓',
          status: '已发货',
          description: '商品已从发货仓发出'
        },
        {
          timestamp: '2024-01-14 08:30:00',
          location: '北京分拣中心',
          status: '运输中',
          description: '商品正在分拣中心处理'
        }
      ]
    });
  }
  
  const response = await request.get<ApiResult<{
    trackingNumber: string;
    carrier: string;
    status: string;
    currentLocation: string;
    estimatedDelivery: string;
    trackingHistory: Array<{
      timestamp: string;
      location: string;
      status: string;
      description: string;
    }>;
  }>>(`/api/purchase-oem/tracking/${trackingNumber}`);
  return response.result;
};

/**
 * 更新订单优先级
 * @param {object} params 更新参数
 * @param {string} params.orderId 订单ID
 * @param {string} params.priority 优先级
 * @param {string} params.reason 更新原因
 * @returns {Promise<void>} 更新结果
 */
export const updateOrderPriority = async (params: {
  orderId: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  reason?: string;
}): Promise<void> => {
  if (USE_MOCK_API) {
    return Promise.resolve();
  }
  
  const response = await request.post<ApiResult<void>>('/api/purchase-oem/update-priority', params);
  return response.result;
};

/**
 * 添加订单备注
 * @param {object} params 备注参数
 * @param {string} params.orderId 订单ID
 * @param {string} params.note 备注内容
 * @param {boolean} params.isInternal 是否内部备注
 * @returns {Promise<{ noteId: string; createdAt: string }>} 备注添加结果
 */
export const addOrderNote = async (params: {
  orderId: string;
  note: string;
  isInternal: boolean;
}): Promise<{ noteId: string; createdAt: string }> => {
  if (USE_MOCK_API) {
    return Promise.resolve({
      noteId: Date.now().toString(),
      createdAt: new Date().toISOString()
    });
  }
  
  const response = await request.post<ApiResult<{ noteId: string; createdAt: string }>>('/api/purchase-oem/add-note', params);
  return response.result;
};

/**
 * 获取订单备注列表
 * @param {string} orderId 订单ID
 * @returns {Promise<Array<object>>} 备注列表
 */
export const getOrderNotes = async (orderId: string): Promise<Array<{
  noteId: string;
  orderId: string;
  content: string;
  isInternal: boolean;
  createdBy: string;
  createdAt: string;
}>> => {
  if (USE_MOCK_API) {
    return Promise.resolve([
      {
        noteId: '1',
        orderId,
        content: '客户要求加急处理',
        isInternal: false,
        createdBy: '张三',
        createdAt: '2024-01-13 10:00:00'
      },
      {
        noteId: '2',
        orderId,
        content: '库存检查已完成，可以发货',
        isInternal: true,
        createdBy: '李四',
        createdAt: '2024-01-13 14:30:00'
      }
    ]);
  }
  
  const response = await request.get<ApiResult<Array<{
    noteId: string;
    orderId: string;
    content: string;
    isInternal: boolean;
    createdBy: string;
    createdAt: string;
  }>>>(`/api/purchase-oem/orders/${orderId}/notes`);
  return response.result;
};