
import request from '@/api';
import type { ApiResult } from '@/types/common/api';
import type {
  InventoryDashboard,
  InventorySearchParams,
  InventoryListResponse,
  InventoryDetail,
  InventoryTrend,
  InventoryAdjustParams,
  InventoryAdjustResult,
  BatchReplenishmentParams,
  BatchReplenishmentResult,
} from '@/types/parts/inventory';

import {
  getMockInventoryDashboard,
  getMockInventoryList,
  getMockInventoryDetail,
  getMockInventoryTrend,
  mockAdjustInventory,
  mockBatchCreateReplenishment,
} from '@/mock/data/parts/inventory';


const USE_MOCK_API = true;
/**
 * 获取库存概览统计
 * @param params - 请求参数
 */
export const getInventoryDashboard = async (params: { storeId: number }): Promise<InventoryDashboard> => {
  if (USE_MOCK_API) {
    return getMockInventoryDashboard(params);
  }
  const response = await request.post<any, ApiResult<InventoryDashboard>>('/api/inventory/dashboard', params);
  return response.result;
};

/**
 * 获取库存列表
 * @param params - 查询参数
 */
export const getInventoryList = async (params: InventorySearchParams): Promise<InventoryListResponse> => {
  if (USE_MOCK_API) {
    return getMockInventoryList(params);
  }
  const response = await request.post<any, ApiResult<InventoryListResponse>>('/api/inventory/list', params);
  return response.result;
};

/**
 * 获取库存详情
 * @param params - 请求参数
 */
export const getInventoryDetail = async (params: { inventoryId: number }): Promise<InventoryDetail> => {
  if (USE_MOCK_API) {
    return getMockInventoryDetail(params);
  }
  const response = await request.post<any, ApiResult<InventoryDetail>>('/api/inventory/detail', params);
  return response.result;
};

/**
 * 获取库存趋势数据
 * @param params - 请求参数
 */
export const getInventoryTrend = async (params: { inventoryId: number, days?: number }): Promise<InventoryTrend> => {
  if (USE_MOCK_API) {
    return getMockInventoryTrend(params);
  }
  const response = await request.post<any, ApiResult<InventoryTrend>>('/api/inventory/trend', params);
  return response.result;
};

/**
 * 库存调整
 * @param params - 调整参数
 */
export const adjustInventory = async (params: InventoryAdjustParams): Promise<InventoryAdjustResult> => {
  if (USE_MOCK_API) {
    return mockAdjustInventory(params);
  }
  const response = await request.post<any, ApiResult<InventoryAdjustResult>>('/api/inventory/adjust', params);
  return response.result;
};

/**
 * 批量补货申请
 * @param params - 请求参数
 */
export const batchCreateReplenishment = async (params: BatchReplenishmentParams): Promise<BatchReplenishmentResult> => {
  if (USE_MOCK_API) {
    return mockBatchCreateReplenishment(params);
  }
  const response = await request.post<any, ApiResult<BatchReplenishmentResult>>('/api/replenishment/batch-create', params);
  return response.result;
};
