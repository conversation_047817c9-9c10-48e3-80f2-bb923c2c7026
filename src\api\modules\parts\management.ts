import request from '@/api';
import type { 
  UnifiedSearchParams, 
  PartManagementPageResponse,
  WorkOrderDetail,
  MaterialOrderItem,
  ReturnPickingItem,
  UnifiedTableItem,
  RequisitionFormData,
  ScrapRecordFormData,
  MaterialOrderFilters
} from '@/types/parts/management';
import { 
  getPartManagementData,
  getWorkOrderList,
  getRequisitionList,
  getScrapRecordsList,
  getMaterialOrderList,
  generateWorkOrderDetail,
  getFilteredMaterialOrderList
} from '@/mock/data/parts/management';
import {MockConfig } from '@/utils/mock-config';
const USE_MOCK_API = true;
// 初始化时打印Mock状态
MockConfig.logStatus('Parts Management Module');

// ===== 核心数据获取API =====

/**
 * 统一数据获取API
 * 根据单据类型路由到不同的处理逻辑
 * @param params 统一搜索参数
 */
export const getPartManagementDataAPI = (params: UnifiedSearchParams): Promise<PartManagementPageResponse> => {
  return MockConfig.execute(
    () => getPartManagementData(params),
    () => request.get<any, PartManagementPageResponse>('/parts/management/list', { params }),
    'getPartManagementData'
  );
};

/**
 * 获取叫料单数据
 * @param params 搜索参数
 */
export const getRequisitionListAPI = (params: UnifiedSearchParams): Promise<PartManagementPageResponse> => {
  return MockConfig.execute(
    () => getRequisitionList(params),
    () => request.get<any, PartManagementPageResponse>('/parts/requisitions', { params }),
    'getRequisitionList'
  );
};

/**
 * 获取报损记录数据
 * @param params 搜索参数
 */
export const getScrapRecordsListAPI = (params: UnifiedSearchParams): Promise<PartManagementPageResponse> => {
  return MockConfig.execute(
    () => getScrapRecordsList(params),
    () => request.get<any, PartManagementPageResponse>('/parts/scrap-records', { params }),
    'getScrapRecordsList'
  );
};

/**
 * 获取工单数据
 * @param params 搜索参数
 */
export const getWorkOrderListAPI = (params: UnifiedSearchParams): Promise<PartManagementPageResponse> => {
  return MockConfig.execute(
    () => getWorkOrderList(params),
    () => request.get<any, PartManagementPageResponse>('/parts/work-orders', { params }),
    'getWorkOrderList'
  );
};

// ===== 工单管理API =====

/**
 * 获取工单详情
 * @param workOrderNumber 工单号
 */
export const getWorkOrderDetail = (workOrderNumber: string): Promise<WorkOrderDetail> => {
  return MockConfig.execute(
    () => Promise.resolve(generateWorkOrderDetail({ workOrderNumber })),
    () => request.get<any, WorkOrderDetail>(`/parts/work-orders/${workOrderNumber}`),
    'getWorkOrderDetail'
  );
};

/**
 * 获取物料单列表
 * @param filters 筛选参数（可选）
 */
export const getMaterialOrders = (filters?: MaterialOrderFilters): Promise<{ data: MaterialOrderItem[], total: number }> => {
  return MockConfig.execute(
    () => Promise.resolve(filters ? getFilteredMaterialOrderList(filters) : { data: getMaterialOrderList(), total: getMaterialOrderList().length }),
    () => request.get<any, { data: MaterialOrderItem[], total: number }>('/parts/material-orders', { params: filters }),
    'getMaterialOrders'
  );
};

/**
 * 完成拣货操作
 * @param workOrderNumber 工单号
 */
export const completePicking = (workOrderNumber: string): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 完成拣货 ${workOrderNumber}`);
      return Promise.resolve();
    },
    () => request.post(`/parts/work-orders/${workOrderNumber}/complete`),
    'completePicking'
  );
};

/**
 * 退拣操作
 * @param workOrderNumber 工单号
 * @param returnItems 退拣项目列表
 */
export const updateReturnPicking = (
  workOrderNumber: string, 
  returnItems: ReturnPickingItem[]
): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 退拣工单 ${workOrderNumber}`, returnItems);
      return Promise.resolve();
    },
    () => request.post(`/parts/work-orders/${workOrderNumber}/return`, { returnItems }),
    'updateReturnPicking'
  );
};

/**
 * 完工操作
 * @param workOrderNumber 工单号
 */
export const completeWork = (workOrderNumber: string): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 完工 ${workOrderNumber}`);
      return Promise.resolve();
    },
    () => request.post(`/parts/work-orders/${workOrderNumber}/finish`),
    'completeWork'
  );
};

/**
 * 打印物料单
 * @param workOrderNumber 工单号
 */
export const printMaterialOrder = (workOrderNumber: string): Promise<Blob> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 打印物料单 ${workOrderNumber}`);
      // 创建一个模拟的PDF Blob
      const mockPdf = new Blob(['Mock PDF content'], { type: 'application/pdf' });
      return Promise.resolve(mockPdf);
    },
    () => request.get(`/parts/work-orders/${workOrderNumber}/print`, { responseType: 'blob' }),
    'printMaterialOrder'
  );
};

// ===== 叫料单管理API =====

/**
 * 创建叫料单
 * @param requisitionData 叫料单数据
 */
export const createRequisition = (requisitionData: RequisitionFormData): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log('Mock: 创建叫料单', requisitionData);
      return Promise.resolve();
    },
    () => request.post('/parts/requisitions', requisitionData),
    'createRequisition'
  );
};

/**
 * 更新叫料单
 * @param requisitionNumber 叫料单号
 * @param requisitionData 叫料单数据
 */
export const updateRequisition = (requisitionNumber: string, requisitionData: RequisitionFormData): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 更新叫料单 ${requisitionNumber}`, requisitionData);
      return Promise.resolve();
    },
    () => request.put(`/parts/requisitions/${requisitionNumber}`, requisitionData),
    'updateRequisition'
  );
};

/**
 * 作废叫料单
 * @param requisitionNumber 叫料单号
 */
export const voidRequisition = (requisitionNumber: string): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 作废叫料单 ${requisitionNumber}`);
      return Promise.resolve();
    },
    () => request.post(`/parts/requisitions/${requisitionNumber}/void`),
    'voidRequisition'
  );
};

/**
 * 批量作废叫料单
 * @param requisitionNumbers 叫料单号列表
 */
export const batchVoidRequisitions = (requisitionNumbers: string[]): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 批量作废叫料单`, requisitionNumbers);
      return Promise.resolve();
    },
    () => request.post('/parts/requisitions/batch-void', { requisitionNumbers }),
    'batchVoidRequisitions'
  );
};

/**
 * 获取叫料单详情
 * @param requisitionNumber 叫料单号
 */
export const getRequisitionDetail = (requisitionNumber: string): Promise<any> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 获取叫料单详情 ${requisitionNumber}`);
      return Promise.resolve({
        requisitionNumber,
        status: 'approved',
        items: []
      });
    },
    () => request.get(`/parts/requisitions/${requisitionNumber}`),
    'getRequisitionDetail'
  );
};

// ===== 报损管理API =====

/**
 * 创建报损单
 * @param scrapData 报损数据
 */
export const createScrapRecord = (scrapData: ScrapRecordFormData): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log('Mock: 创建报损单', scrapData);
      return Promise.resolve();
    },
    () => request.post('/parts/scrap-records', scrapData),
    'createScrapRecord'
  );
};

/**
 * 更新报损单
 * @param scrapRecordId 报损单ID
 * @param scrapData 报损数据
 */
export const updateScrapRecord = (scrapRecordId: string, scrapData: ScrapRecordFormData): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 更新报损单 ${scrapRecordId}`, scrapData);
      return Promise.resolve();
    },
    () => request.put(`/parts/scrap-records/${scrapRecordId}`, scrapData),
    'updateScrapRecord'
  );
};

/**
 * 作废报损单
 * @param scrapRecordId 报损单ID
 */
export const voidScrapRecord = (scrapRecordId: string): Promise<void> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 作废报损单 ${scrapRecordId}`);
      return Promise.resolve();
    },
    () => request.post(`/parts/scrap-records/${scrapRecordId}/void`),
    'voidScrapRecord'
  );
};

// ===== 导出功能API =====

/**
 * 导出选中数据
 * @param exportData 导出数据
 * @param exportType 导出类型
 */
export const exportData = (exportData: UnifiedTableItem[], exportType: 'excel' | 'pdf' = 'excel'): Promise<Blob> => {
  return MockConfig.execute(
    () => {
      console.log(`Mock: 导出数据 (${exportType})`, exportData);
      // 创建一个模拟的Excel/PDF Blob
      const mimeType = exportType === 'excel' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'application/pdf';
      const mockFile = new Blob([`Mock ${exportType} content`], { type: mimeType });
      return Promise.resolve(mockFile);
    },
    () => request.post('/parts/export', { data: exportData, type: exportType }, { responseType: 'blob' }),
    'exportData'
  );
};

// ===== 统计信息API =====

/**
 * 获取零件管理统计信息
 */
export const getManagementStatistics = (): Promise<{
  totalRequisitions: number;
  pendingRequisitions: number;
  totalScrapRecords: number;
  totalWorkOrders: number;
  pendingWorkOrders: number;
}> => {
  return MockConfig.execute(
    () => Promise.resolve({
      totalRequisitions: 156,
      pendingRequisitions: 23,
      totalScrapRecords: 45,
      totalWorkOrders: 89,
      pendingWorkOrders: 12
    }),
    () => request.get('/parts/management/statistics'),
    'getManagementStatistics'
  );
};

// ===== 错误处理和重试机制 =====

/**
 * 带重试机制的API调用
 * @param apiCall API调用函数
 * @param maxRetries 最大重试次数
 * @param retryDelay 重试延迟(ms)
 */
export const apiCallWithRetry = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt < maxRetries) {
        console.warn(`API调用失败，第${attempt}次重试中...`, error);
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }
  }
  
  console.error(`API调用失败，已重试${maxRetries}次`, lastError);
  throw lastError;
};

// 导出主要API函数的重试版本
export const getPartManagementDataWithRetry = (params: UnifiedSearchParams) => 
  apiCallWithRetry(() => getPartManagementDataAPI(params));

export const getWorkOrderDetailWithRetry = (workOrderNumber: string) => 
  apiCallWithRetry(() => getWorkOrderDetail(workOrderNumber));

export const getMaterialOrdersWithRetry = (filters?: MaterialOrderFilters) => 
  apiCallWithRetry(() => getMaterialOrders(filters));