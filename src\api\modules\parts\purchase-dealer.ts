import request from '@/api';
import {
    getMockCancelPurchaseOrder,
    getMockConfirmReceipt,
    getMockCreatePurchaseOrder,
    getMockDealerOrderDetail,
    getMockDealerOrderList,
    getMockDeletePurchaseOrder,
    getMockPartsForSelection,
    getMockSubmitPurchaseOrder,
    getMockUpdatePurchaseOrder,
    mockDealerDashboard
} from '@/mock/data/parts/purchase-dealer';
import type { ApiResult } from '@/types/common/api';
import type {
    CreatePurchaseOrderRequest,
    DealerDashboardStats,
    PartForSelection,
    PurchaseOrder,
    PurchaseOrderListQuery,
    ReceiptRequest,
    ShipmentOrder,
    SubmitOrderResponse,
    UpdatePurchaseOrderRequest
} from '@/types/parts/purchase-dealer';
//import { USE_MOCK_API } from '@/utils/mock-config'
const USE_MOCK_API = true;

/**
 * 经销商端采购管理API
 */
export const purchaseDealerApi = {
  /**
   * 获取采购订单列表
   * @param params 查询参数
   * @returns 订单列表和总数
   */
  async getOrderList(params: PurchaseOrderListQuery): Promise<{
    list: PurchaseOrder[]
    total: number
    page: number
    size: number
  }> {
    if (USE_MOCK_API) {
      return getMockDealerOrderList(params)
    }
    
    const response = await request.get<any, ApiResult<{
      list: PurchaseOrder[]
      total: number
      page: number
      size: number
    }>>('/api/parts/purchase/dealer/orders', { params })
    return response.result
  },

  /**
   * 获取采购订单详情
   * @param id 订单ID
   * @returns 订单详情
   */
  async getOrderDetail(id: string): Promise<PurchaseOrder> {
    if (USE_MOCK_API) {
      return getMockDealerOrderDetail(id)
    }
    
    const response = await request.get<any, ApiResult<PurchaseOrder>>(`/api/parts/purchase/dealer/orders/${id}`)
    return response.result
  },

  /**
   * 创建采购订单
   * @param data 订单数据
   * @returns 创建的订单
   */
  async createOrder(data: CreatePurchaseOrderRequest): Promise<PurchaseOrder> {
    if (USE_MOCK_API) {
      return getMockCreatePurchaseOrder(data)
    }
    
    const response = await request.post<any, ApiResult<PurchaseOrder>>('/api/parts/purchase/dealer/orders', data)
    return response.result
  },

  /**
   * 更新采购订单
   * @param id 订单ID
   * @param data 更新数据
   * @returns 更新后的订单
   */
  async updateOrder(id: string, data: UpdatePurchaseOrderRequest): Promise<PurchaseOrder> {
    if (USE_MOCK_API) {
      return getMockUpdatePurchaseOrder(id, data)
    }
    
    const response = await request.put<any, ApiResult<PurchaseOrder>>(`/api/parts/purchase/dealer/orders/${id}`, data)
    return response.result
  },

  /**
   * 删除采购订单
   * @param id 订单ID
   */
  async deleteOrder(id: string): Promise<void> {
    if (USE_MOCK_API) {
      await getMockDeletePurchaseOrder(id)
      return
    }
    
    await request.delete(`/api/parts/purchase/dealer/orders/${id}`)
  },

  /**
   * 提交采购订单审核
   * @param id 订单ID
   * @returns 提交结果
   */
  async submitForApproval(id: string): Promise<SubmitOrderResponse> {
    if (USE_MOCK_API) {
      return getMockSubmitPurchaseOrder(id)
    }
    
    const response = await request.post<any, ApiResult<SubmitOrderResponse>>(`/api/parts/purchase/dealer/orders/${id}/submit`)
    return response.result
  },

  /**
   * 取消采购订单
   * @param id 订单ID
   */
  async cancelOrder(id: string): Promise<PurchaseOrder> {
    if (USE_MOCK_API) {
      return getMockCancelPurchaseOrder(id)
    }
    
    const response = await request.post<any, ApiResult<PurchaseOrder>>(`/api/parts/purchase/dealer/orders/${id}/cancel`)
    return response.result
  },

  /**
   * 获取配件选择列表
   * @param params 查询参数
   * @returns 配件列表
   */
  async getPartsForSelection(params?: { keyword?: string; partType?: 'ORIGINAL' | 'NON_ORIGINAL'; }): Promise<PartForSelection[]> {
    if (USE_MOCK_API) {
      return getMockPartsForSelection(params)
    }
    
    const response = await request.get<any, ApiResult<PartForSelection[]>>('/api/parts/purchase/dealer/parts-selection', { params })
    return response.result
  },

  /**
   * 确认收货
   * @param id 订单ID
   * @param data 收货数据
   */
  async confirmReceipt(id: string, data: ReceiptRequest): Promise<PurchaseOrder> {
    if (USE_MOCK_API) {
      return getMockConfirmReceipt(id, data)
    }
    
    const response = await request.post<any, ApiResult<PurchaseOrder>>(`/api/parts/purchase/dealer/orders/${id}/receipt`, data)
    return response.result
  },

  /**
   * 获取发货单列表
   * @param orderId 订单ID
   * @returns 发货单列表
   */
  async getShipmentOrders(orderId: string): Promise<ShipmentOrder[]> {
    if (USE_MOCK_API) {
      // 从mockShipmentOrders中筛选相关数据
      const { mockShipmentOrders } = await import('@/mock/data/parts/purchase-dealer')
      return mockShipmentOrders.filter(shipment => 
        shipment.purchaseOrderId.toString() === orderId.replace('order_', '')
      )
    }
    
    const response = await request.get<any, ApiResult<ShipmentOrder[]>>(`/api/parts/purchase/dealer/orders/${orderId}/shipments`)
    return response.result
  },

  /**
   * 获取仪表盘统计数据
   * @returns 统计数据
   */
  async getDashboardStats(): Promise<DealerDashboardStats> {
    if (USE_MOCK_API) {
      return mockDealerDashboard
    }
    
    const response = await request.get<any, ApiResult<DealerDashboardStats>>('/api/parts/purchase/dealer/dashboard')
    return response.result
  },

  /**
   * 获取仓库选项
   * @returns 仓库列表
   */
  async getWarehouses(): Promise<Array<{ id: number; name: string; type: string }>> {
    if (USE_MOCK_API) {
      return [
        { id: 3001, name: '主仓库', type: 'MAIN' },
        { id: 3002, name: '配件仓库', type: 'PARTS' },
        { id: 3003, name: '临时仓库', type: 'TEMP' }
      ]
    }
    
    const response = await request.get<any, ApiResult<Array<{ id: number; name: string; type: string }>>>('/api/parts/purchase/dealer/warehouses')
    return response.result
  },

  /**
   * 获取仪表板统计数据
   * @returns 统计数据
   */
  async getStatistics(): Promise<DealerDashboardStats> {
    if (USE_MOCK_API) {
      return mockDealerDashboard
    }
    
    const response = await request.get<any, ApiResult<DealerDashboardStats>>('/api/parts/purchase/dealer/statistics')
    return response.result
  }
};