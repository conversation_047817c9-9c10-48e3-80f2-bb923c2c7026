import request from '@/api';

import {
  getMockApproveOrder,
  getMockBatchApprove,
  getMockBatchShip,
  getMockCarriers,
  getMockOemOrderDetail,
  getMockOemOrderList,
  getMockOemStatistics,
  getMockShipOrder
} from '@/mock/data/parts/purchase-oem';
import type { ApiResult } from '@/types/common/api';
import type {
  BatchApprovalParams,
  BatchShipmentParams,
  CarrierInfo,
  OemApprovalParams,
  OemDashboard,
  OemOrderDetail,
  OemOrderListParams,
  OemPurchaseOrder,
  OemShipmentParams,
  PaginationResponse
} from '@/types/parts/purchase-oem';
//import { USE_MOCK_API } from '@/utils/mock-config'
const USE_MOCK_API = true;
/**
 * 主机厂端采购管理API
 */
export const purchaseOemApi = {
  /**
   * 获取采购订单列表（所有经销商）
   */
  async getOrderList(params: OemOrderListParams): Promise<PaginationResponse<OemPurchaseOrder>> {
    if (USE_MOCK_API) {
      return getMockOemOrderList(params)
    }
    
    const response = await request.get<any, ApiResult<PaginationResponse<OemPurchaseOrder>>>('/api/parts/purchase/oem/orders', { params })
    return response.result
  },

  /**
   * 获取订单详情
   */
  async getOrderDetail(orderId: number): Promise<OemOrderDetail> {
    if (USE_MOCK_API) {
      return getMockOemOrderDetail(orderId)
    }
    
    const response = await request.get<any, ApiResult<OemOrderDetail>>(`/api/parts/purchase/oem/orders/${orderId}`)
    return response.result
  },

  /**
   * 审批订单
   */
  async approveOrder(data: OemApprovalParams): Promise<void> {
    if (USE_MOCK_API) {
      return getMockApproveOrder(data)
    }
    
    const response = await request.post<any, ApiResult<void>>(`/api/parts/purchase/oem/orders/${data.orderId}/approve`, data)
    return response.result
  },

  /**
   * 批量审批
   */
  async batchApprove(data: BatchApprovalParams): Promise<{
    successCount: number
    failCount: number
    failedOrders?: Array<{ orderId: number; reason: string }>
  }> {
    if (USE_MOCK_API) {
      return getMockBatchApprove(data)
    }
    
    const response = await request.post<any, ApiResult<{
      successCount: number
      failCount: number
      failedOrders?: Array<{ orderId: number; reason: string }>
    }>>('/api/parts/purchase/oem/orders/batch-approve', data)
    return response.result
  },

  /**
   * 发货处理
   */
  async shipOrder(data: OemShipmentParams): Promise<void> {
    if (USE_MOCK_API) {
      return getMockShipOrder(data)
    }
    
    const response = await request.post<any, ApiResult<void>>(`/api/parts/purchase/oem/orders/${data.orderId}/ship`, data)
    return response.result
  },

  /**
   * 批量发货
   */
  async batchShip(data: BatchShipmentParams): Promise<{
    successCount: number
    failCount: number
    failedOrders?: Array<{ orderId: number; reason: string }>
  }> {
    if (USE_MOCK_API) {
      return getMockBatchShip(data)
    }
    
    const response = await request.post<any, ApiResult<{
      successCount: number
      failCount: number
      failedOrders?: Array<{ orderId: number; reason: string }>
    }>>('/api/parts/purchase/oem/orders/batch-ship', data)
    return response.result
  },

  /**
   * 获取统计信息
   */
  async getStatistics(): Promise<OemDashboard> {
    if (USE_MOCK_API) {
      return getMockOemStatistics()
    }
    
    const response = await request.get<any, ApiResult<OemDashboard>>('/api/parts/purchase/oem/statistics')
    return response.result
  },

  /**
   * 获取承运商列表
   */
  async getCarriers(): Promise<CarrierInfo[]> {
    if (USE_MOCK_API) {
      return getMockCarriers()
    }
    
    const response = await request.get<any, ApiResult<CarrierInfo[]>>('/api/parts/purchase/oem/carriers')
    return response.result
  }
}