import request from '@/api';
import {
  getMockReceiptOverview,
  getMockReceiptOrderList,
  getMockReceiptOrderDetail,
  getMockConfirmReceipt,
  getMockAbnormalTypeOptions,
  getMockExportReceiptReport
} from '@/mock/data/parts/receipt';
import type { ApiResult } from '@/types/common/api';
import type {
  ReceiptStatistics,
  ReceiptOrder,
  ReceiptConfirmRequest,
  PurchaseOrderExtended
} from '@/types/parts/purchase-dealer';

const USE_MOCK_API = true;

/**
 * 收货管理API
 */
export const receiptApi = {
  /**
   * 获取收货概览统计
   * @param purchaseOrderId 采购订单ID
   * @returns 收货统计信息
   */
  async getReceiptOverview(purchaseOrderId: string): Promise<{
    orderInfo: PurchaseOrderExtended;
    statistics: ReceiptStatistics;
  }> {
    if (USE_MOCK_API) {
      return getMockReceiptOverview(purchaseOrderId);
    }
    
    const response = await request.get<any, ApiResult<{
      orderInfo: PurchaseOrderExtended;
      statistics: ReceiptStatistics;
    }>>(`/api/parts/receipt/overview/${purchaseOrderId}`);
    return response.result;
  },

  /**
   * 获取收货单列表
   * @param purchaseOrderId 采购订单ID
   * @param params 查询参数
   * @returns 收货单列表
   */
  async getReceiptOrderList(
    purchaseOrderId: string,
    params?: {
      status?: 'PENDING' | 'COMPLETED';
      page?: number;
      size?: number;
    }
  ): Promise<{
    list: ReceiptOrder[];
    total: number;
    page: number;
    size: number;
  }> {
    if (USE_MOCK_API) {
      return getMockReceiptOrderList(purchaseOrderId, params);
    }
    
    const response = await request.get<any, ApiResult<{
      list: ReceiptOrder[];
      total: number;
      page: number;
      size: number;
    }>>(`/api/parts/receipt/orders/${purchaseOrderId}`, { params });
    return response.result;
  },

  /**
   * 获取收货单详情
   * @param receiptOrderId 收货单ID
   * @returns 收货单详情
   */
  async getReceiptOrderDetail(receiptOrderId: string): Promise<ReceiptOrder> {
    if (USE_MOCK_API) {
      return getMockReceiptOrderDetail(receiptOrderId);
    }
    
    const response = await request.get<any, ApiResult<ReceiptOrder>>(`/api/parts/receipt/orders/detail/${receiptOrderId}`);
    return response.result;
  },

  /**
   * 确认收货
   * @param data 收货确认数据
   * @returns 更新后的收货单
   */
  async confirmReceipt(data: ReceiptConfirmRequest): Promise<ReceiptOrder> {
    if (USE_MOCK_API) {
      return getMockConfirmReceipt(data);
    }
    
    const response = await request.post<any, ApiResult<ReceiptOrder>>('/api/parts/receipt/confirm', data);
    return response.result;
  },

  /**
   * 获取异常类型选项
   * @returns 异常类型选项列表
   */
  async getAbnormalTypeOptions(): Promise<Array<{ code: string; name: string; }>> {
    if (USE_MOCK_API) {
      return getMockAbnormalTypeOptions();
    }
    
    const response = await request.get<any, ApiResult<Array<{ code: string; name: string; }>>>('/api/parts/receipt/abnormal-types');
    return response.result;
  },

  /**
   * 导出收货报表
   * @param purchaseOrderId 采购订单ID
   * @param params 导出参数
   * @returns 导出结果
   */
  async exportReceiptReport(
    purchaseOrderId: string,
    params?: {
      format?: 'excel' | 'pdf';
      dateRange?: [string, string];
    }
  ): Promise<{ downloadUrl: string; fileName: string; }> {
    if (USE_MOCK_API) {
      return getMockExportReceiptReport(purchaseOrderId, params);
    }
    
    const response = await request.post<any, ApiResult<{ downloadUrl: string; fileName: string; }>>(
      `/api/parts/receipt/export/${purchaseOrderId}`, 
      params
    );
    return response.result;
  }
};