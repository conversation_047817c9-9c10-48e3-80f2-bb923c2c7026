import request from '@/api/index'
import type {
  OrderPaymentItem,
  OrderPaymentListParams,
  PaginationResponse,
  OrderDetailInfo,
  AddPaymentRecordForm,
  PaymentRecord
} from '@/types/module'
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Payment Module')

// 获取订单收款列表
export const getOrderPaymentList = (params: OrderPaymentListParams): Promise<PaginationResponse<OrderPaymentItem>> => {
  if (USE_MOCK_API) {
    return import('@/mock/data/payment').then(module => module.getOrderPaymentList(params))
  } else {
    return request.post<any, PaginationResponse<OrderPaymentItem>>('/payment/order/list', params)
  }
}

// 获取订单详情
export const getOrderDetail = (orderId: string): Promise<OrderDetailInfo> => {
  if (USE_MOCK_API) {
    return import('@/mock/data/payment').then(module => module.getOrderDetail(orderId))
  } else {
    return request.post<any, OrderDetailInfo>('/payment/order/detail', { orderId })
  }
}

// 添加收退款记录
export const addPaymentRecord = (orderId: string, data: AddPaymentRecordForm): Promise<void> => {
  if (USE_MOCK_API) {
    return import('@/mock/data/payment').then(module => module.addPaymentRecord(orderId, data))
  } else {
    return request.post<any, void>(`/payment/record/${orderId}/create`, data)
  }
}

// 删除收退款记录
export const deletePaymentRecord = (orderId: string, recordId: string): Promise<void> => {
  if (USE_MOCK_API) {
    return import('@/mock/data/payment').then(module => module.deletePaymentRecord(orderId, recordId))
  } else {
    return request.delete<any, void>(`/payment/record/${orderId}/delete/${recordId}`)
  }
}

// 检查流水号是否存在
export const checkTransactionNumber = (transactionNumber: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return import('@/mock/data/payment').then(module => module.checkTransactionNumber(transactionNumber))
  } else {
    return request.get<any, boolean>(`/payment/check-transaction/${transactionNumber}`)
  }
}
