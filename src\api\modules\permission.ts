import { http } from '@/utils/http'
import { mockStores, mockDepartments, mockMenus} from '@/mock/data/permission'
import { mockRoles, mockUsers } from '@/mock/data/userRoleManagement'
import type {
  Store, CreateStoreRequest, UpdateStoreRequest, StoreQueryParams, SelectOption,StoreOption,
  Department, CreateDepartmentRequest, UpdateDepartmentRequest, DepartmentQueryParams,
  Menu, CreateMenuRequest, UpdateMenuRequest, MenuQueryParams,
  Role, CreateRoleRequest, UpdateRoleRequest, RoleQueryParams, RolePermissionRequest, RoleDataPermissionRequest,

  User, CreateUserRequest, UpdateUserRequest, UserQueryParams, UserPermissionAssignRequest,
  PageResponse, ApiResponse,
  TreeSelectOption
} from '@/types/permission'
import { ElMessage } from 'element-plus';
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Permission Module')
// Helper for mock API
const filterTree = <T extends { children?: T[] }>(
  tree: T[],
  predicate: (node: T) => boolean
): T[] => {
  return tree.reduce((acc, node) => {
    const newNode = { ...node };
    if (newNode.children) {
      newNode.children = filterTree(newNode.children, predicate);
    }
    if (predicate(node) || (newNode.children && newNode.children.length > 0)) {
      acc.push(newNode);
    }
    return acc;
  }, [] as T[]);
};


// ==================== 门店管理 API ====================
/**
 * 获取门店列表（分页，树形结构）
 */
export const getStoreList = (params: StoreQueryParams): Promise<ApiResponse<PageResponse<Store>>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { current = 1, size = 10, storeName, storeStatus, storeCode } = params;

        const filteredData = mockStores.filter(store => {
          const nameMatch = !storeName || store.storeName.includes(storeName);
          const statusMatch = !storeStatus || store.storeStatus === storeStatus;
          const codeMatch = !storeCode || store.storeCode.includes(storeCode);
          return nameMatch && statusMatch && codeMatch;
        });

        const total = filteredData.length;
        const list = filteredData.slice((current - 1) * size, current * size);

        resolve({
          code: 200,
          message: 'Success',
          result: {
            records: list,
            total,
            current,
            size,
            pages: Math.ceil(total / size),
          },
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.post('/stores/page', params);
}

/**
 * 获取门店树形结构（无分页）
 */
export const getStoreTree = (params?: { storeName?: string, storeStatus?: string }): Promise<ApiResponse<Store[]>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredData = [...mockStores];

        if (params) {
          const { storeName, storeStatus } = params;
          filteredData = mockStores.filter(store => {
            const nameMatch = !storeName || store.storeName.includes(storeName);
            const statusMatch = !storeStatus || store.storeStatus === storeStatus;
            return nameMatch && statusMatch;
          });
        }

        resolve({
          code: 200,
          message: 'Success',
          result: filteredData,
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.get('/stores/tree', { params });
}

/**
 * 新增门店
 */
export const addStore = (data: CreateStoreRequest): Promise<ApiResponse<Store>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const newStore: Store = {
          id: `store_${Date.now()}`,
          ...data,
          createTime: new Date().toISOString(),
        };
        mockStores.unshift(newStore); // Add to the top of the list
        ElMessage.success("门店添加成功");
        resolve({ code: 200, message: 'Success', result: newStore, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.post('/stores/create', data);
}

/**
 * 更新门店
 */
export const updateStore = (data: UpdateStoreRequest & { id: string }): Promise<ApiResponse<Store>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockStores.findIndex(s => s.id === data.id);
        if (index !== -1) {
          mockStores[index] = { ...mockStores[index], ...data };
          ElMessage.success("门店更新成功");
          resolve({ code: 200, message: 'Success', result: mockStores[index], timestamp: Date.now() });
        } else {
          resolve({ code: 404, message: 'Not Found', result: {} as Store, timestamp: Date.now() });
        }
      }, 300);
    });
  }
  return http.post('/stores/update', data);
}

/**
 * 删除门店
 */
export const deleteStore = (id: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockStores.findIndex(s => s.id === id);
        if (index !== -1) {
          mockStores.splice(index, 1);
          ElMessage.success("门店删除成功");
        }
        resolve({ code: 200, message: 'Success', result: undefined, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.get('/stores/delete', { id });
}

/**
 * 获取门店选项（用于下拉选择）
 */
export const getStoreOptions = (): Promise<ApiResponse<StoreOption[]>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const options = mockStores.map(store => ({
        value: store.id,
        label: store.storeName
      }));
      resolve({ code: 200, message: 'Success', result: options, timestamp: Date.now() });
    });
  }
  return http.post('/stores/options',{});
}

/**
 * 获取门店详情
 */
export const getStoreDetail = (id: string): Promise<ApiResponse<Store>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const store = mockStores.find(s => s.id === id);
        if (store) {
          resolve({ code: 200, message: 'Success', result: store, timestamp: Date.now() });
        } else {
          resolve({ code: 404, message: 'Store not found', result: {} as Store, timestamp: Date.now() });
        }
      }, 300);
    });
  }
  return http.get(`/stores/detail`, { id });
}

/**
 * 获取字典数据
 */
export const getDictionary = (type: string): Promise<ApiResponse<SelectOption[]>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      // Mock dictionary data
      let mockDictData: SelectOption[] = [];

      if (type === '1000') {
        // 门店状态字典
        mockDictData = [
          { value: 'active', label: '正常' },
          { value: 'inactive', label: '停用' }
        ];
      } else if (type === '1001') {
        // 部门状态字典
        mockDictData = [
          { value: 'normal', label: '正常' },
          { value: 'disabled', label: '停用' }
        ];
      } else if (type === '1002') {
        // 菜单状态字典
        mockDictData = [
          { value: 'normal', label: '正常' },
          { value: 'disabled', label: '停用' }
        ];
      } else if (type === '0002') {
        // 门店状态字典
        mockDictData = [
          { value: 'active', label: '正常' },
          { value: 'inactive', label: '停用' }
        ];
      } else if (type === '0200') {
        // 门店属性字典
        mockDictData = [
          { value: 'sales', label: '销售' },
          { value: 'after_sales', label: '售后' },
          { value: 'parts', label: '配件' },
          { value: 'service', label: '维修' }
        ];
      }

      resolve({ code: '200', message: 'Success', result: mockDictData, timestamp: Date.now() });
    });
  }
  return http.get('/basic/dic/list', { type });
}

// ==================== 部门管理 API ====================

/**
 * 获取部门分页列表（树形结构）
 */
export const getDepartmentList = (params: DepartmentQueryParams): Promise<ApiResponse<PageResponse<Department>>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      // 模拟后端返回的分页树形结构数据
      let filteredData = JSON.parse(JSON.stringify(mockDepartments));

      // 应用筛选条件
      if (params.departmentName || params.departmentStatus) {
        filteredData = filteredData.filter((dept: Department) => {
          const nameMatch = !params.departmentName || dept.departmentName.includes(params.departmentName);
          const statusMatch = !params.departmentStatus || dept.departmentStatus === params.departmentStatus;
          return nameMatch && statusMatch;
        });
      }

      const total = filteredData.length;
      const current = params.current || 1;
      const size = params.size || 10;
      const startIndex = (current - 1) * size;
      const endIndex = startIndex + size;
      const records = filteredData.slice(startIndex, endIndex);

      resolve({
        code: 200,
        message: 'Success',
        result: {
          records,
          total,
          size,
          current,
          pages: Math.ceil(total / size)
        },
        timestamp: Date.now()
      });
    });
  }
  return http.post('/departments/page', params);
}

/**
 * 获取部门树形结构（无分页）
 */
export const getDepartmentTree = (params?: { departmentName?: string, departmentStatus?: string }): Promise<ApiResponse<Department[]>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      let filteredData = JSON.parse(JSON.stringify(mockDepartments));

      if (params && (params.departmentName || params.departmentStatus)) {
        filteredData = filteredData.filter((dept: Department) => {
          const nameMatch = !params.departmentName || dept.departmentName.includes(params.departmentName);
          const statusMatch = !params.departmentStatus || dept.departmentStatus === params.departmentStatus;
          return nameMatch && statusMatch;
        });
      }

      resolve({ code: 200, message: 'Success', result: filteredData, timestamp: Date.now() });
    });
  }
  return http.post('/departments/tree',params);
}

/**
 * 获取部门详情
 */
export const getDepartmentDetail = (id: string): Promise<ApiResponse<Department>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const department = mockDepartments.find(d => d.id === id);
      if (department) {
        resolve({ code: 200, message: 'Success', result: department, timestamp: Date.now() });
      } else {
        resolve({ code: 404, message: 'Department not found', result: {} as Department, timestamp: Date.now() });
      }
    });
  }
  return http.get('/departments/detail', { id });
}

/**
 * 新增部门
 */
export const addDepartment = (data: CreateDepartmentRequest): Promise<ApiResponse<Department>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const newDepartment: Department = {
        ...data,
        id: `dept_${Date.now()}`,
        createTime: new Date().toISOString(),
        children: []
      };
      mockDepartments.push(newDepartment);
      ElMessage.success("部门添加成功");
      resolve({ code: 200, message: 'Success', result: newDepartment, timestamp: Date.now() });
    });
  }
  return http.post('/departments/create', data);
}

/**
 * 编辑部门
 */
export const updateDepartment = (data: UpdateDepartmentRequest): Promise<ApiResponse<Department>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const index = mockDepartments.findIndex(d => d.id === data.id);
      if (index !== -1) {
        mockDepartments[index] = { ...mockDepartments[index], ...data };
        ElMessage.success("部门更新成功");
        resolve({ code: 200, message: 'Success', result: mockDepartments[index], timestamp: Date.now() });
      } else {
        resolve({ code: 404, message: 'Department not found', result: {} as Department, timestamp: Date.now() });
      }
    });
  }
  return http.post('/departments/update', data);
}

/**
 * 删除部门
 */
export const deleteDepartment = (id: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const index = mockDepartments.findIndex(d => d.id === id);
      if (index !== -1) {
        mockDepartments.splice(index, 1);
        ElMessage.success("部门删除成功");
      }
      resolve({ code: 200, message: 'Success', result: undefined, timestamp: Date.now() });
    });
  }
  return http.get('/departments/delete',  { id });
}

/**
 * 获取部门选项（用于下拉选择）
 */
export const getDepartmentOptions = (): Promise<ApiResponse<TreeSelectOption[]>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const options = mockDepartments.map(dept => ({
        value: dept.id,
        label: dept.departmentName,
        children: dept.children?.map(child => ({
          value: child.id,
          label: child.departmentName
        }))
      }));
      resolve({ code: 200, message: 'Success', result: options, timestamp: Date.now() });
    });
  }
  return http.get('/departments/options');
}

// ==================== 菜单管理 API ====================

/**
 * 获取菜单分页列表（树形结构）
 */
export const getMenuList = (params: MenuQueryParams): Promise<ApiResponse<PageResponse<Menu>>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        let filteredData = JSON.parse(JSON.stringify(mockMenus));

        // 应用筛选条件
        if (params.menuName || params.menuStatus || params.menuSide) {
          filteredData = filterTree(filteredData, (menu: Menu) => {
            const nameMatch = !params.menuName || menu.menuName.includes(params.menuName);
            const statusMatch = !params.menuStatus || menu.menuStatus === params.menuStatus;
            const sideMatch = !params.menuSide || menu.menuSide === params.menuSide;
            return nameMatch && statusMatch && sideMatch;
          });
        }

        const total = filteredData.length;
        const current = params.current || 1;
        const size = params.size || 10;
        const startIndex = (current - 1) * size;
        const endIndex = startIndex + size;
        const records = filteredData.slice(startIndex, endIndex);

        resolve({
          code: 200,
          message: 'Success',
          result: {
            records,
            total,
            size,
            current,
            pages: Math.ceil(total / size)
          },
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.post('/menus/page', params);
}

/**
 * 获取菜单树形结构（无分页）
 */
export const getMenuTree = (params?: { menuName?: string, menuStatus?: string, menuSide?: string }): Promise<ApiResponse<Menu[]>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        let filteredData = JSON.parse(JSON.stringify(mockMenus));

        if (params && (params.menuName || params.menuStatus || params.menuSide)) {
          filteredData = filterTree(filteredData, (menu: Menu) => {
            const nameMatch = !params.menuName || menu.menuName.includes(params.menuName);
            const statusMatch = !params.menuStatus || menu.menuStatus === params.menuStatus;
            const sideMatch = !params.menuSide || menu.menuSide === params.menuSide;
            return nameMatch && statusMatch && sideMatch;
          });
        }

        resolve({ code: 200, message: 'Success', result: filteredData, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.get('/menus/tree',  params );
}

const findMenuAndParent = (menus: Menu[], menuId: string): { menu?: Menu, parentList: Menu[] } => {
 const result: { menu?: Menu, parentList: Menu[] } = { parentList: menus };
 for (const menu of menus) {
   if (menu.id === menuId) {
     result.menu = menu;
     return result;
   }
   if (menu.children) {
     const found = findMenuAndParent(menu.children, menuId);
     if (found.menu) {
       return { menu: found.menu, parentList: menu.children };
     }
   }
 }
 return result;
}

/**
 * 获取菜单详情
 */
export const getMenuDetail = (id: string): Promise<ApiResponse<Menu>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const { menu } = findMenuAndParent(mockMenus, id);
        if (menu) {
          resolve({ code: 200, message: 'Success', result: menu, timestamp: Date.now() });
        } else {
          resolve({ code: 404, message: 'Menu not found', result: {} as Menu, timestamp: Date.now() });
        }
      }, 300);
    });
  }
  return http.get('/menus/detail', { id });
}

/**
 * 新增菜单
 */
export const addMenu = (data: CreateMenuRequest): Promise<ApiResponse<Menu>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const newMenu: Menu = {
        ...data,
        id: `menu_${Date.now()}`,
        isVisible: data.isVisible !== undefined ? data.isVisible : true,
        isCache: data.isCache !== undefined ? data.isCache : false,
        createTime: new Date().toISOString()
      };
      if (data.parentId && data.parentId !== '0') {
        const { menu: parentMenu } = findMenuAndParent(mockMenus, data.parentId);
        if (parentMenu) {
          parentMenu.children = parentMenu.children || [];
          parentMenu.children.push(newMenu);
        }
      } else {
        mockMenus.push(newMenu);
      }
      ElMessage.success("菜单添加成功");
      resolve({ code: 200, message: 'Success', result: newMenu, timestamp: Date.now() });
    });
  }
  return http.post('/menus/create', data);
}

/**
 * 编辑菜单（ID放入参数实体中）
 */
export const updateMenu = (data: UpdateMenuRequest): Promise<ApiResponse<Menu>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const { menu } = findMenuAndParent(mockMenus, data.id);
      if (menu) {
        Object.assign(menu, data);
        ElMessage.success("菜单更新成功");
        resolve({ code: 200, message: 'Success', result: menu, timestamp: Date.now() });
      } else {
        resolve({ code: 404, message: 'Menu not found', result: {} as Menu, timestamp: Date.now() });
      }
    });
  }
  return http.post('/menus/update', data);
}

/**
 * 删除菜单（GET请求，ID通过查询参数传递）
 */
export const deleteMenu = (id: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      const { menu, parentList } = findMenuAndParent(mockMenus, id);
      if (menu) {
        if (menu.children && menu.children.length > 0) {
          ElMessage.error("该菜单下还有子菜单，无法删除");
          return resolve({ code: 500, message: 'Cannot delete menu with children', result: undefined, timestamp: Date.now() });
        }
        const index = parentList.findIndex(m => m.id === id);
        if (index !== -1) {
          parentList.splice(index, 1);
          ElMessage.success("菜单删除成功");
          return resolve({ code: 200, message: 'Success', result: undefined, timestamp: Date.now() });
        }
      }
      resolve({ code: 404, message: 'Menu not found', result: undefined, timestamp: Date.now() });
    });
  }
  return http.get('/menus/delete', { id });
}

// ==================== 角色管理 API ====================
export const getRoleList = (params: RoleQueryParams): Promise<ApiResponse<PageResponse<Role>>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const { current = 1, size = 10, roleName, roleSource, roleStatus, storeId } = params;

        const filteredData = mockRoles.filter(role => {
          const nameMatch = !roleName || role.roleName.includes(roleName);
          const sourceMatch = !roleSource || role.roleSource === roleSource;
          const statusMatch = !roleStatus || role.roleStatus === roleStatus;
          const storeMatch = !storeId || role.storeId === storeId;
          return nameMatch && sourceMatch && statusMatch && storeMatch;
        });

        const total = filteredData.length;
        const list = filteredData.slice((current - 1) * size, current * size);

        resolve({
          code: 200,
          message: 'Success',
          result: {
            records: list,
            total,
            current,
            size,
            pages: Math.ceil(total / size),
          },
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.post('/roles/page', params);
}

/**
 * 获取角色详情
 */
export const getRoleDetail = (id: string): Promise<ApiResponse<Role>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const role = mockRoles.find(r => r.id === id);
        if (role) {
          resolve({ code: 200, message: 'Success', result: role, timestamp: Date.now() });
        } else {
          resolve({ code: 404, message: 'Role not found', result: {} as Role, timestamp: Date.now() });
        }
      }, 300);
    });
  }
  return http.get('/roles/detail',  { id } );
}

export const addRole = (data: CreateRoleRequest): Promise<ApiResponse<Role>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const { roleName, roleCode, roleStatus, roleScope, roleSource, description } = data;
        const newRole: Role = {
          id: `role_${Date.now()}`,
          roleName,
          roleCode,
          roleStatus,
          roleScope,
          roleSource,
          description,
          createTime: new Date().toISOString(),
        };
        mockRoles.unshift(newRole);
        ElMessage.success("角色添加成功");
        resolve({ code: 200, message: 'Success', result: newRole, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.post('/roles/create', data);
}

export const updateRole = (data: UpdateRoleRequest): Promise<ApiResponse<Role>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockRoles.findIndex(r => r.id === data.id);
        if (index !== -1) {
          const updatedRole = { ...mockRoles[index], ...data };
          mockRoles[index] = updatedRole;
          ElMessage.success("角色更新成功");
          resolve({ code: 200, message: 'Success', result: mockRoles[index], timestamp: Date.now() });
        } else {
          resolve({ code: 404, message: 'Not Found', result: {} as Role, timestamp: Date.now() });
        }
      }, 300);
    });
  }
  return http.post('/roles/update', data);
}

export const deleteRole = (id: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockRoles.findIndex(r => r.id === id);
        if (index !== -1) {
          mockRoles.splice(index, 1);
          ElMessage.success("角色删除成功");
        }
        resolve({ code: 200, message: 'Success', result: undefined, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.get('/roles/delete', { id } );
}

/**
 * 配置角色菜单权限
 */
export const configureRoleMenu = (data: { roleId: string; menuIds: string[] }): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const roleIndex = mockRoles.findIndex(r => r.id === data.roleId);
        if (roleIndex !== -1) {
          mockRoles[roleIndex].menuIds = data.menuIds;
          ElMessage.success("菜单权限配置成功");
        }
        resolve({ code: 200, message: 'Success', result: undefined, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.post('/roles/menu/configure', data);
}

/**
 * 配置角色数据权限
 */
export const configureRoleDataPermission = (data: { roleId: string; dataScope: string; deptIds?: string[] }): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const roleIndex = mockRoles.findIndex(r => r.id === data.roleId);
        if (roleIndex !== -1) {
          mockRoles[roleIndex].roleScope = data.dataScope as any;
          mockRoles[roleIndex].deptIds = data.deptIds || [];
          ElMessage.success("数据权限配置成功");
        }
        resolve({ code: 200, message: 'Success', result: undefined, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.post('/roles/data-permission/configure', data);
}

/**
 * 获取角色菜单权限
 */
export const getRoleMenuPermission = (roleId: string): Promise<ApiResponse<{ menuIds: string[] }>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const role = mockRoles.find(r => r.id === roleId);
        if (role) {
          resolve({
            code: 200,
            message: 'Success',
            result: { menuIds: role.menuIds || [] },
            timestamp: Date.now()
          });
        } else {
          resolve({
            code: 404,
            message: 'Role not found',
            result: { menuIds: [] },
            timestamp: Date.now()
          });
        }
      }, 300);
    });
  }
  return http.get('/roles/menu/permission', { roleId });
}

/**
 * 获取角色数据权限
 */
export const getRoleDataPermission = (roleId: string): Promise<ApiResponse<{ dataScope: string; deptIds: string[] }>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const role = mockRoles.find(r => r.id === roleId);
        if (role) {
          resolve({
            code: 200,
            message: 'Success',
            result: {
              dataScope: role.roleScope || 'onlyPersonal',
              deptIds: role.deptIds || []
            },
            timestamp: Date.now()
          });
        } else {
          resolve({
            code: 404,
            message: 'Role not found',
            result: {
              dataScope: 'onlyPersonal',
              deptIds: []
            },
            timestamp: Date.now()
          });
        }
      }, 300);
    });
  }
  return http.get('/roles/data-permission', { roleId });
}

// ==================== 用户管理 API ====================
/**
 * 获取用户列表（分页）
 */
export const getUserListPage = (params: UserQueryParams): Promise<ApiResponse<PageResponse<User>>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { current = 1, size = 10, username, userType, storeId } = params;

        const filteredData = mockUsers.filter(user => {
          const nameMatch = !username || user.username.includes(username) || user.fullName.includes(username);
          const typeMatch = !userType || user.userType === userType;
          const storeMatch = !storeId || user.primaryStoreId === storeId;
          return nameMatch && typeMatch && storeMatch;
        });

        const total = filteredData.length;
        const list = filteredData.slice((current - 1) * size, current * size);

        resolve({
          code: 200,
          message: 'Success',
          result: {
            records: list,
            total,
            current,
            size,
            pages: Math.ceil(total / size),
          },
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.post('/users/page', params);
}

/**
 * 获取用户详情
 */
export const getUserDetailById = (id: string): Promise<ApiResponse<User>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const user = mockUsers.find(u => u.id === id);
        if (user) {
          resolve({ code: 200, message: 'Success', data: user, timestamp: Date.now() });
        } else {
          resolve({ code: 404, message: 'User not found', data: {} as User, timestamp: Date.now() });
        }
      }, 300);
    });
  }
  return http.get('/users/detail', { id } );
}

/**
 * 新增用户
 */
export const addUserApi = (data: CreateUserRequest): Promise<ApiResponse<User>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const newUser: User = {
          id: `user_${Date.now()}`,
          ...data,
          createTime: new Date().toISOString(),
          lastLoginTime: undefined,
          primaryStoreId: data.primaryStoreId || '',
          primaryStoreName: mockStores.find(s => s.id === data.primaryStoreId)?.storeName || '',
          storeRoles: []
        };
        mockUsers.unshift(newUser);
        ElMessage.success("用户添加成功");
        resolve({ code: 200, message: 'Success', data: newUser, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.post('/users/create', data);
}

/**
 * 更新用户
 */
export const updateUserApi = (data: UpdateUserRequest & { id: string }): Promise<ApiResponse<User>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockUsers.findIndex(u => u.id === data.id);
        if (index !== -1) {
          mockUsers[index] = { ...mockUsers[index], ...data };
          ElMessage.success("用户更新成功");
          resolve({ code: 200, message: 'Success', data: mockUsers[index], timestamp: Date.now() });
        } else {
          resolve({ code: 404, message: 'Not Found', data: {} as User, timestamp: Date.now() });
        }
      }, 300);
    });
  }
  return http.post('/users/update', data);
}

/**
 * 删除用户
 */
export const deleteUserApi = (id: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockUsers.findIndex(u => u.id === id);
        if (index !== -1) {
          mockUsers.splice(index, 1);
          ElMessage.success("用户删除成功");
        }
        resolve({ code: 200, message: 'Success', data: undefined, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.get('/users/delete', { id } );
}

/**
 * 更新用户状态
 */
export const updateUserStatus = (id: string, userStatus: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const index = mockUsers.findIndex(u => u.id === id);
        if (index !== -1) {
          mockUsers[index].userStatus = userStatus;
          ElMessage.success("用户状态更新成功");
        }
        resolve({ code: 200, message: 'Success', data: undefined, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.post('/users/status', { id, userStatus });
}

/**
 * 分配用户角色
 */
export const assignUserRolesApi = (userId: string, storeRoles: any[]): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const userIndex = mockUsers.findIndex(u => u.id === userId);
        if (userIndex !== -1) {
          mockUsers[userIndex].storeRoles = storeRoles;
          ElMessage.success("角色分配成功");
        }
        resolve({ code: 200, message: 'Success', data: undefined, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.post('/users/assign-roles', { userId, storeRoles });
}

/**
 * 获取用户已分配的角色
 */
export const getUserAssignedRoles = (userId: string): Promise<ApiResponse<UserStoreRole[]>> => {
  if (USE_MOCK_API) {
    return new Promise(resolve => {
      setTimeout(() => {
        const user = mockUsers.find(u => u.id === userId);
        if (user && user.storeRoles) {
          resolve({
            code: 200,
            message: 'Success',
            result: user.storeRoles,
            timestamp: Date.now()
          });
        } else {
          resolve({
            code: 200,
            message: 'Success',
            result: [],
            timestamp: Date.now()
          });
        }
      }, 300);
    });
  }
  return http.get('/users/assigned-roles', { userId });
}

// API响应处理
// 获取角色列表（根据门店筛选）
export function getRoles(params: any = {}): Promise<ApiResponse<PageResponse<ExtendedRole>>> {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredRoles = [...mockRoles];

        if (params.storeId) {
          filteredRoles = filteredRoles.filter(role =>
            role.roleSource === 'factory' ||
            (role.roleSource === 'store' && role.storeId === params.storeId)
          );
        }

        if (params.roleName) {
          filteredRoles = filteredRoles.filter(role =>
            role.roleName.includes(params.roleName)
          );
        }

        if (params.roleType) {
          filteredRoles = filteredRoles.filter(role =>
            role.roleType === params.roleType
          );
        }

        if (params.roleSource) {
          filteredRoles = filteredRoles.filter(role =>
            role.roleSource === params.roleSource
          );
        }

        const current = params.current || 1;
        const size = params.size || 10;
        const total = filteredRoles.length;
        const pages = Math.ceil(total / size);
        const start = (current - 1) * size;
        const end = start + size;
        const records = filteredRoles.slice(start, end);

        resolve({
          code: 200,
          message: '获取成功',
          result: { records, current, size, total, pages },
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.post('/roles/page', params);
}

// 获取用户列表
export function getUsers(params: any = {}): Promise<ApiResponse<PageResponse<ExtendedUser>>> {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredUsers = [...mockUsers]

        if (params.username) {
          filteredUsers = filteredUsers.filter(user =>
            user.username.includes(params.username) || user.fullName.includes(params.username)
          )
        }

        if (params.userType) {
          filteredUsers = filteredUsers.filter(user => user.userType === params.userType)
        }

        if (params.storeId) {
          filteredUsers = filteredUsers.filter(user =>
            user.storeRoles?.some(sr => sr.storeId === params.storeId)
          )
        }

        const current = params.current || 1;
        const size = params.size || 10;
        const total = filteredUsers.length;
        const pages = Math.ceil(total / size);
        const start = (current - 1) * size;
        const end = start + size;
        const records = filteredUsers.slice(start, end);

        resolve({
          code: 200,
          message: '获取成功',
          result: { records, current, size, total, pages },
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.post('/users/page', params);
}

// 获取用户详情
export function getUserDetail(userId: string): Promise<ApiResponse<ExtendedUser | undefined>> {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = mockUsers.find(u => u.id === userId);
        resolve({
          code: user ? 200 : 404,
          message: user ? '获取成功' : '用户不存在',
          result: user,
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.get('/users/detail', { id: userId });
}

// 分配用户角色
export function assignUserRoles(
  userId: string,
  storeRolesData: UserStoreRole[]
): Promise<ApiResponse<ExtendedUser | undefined>> {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const userIndex = mockUsers.findIndex(u => u.id === userId);
        if (userIndex === -1) {
          resolve({ code: 404, message: '用户不存在', result: undefined, timestamp: Date.now() });
          return;
        }

        const user = mockUsers[userIndex];
        user.storeRoles = storeRolesData;

        const primaryRole = storeRolesData.find(sr => sr.isPrimary);
        if (primaryRole) {
          user.primaryStoreId = primaryRole.storeId;
          user.primaryStoreName = primaryRole.storeName;
        } else {
          user.primaryStoreId = undefined;
          user.primaryStoreName = undefined;
        }

        resolve({ code: 200, message: '分配成功', result: user, timestamp: Date.now() });
      }, 300);
    });
  }
  return http.post('/users/assign-roles', { userId, storeRoles: storeRolesData });
}

// 获取用户在指定门店的角色
export function getUserRolesByStore(userId: string, storeId: string): Promise<ApiResponse<UserStoreRole[]>> {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = mockUsers.find(u => u.id === userId);
        if (!user || !user.storeRoles) {
          resolve({
            code: 200,
            message: '获取成功',
            result: [],
            timestamp: Date.now()
          });
          return;
        }

        const roles = user.storeRoles.filter(sr => sr.storeId === storeId);

        resolve({
          code: 200,
          message: '获取成功',
          result: roles,
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.get('/users/roles-by-store', { userId, storeId });
}

// 获取用户所有的门店和角色
export function getAllUserStoreRoles(userId: string): Promise<ApiResponse<UserStoreRole[]>> {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = mockUsers.find(u => u.id === userId);
        if (!user || !user.storeRoles) {
          resolve({
            code: 200,
            message: '获取成功',
            result: [],
            timestamp: Date.now()
          });
          return;
        }

        resolve({
          code: 200,
          message: '获取成功',
          result: user.storeRoles,
          timestamp: Date.now()
        });
      }, 300);
    });
  }
  return http.get('/users/all-store-roles', { userId });
}

/**
 * 更新角色信息
 * @param roleData 要更新的角色数据
 * @returns
 */
export const updateExtendedRole = (roleData: Partial<ExtendedRole> & { id: string }): Promise<ApiResponse<ExtendedRole | undefined>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const roleIndex = mockRoles.findIndex(r => r.id === roleData.id)
        if (roleIndex !== -1) {
          mockRoles[roleIndex] = { ...mockRoles[roleIndex], ...roleData }
          resolve({ code: 200, message: '更新成功', result: mockRoles[roleIndex], timestamp: Date.now() });
        } else {
          resolve({ code: 404, message: '角色不存在', result: undefined, timestamp: Date.now() });
        }
      }, 300);
    });
  }
  return http.post('/roles/update', roleData);
}
