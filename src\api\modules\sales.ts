// dms-frontend/src/api/modules/sales.ts
import request from '@/api'; // 导入 Axios 封装实例
// 导入你在 dms-frontend/src/types/module.d.ts 中定义的类型
import { mockVehicleList } from '@/mock/data/sales'; // 导入模拟数据
import { i18nGlobal } from '@/plugins/i18n'; // 导入国际化实例
import type { PaginationResponse, VehicleListItem, VehicleListParams } from '@/types/module.d.ts';
import { ElMessage } from 'element-plus'; // 导入 Element Plus 的消息组件

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Sales Module')

/**
 * @description 获取车辆列表
 * @param params 查询参数 (VehicleListParams)
 * @returns Promise<PaginationResponse<VehicleListItem>> 车辆列表和总数
 */
export const getVehicleList = async (params: VehicleListParams): Promise<PaginationResponse<VehicleListItem>> => {
  // **重要：根据 USE_MOCK_API 决定是返回 Mock 数据还是发起真实请求**
  if (USE_MOCK_API) {
    // 在开发模式下使用本地 Mock 数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page = 1, pageSize = 10, ...filter } = params;

        // 模拟数据过滤逻辑
        const filteredList = mockVehicleList.filter(item => {
          const matchesVin = !filter.vin || item.vin.toLowerCase().includes(filter.vin.toLowerCase());
          const matchesModel = !filter.model || item.model.toLowerCase().includes(filter.model.toLowerCase());
          const matchesBrand = !filter.brand || item.brand.toLowerCase() === filter.brand.toLowerCase();
          const matchesColor = !filter.color || item.color.toLowerCase() === filter.color.toLowerCase();
          const matchesStatus = !filter.status || item.status === filter.status;
          return matchesVin && matchesModel && matchesBrand && matchesColor && matchesStatus;
        });

        // 模拟分页逻辑
        const total = filteredList.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const list = filteredList.slice(start, end);

        resolve({
          list,
          total,
          page,
          pageSize
        });
      }, 500); // 模拟网络延迟 500 毫秒
    });
  } else {
    // **连接真实后端 API**
    // 请求路径通常会以 /api 开头，但因为我们在 vite.config.ts 中没有配置 proxy
    // 如果真实后端不是在同源，这里需要修改 VITE_APP_BASE_API 为完整地址
    const response = await request.get<PaginationResponse<VehicleListItem>>('/sales/vehicles', { params });
    return response.data;
  }
};

/**
 * @description 添加新车辆
 * @param data 车辆数据
 * @returns Promise<boolean> 操作成功与否
 */
export const addVehicle = async (data: VehicleListItem): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟添加数据，生成一个简单的ID
        const newId = (mockVehicleList.length + 1).toString();
        // 注意：这里只是简单地添加到内存中的 mockVehicleList，刷新页面会丢失
        mockVehicleList.push({
          ...data,
          id: newId,
          status: data.status || 'in_stock', // 如果未提供状态，默认设置为 in_stock
          manufactureDate: data.manufactureDate || new Date().toISOString().split('T')[0],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
        console.log('Mock: Successfully added new vehicle:', data);
        ElMessage.success(i18nGlobal.t('common.operationSuccessful')); // 模拟操作成功提示
        resolve(true);
      }, 300);
    });
  } else {
    const response = await request.post<boolean>('/sales/vehicles', data);
    return response.data;
  }
};

/**
 * @description 更新车辆信息
 * @param id 车辆ID
 * @param data 车辆更新数据
 * @returns Promise<boolean> 操作成功与否
 */
export const updateVehicle = async (id: string, data: Partial<VehicleListItem>): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockVehicleList.findIndex(v => v.id === id);
        if (index !== -1) {
          mockVehicleList[index] = { ...mockVehicleList[index], ...data, updatedAt: new Date().toISOString() };
          console.log('Mock: Successfully updated vehicle:', id, data);
          ElMessage.success(i18nGlobal.t('common.operationSuccessful'));
          resolve(true);
        } else {
          console.error('Mock: Vehicle not found for update:', id);
          ElMessage.error(i18nGlobal.t('common.operationFailed') + ': Vehicle not found');
          reject(new Error('Vehicle not found'));
        }
      }, 300);
    });
  } else {
    const response = await request.put<boolean>(`/sales/vehicles/${id}`, data);
    return response.data;
  }
};

/**
 * @description 删除车辆
 * @param id 车辆ID
 * @returns Promise<boolean> 操作成功与否
 */
export const deleteVehicle = async (id: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const initialLength = mockVehicleList.length;
        const updatedList = mockVehicleList.filter(v => v.id !== id);
        if (updatedList.length < initialLength) {
          // 模拟从原数组中删除
          mockVehicleList.splice(0, mockVehicleList.length, ...updatedList);
          console.log('Mock: Successfully deleted vehicle:', id);
          ElMessage.success(i18nGlobal.t('common.operationSuccessful'));
          resolve(true);
        } else {
          console.error('Mock: Vehicle not found for deletion:', id);
          ElMessage.error(i18nGlobal.t('common.operationFailed') + ': Vehicle not found');
          reject(new Error('Vehicle not found'));
        }
      }, 300);
    });
  } else {
    const response = await request.delete<boolean>(`/sales/vehicles/${id}`);
    return response.data;
  }
};
