import request from '@/api'
import type {
  ApprovalListParams,
  ApprovalListItem,
  ApprovalActionRequest,
  BatchApprovalRequest,
  PaginationResponse
} from '@/types/module'

// Mock数据导入
import { mockApprovalData } from '@/mock/data/approval'

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Approval Module')

/**
 * 获取审核列表
 */
export const getApprovalList = (params: ApprovalListParams): Promise<PaginationResponse<ApprovalListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 先对原始mock数据进行清洗，过滤掉不符合当前业务规则的数据
        const cleanData = mockApprovalData.filter(item => {
          const isValidType = ['cancel_order', 'modify_info'].includes(item.approvalType);
          const isValidResult = item.approvalResult ? ['approved', 'rejected'].includes(item.approvalResult) : true;
          return isValidType && isValidResult;
        });

        // 模拟数据过滤和分页逻辑


        let filteredData = [...mockApprovalData]

        // 根据tab状态过滤
        if (params.status === 'pending') {
          filteredData = filteredData.filter(item =>
            ['pending_initial', 'pending_final', 'about_timeout'].includes(item.approvalStatus)
          )
        } else if (params.status === 'approved') {
          filteredData = filteredData.filter(item =>
            ['approved', 'rejected', 'timeout_rejected'].includes(item.approvalStatus)
          )
        }

        // 根据筛选条件过滤
        if (params.approvalType) {
          filteredData = filteredData.filter(item => item.approvalType === params.approvalType)
        }
        if (params.orderNumber) {
          filteredData = filteredData.filter(item =>
            item.orderNumber.toLowerCase().includes(params.orderNumber!.toLowerCase())
          )
        }
        if (params.submittedBy) {
          filteredData = filteredData.filter(item =>
            item.submittedBy.toLowerCase().includes(params.submittedBy!.toLowerCase())
          )
        }
        if (params.submissionTimeStart) {
          filteredData = filteredData.filter(item => item.submissionTime >= params.submissionTimeStart!)
        }
        if (params.submissionTimeEnd) {
          filteredData = filteredData.filter(item => item.submissionTime <= params.submissionTimeEnd!)
        }
        if (params.approvalStatus) {
          filteredData = filteredData.filter(item => item.approvalStatus === params.approvalStatus)
        }
        if (params.approvalResult) {
          filteredData = filteredData.filter(item => item.approvalResult === params.approvalResult)
        }
        if (params.storeId) {
          filteredData = filteredData.filter(item => item.storeId === params.storeId)
        }

        // 分页
        const total = filteredData.length
        const startIndex = ((params.page || 1) - 1) * (params.pageSize || 20)
        const endIndex = startIndex + (params.pageSize || 20)
        const list = filteredData.slice(startIndex, endIndex)

        resolve({
          list,
          total,
          page: params.page || 1,
          pageSize: params.pageSize || 20
        })
      }, 300)
    })
  } else {
    return request.post<any, PaginationResponse<ApprovalListItem>>('/approval/order/list', params)
  }
}

/**
 * 审核通过
 */
export const approveApproval = (data: ApprovalActionRequest): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟成功或失败
        if (Math.random() > 0.9) {
          reject(new Error('审核操作失败'))
        } else {
          // 更新mock数据中的状态
          const item = mockApprovalData.find(item => item.id === data.approvalId)
          if (item) {
            item.approvalStatus = 'approved'
            item.approvalResult = 'approved'
            item.approvalTime = new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0]
            item.approvalComments = data.comments || ''
          }
          resolve()
        }
      }, 500)
    })
  } else {
    return request.post<any, void>('/approval/order/submit', data)
  }
}

/**
 * 审核驳回
 */
export const rejectApproval = (data: ApprovalActionRequest): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟成功或失败
        if (Math.random() > 0.9) {
          reject(new Error('审核操作失败'))
        } else {
          // 更新mock数据中的状态
          const item = mockApprovalData.find(item => item.id === data.approvalId)
          if (item) {
            item.approvalStatus = 'rejected'
            item.approvalResult = 'rejected'
            item.approvalTime = new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0]
            item.rejectionReason = data.reason || ''
          }
          resolve()
        }
      }, 500)
    })
  } else {
    return request.post<any, void>('/approval/order/submit', data)
  }
}

/**
 * 批量审核操作
 */
export const batchApprovalAction = (data: BatchApprovalRequest): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟成功或失败
        if (Math.random() > 0.9) {
          reject(new Error('批量审核操作失败'))
        } else {
          // 更新mock数据中的状态
          data.approvalIds.forEach(id => {
            const item = mockApprovalData.find(item => item.id === id)
            if (item) {
              item.approvalStatus = data.result
              item.approvalResult = data.result
              item.approvalTime = new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0]
              if (data.result === 'approved') {
                item.approvalComments = data.comments || ''
              } else {
                item.rejectionReason = data.reason || ''
              }
            }
          })
          resolve()
        }
      }, 1000)
    })
  } else {
    return request.post<any, void>('/approval/batch', data)
  }
}

/**
 * 导出审核数据
 */
export const exportApprovalData = (exportSettings: any, params: ApprovalListParams): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟导出成功
        console.log('导出设置:', exportSettings)
        console.log('导出参数:', params)
        // 在实际项目中，这里会触发文件下载
        resolve()
      }, 1500)
    })
  } else {
    return request.post<any, void>('/approval/export', { exportSettings, params })
  }
}

/**
 * 获取审核详情
 */
export const getApprovalDetail = (approvalId: string): Promise<ApprovalListItem> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const item = mockApprovalData.find(item => item.id === approvalId)
        if (item) {
          resolve(item)
        } else {
          reject(new Error('审核记录不存在'))
        }
      }, 300)
    })
  } else {
    return request.get<any, ApprovalListItem>(`/approval/order/${approvalId}/detail`)
  }
}

/**
 * 获取审核历史
 */
export const getApprovalHistory = (approvalId: string): Promise<any[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟审核历史数据
        const historyData = [
          {
            id: '1',
            action: 'submit',
            actionTime: '2024-01-15 10:00:00',
            operator: '张三',
            operatorRole: '客户',
            comments: '申请取消订单',
            status: 'submitted'
          },
          {
            id: '2',
            action: 'initial_review',
            actionTime: '2024-01-15 14:30:00',
            operator: '李四',
            operatorRole: '销售经理',
            comments: '初审通过，转至终审',
            status: 'approved'
          }
        ]
        resolve(historyData)
      }, 300)
    })
  } else {
    return request.get<any, any[]>(`/approval/history/${approvalId}`)
  }
}

/**
 * 审核订单
 */
export const approveOrder = (data: ApprovalActionRequest): Promise<void> => {
  return approveApproval(data)
}

/**
 * 获取门店列表
 */
export const getStoreList = (): Promise<any[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const storeList = [
          { id: 'store1', name: '北京旗舰店', code: 'BJ001' },
          { id: 'store2', name: '上海体验店', code: 'SH001' },
          { id: 'store3', name: '深圳展示厅', code: 'SZ001' },
          { id: 'store4', name: '广州服务中心', code: 'GZ001' }
        ]
        resolve(storeList)
      }, 200)
    })
  } else {
    return request.get<any, any[]>('/store/list')
  }
}
