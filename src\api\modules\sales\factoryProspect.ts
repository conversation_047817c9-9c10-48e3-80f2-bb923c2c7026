import request from '@/api';
import type {
  FactoryProspectSearchParams,
  FactoryProspectPageResponse,
  FactoryProspectStatisticsResponse,
} from '@/types/sales/factoryProspect';
import {
  getFactoryProspectList as getMockFactoryProspectList,
  getFactoryProspectStatistics as getMockFactoryProspectStatistics,
  exportFactoryProspectData as getMockExportFactoryProspectData,
  getCustomerBasicInfo as getMockCustomerBasicInfo,
  getStoreAssociations as getMockStoreAssociations,
  getFollowUpRecords as getMockFollowUpRecords,
  getTestDriveRecords as getMockTestDriveRecords,
  getDefeatRecords as getMockDefeatRecords,
  getChangeHistory as getMockChangeHistory,
  getPerformanceAnalysis as getMockPerformanceAnalysis
} from '@/mock/data/sales/factoryProspect';
import { USE_MOCK_API } from '@/utils/mock-config';

/**
 * 获取厂端潜客列表
 */
export const getFactoryProspectList = (params: FactoryProspectSearchParams): Promise<FactoryProspectPageResponse> => {
  if (USE_MOCK_API) {
    return getMockFactoryProspectList(params);
  } else {
    return request.post<never, FactoryProspectPageResponse>('/factory/prospects/list', params);
  }
};

/**
 * 获取厂端潜客统计数据
 */
export const getFactoryProspectStatistics = (params: Partial<FactoryProspectSearchParams>): Promise<FactoryProspectStatisticsResponse> => {
  if (USE_MOCK_API) {
    return getMockFactoryProspectStatistics(params);
  } else {
    return request.post<never, FactoryProspectStatisticsResponse>('/factory/prospects/overview/stats', params);
  }
};

/**
 * 导出厂端潜客数据
 */
export const exportFactoryProspectData = (params: FactoryProspectSearchParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    return getMockExportFactoryProspectData(params);
  } else {
    return request.post<never, Blob>('/factory/prospects/export', params, { responseType: 'blob' });
  }
};

/**
 * 获取客户基本信息
 */
export const getCustomerBasicInfo = (params: { globalCustomerId: string }) => {
  if (USE_MOCK_API) {
    return getMockCustomerBasicInfo(params);
  } else {
    return request.post('/factory/prospects/detail/basicInfo', params);
  }
};

/**
 * 获取门店关联信息
 */
export const getStoreAssociations = (params: { globalCustomerId: string }) => {
  if (USE_MOCK_API) {
    return getMockStoreAssociations(params);
  } else {
    return request.post('/factory/prospects/detail/storeAssociations', params);
  }
};

/**
 * 获取跟进记录
 */
export const getFollowUpRecords = (params: { storeProspectId: string }) => {
  if (USE_MOCK_API) {
    return getMockFollowUpRecords(params);
  } else {
    return request.post('/factory/prospects/detail/followUpRecords', params);
  }
};

/**
 * 获取试驾记录
 */
export const getTestDriveRecords = (params: { storeProspectId: string }) => {
  if (USE_MOCK_API) {
    return getMockTestDriveRecords(params);
  } else {
    return request.post('/factory/prospects/detail/testDriveRecords', params);
  }
};

/**
 * 获取战败记录
 */
export const getDefeatRecords = (params: { storeProspectId: string }) => {
  if (USE_MOCK_API) {
    return getMockDefeatRecords(params);
  } else {
    return request.post('/factory/prospects/detail/defeatRecords', params);
  }
};

/**
 * 获取变更历史
 */
export const getChangeHistory = (params: { storeProspectId: string }) => {
  if (USE_MOCK_API) {
    return getMockChangeHistory(params);
  } else {
    return request.post('/factory/prospects/detail/changeHistory', params);
  }
};

/**
 * 获取绩效分析
 */
export const getPerformanceAnalysis = (params: { globalCustomerId: string }) => {
  if (USE_MOCK_API) {
    return getMockPerformanceAnalysis(params);
  } else {
    return request.post('/factory/prospects/detail/performanceAnalysis', params);
  }
};
