import request from '@/api';
import type {
  InvoiceSearchParams,
  InvoicePageResponse,
  InvoiceDetail,
  InvoiceOperationLog,
  ApiResponse,
  ExportConfig
} from '@/types/sales/invoiceManagement';
import {
  getInvoiceListMock,
  getInvoiceDetailMock,
  printInvoiceMock,
  batchPrintInvoicesMock,
  sendInvoiceEmailMock,
  exportInvoiceDataMock,
  getInvoiceOperationLogsMock
} from '@/mock/data/sales/invoiceManagement';
import { USE_MOCK_API } from '@/utils/mock-config';

// 获取发票列表
export const getInvoiceList = (
  params: InvoiceSearchParams
): Promise<ApiResponse<InvoicePageResponse>> => {
  if (USE_MOCK_API) {
    return getInvoiceListMock(params);
  } else {
    // 保持与原接口一致的请求方式
    return request.get<any, ApiResponse<InvoicePageResponse>>(
      '/invoices', 
      { params }
    );
  }
};

// 获取发票详情
export const getInvoiceDetail = (
  id: string
): Promise<ApiResponse<InvoiceDetail>> => {
  if (USE_MOCK_API) {
    return getInvoiceDetailMock(id);
  } else {
    // 保持与原接口一致的请求方式
    return request.get<any, ApiResponse<InvoiceDetail>>(
      `/invoices/${id}`
    );
  }
};

// 打印发票
export const printInvoice = (
  id: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return printInvoiceMock(id);
  } else {
    // 保持与原接口一致的请求方式
    return request.post<any, ApiResponse<boolean>>(
      `/invoices/${id}/print`
    );
  }
};

// 批量打印发票
export const batchPrintInvoices = (
  ids: string[]
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return batchPrintInvoicesMock(ids);
  } else {
    // 保持与原接口一致的请求方式
    return request.post<any, ApiResponse<boolean>>(
      '/invoices/batch-print',
      { ids }
    );
  }
};

// 发送发票邮件
export const sendInvoiceEmail = (
  id: string,
  email: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return sendInvoiceEmailMock(id, email);
  } else {
    // 保持与原接口一致的请求方式
    return request.post<any, ApiResponse<boolean>>(
      `/invoices/${id}/email`,
      { recipientEmail: email }
    );
  }
};

// 导出发票数据
export const exportInvoiceData = (
  config: ExportConfig
): Promise<ApiResponse<string>> => {
  if (USE_MOCK_API) {
    return exportInvoiceDataMock(config);
  } else {
    // 保持与原接口一致的请求方式
    return request.post<any, ApiResponse<string>>(
      '/invoices/export',
      config
    );
  }
};

// 获取发票操作日志
export const getInvoiceOperationLogs = (
  id: string
): Promise<ApiResponse<InvoiceOperationLog[]>> => {
  if (USE_MOCK_API) {
    return getInvoiceOperationLogsMock(id);
  } else {
    // 保持与原接口一致的请求方式
    return request.get<any, ApiResponse<InvoiceOperationLog[]>>(
      `/invoices/${id}/operation-logs`
    );
  }
};

// 获取门店列表 - 保持与原接口一致
export const getStoreList = (): Promise<{ label: string; value: string }[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          { label: '门店1', value: 'store1' },
          { label: '门店2', value: 'store2' },
          { label: '门店3', value: 'store3' }
        ]);
      }, 200);
    });
  } else {
    // 保持与原接口一致的请求方式
    return request.get('/master-data/dealers').then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '获取门店列表失败');
    });
  }
};

// 获取销售顾问列表 - 保持与原接口一致
export const getConsultantList = (storeId?: string): Promise<{ label: string; value: string }[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const consultants = storeId === 'store1'
          ? [{ label: '张三', value: 'zhangsan' }, { label: '李四', value: 'lisi' }]
          : storeId === 'store2'
          ? [{ label: '王五', value: 'wangwu' }, { label: '赵六', value: 'zhaoliu' }]
          : [{ label: '钱七', value: 'qianqi' }, { label: '孙八', value: 'sunba' }];
        resolve(consultants);
      }, 200);
    });
  } else {
    // 保持与原接口一致的请求方式
    return request.get('/master-data/sales-consultants', {
      params: storeId ? { storeId } : {}
    }).then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '获取销售顾问列表失败');
    });
  }
};
