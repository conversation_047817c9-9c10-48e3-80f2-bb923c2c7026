import request from '@/api';
import type {
  SalesOrderSearchParams,
  SalesOrderListResponse,
  SalesOrderDetail,
  SaveOrderRequest,
  AvailableRight,
  RightSearchParams,
  PaginationResponse
} from '@/types/sales/orders';
import { getOrdersList as getMockOrdersList, getOrderDetail as getMockOrderDetail, getAvailableRights as getMockAvailableRights } from '@/mock/data/sales/orders';
import { USE_MOCK_API } from '@/utils/mock-config';

// 获取销售订单列表
export const getSalesOrdersList = (params: SalesOrderSearchParams): Promise<SalesOrderListResponse> => {
  if (USE_MOCK_API) {
    return getMockOrdersList(params);
  }
  return request.get<any, SalesOrderListResponse>('/sales/orders', { params });
};

// 获取销售订单详情
export const getSalesOrderDetail = (orderNo: string): Promise<SalesOrderDetail> => {
  if (USE_MOCK_API) {
    return getMockOrderDetail(orderNo);
  }
  return request.get<any, SalesOrderDetail>(`/sales/orders/${orderNo}`);
};

// 保存销售订单
export const saveSalesOrder = (data: SaveOrderRequest): Promise<any> => {
  if (USE_MOCK_API) {
    return Promise.resolve({ code: 200, message: '保存成功' });
  }
  return request.put<any, any>(`/sales/orders/${data.orderNo}`, data);
};

// 删除销售订单
export const deleteSalesOrder = (orderNo: string): Promise<any> => {
  if (USE_MOCK_API) {
    return Promise.resolve({ code: 200, message: '删除成功' });
  }
  return request.delete<any, any>(`/sales/orders/${orderNo}`);
};

// 提交订单审核
export const submitOrderApproval = (orderNo: string): Promise<any> => {
  if (USE_MOCK_API) {
    return Promise.resolve({ code: 200, message: '提交审核成功' });
  }
  return request.post<any, any>(`/sales/orders/${orderNo}/submit-approval`);
};

// 取消订单
export const cancelSalesOrder = (orderNo: string, reason: string): Promise<any> => {
  if (USE_MOCK_API) {
    return Promise.resolve({ code: 200, message: '取消成功' });
  }
  return request.post<any, any>(`/sales/orders/${orderNo}/cancel`, { reason });
};

// 获取可用权益列表
export const getAvailableRights = (params: RightSearchParams): Promise<PaginationResponse<AvailableRight>> => {
  if (USE_MOCK_API) {
    return getMockAvailableRights(params);
  }
  return request.get<any, PaginationResponse<AvailableRight>>('/sales/rights/available', { params });
};
