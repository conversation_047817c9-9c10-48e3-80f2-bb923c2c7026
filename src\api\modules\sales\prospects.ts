import request from '@/api';
import type {
  ProspectSearchParams,
  ProspectPageResponse,
  ProspectItem,
  CreateProspectRequest,
  CreateFollowUpRequest,
  FollowUpRecord,
  AssignAdvisorRequest,
  MarkNoIntentionRequest,
  ProspectDetailInfo,
  ExistingLeadInfo,
  SearchExistingLeadRequest
} from '@/types/sales/prospects';
import {
  getProspectList as getMockProspectList,
  createProspect as createMockProspect,
  getProspectDetail as getMockProspectDetail,
  addFollowUpRecord as addMockFollowUpRecord,
  assignAdvisor as assignMockAdvisor,
  markNoIntention as markMockNoIntention,
  searchExistingLead as searchMockExistingLead
} from '@/mock/data/sales/prospects';

// 获取环境变量判断是否使用Mock数据
const USE_MOCK_API = import.meta.env.VITE_USE_MOCK === 'true';

// 定义API响应结构
interface ApiResponse<T> {
  result: T;
  code: number;
  message: string;
}

/**
 * 获取潜客列表
 */
export const getProspects = async (params: ProspectSearchParams): Promise<ProspectPageResponse> => {
  if (USE_MOCK_API) {
    return getMockProspectList(params);
  } else {
    const response = await request.post<any, ApiResponse<ProspectPageResponse>>('/prospects/store/list', params);
    return response.result;
  }
};

/**
 * 获取潜客详情
 */
export const getProspectById = async (id: string): Promise<ProspectDetailInfo> => {
  if (USE_MOCK_API) {
    return getMockProspectDetail(id);
  } else {
    const response = await request.post<any, ApiResponse<ProspectDetailInfo>>(`/prospects/store/detail`, { "storeProspectId":id });
    return response.result;
  }
};

/**
 * 新增潜客
 */
export const createNewProspect = async (data: CreateProspectRequest): Promise<ProspectItem> => {
  if (USE_MOCK_API) {
    return createMockProspect(data);
  } else {
    const response = await request.post<any, ApiResponse<ProspectItem>>('/prospects/store/add', data);
    return response.result;
  }
};

/**
 * 添加跟进记录
 */
export const addFollowUp = async (data: CreateFollowUpRequest): Promise<FollowUpRecord> => {
  if (USE_MOCK_API) {
    return addMockFollowUpRecord(data);
  } else {
    const response = await request.post<any, ApiResponse<FollowUpRecord>>('/prospects/store/followUp', data);
    return response.result;
  }
};

/**
 * 分配顾问
 */
export const assignAdvisorToProspect = async (data: AssignAdvisorRequest): Promise<boolean> => {
  if (USE_MOCK_API) {
    return assignMockAdvisor(data);
  } else {
    const response = await request.post<any, ApiResponse<boolean>>('prospects/store/assignAdvisor', data);
    return response.result;
  }
};

/**
 * 标记无意向
 */
export const markProspectNoIntention = async (data: MarkNoIntentionRequest): Promise<boolean> => {
  if (USE_MOCK_API) {
    return markMockNoIntention(data);
  } else {
    const response = await request.put<any, ApiResponse<boolean>>('/sales/prospects/no-intention', data);
    return response.result;
  }
};

/**
 * 搜索现有线索
 */
export const searchExistingLeads = async (params: SearchExistingLeadRequest): Promise<ExistingLeadInfo | null> => {
  if (USE_MOCK_API) {
    return searchMockExistingLead(params);
  } else {
    const response = await request.post<any, ApiResponse<ExistingLeadInfo | null>>('/prospects/store/leads/search', params);
    return response.result;
  }
};

/**
 * 导出潜客数据
 */
export const exportProspects = async (params: ProspectSearchParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    // Mock导出功能
    return new Promise((resolve) => {
      setTimeout(() => {
        const csvContent = 'data:text/csv;charset=utf-8,潜客编号,潜客姓名,联系电话,来源渠道\nPROSPECT000001,张三,13800138001,线上渠道';
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        resolve(blob);
      }, 1000);
    });
  } else {
    const response = await request.post('/sales/prospects/export', params, {
      responseType: 'blob'
    });
    return response as unknown as Blob;
  }
};

// 导出所有API方法
export const prospectsApi = {
  getProspects,
  getProspectById,
  createNewProspect,
  addFollowUp,
  assignAdvisorToProspect,
  markProspectNoIntention,
  searchExistingLeads,
  exportProspects
};
