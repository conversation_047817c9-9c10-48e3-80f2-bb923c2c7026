import request from '@/api';
import type { VehicleQuerySearchParams, VehicleQueryPageResponse, VehicleConfiguration, WarehouseInfo } from '@/types/sales/vehicleQuery';
import { getVehicleQueryList } from '@/mock/data/sales/vehicleQuery';
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config';

// 初始化时打印Mock状态
MockConfig.logStatus('Vehicle Query Module');

/**
 * 获取车辆查询列表
 * @param params 搜索参数
 */
export const getVehicleQuery = (params: VehicleQuerySearchParams): Promise<VehicleQueryPageResponse> => {
  if (USE_MOCK_API) {
    return getVehicleQueryList(params);
  } else {
    return request.get<any, VehicleQueryPageResponse>('/vehicle-inventories', { params });
  }
};

/**
 * 获取仓库信息列表
 */
export const getWarehouseInfo = (): Promise<{ result: WarehouseInfo[] }> => {
  if (USE_MOCK_API) {
    return Promise.resolve({
      result: [
        { warehouseName: '主仓库', code: 'WH001', warehouseLocation: '总部', warehouseStatus: 'active' },
        { warehouseName: '分仓库A', code: 'WH002', warehouseLocation: '分部A', warehouseStatus: 'active' },
        { warehouseName: '分仓库B', code: 'WH003', warehouseLocation: '分部B', warehouseStatus: 'active' },
        { warehouseName: '分仓库C', code: 'WH004', warehouseLocation: '分部C', warehouseStatus: 'active' }
      ]
    });
  } else {
    return request.get<any, { result: WarehouseInfo[] }>('/sales/warehouses');
  }
};

/**
 * 获取车型配置列表
 */
export const getVehicleModelList = (): Promise<VehicleConfiguration[]> => {
  if (true || USE_MOCK_API) {
    return Promise.resolve([
      {
        model: 'Axia',
        modelName: 'Axia',
        code: 'AXIA',
        variants: [
          {
            variant: '1.0 Standard',
            colorOptions: ['白色', '黑色', '银色']
          },
          {
            variant: '1.0 SE',
            colorOptions: ['白色', '黑色', '银色', '红色']
          }
        ]
      },
      {
        model: 'Bezza',
        modelName: 'Bezza',
        code: 'BEZZA',
        variants: [
          {
            variant: '1.3 Premium',
            colorOptions: ['白色', '黑色', '银色', '蓝色']
          },
          {
            variant: '1.3 AV',
            colorOptions: ['白色', '黑色', '银色', '红色', '蓝色']
          }
        ]
      },
      {
        model: 'Myvi',
        modelName: 'Myvi',
        code: 'MYVI',
        variants: [
          {
            variant: '1.5 Advance',
            colorOptions: ['白色', '黑色', '银色', '红色', '蓝色', '灰色']
          }
        ]
      },
      {
        model: 'Alza',
        modelName: 'Alza',
        code: 'ALZA',
        variants: [
          {
            variant: '1.5 AV',
            colorOptions: ['白色', '黑色', '银色', '灰色']
          }
        ]
      },
      {
        model: 'Aruz',
        modelName: 'Aruz',
        code: 'ARUZ',
        variants: [
          {
            variant: '1.5 AV',
            colorOptions: ['白色', '黑色', '银色', '红色', '蓝色']
          }
        ]
      }
    ]);
  } else {
    return request.get<any, VehicleConfiguration[]>('/sales/vehicle-models');
  }
};
