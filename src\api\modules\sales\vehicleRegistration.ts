// 车辆登记API模块

import request from '@/api';
import type {
  VehicleRegistrationSearchParams,
  VehicleRegistrationPageResponse,
  VehicleRegistrationDetail,
  ApiResponse,
  SalesAdvisorOption
} from '@/types/sales/vehicleRegistration';

// Mock数据导入（开发环境使用）
import {
  getVehicleRegistrationListMock,
  getVehicleRegistrationDetailMock,
  pushVehicleRegistrationMock,
  retryPushVehicleRegistrationMock,
  getSalesAdvisorsListMock,
  exportVehicleRegistrationDataMock
} from '@/mock/data/sales/vehicleRegistration';

// Mock配置导入
import { USE_MOCK_API } from '@/utils/mock-config';

// 是否使用Mock数据
const USE_MOCK = USE_MOCK_API;

/**
 * 获取车辆登记列表
 * 注意：修正MyBatisPlus分页参数 pageNumber → pageNum
 */
export const getVehicleRegistrationList = async (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<VehicleRegistrationPageResponse>> => {
  if (USE_MOCK) {
    return getVehicleRegistrationListMock(params);
  }

  // 确保使用正确的MyBatisPlus分页参数
  const requestParams = {
    ...params,
    pageNum: params.pageNum || 1,  // 使用pageNum而不是pageNumber
    pageSize: params.pageSize || 20
  };

  return request.get<any, ApiResponse<VehicleRegistrationPageResponse>>('/vehicle-registrations', { params: requestParams });
};

/**
 * 获取车辆登记详情
 */
export const getVehicleRegistrationDetail = async (
  id: string
): Promise<ApiResponse<VehicleRegistrationDetail>> => {
  if (USE_MOCK) {
    return getVehicleRegistrationDetailMock(id);
  }

  return request.get<any, ApiResponse<VehicleRegistrationDetail>>(`/vehicle-registrations/${id}`);
};

/**
 * 推送车辆登记到JPJ
 */
export const pushVehicleRegistration = async (
  id: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK) {
    return pushVehicleRegistrationMock(id);
  }

  return request.post<any, ApiResponse<boolean>>(`/vehicle-registrations/push/${id}`);
};

/**
 * 重新推送车辆登记
 */
export const retryPushVehicleRegistration = async (
  id: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK) {
    return retryPushVehicleRegistrationMock(id);
  }

  return request.post<any, ApiResponse<boolean>>(`/vehicle-registrations/retry-push/${id}`);
};

/**
 * 获取销售顾问列表
 */
export const getSalesAdvisorsList = async (): Promise<ApiResponse<SalesAdvisorOption[]>> => {
  if (USE_MOCK) {
    return getSalesAdvisorsListMock();
  }

  return request.get<any, ApiResponse<SalesAdvisorOption[]>>('/sales-advisors/list');
};

/**
 * 导出车辆登记数据
 */
export const exportVehicleRegistrationData = async (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<string>> => {
  if (true || USE_MOCK) {
    return exportVehicleRegistrationDataMock(params);
  }

  // 确保使用正确的MyBatisPlus分页参数
  const requestParams = {
    ...params,
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 20
  };

  return request.post<any, ApiResponse<string>>('/vehicle-registration/export', requestParams, { responseType: 'blob' });
};

/**
 * 批量推送车辆登记
 */
export const batchPushVehicleRegistration = async (
  ids: string[]
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK) {
    // Mock批量推送
    return new Promise((resolve) => {
      setTimeout(() => {
        const success = Math.random() > 0.3; // 70%成功率
        resolve({
          code: success ? '200' : '500',
          message: success ? '批量推送成功' : '批量推送失败',
          result: success,
          timestamp: Date.now()
        });
      }, 2000);
    });
  }

  return request.post<any, ApiResponse<boolean>>('/vehicle-registration/batch-push', { ids });
};
