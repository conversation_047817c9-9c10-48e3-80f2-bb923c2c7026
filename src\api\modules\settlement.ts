import request from '@/api'
import type {
  SettlementListItem,
  SettlementListParams,
  SettlementDetail,
  PaymentManagementData,
  PaymentRecord,
  PaymentForm,
  PaginationResponse
} from '@/types/module'

// Mock数据导入
import { settlementMockData } from '@/mock/data/settlement'

const USE_MOCK_API = import.meta.env.VITE_APP_USE_MOCK_API === 'true'

// 获取结算单列表
export const getSettlementList = (params: SettlementListParams): Promise<PaginationResponse<SettlementListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredData = [...settlementMockData.list]

        // 筛选逻辑
        if (params.settlementNo) {
          filteredData = filteredData.filter(item =>
            item.settlementNo.toLowerCase().includes(params.settlementNo!.toLowerCase())
          )
        }

        if (params.workOrderNo) {
          filteredData = filteredData.filter(item =>
            item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
          )
        }

        if (params.settlementStatus && params.settlementStatus !== 'all') {
          filteredData = filteredData.filter(item => item.settlementStatus === params.settlementStatus)
        }

        if (params.paymentStatus && params.paymentStatus !== 'all') {
          filteredData = filteredData.filter(item => item.paymentStatus === params.paymentStatus)
        }

        if (params.workOrderType && params.workOrderType !== 'all') {
          filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType)
        }

        if (params.customerName) {
          filteredData = filteredData.filter(item =>
            item.customerName.includes(params.customerName!)
          )
        }

        if (params.customerPhone) {
          filteredData = filteredData.filter(item =>
            item.customerPhone.includes(params.customerPhone!)
          )
        }

        if (params.vehiclePlate) {
          filteredData = filteredData.filter(item =>
            item.vehiclePlate.includes(params.vehiclePlate!)
          )
        }

        if (params.technician && params.technician !== 'all') {
          filteredData = filteredData.filter(item => item.technician === params.technician)
        }

        if (params.serviceAdvisor && params.serviceAdvisor !== 'all') {
          filteredData = filteredData.filter(item => item.serviceAdvisor === params.serviceAdvisor)
        }

        if (params.createdAtStart) {
          filteredData = filteredData.filter(item => item.createdAt >= params.createdAtStart!)
        }

        if (params.createdAtEnd) {
          filteredData = filteredData.filter(item => item.createdAt <= params.createdAtEnd!)
        }

        // 分页
        const start = (params.page - 1) * params.pageSize
        const end = start + params.pageSize
        const paginatedData = filteredData.slice(start, end)

        resolve({
          list: paginatedData,
          total: filteredData.length,
          page: params.page,
          pageSize: params.pageSize
        })
      }, 500)
    })
  } else {
    return request.get<any, PaginationResponse<SettlementListItem>>('/settlement/list', { params })
  }
}

// 获取结算单详情
export const getSettlementDetail = (id: string): Promise<SettlementDetail> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const detail = settlementMockData.details.find(item => item.id === id)
        if (detail) {
          resolve(detail)
        } else {
          reject(new Error('结算单不存在'))
        }
      }, 300)
    })
  } else {
    return request.get<any, SettlementDetail>(`/settlement/detail/${id}`)
  }
}

// 推送结算单
export const pushSettlement = (id: string): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟推送成功
        resolve()
      }, 1000)
    })
  } else {
    return request.post<any, void>(`/settlement/push/${id}`)
  }
}

// 编辑结算单
export const updateSettlement = (id: string, data: Partial<SettlementDetail>): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟编辑成功
        const detailIndex = settlementMockData.details.findIndex(item => item.id === id)
        if (detailIndex !== -1) {
          settlementMockData.details[detailIndex] = { ...settlementMockData.details[detailIndex], ...data }
        }
        resolve()
      }, 800)
    })
  } else {
    return request.put<any, void>(`/settlement/${id}`, data)
  }
}

// 获取收退款管理数据
export const getPaymentManagementData = (settlementId: string): Promise<PaymentManagementData> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const settlement = settlementMockData.list.find(item => item.id === settlementId)
        if (!settlement) {
          reject(new Error('结算单不存在'))
          return
        }

        const paymentData: PaymentManagementData = {
          settlementInfo: {
            settlementNo: settlement.settlementNo,
            workOrderNo: settlement.workOrderNo,
            settlementStatus: settlement.settlementStatus,
            paymentStatus: settlement.paymentStatus,
            payableAmount: settlement.payableAmount,
            paidAmount: settlement.paidAmount
          },
          customerInfo: {
            customerName: settlement.customerName,
            customerPhone: settlement.customerPhone,
            vehiclePlate: settlement.vehiclePlate,
            vehicleModel: settlement.vehicleModel
          },
          paymentRecords: settlementMockData.paymentRecords.filter(record =>
            record.id.startsWith(settlementId)
          )
        }

        resolve(paymentData)
      }, 300)
    })
  } else {
    return request.get<any, PaymentManagementData>(`/settlement/payment/${settlementId}`)
  }
}

// 添加收退款记录
export const addPaymentRecord = (settlementId: string, paymentForm: PaymentForm): Promise<PaymentRecord> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newRecord: PaymentRecord = {
          id: `${settlementId}_${Date.now()}`,
          paymentNo: `PY${new Date().toISOString().slice(0, 10).replace(/-/g, '')}${String(Math.floor(Math.random() * 9999)).padStart(4, '0')}`,
          businessType: paymentForm.businessType,
          transactionNo: paymentForm.transactionNo,
          paymentMethod: paymentForm.paymentMethod,
          amount: paymentForm.amount || 0,
          paymentType: paymentForm.paymentType,
          paymentTime: paymentForm.paymentTime,
          remarks: paymentForm.remarks,
          createdAt: new Date().toISOString()
        }

        settlementMockData.paymentRecords.push(newRecord)
        resolve(newRecord)
      }, 500)
    })
  } else {
    return request.post<any, PaymentRecord>(`/settlement/payment/${settlementId}`, paymentForm)
  }
}

// 删除收退款记录
export const deletePaymentRecord = (recordId: string): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const recordIndex = settlementMockData.paymentRecords.findIndex(record => record.id === recordId)
        if (recordIndex !== -1) {
          settlementMockData.paymentRecords.splice(recordIndex, 1)
        }
        resolve()
      }, 300)
    })
  } else {
    return request.delete<any, void>(`/settlement/payment/record/${recordId}`)
  }
}

// 保存收退款记录
export const savePaymentRecords = (settlementId: string): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟保存成功，更新结算单支付状态
        const settlement = settlementMockData.list.find(item => item.id === settlementId)
        if (settlement) {
          const records = settlementMockData.paymentRecords.filter(record =>
            record.id.startsWith(settlementId)
          )

                    const totalReceived = records
            .filter(record => record.businessType === '收款')
            .reduce((sum, record) => sum + record.amount, 0)

          const totalRefunded = records
            .filter(record => record.businessType === '退款')
            .reduce((sum, record) => sum + record.amount, 0)

          const netReceived = totalReceived - totalRefunded
          settlement.paidAmount = netReceived

          // 更新支付状态
          if (netReceived <= 0) {
            settlement.paymentStatus = 'pending'
          } else if (netReceived >= settlement.payableAmount) {
            settlement.paymentStatus = 'fully_paid'
          } else if (netReceived >= settlement.payableAmount * 0.5) {
            settlement.paymentStatus = 'deposit_paid'
          } else {
            settlement.paymentStatus = 'pending'
          }
        }

        resolve()
      }, 800)
    })
  } else {
    return request.post<any, void>(`/settlement/payment/save/${settlementId}`)
  }
}

// 导出结算单数据
export const exportSettlementData = (params: SettlementListParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟导出文件
        const csvContent = 'data:text/csv;charset=utf-8,结算单号,工单号,客户姓名,应付金额\n'
        const blob = new Blob([csvContent], { type: 'text/csv' })
        resolve(blob)
      }, 1000)
    })
  } else {
    return request.post<any, Blob>('/settlement/export', params, { responseType: 'blob' })
  }
}
