import request from '@/api';
import type {
  GetTestDriveListRequest,
  TestDriveRecord
} from '../types/test-drive';

// 定义分页响应类型
interface PaginatedResult<T> {
  total: number;
  pages: number;
  current: number;
  size: number;
  records: T[];
}

// 定义统一响应结构 - 使用 dms_frontend 的标准响应结构
interface ApiResponse<T> {
  success: boolean;
  message: string;
  code: string;
  traceId: string;
  result: T;
  timestamp: number;
}

export const testDriveApi = {
  /**
   * @description: 获取试驾列表
   */
  getTestDriveList: (params: GetTestDriveListRequest): Promise<ApiResponse<PaginatedResult<TestDriveRecord>>> => {
    return request({
      url: '/sales/test-drive/list',
      method: 'post',
      data: params
    });
  },

  /**
   * @description: 创建试驾单
   */
  createTestDrive: (data: Partial<TestDriveRecord>) => {
    return request({
      url: '/sales/test-drive/create',
      method: 'post',
      data
    });
  },

  /**
   * @description: 更新试驾单
   */
  updateTestDrive: (data: Partial<TestDriveRecord>) => {
    return request({
      url: '/sales/test-drive/update',
      method: 'post',
      data
    });
  },

  /**
   * @description: 导出试驾列表
   */
  exportTestDriveList: (params: Omit<GetTestDriveListRequest, 'pageNum' | 'pageSize'>) => {
    return request({
      url: '/sales/test-drive/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    });
  },

  /**
   * @description: 获取试驾详情
   */
  getTestDriveDetail: (testDriveNo: string): Promise<ApiResponse<TestDriveRecord>> => {
    return request({
      url: `/sales/test-drive/detail/${testDriveNo}`,
      method: 'get'
    });
  },

  /**
   * @description: 搜索潜客线索
   */
  searchLeads: (params: { customerName?: string; customerPhone?: string }) => {
    const payload = {
      ...params,
      searchType: 2
    };
    return request({
      url: '/prospects/store/leads/search',
      method: 'post',
      data: payload
    });
  }
}
