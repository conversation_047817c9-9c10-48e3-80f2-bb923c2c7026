import { request } from '@/api/index'
import type { VehicleModelMasterItem, VehicleModelSearchParams, SyncLogItem, PaginationResponse } from '@/types/module'

const USE_MOCK_API = import.meta.env.VITE_APP_USE_MOCK_API === 'true'

// Mock数据
const mockVehicleModels: VehicleModelMasterItem[] = [
  {
    id: '1',
    model: 'MYVI',
    variantName: '1.5L H CVT',
    variantCode: 'BD5HZ',
    colourName: 'GRANITE GREY',
    colourCode: 'S43(M)',
    fmrid: 'C6505',
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-15 10:30:00'
  },
  {
    id: '2',
    model: 'ALZA',
    variantName: '1.5L AV CVT',
    variantCode: 'BD7AV',
    colourName: 'ELECTRIC BLUE',
    colourCode: 'B92(M)',
    fmrid: 'C6506',
    createTime: '2024-01-16 09:15:00',
    updateTime: '2024-01-16 09:15:00'
  }
]

export const getVehicleModelList = (params: VehicleModelSearchParams & { page: number; pageSize: number }): Promise<PaginationResponse<VehicleModelMasterItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const filteredData = mockVehicleModels.filter(item => {
          return (!params.model || item.model.includes(params.model)) &&
                 (!params.variantName || item.variantName.includes(params.variantName)) &&
                 (!params.colourName || item.colourName.includes(params.colourName)) &&
                 (!params.fmrid || item.fmrid.includes(params.fmrid))
        })

        const start = (params.page - 1) * params.pageSize
        const end = start + params.pageSize

        resolve({
          data: filteredData.slice(start, end),
          total: filteredData.length,
          page: params.page,
          pageSize: params.pageSize
        })
      }, 500)
    })
  } else {
    return request.get<any, PaginationResponse<VehicleModelMasterItem>>('/vehicle-models', { params })
  }
}

export const syncVehicleModelData = (): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true, message: 'Data synchronized successfully' })
      }, 2000)
    })
  } else {
    return request.post<any, { success: boolean; message: string }>('/vehicle-models/sync')
  }
}

export const getSyncLogs = (): Promise<SyncLogItem[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            syncTime: '2024-01-15 10:30:00',
            status: 'success',
            recordCount: 150
          },
          {
            id: '2',
            syncTime: '2024-01-14 10:30:00',
            status: 'failed',
            recordCount: 0,
            errorMessage: 'Connection timeout'
          }
        ])
      }, 300)
    })
  } else {
    return request.get<any, SyncLogItem[]>('/vehicle-models/sync-logs')
  }
}
