import request from '@/api/index';
import type {
  VehicleRegistrationListParams,
  VehicleRegistrationListItem,
  VehicleRegistrationDetail,
  PaginationResponse
} from '@/types/vehicleRegistration.d';
import {
  mockVehicleRegistrationList,
  mockVehicleRegistrationDetails,
  mockSalesAdvisors
} from '@/mock/data/vehicleRegistration';

// 判断是否使用 Mock 数据
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('VehicleRegistration Module')
// 获取车辆登记列表
export const getVehicleRegistrationList = (params: VehicleRegistrationListParams): Promise<PaginationResponse<VehicleRegistrationListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredData = [...mockVehicleRegistrationList];

        // 模拟筛选逻辑
        if (params.orderNumber) {
          filteredData = filteredData.filter(item =>
            item.orderNumber.toLowerCase().includes(params.orderNumber!.toLowerCase())
          );
        }

        if (params.customerName) {
          filteredData = filteredData.filter(item =>
            item.customerName.includes(params.customerName!)
          );
        }

        if (params.customerPhone) {
          filteredData = filteredData.filter(item =>
            item.customerPhone.includes(params.customerPhone!)
          );
        }

        if (params.registrationStatus) {
          filteredData = filteredData.filter(item =>
            item.registrationStatus === params.registrationStatus
          );
        }

        if (params.vin) {
          filteredData = filteredData.filter(item =>
            item.vin.toLowerCase().includes(params.vin!.toLowerCase())
          );
        }

        if (params.insuranceStatus) {
          filteredData = filteredData.filter(item =>
            item.insuranceStatus === params.insuranceStatus
          );
        }

        if (params.salesAdvisor) {
          filteredData = filteredData.filter(item =>
            item.salesAdvisor === params.salesAdvisor
          );
        }

        // 模拟分页
        const pageNumber = params.pageNumber || 1;
        const pageSize = params.pageSize || 20;
        const startIndex = (pageNumber - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedData = filteredData.slice(startIndex, endIndex);

        resolve({
          data: paginatedData,
          total: filteredData.length,
          pageNumber,
          pageSize
        });
      }, 300);
    });
  } else {
    return request.get<any, PaginationResponse<VehicleRegistrationListItem>>('/vehicle-registrations', { params });
  }
};

// 获取车辆登记详情
export const getVehicleRegistrationDetail = (id: string): Promise<VehicleRegistrationDetail> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 通过ID查找对应的订单
        const listItem = mockVehicleRegistrationList.find(item => item.id === id);
        if (listItem) {
          // 使用订单号查找详情数据
          const detail = mockVehicleRegistrationDetails[listItem.orderNumber];
          if (detail) {
            resolve(detail);
          } else {
            reject(new Error('订单详情不存在'));
          }
        } else {
          reject(new Error('订单不存在'));
        }
      }, 200);
    });
  } else {
    return request.get<any, VehicleRegistrationDetail>(`/vehicle-registrations/${id}`);
  }
};

// 推送车辆登记
export const pushVehicleRegistration = (id: string): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟推送成功
        const targetItem = mockVehicleRegistrationList.find(item => item.id === id);
        if (targetItem) {
          targetItem.registrationStatus = 'processing';
          targetItem.pushTime = new Date().toLocaleString();
        }

        resolve({
          success: true,
          message: '推送成功'
        });
      }, 1000);
    });
  } else {
    return request.get<any, { success: boolean; message: string }>(`/vehicle-registrations/${id}/push`);
  }
};

// 重新推送车辆登记
export const retryPushVehicleRegistration = (id: string): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟重新推送成功
        const targetItem = mockVehicleRegistrationList.find(item => item.id === id);
        if (targetItem) {
          targetItem.registrationStatus = 'processing';
          targetItem.pushTime = new Date().toLocaleString();
        }

        resolve({
          success: true,
          message: '重新推送成功'
        });
      }, 1000);
    });
  } else {
    return request.get<any, { success: boolean; message: string }>(`/vehicle-registrations/${id}/retry-push`);
  }
};

// 获取销售顾问列表
export const getSalesAdvisorsList = (): Promise<Array<{ value: string; label: string }>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockSalesAdvisors);
      }, 100);
    });
  } else {
    return request.get<any, Array<{ value: string; label: string }>>('/master-data/sales-consultants');
  }
};

// 导出车辆登记数据
export const exportVehicleRegistrationData = (params: VehicleRegistrationListParams): Promise<{ downloadUrl: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          downloadUrl: '/mock/vehicle-registration-export.xlsx'
        });
      }, 2000);
    });
  } else {
    return request.post<any, { downloadUrl: string }>('/vehicle-registration/export', params);
  }
};
