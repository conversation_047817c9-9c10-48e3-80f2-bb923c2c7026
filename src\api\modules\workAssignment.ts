import request from '@/api';
import { ElMessage } from 'element-plus';
import { i18nGlobal } from '@/plugins/i18n';
import type {
  WorkOrderListItem,
  WorkOrderListParams,
  TechnicianInfo,
  AssignmentStatistics,
  TechnicianSchedule,
  AssignWorkOrderRequest,
  ReassignWorkOrderRequest,
  PaginationResponse
} from '@/types/module.d.ts';
import {
  mockWorkOrderList,
  mockTechnicianList,
  mockServiceAdvisors,
  mockAssignmentStatistics,
  generateTechnicianSchedules
} from '@/mock/data/workAssignment';

// 获取环境变量，用于判断是否使用本地 Mock 数据
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('WorkAssignment Module')

/**
 * @description 获取工单列表
 * @param params 查询参数
 * @returns Promise<PaginationResponse<WorkOrderListItem>>
 */
export const getWorkOrderList = async (params: WorkOrderListParams): Promise<PaginationResponse<WorkOrderListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page = 1, pageSize = 20, ...filter } = params;

        // 模拟数据过滤逻辑
        const filteredList = mockWorkOrderList.filter(item => {
          // 工单编号筛选
          const matchesWorkOrderId = !filter.workOrderId ||
            item.workOrderId.toLowerCase().includes(filter.workOrderId.toLowerCase());

          // 优先级筛选
          const matchesPriority = !filter.priority || item.priority === filter.priority;

          // 工单类型筛选
          const matchesType = !filter.workOrderType || item.workOrderType === filter.workOrderType;

          // 送修人姓名筛选
          const matchesCustomerName = !filter.customerName ||
            item.customerName.includes(filter.customerName);

          // 车牌号筛选
          const matchesLicensePlate = !filter.licensePlate ||
            item.licensePlate.includes(filter.licensePlate);

          // 服务顾问筛选
          const matchesServiceAdvisor = !filter.serviceAdvisorId ||
            item.serviceAdvisorId === filter.serviceAdvisorId;

          // 分配状态筛选
          const matchesAssignmentStatus = !filter.assignmentStatus ||
            (filter.assignmentStatus === 'pending_assign' && item.status === 'pending_assign') ||
            (filter.assignmentStatus === 'assigned' && item.status !== 'pending_assign');

          // 工单状态筛选
          const matchesStatus = !filter.status || item.status === filter.status;

          return matchesWorkOrderId && matchesPriority && matchesType &&
                 matchesCustomerName && matchesLicensePlate && matchesServiceAdvisor &&
                 matchesAssignmentStatus && matchesStatus;
        });

        // 模拟分页逻辑
        const total = filteredList.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const list = filteredList.slice(start, end);

        resolve({
          list,
          total,
          page,
          pageSize
        });
      }, 500);
    });
  } else {
    const response = await request.get<PaginationResponse<WorkOrderListItem>>('/work-assignment/work-orders', { params });
    return response.data;
  }
};

/**
 * @description 获取技师列表
 * @returns Promise<TechnicianInfo[]>
 */
export const getTechnicianList = async (): Promise<TechnicianInfo[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockTechnicianList);
      }, 300);
    });
  } else {
    const response = await request.get<TechnicianInfo[]>('/work-assignment/technicians');
    return response.data;
  }
};

/**
 * @description 获取服务顾问列表
 * @returns Promise<{id: string, name: string}[]>
 */
export const getServiceAdvisorList = async (): Promise<{id: string, name: string}[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockServiceAdvisors);
      }, 200);
    });
  } else {
    const response = await request.get<{id: string, name: string}[]>('/work-assignment/service-advisors');
    return response.data;
  }
};

/**
 * @description 获取派工统计数据
 * @returns Promise<AssignmentStatistics>
 */
export const getAssignmentStatistics = async (): Promise<AssignmentStatistics> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 动态更新统计数据
        const currentTime = new Date();
        const updatedStats = {
          ...mockAssignmentStatistics,
          statsDate: currentTime.toISOString().split('T')[0],
          // 模拟数据的小幅波动
          totalOrders: mockAssignmentStatistics.totalOrders + Math.floor(Math.random() * 5),
          pendingAssignment: Math.max(0, mockAssignmentStatistics.pendingAssignment + Math.floor(Math.random() * 3) - 1),
          inProgressOrders: mockAssignmentStatistics.inProgressOrders + Math.floor(Math.random() * 3) - 1
        };
        resolve(updatedStats);
      }, 400);
    });
  } else {
    const response = await request.get<AssignmentStatistics>('/work-assignment/statistics');
    return response.data;
  }
};

/**
 * @description 获取技师工作安排时间表
 * @param params 查询参数，包含日期等
 * @returns Promise<TechnicianSchedule[]>
 */
export const getTechnicianSchedules = async (params?: { date?: string }): Promise<TechnicianSchedule[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const schedules = generateTechnicianSchedules();
        resolve(schedules);
      }, 600);
    });
  } else {
    const response = await request.get<TechnicianSchedule[]>('/work-assignment/technician-schedules', { params });
    return response.data;
  }
};

/**
 * @description 分配工单
 * @param data 分配请求参数
 * @returns Promise<boolean>
 */
export const assignWorkOrder = async (data: AssignWorkOrderRequest): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 查找要分配的工单
        const workOrderIndex = mockWorkOrderList.findIndex(item => item.workOrderId === data.workOrderId);
        const technician = mockTechnicianList.find(tech => tech.technicianId === data.technicianId);

        if (workOrderIndex === -1) {
          const errorMsg = '工单未找到';
          ElMessage.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        if (!technician) {
          const errorMsg = '技师未找到';
          ElMessage.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        // 更新工单信息
        const workOrder = mockWorkOrderList[workOrderIndex];
        workOrder.status = 'pending_start';
        workOrder.assignedTechnicianId = data.technicianId;
        workOrder.assignedTechnicianName = technician.technicianName;
        workOrder.assignmentTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
        workOrder.estimatedStartTime = data.estimatedStartTime;
        workOrder.estimatedFinishTime = data.estimatedFinishTime;
        workOrder.assignmentNotes = data.assignmentNotes;
        workOrder.updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

        // 更新技师工作负荷
        technician.currentWorkOrders += 1;
        technician.totalEstimatedHours += workOrder.estimatedWorkHours;
        technician.availableHours = Math.max(0, 8 - technician.totalEstimatedHours);

        // 更新技师状态
        if (technician.totalEstimatedHours <= 2) {
          technician.workLoadStatus = 'idle';
        } else if (technician.totalEstimatedHours <= 6) {
          technician.workLoadStatus = 'moderate';
        } else if (technician.totalEstimatedHours <= 8) {
          technician.workLoadStatus = 'busy';
        } else {
          technician.workLoadStatus = 'overloaded';
        }

        technician.currentStatus = technician.totalEstimatedHours > 0 ? 'busy' : 'available';

        console.log('Mock: Successfully assigned work order:', data);
        ElMessage.success('分配成功');
        resolve(true);
      }, 800);
    });
  } else {
    const response = await request.post<boolean>('/work-assignment/assign', data);
    return response.data;
  }
};

/**
 * @description 重新分配工单
 * @param data 重新分配请求参数
 * @returns Promise<boolean>
 */
export const reassignWorkOrder = async (data: ReassignWorkOrderRequest): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 查找要重新分配的工单
        const workOrderIndex = mockWorkOrderList.findIndex(item => item.workOrderId === data.workOrderId);
        const newTechnician = mockTechnicianList.find(tech => tech.technicianId === data.newTechnicianId);

        if (workOrderIndex === -1) {
          const errorMsg = '工单未找到';
          ElMessage.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        if (!newTechnician) {
          const errorMsg = '技师未找到';
          ElMessage.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        const workOrder = mockWorkOrderList[workOrderIndex];

        // 释放原技师工作负荷
        if (workOrder.assignedTechnicianId) {
          const oldTechnician = mockTechnicianList.find(tech => tech.technicianId === workOrder.assignedTechnicianId);
          if (oldTechnician) {
            oldTechnician.currentWorkOrders = Math.max(0, oldTechnician.currentWorkOrders - 1);
            oldTechnician.totalEstimatedHours = Math.max(0, oldTechnician.totalEstimatedHours - workOrder.estimatedWorkHours);
            oldTechnician.availableHours = Math.min(8, 8 - oldTechnician.totalEstimatedHours);

            // 更新原技师状态
            if (oldTechnician.totalEstimatedHours <= 2) {
              oldTechnician.workLoadStatus = 'idle';
            } else if (oldTechnician.totalEstimatedHours <= 6) {
              oldTechnician.workLoadStatus = 'moderate';
            } else if (oldTechnician.totalEstimatedHours <= 8) {
              oldTechnician.workLoadStatus = 'busy';
            } else {
              oldTechnician.workLoadStatus = 'overloaded';
            }

            oldTechnician.currentStatus = oldTechnician.totalEstimatedHours > 0 ? 'busy' : 'available';
          }
        }

        // 更新工单信息
        workOrder.assignedTechnicianId = data.newTechnicianId;
        workOrder.assignedTechnicianName = newTechnician.technicianName;
        workOrder.assignmentTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
        workOrder.estimatedStartTime = data.estimatedStartTime;
        workOrder.estimatedFinishTime = data.estimatedFinishTime;
        workOrder.assignmentNotes = data.assignmentNotes;
        workOrder.updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

        // 更新新技师工作负荷
        newTechnician.currentWorkOrders += 1;
        newTechnician.totalEstimatedHours += workOrder.estimatedWorkHours;
        newTechnician.availableHours = Math.max(0, 8 - newTechnician.totalEstimatedHours);

        // 更新新技师状态
        if (newTechnician.totalEstimatedHours <= 2) {
          newTechnician.workLoadStatus = 'idle';
        } else if (newTechnician.totalEstimatedHours <= 6) {
          newTechnician.workLoadStatus = 'moderate';
        } else if (newTechnician.totalEstimatedHours <= 8) {
          newTechnician.workLoadStatus = 'busy';
        } else {
          newTechnician.workLoadStatus = 'overloaded';
        }

        newTechnician.currentStatus = newTechnician.totalEstimatedHours > 0 ? 'busy' : 'available';

        console.log('Mock: Successfully reassigned work order:', data);
        ElMessage.success('重新分配成功');
        resolve(true);
      }, 800);
    });
  } else {
    const response = await request.post<boolean>('/work-assignment/reassign', data);
    return response.data;
  }
};

/**
 * @description 技师开工确认 (Check in)
 * @param workOrderId 工单ID
 * @returns Promise<boolean>
 */
export const checkInWorkOrder = async (workOrderId: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const workOrderIndex = mockWorkOrderList.findIndex(item => item.workOrderId === workOrderId);

        if (workOrderIndex === -1) {
          const errorMsg = '工单未找到';
          ElMessage.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        const workOrder = mockWorkOrderList[workOrderIndex];

        if (workOrder.status !== 'pending_start') {
          const errorMsg = '工单状态不允许开工';
          ElMessage.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        // 更新工单状态
        workOrder.status = 'in_progress';
        workOrder.actualStartTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
        workOrder.updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

        console.log('Mock: Successfully checked in work order:', workOrderId);
        ElMessage.success('开工成功');
        resolve(true);
      }, 400);
    });
  } else {
    const response = await request.post<boolean>(`/work-assignment/check-in/${workOrderId}`);
    return response.data;
  }
};

/**
 * @description 技师完工确认 (Check out)
 * @param workOrderId 工单ID
 * @returns Promise<boolean>
 */
export const checkOutWorkOrder = async (workOrderId: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const workOrderIndex = mockWorkOrderList.findIndex(item => item.workOrderId === workOrderId);

        if (workOrderIndex === -1) {
          const errorMsg = '工单未找到';
          ElMessage.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        const workOrder = mockWorkOrderList[workOrderIndex];

        if (workOrder.status !== 'in_progress') {
          const errorMsg = '工单状态不允许完工';
          ElMessage.error(errorMsg);
          reject(new Error(errorMsg));
          return;
        }

        // 更新工单状态
        workOrder.status = 'pending_qc';
        workOrder.actualFinishTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
        workOrder.updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

        // 更新技师工作负荷
        if (workOrder.assignedTechnicianId) {
          const technician = mockTechnicianList.find(tech => tech.technicianId === workOrder.assignedTechnicianId);
          if (technician) {
            technician.currentWorkOrders = Math.max(0, technician.currentWorkOrders - 1);
            technician.totalEstimatedHours = Math.max(0, technician.totalEstimatedHours - workOrder.estimatedWorkHours);
            technician.availableHours = Math.min(8, 8 - technician.totalEstimatedHours);

            // 更新技师状态
            if (technician.totalEstimatedHours <= 2) {
              technician.workLoadStatus = 'idle';
            } else if (technician.totalEstimatedHours <= 6) {
              technician.workLoadStatus = 'moderate';
            } else if (technician.totalEstimatedHours <= 8) {
              technician.workLoadStatus = 'busy';
            } else {
              technician.workLoadStatus = 'overloaded';
            }

            technician.currentStatus = technician.totalEstimatedHours > 0 ? 'busy' : 'available';
          }
        }

        console.log('Mock: Successfully checked out work order:', workOrderId);
        ElMessage.success('完工成功');
        resolve(true);
      }, 400);
    });
  } else {
    const response = await request.post<boolean>(`/work-assignment/check-out/${workOrderId}`);
    return response.data;
  }
};
