import request from '@/api/index';
import type {
  WorkOrder,
  WorkOrderListItem,
  WorkOrderListParams,
  ProjectItem,
  PartsSelectItem,
  User,
  OperationLog,
  PaginationResponse
} from '@/types/workOrder';
import {
  workOrderListData,
  workOrderDetailData,
  projectItemsData,
  partsSelectData,
  usersData,
  operationLogsData
} from '@/mock/data/workOrder';

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('WorkOrder Module')

// 获取工单列表
export const getWorkOrderList = (params: WorkOrderListParams): Promise<PaginationResponse<WorkOrderListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredData = [...workOrderListData];

        // 应用筛选条件
        if (params.work_order_number) {
          filteredData = filteredData.filter(item =>
            item.work_order_number?.includes(params.work_order_number!)
          );
        }

        if (params.work_order_type) {
          filteredData = filteredData.filter(item =>
            item.work_order_type === params.work_order_type
          );
        }

        if (params.work_order_priority) {
          filteredData = filteredData.filter(item =>
            item.work_order_priority === params.work_order_priority
          );
        }

        if (params.work_order_status && params.work_order_status.length > 0) {
          filteredData = filteredData.filter(item =>
            params.work_order_status!.includes(item.work_order_status!)
          );
        }

        if (params.payment_status) {
          filteredData = filteredData.filter(item =>
            item.payment_status === params.payment_status
          );
        }

        if (params.sender_name) {
          filteredData = filteredData.filter(item =>
            item.sender_name.includes(params.sender_name!)
          );
        }

        if (params.sender_phone) {
          filteredData = filteredData.filter(item =>
            item.sender_phone.includes(params.sender_phone!)
          );
        }

        if (params.license_plate) {
          filteredData = filteredData.filter(item =>
            item.license_plate.includes(params.license_plate!)
          );
        }

        if (params.is_claim !== undefined) {
          filteredData = filteredData.filter(item =>
            item.is_claim === params.is_claim
          );
        }

        if (params.is_outsourced !== undefined) {
          filteredData = filteredData.filter(item =>
            item.is_outsourced === params.is_outsourced
          );
        }

        // 分页处理
        const page = params.page || 1;
        const pageSize = params.pageSize || 20;
        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const data = filteredData.slice(startIndex, endIndex);

        resolve({
          data,
          total,
          page,
          pageSize
        });
      }, 300);
    });
  } else {
    return request.get<any, PaginationResponse<WorkOrderListItem>>('/work-orders', { params });
  }
};

// 获取工单详情
export const getWorkOrderDetail = (workOrderId: string): Promise<WorkOrder> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const workOrder = workOrderDetailData[workOrderId];
        if (workOrder) {
          resolve(workOrder);
        } else {
          reject(new Error('工单不存在'));
        }
      }, 200);
    });
  } else {
    return request.get<any, WorkOrder>(`/work-orders/${workOrderId}`);
  }
};

// 创建工单
export const createWorkOrder = (workOrder: Partial<WorkOrder>): Promise<WorkOrder> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newWorkOrder: WorkOrder = {
          work_order_id: `wo_${Date.now()}`,
          work_order_number: `WO${new Date().toISOString().slice(0, 10).replace(/-/g, '')}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
          work_order_status: 'draft',
          payment_status: 'pending',
          created_time: new Date().toISOString(),
          updated_time: new Date().toISOString(),
          ...workOrder,
        } as WorkOrder;

        // 添加到Mock数据中
        workOrderDetailData[newWorkOrder.work_order_id!] = newWorkOrder;

        resolve(newWorkOrder);
      }, 500);
    });
  } else {
    return request.post<any, WorkOrder>('/work-orders', workOrder);
  }
};

// 更新工单
export const updateWorkOrder = (workOrderId: string, workOrder: Partial<WorkOrder>): Promise<WorkOrder> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const existingWorkOrder = workOrderDetailData[workOrderId];
        if (existingWorkOrder) {
          const updatedWorkOrder = {
            ...existingWorkOrder,
            ...workOrder,
            updated_time: new Date().toISOString()
          };
          workOrderDetailData[workOrderId] = updatedWorkOrder;
          resolve(updatedWorkOrder);
        } else {
          reject(new Error('工单不存在'));
        }
      }, 500);
    });
  } else {
    return request.put<any, WorkOrder>(`/work-orders/${workOrderId}`, workOrder);
  }
};

// 删除工单
export const deleteWorkOrder = (workOrderId: string): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (workOrderDetailData[workOrderId]) {
          delete workOrderDetailData[workOrderId];
          resolve();
        } else {
          reject(new Error('工单不存在'));
        }
      }, 300);
    });
  } else {
    return request.delete<any, void>(`/work-orders/${workOrderId}`);
  }
};

// 推送工单给客户确认
export const pushWorkOrderToCustomer = (workOrderId: string): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const workOrder = workOrderDetailData[workOrderId];
        if (workOrder) {
          workOrder.work_order_status = 'pending_confirmation';
          workOrder.updated_time = new Date().toISOString();
          resolve();
        } else {
          reject(new Error('工单不存在'));
        }
      }, 300);
    });
  } else {
    return request.post<any, void>(`/work-orders/${workOrderId}/push`);
  }
};

// 工单增项
export const addWorkOrderItems = (workOrderId: string, items: { laborItems?: any[], partsItems?: any[] }): Promise<WorkOrder> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const workOrder = workOrderDetailData[workOrderId];
        if (workOrder) {
          if (items.laborItems) {
            workOrder.labor_items.push(...items.laborItems);
          }
          if (items.partsItems) {
            workOrder.parts_items.push(...items.partsItems);
          }
          workOrder.has_additional_items = true;
          workOrder.updated_time = new Date().toISOString();
          resolve(workOrder);
        } else {
          reject(new Error('工单不存在'));
        }
      }, 400);
    });
  } else {
    return request.post<any, WorkOrder>(`/work-orders/${workOrderId}/additional-items`, items);
  }
};

// 获取项目列表（用于选择）
export const getProjectItems = (params?: {
  keyword?: string;
  type?: string;
  workOrderType?: string
}): Promise<ProjectItem[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredItems = [...projectItemsData];

        if (params?.keyword) {
          filteredItems = filteredItems.filter(item =>
            item.item_name.includes(params.keyword!) ||
            item.item_code.includes(params.keyword!)
          );
        }

        if (params?.type) {
          filteredItems = filteredItems.filter(item => item.item_type === params.type);
        }

        // 根据工单类型过滤项目
        if (params?.workOrderType === 'maintenance') {
          filteredItems = filteredItems.filter(item => item.item_type === 'maintenance');
        } else if (params?.workOrderType === 'repair') {
          filteredItems = filteredItems.filter(item =>
            item.item_type === 'repair' || item.item_type === 'claim'
          );
        }

        resolve(filteredItems);
      }, 200);
    });
  } else {
    return request.get<any, ProjectItem[]>('/project-items', { params });
  }
};

// 获取零件列表（用于选择）
export const getPartsItems = (params?: { keyword?: string }): Promise<PartsSelectItem[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredItems = [...partsSelectData];

        if (params?.keyword) {
          filteredItems = filteredItems.filter(item =>
            item.parts_name.includes(params.keyword!) ||
            item.parts_code.includes(params.keyword!)
          );
        }

        resolve(filteredItems);
      }, 200);
    });
  } else {
    return request.get<any, PartsSelectItem[]>('/parts-items', { params });
  }
};

// 获取用户列表（服务顾问、技师等）
export const getUsers = (role?: string): Promise<User[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredUsers = [...usersData];

        if (role) {
          filteredUsers = filteredUsers.filter(user => user.user_role === role);
        }

        resolve(filteredUsers);
      }, 200);
    });
  } else {
    return request.get<any, User[]>('/users', { params: { role } });
  }
};

// 获取工单操作日志
export const getWorkOrderLogs = (workOrderId: string): Promise<OperationLog[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const logs = operationLogsData[workOrderId] || [];
        resolve(logs);
      }, 200);
    });
  } else {
    return request.get<any, OperationLog[]>(`/work-orders/${workOrderId}/logs`);
  }
};

// 校验零件库存
export const validatePartsStock = (partsItems: { parts_id: string; quantity: number }[]): Promise<{ valid: boolean; invalidItems?: any[] }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const invalidItems: any[] = [];

        partsItems.forEach(item => {
          const parts = partsSelectData.find(p => p.parts_id === item.parts_id);
          if (!parts || parts.available_stock < item.quantity) {
            invalidItems.push({
              parts_id: item.parts_id,
              parts_name: parts?.parts_name,
              required_quantity: item.quantity,
              available_stock: parts?.available_stock || 0
            });
          }
        });

        resolve({
          valid: invalidItems.length === 0,
          invalidItems: invalidItems.length > 0 ? invalidItems : undefined
        });
      }, 200);
    });
  } else {
    return request.post<any, { valid: boolean; invalidItems?: any[] }>('/parts-items/validate-stock', { partsItems });
  }
};
