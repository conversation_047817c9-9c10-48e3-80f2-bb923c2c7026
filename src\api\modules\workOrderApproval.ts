import request from '@/api'
import type { WorkOrderApprovalRequest, WorkOrderApprovalResult } from '@/types/module'

export const getClaimApprovalDetail = async (approvalNo: string): Promise<WorkOrderApprovalResult> => {
  return request.get(`/api/work-order-approval/claim/${approvalNo}`)
}

export const getCancelApprovalDetail = async (approvalNo: string): Promise<WorkOrderApprovalResult> => {
  return request.get(`/api/work-order-approval/cancel/${approvalNo}`)
}

export const getPendingApprovalList = async (params: any): Promise<any> => {
  return request.get('/api/work-order-approval/pending', { params })
}

export const getCompletedApprovalList = async (params: any): Promise<any> => {
  return request.get('/api/work-order-approval/completed', { params })
}

export const exportApprovalData = async (params: any): Promise<any> => {
  return request.get('/api/work-order-approval/export', { params })
}

export const submitApproval = async (data: WorkOrderApprovalRequest): Promise<any> => {
  return request.post('/api/work-order-approval/submit', data)
}