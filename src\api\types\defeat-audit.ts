// 战败审核相关类型定义

// 分页响应接口
export interface PaginationResponse<T> {
  records: T[];
  total: number;
  pageNum?: number;
  pageSize: number;
}

// 审核结果枚举
export enum AuditResult {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

// 审核结果选项
export const AUDIT_RESULT_OPTIONS = [
  { label: '审核通过', value: AuditResult.APPROVED },
  { label: '审核驳回', value: AuditResult.REJECTED }
];

// 战败申请信息
export interface DefeatApplication {
  id: number;
  applicationNo?: string;
  applicantId?: number;
  applicantName: string;
  prospectId: number;
  prospectName: string;
  prospectPhone: string;
  defeatReason: string;
  defeatDescription?: string;
  applicationTime: string;
  auditResult: AuditResult;
  auditTime?: string;
  auditorId?: number;
  auditorName?: string;
  auditComment?: string;
  attachments?: string[];
}

// 查询战败申请列表请求参数
export interface GetDefeatApplicationListRequest {
  applicantName?: string;
  prospectName?: string;
  prospectPhone?: string;
  applicationTimeStart?: string;
  applicationTimeEnd?: string;
  auditResult?: AuditResult[];
  pageNum: number;
  pageSize: number;
}

// 审核战败申请请求参数
export interface AuditDefeatApplicationRequest {
  applicationId: number;
  auditStatus: AuditResult;
  auditComment: string;
}

// API响应结构
export interface ApiResponse<T> {
  code: string;
  message: string;
  result: T;
}
