// 厂端潜客类型定义

// 工厂潜客统计概览响应类型
export interface FactoryOverviewStatsResponse {
  totalLeadCount: number;
  hLevelProspectCount: number;
  monthlyConversionProspectCount: number;
  crossStoreCustomerCount: number;
  totalLeadCountVsLastMonth: number;
  hLevelProspectCountVsLastMonth: number;
  monthlyConversionRate: string;
  crossStoreCustomerRatio: string;
}

// 工厂潜客列表项类型
export interface FactoryProspectItem {
  globalCustomerId: string;
  leadId: string;
  customerName: string;
  phoneNumber: string;
  associatedStoreCount: number;
  registrationTime: string;
  prospectStatus: string;
}

// 工厂潜客列表响应类型
export interface FactoryProspectListResponse {
  total: number;
  size: number;
  current: number;
  records: FactoryProspectItem[];
}

// 意向级别配置类型
export interface IntentLevelConfig {
  id: string;
  intentLevel: string;
  followUpDurationHours: number;
  definitionDescription: string;
}

// 意向级别配置响应类型
export interface IntentLevelConfigResponse {
  configs: IntentLevelConfig[];
}

// 客户基本信息响应类型
export interface CustomerBasicInfoResponse {
  customerId: string;
  customerName: string;
  phoneNumber: string;
  idType: string;
  idNumber: string;
  email: string;
  registerTime: string;
  registerSource: string;
  currentStatus: string;
}

// 门店关联记录类型
export interface StoreAssociationRecord {
  storeId: string;
  storeName: string;
  leadAssociationTime: string;
  associationReason: string;
  currentSalesAdvisor: string;
  currentIntentLevel: string;
  lastFollowUpTime: string;
}

// 门店关联响应类型
export interface StoreAssociationsResponse {
  associatedStoreCount: number;
  storeAssociationRecords: StoreAssociationRecord[];
}

// 跟进记录类型
export interface FollowUpRecord {
  id: string;
  storeName: string;
  salesAdvisor: string;
  followUpTime: string;
  followUpMethod: string;
  followUpContent: string;
  intentLevel: string;
  nextFollowUpTime: string;
}

// 跟进记录响应类型
export interface FollowUpRecordsResponse {
  totalCount: number;
  earliestTime: string;
  latestTime: string;
  followUpRecords: FollowUpRecord[];
}

// 试驾记录类型
export interface TestDriveRecord {
  id: string;
  storeName: string;
  salesAdvisor: string;
  testDriveTime: string;
  vehicleModel: string;
  duration: number;
  customerFeedback: string;
}

// 试驾记录响应类型
export interface TestDriveRecordsResponse {
  totalCount: number;
  testDriveRecords: TestDriveRecord[];
}

// 战败记录类型
export interface DefeatRecord {
  id: string;
  storeName: string;
  salesAdvisor: string;
  defeatTime: string;
  defeatReason: string;
  competingBrand: string;
  competingModel: string;
  approvalStatus: string;
}

// 战败记录响应类型
export interface DefeatRecordsResponse {
  totalCount: number;
  defeatRecords: DefeatRecord[];
}

// 变更历史记录类型
export interface ChangeHistoryRecord {
  id: string;
  changedTime: string;
  changedBy: string;
  changedField: string;
  oldValue: string;
  newValue: string;
  changeReason: string;
}

// 变更历史响应类型
export interface ChangeHistoryResponse {
  totalCount: number;
  changeHistoryRecords: ChangeHistoryRecord[];
}

// 绩效分析响应类型
export interface PerformanceAnalysisResponse {
  totalFollowUpCount: number;
  totalAssociatedStoreCount: number;
  mostActiveStoreName: string;
  mostActiveStoreFollowUpCount: number;
}
