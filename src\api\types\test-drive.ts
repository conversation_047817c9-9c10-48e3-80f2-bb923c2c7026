export interface TestDriveRecord {
  // --- Core IDs ---
  id: string; // Frontend-only key
  testDriveNo: string; // Replaces testDriveId
  customerId?: number;

  // --- Prospect/Customer Info ---
  customerName: string; // Replaces prospectName & part of driverName in list
  customerPhone: string; // Replaces prospectPhone & part of driverPhone in list
  customerIdType?: string | number; // Replaces prospectIdType
  customerIdNumber?: string; // Replaces prospectIdNumber
  source?: string; // Replaces prospectSource
  email?: string; // Replaces prospectEmail
  region?: string; // Replaces prospectRegion
  address?: string; // Replaces prospectAddress

  // --- Driver Info (can be different from customer) ---
  driverName: string;
  driverPhone: string;
  driverIdType?: number | string | null;
  driverIdNumber: string;
  driverLicenseNumber: string;

  // --- Test Drive Details ---
  model: number | string | null; // Replaces testDriveModel
  variant: number | string | null; // Replaces testDriveVariant
  startMileage: number | null;
  endMileage: number | null;
  mileage?: number; // From list response
  startTime: string | number | null;
  endTime: string | number | null;
  feedback?: string; // Replaces testDriveFeedback

  // --- Sales & Store Info ---
  consultantId?: number;
  consultantName: number | string | null; // Replaces salesAdvisorName
  storeId?: number;
  storeName?: string; // From list response
  storeRegion?: string;

  // --- Timestamps & Flags ---
  createTime: string; // Replaces entryTime
  updateTime?: string;
  editable?: boolean; // From list response
}

export interface GetTestDriveListRequest {
  testDriveNo?: string;
  customerName?: string;
  customerPhone?: string;
  model?: string;
  variant?: string;
  startTimeBegin?: string;
  startTimeEnd?: string;
  storeIds?: number[];
  regionCodes?: string[];
  pageNum: number;
  pageSize: number;
}
