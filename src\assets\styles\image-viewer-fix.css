/* Element Plus 遮罩背景修复 - 针对模态框、抽屉和图片查看器 */

/* 图片查看器背景修复 */
.el-image-viewer__mask {
  background-color: rgba(0, 0, 0, 0.8) !important;
  background: rgba(0, 0, 0, 0.8) !important;
}

.el-image-viewer__wrapper {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 确保图片查看器关闭按钮可见且样式正确 */
.el-image-viewer__close {
  color: white !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  border-radius: 50% !important;
  width: 44px !important;
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 24px !important;
  font-weight: bold !important;
  z-index: 9999 !important;
  position: absolute !important;
  top: 20px !important;
  right: 20px !important;
  cursor: pointer !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.el-image-viewer__close:hover {
  background-color: rgba(0, 0, 0, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.6) !important;
  transform: scale(1.1) !important;
}

/* 确保图片查看器操作按钮可见 */
.el-image-viewer__actions {
  background-color: rgba(0, 0, 0, 0.7) !important;
  border-radius: 25px !important;
  padding: 12px 20px !important;
  backdrop-filter: blur(5px) !important;
}

.el-image-viewer__actions .el-image-viewer__action {
  color: white !important;
  font-size: 20px !important;
  margin: 0 8px !important;
  padding: 8px !important;
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
}

.el-image-viewer__actions .el-image-viewer__action:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.1) !important;
}

/* 确保图片本身正确显示 */
.el-image-viewer__img {
  max-width: 90% !important;
  max-height: 90% !important;
  object-fit: contain !important;
}

/* 只针对对话框和抽屉的遮罩层 - 设置淡灰色半透明背景 */
html body .el-dialog__wrapper,
body .el-dialog__wrapper,
.el-dialog__wrapper,
html body .el-drawer__wrapper,
body .el-drawer__wrapper,
.el-drawer__wrapper {
  background-color: rgba(0, 0, 0, 0.3) !important;
  background: rgba(0, 0, 0, 0.3) !important;
}

/* 确保模态框本身保持正常的白色背景 */
.el-dialog,
.el-drawer {
  background-color: #ffffff !important;
}

.el-dialog__header,
.el-dialog__body,
.el-dialog__footer {
  background-color: #ffffff !important;
}

.el-drawer__header,
.el-drawer__body {
  background-color: #ffffff !important;
}

/* 确保下拉选择框保持正常显示 */
.el-select,
.el-select__wrapper,
.el-select-dropdown,
.el-select-dropdown__item,
.el-popper,
.el-scrollbar,
.el-scrollbar__view {
  background-color: #ffffff !important;
}

.el-select-dropdown {
  border: 1px solid #e4e7ed !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

/* 图片查看器设置淡色半透明背景，确保关闭按钮可见 */
html body .el-image-viewer__mask,
html body div.el-image-viewer__mask,
body .el-image-viewer__mask,
.el-image-viewer__mask {
  background-color: rgba(0, 0, 0, 0.5) !important;
  background: rgba(0, 0, 0, 0.5) !important;
}

/* 图片查看器的包装器设置淡色背景 */
html body .el-image-viewer__wrapper,
html body div.el-image-viewer__wrapper,
body .el-image-viewer__wrapper,
.el-image-viewer__wrapper {
  background-color: rgba(0, 0, 0, 0.5) !important;
  background: rgba(0, 0, 0, 0.5) !important;
}

/* 确保图片查看器的关闭按钮可见 */
.el-image-viewer__close {
  color: #ffffff !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.el-image-viewer__close:hover {
  background-color: rgba(0, 0, 0, 0.7) !important;
}
