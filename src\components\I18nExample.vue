<template>
  <div class="i18n-example">
    <h2>{{ t('vehicleList') }}</h2>
    <el-button @click="handleSave">{{ tc('save') }}</el-button>
    <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
    <el-button @click="handleConfirm">{{ tc('confirm') }}</el-button>

    <p>{{ t('buyerName') }}: {{ sampleBuyerName }}</p>
    <p>{{ t('orderNumber') }}: {{ sampleOrderNumber }}</p>

    <!-- 使用多模块翻译 -->
    <div class="multi-module-example">
      <h3>多模块示例：</h3>
      <p>销售: {{ sales('vehicleList') }}</p>
      <p>订单: {{ order('orderNumber') }}</p>
      <p>通用: {{ tc('loading') }}</p>
    </div>

    <!-- 语言切换 -->
    <div class="language-switcher">
      <el-radio-group v-model="currentLocale" @change="changeLanguage">
        <el-radio label="zh">中文</el-radio>
        <el-radio label="en">English</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElButton, ElRadioGroup, ElRadio, ElMessage } from 'element-plus'
import { useModuleI18n, useMultiModuleI18n } from '@/composables/useModuleI18n'

// 使用销售模块的国际化
const { t, tc, locale } = useModuleI18n('sales')

// 使用多模块国际化
const { sales, order } = useMultiModuleI18n(['sales', 'order'])

// 当前语言
const currentLocale = computed({
  get: () => locale.value,
  set: (value) => {
    locale.value = value
  }
})

// 示例数据
const sampleBuyerName = ref('张三')
const sampleOrderNumber = ref('ORD-20231201-001')

// 处理保存
const handleSave = () => {
  ElMessage.success(tc('operationSuccessful'))
}

// 处理取消
const handleCancel = () => {
  ElMessage.info(tc('operationCanceled'))
}

// 处理确认
const handleConfirm = () => {
  ElMessage.success(tc('success'))
}

// 切换语言
const changeLanguage = (value: string) => {
  localStorage.setItem('lang', value)
  ElMessage.success(tc('languageChanged'))
}
</script>

<style scoped>
.i18n-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.multi-module-example {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.language-switcher {
  margin-top: 20px;
  padding: 15px;
  border-top: 1px solid #eee;
}

h2, h3 {
  color: #409eff;
}

p {
  margin: 10px 0;
}

.el-button {
  margin-right: 10px;
}
</style>
