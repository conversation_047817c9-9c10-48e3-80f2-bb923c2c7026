<template>
  <el-dialog
    :model-value="visible"
    title="选择零件"
    width="800px"
    :close-on-click-modal="false"
    :modal="false"
    @update:model-value="updateVisible"
  >
    <div class="parts-select-dialog">
      <!-- 搜索区域 -->
      <el-form inline class="search-form">
        <el-form-item label="零件名称/编码">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入零件名称或编码"
            clearable
            @input="handleSearch"
          />
        </el-form-item>
        <el-form-item label="零件类型">
          <el-select v-model="partType" placeholder="请选择类型" clearable @change="handleSearch" :loading="dictionaryLoading">
            <el-option label="全部" value="" />
            <el-option
              v-for="option in partsTypeOptions"
              :key="option.code"
              :value="option.code"
              :label="option.name"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 零件列表 -->
      <el-table
        :data="filteredParts"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="parts_code" label="零件编码" width="120" />
        <el-table-column prop="parts_name" label="零件名称" min-width="200" />
        <el-table-column prop="unit_price" label="单价(元)" width="100" align="right">
          <template #default="{ row }">
            ¥{{ row.unit_price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="available_stock" label="可用库存" width="100" align="center">
          <template #default="{ row }">
            <span :class="{ 'low-stock': row.available_stock < 10 }">
              {{ row.available_stock }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="is_claim" label="是否索赔" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_claim ? 'success' : ''">
              {{ row.is_claim ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="selectedParts.length === 0">
          确定选择 ({{ selectedParts.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { PartsSelectItem } from '@/types/workOrder';
import { getPartsItems } from '@/api/modules/workOrder';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', parts: PartsSelectItem[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 使用字典数据
const {
  options: partsTypeOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.PARTS_TYPE);

// 搜索参数
const searchKeyword = ref('');
const partType = ref('');
const currentPage = ref(1);
const pageSize = ref(20);

// 数据相关
const partsData = ref<PartsSelectItem[]>([]);
const loading = ref(false);
const selectedParts = ref<PartsSelectItem[]>([]);

// 计算属性
const filteredParts = computed(() => {
  let filtered = [...partsData.value];

  // 按关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(part =>
      part.parts_name.toLowerCase().includes(keyword) ||
      part.parts_code.toLowerCase().includes(keyword)
    );
  }

  // 按类型过滤
  if (partType.value) {
    if (partType.value === 'claim') {
      filtered = filtered.filter(part => part.is_claim);
    } else if (partType.value === 'normal') {
      filtered = filtered.filter(part => !part.is_claim);
    }
  }

  return filtered;
});

const total = computed(() => filteredParts.value.length);

// 更新显示状态
const updateVisible = (value: boolean) => {
  emit('update:visible', value);
};

// 获取零件数据
const fetchPartsData = async () => {
  loading.value = true;
  try {
    const response = await getPartsItems({ keyword: searchKeyword.value });
    partsData.value = response;
  } catch (error) {
    ElMessage.error('获取零件数据失败');
    console.error('Failed to fetch parts data:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  // 在实际项目中，这里可能需要重新请求数据
  // 目前使用前端过滤
};

// 选择处理
const handleSelectionChange = (selection: PartsSelectItem[]) => {
  selectedParts.value = selection;
};

// 确认选择
const handleConfirm = () => {
  if (selectedParts.value.length === 0) {
    ElMessage.warning('请至少选择一个零件');
    return;
  }

  emit('confirm', selectedParts.value);
  handleCancel();
};

// 取消
const handleCancel = () => {
  selectedParts.value = [];
  emit('update:visible', false);
};

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    fetchPartsData();
  } else {
    // 重置状态
    searchKeyword.value = '';
    partType.value = '';
    currentPage.value = 1;
    selectedParts.value = [];
  }
});

// 组件挂载时获取数据
onMounted(() => {
  if (props.visible) {
    fetchPartsData();
  }
});
</script>

<style lang="scss" scoped>
.parts-select-dialog {
  .search-form {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .low-stock {
    color: #f56c6c;
    font-weight: bold;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
