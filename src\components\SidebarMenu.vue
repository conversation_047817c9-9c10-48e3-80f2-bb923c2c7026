<template>
  <el-menu
    :default-active="activeMenu"
    class="el-menu-vertical-demo sidebar-menu"
    :collapse="isCollapse"
    router
  >
    <SidebarMenuItem
      v-for="item in menuTree"
      :key="item.menuId"
      :item="item"
      :level="1"
    />
  </el-menu>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useAuthStore } from '@/stores/auth';
import SidebarMenuItem from './SidebarMenuItem.vue';

defineProps({
  isCollapse: {
    type: Boolean,
    default: false
  }
});

const authStore = useAuthStore();
const route = useRoute();
const { t } = useModuleI18n('base');

// 当前激活菜单
const activeMenu = computed(() => route.path);

interface MenuItem {
  menuId: number
  menuName: string
  menuPath?: string | null
  menuCode?: string
  menuIcon?: string | null
  menuType?: string
  parentId?: number | null
  children?: MenuItem[]
}

// 构建菜单树，过滤掉按钮类型
function buildMenuTree(menuList: MenuItem[]): MenuItem[] {
  if (!Array.isArray(menuList)) return [];
  // 只保留目录和菜单类型
  const filtered = menuList.filter(item => item.menuType !== 'button');
  const map: Record<string, MenuItem> = {};
  filtered.forEach(item => map[item.menuId] = { ...item, children: [] });
  const tree: MenuItem[] = [];
  filtered.forEach(item => {
    if (item.parentId && map[item.parentId]) {
      map[item.parentId].children!.push(map[item.menuId]);
    } else {
      tree.push(map[item.menuId]);
    }
  });
  return tree;
}

const menuTree = computed(() => {
  // 首页菜单项（始终显示，不受权限控制）
  const homeMenuItem: MenuItem = {
    menuId: 0,
    menuName: t('home'),
    menuPath: '/',
    menuCode: 'home',
    menuIcon: 'HomeFilled',
    menuType: 'menu',
    parentId: null
  };

  // 获取用户的菜单列表
  const userMenus = authStore.userInfo?.menus || [];

  // 将首页菜单项添加到菜单列表的最前面
  const allMenus = [homeMenuItem, ...userMenus];

  return buildMenuTree(allMenus);
});
</script>

<style scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
}
.el-menu-vertical-demo {
  height: 100%;
  border-right: 1px solid #f0f0f0;
  background: #fff;
}
/* 一级、二级、三级菜单全部左对齐 */
.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title),
.sidebar-menu :deep(.el-sub-menu .el-menu-item),
.sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-menu-item),
.sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-sub-menu .el-menu-item) {
  padding-left: 24px !important;
  text-indent: 0 !important;
  margin-left: 0 !important;
}

/* 强制覆盖 Element Plus 的默认缩进 */
.sidebar-menu :deep(.el-sub-menu .el-menu-item) {
  padding-left: 24px !important;
}

.sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-menu-item) {
  padding-left: 24px !important;
}

.sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-sub-menu .el-menu-item) {
  padding-left: 24px !important;
}
/* 一级菜单样式 */
.sidebar-menu :deep(.el-sub-menu__title) {
  font-weight: bold;
  font-size: 16px;
  color: #222;
}
/* 选中高亮 */
.sidebar-menu :deep(.el-menu-item.is-active) {
  background: #e6f7ff !important;
  color: #1976d2 !important;
  font-weight: bold;
}
/* 其他层级仅用颜色区分 */
.sidebar-menu :deep(.el-menu-item) {
  font-size: 15px;
}
.sidebar-menu :deep(.el-sub-menu .el-menu-item) {
  color: #1976d2;
  background: #f6f8fa;
  border-radius: 4px;
  margin: 2px 0;
}
.sidebar-menu :deep(.el-sub-menu .el-sub-menu .el-menu-item) {
  color: #888;
  background: none;
}
</style>
