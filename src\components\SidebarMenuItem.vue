<template>
  <el-sub-menu v-if="item.children && item.children.length" :index="item.menuPath || item.menuCode">
    <template #title>
      <el-icon v-if="item.menuIcon">
        <component :is="item.menuIcon" />
      </el-icon>
      <span
        :class="[
          'menu-title',
          level === 1 ? 'menu-title-root' : '',
          level === 2 ? 'menu-title-second' : '',
          level >= 3 ? 'menu-title-third' : ''
        ]"
      >
        {{ getMenuTitle(item) }}
      </span>
    </template>
    <SidebarMenuItem v-for="child in item.children" :key="child.menuId" :item="child" :level="level + 1" />
  </el-sub-menu>
  <el-menu-item v-else :index="item.menuPath || item.menuCode" @click="handleClick(item)">
    <el-icon v-if="item.menuIcon">
      <component :is="item.menuIcon" />
    </el-icon>
    <span
      :class="[
        'menu-title',
        level === 1 ? 'menu-title-root' : '',
        level === 2 ? 'menu-title-second' : '',
        level >= 3 ? 'menu-title-third' : ''
      ]"
    >
      {{ getMenuTitle(item) }}
    </span>
  </el-menu-item>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
// import { useModuleI18n } from '@/composables/useModuleI18n'

interface MenuItem {
  menuId: number
  menuName: string
  menuPath?: string | null
  menuCode?: string
  menuIcon?: string | null
  menuType?: string
  parentId?: number | null
  children?: MenuItem[]
}

const props = defineProps<{ item: MenuItem, level?: number }>()
const router = useRouter()
const level = props.level ?? 1

// 使用菜单模块的国际化
// const { t } = useModuleI18n('menu')

function getMenuTitle(item: MenuItem): string {
  // 如果有menuCode，优先使用国际化翻译
  // if (item.menuCode) {
  //   try {
  //     const translated = t(item.menuCode)
  //     // 如果翻译存在且不是翻译键本身，使用翻译结果
  //     if (translated && translated !== `menu.${item.menuCode}`) {
  //       return translated
  //     }
  //   } catch {
  //     console.warn(`Translation not found for menu code: ${item.menuCode}`)
  //   }
  // }

  // 如果没有menuCode或翻译失败，使用原始menuName
  return item.menuName
}

function handleClick(item: MenuItem) {
  if (item.menuPath) {
    router.push(item.menuPath)
  }
}
</script>

<style scoped>
.menu-title-root {
  font-weight: bold;
  font-size: 16px;
  color: #222;
  padding-left: 0;
  letter-spacing: 1px;
}
.menu-title-second {
  font-weight: 600;
  font-size: 15px;
  color: #1976d2;
  padding-left: 0;
  background: #f6f8fa;
  border-radius: 4px;
  margin: 2px 0;
  display: inline-block;
}
.menu-title-third {
  font-size: 14px;
  color: #888;
  padding-left: 0;
}
.menu-title {
  font-size: 14px;
  transition: color 0.2s;
}
</style>
