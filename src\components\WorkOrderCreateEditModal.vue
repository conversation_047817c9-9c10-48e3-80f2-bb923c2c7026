<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :modal="false"
    @update:model-value="updateVisible"
    @close="handleClose"
  >
    <div class="work-order-form">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="top"
        class="dialog-form-modern"
      >
        <!-- 客户信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span>{{ t('customerInfo') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12" v-if="formData.customerSource === 'appointment'">
              <el-form-item :label="t('appointmentCustomer')">
                <el-input
                  :model-value="formData.customerInfo?.appointmentCustomerName"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formData.customerSource === 'appointment'">
              <el-form-item :label="t('appointmentPhone')">
                <el-input
                  :model-value="formData.customerInfo?.appointmentCustomerPhone"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="customerInfo.repairCustomerName"
                :label="t('senderName')"
              >
                <el-input
                  v-model="formData.customerInfo.repairCustomerName"
                  :placeholder="t('placeholders.senderName')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="customerInfo.repairCustomerPhone"
                :label="t('senderPhone')"
              >
                <el-input
                  v-model="formData.customerInfo.repairCustomerPhone"
                  :placeholder="t('placeholders.senderPhone')"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 车辆信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span>{{ t('vehicleInfo') }}</span>
          </template>

          <!-- 车牌号查询区域 -->
          <el-row :gutter="20" class="mb-20">
            <el-col :span="18">
              <el-form-item :label="t('licensePlate')">
                <el-input
                  v-model="licensePlateQuery"
                  :placeholder="t('placeholders.licensePlate')"
                  @keyup.enter="handleQueryVehicleInfo"
                  clearable
                  :disabled="isEdit && !!workOrderId"
                >
                  <template #append>
                    <el-button :icon="Search" @click="handleQueryVehicleInfo"></el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="text-align: right; line-height: 52px;">
              <el-text type="info">{{ t('autoFillOrManual') }}</el-text>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                prop="vehicleInfo.licensePlate"
                :label="t('licensePlate')"
              >
                <el-input
                  v-model="formData.vehicleInfo.licensePlate"
                  :placeholder="t('placeholders.licensePlate')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="vehicleInfo.vin"
                :label="t('vinCode')"
              >
                <el-input
                  v-model="formData.vehicleInfo.vin"
                  :placeholder="t('placeholders.vinCode')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                prop="vehicleInfo.modelConfigColor"
                :label="t('modelConfigColor')"
              >
                <el-input
                  v-model="formData.vehicleInfo.modelConfigColor"
                  :placeholder="t('placeholders.modelConfigColor')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="t('remarks')">
                <el-input
                  v-model="formData.vehicleInfo.remark"
                  type="textarea"
                  :rows="3"
                  :placeholder="t('placeholders.remarks')"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 工单信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span>{{ t('workOrderInfo') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                prop="priority"
                :label="t('priority')"
              >
                <el-radio-group v-model="formData.priority">
                  <el-radio value="urgent">{{ t('priorities.urgent') }}</el-radio>
                  <el-radio value="normal">{{ t('priorities.normal') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                prop="customerSource"
                :label="t('customerSource')"
              >
                <el-radio-group v-model="formData.customerSource">
                  <el-radio value="appointment">{{ t('customerSources.appointment') }}</el-radio>
                  <el-radio value="walk_in">{{ t('customerSources.walkin') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                prop="workOrderType"
                :label="t('type')"
              >
                <el-radio-group v-model="formData.workOrderType">
                  <el-radio value="repair">{{ t('types.repair') }}</el-radio>
                  <el-radio
                    value="maintenance"
                    :disabled="formData.customerSource === 'walk_in'"
                  >
                    {{ t('types.maintenance') }}
                  </el-radio>
                  <el-radio value="claim">{{ t('types.insurance') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 项目选择区 -->
        <el-card class="mb-20" v-if="!isAdditional">
          <template #header>
            <span>{{ t('projectSelection') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="20">
              <el-autocomplete
                v-model="projectSearchKeyword"
                :fetch-suggestions="queryProjectSuggestions"
                :placeholder="t('placeholders.searchProject')"
                @select="handleProjectSelect"
                clearable
                style="width: 100%"
              >
                <template #default="{ item }">
                  <div class="suggestion-item">
                    <div class="project-name">{{ item.projectName }}</div>
                    <div class="project-code">{{ item.projectCode }}</div>
                  </div>
                </template>
              </el-autocomplete>
            </el-col>
            <el-col :span="4">
              <el-button
                type="primary"
                :disabled="!selectedProject"
                @click="addSelectedProject"
              >
                {{ tCommon('add') }}
              </el-button>
            </el-col>
          </el-row>

          <div class="project-suggestions mt-15" v-if="suggestedProjects.length > 0">
            <div class="suggestion-title">{{ t('suggestedProjects') }}：</div>
            <div class="project-tags">
              <el-tag
                v-for="project in suggestedProjects.slice(0, 5)"
                :key="project.projectId"
                @click="addProjectItem(project)"
                class="clickable-tag"
              >
                {{ project.projectName }}
              </el-tag>
            </div>
          </div>
        </el-card>

        <!-- 工时详情区 -->
        <el-card class="mb-20">
          <template #header>
            <div class="card-header">
              <span>{{ t('laborDetails') }}</span>
              <div>
                <el-button type="primary" size="small" @click="handleAddLabor">
                  {{ t('addLabor') }}
                </el-button>
                <el-button size="small" @click="handleClearAllLabor">
                  {{ t('clearAll') }}
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="formData.laborItems" style="width: 100%">
            <el-table-column :label="t('itemType')" width="80" align="center">
              <template #default="{ row }">
                <el-tag
                  :type="row.type === 'maintenance' ? 'success' : (row.type === 'repair' ? 'warning' : 'primary')"
                >
                  {{ t(`types.${row.type}`) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="laborCode" :label="t('itemCode')" width="120" />
            <el-table-column prop="laborName" :label="t('itemName')" width="200" />
            <el-table-column :label="t('hasAdditional')" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.isAdded ? 'warning' : ''">
                  {{ row.isAdded ? tCommon('yes') : tCommon('no') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="t('standardHours')" width="100" align="right">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.standardHours"
                  :min="0.1"
                  :max="99.9"
                  :step="0.1"
                  :precision="1"
                  size="small"
                  @change="updateLaborSubtotal($index)"
                />
              </template>
            </el-table-column>
            <el-table-column :label="t('laborRate')" width="100" align="right">
              <template #default="{ row }">
                ¥{{ row.unitPrice }}
              </template>
            </el-table-column>
            <el-table-column :label="t('subtotal')" width="100" align="right">
              <template #default="{ row }">
                ¥{{ row.subtotal.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column :label="tCommon('operations')" width="80" align="center">
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  :icon="Delete"
                  link
                  @click="removeLaborItem($index)"
                >
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="labor-summary mt-15">
            <span>{{ t('laborTotal') }}：{{ totalLaborHours.toFixed(1) }}{{ tCommon('hours') }}</span>
            <span class="ml-20">{{ t('laborTotalAmount') }}：¥{{ totalLaborAmount.toFixed(2) }}</span>
          </div>
        </el-card>

        <!-- 零件详情区 -->
        <el-card class="mb-20">
          <template #header>
            <div class="card-header">
              <span>{{ t('partsDetails') }}</span>
              <div>
                <el-button type="primary" size="small" @click="handleAddParts">
                  {{ t('addParts') }}
                </el-button>
                <el-button size="small" @click="handleClearAllParts">
                  {{ t('clearAll') }}
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="formData.partsItems" style="width: 100%">
            <el-table-column prop="partName" :label="t('partsName')" width="150" />
            <el-table-column :label="t('isClaim')" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.isClaim ? 'success' : ''">
                  {{ row.isClaim ? tCommon('yes') : tCommon('no') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="t('hasAdditional')" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.isAdded ? 'warning' : ''">
                  {{ row.isAdded ? tCommon('yes') : tCommon('no') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="t('availableStock')" width="100" align="right">
              <template #default="{ row }">
                {{ row.availableStock }}
              </template>
            </el-table-column>
            <el-table-column :label="t('quantity')" width="100" align="right">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.quantity"
                  :min="1"
                  :max="row.availableStock"
                  size="small"
                  @change="updatePartsSubtotal($index)"
                />
              </template>
            </el-table-column>
            <el-table-column :label="t('unitPrice')" width="100" align="right">
              <template #default="{ row }">
                ¥{{ row.unitPrice }}
              </template>
            </el-table-column>
            <el-table-column :label="t('subtotal')" width="100" align="right">
              <template #default="{ row }">
                ¥{{ row.subtotal.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column :label="tCommon('operations')" width="80" align="center">
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  :icon="Delete"
                  link
                  @click="removePartsItem($index)"
                >
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="parts-summary mt-15">
            <span>{{ t('partsTotal') }}：{{ totalPartsQuantity }}{{ t('unit') }}</span>
            <span class="ml-20">{{ t('partsTotalAmount') }}：¥{{ totalPartsAmount.toFixed(2) }}</span>
          </div>
        </el-card>

        <!-- 工单总金额统计 -->
        <el-card class="mb-20">
          <template #header>
            <span>{{ t('costSummary') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="cost-item">
                <span class="cost-label">{{ t('laborAmount') }}：</span>
                <span class="cost-value">¥{{ totalLaborAmount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="cost-item">
                <span class="cost-label">{{ t('partsAmount') }}：</span>
                <span class="cost-value">¥{{ totalPartsAmount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="cost-item">
                <span class="cost-label">{{ t('additionalAmount') }}：</span>
                <span class="cost-value">¥{{ additionalAmount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="cost-item total-cost">
                <span class="cost-label">{{ t('workOrderTotal') }}：</span>
                <span class="cost-value">¥{{ totalAmount.toFixed(2) }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="handleClose">{{ tCommon('close') }}</el-button>
        <el-button @click="handleSave">{{ t('saveOnly') }}</el-button>
        <el-button
          type="primary"
          @click="handleSaveAndPush"
          :disabled="!canSaveAndPush"
        >
          {{ t('saveAndPush') }}
        </el-button>
      </span>
    </template>

    <!-- 项目选择弹窗 -->
    <ProjectSelectDialog
      v-model:visible="projectSelectVisible"
      :work-order-type="formData.workOrderType"
      @confirm="handleProjectDialogConfirm"
    />

    <!-- 零件选择弹窗 -->
    <PartsSelectDialog
      v-model:visible="partsSelectVisible"
      @confirm="handlePartsSelect"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Delete } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrder, LaborItem, PartsItem, ProjectItem } from '@/types/workOrder';
import {
  getWorkOrderDetail,
  createWorkOrder,
  updateWorkOrder,
  pushWorkOrderToCustomer,
  getProjectItems,
  validatePartsStock
} from '@/api/modules/workOrder';
import ProjectSelectDialog from './ProjectSelectDialog.vue';
import PartsSelectDialog from './PartsSelectDialog.vue';

// 国际化
const { t } = useModuleI18n('workOrder');
const { t: tCommon } = useModuleI18n('common');

// Props
interface Props {
  visible: boolean;
  workOrderId?: string;
  isEdit?: boolean;
  isAdditional?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  workOrderId: '',
  isEdit: false,
  isAdditional: false
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

// 表单引用
const formRef = ref();

// 表单数据
const formData = ref<WorkOrder>({
  workOrderId: '',
  status: 'draft',
  priority: 'normal',
  customerSource: 'walk_in', // 默认为自然到店
  workOrderType: 'repair',
  customerInfo: {
    repairCustomerName: '',
    repairCustomerPhone: ''
  },
  vehicleInfo: {
    licensePlate: '',
    vin: '',
    modelConfigColor: '',
    remark: ''
  },
  laborItems: [],
  partsItems: [],
  costSummary: {
    laborCost: 0,
    partsCost: 0,
    totalAmount: 0
  },
  paymentStatus: 'unpaid',
  createdAt: '',
  updatedAt: '',
  serviceAdvisorId: '',
  serviceAdvisorName: '',
  operationLogs: []
});

// 车牌号查询
const licensePlateQuery = ref('');

// 项目搜索
const projectSearchKeyword = ref('');
const suggestedProjects = ref<ProjectItem[]>([]);
const selectedProject = ref<ProjectItem | null>(null);

// 弹窗控制
const projectSelectVisible = ref(false);
const partsSelectVisible = ref(false);

// 弹窗标题
const dialogTitle = computed(() => {
  if (props.isAdditional) {
    return `${t('addItem')} - ${formData.value.workOrderId}`;
  }
  return props.isEdit ?
    `${t('edit')} - ${formData.value.workOrderId}` :
    t('create');
});

// 表单验证规则
const rules = computed(() => ({
  'customerInfo.repairCustomerName': [
    { required: true, message: t('validation.senderNameRequired'), trigger: 'blur' }
  ],
  'customerInfo.repairCustomerPhone': [
    { required: true, message: t('validation.senderPhoneRequired'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('validation.senderPhoneFormat'), trigger: 'blur' }
  ],
  'vehicleInfo.licensePlate': [
    { required: true, message: t('validation.licensePlateRequired'), trigger: 'blur' }
  ],
  'vehicleInfo.vin': [
    { required: true, message: t('validation.vinCodeRequired'), trigger: 'blur' },
    { len: 17, message: t('validation.vinCodeLength'), trigger: 'blur' }
  ],
  'vehicleInfo.modelConfigColor': [
    { required: true, message: t('validation.modelConfigColorRequired'), trigger: 'blur' }
  ],
  priority: [
    { required: true, message: t('validation.priorityRequired'), trigger: 'change' }
  ],
  customerSource: [
    { required: true, message: t('validation.customerSourceRequired'), trigger: 'change' }
  ],
  workOrderType: [
    { required: true, message: t('validation.workOrderTypeRequired'), trigger: 'change' }
  ]
}));

// 计算属性
const totalLaborHours = computed(() => {
  return formData.value.laborItems.reduce((total: number, item: LaborItem) => total + item.standardHours, 0);
});

const totalLaborAmount = computed(() => {
  return formData.value.laborItems.reduce((total: number, item: LaborItem) => total + item.subtotal, 0);
});

const totalPartsQuantity = computed(() => {
  return formData.value.partsItems.reduce((total: number, item: PartsItem) => total + item.quantity, 0);
});

const totalPartsAmount = computed(() => {
  return formData.value.partsItems.reduce((total: number, item: PartsItem) => total + item.subtotal, 0);
});

const additionalAmount = computed(() => {
  const additionalLabor = formData.value.laborItems
    .filter((item: LaborItem) => item.isAdded)
    .reduce((total: number, item: LaborItem) => total + item.subtotal, 0);
  const additionalParts = formData.value.partsItems
    .filter((item: PartsItem) => item.isAdded)
    .reduce((total: number, item: PartsItem) => total + item.subtotal, 0);
  return additionalLabor + additionalParts;
});

const totalAmount = computed(() => {
  return totalLaborAmount.value + totalPartsAmount.value;
});

const canSaveAndPush = computed(() => {
  return formData.value.laborItems.length > 0 || formData.value.partsItems.length > 0;
});

// 监听客户来源变化
watch(() => formData.value.customerSource, (newSource) => {
  if (newSource === 'walk_in' && formData.value.workOrderType === 'maintenance') {
    formData.value.workOrderType = 'repair';
  }
});

// 车牌号查询车辆信息
const handleQueryVehicleInfo = async () => {
  if (!licensePlateQuery.value.trim()) {
    ElMessage.warning(t('messages.enterLicensePlate'));
    return;
  }

  try {
    // 这里调用车辆信息查询API
    // const vehicleInfo = await queryVehicleInfo(licensePlateQuery.value);
    // 临时模拟数据
    const vehicleInfo = {
      licensePlate: licensePlateQuery.value,
      vin: 'LVVDB11B8CE123456',
      modelConfigColor: '大众速腾 1.4T 舒适版 白色',
      repairCustomerName: '张三',
      repairCustomerPhone: '13812345678'
    };

    // 填充表单数据
    formData.value.vehicleInfo.licensePlate = vehicleInfo.licensePlate;
    formData.value.vehicleInfo.vin = vehicleInfo.vin;
    formData.value.vehicleInfo.modelConfigColor = vehicleInfo.modelConfigColor;
    formData.value.customerInfo.repairCustomerName = vehicleInfo.repairCustomerName;
    formData.value.customerInfo.repairCustomerPhone = vehicleInfo.repairCustomerPhone;

    ElMessage.success(t('messages.vehicleInfoQuerySuccess'));
  } catch (error) {
    console.error('Failed to query vehicle info:', error);
    ElMessage.warning(t('messages.vehicleInfoNotFound'));
  }
};

// 项目搜索建议
const queryProjectSuggestions = (queryString: string, cb: (suggestions: ProjectItem[]) => void) => {
  if (!queryString.trim()) {
    cb([]);
    return;
  }

  // 模拟项目数据
  const projects: ProjectItem[] = [
    { projectId: '1', projectCode: 'P001', projectName: t('projectItems.oilChange'), projectType: 'maintenance', standardHours: 0.5, unitPrice: 80 },
    { projectId: '2', projectCode: 'P002', projectName: t('projectItems.filterChange'), projectType: 'maintenance', standardHours: 0.3, unitPrice: 60 },
    { projectId: '3', projectCode: 'P003', projectName: t('projectItems.brakepadReplacement'), projectType: 'repair', standardHours: 1.0, unitPrice: 120 },
    { projectId: '4', projectCode: 'P004', projectName: t('projectItems.tireReplacement'), projectType: 'repair', standardHours: 0.8, unitPrice: 100 },
    { projectId: '5', projectCode: 'P005', projectName: t('projectItems.engineDiagnosis'), projectType: 'repair', standardHours: 2.0, unitPrice: 150 }
  ];

  const suggestions = projects.filter(project =>
    project.projectName.includes(queryString) || project.projectCode.includes(queryString)
  );

  cb(suggestions);
};

// 选择项目
const handleProjectSelect = (item: ProjectItem) => {
  selectedProject.value = item;
  projectSearchKeyword.value = item.projectName;
};

// 添加选中的项目
const addSelectedProject = () => {
  if (!selectedProject.value) {
    ElMessage.warning(t('messages.selectProjectFirst'));
    return;
  }

  addProjectItem(selectedProject.value);

  // 清空选择
  selectedProject.value = null;
  projectSearchKeyword.value = '';
};

// 项目搜索（保留原有方法）
const handleProjectSearch = async () => {
  if (projectSearchKeyword.value.trim()) {
    try {
      const projects = await getProjectItems({
        keyword: projectSearchKeyword.value,
        workOrderType: formData.value.workOrderType
      });
      suggestedProjects.value = projects;
    } catch (error) {
      console.error('Failed to search projects:', error);
    }
  } else {
    // 获取推荐项目
    try {
      const projects = await getProjectItems({
        workOrderType: formData.value.workOrderType
      });
      suggestedProjects.value = projects.slice(0, 5);
    } catch (error) {
      console.error('Failed to get suggested projects:', error);
    }
  }
};

// 快速添加项目
const addProjectItem = (project: ProjectItem) => {
  // 检查是否已存在
  const exists = formData.value.laborItems.some((item: LaborItem) => item.laborCode === project.projectCode);
  if (exists) {
    ElMessage.warning(t('messages.projectExists'));
    return;
  }

  const laborItem: LaborItem = {
    laborId: project.projectId,
    laborCode: project.projectCode,
    laborName: project.projectName,
    type: project.projectType,
    isClaim: project.projectType === 'claim',
    isAdded: props.isAdditional,
    standardHours: project.standardHours || 1,
    unitPrice: project.unitPrice || 100,
    subtotal: (project.standardHours || 1) * (project.unitPrice || 100)
  };

  formData.value.laborItems.push(laborItem);
  projectSearchKeyword.value = '';
  suggestedProjects.value = [];
};

// 添加工时
const handleAddLabor = () => {
  projectSelectVisible.value = true;
};

// 添加零件
const handleAddParts = () => {
  partsSelectVisible.value = true;
};

// 清空所有工时
const handleClearAllLabor = async () => {
  try {
    await ElMessageBox.confirm(
      t('messages.clearAllConfirm'),
      tCommon('warning'),
      {
        confirmButtonText: tCommon('confirm'),
        cancelButtonText: tCommon('cancel'),
        type: 'warning'
      }
    );
    formData.value.laborItems = [];
  } catch {
    // 用户取消
  }
};

// 清空所有零件
const handleClearAllParts = async () => {
  try {
    await ElMessageBox.confirm(
      t('messages.clearAllConfirm'),
      tCommon('warning'),
      {
        confirmButtonText: tCommon('confirm'),
        cancelButtonText: tCommon('cancel'),
        type: 'warning'
      }
    );
    formData.value.partsItems = [];
  } catch {
    // 用户取消
  }
};

// 删除工时项目
const removeLaborItem = (index: number) => {
  formData.value.laborItems.splice(index, 1);
};

// 删除零件项目
const removePartsItem = (index: number) => {
  formData.value.partsItems.splice(index, 1);
};

// 更新工时小计
const updateLaborSubtotal = (index: number) => {
  const item = formData.value.laborItems[index];
  item.subtotal = item.standardHours * item.unitPrice;
};

// 更新零件小计
const updatePartsSubtotal = (index: number) => {
  const item = formData.value.partsItems[index];
  item.subtotal = item.quantity * item.unitPrice;
};

// 项目选择弹窗确认
const handleProjectDialogConfirm = (projects: ProjectItem[]) => {
  projects.forEach(project => {
    const exists = formData.value.laborItems.some((item: LaborItem) => item.laborCode === project.projectCode);
    if (!exists) {
      const laborItem: LaborItem = {
        laborId: project.projectId,
        laborCode: project.projectCode,
        laborName: project.projectName,
        type: project.projectType,
        isClaim: project.projectType === 'claim',
        isAdded: props.isAdditional,
        standardHours: project.standardHours || 1,
        unitPrice: project.unitPrice || 100,
        subtotal: (project.standardHours || 1) * (project.unitPrice || 100)
      };
      formData.value.laborItems.push(laborItem);
    }
  });
};

// 零件选择确认
const handlePartsSelect = (parts: PartsItem[]) => {
  parts.forEach(part => {
    const exists = formData.value.partsItems.some((item: PartsItem) => item.partId === part.partId);
    if (!exists) {
      const partsItem: PartsItem = {
        partId: part.partId,
        partName: part.partName,
        isClaim: part.isClaim || false,
        isAdded: props.isAdditional,
        availableStock: part.availableStock,
        quantity: 1,
        unitPrice: part.unitPrice,
        subtotal: part.unitPrice
      };
      formData.value.partsItems.push(partsItem);
    }
  });
};

// 保存工单
const handleSave = async () => {
  try {
    await formRef.value?.validate();

    // 校验至少有一个项目
    if (formData.value.laborItems.length === 0 && formData.value.partsItems.length === 0) {
      ElMessage.error(t('validation.atLeastOneItem'));
      return;
    }

    // 校验零件库存
    if (formData.value.partsItems.length > 0) {
      const partsToValidate = formData.value.partsItems.map((item: PartsItem) => ({
        parts_id: item.partId,
        quantity: item.quantity
      }));

      const stockValidation = await validatePartsStock(partsToValidate);
      if (!stockValidation.valid) {
        ElMessage.error(t('messages.stockValidationFailed'));
        return;
      }
    }

    // 更新费用统计
    formData.value.costSummary = {
      laborCost: totalLaborAmount.value,
      partsCost: totalPartsAmount.value,
      totalAmount: totalAmount.value
    };

    if (props.isEdit && props.workOrderId) {
      await updateWorkOrder(props.workOrderId, formData.value);
      ElMessage.success(t('messages.saveSuccess'));
    } else {
      await createWorkOrder(formData.value);
      ElMessage.success(t('messages.saveSuccess'));
    }

    emit('success');
  } catch (error) {
    console.error('Save failed:', error);
  }
};

// 保存并推送客户
const handleSaveAndPush = async () => {
  try {
    await handleSave();

    if (props.workOrderId) {
      await pushWorkOrderToCustomer(props.workOrderId);
      ElMessage.success(t('messages.pushSuccess'));
    }

    updateVisible(false);
  } catch (error) {
    console.error('Save and push failed:', error);
  }
};

// 关闭弹窗
const handleClose = () => {
  updateVisible(false);
};

// 更新显示状态
const updateVisible = (value: boolean) => {
  emit('update:visible', value);
};

// 加载工单详情
const loadWorkOrderDetail = async () => {
  if (props.workOrderId && props.isEdit) {
    try {
      const workOrder = await getWorkOrderDetail(props.workOrderId);
      formData.value = { ...workOrder };
    } catch {
      ElMessage.error(t('messages.loadWorkOrderFailed'));
    }
  }
};

// 重置表单
const resetForm = () => {
  formData.value = {
    workOrderId: '',
    status: 'draft',
    priority: 'normal',
    customerSource: 'walk_in',
    workOrderType: 'repair',
    customerInfo: {
      repairCustomerName: '',
      repairCustomerPhone: ''
    },
    vehicleInfo: {
      licensePlate: '',
      vin: '',
      modelConfigColor: '',
      remark: ''
    },
    laborItems: [],
    partsItems: [],
    costSummary: {
      laborCost: 0,
      partsCost: 0,
      totalAmount: 0
    },
    paymentStatus: 'unpaid',
    createdAt: '',
    updatedAt: '',
    serviceAdvisorId: '',
    serviceAdvisorName: '',
    operationLogs: []
  };
};

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    if (props.isEdit && props.workOrderId) {
      loadWorkOrderDetail();
    } else {
      resetForm();
    }
    // 加载推荐项目
    handleProjectSearch();
  }
});

onMounted(() => {
  handleProjectSearch();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.work-order-form {
  max-height: 600px;
  overflow-y: auto;
}

.dialog-form-modern {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-item {
  display: flex;
  flex-direction: column;

  .project-name {
    font-size: 14px;
    color: #303133;
    margin-bottom: 2px;
  }

  .project-code {
    font-size: 12px;
    color: #909399;
  }
}

.project-suggestions {
  .suggestion-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .project-tags {
    .clickable-tag {
      margin-right: 8px;
      margin-bottom: 8px;
      cursor: pointer;

      &:hover {
        background-color: #409eff;
        color: white;
      }
    }
  }
}

.labor-summary, .parts-summary {
  text-align: right;
  font-size: 14px;
  color: #666;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;

  .cost-label {
    font-size: 14px;
    color: #666;
  }

  .cost-value {
    font-size: 16px;
    font-weight: bold;
    color: #409eff;
  }

  &.total-cost {
    .cost-value {
      font-size: 18px;
      color: #f56c6c;
    }
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;

  .el-button {
    margin-left: 10px;
  }
}

.mt-15 {
  margin-top: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
