<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="1400px"
    :close-on-click-modal="false"
    :modal="false"
    @update:model-value="updateVisible"
  >
    <div class="work-order-detail" v-loading="loading">
      <!-- 工单基础信息区 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('title') }}{{ t('basicInfo') }}</span>
        </template>

        <el-row :gutter="20" class="info-grid">
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('number') }}</div>
              <div class="info-value">{{ workOrderDetail?.workOrderId || '-' }}</div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('registrationTime') }}</div>
              <div class="info-value">{{ formatDate(workOrderDetail?.createdAt) }}</div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('status') }}</div>
              <div class="info-value">
                <el-tag :type="getWorkOrderStatusColor(workOrderDetail?.status)">
                  {{ getWorkOrderStatusText(workOrderDetail?.status) }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('paymentStatus') }}</div>
              <div class="info-value">
                <el-tag :type="getPaymentStatusColor(workOrderDetail?.paymentStatus)">
                  {{ getPaymentStatusText(workOrderDetail?.paymentStatus) }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('isClaim') }}</div>
              <div class="info-value">
                <el-tag :type="isClaimWorkOrder ? 'success' : ''">
                  {{ isClaimWorkOrder ? tCommon('yes') : tCommon('no') }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('isOutsourced') }}</div>
              <div class="info-value">
                <el-tag :type="isOutsourcedWorkOrder ? 'success' : ''">
                  {{ isOutsourcedWorkOrder ? tCommon('yes') : tCommon('no') }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('priority') }}</div>
              <div class="info-value">
                <el-tag :type="workOrderDetail?.priority === 'urgent' ? 'danger' : 'primary'">
                  {{ getWorkOrderPriorityText(workOrderDetail?.priority) }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('customerSource') }}</div>
              <div class="info-value">
                <el-tag>
                  {{ getCustomerSourceText(workOrderDetail?.customerSource) }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="info-grid mt-15">
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('settlementNumber') }}</div>
              <div class="info-value">{{ settlementNumber || '-' }}</div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('hasAdditional') }}</div>
              <div class="info-value">
                <el-tag :type="hasAdditionalItems ? 'warning' : ''">
                  {{ hasAdditionalItems ? tCommon('yes') : tCommon('no') }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('startTime') }}</div>
              <div class="info-value">{{ formatTime(startTime) }}</div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('endTime') }}</div>
              <div class="info-value">{{ formatTime(endTime) }}</div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('workOrder.estimatedHours') }}</div>
              <div class="info-value">{{ workOrderDetail?.estimated_hours || '-' }}{{ t('workOrder.hours') }}</div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('workOrder.actualHours') }}</div>
              <div class="info-value">{{ workOrderDetail?.actual_hours || '-' }}{{ t('workOrder.hours') }}</div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('qcStatus') }}</div>
              <div class="info-value">
                <el-tag v-if="qcStatus">
                  {{ getQCStatusText(qcStatus) }}
                </el-tag>
                <span v-else>-</span>
              </div>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="info-item">
              <div class="info-label">{{ t('qcNumber') }}</div>
              <div class="info-value">{{ qcNumber || '-' }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户和车辆信息区 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="mb-20">
            <template #header>
              <span>{{ t('customerInfo') }}</span>
            </template>

            <div class="info-item" v-if="workOrderDetail?.customerInfo?.appointmentCustomerName">
              <div class="info-label">{{ t('appointmentCustomer') }}：</div>
              <div class="info-value">{{ workOrderDetail.customerInfo.appointmentCustomerName }}</div>
            </div>
            <div class="info-item" v-if="workOrderDetail?.customerInfo?.appointmentCustomerPhone">
              <div class="info-label">{{ t('appointmentPhone') }}：</div>
              <div class="info-value">{{ workOrderDetail.customerInfo.appointmentCustomerPhone }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ t('senderName') }}：</div>
              <div class="info-value">{{ workOrderDetail?.customerInfo?.repairCustomerName || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ t('senderPhone') }}：</div>
              <div class="info-value">{{ workOrderDetail?.customerInfo?.repairCustomerPhone || '-' }}</div>
            </div>
            <div class="info-item" v-if="confirmationMethod">
              <div class="info-label">{{ t('confirmationMethod') }}：</div>
              <div class="info-value">{{ getConfirmationMethodText(confirmationMethod) }}</div>
            </div>
            <div class="info-item" v-if="confirmationTime">
              <div class="info-label">{{ t('confirmationTime') }}：</div>
              <div class="info-value">{{ formatDateTime(confirmationTime) }}</div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="mb-20">
            <template #header>
              <span>{{ t('vehicleInfo') }}</span>
            </template>

            <div class="info-item">
              <div class="info-label">{{ t('licensePlate') }}：</div>
              <div class="info-value">{{ workOrderDetail?.vehicleInfo?.licensePlate || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ t('vinCode') }}：</div>
              <div class="info-value">{{ workOrderDetail?.vehicleInfo?.vin || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ t('modelConfigColor') }}：</div>
              <div class="info-value">{{ workOrderDetail?.vehicleInfo?.modelConfigColor || '-' }}</div>
            </div>
            <div class="info-item" v-if="warrantyStatus">
              <div class="info-label">{{ t('warrantyStatus') }}：</div>
              <div class="info-value">{{ warrantyStatus }}</div>
            </div>
            <div class="info-item" v-if="warrantyExpiry">
              <div class="info-label">{{ t('warrantyExpiry') }}：</div>
              <div class="info-value">{{ warrantyExpiry }}</div>
            </div>
            <div class="info-item" v-if="workOrderDetail?.vehicleInfo?.remark">
              <div class="info-label">{{ t('remarks') }}：</div>
              <div class="info-value">{{ workOrderDetail.vehicleInfo.remark }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 工时详情区 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('laborDetails') }}</span>
        </template>

        <el-table :data="workOrderDetail?.laborItems || []" style="width: 100%">
          <el-table-column :label="t('itemType')" width="80" align="center">
            <template #default="{ row }">
              <el-tag
                :type="row.type === 'maintenance' ? 'success' : (row.type === 'repair' ? 'warning' : 'primary')"
              >
                {{ t(`types.${row.type}`) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="laborCode" :label="t('itemCode')" width="120" />
          <el-table-column prop="laborName" :label="t('itemName')" width="200" />
          <el-table-column :label="t('isClaim')" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.is_claim ? 'success' : ''">
                {{ row.is_claim ? t('common.yes') : t('common.no') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('hasAdditional')" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.is_additional ? 'warning' : ''">
                {{ row.is_additional ? t('common.yes') : t('common.no') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('standardHours')" width="100" align="right">
            <template #default="{ row }">
                              {{ row.standard_hours }}{{ t('workOrder.hours') }}
            </template>
          </el-table-column>
          <el-table-column :label="t('laborRate')" width="100" align="right">
            <template #default="{ row }">
                              ¥{{ row.labor_rate }}{{ t('workOrder.hourly') }}
            </template>
          </el-table-column>
          <el-table-column :label="t('subtotal')" width="100" align="right">
            <template #default="{ row }">
              ¥{{ row.subtotal.toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>

        <div class="summary-row">
          <span>{{ t('workOrder.statistics') }}：{{ t('workOrder.laborTotal') }}：{{ totalLaborHours.toFixed(1) }}{{ t('workOrder.hours') }}</span>
          <span class="ml-20">{{ t('workOrder.laborTotalAmount') }}：¥{{ totalLaborAmount.toFixed(2) }}</span>
        </div>
      </el-card>

      <!-- 零件详情区 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('partsDetails') }}</span>
        </template>

        <el-table :data="workOrderDetail?.partsItems || []" style="width: 100%">
          <el-table-column prop="partName" :label="t('partsName')" width="150" />
          <el-table-column :label="t('isClaim')" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.is_claim ? 'success' : ''">
                {{ row.is_claim ? t('common.yes') : t('common.no') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('hasAdditional')" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.is_additional ? 'warning' : ''">
                {{ row.is_additional ? t('common.yes') : t('common.no') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('availableStock')" width="100" align="right">
            <template #default="{ row }">
              {{ row.availableStock }}
            </template>
          </el-table-column>
          <el-table-column :label="t('quantity')" width="100" align="right">
            <template #default="{ row }">
              {{ row.quantity }}
            </template>
          </el-table-column>
          <el-table-column :label="t('unitPrice')" width="100" align="right">
            <template #default="{ row }">
              ¥{{ row.unitPrice }}
            </template>
          </el-table-column>
          <el-table-column :label="t('subtotal')" width="100" align="right">
            <template #default="{ row }">
              ¥{{ row.subtotal.toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>

        <div class="summary-row">
          <span>{{ t('workOrder.statistics') }}：{{ t('workOrder.partsTotal') }}：{{ totalPartsQuantity }}{{ t('workOrder.unit') }}</span>
          <span class="ml-20">{{ t('workOrder.partsTotalAmount') }}：¥{{ totalPartsAmount.toFixed(2) }}</span>
        </div>
      </el-card>

      <!-- 费用统计区 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('costSummary') }}</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="cost-summary-item">
              <div class="cost-label">{{ t('laborAmount') }}</div>
              <div class="cost-value">¥{{ workOrderDetail?.costSummary?.laborCost?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-summary-item">
              <div class="cost-label">{{ t('partsAmount') }}</div>
              <div class="cost-value">¥{{ workOrderDetail?.costSummary?.partsCost?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-summary-item">
              <div class="cost-label">{{ t('additionalAmount') }}</div>
              <div class="cost-value">¥{{ additionalCost?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-summary-item total">
              <div class="cost-label">{{ t('workOrderTotal') }}</div>
              <div class="cost-value">¥{{ workOrderDetail?.costSummary?.totalAmount?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-15">
          <el-col :span="6">
            <div class="cost-summary-item">
              <div class="cost-label">{{ t('depositAmount') }}</div>
              <div class="cost-value">¥{{ depositAmount?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-summary-item">
              <div class="cost-label">{{ t('balanceAmount') }}</div>
              <div class="cost-value">¥{{ balanceAmount?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-summary-item">
              <div class="cost-label">{{ t('additionalAmount') }}</div>
              <div class="cost-value">¥{{ additionalCost?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-summary-item">
              <div class="cost-label">{{ t('paymentStatus') }}</div>
              <div class="cost-value">
                <el-tag :type="getPaymentStatusColor(workOrderDetail?.paymentStatus)">
                  {{ getPaymentStatusText(workOrderDetail?.paymentStatus) }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 操作日志区 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('operationLogs') }}</span>
        </template>

        <el-table :data="operationLogs" style="width: 100%">
          <el-table-column prop="action" :label="t('operationType')" width="120" />
          <el-table-column prop="operator" :label="t('operator')" width="100" />
          <el-table-column :label="t('operationTime')" width="140" align="center">
            <template #default="{ row }">
              {{ formatDateTime(row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column prop="details" :label="t('operationDescription')" />
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="updateVisible(false)">{{ tCommon('close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrder, OperationLog } from '@/types/workOrder';
import { getWorkOrderDetail } from '@/api/modules/workOrder';

// 临时的操作日志获取函数
const getWorkOrderLogs = async (workOrderId: string): Promise<OperationLog[]> => {
  // 这里返回模拟数据，实际应该从API获取
  return [
    {
      id: '1',
      workOrderId: workOrderId,
      action: '创建工单',
      operator: '张三',
      timestamp: new Date().toISOString(),
      details: '工单创建成功'
    },
    {
      id: '2',
      workOrderId: workOrderId,
      action: '分配技师',
      operator: '李四',
      timestamp: new Date().toISOString(),
      details: '工单已分配给技师王五'
    }
  ];
};

// 国际化
const { t } = useModuleI18n('workOrder');
const { t: tCommon } = useModuleI18n('common');

// Props
interface Props {
  visible: boolean;
  workOrderId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  workOrderId: ''
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 数据
const loading = ref(false);
const workOrderDetail = ref<WorkOrder>();
const operationLogs = ref<OperationLog[]>([]);

// 弹窗标题
const dialogTitle = computed(() => {
  return `${t('detail')} - ${workOrderDetail.value?.workOrderId || ''}`;
});

// 计算属性 - 扩展字段
const isClaimWorkOrder = computed(() => {
  return workOrderDetail.value?.workOrderType === 'claim';
});

const isOutsourcedWorkOrder = computed(() => {
  // 根据业务逻辑判断是否委外，这里暂时返回false
  return false;
});

const hasAdditionalItems = computed(() => {
  const hasAdditionalLabor = workOrderDetail.value?.laborItems?.some(item => item.isAdded) || false;
  const hasAdditionalParts = workOrderDetail.value?.partsItems?.some(item => item.isAdded) || false;
  return hasAdditionalLabor || hasAdditionalParts;
});

const settlementNumber = computed(() => {
  // 结算单号，需要从其他接口获取或从工单扩展信息中获取
  return '';
});

const startTime = computed(() => {
  // 开工时间，需要从操作日志中获取或从工单扩展信息中获取
  return '';
});

const endTime = computed(() => {
  // 完工时间，需要从操作日志中获取或从工单扩展信息中获取
  return '';
});

const estimatedHours = computed(() => {
  // 预计工时，需要从工单扩展信息中获取
  return 0;
});

const actualHours = computed(() => {
  // 实际工时，需要从工单扩展信息中获取
  return 0;
});

const qcStatus = computed(() => {
  // 质检状态，需要从工单扩展信息中获取
  return '';
});

const qcNumber = computed(() => {
  // 质检单号，需要从工单扩展信息中获取
  return '';
});

const confirmationMethod = computed(() => {
  // 确认方式，需要从客户信息扩展中获取
  return '';
});

const confirmationTime = computed(() => {
  // 确认时间，需要从客户信息扩展中获取
  return '';
});

const warrantyStatus = computed(() => {
  // 保修状态，需要从车辆信息扩展中获取
  return '';
});

const warrantyExpiry = computed(() => {
  // 保修到期时间，需要从车辆信息扩展中获取
  return '';
});

const additionalCost = computed(() => {
  const additionalLabor = workOrderDetail.value?.laborItems
    ?.filter(item => item.isAdded)
    ?.reduce((total, item) => total + item.subtotal, 0) || 0;
  const additionalParts = workOrderDetail.value?.partsItems
    ?.filter(item => item.isAdded)
    ?.reduce((total, item) => total + item.subtotal, 0) || 0;
  return additionalLabor + additionalParts;
});

const depositAmount = computed(() => {
  // 定金金额，需要从费用统计扩展中获取
  return 0;
});

const balanceAmount = computed(() => {
  // 余额，需要从费用统计扩展中获取
  return 0;
});

// 计算属性
const totalLaborHours = computed(() => {
  return workOrderDetail.value?.laborItems?.reduce((total: number, item) => total + item.standardHours, 0) || 0;
});

const totalLaborAmount = computed(() => {
  return workOrderDetail.value?.laborItems?.reduce((total: number, item) => total + item.subtotal, 0) || 0;
});

const totalPartsQuantity = computed(() => {
  return workOrderDetail.value?.partsItems?.reduce((total: number, item) => total + item.quantity, 0) || 0;
});

const totalPartsAmount = computed(() => {
  return workOrderDetail.value?.partsItems?.reduce((total: number, item) => total + item.subtotal, 0) || 0;
});

// 加载工单详情
const loadWorkOrderDetail = async () => {
  if (!props.workOrderId) return;

  loading.value = true;
  try {
    const [detail, logs] = await Promise.all([
      getWorkOrderDetail(props.workOrderId),
      getWorkOrderLogs(props.workOrderId)
    ]);

    workOrderDetail.value = detail;
    operationLogs.value = logs;
  } catch {
    ElMessage.error(t('messages.loadWorkOrderFailed'));
  } finally {
    loading.value = false;
  }
};

// 更新显示状态
const updateVisible = (value: boolean) => {
  emit('update:visible', value);
};

// 格式化函数
const formatDate = (dateTime?: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  });
};

const formatTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  if (targetDate.getTime() === today.getTime()) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

// 状态颜色和文本
const getWorkOrderStatusColor = (status?: string) => {
  const colorMap: Record<string, string> = {
    draft: 'info',
    pending_confirmation: 'warning',
    confirmed: 'primary',
    pending_assignment: '',
    pending_start: '',
    in_progress: 'warning',
    pending_qc: '',
    pending_settlement: '',
    rework_required: 'danger',
    completed: 'success',
    cancelled: 'info',
    additional_pending: 'warning',
    waiting_approval: 'primary',
    waiting_parts: 'warning'
  };
  return colorMap[status || ''] || '';
};

const getWorkOrderStatusText = (status?: string) => {
  if (!status) return '-';
  return t(`statuses.${status}`);
};

const getPaymentStatusColor = (status?: string) => {
  const colorMap: Record<string, string> = {
    unpaid: 'danger',
    partially_paid: 'warning',
    paid: 'success',
    refunding: 'warning',
    refunded: 'info'
  };
  return colorMap[status || ''] || '';
};

const getPaymentStatusText = (status?: string) => {
  if (!status) return '-';
  return t(`paymentStatuses.${status}`);
};

const getQCStatusText = (status?: string) => {
  if (!status) return '-';
  return t(`qcStatuses.${status}`);
};

const getWorkOrderPriorityText = (priority?: string) => {
  if (!priority) return '-';
  return t(`priorities.${priority}`);
};

const getCustomerSourceText = (source?: string) => {
  if (!source) return '-';
  return t(`customerSources.${source}`);
};

const getConfirmationMethodText = (method?: string) => {
  if (!method) return '-';
  return t(`confirmationMethods.${method}`);
};

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.workOrderId) {
    loadWorkOrderDetail();
  }
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.work-order-detail {
  max-height: 600px;
  overflow-y: auto;
}

.info-grid {
  .info-item {
    padding: 8px 0;

    .info-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .info-value {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
}

.info-item {
  display: flex;
  margin-bottom: 8px;

  .info-label {
    min-width: 80px;
    font-size: 14px;
    color: #666;
  }

  .info-value {
    flex: 1;
    font-size: 14px;
    color: #333;
  }
}

.summary-row {
  margin-top: 15px;
  text-align: right;
  font-size: 14px;
  color: #666;
}

.cost-summary-item {
  text-align: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  .cost-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .cost-value {
    font-size: 18px;
    font-weight: bold;
    color: #409eff;
  }

  &.total {
    border-color: #f56c6c;
    background-color: #fef0f0;

    .cost-value {
      color: #f56c6c;
    }
  }
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-15 {
  margin-top: 15px;
}

.ml-20 {
  margin-left: 20px;
}
</style>
