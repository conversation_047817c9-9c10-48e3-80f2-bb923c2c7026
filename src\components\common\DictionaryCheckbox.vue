<template>
  <el-checkbox-group
    :model-value="modelValue"
    @update:model-value="handleChange"
    :disabled="disabled"
    :size="size"
    v-loading="dictionaryLoading"
    v-bind="$attrs"
  >
    <el-checkbox
      v-for="option in options"
      :key="option.code"
      :value="option.code"
      :disabled="isOptionDisabled(option)"
    >
      {{ option.name }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<script setup lang="ts">
import { useDictionary } from '@/composables/useDictionary';
import type { DictionaryType, DictionaryOption } from '@/constants/dictionary';

interface Props {
  modelValue?: string[] | number[] | boolean[];
  dictionaryType: DictionaryType;
  disabled?: boolean;
  size?: 'large' | 'default' | 'small';
  disabledOptions?: string[];
}

interface Emits {
  (e: 'update:modelValue', value: string[] | number[] | boolean[]): void;
  (e: 'change', value: string[] | number[] | boolean[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  size: 'default',
  disabledOptions: () => [],
});

const emit = defineEmits<Emits>();

// 使用字典数据
const { 
  options, 
  loading: dictionaryLoading 
} = useDictionary(props.dictionaryType);

// 处理值变化
const handleChange = (value: string[] | number[] | boolean[]) => {
  emit('update:modelValue', value);
  emit('change', value);
};

// 检查选项是否禁用
const isOptionDisabled = (option: DictionaryOption): boolean => {
  return props.disabledOptions.includes(option.code);
};
</script>

<script lang="ts">
export default {
  name: 'DictionaryCheckbox',
  inheritAttrs: false,
};
</script>
