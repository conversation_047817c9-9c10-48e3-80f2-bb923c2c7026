<template>
  <el-dropdown @command="handleLanguageChange" class="language-selector" :loading="switchingLanguage">
    <span class="el-dropdown-link">
      <span class="language-icon">🌐</span>
      {{ currentLanguageName }}
      <el-icon class="el-icon--right" v-if="!switchingLanguage"><arrow-down /></el-icon>
      <el-icon class="el-icon--right rotating" v-else><Loading /></el-icon>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="lang in availableLanguages"
          :key="lang.code"
          :command="lang.code"
          :class="{ 'is-active': lang.code === currentLanguage }"
          :disabled="switchingLanguage"
        >
          {{ lang.name }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { ArrowDown, Loading } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import {
  switchLanguage,
  getCurrentLanguage,
  getLanguageDisplayName,
  getAvailableLanguages
} from '@/plugins/i18n';

// 切换语言的加载状态
const switchingLanguage = ref(false);

// 当前语言
const currentLanguage = computed(() => getCurrentLanguage());

// 当前语言显示名称
const currentLanguageName = computed(() => getLanguageDisplayName(currentLanguage.value));

// 可用语言列表
const availableLanguages = getAvailableLanguages();

// 处理语言切换
const handleLanguageChange = async (langCode: string) => {
  if (langCode !== currentLanguage.value && !switchingLanguage.value) {
    switchingLanguage.value = true;

    try {
      await switchLanguage(langCode as 'zh' | 'en');
      ElMessage.success('语言切换成功');
    } catch (error) {
      console.error('语言切换失败:', error);
      ElMessage.error('语言切换失败，请重试');
    } finally {
      switchingLanguage.value = false;
    }
  }
};
</script>

<style scoped lang="scss">
.language-selector {
  .el-dropdown-link {
    cursor: pointer;
    color: var(--el-text-color-regular);
    display: flex;
    align-items: center;
    gap: 4px;
    transition: color 0.3s;

    &:hover {
      color: var(--el-color-primary);
    }

    .language-icon {
      font-size: 16px;
      display: flex;
      align-items: center;
    }

    .rotating {
      animation: rotate 1s linear infinite;
    }
  }

  :deep(.el-dropdown-menu__item) {
    &.is-active {
      color: var(--el-color-primary);
      font-weight: 500;
    }

    &.is-disabled {
      color: var(--el-text-color-placeholder);
      cursor: not-allowed;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
