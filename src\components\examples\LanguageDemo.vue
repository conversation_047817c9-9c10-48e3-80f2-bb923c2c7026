<template>
  <div class="language-demo">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card header="🌐 语言切换功能演示">
          <div class="demo-section">
            <h3>1. 语言切换器（含菜单重载）</h3>
            <div class="demo-content">
              <LanguageSelector />
              <p class="demo-note">
                当前语言: <strong>{{ currentLanguage }}</strong> ({{ currentLanguageName }})
              </p>
              <p class="demo-note">
                <el-icon><InfoFilled /></el-icon>
                切换语言时会自动重新加载菜单数据
              </p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card header="📋 菜单国际化演示">
          <div class="demo-section">
            <h4>模拟菜单项</h4>
            <div class="menu-demo">
              <div
                v-for="menuItem in demoMenuItems"
                :key="menuItem.code"
                class="menu-item-demo"
              >
                <span>{{ getMenuTranslation(menuItem.code) }}</span>
              </div>
            </div>
            <p class="demo-note">
              <el-icon><Refresh /></el-icon>
              这些菜单项会随语言切换实时更新
            </p>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card header="🔄 通用翻译演示">
          <div class="demo-section">
            <h4>通用模块翻译</h4>
            <div class="translation-demo">
              <div class="translation-item">
                <span class="translation-key">confirm:</span>
                <span class="translation-value">{{ tc('confirm') }}</span>
              </div>
              <div class="translation-item">
                <span class="translation-key">cancel:</span>
                <span class="translation-value">{{ tc('cancel') }}</span>
              </div>
              <div class="translation-item">
                <span class="translation-key">save:</span>
                <span class="translation-value">{{ tc('save') }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="🌍 HTTP请求语言传递演示">
          <div class="demo-section">
            <div class="request-demo">
              <el-button
                type="primary"
                @click="testHttpRequest"
                :loading="requestLoading"
              >
                发起测试请求
              </el-button>

              <div v-if="lastRequest" class="request-result">
                <h4>最后请求信息:</h4>
                <div class="request-info">
                  <p><strong>请求时间:</strong> {{ lastRequest.timestamp }}</p>
                  <p><strong>当前语言:</strong> {{ lastRequest.language }}</p>
                  <p><strong>传递给后端:</strong> {{ lastRequest.backendLang }}</p>
                  <p><strong>请求参数:</strong> <code>lang={{ lastRequest.backendLang }}</code></p>
                  <p><strong>请求头:</strong> <code>Accept-Language: {{ lastRequest.backendLang }}</code></p>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="💡 功能特性说明">
          <div class="usage-guide">
            <h4>🔄 自动菜单重载:</h4>
            <el-alert
              title="语言切换时会自动重新从后端获取菜单数据，确保菜单项显示正确的语言内容"
              type="info"
              :closable="false"
              style="margin-bottom: 16px;"
            />

            <h4>📡 异步语言切换:</h4>
            <pre class="code-example">
// 异步语言切换，支持菜单重载
import { switchLanguage } from '@/plugins/i18n';

try {
  await switchLanguage('zh'); // 切换到中文并重载菜单
  console.log('语言切换完成');
} catch (error) {
  console.error('语言切换失败:', error);
}
            </pre>

            <h4>🌐 模块化国际化使用:</h4>
            <pre class="code-example">
// 在Vue组件中使用
import { useModuleI18n } from '@/composables/useModuleI18n';

const { t, tc } = useModuleI18n('menu');
// t('home') - 获取menu模块的翻译
// tc('confirm') - 获取common模块的翻译
            </pre>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getCurrentLanguage, getLanguageDisplayName } from '@/plugins/i18n';
import { http } from '@/utils/http';
import { InfoFilled, Refresh } from '@element-plus/icons-vue';
import LanguageSelector from '@/components/common/LanguageSelector.vue';

// 使用模块化国际化
const { t: tMenu } = useModuleI18n('menu');
const { tc } = useModuleI18n('common');

// 当前语言信息
const currentLanguage = computed(() => getCurrentLanguage());
const currentLanguageName = computed(() => getLanguageDisplayName(currentLanguage.value));

// 演示菜单项
const demoMenuItems = [
  { code: 'home' },
  { code: 'checkinList' },
  { code: 'salesOrder' },
  { code: 'customerDetail' }
];

// 获取菜单翻译
const getMenuTranslation = (menuCode: string) => {
  try {
    return tMenu(menuCode);
  } catch {
    return menuCode;
  }
};

// HTTP请求测试
const requestLoading = ref(false);
const lastRequest = ref<Record<string, unknown> | null>(null);

const testHttpRequest = async () => {
  requestLoading.value = true;

  try {
    // 模拟API请求
    await http.get('/api/test', { demo: true });

    // 记录请求信息
    lastRequest.value = {
      timestamp: new Date().toLocaleString(),
      language: currentLanguage.value,
      backendLang: currentLanguage.value === 'zh' ? 'zh_CN' : 'en_US'
    };
  } catch (error) {
    console.log('请求失败(预期行为):', error);

    // 即使请求失败，也记录请求信息
    lastRequest.value = {
      timestamp: new Date().toLocaleString(),
      language: currentLanguage.value,
      backendLang: currentLanguage.value === 'zh' ? 'zh_CN' : 'en_US'
    };
  } finally {
    requestLoading.value = false;
  }
};
</script>

<style scoped lang="scss">
.language-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  .demo-content {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .demo-note {
      margin: 0;
      color: var(--el-text-color-regular);
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.menu-demo {
  .menu-item-demo {
    padding: 8px 12px;
    margin: 4px 0;
    background: var(--el-fill-color-light);
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background: var(--el-fill-color);
    }
  }
}

.translation-demo {
  .translation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .translation-key {
      font-weight: 500;
      color: var(--el-text-color-regular);
    }

    .translation-value {
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }
}

.request-demo {
  .request-result {
    margin-top: 20px;
    padding: 16px;
    background: var(--el-fill-color-light);
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
    }

    .request-info {
      p {
        margin: 8px 0;
        color: var(--el-text-color-regular);

        strong {
          color: var(--el-text-color-primary);
        }

        code {
          background: var(--el-fill-color);
          padding: 2px 6px;
          border-radius: 3px;
          font-family: 'Courier New', monospace;
          color: var(--el-color-primary);
        }
      }
    }
  }
}

.usage-guide {
  h4 {
    margin: 16px 0 8px 0;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .code-example {
    background: var(--el-fill-color-dark);
    padding: 16px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    color: var(--el-text-color-primary);
    overflow-x: auto;
    margin: 8px 0 16px 0;
  }
}
</style>
