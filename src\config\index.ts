// 应用配置
export const config = {
  // 是否使用Mock API (开发环境默认开启)
  // useMockApi: import.meta.env.VITE_APP_USE_MOCK_API === 'true' || import.meta.env.DEV,
  useMockApi: import.meta.env.VITE_APP_USE_MOCK_API === 'true',
  // API基础地址
  apiBaseUrl: import.meta.env.VITE_APP_BASE_API || 'http://localhost:8080/api/v1',

  // 应用标题
  appTitle: import.meta.env.VITE_APP_TITLE || 'DMS 经销商管理系统',

  // 请求超时时间
  timeout: 30000,

  // Token键名
  tokenKey: 'token',
  refreshTokenKey: 'refreshToken',

  // 语言配置
  defaultLanguage: 'zh',
  availableLanguages: ['zh', 'en']
}

export default config
