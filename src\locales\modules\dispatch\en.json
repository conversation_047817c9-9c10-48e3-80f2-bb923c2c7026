{"pageTitle": "Dispatch Management", "workOrderNo": "Work Order No.", "workOrderNoPlaceholder": "Enter Work Order No.", "priority": "Priority", "priorityUrgent": "<PERSON><PERSON>", "priorityNormal": "Normal", "workOrderType": "Work Order Type", "typeRepair": "Repair", "typeMaintenance": "Maintenance", "typeInsurance": "Insurance", "assignmentStatus": "Assignment Status", "workOrderStatus": "Work Order Status", "workOrderCreationTime": "Work Order Creation Time", "startTime": "Start Time", "endTime": "End Time", "customerSource": "Customer Source", "repairmanName": "Repairman Name", "repairmanNamePlaceholder": "Enter repairman name", "licensePlateNumber": "License Plate Number", "licensePlateNumberPlaceholder": "Enter license plate number", "serviceAdvisor": "Service Advisor", "technician": "Technician", "months": " months", "km": " km", "vehicleModel": "Vehicle Model", "configuration": "Configuration", "color": "Color", "vehicleAge": "Vehicle Age", "mileage": "Mileage", "repairmanPhone": "Repairman Phone", "estimatedWorkHours": "Est. Work Hours", "estimatedStartTime": "Est. Start Time", "estimatedFinishTime": "Est. Finish Time", "actualStartTime": "Actual Start Time", "actualFinishTime": "Actual Finish Time", "assign": "Assign", "reassign": "Reassign", "startWork": "Start Work", "pause": "Pause", "resume": "Resume", "completeWork": "Complete Work", "detailDialogTitle": "Work Order Details", "detailDialogContent": "Here are the details of the work order...", "assignDialogTitle": "Assign Work Order", "assignedTechnician": "Assigned Technician", "assignmentNotes": "Assignment Notes", "assignmentNotesPlaceholder": "Enter assignment notes", "reassignDialogTitle": "Reassign Work Order", "originalTechnician": "Original Technician", "newTechnician": "New Technician", "reassignmentReason": "Reassignment Reason", "reassignmentReasonPlaceholder": "Enter reassignment reason", "assignSuccess": "Assignment successful", "reassignSuccess": "Reassignment successful", "startWorkSuccess": "Work order started: ", "resumeWorkSuccess": "Work order resumed: ", "pauseWorkSuccess": "Work order paused: ", "completeWorkSuccess": "Work order completed: ", "status": {"pendingAssignment": "Pending Assignment", "assigned": "Assigned", "pendingStart": "Pending Start", "inProgress": "In Progress", "pendingQualityInspection": "Pending QC", "pendingRework": "Pending Rework", "completed": "Completed", "additionalItemsPendingConfirmation": "Add. Items Pending", "waitingForParts": "Waiting for Parts", "pending": "Pending", "pendingConfirm": "Pending Confirmation", "confirmed": "Confirmed"}, "source": {"appointment": "Appointment", "walkIn": "Walk-in", "undefined": "undefined"}}