{"wholeVehicleCollection": {"title": "Whole Vehicle Collection Management", "search": {"orderNumber": "Order Number", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "dateRange": "Order Date", "canInvoice": "Can Invoice", "searchButton": "Search", "resetButton": "Reset", "exportButton": "Export"}, "table": {"orderNumber": "Order Number", "buyerInfo": "Buyer Information", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "dealerInfo": "Dealer Information", "dealerStoreName": "Dealer Store", "salesConsultantName": "Sales Consultant", "vehicleInfo": "Vehicle Information", "vin": "VIN", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "orderCreateTime": "Order Time", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "priceInfo": "Price Information", "vehicleSalesPrice": "Vehicle Sales Price", "insuranceAmount": "Insurance Amount", "otrAmount": "OTR Amount", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "unpaidAmount": "Unpaid Amount", "loanAmount": "<PERSON><PERSON>", "invoiceInfo": "Invoice Information", "canInvoice": "Can Invoice", "invoiceTime": "Invoice Time", "invoiceNumber": "Invoice Number", "actions": "Actions", "viewDetail": "View Detail", "paymentOperation": "Payment Operation"}, "status": {"orderStatus": {"submitted": "Submitted", "cancelReviewing": "Cancel Reviewing", "cancelApproved": "Cancel Approved", "cancelled": "Cancelled", "confirmed": "Confirmed", "pendingReview": "Pending Review", "reviewed": "Reviewed", "pendingDelivery": "Pending Delivery", "delivered": "Delivered"}, "paymentStatus": {"pendingDeposit": "Pending Deposit", "depositPaid": "<PERSON><PERSON><PERSON><PERSON>", "refunding": "Refunding", "refunded": "Refunded", "pendingFinalPayment": "Pending Final Payment", "finalPaymentPaid": "Final Payment Paid"}, "canInvoice": {"yes": "Yes", "no": "No"}}, "pagination": {"total": "Total {total} items", "pageSize": "Items per page", "items": "items"}, "messages": {"loadSuccess": "Data loaded successfully", "loadError": "Failed to load data", "exportSuccess": "Export successful", "exportError": "Export failed", "noData": "No data available", "confirmDelete": "Are you sure to delete this record?", "deleteSuccess": "Delete successful", "deleteError": "Delete failed"}}, "orderDetail": {"title": "Order Detail", "tabs": {"basicInfo": "Basic Information", "paymentRecords": "Payment Records"}, "basicInfo": {"orderInfo": "Order Information", "orderId": "Order ID", "orderNumber": "Order Number", "orderCreateTime": "Order Time", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "paymentMethod": "Payment Method", "loanAmount": "<PERSON><PERSON>", "canInvoice": "Can Invoice", "invoiceTime": "Invoice Time", "invoiceNumber": "Invoice Number", "ordererInfo": "Orderer Information", "ordererName": "Orderer Name", "ordererPhone": "Orderer Phone", "buyerInfo": "Buyer Information", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "buyerIdType": "ID Type", "buyerIdNumber": "ID Number", "buyerEmail": "Email", "buyerAddress": "Address Information", "buyerState": "State", "buyerCity": "City", "buyerPostcode": "Postcode", "buyerFullAddress": "Full Address", "dealerInfo": "Dealer Information", "dealerRegion": "Dealer Region", "dealerCity": "Dealer City", "dealerStoreName": "Dealer Store", "salesConsultantInfo": "Sales Consultant Information", "salesConsultantName": "Sales Consultant Name", "salesConsultantPhone": "Sales Consultant Phone", "salesConsultantEmail": "Sales Consultant Email", "vehicleInfo": "Vehicle Information", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "vin": "VIN", "warehouseName": "Warehouse Name", "productionDate": "Production Date", "options": "Options", "optionsTotalPrice": "Options Total Price", "priceInfo": "Price Information", "salesSubtotal": "Sales Subtotal", "salesSubtotalIncludeGASA": "Sales Subtotal (Include GASA)", "salesSubtotalIncludeGASADesc": "Including Government Agency Service Fee", "consumptionTax": "Consumption Tax", "consumptionTaxDesc": "Government consumption tax", "salesTax": "Sales Tax", "salesTaxDesc": "Government sales tax", "numberPlatesFee": "Number Plates Fee", "numberPlatesFeeDesc": "Vehicle registration fee", "optionsPrice": "Options Price", "optionsPriceDesc": "Total price of all optional accessories", "vehicleSalesPriceSubtotal": "Vehicle Sales Price Subtotal", "vehicleSalesPriceSubtotalDesc": "Vehicle base price plus all taxes and options", "accessoriesTotalAmount": "Accessories Total Amount", "vehicleSalesPrice": "Vehicle Sales Price", "insuranceAmount": "Insurance Amount", "insuranceAmountDesc": "Vehicle insurance cost", "otrAmount": "OTR Amount", "otrAmountDesc": "On-the-road cost", "orderDiscountAmount": "Order Discount Amount", "orderDiscountAmountDesc": "Total order discount amount", "orderTotalAmount": "Order Total Amount", "orderTotalAmountDesc": "Final order total amount", "orderPaidAmount": "<PERSON><PERSON>", "orderPaidAmountDesc": "Amount paid by customer", "orderUnpaidAmount": "Unpaid Amount", "orderUnpaidAmountDesc": "Amount not yet paid by customer", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "unpaidAmount": "Unpaid Amount"}, "paymentRecords": {"title": "Payment Records", "addRecord": "Add Record", "table": {"paymentRecordNumber": "Record Number", "businessType": "Business Type", "transactionNumber": "Transaction Number", "channel": "Channel", "amount": "Amount", "paymentType": "Payment Type", "arrivalTime": "Arrival Time", "remark": "Remark", "dataSource": "Data Source", "createTime": "Create Time", "creator": "Creator", "actions": "Actions", "delete": "Delete"}, "businessType": {"payment": "Payment", "refund": "Refund"}, "addForm": {"title": "Add Payment Record", "businessType": "Business Type", "transactionNumber": "Transaction Number", "channel": "Channel", "amount": "Amount", "paymentType": "Payment Type", "arrivalTime": "Arrival Time", "remark": "Remark", "submitButton": "Submit", "cancelButton": "Cancel"}, "messages": {"addSuccess": "Add successful", "addError": "Add failed", "deleteSuccess": "Delete successful", "deleteError": "Delete failed", "confirmDelete": "Are you sure to delete this record?"}}}, "paymentOperation": {"title": "Payment Operation", "orderInfo": "Order Information", "orderNumber": "Order Number", "buyerName": "Buyer Name", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "unpaidAmount": "Unpaid Amount", "operationForm": "Operation Form", "businessType": "Business Type", "transactionNumber": "Transaction Number", "channel": "Channel", "amount": "Amount", "paymentType": "Payment Type", "arrivalTime": "Arrival Time", "remark": "Remark", "submitButton": "Submit", "cancelButton": "Cancel", "businessTypes": {"payment": "Payment", "refund": "Refund"}, "channels": {"bankTransfer": "Bank Transfer", "onlinePayment": "Online Payment", "cash": "Cash", "check": "Check"}, "paymentTypes": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "finalPayment": "Final Payment", "fullPayment": "Full Payment"}, "messages": {"submitSuccess": "Operation successful", "submitError": "Operation failed", "amountRequired": "Please enter amount", "transactionNumberRequired": "Please enter transaction number", "arrivalTimeRequired": "Please select arrival time"}}}