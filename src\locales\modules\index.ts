// 模块化国际化索引文件
// 项目采用七个核心模块进行国际化管理

export const moduleNames = [
  'common',          // 通用模块：按钮、操作、状态等
  'sales',           // 销售模块：车辆管理、订单管理、客户管理等
  'afterSales',      // 售后模块：维修管理、质量检查、客户服务等
  'parts',           // 零件模块：零件库存、采购管理、供应商管理等
  'finance',         // 财务模块：收款管理、发票管理、财务报表等
  'base',            // 基础模块：登录系统、用户管理、权限管理等
  'quotaManagement', // 配额管理模块：预约限量管理
  'menu'             // 菜单模块：导航菜单、页面标题等
] as const

export type ModuleName = typeof moduleNames[number]

// 模块描述，用于开发参考
export const moduleDescriptions = {
  common: '通用文本：按钮、操作、状态等跨模块使用的通用内容',
  sales: '销售管理：车辆管理、订单管理、客户管理、销售报表等',
  afterSales: '售后服务：维修管理、配件管理、质量检查、客户服务等',
  parts: '零件管理：零件库存、采购管理、供应商管理、零件档案等',
  finance: '财务管理：收款管理、发票管理、财务报表、资金流水等',
  base: '基础系统：登录系统、用户管理、权限管理、系统设置等',
  quotaManagement: '配额管理：预约限量配置、时段管理、预约统计等',
  menu: '菜单导航：页面标题、导航菜单、面包屑等'
} as const
