{"title": "Inspection Form List", "form": {"inspectionNo": "Inspection No.", "inspectionStatus": "Inspection Status", "licensePlateNo": "License Plate No.", "repairmanName": "Repairman Name", "technician": "Technician", "repairmanPhone": "Repairman Phone", "createTime": "Create Time"}, "status": {"pending": "Pending", "in_progress": "In Progress", "pending_confirm": "Pending Confirmation", "confirmed": "Confirmed", "undefined": "Undefined"}, "table": {"index": "No.", "inspectionNo": "Inspection No.", "inspectionStatus": "Inspection Status", "repairmanName": "Repairman Name", "repairmanPhone": "Repairman Phone", "licensePlateNo": "License Plate No.", "color": "Color", "serviceAdvisor": "Service Advisor", "technician": "Technician", "registerType": "Register Type", "serviceType": "Service Type", "customerConfirmTime": "Customer Confirm Time", "createTime": "Create Time", "updateTime": "Update Time"}, "mockData": {"names": {"zhangSan": "<PERSON>", "liSi": "<PERSON>", "wangWu": "<PERSON>", "liHua": "<PERSON>", "zhaoLiu": "<PERSON>", "wangDaChui": "<PERSON>", "qianQi": "<PERSON><PERSON>", "sunBa": "Sun Ba", "linYiYi": "<PERSON>", "zhouJiu": "<PERSON>", "wuShi": "<PERSON>"}, "colors": {"white": "White", "black": "Black", "blue": "Blue", "gray": "<PERSON>"}, "registerTypes": {"appointment": "Appointment", "walkIn": "Walk-in"}, "serviceTypes": {"maintenance": "Maintenance", "repair": "Repair"}}, "createWorkOrderModal": {"form": {"vehicleModel": "Vehicle Model", "vehicleConfig": "Configuration", "mileage": "Mileage", "vehicleAge": "Vehicle Age"}, "messages": {"createSuccess": "Work order created successfully", "saveDraftSuccess": "Work order draft saved successfully"}}, "messages": {"exportingData": "Exporting data", "viewDetail": "View detail", "assignInspectionForm": "Assign inspection form", "editInspectionForm": "Edit inspection form", "submitConfirm": "Submit for customer confirmation", "recall": "Recall", "print": "Print", "customerConfirm": "Customer confirm", "createWorkOrder": "Create work order", "saveInspectionFormDetail": "Save inspection form detail"}, "detailEdit": {"titleEdit": "Edit Inspection Form Detail", "titleDetail": "Inspection Form Detail", "clientInfo": "Client Information", "reserveName": "Reservation Name", "reservePhone": "Reservation Phone", "repairmanName": "Repairman Name", "repairmanPhone": "Repairman Phone", "remark": "Remark", "vehicleInfo": "Vehicle Information", "licensePlateNo": "License Plate No.", "vin": "VIN", "modelConfig": "Model/Configuration", "color": "Color", "mileage": "Mileage (km)", "vehicleAge": "Vehicle Age (months)", "inspectionFormInfo": "Inspection Form Information", "inspectionStatus": "Inspection Status", "registerType": "Register Type", "servicePackageName": "Service Package Name", "createTime": "Create Time", "serviceAdvisor": "Service Advisor", "technician": "Technician", "customerConfirmTime": "Customer Confirmation Time", "customerConfirmImage": "Customer Confirmation Signature Image", "inspectionContentList": "Inspection Content Checklist", "parkingAreaRecord": "Parking Area Record", "waitingArea": "Waiting Area", "leavingArea": "Leaving Area", "parkingZone": "Parking Zone", "dashboardInspection": "Dashboard Inspection", "mileageRecord": "Mileage Record", "batteryLevel": "Battery Level", "remainingRange": "Remaining Range (km)", "energyConsumption": "Energy Consumption (kWh/100km)", "functionalityCheck": "Functionality Check", "gaugesIndicators": "Gauges and Indicators", "airConditioningSystem": "Air Conditioning System", "wiperWasher": "Wiper and Washer", "infotainmentSystem": "Infotainment System", "warningLightsCheck": "Warning Lights Check", "exteriorInspection": "Exterior Inspection", "bodyExteriorInspection": "Body Exterior Inspection", "chargingPortCover": "Charging Port Cover", "electricSystemInspection": "Electric System Inspection", "batteryPackVisualInspection": "Battery Pack Visual Inspection", "batteryCoolingSystem": "Battery Cooling System", "highVoltageCableInspection": "High Voltage Cable Inspection", "driveMotor": "Drive Motor", "motorController": "Motor Controller", "chargingSystemCheck": "Charging System Check", "onboardCharger": "Onboard Charger", "chargingInterface": "Charging Interface", "tireWearInspection": "Tire Wear Inspection", "tyreThreadCheck": "<PERSON><PERSON> Thread <PERSON>", "checkDeviation": "Check Deviation", "lowRollingResistanceTyreStatus": "Low Rolling Resistance Tyre Status", "tirePosition": "Tire Position", "frontRight": "Front Right", "frontLeft": "Front Left", "rearRight": "Rear Right", "rearLeft": "Rear Left", "tirePressureMonitoring": "Tire Pressure Monitoring", "customerSelfDescribedProblem": "Customer's Self-Described Problem", "registerTypeAppointment": "Appointment", "registerTypeWalkIn": "Walk-in", "serviceTypeMaintenance": "Maintenance"}, "assignInspectionForm": {"title": "Assign Inspection Form", "inspectionNo": "Inspection No.", "licensePlateNo": "License Plate No.", "repairmanName": "Repairman Name", "registerType": "Register Type", "serviceType": "Service Type", "technician": "Technician", "selectTechnicianPlaceholder": "Please select a technician"}, "technicians": {"technicianA": "Technician A", "technicianB": "Technician B", "technicianC": "Technician C"}, "customerConfirm": {"title": "Customer Confirm", "inspectionNo": "Inspection No.", "licensePlateNo": "License Plate No.", "repairmanName": "Repairman Name", "customerConfirmTime": "Customer Confirm Time", "uploadedFiles": "Uploaded Files", "selectDatePlaceholder": "Select Date", "clickToUploadImage": "Click to Upload Image", "pleaseSelectConfirmTime": "Please select confirm time", "pleaseUploadFile": "Please upload file"}}