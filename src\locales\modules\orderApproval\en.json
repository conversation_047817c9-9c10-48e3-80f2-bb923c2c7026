{"pageTitle": "Order Approval", "pendingTab": "To be approved", "approvedTab": "Approved", "searchTitle": "Filter Conditions", "approvalType": "Approval Type", "approvalTypePlaceholder": "Please select approval type", "cancelOrderApproval": "Cancel Order Approval", "modifyOrderApproval": "Modify Order Approval", "vehicleColorModificationApproval": "Vehicle Color Modification Approval", "orderNumber": "Order Number", "orderNumberPlaceholder": "Please enter order number", "submittedBy": "Submitted by", "submittedByPlaceholder": "Please enter submitter name", "submissionTime": "Submission Time", "submissionTimeEnd": "Submission Time End", "startDate": "Start Date", "endDate": "End Date", "approvalStatus": "Approval Status", "approvalStatusPlaceholder": "Please select approval status", "pendingInitialReview": "Pending Initial Review", "pendingFinalReview": "Pending Final Review", "aboutToTimeout": "About to Timeout", "approvalResult": "A<PERSON><PERSON><PERSON> Result", "approvalResultPlaceholder": "Please select approval result", "approved": "Approved", "rejected": "Rejected", "timeoutRejected": "Timeout Rejected", "store": "Store", "storePlaceholder": "Please select store", "serialNumber": "Serial Number", "approvalNumber": "Approval Number", "remainingTime": "Remaining Time", "approvalTime": "Approval Time", "approvedBy": "Approved By", "approve": "Approve", "reject": "Reject", "review": "Review", "approvalHistory": "Approval History", "applicationReason": "Application Reason", "totalCount": "Total {count} records", "selectedCount": "Selected {count} records", "batchApprove": "<PERSON><PERSON> Approve", "batchReject": "<PERSON>ch Reject", "days": "days", "minutes": "minutes", "timeout": "Timeout", "urgent": "<PERSON><PERSON>", "aboutTimeout": "About Timeout", "highPriority": "High Priority", "initialReview": "Initial Review", "finalReview": "Final Review", "pendingApprovalList": "Pending Approval List", "approvedList": "Approved List", "fetchDataFailed": "Failed to fetch data", "orderDetailNotImplemented": "Order detail function not implemented yet", "approveSuccess": "Approval successful", "rejectSuccess": "Rejection successful", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "batchOperationSuccess": "Batch operation successful", "batchOperationFailed": "Batch operation failed", "exportSuccess": "Export successful", "exportFailed": "Export failed", "cancelReason": "Cancel Reason", "changeDetails": "Change Details", "changedField": "Changed Field", "originalValue": "Original Value", "newValue": "New Value", "approvalInfo": "Approval Information", "approvalComment": "Approval Comment", "dialogs": {"approveTitle": "Approval Action", "historyTitle": "Approval History", "exportTitle": "Export Approval Data", "resultRequired": "Please select approval result", "reasonRequired": "Reason is required for rejection", "approveSuccess": "Approval successful", "rejectSuccess": "Rejection successful", "exportSuccess": "Export successful"}, "comments": "Comments", "reason": "Reason", "placeholders": {"enterComments": "Please enter comments", "enterReason": "Please enter reason for rejection"}, "result": {"approved": "Approved", "rejected": "Rejected", "timeout": "Timeout"}, "export": {"format": "Export Format", "range": "Export Range", "currentPage": "Current Page", "filteredResult": "Filtered Result", "allData": "All Data"}, "timeRange": "Time Range", "emptyHistory": "No approval history", "formatRequired": "Please select export format", "rangeRequired": "Please select export range"}