{"title": "Parts Receipt (Store)", "partName": "Part Name", "partNamePlaceholder": "Enter part name", "partNumber": "Part Number", "partNumberPlaceholder": "Enter part number", "supplierName": "Supplier Name", "supplierNamePlaceholder": "Enter supplier name", "requisitionNumber": "Requisition No.", "requisitionNumberPlaceholder": "Enter requisition number", "purchaseOrderNumber": "Purchase Order No.", "purchaseOrderNumberPlaceholder": "Enter purchase order number", "deliveryOrderNumber": "Delivery Order No.", "deliveryOrderNumberPlaceholder": "Enter delivery order number", "receipt": "Receipt", "generateReceiptOrder": "Generate Receipt Order", "invalidateReceiptOrder": "Invalidate Receipt Order", "receiptOrderNumber": "Receipt Order No.", "partQuantity": "Part Quantity", "unit": "Unit", "receiptDialogTitle": "Part Receipt", "receiptQuantity": "Receipt Quantity", "damagedQuantity": "Damaged Quantity", "damageDescription": "Damage Description", "damageDescriptionPlaceholder": "Enter damage description", "selectItemForReceipt": "Please select items to receive.", "damageDescriptionRequired": "Damage description is required when damaged quantity is not zero.", "receiptSuccess": "Receipt successful!", "generateReceiptOrderDialogTitle": "Generate Receipt Order", "receiptTime": "Receipt Time", "receiptTimePlaceholder": "Select receipt time", "quantity": "Quantity", "printReceiptOrder": "Print Receipt Order", "selectItemToPrint": "Please select items to print.", "generateReceiptOrderSuccess": "Receipt order generated successfully!", "invalidateReceiptOrderDialogTitle": "Invalidate Receipt Order", "receiptOrderStatus": "Receipt Order Status", "receiptOrderStatusPlaceholder": "Select status", "statusActive": "Active", "statusInvalid": "Invalid", "generateDate": "Generation Date", "confirmInvalidate": "Are you sure you want to invalidate this order?", "invalidateReceiptOrderSuccess": "Receipt order invalidated successfully!", "viewDetails": "View Details", "invalidate": "Invalidate"}