{"salesProspectManagement": "Sales Prospect Management", "prospectId": "Prospect ID", "inputProspectId": "Please enter prospect ID", "prospectName": "Prospect Name", "inputProspectName": "Please enter prospect name", "prospectPhone": "Prospect Phone", "inputPhoneNumber": "Please enter phone number", "sourceChannel": "Source Channel", "prospectLevel": "Prospect Level", "prospectStatus": "Prospect Status", "intentModel": "Intended Model", "intentVariant": "Intended Configuration", "intentColor": "Intended Color", "salesAdvisorId": "ID Staff", "salesAdvisorName": "Sales Advisor", "prospectCreationTime": "Prospect Creation Time", "lastFollowUpTime": "Last Follow Up Time", "nextFollowUpTime": "Next Follow Up Time", "markNoIntention": "Mark No Intention", "changeAdvisor": "Change Advisor", "addProspectSuccess": "Add prospect successfully", "followUpRecordAddSuccess": "Follow up record added successfully", "markNoIntentionApplySuccess": "Mark no intention application submitted successfully", "changeAdvisorSuccess": "Advisor changed successfully", "levelH": "Level H", "levelA": "Level A", "levelB": "Level B", "levelC": "Level C", "statusNew": "New", "statusFollowing": "Following Up", "statusClosed": "Closed", "statusNoIntention": "No Intention", "addProspect": "Add Prospect", "foundMatchingLead": "Found matching lead", "noMatchingLead": "No matching lead found", "customerName": "Customer Name", "phoneNumber": "Phone Number", "email": "Email", "idType": "ID Type", "idNumber": "ID Number", "inputNameOrPhoneRequired": "Please enter customer name or phone number", "noMatchingLeadMessage": "No matching lead information found in the system, cannot add prospect. Please confirm the entered customer information is correct, or contact the administrator to create a lead first.", "inputIdNumber": "Please enter ID number", "inputEmail": "Please enter email address", "intentionLevel": "Intention Level", "idCard": "ID Card", "passport": "Passport", "residencePermit": "Residence Permit", "inputProspectNameRequired": "Please enter prospect name", "inputPhoneNumberRequired": "Please enter phone number", "invalidPhoneNumber": "Please enter a valid phone number", "selectIdTypeRequired": "Please select ID type", "inputIdNumberRequired": "Please enter ID number", "inputEmailRequired": "Please enter email", "invalidEmail": "Please enter a valid email address", "selectIntentionLevelRequired": "Please select intention level", "prospectFollowUp": "Prospect Follow Up", "prospectInfo": "Prospect Information", "idTypePlaceholder": "Please select", "region": "Region", "selectRegion": "Please select region", "prospectIntention": "Prospect Intention", "selectIntentModel": "Please select intended model", "selectIntentVariant": "Please select intended configuration", "selectIntentColor": "Please select intended color", "followUpRecord": "Follow Up Record", "currentAdvisor": "Current Advisor", "followUpMethod": "Follow Up Method", "selectFollowUpMethod": "Please select follow up method", "followUpTime": "Follow Up Time", "selectFollowUpTime": "Please select follow up time", "intentionLevelPlaceholder": "Please select", "nextFollowUpTimePlaceholder": "System will automatically calculate based on intention level", "followUpDetails": "Follow Up Details", "followUpDetailsPlaceholder": "Please describe follow up details...", "inputFollowUpDetailsRequired": "Please fill in follow up details", "selectFollowUpMethodRequired": "Please select follow up method", "selectFollowUpTimeRequired": "Please select follow up time", "selectIntentionLevelRequiredFollowUp": "Please select intention level", "selectIntentionModelRequired": "Please select intended model", "selectIntentionVariantRequired": "Please select intended configuration", "selectIntentionColorRequired": "Please select intended color", "changeAdvisorModalTitle": "Change Advisor", "currentAdvisorId": "Current Advisor ID", "currentAdvisorName": "Current Advisor Name", "changeAdvisorId": "Change Advisor ID", "changeAdvisorName": "Change Advisor Name", "selectNewAdvisor": "Please select new sales advisor", "changeReason": "Reason for Reassigning Advisor", "inputChangeReason": "Please enter reason for reassigning advisor", "newAdvisorCannotBeSame": "New advisor cannot be the same as current advisor", "changeFailed": "Change failed", "selectNewAdvisorRequired": "Please select new sales advisor", "inputChangeReasonRequired": "Please enter reason for reassigning advisor", "markNoIntentionConfirm": "Mark No Intention Confirmation", "markTime": "<PERSON>", "noIntentionReason": "No Intention Reason", "selectNoIntentionReason": "Please select no intention reason", "detailedDescription": "Detailed Description", "detailedDescriptionPlaceholder": "Please describe no intention reason in detail...", "inputDetailedDescriptionRequired": "Please enter detailed description", "markNoIntentionSubmitSuccess": "Mark no intention application submitted successfully, waiting for review", "formValidationFailed": "Form validation failed or submission failed", "prospectDetails": "Prospect Details", "prospectSource": "Prospect Source", "idDocumentType": "ID Document Type", "idDocumentNumber": "ID Document Number", "address": "Address", "testDriveRecord": "Test Drive Record", "driver": "Driver", "model": "Model", "time": "Time", "feedback": "<PERSON><PERSON><PERSON>", "noTestDriveRecord": "No test drive record", "changeLog": "Change Log", "changeContent": "Change Content", "originalInfo": "Original Info", "changedInfo": "Changed Info", "operator": "Operator", "operationTime": "Operation Time", "noFollowUpRecord": "No follow up record", "noChangeLog": "No change log", "testDriveManagement": "Test Drive Management", "testDriveList": "Test Drive Registration List", "testDriveRegistration": "Test Drive Registration", "testDriveDetail": "Test Drive Details", "testDriveEdit": "Edit Test Drive", "testDriveCreate": "Register Test Drive", "testDriveNo": "Test Drive No.", "testDriveTime": "Test Drive Time", "testDriveModel": "Test Drive Model", "testDriveVariant": "Test Drive Variant", "testDrivePerson": "Test Driver", "testDrivePersonPhone": "Test Driver Phone", "testDrivePersonIdCard": "Test Driver ID", "testDrivePersonLicense": "Test Driver License", "testDriveStartMileage": "Start Mileage", "testDriveEndMileage": "End Mileage", "testDriveStartTime": "Start Time", "testDriveEndTime": "End Time", "testDriveFeedback": "Test Drive Feedback", "testDriveEntryTime": "Registration Time", "searchProspect": "Search Prospect", "prospectSearch": "Quick Search Prospect", "testDriveInfo": "Test Drive Information", "salesConsultant": "Sales Consultant", "pleaseSelectModel": "Please select model", "pleaseSelectVariant": "Please select variant", "pleaseEnterDriverName": "Please enter driver name", "pleaseEnterDriverPhone": "Please enter driver phone", "pleaseSelectIdType": "Please select ID type", "pleaseEnterIdNumber": "Please enter ID number", "pleaseEnterLicenseNumber": "Please enter license number", "pleaseEnterStartMileage": "Please enter start mileage", "pleaseEnterEndMileage": "Please enter end mileage", "pleaseSelectStartTime": "Please select start time", "pleaseSelectEndTime": "Please select end time", "pleaseEnterFeedback": "Please enter feedback", "autoFillAfterSearch": "Auto-fill after search", "autoGenerateOnSave": "Auto-generate on save", "belongingConsultant": "Prospect's consultant", "defaultFromProspect": "Default from prospect info, editable", "registrationSuccess": "Registration successful", "updateSuccess": "Test drive updated successfully", "searchProspectFirst": "Please search and select prospect", "needSalesConsultant": "Prospect needs assigned sales consultant", "endMileageError": "End mileage cannot be less than start mileage", "endTimeError": "End time must be later than start time", "pleaseEnterProspectName": "Please enter prospect name to search", "pleaseEnterProspectPhone": "Enter phone number to search", "searchSuccess": "Prospect information loaded successfully", "noMatchingProspect": "No matching prospect found", "pleaseEnterNameOrPhone": "Please enter prospect name or phone number to search", "syncSuccess": "sync success", "errorMessage": "Error message", "recordCount": "Record count", "syncTime": "Sync time", "No": "No", "prospects": {"title": "Prospect Management", "salesProspectManagement": "Sales Prospect Management", "prospectId": "Prospect ID", "prospectName": "Prospect Name", "prospectPhone": "Contact Phone", "sourceChannel": "Source Channel", "prospectLevel": "Intent Level", "prospectStatus": "Prospect Status", "intentModel": "Intent Model", "intentVariant": "Intent Variant", "intentColor": "Intent Color", "salesAdvisorId": "ID Staff", "salesAdvisorName": "Sales Advisor", "prospectAssociatedTime": "Prospect Associated Time", "prospectCreationTime": "Prospect Creation Time", "lastFollowUpTime": "Last Follow Up Time", "nextFollowUpTime": "Next Follow Up Time", "markNoIntention": "Mark No Intention", "changeAdvisor": "Change Advisor", "addProspect": "Add Prospect", "prospectFollowUp": "Prospect Follow-up", "prospectDetails": "Prospect Details", "addProspectSuccess": "Prospect added successfully", "followUpRecordAddSuccess": "Follow-up record added successfully", "markNoIntentionApplySuccess": "No intention mark applied successfully", "changeAdvisorSuccess": "Advisor changed successfully", "inputProspectId": "Please enter prospect ID", "inputProspectName": "Please enter prospect name", "inputPhoneNumber": "Please enter phone number", "prospectInfo": "Prospect Information", "searchExistingLead": "Search Existing Lead", "existingLeadFound": "Existing Lead Found", "existingLeadInfo": "The system has detected an existing lead record for this customer. A new prospect will be created based on this lead.", "foundMatchingLead": "Found matching lead", "noMatchingLead": "No matching lead found", "noMatchingLeadMessage": "No matching lead information found in the system. A new prospect will be created.", "inputNameOrPhoneRequired": "Please enter customer name or phone number", "nameRequired": "Please enter prospect name", "nameLength": "Name must be between 2 and 50 characters", "phoneRequired": "Please enter phone number", "phoneFormat": "Please enter a valid phone number", "sourceRequired": "Please select a source channel", "levelRequired": "Please select an intent level", "emailFormat": "Please enter a valid email address", "searchCriteriaRequired": "Please enter name or phone to search", "noExistingLeadFound": "No existing lead found. Please fill out the form below to create a new prospect.", "selectSourceChannel": "Select Source Channel", "selectProspectLevel": "Select Intent Level", "selectIdType": "Select ID Type", "inputIdNumber": "Enter ID Number", "inputEmail": "Enter Email Address", "inputRegion": "Enter Region", "idType": "ID Type", "idNumber": "ID Number", "email": "Email", "region": "Region", "changeAdvisorModalTitle": "Change Sales Advisor", "currentAdvisorInfo": "Current Advisor Information", "currentAdvisor": "Current Advisor", "noAdvisorAssigned": "No Advisor Assigned", "changeAdvisorInfo": "Change Information", "newAdvisor": "New Sales Advisor", "selectNewAdvisor": "Please select a new sales advisor", "changeReason": "Reason for Change", "inputChangeReason": "Please enter the reason for the change", "newAdvisorRequired": "A new sales advisor must be selected", "changeReasonRequired": "The reason for the change is required", "changeReasonLength": "The reason must be between 5 and 200 characters", "newAdvisorCannotBeSame": "The new advisor cannot be the same as the current one", "changeFailed": "Failed to change advisor", "markNoIntentionConfirm": "Confirm Mark as No Intention", "applicationInfo": "Application Information", "defeatReason": "Reason for Lost", "selectDefeatReason": "Please select the reason for lost", "defeatDetails": "Detailed Description", "inputDefeatDetails": "Please enter a detailed description", "applicationTime": "Application Time", "defeatReasonRequired": "The reason for lost is required", "defeatDetailsRequired": "A detailed description is required", "defeatDetailsLength": "The description must be between 10 and 500 characters", "confirmMarkNoIntention": "Confirm Mark", "markNoIntentionSubmitSuccess": "Application to mark as no intention has been submitted", "formValidationFailed": "Form validation failed", "followUpInfo": "Follow-up Information", "followUpType": "Follow-up Type", "selectFollowUpType": "Please select a follow-up type", "followUpTime": "Follow-up Time", "selectFollowUpTime": "Please select a follow-up time", "followUpContent": "Follow-up Content", "inputFollowUpContent": "Please enter the follow-up content", "followUpResult": "Follow-up Result", "inputFollowUpResult": "Please enter the follow-up result", "intentLevelAfterFollowUp": "Intent Level After Follow-up", "selectIntentLevel": "Please select an intent level", "selectNextFollowUpTime": "Please select the next follow-up time", "followUpTypeRequired": "A follow-up type is required", "followUpTimeRequired": "A follow-up time is required", "followUpContentRequired": "Follow-up content is required", "followUpContentLength": "Content must be between 10 and 500 characters", "followUpResultRequired": "A follow-up result is required", "followUpResultLength": "Result must be between 5 and 300 characters", "intentLevelRequired": "Intent level after follow-up is required", "idTypePlaceholder": "Select ID Type", "idCard": "ID Card", "passport": "Passport", "residencePermit": "Residence Permit", "prospectIntention": "Prospect Intention", "selectIntentVariant": "Select Intent Variant", "selectIntentColor": "Select Intent Color", "followUpRecord": "Follow-up Record", "followUpMethod": "Follow-up Method", "selectFollowUpMethod": "Select Follow-up Method", "intentionLevel": "Intention Level", "intentionLevelPlaceholder": "Select Intention Level", "nextFollowUpTimePlaceholder": "System will calculate automatically based on intent level", "followUpDetails": "Follow-up Details", "followUpDetailsPlaceholder": "Describe the follow-up details...", "inputFollowUpDetailsRequired": "Please enter follow-up details", "selectIntentionLevelRequiredFollowUp": "Please select the intent level after follow-up", "selectNextFollowUpTimeRequired": "Please select the next follow-up time", "selectIntentionModelRequired": "Please select an intent model", "selectIntentionVariantRequired": "Please select an intent variant", "selectIntentionColorRequired": "Please select an intent color"}, "vehicleQuery": {"title": "Vehicle Query", "vin": "VIN", "vinPlaceholder": "Please enter the VIN", "factoryOrderNo": "Factory Order Number", "factoryOrderNoPlaceholder": "Please enter the factory order number", "warehouseName": "Warehouse Name", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "fmrId": "FMRID", "fmrIdPlaceholder": "Please enter the FMRID", "lockStatus": "Lock Status", "invoiceStatus": "Invoice Status", "deliveryStatus": "Delivery Status", "stockStatus": "Stock Status", "invoiceDate": "Invoice Date", "deliveryDate": "Delivery Date", "storageDate": "Storage Date", "productionDate": "Production Date", "detailDialogTitle": "Vehicle Details", "exportDialogTitle": "Export Data", "exportFormat": "Export Format", "exportFormatPlaceholder": "Please select export format", "excel": "Excel", "csv": "CSV", "exportScope": "Export <PERSON>", "exportCurrentPage": "Current Page Data", "exportAllData": "All Data", "exportSuccess": "Exported {format} file successfully!", "fetchConfigFailed": "Failed to fetch model configuration"}, "orders": {"title": "Sales Orders", "list": {"title": "Sales Order List", "search": {"title": "Search Criteria", "orderNumber": "Order Number", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "buyerType": "Buyer Type", "model": "Model", "orderStatus": "Order Status", "approvalStatus": "Approval Status", "paymentStatus": "Payment Status", "insuranceStatus": "Insurance Status", "loanApprovalStatus": "<PERSON>an <PERSON>al <PERSON>", "jpjRegistrationStatus": "JPJ Registration Status", "createTime": "Create Time", "ordererName": "Orderer Name", "ordererPhone": "Orderer Phone", "search": "Search", "reset": "Reset"}, "columns": {"orderNumber": "Order Number", "createTime": "Create Time", "customerName": "Customer Name", "customerPhone": "Customer Phone", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "buyerType": "Buyer Type", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "vin": "VIN", "paymentMethod": "Payment Method", "orderStatus": "Order Status", "approvalStatus": "Approval Status", "paymentStatus": "Payment Status", "insuranceStatus": "Insurance Status", "jpjRegistrationStatus": "JPJ Registration Status", "totalAmount": "Total Amount"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete", "submitApproval": "Submit Approval", "cancelOrder": "Cancel Order"}}, "detail": {"title": "Order Details", "orderNumber": "Order Number", "createTime": "Create Time", "orderStatus": "Order Status", "approvalStatus": "Approval Status", "paymentStatus": "Payment Status", "insuranceStatus": "Insurance Status", "jpjRegistrationStatus": "JPJ Registration Status", "customerInfo": "Customer Info", "ordererName": "Orderer Name", "ordererPhone": "Orderer Phone", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "buyerIdType": "ID Type", "buyerIdNumber": "ID Number", "buyerEmail": "Email", "buyerAddress": "Address", "buyerState": "State", "buyerCity": "City", "buyerPostcode": "Postcode", "buyerType": "Buyer Type", "storeInfo": "Store Info", "storeRegion": "Store Region", "storeCity": "Store City", "storeName": "Store Name", "salesConsultantName": "Sales Consultant", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "salesSubtotal": "Vehicle Price", "numberPlatesFee": "Number Plate Fee", "vin": "VIN", "accessoriesInformation": "Accessories Information", "accessoryCategory": "Accessory Category", "accessoryName": "Accessory Name", "unitPrice": "Unit Price", "quantity": "Quantity", "totalPrice": "Total Price", "accessoriesTotalAmount": "Total Accessories Amount", "invoiceTypeLabel": "Invoice Type", "invoiceNameLabel": "Invoice Name", "invoicePhoneLabel": "Invoice Phone", "invoiceAddressLabel": "Invoice Address", "rightsInfo": "Rights Information", "rightCode": "Right Code", "rightName": "Right Name", "rightMode": "Right Type", "discountAmount": "Discount Amount", "effectiveDate": "Effective Date", "expiryDate": "Expiry Date", "rightsDiscountTotalAmount": "Total Rights Discount", "paymentMethod": "Payment Method", "loanStatusLabel": "Loan Status", "depositAmountLabel": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "loanAmountLabel": "<PERSON><PERSON>", "finalPaymentLabel": "Final Payment", "insuranceInfo": "Insurance Information", "policyNumber": "Policy Number", "insuranceType": "Insurance Type", "insuranceCompany": "Insurance Company", "insurancePrice": "Insurance Premium", "remarksLabel": "Remarks", "insuranceTotalAmount": "Total Insurance Amount", "otrFeesInfo": "OTR Fees Information", "ticketNumber": "Ticket Number", "feeItem": "<PERSON><PERSON>em", "feePrice": "<PERSON><PERSON> Amount", "otrFeesTotalAmount": "Total OTR Fees", "changeRecords": "Change Records", "operationTime": "Operation Time", "operator": "Operator", "originalContent": "Original Content", "changedContent": "Changed Content", "totalAmount": "Total Amount", "remainingReceivable": "Remaining Receivable", "missingOrderNumber": "Missing order number", "fetchDetailError": "Failed to fetch order details", "returnToList": "Return to List"}, "edit": {"title": "Edit Order", "orderNumber": "Order Number", "createTime": "Create Time", "customerInformation": "Customer Information", "ordererNameLabel": "Orderer Name", "ordererPhoneLabel": "Orderer Phone", "buyerNameLabel": "Buyer Name", "buyerPhoneLabel": "Buyer Phone", "buyerIdTypeLabel": "ID Type", "buyerIdNumberLabel": "ID Number", "buyerEmailLabel": "Email", "buyerAddressLabel": "Address", "buyerStateLabel": "State", "buyerCityLabel": "City", "buyerPostcodeLabel": "Postcode", "buyerTypeLabel": "Buyer Type", "dealershipInformation": "Dealership Information", "regionLabel": "Region", "cityLabel": "City", "dealershipLabel": "Dealership", "salesAdvisorLabel": "Sales Advisor", "purchaseInformation": "Purchase Information", "vehicleInformation": "Vehicle Information", "modelLabel": "Model", "variantLabel": "<PERSON><PERSON><PERSON>", "colorLabel": "Color", "salesSubtotalLabel": "Vehicle Price", "numberPlatesFeeLabel": "Number Plate Fee", "vinLabel": "VIN", "invoiceInfoTab": "Invoice Info", "invoicingType": "Invoice Type", "invoicingName": "Invoice Name", "invoicingPhone": "Invoice Phone", "invoicingAddress": "Invoice Address", "addRights": "Add Rights", "rightsDiscountAmount": "Rights Discount Amount", "loanApprovalStatusField": "<PERSON>an <PERSON>al <PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "loanAmount": "<PERSON><PERSON>", "balanceAmount": "Balance Amount", "insuranceNotes": "Insurance Notes", "otrInfoTab": "OTR Fees", "totalInvoiceAmount": "Total Invoice Amount", "remainingAmount": "Remaining Amount", "save": "Save", "saveSuccess": "Save Successfully", "saveFailed": "Save Failed", "confirmCancel": "Confirm Cancel", "cancelSuccess": "Cancel Successfully", "validation": {"selectColor": "Please select color", "selectPaymentMethod": "Please select payment method", "enterLoanAmount": "Please enter loan amount"}, "messages": {"saveSuccess": "Save successfully", "saveFailed": "Save failed", "colorChangeAlert": "Color change requires re-approval"}}, "customerInformation": "Customer Information", "ordererNameLabel": "Orderer Name", "ordererPhoneLabel": "Orderer Phone", "buyerNameLabel": "Buyer Name", "buyerPhoneLabel": "Buyer Phone", "buyerIdTypeLabel": "ID Type", "buyerIdNumberLabel": "ID Number", "buyerEmailLabel": "Email", "buyerAddressLabel": "Address", "buyerStateLabel": "State", "buyerCityLabel": "City", "buyerPostcodeLabel": "Postcode", "buyerTypeLabel": "Buyer Type", "dealershipInformation": "Dealership Information", "regionLabel": "Region", "cityLabel": "City", "dealershipLabel": "Dealership", "salesAdvisorLabel": "Sales Advisor", "purchaseInformation": "Purchase Information", "vehicleInformation": "Vehicle Information", "modelLabel": "Model", "variantLabel": "<PERSON><PERSON><PERSON>", "colorLabel": "Color", "salesSubtotalLabel": "Vehicle Price", "numberPlatesFeeLabel": "Number Plate Fee", "vinLabel": "VIN", "invoiceInfoTab": "Invoice Info", "invoicingType": "Invoice Type", "invoicingName": "Invoice Name", "invoicingPhone": "Invoice Phone", "invoicingAddress": "Invoice Address", "addRights": "Add Rights", "rightsDiscountAmount": "Rights Discount Amount", "loanApprovalStatusField": "<PERSON>an <PERSON>al <PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "loanAmount": "<PERSON><PERSON>", "balanceAmount": "Balance Amount", "insuranceNotes": "Insurance Notes", "otrInfoTab": "OTR Fees", "totalInvoiceAmount": "Total Invoice Amount", "remainingAmount": "Remaining Amount", "rightCode": "Right Code", "rightName": "Right Name", "rightMode": "Right Type", "discountAmount": "Discount Amount", "effectiveDate": "Effective Date", "expiryDate": "Expiry Date", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "vin": "VIN", "salesSubtotal": "Vehicle Price", "numberPlatesFee": "Number Plate Fee", "accessoriesInformation": "Accessories Information", "accessoryCategory": "Accessory Category", "accessoryName": "Accessory Name", "unitPrice": "Unit Price", "quantity": "Quantity", "totalPrice": "Total Price", "accessoriesTotalAmount": "Total Accessories Amount", "invoiceTypeLabel": "Invoice Type", "invoiceNameLabel": "Invoice Name", "invoicePhoneLabel": "Invoice Phone", "invoiceAddressLabel": "Invoice Address", "rightsInfo": "Rights Information", "rightsDiscountTotalAmount": "Total Rights Discount", "paymentMethod": "Payment Method", "loanStatusLabel": "Loan Status", "depositAmountLabel": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "loanAmountLabel": "<PERSON><PERSON>", "finalPaymentLabel": "Final Payment", "insuranceInfo": "Insurance Information", "policyNumber": "Policy Number", "insuranceType": "Insurance Type", "insuranceCompany": "Insurance Company", "insurancePrice": "Insurance Premium", "remarksLabel": "Remarks", "insuranceTotalAmount": "Total Insurance Amount", "otrFeesInfo": "OTR Fees Information", "ticketNumber": "Ticket Number", "feeItem": "<PERSON><PERSON>em", "feePrice": "<PERSON><PERSON> Amount", "otrFeesTotalAmount": "Total OTR Fees", "changeRecords": "Change Records", "operationTime": "Operation Time", "operator": "Operator", "originalContent": "Original Content", "changedContent": "Changed Content", "totalAmount": "Total Amount", "remainingReceivable": "Remaining Receivable", "salesOrderEdit": {"colorChangeNotice": "Color change will require re-approval", "loanTermLabel": "<PERSON>an <PERSON>", "fullPaymentAmount": "Full Payment Amount", "placeholders": {"selectColor": "Please select color", "selectPaymentMethod": "Please select payment method", "selectApprovalStatus": "Please select approval status", "selectLoanTerm": "Please select loan term", "insuranceNotes": "Please enter insurance notes"}, "loanTerms": {"12": "12 months", "24": "24 months", "36": "36 months", "48": "48 months", "60": "60 months", "72": "72 months"}, "messages": {"fetchDetailFailed": "Failed to fetch order details"}}, "rightsSelectionDialog": {"title": "Select Rights", "rightCodePlaceholder": "Enter right code", "rightNamePlaceholder": "Enter right name", "availableRights": "Available Rights", "selectedRights": "Selected Rights", "totalCount": "Total {count} items", "selectedCount": "Selected {count} items", "noRightsSelected": "No rights selected", "totalDiscountAmount": "Total Discount Amount", "confirmButtonText": "Confirm Selection({count} items)", "fetchRightsError": "Failed to fetch rights list"}, "status": {"orderStatus": {"submitted": "Submitted", "confirmed": "Confirmed", "pending_delivery": "Pending Delivery", "completed": "Completed", "canceled": "Canceled"}, "buyerType": {"individual": "Individual", "company": "Company"}, "paymentMethod": {"full_payment": "Full Payment", "installment": "Installment"}, "approvalStatus": {"pending_approval": "Pending Approval", "approved": "Approved", "rejected": "Rejected"}, "paymentStatus": {"pending_deposit": "Pending Deposit", "fully_paid": "<PERSON>y Paid", "refund_completed": "Refund Completed"}, "insuranceStatus": {"not_insured": "Not Insured", "pending": "Pending", "insured": "Insured"}, "jpjRegistrationStatus": {"pending_registration": "Pending Registration", "registering": "Registering", "registered": "Registered", "registration_failed": "Registration Failed"}}}, "orderApproval": {"pageTitle": "Order Approval Management", "pendingTab": "Pending", "approvedTab": "Approved", "searchTitle": "Filter Conditions", "approvalType": "Approval Type", "approvalTypePlaceholder": "Please select approval type", "orderNumber": "Order Number", "orderNumberPlaceholder": "Please enter order number", "submittedBy": "Submitted By", "submittedByPlaceholder": "Please enter submitter", "submissionTime": "Submission Time", "startDate": "Start Date", "endDate": "End Date", "approvalResult": "A<PERSON><PERSON><PERSON> Result", "approvalResultPlaceholder": "Please select approval result", "store": "Store", "storePlaceholder": "Please select store", "serialNumber": "Serial Number", "approvalNumber": "Approval Number", "applicationReason": "Application Reason", "approvalTime": "Approval Time", "approvedBy": "Approved By", "review": "Review", "pendingApprovalList": "Pending Approval List", "approvedList": "Approved List", "totalCount": "Total {count} records", "fetchDataFailed": "Failed to fetch data", "orderDetailNotImplemented": "Order detail feature not implemented", "operationSuccess": "Operation successful", "exportSuccess": "Export successful", "approvalTypeOptions": {"cancel_order": "Cancel Order", "modify_info": "Modify Information", "price_adjustment": "Price Adjustment"}, "approvalResultOptions": {"approved": "Approved", "rejected": "Rejected", "pending": "Pending"}, "approvalDetail": "Approval Detail", "approvalInfo": "Approval Information", "approvalComment": "Approval Comment", "cancelOrderApproval": "Cancel Order", "modifyOrderApproval": "Modify Information", "approved": "Approved", "rejected": "Rejected", "cancelReason": "Cancel Reason", "changeDetails": "Change Details", "changedField": "Changed Field", "originalValue": "Original Value", "newValue": "New Value", "comments": "Comments", "dialogs": {"approveTitle": "Approval Action", "resultRequired": "Please select approval result", "reasonRequired": "Please enter rejection reason", "approveSuccess": "Approved successfully", "rejectSuccess": "Rejected successfully", "exportTitle": "Export Approval Data", "historyTitle": "Approval History"}, "result": {"approved": "Approve", "rejected": "Reject", "timeout": "Timeout"}, "placeholders": {"enterComments": "Please enter comments", "enterReason": "Please enter rejection reason"}, "export": {"format": "Export Format", "range": "Export Range", "currentPage": "Current Page", "filteredResult": "Filtered Results", "allData": "All Data"}, "timeRange": "Time Range", "approver": "Approver", "emptyHistory": "No approval history"}, "delivery": {"pageTitle": "Delivery Management", "searchTitle": "Search Criteria", "listTitle": "Delivery List", "totalCount": "Total {count} records", "deliveryNumber": "Delivery Number", "deliveryNumberPlaceholder": "Enter delivery number", "orderNumber": "Order Number", "orderNoPlaceholder": "Enter order number", "customerName": "Customer Name", "customerNamePlaceholder": "Enter customer name", "customerPhone": "Customer Phone", "customerMobilePlaceholder": "Enter customer phone", "vin": "VIN", "vinPlaceholder": "Enter VIN", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "orderStatus": "Order Status", "orderStatusPlaceholder": "Select order status", "dealerStore": "Dealer Store", "dealerStorePlaceholder": "Select store", "salesConsultant": "Sales Consultant", "salesmanPlaceholder": "Enter sales consultant", "deliveryStatus": "Delivery Status", "deliveryStatusPlaceholder": "Select delivery status", "customerConfirmed": "Customer Confirmed", "customerConfirmedPlaceholder": "Select confirmation status", "confirmationType": "Confirmation Type", "confirmationTypePlaceholder": "Select confirmation type", "deliveryTime": "Delivery Time", "customerConfirmTime": "Customer Confirm Time", "startDate": "Start Date", "endDate": "End Date", "customerConfirmTimeStartPlaceholder": "Confirm start date", "customerConfirmTimeEndPlaceholder": "Confirm end date", "invoiceTime": "Invoice Time", "actualDeliveryDate": "Actual Delivery Date", "deliveryNotes": "Delivery Notes", "signaturePhoto": "Signature Photo", "submitConfirm": "Submit Confirm", "deliveryConfirm": "Delivery Confirm", "statusPending": "Pending Delivery", "statusConfirming": "Pending Confirm", "statusCompleted": "Completed", "orderStatusNormal": "Normal", "orderStatusCancelled": "Cancelled", "orderStatusPendingAllocation": "Pending Allocation", "orderStatusAllocating": "Allocating", "orderStatusAllocated": "Allocated", "orderStatusPendingDelivery": "Pending Delivery", "orderStatusDelivered": "Delivered", "confirmationTypeApp": "APP Confirmation", "confirmationTypeOffline": "Offline Confirmation", "fetchDataFailed": "Failed to fetch data", "detailNotFound": "Detail data not found", "fetchDetailFailed": "Failed to fetch detail", "printFeatureNotImplemented": "Print feature not implemented", "submitConfirmSuccess": "Submit confirm success", "submitConfirmFailed": "Submit confirm failed", "deliveryConfirmSuccess": "Delivery confirm success", "deliveryConfirmFailed": "Delivery confirm failed", "exportSuccess": "Export success", "exportFailed": "Export failed", "submitConfirmTitle": "Submit Confirmation", "submitConfirmQuestion": "Are you sure you want to submit this delivery order?", "deliveryNumberLabel": "Delivery Number", "orderNumberLabel": "Order Number", "customerNameLabel": "Customer Name", "customerPhoneLabel": "Customer Phone", "submitConfirmNote": "After submission, status will be change", "statusPendingDelivery": "Pending Delivery", "statusPendingConfirm": "Pending Confirm", "confirmSubmit": "Confirm Submit", "noOrderSelected": "No order data selected", "deliveryConfirmTitle": "Delivery Confirmation", "orderInfoTitle": "Order Information", "modelLabel": "Model", "variantLabel": "<PERSON><PERSON><PERSON>", "colorLabel": "Color", "deliveryInfoTitle": "Delivery Information", "deliveryTimeLabel": "Delivery Time", "deliveryTimeRequired": "Please select delivery time", "deliveryTimePlaceholder": "Please select delivery time", "customerSignatureConfirm": "Customer Signature Confirmation", "uploadSignaturePhoto": "Upload Signature Photo", "uploadSignaturePhotoTip": "Support JPG, PNG format, file size no more than 5MB", "deliveryNotesLabel": "Delivery Notes", "deliveryNotesPlaceholder": "Please enter delivery notes (optional)", "signaturePhotoFormatError": "Signature photo format error, please upload JPG or PNG format", "signaturePhotoSizeError": "Signature photo size cannot exceed 5MB", "signaturePhotoRequired": "Please upload customer signature photo", "signatureUploadFailed": "Signature photo upload failed", "detailTitle": "Delivery Details", "basicInfo": "Basic Information", "orderCreatorName": "Order Creator Name", "orderCreatorPhone": "Order Creator Phone", "customerType": "Customer Type", "idType": "ID Type", "idNumber": "ID Number", "address": "Address", "city": "City", "postcode": "Postcode", "state": "State/Province", "orderInfo": "Order Information", "orderCreateTime": "Order Create Time", "orderPaymentStatus": "Payment Status", "paymentMethod": "Payment Method", "vehicleInfo": "Vehicle Information", "warehouseName": "Warehouse Name", "productionDate": "Production Date", "entryTime": "Entry Time", "deliveryInfo": "Delivery Information", "noDetailData": "No detail data available", "statusProcessing": "Processing", "statusDelivered": "Delivered", "statusCancelled": "Cancelled", "orderStatusPending": "Pending", "orderStatusConfirmed": "Confirmed", "confirmationTypeOnline": "Online Confirmation", "confirmationTypePhone": "Phone Confirmation", "exportSettingsTitle": "Export Settings", "exportFormat": "Export Format", "exportFormatRequired": "Please select export format", "exportRange": "Export Range", "exportRangeRequired": "Please select export range", "exportRangeCurrentPage": "Current Page Data", "exportRangeFilteredResult": "Filtered Results", "exportRangeAllData": "All Data", "exportTimeRange": "Time Range"}, "testDriveReport": {"title": "Test Drive Report", "monthlyCount": "Monthly Test Drives", "dailyCount": "Daily Test Drives", "topStore": "Top Store by Test Drives", "topModel": "Top Model by Test Drives", "store": "Store", "model": "Test Drive Model", "variant": "Test Drive Configuration", "testDriveTime": "Test Drive Time", "recordList": "Test Drive Records", "testDriveNo": "Test Drive No.", "createTime": "Record Creation Time", "customerName": "Test Driver", "customerPhone": "Test Driver Phone", "mileage": "Mileage", "startTime": "Start Time", "endTime": "End Time", "consultantName": "Sales Consultant", "detailTitle": "Test Drive Details", "customerInfo": "Customer Information", "customerSource": "Customer Source", "idType": "ID Type", "idNumber": "Customer ID Number", "email": "Customer <PERSON><PERSON>", "testDriveInfo": "Test Drive Information", "driverName": "Driver Name", "driverPhone": "Driver Phone", "driverIdType": "Driver ID Type", "driverIdNumber": "Driver ID Number", "vehicleInfo": "Test Drive Vehicle", "startMileage": "Start Mileage", "endMileage": "End Mileage", "feedback": "Test Drive Feedback"}, "factoryOrderManagement": {"title": "Factory Order Management", "monthlyOrderCount": "Monthly Orders", "dailyOrderCount": "Daily Orders", "monthlyGrowthRate": "Monthly Growth Rate", "dailyGrowthRate": "Daily Growth Rate", "topDealer": "Top Dealer by Orders", "topVehicle": "Top Vehicle by Sales", "dealerName": "Dealer Name", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "orderDate": "Order Date", "orderNumber": "Order Number", "listTitle": "Order List", "exportExcel": "Export Excel", "creationTime": "Creation Time", "customerName": "Customer Name", "customerPhone": "Customer Phone", "customerType": "Customer Type", "vin": "VIN", "paymentMethod": "Payment Method", "loanStatus": "Loan Status", "approvalStatus": "Approval Status", "insuranceStatus": "Insurance Status", "jpjRegistrationStatus": "JPJ Registration Status", "orderDetailTitle": "Order Details", "basicInfo": "Basic Information", "orderInfo": "Order Information", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "paymentInfo": "Payment Information", "loanInfo": "Loan Information", "insuranceInfo": "Insurance Information", "jpjInfo": "JPJ Registration Information", "customerEmail": "Customer <PERSON><PERSON>", "engineNumber": "Engine Number", "chassisNumber": "<PERSON><PERSON><PERSON> Number", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "remainingAmount": "Remaining Amount", "loanAmount": "<PERSON><PERSON>", "loanBank": "Loan Bank", "approvalDate": "Approval Date", "insuranceCompany": "Insurance Company", "insuranceType": "Insurance Type", "insuranceAmount": "Insurance Amount", "registrationNumber": "Registration Number", "registrationDate": "Registration Date", "personalDetails": "Personal Details", "preferredOutletSalesAdvisor": "Preferred Outlet Sales Advisor", "purchaseDetails": "Purchase Details", "vehicleInfoTab": "Vehicle Info", "invoiceInfoTab": "Invoice Info", "serviceRightsTab": "Service & Rights Info", "paymentInfoTab": "Payment Info", "insuranceInfoTab": "Insurance Info", "otrFeesTab": "OTR Fees", "accessoriesInfo": "Accessories Info", "accessoryName": "Accessory Name", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Total Price", "accessoriesTotalAmount": "Accessories Total Amount", "invoiceInfo": "Invoice Info", "invoiceNumber": "Invoice Number", "invoiceDate": "Invoice Date", "invoiceAmount": "Invoice Amount", "invoiceStatus": "Invoice Status", "invoiceCompany": "Invoice Company", "invoiceAddress": "Invoice Address", "serviceRightsInfo": "Service & Rights Info", "serviceName": "Service Name", "serviceType": "Service Type", "servicePrice": "Service Price", "validityPeriod": "Validity Period", "serviceStatus": "Service Status", "servicesTotalAmount": "Services Total Amount", "totalInvoicePrice": "Vehicle Invoice Price", "downPaymentAmount": "Down Payment Amount", "balanceAmount": "Balance Amount", "otrFeesInfo": "OTR Fees Info", "ticketNumber": "Ticket Number", "feeItem": "<PERSON><PERSON>em", "feePrice": "<PERSON><PERSON>", "otrFeesTotalAmount": "OTR Fees Total Amount", "policyNumber": "Policy Number", "insurancePrice": "Insurance Price", "insurancesTotalAmount": "Insurance Total Amount", "insuranceNotes": "Insurance Notes", "changeRecordsInfo": "Change Records Info", "changeRecordsTab": "Change Records", "originalContent": "Original Content", "changedContent": "Changed Content", "operator": "Operator", "operationTime": "Operation Time", "vehicleInvoicePrice": "Vehicle Invoice Price", "remainingReceivable": "Remaining Receivable", "backToList": "Back to List", "ordererNameField": "Orderer Name", "ordererPhoneField": "Orderer Phone", "buyerNameField": "Buyer Name", "buyerPhoneField": "Buyer Phone", "buyerIdType": "ID Type", "buyerIdNumber": "ID Number", "buyerEmail": "Email", "buyerAddress": "Address", "buyerState": "State", "buyerCity": "City", "buyerPostcode": "Postcode", "region": "Region", "dealerCity": "Dealer City", "salesConsultant": "Sales Consultant", "storeInfo": "Store Info", "purchaseInfo": "Purchase Info", "vehiclePrice": "Vehicle Price", "deliveryDate": "Delivery Date", "effectiveDateField": "Effective Date", "expirationDateField": "Expiration Date"}, "vehicleModel": {"title": "Vehicle Model Master Data Management", "model": "Model", "variantName": "Variant Name", "variantCode": "Variant Code", "colourName": "Color Name", "colourCode": "Color Code", "fmrid": "FMRID", "createTime": "Create Time", "updateTime": "Update Time", "syncData": "Sync Data", "syncLog": "Sync Log", "exportData": "Export Data", "syncSuccess": "Data synchronized successfully", "exportSuccess": "Data exported successfully", "fmridPlaceholder": "Please enter FMRID", "syncLogTitle": "Data Sync Log"}, "factoryProspect": {"title": "Factory Prospect Pool Management", "storeUnit": "Store", "notSet": "Not Set", "notConverted": "Not Converted", "storeCountTypes": {"single": "Single Store", "multiple": "Multiple Stores", "all": "All Stores"}, "statistics": {"totalLeadCount": "Total Leads", "hLevelProspectCount": "H-Level Prospects", "monthlyDealProspectCount": "Monthly Deals", "crossStoreCustomerCount": "Cross-Store Prospects", "vsLastMonth": "vs Last Month", "conversionRate": "Conversion Rate", "ratio": "<PERSON><PERSON>"}, "search": {"leadId": "Prospect ID", "inputLeadId": "Please enter Prospect ID", "store": "Store", "allStores": "All Stores", "storeCount": "Associated Store Count", "allStoreCount": "All", "singleStore": "Single Store", "multiStore": "Multiple Stores", "registrationTime": "Registration Time", "search": "Search", "reset": "Reset", "export": "Export Data"}, "viewType": {"all": "All Prospects", "crossStore": "Cross-Store Prospects", "noIntention": "No Intention Prospects", "converted": "Converted"}, "table": {"index": "Index", "leadId": "Prospect ID", "customerName": "Customer Name", "phoneNumber": "Phone Number", "associatedStoreCount": "Associated Store Count", "registrationTime": "Registration Time", "prospectStatus": "Prospect Status", "actions": "Actions", "detail": "Detail"}, "messages": {"resetSuccess": "Filter conditions have been reset", "exportStart": "Exporting data, please wait...", "exportSuccess": "Data exported successfully", "exportFailed": "Failed to export data", "fetchStatsFailed": "Failed to fetch statistics", "fetchListFailed": "Failed to fetch prospect list"}, "common": {"all": "All", "noData": "N/A", "notSet": "Not Set", "notConverted": "Not Converted", "storeUnit": " stores", "exporting": "Exporting data, please wait...", "resetSuccess": "Filter conditions have been reset", "exportSuccess": "Data exported successfully", "exportFailed": "Failed to export data", "fetchStatsFailed": "Failed to fetch statistics", "fetchDataFailed": "Failed to fetch data"}, "customerDetail": {"title": "Customer Details", "close": "Close", "tabs": {"basic": "Basic Info", "stores": "Store Association", "followup": "Follow-up Records", "testdrive": "Test Drive Records", "failed": "No Intention Records", "history": "Change History", "analytics": "Performance Analysis"}, "basicInfo": {"customerId": "Customer ID", "customerName": "Customer Name", "phoneNumber": "Phone Number", "idType": "ID Type", "idNumber": "ID Number", "email": "Email", "registerTime": "Registration Time", "registerSource": "Registration Source", "currentStatus": "Current Status"}, "storeAssociation": {"title": "Store Association Records", "storeCount": "Total {count} stores", "associationTime": "Association Time", "associationReason": "Association Reason", "currentAdvisor": "Current Advisor", "lastFollowUp": "Last Follow-up", "noFollowUp": "No Follow-up", "level": " Level"}, "followUpTable": {"store": "Store", "advisor": "Sales Advisor", "method": "Follow-up Method", "time": "Follow-up Time", "intentLevel": "Intent Level", "details": "Follow-up Details"}, "testDriveTable": {"store": "Store", "driver": "Driver", "phone": "Phone Number", "model": "Test Drive Model", "variant": "Test Drive Variant", "time": "Test Drive Time", "feedback": "Customer <PERSON><PERSON><PERSON>"}, "defeatTable": {"markTime": "<PERSON>", "store": "Store", "marker": "<PERSON><PERSON>", "reason": "Defeat Reason", "applyTime": "Application Time", "applicant": "Applicant", "status": "Approval Status", "auditTime": "Audit Time", "auditor": "Auditor", "approved": "Approved", "rejected": "Rejected", "pending": "Pending"}, "historyTable": {"changeTime": "Change Time", "store": "Store", "changeType": "Change Type", "operator": "Operator", "oldValue": "Before", "newValue": "After", "reason": "Change Reason"}, "analytics": {"totalFollowUp": "Total Follow-ups", "mostActiveStore": "Most Active Store", "crossStores": "Across {count} stores", "noStoreData": "No store data", "noStore": "None", "followUpTimes": "{count} follow-ups", "noFollowUp": "No follow-up"}, "messages": {"fetchDetailFailed": "Failed to fetch customer details"}}}, "vehicleRegistration": {"title": "Vehicle Registration Management", "pageTitle": "Vehicle Registration Management", "search": {"orderNumber": "Order Number", "customerName": "Customer Name", "customerPhone": "Customer Phone", "registrationStatus": "Registration Status", "vin": "VIN", "insuranceStatus": "Insurance Status", "salesAdvisor": "Sales Advisor", "pushTimeRange": "Push Time Range", "pushTimeStart": "Push Start Time", "pushTimeEnd": "Push End Time", "searchButton": "Search", "resetButton": "Reset", "exportButton": "Export", "batchPushButton": "<PERSON><PERSON>", "refreshButton": "Refresh", "placeholder": {"orderNumber": "Please enter order number", "customerName": "Please enter customer name", "customerPhone": "Please enter customer phone", "vin": "Please enter VIN", "registrationStatus": "Please select registration status", "insuranceStatus": "Please select insurance status", "salesAdvisor": "Please select sales advisor"}}, "table": {"orderNumber": "Order Number", "customerName": "Customer Name", "customerPhone": "Customer Phone", "vin": "VIN", "vehicleModel": "Vehicle Model", "vehicleColor": "Vehicle Color", "insuranceStatus": "Insurance Status", "companyName": "Insurance Company", "registrationStatus": "Registration Status", "lastPushTime": "Last Push Time", "registrationFee": "Registration Fee", "salesAdvisor": "Sales Advisor", "createdAt": "Created At", "actions": "Actions", "selectAll": "Select All", "selectedCount": "Selected {count} items", "totalCount": "Total {total} records", "serialNumber": "No."}, "actions": {"viewDetail": "View Details", "push": "<PERSON><PERSON>", "retryPush": "<PERSON><PERSON>", "batchPush": "<PERSON><PERSON>", "export": "Export", "refresh": "Refresh"}, "status": {"pending": "Pending", "processing": "Processing", "success": "Success", "failed": "Failed", "all": "All Status"}, "insurance": {"insured": "Insured", "not_insured": "Not Insured", "all": "All Status"}, "dialog": {"detail": {"title": "Vehicle Registration Details", "orderInfo": "Order Information", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "insuranceInfo": "Insurance Information", "jpjInfo": "JPJ Registration Information", "operationLogs": "Operation Logs", "closeButton": "Close"}, "pushConfirm": {"title": "Push Confirmation", "content": "Are you sure to push the selected vehicle registration information to JPJ system?", "singleContent": "Are you sure to push this vehicle registration information to JPJ system?", "batchContent": "Are you sure to batch push the selected {count} vehicle registration records to JPJ system?", "confirmButton": "Confirm <PERSON><PERSON>", "cancelButton": "Cancel", "selectedRecords": "Selected Records", "moreRecords": "{count} more records..."}, "retryPush": {"title": "Retry Push Confirmation", "content": "This registration failed to push before, are you sure to retry pushing to JPJ system?", "confirmButton": "Confirm <PERSON><PERSON> Push", "cancelButton": "Cancel", "failureReason": "Failure Reason", "recordInfo": "Record Information"}}, "fields": {"orderInfo": {"orderNumber": "Order Number", "orderStatus": "Order Status", "lastPushTime": "Last Push Time", "createdAt": "Created At", "salesAdvisor": "Sales Advisor"}, "customerInfo": {"name": "Customer Name", "idType": "ID Type", "idNumber": "ID Number", "phone": "Contact Phone", "email": "Email Address", "address": "Detailed Address", "city": "City", "postcode": "Postcode", "state": "State/Province"}, "vehicleInfo": {"vin": "VIN", "model": "Model", "color": "Color", "engineNumber": "Engine Number", "modelCode": "Model Code", "variant": "<PERSON><PERSON><PERSON>", "productionYear": "Production Year", "manufactureDate": "Manufacture Date"}, "insuranceInfo": {"status": "Insurance Status", "company": "Insurance Company", "policyNumber": "Policy Number", "period": "Insurance Period", "date": "Insurance Date", "fee": "Insurance Fee"}, "jpjInfo": {"status": "Registration Status", "certificateNumber": "Registration Certificate Number", "pushTime": "Push Time", "completionTime": "Completion Time", "operator": "Operator", "failureReason": "Failure Reason"}, "operationLog": {"operationTime": "Operation Time", "operationType": "Operation Type", "operatorName": "Operator", "result": "Result", "remark": "Remark"}}, "messages": {"success": {"pushSuccess": "Push successful", "retryPushSuccess": "Retry push successful", "batchPushSuccess": "Batch push successful", "exportSuccess": "Export successful", "refreshSuccess": "Refresh successful"}, "error": {"pushFailed": "Push failed, please try again", "retryPushFailed": "Retry push failed, please try again", "batchPushFailed": "Batch push failed, please try again", "exportFailed": "Export failed, please try again", "loadFailed": "Data loading failed, please try again", "detailLoadFailed": "Detail loading failed, please try again", "noSelection": "Please select records to operate first", "networkError": "Network error, please check network connection"}, "warning": {"noData": "No data available", "confirmOperation": "Please confirm operation", "unsavedChanges": "There are unsaved changes", "noPushableItems": "No pushable items in selected records"}, "info": {"loading": "Loading...", "pushing": "Pushing...", "exporting": "Exporting...", "processing": "Processing..."}}, "units": {"currency": "RM", "count": "items", "page": "page"}}, "invoiceManagement": {"title": "Invoice Management", "invoiceDetail": "Invoice Detail", "emailConfirm": "Email Confirmation", "confirmSendEmail": "Confirm to send invoice email to customer?", "companyInfo": "Company Information", "state": "State", "city": "City", "postcode": "Postcode", "financeInfo": "Finance Information", "financeType": "Finance Type", "priceStructureDetails": "Price Structure Details", "optionalAccessories": "Optional Accessories", "category": "Category", "totalPrice": "Total Price", "totalAccessoryAmount": "Total Accessory Amount", "subtotal": "Subtotal", "otrRegistrationFees": "OTR Registration Fees", "billNumber": "<PERSON>", "feeItem": "<PERSON><PERSON>em", "feePrice": "<PERSON><PERSON>", "totalOtrFeeAmount": "Total OTR Fee Amount", "insurancePremium": "Insurance Premium", "totalSalesPrice": "Total Sales Price", "businessType": "Business Type", "serialNumber": "Serial Number", "channel": "Channel", "amount": "Amount", "remark": "Remark", "invoiceNumber": "Invoice Number", "invoiceNumberPlaceholder": "Please enter invoice number", "invoiceDate": "Invoice Date", "orderNumber": "Order Number", "orderNumberPlaceholder": "Please enter order number", "customerName": "Customer Name", "customerNamePlaceholder": "Please enter customer name", "customerPhone": "Customer Phone", "customerPhonePlaceholder": "Please enter customer phone", "customerEmail": "Customer <PERSON><PERSON>", "customerEmailPlaceholder": "Please enter customer email", "customerAddress": "Customer Address", "customerState": "State", "customerCity": "City", "customerPostcode": "Postcode", "deliveryNumber": "Delivery Number", "salesConsultantId": "Sales Consultant ID", "contactPhone": "Contact Phone", "contactEmail": "Contact Email", "companyAddress": "Company Address", "vin": "VIN", "vinPlaceholder": "Please enter VIN", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "salesStore": "Sales Store", "salesConsultant": "Sales Consultant", "paymentMethod": "Payment Method", "financeCompany": "Finance Company", "loanAmount": "<PERSON><PERSON>", "invoiceAmount": "Invoice Amount", "createdTime": "Created Time", "salesType": "Sales Type", "export": "Export", "batchPrint": "Batch Print", "detail": "Detail", "print": "Print", "email": "Email", "log": "Log", "pleaseSelectRecords": "Please select records to operate", "printSuccess": "Print successful", "printFailed": "Print failed", "batchPrintSuccess": "Batch print successful", "batchPrintFailed": "Batch print failed", "emailSentSuccess": "<PERSON>ail sent successfully", "emailSentFailed": "Email sending failed", "exportSuccess": "Export successful", "exportFailed": "Export failed", "detailTitle": "Invoice Details", "basicInfo": "Basic Information", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "salesInfo": "Sales Information", "priceDetails": "Price Details", "accessoryDetails": "Accessory Details", "receiptDetails": "Receipt Details", "invoiceCompany": "Invoice Company", "gstNumber": "GST Number", "sstNumber": "SST Number", "engineNumber": "Engine Number", "chassisNumber": "<PERSON><PERSON><PERSON> Number", "tinNumber": "TIN Number", "modelCode": "Model Code", "modelDescription": "Model Description", "engineDisplacement": "Engine Displacement", "vehicleRegistrationDate": "Vehicle Registration Date", "insuranceInfo": "Insurance Information", "insuranceCompany": "Insurance Company", "agentCode": "Agent Code", "policyNumber": "Policy Number", "issueDate": "Issue Date", "insuranceAmount": "Insurance Amount", "licensePlateFee": "License Plate Fee", "otrFeeDetails": "OTR Fee Details", "feeCode": "Fee Code", "feeType": "Fee Type", "taxAmount": "Tax Amount", "effectiveDate": "Effective Date", "expiryDate": "Expiry Date", "receiptNo": "Receipt No", "collectionType": "Collection Type", "loanPeriod": "Loan Period", "vehicleSalesPrice": "Vehicle Sales Price", "accessoryAmount": "Accessory Amount", "otrAmount": "OTR Amount", "adjustmentAmount": "Adjustment Amount", "invoiceNetValue": "Invoice Net Value", "specification": "Specification", "accessoryName": "Accessory Name", "unitPrice": "Unit Price", "quantity": "Quantity", "receiptNumber": "Receipt Number", "receiptType": "Receipt Type", "paymentChannel": "Payment Channel", "paidAmount": "<PERSON><PERSON>", "arrivalTime": "Arrival Time", "remarks": "Remarks", "emailTitle": "Send Invoice Email", "recipientEmail": "Recipient Email", "emailPlaceholder": "Please enter email address", "emailSubject": "Email Subject", "emailContent": "Email Content", "emailContentPlaceholder": "Please enter email content", "emailRequired": "Please enter email address", "emailFormatError": "Invalid email format", "subjectRequired": "Please enter email subject", "contentRequired": "Please enter email content", "defaultEmailSubject": "Invoice {invoiceNumber} - Perodua", "defaultEmailContent": "Dear {customerName},\n\nThank you for purchasing a Perodua vehicle. Please find your invoice information below:\n\nInvoice Number: {invoiceNumber}\nInvoice Date: {invoiceDate}\nInvoice Amount: {invoiceAmount}\n\nIf you have any questions, please feel free to contact us.\n\nThank you!\nPerodua Sales Team", "exportTitle": "Export Configuration", "exportFormat": "Export Format", "exportScope": "Export <PERSON>", "exportFields": "Export Fields", "currentPage": "Current Page Data", "filteredData": "Filtered Data", "allData": "All Data", "selectAll": "Select All", "clearAll": "Clear All", "startExport": "Start Export", "formatRequired": "Please select export format", "scopeRequired": "Please select export scope", "fieldsRequired": "Please select at least one export field", "logTitle": "Operation Log", "operationType": "Operation Type", "operator": "Operator", "operationTime": "Operation Time", "operationDescription": "Operation Description", "operationResult": "Operation Result", "errorMessage": "Error Message", "noLogData": "No log data", "viewDetail": "View Detail", "emailSend": "Send Email", "exportData": "Export Data", "create": "Create", "update": "Update", "delete": "Delete", "success": "Success", "failed": "Failed", "pending": "Pending"}}