{"title": "Vehicle Registration Management", "pageTitle": "Vehicle Registration Management", "search": {"orderNumber": "Order Number", "customerName": "Customer Name", "customerPhone": "Customer Phone", "registrationStatus": "Registration Status", "vin": "VIN", "insuranceStatus": "Insurance Status", "salesAdvisor": "Sales Advisor", "pushTimeRange": "Push Time Range", "pushTimeStart": "Push Start Time", "pushTimeEnd": "Push End Time", "searchButton": "Search", "resetButton": "Reset", "exportButton": "Export", "batchPushButton": "Batch Push", "refreshButton": "Refresh", "placeholder": {"orderNumber": "Please enter order number", "customerName": "Please enter customer name", "customerPhone": "Please enter customer phone", "vin": "Please enter VIN", "registrationStatus": "Please select registration status", "insuranceStatus": "Please select insurance status", "salesAdvisor": "Please select sales advisor"}}, "table": {"orderNumber": "Order Number", "customerName": "Customer Name", "customerPhone": "Customer Phone", "vin": "VIN", "vehicleModel": "Vehicle Model", "vehicleColor": "Vehicle Color", "insuranceStatus": "Insurance Status", "companyName": "Insurance Company", "registrationStatus": "Registration Status", "lastPushTime": "Last Push Time", "registrationFee": "Registration Fee", "salesAdvisor": "Sales Advisor", "createdAt": "Created At", "actions": "Actions", "selectAll": "Select All", "selectedCount": "Selected {count} items", "totalCount": "Total {total} records", "serialNumber": "No."}, "actions": {"viewDetail": "View Details", "push": "Push", "retryPush": "Retry Push", "batchPush": "Batch Push", "export": "Export", "refresh": "Refresh"}, "status": {"pending": "Pending", "processing": "Processing", "success": "Success", "failed": "Failed", "all": "All Status"}, "insurance": {"insured": "Insured", "not_insured": "Not Insured", "all": "All Status"}, "dialog": {"detail": {"title": "Vehicle Registration Details", "orderInfo": "Order Information", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "insuranceInfo": "Insurance Information", "jpjInfo": "JPJ Registration Information", "operationLogs": "Operation Logs", "closeButton": "Close"}, "pushConfirm": {"title": "Push Confirmation", "content": "Are you sure to push the selected vehicle registration information to JPJ system?", "singleContent": "Are you sure to push this vehicle registration information to JPJ system?", "batchContent": "Are you sure to batch push the selected {count} vehicle registration records to JPJ system?", "confirmButton": "Confirm Push", "cancelButton": "Cancel", "selectedRecords": "Selected Records", "moreRecords": "{count} more records..."}, "retryPush": {"title": "Retry Push Confirmation", "content": "This registration failed to push before, are you sure to retry pushing to JPJ system?", "confirmButton": "Confirm Retry Push", "cancelButton": "Cancel", "failureReason": "Failure Reason", "recordInfo": "Record Information"}}, "fields": {"orderInfo": {"orderNumber": "Order Number", "orderStatus": "Order Status", "lastPushTime": "Last Push Time", "createdAt": "Created At", "salesAdvisor": "Sales Advisor"}, "customerInfo": {"name": "Customer Name", "idType": "ID Type", "idNumber": "ID Number", "phone": "Contact Phone", "email": "Email Address", "address": "Detailed Address", "city": "City", "postcode": "Postcode", "state": "State/Province"}, "vehicleInfo": {"vin": "VIN", "model": "Model", "color": "Color", "engineNumber": "Engine Number", "modelCode": "Model Code", "variant": "Variant", "productionYear": "Production Year", "manufactureDate": "Manufacture Date"}, "insuranceInfo": {"status": "Insurance Status", "company": "Insurance Company", "policyNumber": "Policy Number", "period": "Insurance Period", "date": "Insurance Date", "fee": "Insurance Fee"}, "jpjInfo": {"status": "Registration Status", "certificateNumber": "Registration Certificate Number", "pushTime": "Push Time", "completionTime": "Completion Time", "operator": "Operator", "failureReason": "Failure Reason"}, "operationLog": {"operationTime": "Operation Time", "operationType": "Operation Type", "operatorName": "Operator", "result": "Result", "remark": "Remark"}}, "messages": {"success": {"pushSuccess": "Push successful", "retryPushSuccess": "Retry push successful", "batchPushSuccess": "Batch push successful", "exportSuccess": "Export successful", "refreshSuccess": "Refresh successful"}, "error": {"pushFailed": "Push failed, please try again", "retryPushFailed": "Retry push failed, please try again", "batchPushFailed": "Batch push failed, please try again", "exportFailed": "Export failed, please try again", "loadFailed": "Data loading failed, please try again", "detailLoadFailed": "Detail loading failed, please try again", "noSelection": "Please select records to operate first", "networkError": "Network error, please check network connection"}, "warning": {"noData": "No data available", "confirmOperation": "Please confirm operation", "unsavedChanges": "There are unsaved changes", "noPushableItems": "No pushable items in selected records"}, "info": {"loading": "Loading...", "pushing": "Pushing...", "exporting": "Exporting...", "processing": "Processing..."}}, "units": {"currency": "RM", "count": "items", "page": "page"}}
{
  "title": "Vehicle Registration Management",
  "pageTitle": "Vehicle Registration Management",
  "search": {
    "orderNumber": "Order Number",
    "customerName": "Customer Name",
    "customerPhone": "Customer Phone",
    "registrationStatus": "Registration Status",
    "vin": "VIN",
    "insuranceStatus": "Insurance Status",
    "salesAdvisor": "Sales Advisor",
    "pushTimeRange": "Push Time Range",
    "pushTimeStart": "Push Start Time",
    "pushTimeEnd": "Push End Time",
    "searchButton": "Search",
    "resetButton": "Reset",
    "exportButton": "Export",
    "batchPushButton": "Batch Push",
    "refreshButton": "Refresh",
    "placeholder": {
      "orderNumber": "Please enter order number",
      "customerName": "Please enter customer name",
      "customerPhone": "Please enter customer phone",
      "vin": "Please enter VIN",
      "registrationStatus": "Please select registration status",
      "insuranceStatus": "Please select insurance status",
      "salesAdvisor": "Please select sales advisor"
    }
  },
  "table": {
    "orderNumber": "Order Number",
    "customerName": "Customer Name",
    "customerPhone": "Customer Phone",
    "vin": "VIN",
    "vehicleModel": "Vehicle Model",
    "vehicleColor": "Vehicle Color",
    "insuranceStatus": "Insurance Status",
    "companyName": "Insurance Company",
    "registrationStatus": "Registration Status",
    "lastPushTime": "Last Push Time",
    "registrationFee": "Registration Fee",
    "salesAdvisor": "Sales Advisor",
    "createdAt": "Created At",
    "actions": "Actions",
    "selectAll": "Select All",
    "selectedCount": "Selected {count} items",
    "totalCount": "Total {total} records",
    "serialNumber": "No."
  },
  "actions": {
    "viewDetail": "View Details",
    "push": "Push",
    "retryPush": "Retry Push",
    "batchPush": "Batch Push",
    "export": "Export",
    "refresh": "Refresh"
  },
  "status": {
    "pending": "Pending",
    "processing": "Processing",
    "success": "Success",
    "failed": "Failed",
    "all": "All Status"
  },
  "insurance": {
    "insured": "Insured",
    "not_insured": "Not Insured",
    "all": "All Status"
  },
  "dialog": {
    "detail": {
      "title": "Vehicle Registration Details",
      "orderInfo": "Order Information",
      "customerInfo": "Customer Information",
      "vehicleInfo": "Vehicle Information",
      "insuranceInfo": "Insurance Information",
      "jpjInfo": "JPJ Registration Information",
      "operationLogs": "Operation Logs",
      "closeButton": "Close"
    },
    "pushConfirm": {
      "title": "Push Confirmation",
      "content": "Are you sure to push the selected vehicle registration information to JPJ system?",
      "singleContent": "Are you sure to push this vehicle registration information to JPJ system?",
      "batchContent": "Are you sure to batch push the selected {count} vehicle registration records to JPJ system?",
      "confirmButton": "Confirm Push",
      "cancelButton": "Cancel"
    },
    "retryPush": {
      "title": "Retry Push Confirmation",
      "content": "This registration failed to push before, are you sure to retry pushing to JPJ system?",
      "confirmButton": "Confirm Retry Push",
      "cancelButton": "Cancel"
    }
  },
  "fields": {
    "orderInfo": {
      "orderNumber": "Order Number",
      "orderStatus": "Order Status",
      "lastPushTime": "Last Push Time",
      "createdAt": "Created At",
      "salesAdvisor": "Sales Advisor"
    },
    "customerInfo": {
      "name": "Customer Name",
      "idType": "ID Type",
      "idNumber": "ID Number",
      "phone": "Contact Phone",
      "email": "Email Address",
      "address": "Detailed Address",
      "city": "City",
      "postcode": "Postcode",
      "state": "State/Province"
    },
    "vehicleInfo": {
      "vin": "VIN",
      "model": "Model",
      "color": "Color",
      "engineNumber": "Engine Number",
      "modelCode": "Model Code",
      "variant": "Variant",
      "productionYear": "Production Year",
      "manufactureDate": "Manufacture Date"
    },
    "insuranceInfo": {
      "status": "Insurance Status",
      "company": "Insurance Company",
      "policyNumber": "Policy Number",
      "period": "Insurance Period",
      "date": "Insurance Date",
      "fee": "Insurance Fee"
    },
    "jpjInfo": {
      "status": "Registration Status",
      "certificateNumber": "Registration Certificate Number",
      "pushTime": "Push Time",
      "completionTime": "Completion Time",
      "operator": "Operator",
      "failureReason": "Failure Reason"
    },
    "operationLog": {
      "operationTime": "Operation Time",
      "operationType": "Operation Type",
      "operatorName": "Operator",
      "result": "Result",
      "remark": "Remark"
    }
  },
  "messages": {
    "success": {
      "pushSuccess": "Push successful",
      "retryPushSuccess": "Retry push successful",
      "batchPushSuccess": "Batch push successful",
      "exportSuccess": "Export successful",
      "refreshSuccess": "Refresh successful"
    },
    "error": {
      "pushFailed": "Push failed, please try again",
      "retryPushFailed": "Retry push failed, please try again",
      "batchPushFailed": "Batch push failed, please try again",
      "exportFailed": "Export failed, please try again",
      "loadFailed": "Data loading failed, please try again",
      "detailLoadFailed": "Detail loading failed, please try again",
      "noSelection": "Please select records to operate first",
      "networkError": "Network error, please check network connection"
    },
    "warning": {
      "noData": "No data available",
      "confirmOperation": "Please confirm operation",
      "unsavedChanges": "There are unsaved changes",
      "noPushableItems": "No pushable items in selected records"
    },
    "info": {
      "loading": "Loading...",
      "pushing": "Pushing...",
      "exporting": "Exporting...",
      "processing": "Processing..."
    }
  },
  "units": {
    "currency": "RM",
    "count": "items",
    "page": "page"
  }
}