{"title": "Work Order Approval", "subtitle": "Work Order Approval Management", "searchCard": "Filter Conditions", "operationCard": "Operations", "tableCard": "Approval List", "pendingTab": "Pending", "completedTab": "Completed", "allTab": "All", "approvalNo": "Approval No.", "approvalType": "Approval Type", "approvalStatus": "Approval Status", "approvalResult": "A<PERSON><PERSON><PERSON> Result", "submitter": "Submitter", "submitTime": "Submit Time", "workOrderNo": "Work Order No.", "requestReason": "Request Reason", "customerInfo": "Customer Info", "licensePlate": "License Plate", "store": "Store", "currentLevel": "Current Level", "overtimeStatus": "Overtime Status", "approvalTime": "Approval Time", "approver": "Approver", "approvalRemark": "Approval Remark", "claimApproval": "<PERSON><PERSON><PERSON>", "cancelOrderApproval": "Cancel Order Approval", "pending": "Pending", "completed": "Completed", "approved": "Approved", "rejected": "Rejected", "firstLevel": "First Level", "secondLevel": "Second Level", "approvalNoPlaceholder": "Enter approval number", "submitterPlaceholder": "Enter submitter name", "workOrderNoPlaceholder": "Enter work order number", "customerInfoPlaceholder": "Enter customer name or phone", "licensePlatePlaceholder": "Enter license plate", "selectApprovalType": "Select approval type", "selectApprovalStatus": "Select approval status", "selectApprovalLevel": "Select approval level", "selectOvertimeStatus": "Select overtime status", "selectStore": "Select store", "selectTimeRange": "Select time range", "submitTimeStart": "Submit start time", "submitTimeEnd": "Submit end time", "approve": "Approve", "viewDetail": "View Detail", "operationLog": "Operation Log", "approvalAction": "Approval Action", "approvalRemarkPlaceholder": "Enter approval remark (required for rejection)", "approvalRemarkRequired": "Approval remark is required for rejection", "approvalSuccess": "Approval operation successful", "approvalFailed": "Approval operation failed", "exportSuccess": "Export successful", "exportFailed": "Export failed", "confirmApproval": "Confirm Approval", "approvalDetail": "Approval Detail", "basicInfo": "Basic Information", "vehicleInfo": "Vehicle Information", "claimDetails": "<PERSON><PERSON><PERSON>", "workOrderInfo": "Work Order Information", "approvalHistory": "Approval History", "claimLabor": "<PERSON><PERSON>m Labor", "claimParts": "Claim Parts", "totalClaimAmount": "Total Claim Amount", "laborCode": "Labor Code", "laborName": "Labor Name", "standardLabor": "Standard Labor", "laborPrice": "Labor Price", "partCode": "Part Code", "partName": "Part Name", "quantity": "Quantity", "unitPrice": "Unit Price", "claimAmount": "<PERSON><PERSON><PERSON>", "customerName": "Customer Name", "customerPhone": "Customer Phone", "senderName": "Sender Name", "senderPhone": "Sender Phone", "vin": "VIN", "model": "Model", "configuration": "Configuration", "color": "Color", "mileage": "Mileage", "vehicleAge": "Vehicle Age", "repairTime": "Repair Time", "saleTime": "Sale Time", "workOrderStatus": "Work Order Status", "paymentStatus": "Payment Status", "estimatedStartTime": "Estimated Start Time", "assignedTechnician": "Assigned Technician", "cancelReason": "Cancel Reason", "firstApprover": "First Approver", "firstApprovalTime": "First Approval Time", "firstApprovalResult": "First Approval Result", "firstApprovalRemark": "First Approval Remark", "secondApprover": "Second Approver", "secondApprovalTime": "Second Approval Time", "secondApprovalResult": "Second Approval Result", "secondApprovalRemark": "Second Approval Remark", "operationContent": "Operation Content", "operator": "Operator", "operationTime": "Operation Time", "operationRemark": "Operation Remark", "systemOperation": "System Operation", "submitClaimApproval": "Submit <PERSON><PERSON><PERSON>", "firstLevelApprovalPass": "First Level Approval Pass", "firstLevelApprovalReject": "First Level Approval Reject", "secondLevelApprovalPass": "Second Level Approval Pass", "secondLevelApprovalReject": "Second Level Approval Reject", "submitCancelRequest": "Submit Cancel Request", "cancelApprovalPass": "Cancel Approval Pass", "cancelApprovalReject": "Cancel Approval Reject", "hours": "hours", "yuan": "¥", "pieces": "pcs", "months": "months", "km": "km", "draft": "Draft", "waitingApproval": "Waiting Approval", "confirmed": "Confirmed", "cancelled": "Cancelled", "pendingPayment": "Pending Payment", "paid": "Paid", "technician": "Technician", "serviceAdvisor": "Service Advisor", "technicianManager": "Technician Manager", "factoryManager": "Factory Manager", "allStores": "All Stores", "allTypes": "All Types", "allStatus": "All Status", "allLevels": "All Levels", "allOvertimeStatus": "All Overtime Status", "records": "records", "currentPage": "Page", "totalPages": "Total", "pages": "pages", "itemsPerPage": "Items per page", "goToPage": "Go to page", "pageNumber": "page", "statistics": "Statistics", "totalApprovals": "Total Approvals", "pendingApprovals": "Pending Approvals", "completedApprovals": "Completed Approvals", "overtimeApprovals": "Overtime Approvals", "approvalPassRate": "Approval Pass Rate", "refreshData": "Refresh Data", "columnSettings": "<PERSON>umn <PERSON>", "tableView": "Table View", "cardView": "Card View", "advancedFilter": "Advanced Filter", "collapseFilter": "Collapse Filter", "expandFilter": "Expand Filter", "noPermission": "No Permission", "dataLoading": "Data loading...", "networkError": "Network connection failed", "serverError": "Server error", "dataError": "Data format error", "permissionDenied": "Permission denied", "dataExpired": "Data expired, please refresh", "retryRequest": "Retry Request", "reportError": "Report Error", "feedbackChannel": "<PERSON><PERSON><PERSON>", "approvalTimeout": "Approval timeout reminder", "approvalNotification": "Approval status notification", "resultNotification": "Approval result notification", "systemMessage": "System message", "emailNotification": "Email notification", "smsNotification": "SMS notification", "notificationHistory": "Notification history", "enableNotification": "Enable notification", "disableNotification": "Disable notification", "notificationSettings": "Notification settings"}