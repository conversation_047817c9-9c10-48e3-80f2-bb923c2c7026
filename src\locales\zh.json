{
  "common": {
    "confirm": "确定",
    "cancel": "取消",
    "void": "作废",
    "search": "查询",
    "operationSuccessful": "操作成功！",
    "operationFailed": "操作失败！",
    "reLoginPrompt": "登录已过期，请重新登录。",
    "warning": "警告",
    "noPermission": "您没有操作权限。",
    "networkError": "网络错误，请稍后重试。",
    "badRequest": "请求错误。",
    "unauthorized": "未授权，请重新登录。",
    "forbidden": "拒绝访问。",
    "notFound": "请求地址出错，资源未找到。",
    "requestTimeout": "请求超时。",
    "serverError": "服务器内部错误。",
    "notImplemented": "服务未实现。",
    "badGateway": "网关错误。",
    "serviceUnavailable": "服务不可用。",
    "gatewayTimeout": "网关超时。",
    "unknownError": "未知错误。",
    "noResponse": "服务器没有响应。",
    "requestSetupError": "请求配置错误。",
    "reset": "重置",
    "home": "首页",
    "afterSales": "售后服务",
    "total": "共 {total} 条记录，第 {current} 页，共 {pages} 页",
    "to": "至",
    "startDate": "开始日期",
    "endDate": "结束日期",
    "edit": "编辑",
    "add": "新增",
    "delete": "删除",
    "save": "保存",
    "operations": "操作",
    "yes": "是",
    "no": "否",
    "hour": "小时",
    "hours": "小时",
    "confirmDelete": "确定删除 '{item}' 吗？",
    "languageChanged": "语言切换成功！",
    "edit": "编辑",
    "delete": "删除",
    "startDate": "开始日期",
    "endDate": "结束日期",
    "selectDate": "选择日期",
    "confirmSave": "确定保存吗？",
    "confirmAdd": "确定新增吗？",
    "exporting": "正在导出...",
    "noData": "暂无数据",
    "query": "查询",
    "queryItems": "查询项",
    "sequence": "序号",
    "serialNo": "序号",
    "detail": "详情",
    "details": "详情",
    "scrapRecord": "报损记录",
    "view": "查看",
    "to": "至",
    "fetchFailed": "获取数据失败",
    "exportReport": "导出报表",
    "exportingReport": "报表导出中...",
    "pleaseSelectOne": "请选择一条数据",
    "pleaseSelectData": "请选择数据",
    "upload": "上传",
    "good": "良好",
    "needsAttention": "需要关注",
    "bad": "差",
    "notApplicable": "不适用",
    "all": "全部",
    "noDataTip": "当前查询条件下没有找到相关数据",
    "close": "关闭",
    "confirmExport": "确认导出",
    "success": "成功",
    "paginationLayout": "total, sizes, prev, pager, next, jumper",
    "add": "新增",
    "optional": "（可选）",
    "required": "（必填）",
    "selectPlaceholder": "请选择",
    "inputPlaceholder": "请输入",
    "loading": "加载中...",
    "items": "条",
    "page": "页",
    "assign": "分配",
    "submitConfirm": "提交确认",
    "recall": "撤回",
    "print": "打印",
    "customerConfirm": "客户确认",
    "createWorkOrder": "创建工单",
    "export": "导出",
    "save": "保存",
    "clear": "清除",
    "uploadSuccessful": "上传成功",
    "pleaseSign": "请先签名",
    "pleaseUploadSignature": "请先上传签名",
    "tip": "提示",
    "confirmSubmitConfirm": "确定要提交确认吗？",
    "confirmRecall": "确定要撤回吗？",
    "confirmPrint": "确定要打印吗？",
    "operationCanceled": "操作已取消",
    "index": "序号",
    "saveConfig": "保存配置",
    "submit": "提交",
    "submitConfirm": "提交确认",
    "fillAllRequired": "请填写所有必填项！",
    "unknown": "未知",
    "serialNumber": "序号",
    "syncTime": "同步时间",
    "status": "状态",
    "recordCount": "记录数",
    "errorMessage": "错误信息",
    "close": "关闭",
    "success": "成功",
    "failed": "失败"
  },
  "customer": {
    "detailViewTitle": "客户详情"
  },
  "menu": {
    "home": "DMS看板",
    "about": "关于",
    "customerDetail": "客户详情",
    "checkinList": "到店登记列表",
    "appointmentDashboard": "预约看板",
    "appointmentManagement": "预约管理",
    "quotaManagement": "预约限量管理",
    "salesOrder": "销售订单",
    "salesOrderEdit": "销售订单编辑",
    "salesOrderDetail": "销售订单详情",
    "inventoryReport": "库存信息",
    "inventoryReportHQ": "库存报表HQ",
    "partManagement": "零件管理",
    "partsManagementHQ": "零件管理HQ",
    "partsReceipt": "零件收货",
    "partsReceiptQuery": "零件收货查询",
    "partArchives": "零件档案",
    "partConfiguration": "零件配置",
    "inspectionForm": "环检单管理",
    "workOrder": "工单管理",
    "dispatchManagement": "派工管理",
    "workAssignmentDashboard": "派工看板",
    "vehicleQuery": "车辆查询",
    "vehicleAllocation": "车辆配车管理",
    "orderApprovalManagement": "订单审批管理",
    "deliveryManagement": "交车管理",
    "vehicleRegistration": "车辆登记",
    "invoiceManagement": "发票管理",
    "leadPoolManagement": "线索池管理",
    "orderStatisticsManagement": "订单统计管理",
    "factoryOrderManagement": "厂端订单管理",
    "potentialCustomerDefeatApprovalManagement": "潜客战败审批管理",
    "testDriveRegistration": "到店试驾登记",
    "wholeVehicleCollectionManagement": "整车收款管理",
    "salesProspect": "销售潜客管理",
    "factoryProspect": "厂端潜客池管理",
    "defeatAudit": "战败审核",
    "addProspect": "新增潜客",
    "testDriveList": "试驾登记列表",
    "workOrderApproval": "工单审批",
    "testDriveReport": "试驾报表",
    "order": "订单管理"
  },

  "customer": {
    "detailViewTitle": "客户详情"
  },
  "login": {
    "title": "登录",
    "username": "用户名",
    "password": "密码",
    "loginButton": "登录",
    "notFound": "记录未找到",
    "ownerPhone": "车主手机号",
    "confirmCreateRepairOrder": "确定为登记单 {checkinId} 创建环检单吗？"
  },
  "sales": {
    "vehicleList": "车辆列表",
    "salesOrderList": "销售订单列表",
    "id": "ID",
    "vin": "车辆识别码",
    "model": "车型",
    "brand": "品牌",
    "color": "颜色",
    "price": "价格",
    "statusLabel": "状态",
    "manufactureDate": "制造日期",
    "engineNumber": "发动机号",
    "buyerName": "购车人",
    "buyerNamePlaceholder": "请输入购车人姓名",
    "buyerPhone": "购车人手机号",
    "buyerPhonePlaceholder": "请输入购车人手机号",
    "buyerType": "购车人类别",
    "selectBuyerType": "请选择购车人类别",
    "individual": "个人",
    "company": "公司",
    "selectModel": "请选择车型",
    "orderNumber": "订单号",
    "orderNumberPlaceholder": "请输入订单号",
    "orderStatus": "订单状态",
    "selectOrderStatus": "请选择订单状态",
    "approvalStatus": "订单审核状态",
    "selectApprovalStatus": "请选择订单审核状态",
    "paymentStatus": "订单支付状态",
    "selectPaymentStatus": "请选择订单支付状态",
    "insuranceStatus": "投保状态",
    "selectInsuranceStatus": "请选择投保状态",
    "loanApprovalStatus": "贷款审核状态",
    "selectLoanApprovalStatus": "请选择贷款审核状态",
    "jpjRegistrationStatus": "JPJ车辆注册状态",
    "selectJpjRegistrationStatus": "请选择JPJ车辆注册状态",
    "createTime": "订单创建时间",
    "ordererName": "下单人",
    "ordererNamePlaceholder": "请输入下单人姓名",
    "ordererPhone": "下单人手机号",
    "ordererPhonePlaceholder": "请输入下单人手机号",
    "variant": "Variant",
    "paymentMethod": "支付方式",
    "totalPrice": "订单总金额",
    "orderAmount": "订单金额",
    "status": {
      "in_stock": "在库",
      "sold": "已售",
      "reserved": "已预定"
    },
    "models": {
      "AXIA": "AXIA",
      "BEZZA": "BEZZA",
      "MYVI": "MYVI"
    },
    "addVehicle": "新增车辆",
    "editVehicle": "编辑车辆",
    "deleteVehicle": "删除车辆",
    "vinPlaceholder": "请输入车辆识别码",
    "modelPlaceholder": "请输入车型",
    "brandPlaceholder": "请输入品牌",
    "statusPlaceholder": "请选择状态",
    "colorPlaceholder": "请输入颜色",
    "pricePlaceholder": "请输入价格",
    "manufactureDatePlaceholder": "请选择制造日期",
    "engineNumberPlaceholder": "请输入发动机号",
    "selectStatus": "请选择状态",
    "orderDetail": "订单详情",
    "orderCreateTime": "订单创建时间",
    "accessoriesInfo": "选配件信息",
    "missingOrderNumber": "缺少订单号，将返回列表页",
    "fetchDetailError": "获取订单详情失败，请重试",
    "vin": "VIN码",
    "deposit": "定金金额",
    "finalPayment": "尾款金额",
    "totalAmount": "整车开票价",
    "remainingReceivable": "剩余应收",
    "returnToList": "返回",
    "rightsInfoTab": "服务&权益信息",
    "remarks": "备注",
    "rightsTotalDiscount": "权益优惠总金额",
    "buyerTypes": {
      "individual": "个人",
      "company": "公司"
    },
    "paymentMethods": {
      "full_payment": "全款",
      "installment": "分期"
    },
    "loanApprovalStatuses": {
      "pending_review": "待审核",
      "approved": "审核通过",
      "rejected": "审核驳回"
    },
    "orderStatuses": {
      "submitted": "已提交",
      "confirmed": "已确认",
      "cancel_pending": "取消审核中",
      "cancel_approved": "取消审核通过",
      "cancelled": "已取消",
      "pending_delivery": "待交车",
      "delivered": "已交车"
    },
    "orderStatus": {
      "submitted": "已提交",
      "confirmed": "已确认",
      "cancelPending": "取消审核中",
      "cancelApproved": "取消审核通过",
      "canceled": "已取消",
      "pendingDelivery": "待交车",
      "delivered": "已交车"
    },
    "approvalStatuses": {
      "pending_approval": "待审批",
      "approved": "已审批"
    },
    "approvalStatus": {
      "pendingApproval": "待审批",
      "approved": "已审批"
    },
    "paymentStatuses": {
      "pending_deposit": "待支付定金",
      "deposit_paid": "已支付定金",
      "pending_balance": "待支付尾款",
      "balance_paid": "已支付尾款",
      "refunding": "退款中",
      "refunded": "退款完成"
    },
    "paymentStatus": {
      "pendingDeposit": "待支付定金",
      "depositPaid": "已支付定金",
      "pendingFinalPayment": "待支付尾款",
      "balancePaid": "已支付尾款",
      "refunding": "退款中",
      "refundCompleted": "退款完成"
    },
    "insuranceStatuses": {
      "not_insured": "未投保",
      "insuring": "投保中",
      "insured": "已投保"
    },
    "jpjRegistrationStatuses": {
      "pending_registration": "待登记",
      "registering": "登记中",
      "registered": "已登记",
      "registration_failed": "登记失败"
    }
  },
  "checkin": {
    "checkinList": "到店登记列表",
    "checkinId": "登记单号",
    "checkinIdPlaceholder": "请输入登记单号",
    "licensePlate": "车牌号",
    "licensePlatePlaceholder": "请输入车牌号",
    "vin": "VIN码",
    "vinPlaceholder": "请输入VIN码",
    "repairPersonName": "送修人名称",
    "repairPersonNamePlaceholder": "请输入送修人名称",
    "repairPersonPhone": "送修人手机号",
    "repairPersonPhonePlaceholder": "请输入送修人手机号",
    "createdAt": "创建时间",
    "createdAtPlaceholder": "请选择创建日期范围",
    "export": "导出",
    "createRecord": "创建登记单",
    "id": "序号",
    "checkinId": "登记单号",
    "vehicleModel": "车型",
    "vehicleConfiguration": "配置",
    "color": "颜色",
    "mileage": "里程数",
    "mileagePlaceholder": "请输入里程数",
    "mileageUnit": "公里",
    "serviceAdvisor": "服务顾问",
    "relatedRepairOrderId": "关联环检单号",
    "serviceType": "服务类型",
    "notes": "备注",
    "viewDetails": "查看详情",
    "edit": "编辑",
    "delete": "删除",
    "createRepairOrder": "创建环检单",
    "addCheckinRecord": "新增到店登记单",
    "editCheckinRecord": "编辑到店登记单",
    "vehicleInfoNotFound": "未找到相关车辆信息",
    "vehicleInfoAutoFill": "自动填充或手动填写",
    "vehicleAge": "车龄(月)",
    "ownerInfo": "车主信息",
    "serviceTypePlaceholder": "请选择服务类型",
    "notesPlaceholder": "请输入备注",
    "save": "保存",
    "cancel": "取消",
    "serviceTypeOptions": {
        "repair": "维修",
        "maintenance": "保养",
        "inspection": "检测",
        "paint": "喷漆"
    },
    "repairOrderAlreadyExists": "该到店登记单已存在关联环检单。",
    "repairOrderCreatedSuccess": "环检单创建成功！",
    "notFound": "记录未找到",
    "ownerPhone": "车主手机号",
    "updatedAt": "更新时间"
  },
  "afterSales": {
    "appointmentManagement": "预约管理",
    "breadcrumb": "售后服务 / 预约管理",
    "status": "预约状态",
    "selectStatus": "请选择预约状态",
    "serviceType": "服务类型",
    "selectServiceType": "请选择服务类型",
    "serviceAdvisor": "服务顾问",
    "selectServiceAdvisor": "请选择服务顾问",
    "appointmentNo": "预约单号",
    "appointmentNoPlaceholder": "请输入预约单号",
    "customerName": "客户姓名",
    "customerNamePlaceholder": "请输入客户姓名",
    "customerPhone": "客户手机号",
    "customerPhonePlaceholder": "请输入客户手机号",
    "createTime": "创建日期",
    "createTimePlaceholder": "请选择创建日期",
    "export": "导出",
    "createAppointment": "新增预约",
    "serialNumber": "序号",
    "licensePlate": "车牌号",
    "vehicleModel": "车型",
    "vehicleColor": "颜色",
    "expectedArrivalTime": "预计到店日期",
    "actualArrivalTime": "实际到店日期",
    "operations": "操作",
    "viewDetails": "查看详情",
    "edit": "编辑",
    "cancelAppointment": "取消预约",
    "checkIn": "到店",
    "statuses": {
      "not_arrived": "未到店",
      "arrived": "已到店",
      "cancelled": "已取消",
      "no_show": "未履约",
      "pending_payment": "待支付"
    },
    "serviceTypes": {
      "maintenance": "保养",
      "repair": "维修"
    },
    "confirmCancel": "确定取消此预约吗？",
    "appointmentDetail": "预约详情",
    "appointmentId": "预约单号",
    "appointmentTime": "预约日期",
    "customerInfo": "客户信息",
    "vehicleInfo": "车辆信息",
    "mileage": "里程数",
    "appointmentInfo": "预约信息",
    "store": "门店",
    "technician": "技师",
    "serviceContent": "服务内容",
    "paymentInfo": "支付信息",
    "paymentStatus": "支付状态",
    "paymentAmount": "支付金额",
    "paymentOrderNumber": "支付流水号",
    "paymentStatuses": {
      "paid": "已支付",
      "unpaid": "未支付",
      "refunded": "已退款"
    },
    "viewMore": "查看更多"
  },
  "orderApproval": {
    "pageTitle": "订单审批",
    "pendingTab": "待审批",
    "approvedTab": "已审批",
    "searchTitle": "筛选条件",
    "approvalType": "审批类型",
    "approvalTypePlaceholder": "请选择审批类型",
    "cancelOrderApproval": "取消订单审批",
    "modifyOrderApproval": "修改订单审批",
    "vehicleColorModificationApproval": "车辆颜色修改审批",
    "orderNumber": "订单编号",
    "orderNumberPlaceholder": "请输入订单编号",
    "submittedBy": "提交人",
    "submittedByPlaceholder": "请输入提交人姓名",
    "submissionDate": "提交日期",
    "submissionDatePlaceholder": "请选择提交日期",
    "approvalStatus": "审批状态",
    "approvalStatusPlaceholder": "请选择审批状态",
    "approvedBy": "审批人",
    "approvedByPlaceholder": "请输入审批人姓名",
    "approvalDate": "审批日期",
    "approvalDatePlaceholder": "请选择审批日期",
    "export": "导出",
    "pendingApprovalList": "待审批列表",
    "approvedList": "已审批列表",
    "serialNumber": "序号",
    "approvalContent": "审批内容",
    "currentStatus": "当前状态",
    "actions": "操作",
    "viewDetail": "查看详情",
    "approve": "审批",
    "reject": "拒绝",
    "reasonsForRejection": "拒绝原因",
    "reasonsForRejectionPlaceholder": "请输入拒绝原因",
    "confirmApproval": "确认审批",
    "confirmReject": "确认拒绝",
    "approvalDetail": "审批详情",
    "detailTitle": "审批详情",
    "approvalForm": "审批表单",
    "approvalRecord": "审批记录",
    "type": "类型",
    "submitter": "提交人",
    "submissionTime": "提交时间",
    "submissionTimeEnd": "提交结束时间",
    "pendingInitialReview": "待初审",
    "pendingFinalReview": "待终审",
    "aboutToTimeout": "即将超时",
    "timeoutRejected": "超时驳回",
    "approver": "审批人",
    "approvalTime": "审批时间",
    "remarks": "备注",
    "rejectionReason": "拒绝原因",
    "status": {
      "pending": "待审批",
      "approved": "已审批",
      "rejected": "已拒绝"
    },
    "selectApprovalType": "请选择审批类型",
    "selectApprovalStatus": "请选择审批状态",
    "approvedBy": "审批人",
    "approve": "批准",
    "reject": "驳回",
    "approvalHistory": "审核历史",
    "applicationReason": "申请原因",
    "fetchDataFailed": "获取审核列表失败",
    "orderDetailNotImplemented": "订单详情功能暂未实现",
    "approveSuccess": "批准成功",
    "rejectSuccess": "驳回操作成功",
    "batchOperationSuccess": "批量操作成功",
    "batchOperationFailed": "批量操作失败",
    "review": "审核",
    "exportSuccess": "导出成功",
    "exportFailed": "导出失败",
    "selectedCount": "已选择 {count} 项",
    "cancelReason": "取消原因",
    "changeDetails": "变更内容",
    "changedField": "变更字段",
    "originalValue": "原始数据",
    "newValue": "变更后数据",
    "approvalInfo": "审批信息",
    "approvalComment": "审批备注"
  },
  "vehicleAllocation": {
    "title": "车辆配车管理",
    "orderNumber": "订单号",
    "orderNumberPlaceholder": "请输入订单号",
    "customerName": "客户姓名",
    "customerNamePlaceholder": "请输入客户姓名",
    "customerPhone": "客户手机",
    "customerPhonePlaceholder": "请输入客户手机",
    "allocationStatus": "配车状态",
    "allocationStatusPlaceholder": "请选择配车状态",
    "allocated": "已配车",
    "unallocated": "未配车",
    "orderStatus": "订单状态",
    "orderStatusPlaceholder": "请选择订单状态",
    "vin": "VIN",
    "vinPlaceholder": "请输入VIN",
    "factoryOrderNumber": "工厂订单号",
    "factoryOrderNumberPlaceholder": "请输入工厂订单号",
    "allocationTime": "配车时间",
    "salesConsultant": "销售顾问",
    "salesConsultantPlaceholder": "请选择销售顾问",
    "orderCreateTime": "订单创建时间",
    "model": "车型",
    "variant": "配置",
    "color": "颜色",
    "allocate": "配车",
    "cancelAllocate": "取消配车",
    "records": "记录",
    "exportData": "导出数据",
    "confirmExport": "确定要导出当前筛选条件下的数据吗？",
    "allocationConfirm": "配车确认",
    "orderDetail": "订单详情",
    "availableVehiclesQuery": "可配车辆查询",
    "availableVehicles": "可配车辆列表",
    "warehouseName": "所在仓库",
    "inStockTime": "入库时间",
    "noVehicleSelected": "请选择一辆车进行配车",
    "vehicleConfigMismatch": "所选车辆的配置与订单不符，无法配车",
    "allocationSuccess": "配车成功",
    "allocationFailed": "配车失败",
    "confirmCancelAllocation": "确定要取消该订单的配车吗？",
    "cancelAllocationSuccess": "取消配车成功",
    "cancelAllocationFailed": "取消配车失败",
    "allocationRecordDetail": "配车记录详情",
    "allocationHistory": "配车历史",
    "operator": "操作人",
    "remarks": "备注",
    "orderStatuses": {
      "submitted": "已提交",
      "confirmed": "已确认",
      "cancel_review": "取消审核中",
      "cancel_approved": "取消已批准",
      "cancelled": "已取消",
      "ready_delivery": "准备交付",
      "delivered": "已交付"
    },
    "operationTypes": {
      "allocate": "配车",
      "cancel_allocate": "取消配车",
      "view_detail": "查看详情",
      "system_auto_cancel": "系统自动取消"
    },
    "processResults": {
      "success": "成功",
      "failed": "失败"
    },
    "cancelReasonLabel": "取消原因",
    "cancelReasonPlaceholder": "请输入取消配车原因",
    "reasonRequired": "必须填写取消原因"
  },
  "workAssignment": {
    "pageTitle": "工单分配",
    "workOrderNo": "工单号",
    "taskName": "任务名称",
    "assignee": "分配人",
    "status": "状态",
    "createTime": "创建时间",
    "addWorkAssignment": "新增工单分配",
    "editWorkAssignment": "编辑工单分配",
    "workAssignmentDetails": "工单分配详情",
    "taskNamePlaceholder": "请输入任务名称",
    "assigneePlaceholder": "请输入分配人姓名",
    "statusPlaceholder": "请选择状态",
    "confirmAdd": "确定新增工单分配吗？",
    "confirmEdit": "确定修改工单分配吗？",
    "statusOptions": {
      "pending": "待处理",
      "inProgress": "处理中",
      "completed": "已完成",
      "cancelled": "已取消"
    },
    "dashboard": {
      "title": "派工看板",
      "autoRefresh": "自动刷新",
      "pendingAssignment": "待派工",
      "assignedOrders": "已分配",
      "inProgressOrders": "进行中",
      "completedOrders": "已完成",
      "availableTechnicians": "可用技师",
      "averageEfficiency": "平均效率",
      "utilizationRate": "利用率",
      "averageWaitingTime": "平均等待时间",
      "technicianSchedule": "技师工作安排",
      "selectDate": "选择日期",
      "today": "今天",
      "tomorrow": "明天",
      "thisWeek": "本周",
      "workload": "工作负荷",
      "available": "空闲",
      "orderStatusDistribution": "工单状态分布",
      "technicianWorkload": "技师工作负荷",
      "workloadPercentage": "负荷百分比",
      "workOrderDetail": "工单详情"
    },
    "management": {
      "title": "派工管理"
    },
    "common": {
      "refresh": "刷新",
      "search": "搜索",
      "reset": "重置",
      "confirm": "确认",
      "cancel": "取消",
      "detail": "详情",
      "hours": "小时",
      "minutes": "分钟",
      "unassigned": "未分配",
      "notScheduled": "未安排",
      "actions": "操作"
    },
    "type": {
      "maintenance": "保养",
      "repair": "维修",
      "insurance": "保险"
    },
    "priority": {
      "normal": "普通",
      "urgent": "紧急"
    },
    "status": {
      "draft": "草稿",
      "pendingConfirm": "待确认",
      "confirmed": "已确认",
      "pendingAssignment": "待分配",
      "assigned": "已分配",
      "inProgress": "进行中",
      "pendingQc": "待质检",
      "pendingSettle": "待结算",
      "completed": "已完成",
      "cancelled": "已取消"
    },
    "actions": {
      "assign": "分配",
      "reassign": "重新分配",
      "checkIn": "开工",
      "checkOut": "完工"
    },
    "placeholder": {
      "enterWorkOrderNo": "请输入工单编号",
      "selectStatus": "请选择状态",
      "selectType": "请选择类型",
      "selectPriority": "请选择优先级",
      "selectTechnician": "请选择技师"
    },
    "dialog": {
      "assignTitle": "分配工单",
      "reassignTitle": "重新分配工单"
    },
    "confirmMessages": {
      "checkIn": "确定要开工此工单吗？",
      "checkOut": "确定要完工此工单吗？"
    },
    "messages": {
      "loadDataFailed": "加载数据失败",
      "assignSuccess": "分配成功",
      "assignFailed": "分配失败",
      "reassignSuccess": "重新分配成功",
      "reassignFailed": "重新分配失败",
      "checkInSuccess": "开工成功",
      "checkInFailed": "开工失败",
      "checkOutSuccess": "完工成功",
      "checkOutFailed": "完工失败"
    }
  },
  "invoice": {
    "title": "发票管理",
    "invoiceNumber": "发票编号",
    "invoiceNumberPlaceholder": "请输入发票编号",
    "customerName": "购车人姓名",
    "customerNamePlaceholder": "请输入购车人姓名",
    "customerPhone": "购车人手机号",
    "customerPhonePlaceholder": "请输入购车人手机号",
    "customerEmail": "购车人邮箱",
    "customerEmailPlaceholder": "请输入购车人邮箱",
    "customerAddress": "购车人地址",
    "customerState": "购车人州",
    "customerCity": "购车人城市",
    "orderNumber": "订单编号",
    "orderNumberPlaceholder": "请输入订单编号",
    "vin": "车辆识别代号",
    "vinPlaceholder": "请输入车辆识别代号",
    "salesType": "销售类型",
    "salesStore": "销售门店",
    "salesConsultant": "销售顾问",
    "invoiceDate": "开票日期",
    "invoiceDateRange": "开票日期范围",
    "batchPrint": "批量打印",
    "export": "导出",
    "detail": "详情",
    "print": "打印",
    "email": "邮件",
    "log": "日志",
    "invoiceAmount": "发票总额",
    "createdTime": "创建时间",
    "model": "车型系列",
    "variant": "车型配置版本",
    "color": "车辆颜色",
    "paymentMethod": "客户付款方式",
    "financeCompany": "贷款金融公司",
    "loanAmount": "贷款金额",
    "emailConfirm": "邮件发送确认",
    "confirmSendEmail": "确认发送邮件到客户邮箱？",
    "exportConfig": "导出配置",
    "exportFormat": "导出格式",
    "exportScope": "导出范围",
    "currentPage": "当前页",
    "allData": "全部数据",
    "filteredData": "筛选结果",
    "operationLog": "操作日志",
    "operationType": "操作类型",
    "operator": "操作人",
    "operationTime": "操作时间",
    "operationResult": "操作结果",
    "operationDescription": "操作描述",
    "errorMessage": "错误信息",
    "remark": "备注",
    "printSuccess": "打印成功",
    "emailSentSuccess": "邮件发送成功",
    "exportSuccess": "导出成功",
    "batchPrintSuccess": "批量打印成功",
    "pleaseSelectRecords": "请选择要操作的记录",
    "invoiceDetail": "发票详情",
    "basicInfo": "发票基本信息",
    "customerInfo": "客户详细信息",
    "vehicleInfo": "车辆详细信息",
    "financeInfo": "金融信息详情",
    "insuranceInfo": "保险信息详情",
    "priceStructure": "价格结构明细",
    "receipts": "收据明细信息",
    "otrFees": "OTR费用明细",
    "companyName": "公司名称",
    "companyAddress": "公司地址",
    "gstNumber": "GST编号",
    "sstNumber": "SST编号",
    "engineNumber": "发动机号",
    "chassisNumber": "底盘号",
    "engineCapacity": "发动机排量",
    "transmission": "变速箱",
    "loanTerm": "贷款期限",
    "months": "个月",
    "interestRate": "利率",
    "monthlyPayment": "月供",
    "insuranceCompany": "保险公司",
    "agentCode": "代理编码",
    "policyNumber": "保险单号",
    "policyDate": "出单日期",
    "insuranceAmount": "保险费用",
    "vehiclePrice": "车辆销售价",
    "adjustmentAmount": "调整金额",
    "accessories": "选配件明细",
    "category": "类别",
    "accessoryName": "配件名称",
    "unitPrice": "单价",
    "quantity": "数量",
    "totalPrice": "总价",
    "feeType": "费用类型",
    "description": "费用说明",
    "amount": "金额",
    "receiptNumber": "票据单号",
    "effectiveDate": "生效日期",
    "paymentDate": "付款日期",
    "bankName": "银行名称",
    "accountNumber": "账户号码",
    "status": "状态",
    "invoiceStatuses": {
      "issued": "已开票",
      "printed": "已打印", 
      "sent": "已发送"
    },
    "operationTypes": {
      "view_detail": "查看详情",
      "print": "打印",
      "allocate": "配车"
    },
    "operationResults": {
      "success": "成功",
      "failed": "失败"
    },
    "commonFields": {
      "paginationLayout": "total, sizes, prev, pager, next, jumper",
      "total": "总计",
      "add": "新增",
      "optional": "（可选）",
      "required": "（必填）",
      "selectPlaceholder": "请选择",
      "inputPlaceholder": "请输入",
      "all": "全部",
      "yes": "是",
      "no": "否",
      "loading": "加载中...",
      "items": "条",
      "page": "页",
      "operationSuccessful": "操作成功",
      "operationFailed": "操作失败",
      "pleaseInput": "请输入",
      "required": "必填项",
      "unknown": "未知",
      "vehicleAge": "车龄",
      "months": "月",
      "good": "良好",
      "needsAttention": "需要注意",
      "bad": "不良",
      "notApplicable": "不适用"
    },
    "operationRemarks": {
      "viewDetail": "查看发票详情",
      "printSuccess": "打印发票成功"
    },
    "messages": {
      "fetchListFailed": "获取发票列表失败",
      "fetchStoreListFailed": "获取门店列表失败",
      "fetchConsultantListFailed": "获取销售顾问列表失败",
      "fetchDetailFailed": "获取发票详情失败",
      "printFailed": "打印失败",
      "batchPrintFailed": "批量打印失败",
      "fetchInvoiceInfoFailed": "获取发票信息失败",
      "emailSendFailed": "邮件发送失败",
      "exportFailed": "导出失败"
    },
    "salesTypeOptions": {
      "PHP": "PHP",
      "CASH": "现金",
      "FINANCE": "贷款"
    },
    "defaultValues": {
      "companyName": "PERODUA SALES SDN BHD",
      "companyAddress": "公司地址",
      "gstNumber": "001821038956",
      "sstNumber": "B16-1808-31036508",
      "contactPhone": "03-79474600",
      "contactEmail": "<EMAIL>",
      "salesConsultantId": "500598",
      "tinNumber": "EI0000000010",
      "modelCode": "MODELX",
      "modelDescription": "轿车",
      "engineNumber": "2NR3D31119",
      "chassisNumber": "PM2M8D6SD02448917",
      "engineCapacity": "1496 cc",
      "fuelType": "汽油",
      "transmission": "自动",
      "year": "2025",
      "vehicleRegistrationDate": "09-MAY-2025",
      "creator": "系统管理员",
      "updater": "系统管理员",
      "updateTime": "2024-01-15 11:00:00",
      "financeType": "Finance",
      "loanTerm": 108,
      "insuranceCompany": "TAKAFUL IKHLAS GENERAL BERHAD",
      "agentCode": "TI",
      "policyNumber": "MMP25635488",
      "policyDate": "08-MAY-2025",
      "vehiclePrice": 47898.33,
      "licensePlateFee": 0.00,
      "totalAccessoryAmount": 2800.00,
      "subtotal": 60015.55,
      "totalOtrFeeAmount": 320.00,
      "insurancePremium": 2179.10,
      "totalSalesPrice": 62484.65,
      "adjustmentAmount": 0.00,
      "invoiceNetValue": 62484.65
    },
    "mockData": {
      "registerTypes": {
        "appointment": "预约",
        "walkIn": "自然到店"
      },
      "serviceTypes": {
        "maintenance": "保养",
        "repair": "维修"
      },
      "colors": {
        "white": "白色",
        "black": "黑色",
        "blue": "蓝色",
        "gray": "灰色"
      },
      "names": {
        "zhangSan": "张三",
        "liHua": "李华",
        "wangDaChui": "王大锤",
        "linYiYi": "林依依",
        "liSi": "李四",
        "wangWu": "王五",
        "zhaoLiu": "赵六",
        "qianQi": "钱七",
        "sunBa": "孙八",
        "zhouJiu": "周九",
        "wuShi": "吴十"
      },
      "carModels": {
        "modelY2023Long": "Model Y 2023 长续航版",
        "model32022Standard": "Model 3 2022 标准续航版",
        "modelX2024Performance": "Model X 2024 高性能版",
        "modelY2023Performance": "Model Y 2023 高性能版"
      }
    },
    "processResults": {
      "success": "成功",
      "failed": "失败"
    }
  },
  "quota": {
      "pageTitle": "预约限量配置",
      "storeName": "门店名称",
      "storeCode": "门店编号",
      "permissionTip": "当前登录用户仅可配置所属门店的预约限量",
      "configuredListTitle": "已配置限量列表",
      "addNewQuota": "新增预约限量",
      "emptyState": "暂未配置预约限量。点击\"新增预约限量\"进行配置。",
      "unsavedChangesWarning": "您有未保存的更改，确定要关闭吗？",
      "dateMustBeFutureOrToday": "配置日期不能早于今天",
      "selectDateFirst": "请先选择配置日期",
      "timeSlotExceedsOperatingHours": "时间段不能超过营业时间（18:00）",
      "addNewQuotaTitle": "新增预约限量",
      "editQuotaTitle": "编辑预约限量",
      "store": {
        "name": "吉隆坡中央店",
        "code": "KL001"
      },
      "table": {
        "configDate": "配置日期",
        "timeSlotCount": "时段数量",
        "totalQuota": "总限量",
        "bookedQuantity": "已预约数量",
        "lastUpdateTime": "最后更新时间",
        "expired": "已过期"
      },
      "summary": {
        "selectDate": "请选择配置日期",
        "addTimeSlot": "请添加时间段",
        "timeSlotsUnit": "个时段",
        "totalQuota": "总限量"
      },
      "modal": {
        "selectDateTitle": "选择配置日期",
        "dateLabel": "配置日期",
        "datePlaceholder": "请选择配置日期",
        "existingConfig": "该日期已有配置",
        "dateTip": "只能配置今天及以后的日期",
        "timeSlotConfigTitle": "时段配置",
        "addTimeSlot": "添加时段",
        "noTimeSlots": "暂无时段配置",
        "clickAddPrompt": "点击\"添加时段\"开始配置",
        "timeSlot": "时段",
        "startTime": "开始时间",
        "endTime": "结束时间",
        "quota": "限量",
        "startTimePlaceholder": "请选择开始时间",
        "endTimePlaceholder": "请选择结束时间",
        "quotaPlaceholder": "请输入限量",
        "configDescriptionTitle": "配置说明",
        "configDescription": {
          "item1": "每个时段的限量数代表该时段内可预约的最大数量",
          "item2": "时段之间不能重叠，系统会自动检测冲突",
          "item3": "营业时间为 8:00-18:00，时段不能超出此范围",
          "item4": "保存后立即生效，客户可在小程序中选择对应时段预约"
        }
      },
      "validation": {
        "dateRequired": "请选择配置日期",
        "atLeastOneTimeSlot": "至少需要添加一个时段",
        "timeRequired": "请完善时段的开始和结束时间",
        "startBeforeEnd": "开始时间必须早于结束时间",
        "quotaPositive": "限量必须大于0",
        "timeSlotOverlap": "时段 {slot1} 与 {slot2} 存在时间重叠，请调整"
      }
  },
  "workAssignment": {
    "dashboard": {
      "title": "派工看板",
      "autoRefresh": "自动刷新",
      "pendingAssignment": "待派工",
      "assignedOrders": "已分配",
      "inProgressOrders": "进行中",
      "completedOrders": "已完成",
      "availableTechnicians": "可用技师",
      "averageEfficiency": "平均效率",
      "utilizationRate": "利用率",
      "averageWaitingTime": "平均等待时间",
      "technicianSchedule": "技师工作安排",
      "selectDate": "选择日期",
      "today": "今天",
      "tomorrow": "明天",
      "thisWeek": "本周",
      "workload": "工作负荷",
      "available": "空闲",
      "orderStatusDistribution": "工单状态分布",
      "technicianWorkload": "技师工作负荷",
      "workloadPercentage": "负荷百分比",
      "workOrderDetail": "工单详情"
    },
    "management": {
      "title": "派工管理"
    },
    "common": {
      "refresh": "刷新",
      "search": "搜索",
      "reset": "重置",
      "confirm": "确认",
      "cancel": "取消",
      "detail": "详情",
      "hours": "小时",
      "minutes": "分钟",
      "unassigned": "未分配",
      "notScheduled": "未安排",
      "actions": "操作"
    },
    "workOrder": {
      "workOrderNo": "工单编号",
      "customerName": "客户姓名",
      "vehicleInfo": "车辆信息",
      "type": "工单类型",
      "priority": "优先级",
      "status": "工单状态",
      "estimatedDuration": "预计工时",
      "createdAt": "创建时间",
      "scheduledStartTime": "预计开工时间"
    },
    "technician": {
      "technician": "技师",
      "assignedTechnician": "分配技师",
      "currentTechnician": "当前技师",
      "status": {
        "available": "可用",
        "busy": "忙碌",
        "onLeave": "请假",
        "training": "培训",
        "resigned": "离职"
      }
    },
    "salesOrder": {
      "title": "销售订单列表",
      "breadcrumb": "客户订单管理 / 销售订单列表",
      "searchForm": "搜索条件",
      "operationButtons": "操作",
      "orderList": "订单列表",
      "buyerName": "购车人",
      "buyerNamePlaceholder": "请输入购车人姓名",
      "buyerPhone": "购车人手机号",
      "buyerPhonePlaceholder": "请输入购车人手机号",
      "buyerType": "购车人类别",
      "buyerTypePlaceholder": "请选择购车人类别",
      "model": "车型",
      "modelPlaceholder": "请选择车型",
      "orderNumber": "订单号",
      "orderNumberPlaceholder": "请输入订单号",
      "orderStatus": "订单状态",
      "orderStatusPlaceholder": "请选择订单状态",
      "approvalStatus": "订单审核状态",
      "approvalStatusPlaceholder": "请选择订单审核状态",
      "paymentStatus": "订单支付状态",
      "paymentStatusPlaceholder": "请选择订单支付状态",
      "createTime": "订单创建时间",
      "createTimeRange": "订单创建时间范围",
      "insuranceStatus": "投保状态",
      "insuranceStatusPlaceholder": "请选择投保状态",
      "loanApprovalStatus": "贷款审核状态",
      "loanApprovalStatusPlaceholder": "请选择贷款审核状态",
      "jpjRegistrationStatus": "JPJ车辆注册状态",
      "jpjRegistrationStatusPlaceholder": "请选择JPJ车辆注册状态",
      "index": "序号",
      "ordererName": "下单人",
      "ordererPhone": "下单人手机号",
      "variant": "Variant",
      "color": "Color",
      "vin": "VIN",
      "paymentMethod": "支付方式",
      "totalAmount": "订单总金额",
      "buyerTypes": {
        "individual": "个人",
        "company": "公司"
      },
      "models": {
        "AXIA": "AXIA",
        "BEZZA": "BEZZA",
        "MYVI": "MYVI"
      },
      "orderStatuses": {
        "submitted": "已提交",
        "confirmed": "已确认",
        "cancel_pending": "取消审核中",
        "cancel_approved": "取消审核通过",
        "cancelled": "已取消",
        "pending_delivery": "待交车",
        "delivered": "已交车"
      },
      "approvalStatuses": {
        "pending_approval": "待审批",
        "approved": "已审批"
      },
      "paymentStatuses": {
        "pending_deposit": "待支付定金",
        "deposit_paid": "已支付定金",
        "pending_balance": "待支付尾款",
        "balance_paid": "已支付尾款",
        "refunding": "退款中",
        "refunded": "退款完成"
      },
      "insuranceStatuses": {
        "not_insured": "未投保",
        "insuring": "投保中",
        "insured": "投保完成"
      },
      "loanApprovalStatuses": {
        "pending_review": "待审核",
        "approved": "审核通过",
        "rejected": "审核驳回"
      },
      "jpjRegistrationStatuses": {
        "pending_registration": "待登记",
        "registering": "登记中",
        "registered": "登记成功",
        "registration_failed": "登记失败"
      },
      "paymentMethods": {
        "full_payment": "全款",
        "installment": "分期"
      },
      "viewDetail": "详情",
      "editOrder": "编辑",
      "returnToList": "返回列表",
      "exportSuccess": "导出成功",
      "exportFailed": "导出失败",
      "noDataToExport": "没有数据可导出",
      "confirmExportTitle": "确认导出",
      "confirmExportMessage": "确定要导出当前筛选条件下的所有订单数据吗？",
      "orderDetail": "销售订单详情",
      "customerInfo": "客户信息",
      "storeInfo": "购车门店信息",
      "purchaseInfo": "购车信息",
      "vehicleInfoTab": "车辆信息",
      "invoicingInfoTab": "开票信息",
      "rightsInfoTab": "服务&权益信息",
      "paymentInfoTab": "支付信息",
      "insuranceInfoTab": "保险信息",
      "otrFeesTab": "OTR费用信息",
      "changeRecords": "订单变更记录",
      "buyerIdType": "购车人身份证件类别",
      "buyerIdNumber": "购车人身份证件号",
      "buyerEmail": "购车人邮箱",
      "buyerAddress": "购车人地址",
      "buyerState": "购车人所在州",
      "buyerCity": "购车人所在城市",
      "buyerPostcode": "购车人所在地邮编",
      "storeRegion": "所在地区",
      "storeCity": "所在城市",
      "storeName": "购车门店",
      "salesConsultantName": "销售顾问",
      "salesSubtotal": "销售小计(包含GASA、消费税、销售税)",
      "numberPlatesFee": "车牌费",
      "vinCode": "VIN码",
      "accessoryInfo": "选配件信息",
      "accessoryCategory": "类别",
      "accessoryName": "配件名称",
      "unitPrice": "配件单价",
      "quantity": "数量",
      "totalPrice": "总价",
      "accessoriesTotalAmount": "选配件总金额",
      "invoicingType": "开票类型",
      "invoicingName": "开票名称",
      "invoicingPhone": "开票电话",
      "invoicingAddress": "开票地址",
      "rightsInfo": "服务&权益信息",
      "rightCode": "权益代码",
      "rightName": "权益名称",
      "rightMode": "权益模式",
      "discountAmount": "权益优惠价格",
      "effectiveDate": "权益生效日期",
      "expiryDate": "权益终止日期",
      "rightsDiscountAmount": "权益优惠总金额",
      "loanApprovalStatusField": "贷款资质审核状态",
      "depositAmount": "定金金额",
      "loanAmount": "贷款金额",
      "balanceAmount": "尾款金额",
      "insuranceInfo": "保单信息",
      "policyNumber": "保单号",
      "insuranceType": "保险类型",
      "insuranceCompany": "保险公司",
      "insurancePrice": "保险价格",
      "insuranceTotalAmount": "保险总金额",
      "insuranceNotes": "备注",
      "otrFeesInfo": "On The Road登记费用",
      "ticketNumber": "票据单号",
      "feeItem": "收费项目",
      "feePrice": "收费价格",
      "otrFeesTotalAmount": "OTR费用总金额",
      "changeRecordIndex": "序号",
      "originalContent": "原始内容",
      "changedContent": "变更后内容",
      "operator": "操作人",
      "operationTime": "操作时间",
      "totalInvoiceAmount": "整车开票价",
      "remainingAmount": "剩余应收",
      "editOrderTitle": "销售订单编辑",
      "personalDetails": "客户信息 - Personal Details",
      "preferredOutlet": "购车门店信息 - Preferred Outlet & Sales Advisor",
      "purchaseDetails": "购车信息 - Purchase Details",
      "addRights": "新增权益",
      "selectRights": "选择权益",
      "rightCodeSearch": "权益代码搜索",
      "rightNameSearch": "权益名称搜索",
      "selectAll": "全选",
      "addSelected": "添加",
      "deleteRight": "删除权益",
      "confirmDeleteRight": "确定要删除此权益吗？",
      "pushToInsurance": "推送至保险系统",
      "insurancePushed": "已推送",
      "confirmPushInsurance": "确定要推送至保险系统吗？",
      "pushInsuranceSuccess": "推送保险系统成功",
      "pushInsuranceFailed": "推送保险系统失败",
      "submitDelivery": "提交交车",
      "confirmSubmitDelivery": "确定要提交交车申请吗？",
      "submitDeliverySuccess": "提交交车成功",
      "submitDeliveryFailed": "提交交车失败",
      "deliveryConditionsNotMet": "交车条件不满足",
      "colorChangeNotice": "您已更改车辆颜色，是否提交审核？",
      "colorChangeSubmitted": "颜色变更已提交审核",
      "roadTax": "路税",
      "registrationFee": "注册/过户费",
      "ownershipClaimFee": "所有权索赔费",
      "interchangeFee": "咨询费",
      "otrFeeTotal": "登记费用总金额",
      "loanTerm": "贷款期数（月）",
      "loanTermPlaceholder": "请输入贷款期数",
      "loanAmountPlaceholder": "请输入贷款金额",
      "printFeatureNotImplemented": "打印功能暂未实现",
      "fetchDataFailed": "获取数据失败",
      "submitConfirmSuccess": "提交确认成功！",
      "submitConfirmFailed": "提交确认失败！",
      "deliveryConfirmSuccess": "交车确认成功！",
      "deliveryConfirmFailed": "交车确认失败！",
      "exportSuccess": "导出成功！",
      "exportFailed": "导出失败！",
      "exportSettingsTitle": "导出设置",
      "exportFormat": "导出格式",
      "exportFormatRequired": "请选择导出格式",
      "exportRange": "导出范围",
      "exportRangeRequired": "请选择导出范围",
      "exportRangeCurrentPage": "当前页",
      "exportRangeFilteredResult": "筛选结果",
      "exportRangeAllData": "全部数据",
      "exportTimeRange": "导出时间范围",
      "signaturePhotoFormatError": "签名照片只能是 JPG/PNG 格式！",
      "signaturePhotoSizeError": "签名照片大小不能超过 5MB！",
      "signatureUploadFailed": "签名照片上传失败！",
      "noOrderSelected": "未选择订单！",
      "customerConfirmTimeStart": "客户确认开始时间",
      "customerConfirmTimeEnd": "客户确认结束时间",
      "customerConfirmTimeStartPlaceholder": "请选择客户确认开始日期",
      "customerConfirmTimeEndPlaceholder": "请选择客户确认结束日期",
      "basicInfo": "基本信息",
      "orderCreatorName": "下单人名称",
      "orderCreatorPhone": "下单人手机号",
      "customerType": "客户类型",
      "idType": "证件类型",
      "idNumber": "证件号",
      "address": "地址",
      "city": "城市",
      "postcode": "邮编",
      "state": "州",
      "city": "城市",
      "deliveryNumber": "交车单编号",
      "salesConsultantId": "销售顾问ID",
      "tinNumber": "TIN编号",
      "modelCode": "车型代码",
      "modelDescription": "车型描述",
      "fuelType": "燃料类型",
      "year": "年款",
      "vehicleRegistrationDate": "车辆登记日期",
      "creator": "创建人",
      "updater": "更新人",
      "updateTime": "更新时间",
      "financeType": "金融方式",
      "issueDate": "出单日期",
      "priceStructureDetails": "价格结构明细",
      "vehicleSalesPrice": "车辆销售价",
      "licensePlateFee": "车牌费用",
      "optionalAccessories": "选配件明细",
      "totalAccessoryAmount": "选配件总金额",
      "subtotal": "小计",
      "otrRegistrationFees": "OTR费用明细",
      "billNumber": "票据单号",
      "feeItem": "费用项目",
      "feePrice": "金额",
      "expiryDate": "到期日期",
      "totalOtrFeeAmount": "OTR费用总金额",
      "totalSalesPrice": "销售总价",
      "invoiceNetValue": "发票净值",
      "receiptDetails": "收据明细信息",
      "businessType": "业务类型",
      "serialNumber": "流水号",
      "channel": "渠道",
      "collectionType": "收款类型",
      "arrivalTime": "到账时间"
    }
  },
  "parts": {
    "title": "零件管理",
    "partsManagementHQ": "零件管理HQ",
    "approvalType": "审批类型",
    "selectApprovalType": "请选择审批类型",
    "requisitionApproval": "叫料审批",
    "damageApproval": "零件破损",
    "partNo": "零件号",
    "partName": "零件名称",
    "partNamePlaceholder": "请输入零件名称",
    "partNumber": "零件编号",
    "partNumberPlaceholder": "请输入零件编号",
    "requisitionNumber": "单据号",
    "requisitionNumberPlaceholder": "请输入单据号",
    "documentNumber": "单据号",
    "documentTypeHQ": "单据类型",
    "approvalTypeHQ": "审批类型",
    "generateDate": "生成日期",
    "storeName": "门店名称",
    "documentStatus": "单据状态",
    "documentTypeRequisition": "叫料",
    "documentTypeDamage": "破损",
    "pendingRequisitionApproval": "待审批叫料单",
    "pendingDamageApproval": "待审批破损申请",
    "damageDetail": "破损详情",
    "scrapOrderNumber": "报损单号",
    "scrapDate": "报损日期",
    "scrapSource": "报损来源",
    "scrapSourceReceipt": "收货",
    "scrapSourceRepair": "维修",
    "scrapSourceOther": "其它",
    "scrapQuantity": "报损数量",
    "scrapReason": "报损原因",
    "scrapImages": "报损图片",
    "damageDetail": "破损详情",
    "pleaseSelectApprovalTypeFirst": "请先选择审批类型",
    "documentNumber": "单据号",
    "documentNumberPlaceholder": "输入框（全模糊查询）",
    "documentDateRange": "单据生成日区间",
    "documentStatus": "单据状态",
    "storeNamePlaceholder": "可输入筛选下拉框",
    "switchedToRequisitionApproval": "已切换到叫料审批模式",
    "switchedToDamageApproval": "已切换到破损审批模式",
    "noFilterResults": "未找到符合筛选条件的数据",
    "clearFilters": "清空筛选条件",
    "documentType": "单据类型",
    "documentTypeRequisition": "叫料",
    "documentTypeDamage": "破损",
    "generateDate": "生成日期",
    "documentStatus": "单据状态",
    "supplierName": "供应商名称",
    "supplierNamePlaceholder": "可输入筛选下拉框",
    "requisitionDate": "叫料日期",
    "requisitionDateRange": "叫料日期区间",
    "arrivalDateRange": "到货日期区间",
    "arrivalTimeRange": "到货日期区间",
    "requisitionStatus": "叫料单状态",
    "selectRequisitionStatus": "请选择叫料单状态",
    "selectStatus": "请选择状态",
    "statusSubmitted": "已提交",
    "statusApproved": "通过",
    "statusRejected": "驳回",
    "statusShipped": "已发货",
    "statusPartialShipped": "部分发货",
    "statusPartialReceived": "部分收货",
    "statusReceived": "已收货",
    "statusCancelled": "已作废",
    "statusVoided": "已作废",
    "inventoryStatus": "库存状态",
    "selectInventoryStatus": "请选择库存状态",
    "statusNormal": "正常",
    "statusBelowSafetyStock": "低于安全库存",
    "status": {
      "undefined": "未定义",
      "submitted": "已提交",
      "approved": "通过",
      "rejected": "驳回",
      "shipped": "已发货",
      "partialShipped": "部分发货",
      "partialReceived": "部分收货",
      "received": "已收货",
      "cancelled": "已作废",
      "voided": "已作废"
    },
    "approve": "审批",
    "approveRequisition": "审批叫料单",
    "approveDamage": "审批破损单",
    "approvalResult": "审批结果",
    "approvalResultRequired": "请选择审批结果",
    "rejectionReason": "驳回原因",
    "rejectionReasonRequired": "请输入驳回原因",
    "rejectedRequisitionResubmitted": "已驳回的叫料单重新提交成功，已生成新的叫料单",
    "rejectedRequisitionConfirmMessage": "此次修改会创建一条新的叫料单，原叫料单保持已驳回状态。确定要继续吗？",
    "requisitionUpdated": "叫料单更新成功",
    "selectOneForApproval": "请选择一条记录进行审批",
    "requisitionDetail": "叫料单详情",
    "serialNumber": "序号",
    "quantity": "数量",
    "unit": "单位",
    "expectedArrivalTime": "预计到货时间",
    "ship": "发货",
    "shipParts": "零件发货",
    "shipPartially": "部分发货",
    "shipCompletely": "完整发货",
    "shippedQuantity": "已发货数量",
    "shippingStatus": "发货状态",
    "notShipped": "未发货",
    "partiallyShipped": "部分发货",
    "fullyShipped": "已发货",
    "shipSuccess": "发货成功",
    "shipFailed": "发货失败",
    "status": {
      "submitted": "已提交",
      "approved": "通过",
      "rejected": "驳回",
      "shipped": "已发货",
      "partialShipped": "部分发货",
      "partialReceived": "部分收货",
      "received": "已收货",
      "voided": "已作废"
    },
    "newRequisitionForm": {
      "partName": "零件名称",
      "partNumber": "零件号",
      "quantity": "数量",
      "storeName": "门店名称",
      "expectedArrivalTime": "期望收货时间"
    },
    "partScrapForm": {
      "partName": "零件名称",
      "partNumber": "零件号",
      "partNamePlaceholder": "请输入零件名称",
      "partNumberPlaceholder": "请输入零件号",
      "quantity": "数量",
      "scrapReason": "报损原因",
      "scrapSource": "报损来源",
      "selectScrapSource": "请选择报损来源",
      "sourceReceipt": "收货",
      "sourceRepair": "维修",
      "sourceOther": "其它",
      "partNameRequired": "请输入零件名称",
      "partNumberRequired": "请输入零件号",
      "quantityRequired": "请输入数量",
      "scrapReasonRequired": "请输入报损原因",
      "scrapSourceRequired": "请选择报损来源"
    },
    "scrapRecordForm": {
      "partName": "零件名称",
      "partNamePlaceholder": "可输入筛选下拉框",
      "partNumber": "零件编号",
      "partNumberPlaceholder": "可输入筛选下拉框",
      "scrapDate": "报损日期",
      "scrapSource": "报损来源",
      "selectScrapSource": "请选择报损来源",
      "sourceReceipt": "收货",
      "sourceRepair": "维修",
      "sourceOther": "其它",
      "receiptTime": "收货时间",
      "scrapReason": "报损原因详情"
    }
  },
  "partManagementHQ": {
    "title": "零件管理HQ",
    "partName": "零件名称",
    "partNamePlaceholder": "可输入筛选下拉框",
    "partNumber": "零件编号",
    "partNumberPlaceholder": "可输入筛选下拉框",
    "requisitionNumber": "叫料单号",
    "requisitionNumberPlaceholder": "输入框（全模糊查询）",
    "purchaseOrderNumber": "采购单号",
    "purchaseOrderNumberPlaceholder": "输入框（全模糊查询）",
    "supplierName": "供应商名称",
    "supplierNamePlaceholder": "可输入筛选下拉框",
    "requisitionDateRange": "叫料日期区间",
    "arrivalTimeRange": "到货日期区间",
    "requisitionStatus": "叫料单状态",
    "selectRequisitionStatus": "请选择叫料单状态",
    "statusSubmitted": "已提交",
    "statusApproved": "通过",
    "statusRejected": "驳回",
    "statusShipped": "已发货",
    "statusReceived": "已收货",
    "statusCancelled": "已作废",
    "inventoryStatus": "库存状态",
    "selectInventoryStatus": "请选择库存状态",
    "statusNormal": "正常",
    "statusBelowSafetyStock": "低于安全库存",
    "exportReport": "导出报表",
    "approve": "审批",
    "approvalResult": "审批结果",
    "selectApprovalResult": "请选择审批结果",
    "rejectionReason": "驳回原因",
    "rejectionReasonRequired": "请输入驳回原因",
    "enterRejectionReason": "请输入驳回原因，字数限制1000",
    "requisitionDetails": "叫料单明细",
    "quantity": "数量",
    "unit": "单位",
    "expectedArrivalTime": "预计到货时间",
    "approvalSuccess": "审批成功！",
    "approvalResultRequired": "审批结果必选"
  },
  "partsReceipt": {
    "title": "零件收货（店端）",
    "partName": "零件名称",
    "partNamePlaceholder": "请输入零件名称",
    "partNumber": "零件编号",
    "partNumberPlaceholder": "请输入零件编号",
    "supplierName": "供应商名称",
    "supplierNamePlaceholder": "请输入供应商名称",
    "requisitionNumber": "单据号",
    "requisitionNumberPlaceholder": "请输入单据号",
    "purchaseOrderNumber": "采购单号",
    "purchaseOrderNumberPlaceholder": "请输入采购单号",
    "deliveryOrderNumber": "到货单号",
    "deliveryOrderNumberPlaceholder": "请输入到货单号",
    "receipt": "收货",
    "receive": "收货",
    "receiptStatus": "收货状态",
    "received": "已收货",
    "notReceived": "未收货",
    "billed": "已开单",
    "generateReceiptOrder": "生成收货单",
    "invalidateReceiptOrder": "作废收货单",
    "receiptOrderNumber": "收货单号",
    "partQuantity": "零件数量",
    "unit": "单位",
    "receiptDialogTitle": "零件收货",
    "receivedQuantity": "收货数量",
    "receiptQuantity": "收货数量",
    "damagedQuantity": "破损数量",
    "damageReason": "破损情况",
    "damageDescription": "破损情况",
    "damageReasonPlaceholder": "请输入破损情况",
    "damageDescriptionPlaceholder": "请输入破损情况",
    "selectItemForReceipt": "请选择要收货的零件。",
    "damageReasonRequired": "破损数量不为0时，破损情况必填。",
    "receiptSuccess": "收货成功！",
    "generateReceiptOrderDialogTitle": "生成收货单",
    "receiptTime": "收货时间",
    "receiptTimePlaceholder": "请选择收货时间",
    "quantity": "数量",
    "printReceiptOrder": "打印收货单",
    "selectItemToPrint": "请选择要打印收货单的零件。",
    "generateReceiptOrderSuccess": "收货单生成成功！",
    "invalidateReceiptOrderDialogTitle": "作废收货单",
    "receiptOrderStatus": "收货单状态",
    "receiptOrderStatusPlaceholder": "请选择状态",
    "statusActive": "有效",
    "statusInvalid": "无效",
    "statusEffective": "生效",
    "statusInvalidated": "作废",
    "generateDate": "生成日期",
    "generationDate": "生成日期",
    "confirmInvalidateReceiptOrder": "是否作废此单据？",
    "invalidateReceiptOrderSuccess": "收货单作废成功！",
    "receiptOrderDetailTitle": "收货单明细",
    "viewReceiptOrder": "查看收货单"
  },
  "inventory": {
    "title": "库存信息（店端）",
    "partName": "零件名称",
    "partNamePlaceholder": "请输入零件名称",
    "partNumber": "零件编号",
    "partNumberPlaceholder": "请输入零件编号",
    "supplierName": "供应商名称",
    "supplierNamePlaceholder": "请输入供应商名称",
    "inventoryStatus": "库存状态",
    "inventoryStatusPlaceholder": "请选择库存状态",
    "totalInventory": "库存总数",
    "availableInventory": "可用库存数",
    "lockedInventory": "锁定库存数",
    "defectiveCount": "残次品数",
    "pendingCount": "待收数",
    "safetyStock": "安全库存数",
    "status": {
      "normal": "正常",
      "belowSafety": "低于安全库存"
    }
  },
  "partsReceiptQuery": {
    "title": "零件收货查询",
    "queryDimension": "查询维度",
    "queryDimensionPlaceholder": "请选择查询维度",
    "byReceiptOrder": "按收货单",
    "byReceiptDetail": "按收货明细",
    "partName": "零件名称",
    "partNamePlaceholder": "请输入零件名称",
    "partNumber": "零件编号",
    "partNumberPlaceholder": "请输入零件编号",
    "supplierName": "供应商名称",
    "supplierNamePlaceholder": "请输入供应商名称",
    "requisitionNumber": "单据号",
    "requisitionNumberPlaceholder": "请输入单据号",
    "purchaseOrderNumber": "采购单号",
    "purchaseOrderNumberPlaceholder": "请输入采购单号",
    "deliveryOrderNumber": "送货单号",
    "deliveryOrderNumberPlaceholder": "请输入送货单号",
    "receiptOrderNumber": "收货单号",
    "receiptOrderNumberPlaceholder": "请输入收货单号",
    "generateDate": "生成日期",
    "orderStatus": "单据状态",
    "status": {
      "active": "有效",
      "invalid": "已作废"
    },
    "viewDetails": "查看明细",
    "details": "明细",
    "quantity": "数量",
    "unit": "单位",
    "receiptTime": "收货时间",
    "searchFailed": "查询失败",
    "queryDimensionRequired": "请选择查询维度"
  },
  "partsConfiguration": {
    "title": "零件配置",
    "partName": "零件名称",
    "partNumber": "零件编码",
    "supplierName": "供应商名称",
    "partNamePlaceholder": "请输入零件名称",
    "partNumberPlaceholder": "请输入零件编码",
    "supplierNamePlaceholder": "请输入供应商名称",
    "unit": "单位",
    "safetyStock": "安全库存",
    "configure": "配置",
    "configurationModalTitle": "零件配置"
  },
  "order": {
    "orderManagement": "厂端订单管理",
    "orderOverview": "订单概览统计",
    "filterConditions": "筛选条件",
    "orderNumber": "订单编号",
    "orderNumberPlaceholder": "请输入订单编号",
    "storeName": "门店",
    "storeNamePlaceholder": "请选择门店",
    "model": "车型",
    "modelPlaceholder": "请选择车型",
    "variant": "配置",
    "variantPlaceholder": "请先选择车型",
    "orderStatus": "订单状态",
    "orderStatusPlaceholder": "请选择订单状态",
    "paymentStatus": "支付状态",
    "paymentStatusPlaceholder": "请选择支付状态",
    "createDateRange": "下单日期",
    "createDateRangePlaceholder": "请选择下单日期范围",
    "exportExcel": "导出Excel",
    "customerName": "下单人",
    "customerPhone": "下单人手机号",
    "buyerName": "购车人",
    "buyerPhone": "购车人手机号",
    "buyerType": "购车人类别",
    "vin": "VIN",
    "paymentMethod": "付款方式",
    "loanApprovalStatus": "贷款审核状态",
    "approvalStatus": "订单审核状态",
    "insuranceStatus": "投保状态",
    "jpjRegistrationStatus": "JPJ车辆注册状态",
    "createTime": "订单创建时间",
    "color": "颜色",
    "monthlyOrderCount": "本月订单数量",
    "todayOrderCount": "今日订单数量",
    "topStore": "订单最多门店",
    "hotModel": "最热销车型",
    "pendingDeliveryCount": "待交车订单",
    "growth": "环比增长",
    "thisMonthOrders": "本月订单量",
    "orderStatuses": {
      "vehicleModel": {
        "title": "车辆车型主数据管理",
        "model": "车型",
        "variantName": "版本名称",
        "variantCode": "版本代码",
        "colourName": "颜色名称",
        "colourCode": "颜色代码",
        "fmrid": "FMRID",
        "createTime": "创建时间",
        "updateTime": "更新时间",
        "syncData": "同步数据",
        "syncLog": "同步日志",
        "exportData": "导出数据",
        "modelPlaceholder": "请选择车型",
        "variantNamePlaceholder": "请选择版本名称",
        "colourNamePlaceholder": "请选择颜色名称",
        "fmridPlaceholder": "请输入FMRID",
        "syncSuccess": "数据同步成功",
        "exportSuccess": "数据导出成功",
        "syncLogTitle": "数据同步日志"
      }
    }
  }
}
      "pending_deposit": "待支付定金",
      "confirmed": "已确认",
      "cancelled": "已取消",
      "pending_delivery": "待交车",
      "completed": "已完成"
    },
    "paymentStatuses": {
      "pending_deposit": "待支付定金",
      "deposit_paid": "定金已付",
      "pending_final": "待支付尾款",
      "fully_paid": "已全款"
    },
    "paymentMethods": {
      "full_payment": "全款",
      "installment": "分期"
    },
    "approvalStatuses": {
      "pending": "待审批",
      "approved": "已审批-通过",
      "rejected": "已审批-驳回"
    },
    "insuranceStatuses": {
      "pending": "未投保",
      "completed": "投保完成",
      "failed": "投保失败"
    },
    "jpjRegistrationStatuses": {
      "pending": "待登记",
      "completed": "已登记",
      "failed": "登记失败"
    },
    "editOrder": "编辑订单",
    "orderDetail": "订单详情",
    "personalDetails": "客户信息",
    "preferredOutletSalesAdvisor": "购车门店信息",
    "purchaseDetails": "购车信息",
    "vehicleInfo": "车辆信息",
    "invoiceInfo": "开票信息",
    "serviceRightsInfo": "服务&权益信息",
    "paymentInfo": "支付信息",
    "insuranceInfo": "保险信息",
    "otrFeesInfo": "OTR费用信息",
    "addRights": "新增权益",
    "pushToInsurance": "推送至保险系统",
    "submitDelivery": "提交交车",
    "backToList": "返回列表",
    "save": "保存",
    "salesSubtotal": "销售小计",
    "numberPlatesFee": "车牌费",
    "totalAmount": "整车开票价",
    "deposit": "定金",
    "finalPayment": "尾款金额",
    "remainingReceivable": "剩余应收",
    "loanAmount": "贷款金额",
    "loanTerm": "贷款期数",
    "accessoryTotalAmount": "选配件总金额",
    "rightsTotalDiscount": "权益优惠总金额",
    "insuranceTotalAmount": "保险总金额",
    "otrFeesTotalAmount": "登记费用总金额",
    "accessories": "选配件信息",
    "accessoryCode": "配件代码",
    "accessoryName": "配件名称",
    "accessoryCategory": "类别",
    "accessoryPrice": "配件单价",
    "accessoryQuantity": "数量",
    "accessoryTotalPrice": "总价",
    "rights": "权益列表",
    "rightCode": "权益代码",
    "rightName": "权益名称",
    "rightMode": "权益模式",
    "rightDiscountAmount": "优惠金额",
    "rightEffectiveDate": "生效日期",
    "rightExpiryDate": "终止日期",
    "deleteRight": "删除",
    "policyNumber": "保单号",
    "insuranceType": "保险类型",
    "insuranceCompany": "保险公司",
    "insuranceAmount": "保险价格",
    "insuranceEffectiveDate": "生效日期",
    "insuranceExpiryDate": "到期日期",
    "insuranceNotes": "备注",
    "otrTicketNumber": "票据单号",
    "otrFeeItem": "收费项目",
    "otrFeePrice": "收费价格",
    "otrEffectiveDate": "生效日期",
    "otrExpiryDate": "到期日期",
    "buyerIdType": "购车人身份证件类别",
    "buyerIdNumber": "购车人身份证件号",
    "buyerEmail": "购车人邮箱",
    "buyerAddress": "购车人地址",
    "buyerState": "购车人所在州",
    "buyerCity": "购车人所在城市",
    "buyerPostalCode": "购车人所在地邮编",
    "storeState": "所在州",
    "storeCity": "所在城市",
    "salesConsultant": "销售顾问",
    "invoicingType": "开票",
    "invoicingName": "开票名称",
    "invoicingPhone": "开票电话",
    "invoicingAddress": "开票地址",
    "confirmColorChange": "您已更改车辆颜色，是否提交审核？",
    "confirmSubmitDelivery": "确定要提交交车吗？",
    "deliveryConditionsNotMet": "交车前置条件不满足",
    "selectRights": "选择权益",
    "availableRights": "可用权益",
    "selectAll": "全选",
    "addSelectedRights": "添加"
  },
  "vehicleQuery": {
    "title": "车辆查询",
    "vin": "车架号",
    "vinPlaceholder": "请输入车架号",
    "factoryOrderNo": "工厂订单号",
    "factoryOrderNoPlaceholder": "请输入工厂订单号",
    "warehouseName": "仓库名称",
    "warehouseNamePlaceholder": "请选择仓库",
    "model": "车型",
    "modelPlaceholder": "请选择车型",
    "variant": "配置",
    "variantPlaceholder": "请先选择车型",
    "color": "颜色",
    "colorPlaceholder": "请先选择配置",
    "lockStatus": "锁定状态",
    "lockStatusPlaceholder": "请选择锁定状态",
    "locked": "已锁定",
    "unlocked": "未锁定",
    "invoiceStatus": "开票状态",
    "invoiceStatusPlaceholder": "请选择开票状态",
    "invoiced": "已开票",
    "notInvoiced": "未开票",
    "invoiceDate": "开票时间",
    "storageDate": "入库时间",
    "productionDate": "生产时间",
    "stockStatus": "库存状态",
    "deliveryStatus": "交车状态",
    "deliveryDate": "交车时间",
    "stockStatusOptions": {
      "inStock": "在库",
      "allocated": "配车",
      "inTransit": "在途",
      "transferred": "调拨"
    },
    "lockStatusOptions": {
      "locked": "已锁定",
      "unlocked": "未锁定"
    },
    "invoiceStatusOptions": {
      "invoiced": "已开票",
      "notInvoiced": "未开票"
    },
    "deliveryStatusOptions": {
      "delivered": "已交车",
      "notDelivered": "未交车"
    },
    "detailDialogTitle": "车辆详情",
    "exportDialogTitle": "导出数据",
    "exportFormat": "导出格式",
    "exportFormatPlaceholder": "请选择导出格式",
    "excel": "Excel",
    "csv": "CSV",
    "exportScope": "导出范围",
    "exportCurrentPage": "当前页数据",
    "exportAllData": "全部数据",
    "exportSuccess": "导出{format}文件成功！",
    "fetchWarehouseFailed": "获取仓库信息失败",
    "fetchConfigFailed": "获取车型配置失败"
  },
  "vehicleRegistration": {
    "title": "车辆登记管理",
    "orderNumber": "订单编号",
    "orderNumberPlaceholder": "请输入订单编号",
    "customerName": "客户姓名",
    "customerNamePlaceholder": "请输入客户姓名",
    "customerPhone": "客户手机号",
    "customerPhonePlaceholder": "请输入客户手机号",
    "registrationStatus": "登记状态",
    "registrationStatusAll": "全部状态",
    "registrationStatusPending": "待登记",
    "registrationStatusProcessing": "登记中",
    "registrationStatusSuccess": "登记成功",
    "registrationStatusFailed": "登记失败",
    "vin": "VIN",
    "vinPlaceholder": "请输入VIN",
    "insuranceStatus": "投保状态",
    "insuranceStatusAll": "全部状态",
    "insuranceStatusInsured": "已投保",
    "insuranceStatusNotInsured": "未投保",
    "pushTimeRange": "推送时间范围",
    "salesAdvisor": "销售顾问",
    "salesAdvisorAll": "全部销售顾问",
    "export": "导出",
    "loadingData": "数据加载中...",
    "serialNumber": "序号",
    "vehicleModel": "车型",
    "vehicleColor": "车身颜色",
    "insuranceCompany": "保险公司",
    "pushTime": "推送时间",
    "registrationFee": "登记费用",
    "operations": "操作",
    "pushRegistration": "推送登记",
    "viewDetails": "查看详情",
    "pushConfirmTitle": "确认推送登记",
    "pushConfirmContent": "您确定要推送此车辆进行登记吗？",
    "confirmPush": "确认推送",
    "retryPushTitle": "重新推送登记",
    "failureReason": "失败原因",
    "retryPushContent": "您确定要重新推送此车辆进行登记吗？",
    "confirmRetry": "确认重新推送",
    "orderDetailsTitle": "订单详情",
    "orderBasicInfo": "订单基本信息",
    "orderStatus": "订单状态",
    "createTime": "创建时间",
    "customerInfo": "购车人信息",
    "idType": "证件类型",
    "idNumber": "证件号码",
    "email": "邮箱",
    "address": "地址",
    "city": "城市",
    "state": "州/省",
    "postcode": "邮政编码",
    "vehicleInfo": "车辆信息",
    "engineNumber": "发动机号",
    "modelCode": "型号代码",
    "variant": "版本",
    "productionYear": "生产年份",
    "manufactureDate": "制造日期",
    "insuranceInfo": "保险信息",
    "policyNumber": "保单号",
    "insurancePeriod": "保险期限",
    "insuranceDate": "投保日期",
    "insuranceFee": "保险费用",
    "jpjInfo": "JPJ登记信息",
    "certificateNumber": "证书编号",
    "completionTime": "完成时间",
    "operator": "操作员",
    "retryPush": "重新推送",
    "feeDetails": "费用明细",
    "feeType": "费用类型",
    "feeAmount": "费用金额",
    "operationLogs": "操作日志",
    "operationTime": "操作时间",
    "operationType": "操作类型",
    "operationResult": "操作结果",
    "remark": "备注",
    "pushSuccess": "推送成功！",
    "pushFailed": "推送失败！",
    "retrySuccess": "重新推送成功！",
    "retryFailed": "重新推送失败！",
    "exportSuccess": "导出成功！",
    "exportFailed": "导出失败！"
  },
  "dispatch": {
    "pageTitle": "派工管理",
    "workOrderNo": "工单号",
    "workOrderNoPlaceholder": "请输入工单号",
    "priority": "优先级",
    "priorityUrgent": "紧急",
    "priorityNormal": "普通",
    "workOrderType": "工单类型",
    "typeRepair": "维修",
    "typeMaintenance": "保养",
    "typeInsurance": "保险",
    "assignmentStatus": "分配状态",
    "statusPendingAssignment": "待分配",
    "statusAssigned": "已分配",
    "workOrderStatus": "工单状态",
    "statusPendingStart": "待开工",
    "statusInProgress": "进行中",
    "statusPendingQualityInspection": "待质检",
    "statusPendingRework": "待返工",
    "statusCompleted": "已完成",
    "statusAdditionalItemsPendingConfirmation": "增项待确认",
    "statusWaitingForParts": "待配件",
    "workOrderCreationTime": "工单创建时间",
    "startTime": "开始时间",
    "endTime": "结束时间",
    "customerSource": "客户来源",
    "sourceAppointment": "预约",
    "sourceWalkIn": "自然到店",
    "repairmanName": "送修人姓名",
    "repairmanNamePlaceholder": "请输入送修人姓名",
    "repairmanPhone": "送修人电话",
    "licensePlateNumber": "车牌号",
    "licensePlateNumberPlaceholder": "请输入车牌号",
    "vehicleModel": "车型",
    "configuration": "配置",
    "color": "颜色",
    "vehicleAge": "车龄",
    "months": "月",
    "mileage": "里程",
    "km": "公里",
    "serviceAdvisor": "服务顾问",
    "technician": "技师",
    "estimatedWorkHours": "预计工时",
    "estimatedStartTime": "预计开工时间",
    "estimatedFinishTime": "预计完工时间",
    "actualStartTime": "实际开工时间",
    "actualFinishTime": "实际完工时间",
    "assign": "分配",
    "reassign": "重新分配",
    "startWork": "开工",
    "pause": "暂停",
    "resume": "恢复",
    "completeWork": "完工",
    "detailDialogTitle": "工单详情",
    "detailDialogContent": "工单详情内容",
    "assignDialogTitle": "分配工单",
    "assignedTechnician": "分配技师",
    "assignmentNotes": "分配备注",
    "assignmentNotesPlaceholder": "请输入分配备注",
    "reassignDialogTitle": "重新分配工单",
    "originalTechnician": "原技师",
    "newTechnician": "新技师",
    "reassignmentReason": "重新分配原因",
    "reassignmentReasonPlaceholder": "请输入重新分配原因",
    "assignSuccess": "分配成功",
    "reassignSuccess": "重新分配成功",
    "startWorkSuccess": "开工成功：",
    "resumeWorkSuccess": "恢复工作成功：",
    "pauseWorkSuccess": "暂停工作成功：",
    "completeWorkSuccess": "完工成功：",
    "status": {
      "pending": "待分配",
      "assigned": "已分配",
      "pendingAssignment": "待分配",
      "pendingStart": "待开工",
      "inProgress": "进行中",
      "pendingQualityInspection": "待质检",
      "pendingRework": "待返工",
      "completed": "已完成",
      "additionalPendingConfirmation": "增项待确认",
      "waitingForParts": "待配件"
    },
    "source": {
      "appointment": "预约",
      "walkIn": "自然到店"
    }
  },
  "delivery": {
    "pageTitle": "交车管理",
    "searchTitle": "筛选查询",
    "deliveryNumber": "交车单号",
    "deliveryNumberPlaceholder": "请输入交车单号",
    "orderNumber": "订单编号",
    "orderNoPlaceholder": "请输入订单编号",
    "customerName": "购车人名称",
    "customerNamePlaceholder": "请输入购车人名称",
    "customerPhone": "购车人手机号",
    "customerMobilePlaceholder": "请输入手机号",
    "vin": "VIN",
    "vinPlaceholder": "请输入VIN",
    "orderStatus": "订单状态",
    "orderStatusPlaceholder": "请选择订单状态",
    "orderStatusPendingAllocation": "待分配",
    "orderStatusAllocating": "分配中",
    "orderStatusAllocated": "已分配",
    "orderStatusPendingDelivery": "待交车",
    "orderStatusDelivered": "已交车",
    "dealerStore": "门店",
    "dealerStorePlaceholder": "请选择门店",
    "salesConsultant": "销售顾问",
    "salesmanPlaceholder": "请输入销售顾问",
    "deliveryStatus": "交车状态",
    "deliveryStatusPlaceholder": "请选择交车状态",
    "statusPending": "待交车",
    "statusConfirming": "待确认",
    "statusCompleted": "已交车",
    "customerConfirmed": "客户是否确认",
    "customerConfirmedPlaceholder": "请选择客户是否确认",
    "confirmationType": "客户确认类型",
    "confirmationTypePlaceholder": "请选择客户确认类型",
    "confirmationTypeApp": "APP",
    "confirmationTypeOffline": "线下",
    "deliveryTime": "交车时间",
    "startDate": "开始日期",
    "deliveryTimeEnd": "结束日期",
    "invoiceTimeStart": "开票开始时间",
    "invoiceTimeEnd": "开票结束时间",
    "listTitle": "交车单列表",
    "totalCount": "共 {count} 条",
    "model": "车型",
    "variant": "配置",
    "color": "颜色",
    "actualDeliveryDate": "实际交车日期",
    "deliveryNotes": "交车备注",
    "signaturePhoto": "签字照片",
    "submitConfirm": "提交确认",
    "deliveryConfirm": "交车确认",
    "confirmDeliveryTitle": "确认交车",
    "confirmDeliveryContent": "您确定要确认此交车单吗？",
    "submitConfirmSuccess": "提交确认成功！",
    "submitConfirmFailed": "提交确认失败！",
    "deliveryConfirmSuccess": "交车确认成功！",
    "deliveryConfirmFailed": "交车确认失败！",
    "fetchDataFailed": "获取交车单数据失败！",
    "detailNotFound": "未找到交车单详情！",
    "fetchDetailFailed": "获取交车单详情失败！",
    "noDetailData": "暂无详情数据",
    "printFeatureNotImplemented": "打印功能暂未实现！",
    "exportSuccess": "导出成功！",
    "exportFailed": "导出失败！",
    "orderStatusNormal": "正常",
    "orderStatusCancelled": "已取消",
    "confirmSubmitDeliveryOrder": "确认要提交以下交车单吗？",
    "deliveryNumber": "交车单号",
    "orderNumber": "订单编号",
    "customerName": "购车人",
    "customerPhone": "手机号",
    "deliveryStatusChangeNote": "提交后，交车状态将从\"{from}\"变更为\"{to}\"，等待客户确认或销售顾问完成交车确认。"
  },
  "payment": {
    "title": "整车收款管理",
    "orderNumber": "订单编号",
    "buyerName": "购车人名称",
    "buyerPhone": "购车人手机号",
    "dealerStore": "订车门店",
    "salesConsultant": "销售顾问",
    "vin": "VIN",
    "model": "Model",
    "variant": "Variant",
    "color": "Color",
    "orderCreateTime": "订单创建时间",
    "orderStatus": "订单状态",
    "paymentStatus": "订单支付状态",
    "vehicleSalesPrice": "车辆销售价格",
    "insuranceAmount": "保险金额",
    "otrAmount": "OTR金额",
    "discountAmount": "订单优惠金额",
    "totalAmount": "订单总金额",
    "paidAmount": "订单已支付金额",
    "unpaidAmount": "订单未支付金额",
    "loanAmount": "贷款金额",
    "canInvoice": "是否可开票",
    "invoiceTime": "开票时间",
    "invoiceNumber": "发票号",
    "createTime": "创建时间",
    "updateTime": "更新时间",
    "paymentOperation": "收退款操作",
    "orderDetail": "订单详情",
    "paymentRecords": "收退款历史记录",
    "addPaymentRecord": "添加收退款信息",
    "enterOrderNumber": "请输入订单编号",
    "enterBuyerName": "请输入购车人名称",
    "enterBuyerPhone": "请输入购车人手机号",
    "selectOrderStatus": "请选择订单状态",
    "selectPaymentStatus": "请选择订单支付状态",
    "selectCanInvoice": "请选择是否可开票",
    "selectDateRange": "请选择时间范围",
    "orderStatusSubmitted": "已提交",
    "orderStatusCancelPending": "取消审核中",
    "orderStatusCancelApproved": "取消审核通过",
    "orderStatusCancelled": "已取消",
    "orderStatusConfirmed": "已确认",
    "orderStatusPendingReview": "待审核",
    "orderStatusReviewed": "已审核",
    "orderStatusPendingDelivery": "待交车",
    "orderStatusDelivered": "已交车",
    "paymentStatusPendingDeposit": "待支付定金",
    "paymentStatusDepositPaid": "已支付定金",
    "paymentStatusRefunding": "退款中",
    "paymentStatusRefunded": "退款完成",
    "paymentStatusPendingFinal": "待支付尾款",
    "paymentStatusFullyPaid": "已支付尾款",
    "businessType": "业务类型",
    "transactionNumber": "流水号",
    "channel": "渠道",
    "amount": "金额",
    "paymentType": "收款类型",
    "arrivalTime": "到账时间",
    "remark": "备注",
    "creator": "创建人",
    "dataSource": "数据来源",
    "payment": "收款",
    "refund": "退款",
    "channelAPP": "APP",
    "channelBankCard": "银行卡",
    "channelTransfer": "转账",
    "paymentTypeBookFee": "Book Fee",
    "paymentTypeLoan": "贷款",
    "paymentTypeFinal": "尾款",
    "dataSourceManual": "手动录入",
    "dataSourceApp": "APP推送",
    "paymentMethodFull": "全款",
    "paymentMethodLoan": "贷款",
    "transactionNumberRequired": "请输入流水号",
    "transactionNumberExists": "流水号已存在",
    "amountRequired": "请输入金额",
    "amountInvalid": "请输入有效金额",
    "arrivalTimeRequired": "请选择到账时间",
    "operationNotAllowed": "当前状态不允许此操作",
    "appDataCannotDelete": "APP数据不可删除",
    "confirmDelete": "确认删除该记录？",
    "deleteSuccess": "删除成功",
    "addSuccess": "添加成功",
    "orderInfo": "订单基础信息",
    "customerInfo": "客户详细信息",
    "personalDetails": "Personal Details",
    "ordererInfo": "下单人信息",
    "buyerInfo": "购车人信息",
    "ordererName": "下单人姓名",
    "ordererPhone": "下单人手机号",
    "buyerIdType": "身份证件类别",
    "buyerIdNumber": "身份证件号",
    "buyerEmail": "购车人邮箱",
    "buyerAddress": "购车人详细地址",
    "buyerState": "购车人所在州",
    "buyerCity": "购车人所在城市",
    "buyerPostcode": "购车人所在地邮编",
    "storeInfo": "门店与销售信息",
    "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor",
    "dealerRegion": "所在地区",
    "dealerCity": "所在城市",
    "salesConsultantPhone": "联系电话",
    "salesConsultantEmail": "邮箱地址",
    "vehicleInfo": "车辆详细信息",
    "options": "选装件",
    "warehouseName": "仓库",
    "productionDate": "生产日期",
    "priceInfo": "订单价格详细信息",
    "salesSubtotal": "销售小计(包含GASA)",
    "consumptionTax": "消费税",
    "salesTax": "销售税",
    "numberPlatesFee": "车牌费（Number Plates）",
    "optionsPrice": "选装包价格",
    "vehicleSalesSubtotal": "车辆销售价格小计",
    "otrAmountDetail": "OTR金额",
    "paymentRecordNumber": "收退款单号",
    "totalPayment": "总收款金额",
    "totalRefund": "总退款金额",
    "netPayment": "净收款金额",
    "recordCount": "记录总数",
    "enterTransactionNumber": "请输入流水号",
    "enterAmount": "请输入金额",
    "enterRemark": "请输入备注信息",
    "selectBusinessType": "请选择业务类型",
    "selectChannel": "请选择渠道",
    "selectPaymentType": "请选择收款类型",
    "selectArrivalTime": "请选择到账时间",
    "paymentOperation": "收退款操作",
    "customerDetailInfo": "客户详细信息",
    "ordererInfo": "下单人信息",
    "ordererName": "下单人姓名",
    "ordererPhone": "下单人手机号",
    "buyerInfo": "购车人信息",
    "buyerIdType": "身份证件类别",
    "buyerIdNumber": "身份证件号",
    "variant": "Variant",
    "color": "Color",
    "vin": "VIN",
    "paymentMethod": "支付方式",
    "totalAmount": "总金额",
    "pageTitle": "销售订单列表",
    "searchTitle": "搜索条件",
    "batchOperation": "批量操作",
    "listTitle": "销售订单列表",
    "totalCount": "总数",
    "detailTitle": "销售订单详情",
    "statusSubmitted": "已提交",
    "statusConfirmed": "已确认",
    "statusCancelPending": "取消待处理",
    "statusCancelApproved": "取消已批准",
    "statusCancelled": "已取消",
    "statusPendingDelivery": "待交付",
    "statusDelivered": "已交付",
    "viewDetail": "详情",
    "editOrder": "编辑",
    "returnToList": "返回列表",
    "exportSuccess": "导出成功",
    "exportFailed": "导出失败",
    "noDataToExport": "没有数据可导出",
    "confirmExportTitle": "确认导出",
    "confirmExportMessage": "确定要导出当前筛选条件下的所有订单数据吗？",
    "orderDetail": "销售订单详情",
    "customerInfo": "客户信息",
    "storeInfo": "购车门店信息",
    "purchaseInfo": "购车信息",
    "vehicleInfoTab": "车辆信息",
    "invoicingInfoTab": "开票信息",
    "rightsInfoTab": "服务&权益信息",
    "paymentInfoTab": "支付信息",
    "insuranceInfoTab": "保险信息",
    "otrFeesTab": "OTR费用信息",
    "changeRecords": "订单变更记录",
    "buyerIdType": "购车人身份证件类别",
    "buyerIdNumber": "购车人身份证件号",
    "buyerEmail": "购车人邮箱",
    "buyerState": "所在州",
    "buyerCity": "所在城市",
    "buyerPostcode": "邮编",
    "buyerAddress": "购车人详细地址",
    "dealerAndSalesInfo": "门店与销售信息",
    "dealerRegion": "所在地区",
    "dealerCity": "所在城市",
    "salesConsultantInfo": "销售顾问信息",
    "salesConsultantPhone": "联系电话",
    "salesConsultantEmail": "邮箱地址",
    "vehicleDetailInfo": "车辆详细信息",
    "options": "选装件",
    "warehouse": "仓库",
    "productionDate": "生产日期",
    "optionsTotalPrice": "选装件总价",
    "priceDetailInfo": "订单价格详细信息",
    "salesSubtotalIncludeGASA": "销售小计(包含GASA)",
    "salesSubtotalIncludeGASADesc": "含各种税费",
    "consumptionTax": "消费税",
    "consumptionTaxDesc": "税费明细",
    "salesTax": "销售税",
    "salesTaxDesc": "税费明细",
    "numberPlatesFee": "车牌费（Number Plates）",
    "numberPlatesFeeDesc": "车牌相关费用",
    "optionsPrice": "选装包价格",
    "optionsPriceDesc": "选装件总价",
    "buyerAddress": "购车人地址",
    "buyerState": "购车人所在州",
    "buyerCity": "购车人所在城市",
    "buyerPostcode": "购车人所在地邮编",
    "storeRegion": "所在地区",
    "storeCity": "所在城市",
    "storeName": "购车门店",
    "salesConsultantName": "销售顾问",
    "salesSubtotal": "销售小计(包含GASA、消费税、销售税)",
    "numberPlatesFee": "车牌费",
    "vinCode": "VIN码",
    "accessoryInfo": "选配件信息",
    "accessoryCategory": "类别",
    "accessoryName": "配件名称",
    "unitPrice": "配件单价",
    "quantity": "数量",
    "totalPrice": "总价",
    "accessoriesTotalAmount": "选配件总金额",
    "invoicingType": "开票类型",
    "invoicingName": "开票名称",
    "invoicingPhone": "开票电话",
    "invoicingAddress": "开票地址",
    "rightsInfo": "服务&权益信息",
    "rightCode": "权益代码",
    "rightName": "权益名称",
    "rightMode": "权益模式",
    "discountAmount": "权益优惠价格",
    "effectiveDate": "权益生效日期",
    "expiryDate": "权益终止日期",
    "rightsDiscountAmount": "权益优惠总金额",
    "loanApprovalStatusField": "贷款资质审核状态",
    "depositAmount": "定金金额",
    "loanAmount": "贷款金额",
    "balanceAmount": "尾款金额",
    "insuranceInfo": "保单信息",
    "policyNumber": "保单号",
    "insuranceType": "保险类型",
    "insuranceCompany": "保险公司",
    "insurancePrice": "保险价格",
    "insuranceTotalAmount": "保险总金额",
    "insuranceNotes": "备注",
    "otrFeesInfo": "On The Road登记费用",
    "ticketNumber": "票据单号",
    "feeItem": "收费项目",
    "feePrice": "收费价格",
    "otrFeesTotalAmount": "OTR费用总金额",
    "changeRecordIndex": "序号",
    "originalContent": "原始内容",
    "changedContent": "变更后内容",
    "operator": "操作人",
    "operationTime": "操作时间",
    "totalInvoiceAmount": "整车开票价",
    "remainingAmount": "剩余应收",
    "editOrderTitle": "销售订单编辑",
    "personalDetails": "客户信息 - Personal Details",
    "preferredOutlet": "购车门店信息 - Preferred Outlet & Sales Advisor",
    "purchaseDetails": "购车信息 - Purchase Details",
    "addRights": "新增权益",
    "selectRights": "选择权益",
    "rightCodeSearch": "权益代码搜索",
    "rightNameSearch": "权益名称搜索",
    "selectAll": "全选",
    "addSelected": "添加",
    "deleteRight": "删除权益",
    "confirmDeleteRight": "确定要删除此权益吗？",
    "pushToInsurance": "推送至保险系统",
    "insurancePushed": "已推送",
    "confirmPushInsurance": "确定要推送至保险系统吗？",
    "pushInsuranceSuccess": "推送保险系统成功",
    "pushInsuranceFailed": "推送保险系统失败",
    "submitDelivery": "提交交车",
    "confirmSubmitDelivery": "确定要提交交车申请吗？",
    "submitDeliverySuccess": "提交交车成功",
    "submitDeliveryFailed": "提交交车失败",
    "deliveryConditionsNotMet": "交车条件不满足",
    "colorChangeNotice": "您已更改车辆颜色，是否提交审核？",
    "colorChangeSubmitted": "颜色变更已提交审核",
    "roadTax": "路税",
    "registrationFee": "注册/过户费", 
    "ownershipClaimFee": "所有权索赔费",
    "interchangeFee": "咨询费",
    "otrFeeTotal": "登记费用总金额",
    "loanTerm": "贷款期数（月）",
    "loanTermPlaceholder": "请输入贷款期数",
    "loanAmountPlaceholder": "请输入贷款金额",
    "statusDelivered": "已交付",
    "buyerTypeIndividual": "个人",
    "buyerTypeCompany": "公司",
    "approvalStatusPending": "待审批",
    "approvalStatusApproved": "已审批",
    "paymentStatusPendingDeposit": "待付定金",
    "paymentStatusDepositPaid": "定金已付",
    "paymentStatusPendingBalance": "待付尾款",
    "paymentStatusBalancePaid": "尾款已付",
    "paymentStatusRefunding": "退款中",
    "paymentStatusRefunded": "已退款",
    "insuranceStatusNotInsured": "未投保",
    "insuranceStatusInsuring": "投保中",
    "insuranceStatusInsured": "已投保",
    "loanApprovalStatusPending": "待审核",
    "loanApprovalStatusApproved": "已通过",
    "loanApprovalStatusRejected": "已驳回",
    "jpjStatusPending": "待注册",
    "jpjStatusRegistering": "注册中",
    "jpjStatusRegistered": "已注册",
    "jpjStatusFailed": "注册失败",
    "paymentMethodInstallment": "分期",
    "batchOperationTip": "请至少选择一条记录进行批量操作",
    "buyerTypes": {
      "individual": "个人",
      "company": "公司"
    }
  },
  "quotaManagement": {
    "pageTitle": "预约限量管理",
    "storeName": "门店名称",
    "storeCode": "门店编号",
    "permissionTip": "您当前查看的是默认门店的预约限量配置。如需配置其他门店，请联系管理员。",
    "configuredListTitle": "已配置限量列表",
    "addNewQuota": "新增预约限量",
    "editQuotaTitle": "编辑预约限量",
    "addNewQuotaTitle": "新增预约限量",
    "emptyState": "暂未配置预约限量。点击\"新增预约限量\"进行配置。",
    "unsavedChangesWarning": "您有未保存的更改，确定要关闭吗？",
    "dateMustBeFutureOrToday": "配置日期不能早于今天",
    "selectDateFirst": "请先选择日期",
    "timeSlotExceedsOperatingHours": "时段设置超过营业时间范围（8:00-18:00）",
    "table": {
      "configDate": "配置日期",
      "timeSlotCount": "时段数量",
      "totalQuota": "总限量",
      "bookedQuantity": "已预约数量",
      "lastUpdateTime": "最后更新时间",
      "operations": "操作",
      "edit": "编辑",
      "expired": "已过期"
    },
    "modal": {
      "selectDateTitle": "选择日期",
      "dateLabel": "配置日期",
      "datePlaceholder": "请选择配置日期",
      "existingConfig": "已有配置",
      "dateTip": "配置日期不能早于今天",
      "timeSlotConfigTitle": "时段配置",
      "addTimeSlot": "添加时段",
      "noTimeSlots": "尚未添加时段",
      "clickAddPrompt": "点击\"添加时段\"开始配置",
      "timeSlot": "时段",
      "startTime": "开始时间",
      "startTimePlaceholder": "请选择开始时间",
      "endTime": "结束时间",
      "endTimePlaceholder": "请选择结束时间",
      "quota": "限量",
      "quotaPlaceholder": "请输入限量",
      "configDescriptionTitle": "配置说明",
      "configDescription": {
        "item1": "每个时段的限量表示该时段最多可预约的车辆数量。",
        "item2": "时段之间不能重叠，结束时间必须晚于开始时间。",
        "item3": "建议根据门店实际接待能力设置合理的限量。",
        "item4": "所有时段限量之和即为当天的总限量。"
      }
    },
    "validation": {
      "dateRequired": "请选择配置日期",
      "atLeastOneTimeSlot": "至少需要添加一个时段",
      "timeRequired": "请设置时段的开始和结束时间",
      "startBeforeEnd": "开始时间必须早于结束时间",
      "quotaPositive": "限量必须大于0",
      "timeSlotOverlap": "时段 {slot1} 与 {slot2} 时间重叠，请调整"
    },
    "summary": {
      "selectDate": "请选择日期",
      "addTimeSlot": "请添加时段",
      "timeSlotsUnit": "个时段",
      "totalQuota": "总限量"
    }
  },
  "salesOrderManagement": {
    "title": "销售订单列表",
    "breadcrumb": "客户订单管理 / 销售订单列表",
    "searchForm": "搜索条件",
    "operationButtons": "操作",
    "orderList": "订单列表",
    "buyerName": "购车人",
    "buyerNamePlaceholder": "请输入购车人姓名",
    "buyerPhone": "购车人手机号",
    "buyerPhonePlaceholder": "请输入购车人手机号",
    "buyerType": "购车人类别",
    "buyerTypePlaceholder": "请选择购车人类别",
    "model": "车型",
    "modelPlaceholder": "请选择车型",
    "orderNumber": "订单号",
    "orderNumberPlaceholder": "请输入订单号",
    "orderStatus": "订单状态",
    "orderStatusPlaceholder": "请选择订单状态",
    "approvalStatus": "订单审核状态",
    "approvalStatusPlaceholder": "请选择订单审核状态",
    "paymentStatus": "订单支付状态",
    "paymentStatusPlaceholder": "请选择订单支付状态",
    "createTime": "订单创建时间",
    "createTimeRange": "订单创建时间范围",
    "insuranceStatus": "投保状态",
    "insuranceStatusPlaceholder": "请选择投保状态",
    "loanApprovalStatus": "贷款审核状态",
    "loanApprovalStatusPlaceholder": "请选择贷款审核状态",
    "jpjRegistrationStatus": "JPJ车辆注册状态",
    "jpjRegistrationStatusPlaceholder": "请选择JPJ车辆注册状态",
    "index": "序号",
    "ordererName": "下单人",
    "ordererPhone": "下单人手机号",
    "variant": "Variant",
    "color": "Color",
    "vin": "VIN",
    "paymentMethod": "支付方式",
    "totalAmount": "总金额",
    "pageTitle": "销售订单列表",
    "searchTitle": "搜索条件",
    "batchOperation": "批量操作",
    "listTitle": "销售订单列表",
    "totalCount": "总数",
    "detailTitle": "销售订单详情",
    "statusSubmitted": "已提交",
    "statusConfirmed": "已确认",
    "statusCancelPending": "取消待处理",
    "statusCancelApproved": "取消已批准",
    "statusCancelled": "已取消",
    "statusPendingDelivery": "待交付",
    "statusDelivered": "已交付",
    "buyerTypes": {
      "individual": "个人",
      "company": "公司"
    },
    "models": {
      "AXIA": "AXIA",
      "BEZZA": "BEZZA", 
      "MYVI": "MYVI"
    },
    "orderStatuses": {
      "submitted": "已提交",
      "confirmed": "已确认",
      "cancel_pending": "取消审核中",
      "cancel_approved": "取消审核通过",
      "cancelled": "已取消",
      "pending_delivery": "待交车",
      "delivered": "已交车"
    },
    "approvalStatuses": {
      "pending_approval": "待审批",
      "approved": "已审批"
    },
    "paymentStatuses": {
      "pending_deposit": "待支付定金",
      "deposit_paid": "已支付定金",
      "pending_balance": "待支付尾款",
      "balance_paid": "已支付尾款",
      "refunding": "退款中",
      "refunded": "退款完成"
    },
    "insuranceStatuses": {
      "not_insured": "未投保",
      "insuring": "投保中",
      "insured": "投保完成"
    },
    "loanApprovalStatuses": {
      "pending_review": "待审批",
      "approved": "已通过",
      "rejected": "已驳回"
    },
    "jpjRegistrationStatuses": {
      "pending_registration": "待注册",
      "registering": "注册中",
      "registered": "已注册",
      "registration_failed": "注册失败"
    },
    "paymentMethods": {
      "full_payment": "全款",
      "installment": "分期"
    },
    "viewDetail": "详情",
    "editOrder": "编辑",
    "returnToList": "返回列表",
    "exportSuccess": "导出成功",
    "exportFailed": "导出失败",
    "noDataToExport": "没有数据可导出",
    "confirmExportTitle": "确认导出",
    "confirmExportMessage": "确定要导出当前筛选条件下的所有订单数据吗？",
    "orderDetail": "销售订单详情",
    "customerInfo": "客户信息",
    "storeInfo": "购车门店信息",
    "purchaseInfo": "购车信息",
    "vehicleInfoTab": "车辆信息",
    "invoicingInfoTab": "开票信息", 
    "rightsInfoTab": "服务&权益信息",
    "paymentInfoTab": "支付信息",
    "insuranceInfoTab": "保险信息",
    "otrFeesTab": "OTR费用信息",
    "changeRecords": "订单变更记录",
    "buyerIdType": "购车人身份证件类别",
    "buyerIdNumber": "购车人身份证件号",
    "buyerEmail": "购车人邮箱",
    "buyerAddress": "购车人地址",
    "buyerState": "购车人所在州",
    "buyerCity": "购车人所在城市",
    "buyerPostcode": "购车人所在地邮编",
    "storeRegion": "所在地区",
    "storeCity": "所在城市",
    "storeName": "购车门店",
    "salesConsultantName": "销售顾问",
    "salesSubtotal": "销售小计(包含GASA、消费税、销售税)",
    "numberPlatesFee": "车牌费",
    "vinCode": "VIN码",
    "accessoryInfo": "选配件信息",
    "accessoryCategory": "类别",
    "accessoryName": "配件名称",
    "unitPrice": "配件单价",
    "quantity": "数量",
    "totalPrice": "总价",
    "accessoriesTotalAmount": "选配件总金额",
    "invoicingType": "开票类型",
    "invoicingName": "开票名称",
    "invoicingPhone": "开票电话",
    "invoicingAddress": "开票地址",
    "rightsInfo": "服务&权益信息",
    "rightCode": "权益代码",
    "rightName": "权益名称",
    "rightMode": "权益模式",
    "discountAmount": "权益优惠价格",
    "effectiveDate": "权益生效日期",
    "expiryDate": "权益终止日期",
    "rightsDiscountAmount": "权益优惠总金额",
    "loanApprovalStatusField": "贷款资质审核状态",
    "depositAmount": "定金金额",
    "loanAmount": "贷款金额",
    "balanceAmount": "尾款金额",
    "insuranceInfo": "保单信息",
    "policyNumber": "保单号",
    "insuranceType": "保险类型",
    "insuranceCompany": "保险公司",
    "insurancePrice": "保险价格",
    "insuranceTotalAmount": "保险总金额",
    "insuranceNotes": "备注",
    "otrFeesInfo": "On The Road登记费用",
    "ticketNumber": "票据单号",
    "feeItem": "收费项目",
    "feePrice": "收费价格",
    "otrFeesTotalAmount": "OTR费用总金额",
    "changeRecordIndex": "序号",
    "originalContent": "原始内容",
    "changedContent": "变更后内容",
    "operator": "操作人",
    "operationTime": "操作时间",
    "totalInvoiceAmount": "整车开票价",
    "remainingAmount": "剩余应收",
    "editOrderTitle": "销售订单编辑",
    "personalDetails": "客户信息 - Personal Details",
    "preferredOutlet": "购车门店信息 - Preferred Outlet & Sales Advisor",
    "purchaseDetails": "购车信息 - Purchase Details",
    "addRights": "新增权益",
    "selectRights": "选择权益",
    "rightCodeSearch": "权益代码搜索",
    "rightNameSearch": "权益名称搜索",
    "selectAll": "全选",
    "addSelected": "添加",
    "deleteRight": "删除权益",
    "confirmDeleteRight": "确定要删除此权益吗？",
    "pushToInsurance": "推送至保险系统",
    "insurancePushed": "已推送",
    "confirmPushInsurance": "确定要推送至保险系统吗？",
    "pushInsuranceSuccess": "推送保险系统成功",
    "pushInsuranceFailed": "推送保险系统失败",
    "submitDelivery": "提交交车",
    "confirmSubmitDelivery": "确定要提交交车申请吗？",
    "submitDeliverySuccess": "提交交车成功",
    "submitDeliveryFailed": "提交交车失败",
    "deliveryConditionsNotMet": "交车条件不满足",
    "colorChangeNotice": "您已更改车辆颜色，是否提交审核？",
    "colorChangeSubmitted": "颜色变更已提交审核",
    "roadTax": "路税",
    "registrationFee": "注册/过户费", 
    "ownershipClaimFee": "所有权索赔费",
    "interchangeFee": "咨询费",
    "otrFeeTotal": "登记费用总金额",
    "loanTerm": "贷款期数（月）",
    "loanTermPlaceholder": "请输入贷款期数",
    "loanAmountPlaceholder": "请输入贷款金额",
    "loanApprovalStatusPending": "待审核",
    "loanApprovalStatusApproved": "审核通过",
    "loanApprovalStatusRejected": "审核驳回"
  },
  "workOrder": {
    "title": "工单管理",
    "number": "工单号",
    "priority": "优先级",
    "type": "工单类型",
    "isClaim": "是否理赔",
    "isOutsourced": "是否外协",
    "status": "工单状态",
    "paymentStatus": "支付状态",
    "senderName": "送修人姓名",
    "senderPhone": "送修人电话",
    "licensePlate": "车牌号",
    "serviceAdvisor": "服务顾问",
    "technician": "技师",
    "createdTime": "创建时间",
    "confirmationTime": "工单确认日期",
    "startTime": "开始时间",
    "endTime": "结束时间",
    "totalAmount": "总金额",
    "modelConfigColor": "车型配置颜色",
    "hasAdditionalItems": "是否有追加项目",
    "create": "创建工单",
    "exportWorkOrder": "导出工单",
    "addItem": "添加项目",
    "submitApproval": "提交审批",
    "priorities": {
      "urgent": "紧急",
      "normal": "普通"
    },
    "types": {
      "repair": "维修",
      "maintenance": "保养",
      "insurance": "保险"
    },
    "statuses": {
      "draft": "草稿",
      "pending_confirmation": "待确认",
      "confirmed": "已确认",
      "pending_assignment": "待分配",
      "pending_start": "待开工",
      "in_progress": "进行中",
      "pending_qc": "待质检",
      "pending_settlement": "待结算",
      "rework_required": "需返工",
      "completed": "已完成",
      "cancelled": "已取消",
      "additional_pending": "追加待审",
      "waiting_approval": "等待审批",
      "waiting_parts": "等待零件"
    },
    "paymentStatuses": {
      "pending": "待支付",
      "deposit_paid": "已付定金",
      "paid": "已支付",
      "refunding": "退款中",
      "refunded": "已退款"
    },
    "placeholders": {
      "workOrderNumber": "请输入工单号",
      "customerName": "请输入客户姓名",
      "customerPhone": "请输入客户电话",
      "licensePlate": "请输入车牌号",
      "selectServiceAdvisor": "请选择服务顾问",
      "selectDateRange": "请选择日期范围"
    },
    "messages": {
      "confirmCancel": "确定要取消工单 {workOrderNumber} 吗？"
    }
  },
  "workOrderApproval": {
    "title": "工单审批",
    "subtitle": "审批工单索赔申请和取消申请",
    "pendingApproval": "待审批",
    "completedApproval": "已审批",
    "approvalNo": "审批单号",
    "approvalNoPlaceholder": "请输入审批单号",
    "approvalType": "审批类型",
    "submitter": "提交人",
    "submitterPlaceholder": "请输入提交人",
    "orderNo": "工单编号",
    "orderNoPlaceholder": "请输入工单编号",
    "submitTime": "提交时间",
    "customer": "客户",
    "customerPlaceholder": "请输入客户姓名",
    "licensePlate": "车牌号",
    "licensePlatePlaceholder": "请输入车牌号",
    "timeoutStatus": "超时状态",
    "approvalLevel": "审批级别",
    "currentLevel": "当前审批级别",
    "requestReason": "申请原因",
    "vehicleModel": "车型",
    "store": "门店",
    "approve": "审批",
    "reject": "驳回",
    "approved": "审批通过",
    "rejected": "审批驳回",
    "normal": "正常",
    "aboutToTimeout": "即将超时",
    "timeout": "已超时",
    "firstLevel": "一级审批",
    "secondLevel": "二级审批",
    "claimApproval": "索赔审批",
    "cancelApproval": "取消工单审批",
    "pendingReview": "待审核",
    "reviewed": "已审核",
    "claimApprovalDetail": "索赔审批详情",
    "cancelApprovalDetail": "取消工单审批详情",
    "basicInfo": "基础信息",
    "customerInfo": "客户信息",
    "vehicleInfo": "车辆信息",
    "claimContent": "索赔内容",
    "workOrderStatus": "工单状态信息",
    "approvalHistory": "审批历史",
    "approvalAction": "审批操作",
    "customerName": "客户姓名",
    "customerPhone": "联系电话",
    "senderName": "送修人姓名",
    "senderPhone": "送修人电话",
    "vin": "VIN码",
    "vehicleColor": "车辆颜色",
    "saleTime": "销售时间",
    "mileage": "里程数",
    "vehicleAge": "车龄",
    "months": "月",
    "serviceTime": "送修时间",
    "currentOrderStatus": "当前工单状态",
    "estimatedStartTime": "预计开工时间",
    "assignedTechnician": "分配技师",
    "claimLabor": "索赔工时",
    "claimParts": "索赔零件",
    "claimLaborTotal": "工时总额",
    "claimPartsTotal": "零件总额",
    "grandTotal": "总计",
    "itemCode": "项目编码",
    "itemName": "项目名称",
    "quantity": "数量",
    "unitPrice": "单价",
    "claimAmount": "索赔金额",
    "hours": "小时",
    "pieces": "个",
    "laborTotal": "工时总额",
    "partsTotal": "零件总额",
    "approvalResult": "审批结果",
    "approvalResultRequired": "请选择审批结果",
    "approvalRemark": "审批备注",
    "approvalRemarkPlaceholder": "请填写审批备注（驳回时必填）",
    "approvalRemarkRequiredForReject": "审批驳回时必须填写审批备注",
    "submitApproval": "提交审批",
    "approver": "审批人",
    "approvalTime": "审批时间",
    "isOvertime": "是否超时",
    "pending": "待处理",
    "cancelReason": "取消原因",
    "draft": "草稿",
    "waitingApproval": "待审批",
    "confirmed": "已确认",
    "cancelled": "已取消",
    "assigned": "已分配",
    "inProgress": "进行中",
    "completed": "已完成",
    "noApprovalHistory": "暂无审批历史",
    "confirmSubmitApproval": "确定要提交审批吗？",
    "approvalSubmitSuccess": "审批提交成功",
    "approvalSubmitFailed": "审批提交失败",
    "loadDetailFailed": "加载详情失败",
    "approvalSuccess": "审批成功",
    "searchForm": {
      "approvalNo": "审批单号",
      "approvalNoPlaceholder": "请输入审批单号",
      "approvalType": "审批类型",
      "approvalTypePlaceholder": "请选择审批类型",
      "approvalStatus": "审批状态",
      "approvalStatusPlaceholder": "请选择审批状态",
      "submitTimeRange": "提交时间",
      "submitTimePlaceholder": "请选择时间范围",
      "submitterName": "提交人",
      "submitterNamePlaceholder": "请输入提交人",
      "orderNo": "工单编号",
      "orderNoPlaceholder": "请输入工单编号",
      "store": "门店",
      "storePlaceholder": "请选择门店",
      "customerNameOrPhone": "送修人名称/手机号",
      "customerNameOrPhonePlaceholder": "请输入送修人信息",
      "licensePlate": "车牌号",
      "licensePlatePlaceholder": "请输入车牌号",
      "timeoutStatus": "超时状态",
      "timeoutStatusPlaceholder": "请选择超时状态",
      "currentLevel": "当前审批级别",
      "currentLevelPlaceholder": "请选择审批级别",
      "approvalResult": "审批结果",
      "approvalResultPlaceholder": "请选择审批结果",
      "approvalTimeRange": "审批时间",
      "approvalTimePlaceholder": "请选择审批时间范围"
    },
    "approvalTypes": {
      "claim_approval": "索赔审批",
      "cancel_approval": "取消工单审批"
    },
    "approvalStatuses": {
      "pending_review": "待审核",
      "reviewed": "已审核"
    },
    "approvalResults": {
      "approved": "审批通过",
      "rejected": "审批驳回"
    },
    "timeoutStatuses": {
      "normal": "正常",
      "about_to_timeout": "即将超时",
      "timeout": "已超时"
    },
    "approvalLevels": {
      "first_level": "一级审批",
      "second_level": "二级审批"
    },
    "tabs": {
      "pending": "待审批",
      "completed": "已审批"
    },
    "tableColumns": {
      "sequence": "序号",
      "approvalNo": "审批单号",
      "approvalType": "审批类型",
      "submitter": "提交人",
      "submitTime": "提交时间",
      "orderNo": "工单编号",
      "requestReason": "申请原因",
      "timeoutStatus": "超时状态",
      "approvalResult": "审批结果",
      "approvalRemark": "审批备注",
      "approvalTime": "审批时间",
      "approver": "审批人",
      "customerName": "客户姓名",
      "licensePlate": "车牌号",
      "vehicleModel": "车型",
      "storeName": "门店名称",
      "operations": "操作"
    },
    "operations": {
      "review": "审核",
      "viewDetail": "详情",
      "export": "导出",
      "refresh": "刷新"
    },
    "statistics": {
      "totalRecords": "共{total}条记录",
      "pendingApprovals": "待审批：{count}条",
      "completedApprovals": "已审批：{count}条",
      "overtimeApprovals": "超时：{count}条"
    },
    "approvalModal": {
      "claimApprovalTitle": "索赔审批",
      "cancelApprovalTitle": "取消工单审批",
      "detailViewTitle": "审批详情",
      "basicInfo": "基础信息",
      "customerInfo": "客户信息",
      "vehicleInfo": "车辆信息",
      "claimContent": "索赔内容详情",
      "workOrderStatus": "工单状态信息",
      "approvalHistory": "审批过程记录",
      "approvalOperation": "审批操作",
      "approvalNo": "审批单号",
      "approvalType": "审批类型",
      "submitter": "提交人",
      "submitTime": "提交时间",
      "orderNo": "工单编号",
      "requestReason": "申请原因",
      "cancelReason": "取消原因",
      "customerName": "客户姓名",
      "customerPhone": "联系电话",
      "senderName": "送修人名称",
      "senderPhone": "送修人手机号",
      "licensePlate": "车牌号",
      "vin": "VIN",
      "vehicleModel": "车型",
      "configuration": "配置",
      "color": "颜色",
      "mileage": "里程数",
      "vehicleAge": "车龄",
      "serviceTime": "送修时间",
      "currentOrderStatus": "当前工单状态",
      "estimatedStartTime": "预计开工时间",
      "assignedTechnician": "分配技师",
      "claimLabor": "索赔工时",
      "claimParts": "索赔零件",
      "claimLaborTotal": "索赔工时总额",
      "claimPartsTotal": "索赔零件总额",
      "claimTotalAmount": "索赔总金额",
      "laborCode": "工时代码",
      "laborName": "工时名称",
      "standardHours": "标准工时",
      "laborUnitPrice": "工时单价",
      "laborAmount": "索赔金额",
      "partCode": "零件编码",
      "partName": "零件名称",
      "quantity": "数量",
      "partUnitPrice": "单价",
      "partAmount": "索赔金额",
      "approvalResult": "审批结果",
      "approvalResultRequired": "请选择审批结果",
      "approvalRemark": "审批备注",
      "approvalRemarkPlaceholder": "请填写审批备注（驳回时必填）",
      "approvalRemarkRequired": "审批驳回时必须填写驳回理由",
      "submitApproval": "提交审批",
      "approvalLevel": "审批级别",
      "approver": "审批人",
      "approvalTime": "审批时间",
      "approvalRemark": "审批备注",
      "unit": "个",
      "hours": "小时",
      "currency": "元"
    },
    "operationLog": {
      "title": "操作日志",
      "operationType": "操作内容",
      "operator": "操作人",
      "operationTime": "操作时间",
      "operationRemark": "操作备注"
    },
    "messages": {
      "approvalSuccess": "审批提交成功",
      "approvalFailed": "审批提交失败",
      "exportSuccess": "导出成功",
      "exportFailed": "导出失败",
      "loadDataFailed": "加载数据失败",
      "confirmApproval": "确定要提交审批吗？",
      "confirmExport": "确定要导出当前筛选结果吗？",
      "approvalRemarkMaxLength": "审批备注不能超过500字符",
      "noPermissionApproval": "您没有审批权限",
      "approvalAlreadyProcessed": "该审批已被处理，请刷新页面",
      "timeoutWarning": "该审批即将超时，请尽快处理",
      "overtimeWarning": "该审批已超时"
    },
    "statusTags": {
      "pending_review": {
        "text": "待审核",
        "type": "warning"
      },
      "reviewed": {
        "text": "已审核",
        "type": "success"
      },
      "approved": {
        "text": "审批通过",
        "type": "success"
      },
      "rejected": {
        "text": "审批驳回",
        "type": "danger"
      },
      "normal": {
        "text": "正常",
        "type": "success"
      },
      "about_to_timeout": {
        "text": "即将超时",
        "type": "warning"
      },
      "timeout": {
        "text": "已超时",
        "type": "danger"
      }
    }
  },
  "settlement": {
    "title": "售后结算管理",
    "breadcrumb": "售后服务 / 结算管理",
    "searchForm": "筛选条件",
    "operationButtons": "操作",
    "settlementList": "结算单列表",
    "settlementNo": "结算单号",
    "workOrderNo": "工单号",
    "settlementStatus": "结算状态",
    "paymentStatus": "支付状态",
    "workOrderType": "工单类型",
    "customerName": "客户姓名",
    "customerPhone": "客户手机号",
    "vehiclePlate": "车牌号",
    "vehicleModel": "车型",
    "vehicleConfig": "车辆配置",
    "vehicleColor": "车辆颜色",
    "technician": "技师",
    "serviceAdvisor": "服务顾问",
    "totalAmount": "结算单金额",
    "paidAmount": "已付金额",
    "payableAmount": "应付金额",
    "createdTime": "创建时间",
    "push": "推送",
    "payment": "收退款",
    "detailTitle": "结算单详情",
    "basicInfo": "基础信息",
    "paymentInfo": "支付信息",
    "settlementInfo": "结算信息",
    "paymentManagement": "收退款管理",
    "packageRightsDeduction": "套餐权益抵扣",
    "discountAmount": "优惠金额",
    "form": {
      "settlementNo": "结算单号",
      "settlementNoPlaceholder": "请输入结算单号",
      "workOrderNo": "工单号",
      "workOrderNoPlaceholder": "请输入工单号",
      "customerName": "客户姓名",
      "customerNamePlaceholder": "请输入客户姓名",
      "customerPhone": "客户手机号",
      "customerPhonePlaceholder": "请输入客户手机号",
      "licensePlate": "车牌号",
      "licensePlatePlaceholder": "请输入车牌号",
      "vin": "VIN码",
      "vinPlaceholder": "请输入VIN码",
      "serviceAdvisor": "服务顾问",
      "serviceAdvisorPlaceholder": "请选择服务顾问",
      "technician": "技师",
      "technicianPlaceholder": "请选择技师",
      "settlementStatus": "结算状态",
      "settlementStatusPlaceholder": "请选择结算状态",
      "paymentStatus": "支付状态",
      "paymentStatusPlaceholder": "请选择支付状态",
      "settlementDateRange": "结算日期范围",
      "settlementDateStart": "结算开始日期",
      "settlementDateEnd": "结算结束日期",
      "vehicleModel": "车型",
      "vehicleConfig": "车辆配置",
      "vehicleColor": "车辆颜色",
      "mileage": "里程数",
      "createTime": "创建时间",
      "updateTime": "更新时间"
    },
    "placeholder": {
      "settlementNo": "请输入结算单号",
      "workOrderNo": "请输入工单号",
      "customerName": "请输入客户姓名",
      "customerPhone": "请输入客户手机号",
      "vehiclePlate": "请输入车牌号"
    },
    "workOrderTypes": {
      "maintenance": "保养",
      "repair": "维修",
      "warranty": "保修"
    },
    "technicians": {
      "technicianA": "技师A",
      "technicianB": "技师B",
      "technicianC": "技师C"
    },
    "serviceAdvisors": {
      "chen": "陈经理",
      "li": "李顾问"
    },
    "table": {
      "sequence": "序号",
      "settlementNo": "结算单号",
      "workOrderNo": "工单号",
      "customerName": "客户姓名",
      "customerPhone": "客户手机号",
      "licensePlate": "车牌号",
      "vehicleModel": "车型",
      "serviceAdvisor": "服务顾问",
      "technician": "技师",
      "settlementStatus": "结算状态",
      "paymentStatus": "支付状态",
      "totalAmount": "应付金额",
      "paidAmount": "已付金额",
      "unpaidAmount": "未付金额",
      "settlementDate": "结算日期",
      "createTime": "创建时间",
      "operations": "操作"
    },
    "status": {
      "draft": "草稿",
      "pending": "待结算",
      "settled": "已结算",
      "cancelled": "已取消",
      "partial_paid": "部分支付",
      "fully_paid": "全额支付",
      "unpaid": "未支付",
      "refunded": "已退款",
      "pre_settlement": "预结算",
      "pending_settlement": "待结算",
      "completed": "已完成"
    },
    "paymentStatus": {
      "label": "支付状态",
      "pending": "待支付",
      "deposit_paid": "已付定金",
      "fully_paid": "已全付",
      "refunding": "退款中",
      "refunded": "已退款"
    },
    "operation": {
      "detail": "详情",
      "push": "推送",
      "edit": "编辑",
      "payment": "收退款",
      "cancel": "取消",
      "print": "打印",
      "export": "导出",
      "refresh": "刷新"
    },
    "detail": {
      "title": "结算单详情",
      "basicInfo": "基础信息",
      "customerInfo": "客户信息",
      "vehicleInfo": "车辆信息",
      "serviceInfo": "服务信息",
      "costBreakdown": "费用明细",
      "paymentInfo": "支付信息",
      "operationLog": "操作日志",
      "settlementNo": "结算单号",
      "workOrderNo": "工单号",
      "settlementDate": "结算日期",
      "settlementStatus": "结算状态",
      "paymentStatus": "支付状态",
      "customerName": "客户姓名",
      "customerPhone": "客户手机号",
      "customerIdType": "证件类型",
      "customerIdNumber": "证件号码",
      "customerAddress": "客户地址",
      "licensePlate": "车牌号",
      "vin": "VIN码",
      "vehicleModel": "车型",
      "vehicleConfig": "车辆配置",
      "vehicleColor": "车辆颜色",
      "mileage": "里程数",
      "engineNumber": "发动机号",
      "serviceAdvisor": "服务顾问",
      "technician": "技师",
      "serviceType": "服务类型",
      "serviceDate": "服务日期",
      "completionDate": "完工日期",
      "packageInfo": "服务包信息",
      "packageName": "服务包名称",
      "packagePrice": "服务包价格",
      "laborCost": "工时费用",
      "partsCost": "零件费用",
      "additionalCost": "增项费用",
      "discount": "优惠金额",
      "totalCost": "费用合计",
      "taxAmount": "税费",
      "finalAmount": "应付金额",
      "paidAmount": "已付金额",
      "unpaidAmount": "未付金额",
      "paymentMethod": "支付方式",
      "paymentDate": "支付日期",
      "paymentRemark": "支付备注",
      "operationType": "操作类型",
      "operator": "操作人",
      "operationTime": "操作时间",
      "operationRemark": "操作备注",
      "laborItems": "工时项目",
      "partsItems": "零件项目",
      "laborCode": "工时编码",
      "laborName": "工时名称",
      "standardHours": "标准工时",
      "actualHours": "实际工时",
      "laborUnitPrice": "工时单价",
      "laborSubtotal": "工时小计",
      "partCode": "零件编码",
      "partName": "零件名称",
      "partQuantity": "数量",
      "partUnitPrice": "单价",
      "partSubtotal": "零件小计",
      "laborTotal": "工时总计",
      "partsTotal": "零件总计",
      "grandTotal": "总计",
      "isPackageItem": "是否套餐内",
      "isAdditionalItem": "是否增项",
      "unit": "单位",
      "hours": "小时",
      "pieces": "个",
      "currency": "元"
    },
    "payment": {
      "title": "收退款管理",
      "addPayment": "新增收退款",
      "paymentHistory": "收退款记录",
      "paymentType": "收退款类型",
      "paymentMethod": "支付方式",
      "transactionNo": "流水号",
      "amount": "金额",
      "paymentDate": "收退款日期",
      "remark": "备注",
      "operator": "操作人",
      "createTime": "创建时间",
      "receipt": "收款",
      "refund": "退款",
      "cash": "现金",
      "bankCard": "银行卡",
      "wechatPay": "微信支付",
      "alipay": "支付宝",
      "bankTransfer": "银行转账",
      "other": "其他",
      "paymentTypePlaceholder": "请选择收退款类型",
      "paymentMethodPlaceholder": "请选择支付方式",
      "transactionNoPlaceholder": "请输入流水号",
      "amountPlaceholder": "请输入金额",
      "paymentDatePlaceholder": "请选择收退款日期",
      "remarkPlaceholder": "请输入备注",
      "totalReceipt": "总收款",
      "totalRefund": "总退款",
      "netAmount": "净收款",
      "paymentSummary": "收退款汇总",
      "confirmDelete": "确定要删除收退款记录 {paymentNo} 吗？",
      "deleteSuccess": "删除成功",
      "addSuccess": "新增成功",
      "updateSuccess": "更新成功"
    },
    "validation": {
      "settlementNoRequired": "结算单号不能为空",
      "workOrderNoRequired": "工单号不能为空",
      "customerNameRequired": "客户姓名不能为空",
      "customerPhoneRequired": "客户手机号不能为空",
      "customerPhoneFormat": "请输入正确的手机号格式",
      "licensePlateRequired": "车牌号不能为空",
      "vinRequired": "VIN码不能为空",
      "vinFormat": "VIN码格式不正确",
      "serviceAdvisorRequired": "请选择服务顾问",
      "technicianRequired": "请选择技师",
      "settlementStatusRequired": "请选择结算状态",
      "paymentStatusRequired": "请选择支付状态",
      "settlementDateRequired": "请选择结算日期",
      "paymentTypeRequired": "请选择收退款类型",
      "paymentMethodRequired": "请选择支付方式",
      "transactionNoRequired": "流水号不能为空",
      "transactionNoExists": "流水号已存在",
      "amountRequired": "金额不能为空",
      "amountFormat": "请输入正确的金额格式",
      "amountPositive": "金额必须大于0",
      "paymentDateRequired": "请选择收退款日期",
      "remarkMaxLength": "备注长度不能超过500字符",
      "transactionNoLength": "流水号长度应为1-50个字符",
      "transactionNoExists": "流水号已存在，请输入其他流水号"
    },
    "messages": {
      "loadDataSuccess": "数据加载成功",
      "loadDataFailed": "数据加载失败",
      "pushSuccess": "推送成功",
      "pushFailed": "推送失败",
      "pushConfirmTitle": "推送确认",
      "confirmPush": "确定要推送结算单 {settlementNo} 吗？",
      "editSuccess": "编辑成功",
      "editFailed": "编辑失败",
      "editNotImplemented": "编辑功能待实现",
      "cancelSuccess": "取消成功",
      "cancelFailed": "取消失败",
      "printSuccess": "打印成功",
      "printFailed": "打印失败",
      "exportSuccess": "导出成功",
      "exportFailed": "导出失败",
      "saveSuccess": "保存成功",
      "saveFailed": "保存失败",
      "deleteSuccess": "删除成功",
      "deleteFailed": "删除失败",
      "confirmEdit": "确定要编辑此结算单吗？",
      "confirmCancel": "确定要取消此结算单吗？",
      "confirmPrint": "确定要打印此结算单吗？",
      "confirmExport": "确定要导出当前数据吗？",
      "confirmDelete": "确定要删除此记录吗？",
      "unsavedChanges": "存在未保存的更改，确定要离开吗？",
      "operationNotAllowed": "当前状态不允许此操作",
      "noPermission": "您没有执行此操作的权限",
      "dataChanged": "数据已发生变化，请刷新后重试",
      "networkError": "网络错误，请检查网络连接",
      "systemError": "系统错误，请联系管理员",
      "pleaseSelectRecord": "请选择要操作的记录",
      "pleaseSelectAtLeastOne": "请至少选择一条记录",
      "batchOperationSuccess": "批量操作成功",
      "batchOperationFailed": "批量操作失败",
      "partialSuccess": "部分操作成功",
      "fetchServiceAdvisorsFailed": "获取服务顾问列表失败",
      "fetchTechniciansFailed": "获取技师列表失败",
      "fetchSettlementDetailFailed": "获取结算单详情失败",
      "fetchPaymentRecordsFailed": "获取收退款记录失败",
      "addPaymentRecordFailed": "新增收退款记录失败",
      "updatePaymentRecordFailed": "更新收退款记录失败",
      "deletePaymentRecordFailed": "删除收退款记录失败"
    },
    "filters": {
      "all": "全部",
      "today": "今天",
      "yesterday": "昨天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "lastMonth": "上月",
      "thisQuarter": "本季度",
      "thisYear": "今年",
      "customRange": "自定义范围"
    },
    "statistics": {
      "totalCount": "总数",
      "totalAmount": "总金额",
      "paidAmount": "已付金额",
      "unpaidAmount": "未付金额",
      "avgAmount": "平均金额",
      "settlementRate": "结算率",
      "paymentRate": "付款率"
    },
    "export": {
      "title": "导出设置",
      "format": "导出格式",
      "formatPlaceholder": "请选择导出格式",
      "excel": "Excel",
      "csv": "CSV",
      "pdf": "PDF",
      "scope": "导出范围",
      "currentPage": "当前页",
      "allData": "全部数据",
      "selectedData": "选中数据",
      "filteredData": "筛选结果",
      "columns": "导出列",
      "selectAll": "全选",
      "basicInfo": "基础信息",
      "customerInfo": "客户信息",
      "vehicleInfo": "车辆信息",
      "serviceInfo": "服务信息",
      "costInfo": "费用信息",
      "paymentInfo": "支付信息",
      "fileName": "文件名",
      "fileNamePlaceholder": "请输入文件名",
      "exportSettings": "导出设置"
    }
  },
  "quota": {
      "pageTitle": "预约限量配置",
      "storeName": "门店名称",
      "storeCode": "门店编号",
      "permissionTip": "当前登录用户仅可配置所属门店的预约限量",
      "configuredListTitle": "已配置限量列表",
      "addNewQuota": "新增预约限量",
      "emptyState": "暂未配置预约限量。点击\"新增预约限量\"进行配置。",
      "unsavedChangesWarning": "您有未保存的更改，确定要关闭吗？",
      "dateMustBeFutureOrToday": "配置日期不能早于今天",
      "selectDateFirst": "请先选择配置日期",
      "timeSlotExceedsOperatingHours": "时间段不能超过营业时间（18:00）",
      "addNewQuotaTitle": "新增预约限量",
      "editQuotaTitle": "编辑预约限量",
      "store": {
        "name": "吉隆坡中央店",
        "code": "KL001"
      },
      "table": {
        "configDate": "配置日期",
        "timeSlotCount": "时段数量",
        "totalQuota": "总限量",
        "bookedQuantity": "已预约数量",
        "lastUpdateTime": "最后更新时间",
        "expired": "已过期"
      },
      "summary": {
        "selectDate": "请选择配置日期",
        "addTimeSlot": "请添加时间段",
        "timeSlotsUnit": "个时段",
        "totalQuota": "总限量"
      },
      "modal": {
        "selectDateTitle": "选择配置日期",
        "dateLabel": "配置日期",
        "datePlaceholder": "请选择配置日期",
        "existingConfig": "该日期已有配置",
        "dateTip": "只能配置今天及以后的日期",
        "timeSlotConfigTitle": "时段配置",
        "addTimeSlot": "添加时段",
        "noTimeSlots": "暂无时段配置",
        "clickAddPrompt": "点击\"添加时段\"开始配置",
        "timeSlot": "时段",
        "startTime": "开始时间",
        "endTime": "结束时间",
        "quota": "限量",
        "startTimePlaceholder": "请选择开始时间",
        "endTimePlaceholder": "请选择结束时间",
        "quotaPlaceholder": "请输入限量",
        "configDescriptionTitle": "配置说明",
        "configDescription": {
          "item1": "每个时段的限量数代表该时段内可预约的最大数量",
          "item2": "时段之间不能重叠，系统会自动检测冲突",
          "item3": "营业时间为 8:00-18:00，时段不能超出此范围",
          "item4": "保存后立即生效，客户可在小程序中选择对应时段预约"
        }
      },
      "validation": {
        "dateRequired": "请选择配置日期",
        "atLeastOneTimeSlot": "至少需要添加一个时段",
        "timeRequired": "请完善时段的开始和结束时间",
        "startBeforeEnd": "开始时间必须早于结束时间",
        "quotaPositive": "限量必须大于0",
        "timeSlotOverlap": "时段 {slot1} 与 {slot2} 存在时间重叠，请调整"
      }
  },
  "approval": {
    "title": "订单审批管理",
    "pending": "待审批",
    "approved": "已审批",
    "approvalType": "审批类型",
    "orderNumber": "订单号",
    "submitter": "提交人",
    "submitTime": "提交时间",
    "timeRange": "时间范围",
    "approvalStatus": "审批状态",
    "approvalResult": "审批结果",
    "store": "门店",
    "batchApproval": "批量审批",
    "batchReject": "批量驳回",
    "approve": "审批",
    "reject": "驳回",
    "viewDetails": "查看详情",
    "viewHistory": "查看历史",
    "export": "导出",
    "remainingTime": "剩余时间",
    "types": {
      "orderCancel": "订单取消",
      "customerInfoModify": "客户信息修改",
      "vehicleColorModify": "车辆颜色修改"
    },
    "status": {
      "pending": "待审批",
      "approved": "已通过",
      "rejected": "已驳回",
      "timeout": "审批超时"
    },
    "result": {
      "approved": "通过",
      "rejected": "驳回",
      "timeout": "超时"
    }
  },
  "inspectionForm": {
    "title": "环检单管理",
    "form": {
      "inspectionNo": "环检单号",
      "inspectionStatus": "环检状态",
      "licensePlateNo": "车牌号",
      "repairmanName": "送修人名称",
      "technician": "技师",
      "repairmanPhone": "送修人手机号",
      "createTime": "创建时间"
    },
    "status": {
      "pending": "待环检",
      "inProgress": "环检中",
      "pendingConfirm": "待确认",
      "confirmed": "已确认"
    },
    "table": {
      "index": "序号",
      "inspectionNo": "环检单号",
      "inspectionStatus": "环检状态",
      "repairmanName": "送修人名称",
      "repairmanPhone": "送修人手机号",
      "licensePlateNo": "车牌号",
      "modelConfig": "车型配置",
      "color": "颜色",
      "serviceAdvisor": "服务顾问",
      "technician": "技师",
      "registerType": "登记类型",
      "serviceType": "服务类型",
      "customerConfirmTime": "客户确认时间",
      "createTime": "创建时间",
      "updateTime": "更新时间"
    }
  },
  "assignInspectionForm": {
    "title": "分配环检单",
    "inspectionNo": "环检单号",
    "licensePlateNo": "车牌号",
    "repairmanName": "送修人名称",
    "registerType": "登记类型",
    "serviceType": "服务类型",
    "technician": "技师",
    "selectTechnicianPlaceholder": "请选择技师"
  },
  "inspectionFormDetailEdit": {
    "titleEdit": "编辑环检单",
    "titleDetail": "环检单详情",
    "clientInfo": "客户信息",
    "reserveName": "预约人名称",
    "reservePhone": "预约人手机号",
    "repairmanName": "送修人名称",
    "repairmanPhone": "送修人手机号",
    "remark": "备注",
    "vehicleInfo": "车辆信息",
    "licensePlateNo": "车牌号",
    "vin": "VIN号",
    "modelConfig": "车型配置",
    "color": "颜色",
    "mileage": "里程数",
    "vehicleAge": "车龄",
    "inspectionFormInfo": "环检单信息",
    "inspectionStatus": "环检单状态",
    "registerType": "登记类型",
    "servicePackageName": "服务包名称",
    "createTime": "创建时间",
    "serviceAdvisor": "服务顾问",
    "technician": "技师",
    "customerConfirmTime": "客户确认时间",
    "customerConfirmImage": "客户确认图片",
    "inspectionContentList": "环检内容清单",
    "parkingAreaRecord": "停车区域记录",
    "waitingArea": "等候区",
    "leavingArea": "离开区",
    "parkingZone": "停车区",
    "dashboardInspection": "仪表盘检查",
    "mileageRecord": "里程数记录",
    "batteryLevel": "电池电量检查",
    "remainingRange": "续航里程显示",
    "energyConsumption": "能耗显示",
    "functionalityCheck": "功能性检查",
    "gaugesIndicators": "仪表和指示器",
    "checkFunction": "检查功能",
    "batteryStatusIndicator": "电池状态指示",
    "chargingStatusIndicator": "充电状态指示",
    "energyConsumptionFunction": "能耗显示功能",
    "airConditioningSystem": "空调系统",
    "heatPumpCheck": "热泵功能检查",
    "batteryPreheating": "电池预加热功能",
    "wiperWasher": "雨刮和清洗器",
    "checkVisualCondition": "检查外观状况",
    "infotainmentSystem": "信息娱乐系统",
    "chargingStationNavigation": "充电站导航功能",
    "energyMonitoring": "能耗监控功能",
    "otaUpdateStatus": "OTA更新状态",
    "warningLightsCheck": "警告灯检查",
    "batterySystem": "电池系统",
    "motorSystem": "电机系统",
    "chargingSystem": "充电系统",
    "highVoltageSystem": "高压系统",
    "coolingSystem": "冷却系统",
    "regenerativeBraking": "再生制动",
    "insulationMonitoring": "绝缘监测",
    "abs": "ABS",
    "eps": "EPS电动转向",
    "exteriorInspection": "外观检查",
    "bodyExteriorInspection": "车身外观检查",
    "frontCheck": "前检查",
    "rearCheck": "后检查",
    "leftSideCheck": "左侧检查",
    "rightSideCheck": "右侧检查",
    "roofViewCheck": "车顶视图检查",
    "chargingPortCover": "充电口盖检查",
    "electricSystemInspection": "电动系统检查",
    "highVoltageBatteryCheck": "高压电池检查",
    "batteryPackVisualInspection": "电池包外观检查",
    "noDamage": "无损伤",
    "noLeakage": "无泄漏",
    "mountingBoltsSecure": "固定螺栓紧固",
    "batteryCoolingSystem": "电池冷却系统",
    "coolantLevel": "冷却液液位",
    "coolingFanFunction": "冷却风扇功能",
    "radiatorCleanliness": "散热器清洁度",
    "highVoltageCableInspection": "高压线束检查",
    "insulationIntegrity": "绝缘层完整性",
    "connectorSealing": "连接器密封性",
    "fixingClipsStatus": "固定卡扣状态",
    "motorSystemCheck": "电机系统检查",
    "driveMotor": "驱动电机",
    "operatingNoiseCheck": "运行噪音检查",
    "vibrationCheck": "震动检查",
    "temperatureMonitoring": "温度监控",
    "motorController": "电机控制器",
    "visualInspection": "外观检查",
    "heatDissipationSystem": "散热系统",
    "connectionHarness": "连接线束",
    "chargingSystemCheck": "充电系统检查",
    "onboardCharger": "车载充电器",
    "functionTest": "功能测试",
    "heatDissipationCheck": "散热检查",
    "chargingInterface": "充电接口",
    "contactCleanliness": "接触片清洁度",
    "lockingMechanism": "锁止机构",
    "sealingPerformance": "密封性能",
    "tireWearInspection": "轮胎磨损检查",
    "tyreThreadCheck": "胎纹深度",
    "checkDeviation": "磨损偏差检查",
    "lowRollingResistanceTyreStatus": "低滚阻轮胎状态",
    "tirePosition": "轮胎位置",
    "frontRight": "前右轮",
    "frontLeft": "前左轮",
    "rearRight": "后右轮",
    "rearLeft": "后左轮",
    "tirePressureMonitoring": "轮胎压力监测",
    "tpmsFunctionCheck": "TPMS功能检查",
    "tyrePressureStandardConfirmation": "胎压标准值确认",
    "temperatureMonitoringFunction": "温度监测功能",
    "customerSelfDescribedProblem": "客户自述问题",
    "good": "良好",
    "attentionRequired": "需要注意",
    "notGood": "不良",
    "notApplicable": "不适用"
  },
  "customerConfirm": {
    "title": "客户确认",
    "inspectionNo": "环检单号",
    "repairmanName": "送修人名称",
    "licensePlateNo": "车牌号",
    "customerConfirmTime": "客户确认时间",
    "selectDatePlaceholder": "选择日期",
    "uploadedFiles": "上传图片",
    "clickToUploadImage": "点击上传图片",
    "pleaseSelectConfirmTime": "请选择客户确认时间",
    "pleaseUploadFile": "请上传至少一张图片",
    "customerSignature": "客户签名",
    "uploadSignature": "上传签名图片",
    "signPlaceholder": "请客户在此区域签名"
  },
  "technicians": {
    "technicianA": "技师A",
    "technicianB": "技师B",
    "technicianC": "技师C"
  },
  "mockData": {
    "registerTypes": {
      "appointment": "预约",
      "walkIn": "自然到店"
    },
    "serviceTypes": {
      "maintenance": "保养",
      "repair": "维修"
    },
    "colors": {
      "white": "白色",
      "black": "黑色",
      "blue": "蓝色",
      "gray": "灰色"
    },
    "names": {
      "zhangSan": "张三",
      "liHua": "李华",
      "wangDaChui": "王大锤",
      "linYiYi": "林依依",
      "liSi": "李四",
      "wangWu": "王五",
      "zhaoLiu": "赵六",
      "qianQi": "钱七",
      "sunBa": "孙八",
      "zhouJiu": "周九",
      "wuShi": "吴十"
    },
    "carModels": {
      "modelY2023Long": "Model Y 2023 长续航版",
      "model32022Standard": "Model 3 2022 标准续航版",
      "modelX2024Performance": "Model X 2024 高性能版",
      "modelY2023Performance": "Model Y 2023 高性能版"
    }
  },
  "processResults": {
    "success": "成功",
    "failed": "失败"
  },
  "messages": {
    "createWorkOrderFor": "为环检单 {inspectionNo} 创建工单",
    "exportingData": "导出数据",
    "viewDetail": "查看详情",
    "assignInspectionForm": "分配环检单",
    "editInspectionForm": "编辑环检单",
    "submitConfirm": "提交确认",
    "recall": "撤回",
    "print": "打印环检单",
    "customerConfirm": "客户确认",
    "createWorkOrder": "创建工单",
    "saveInspectionFormDetail": "保存环检单详情：",
    "assignInspectionFormData": "分配环检单：",
    "customerConfirmData": "客户确认：",
    "savingConfig": "保存配置："
  },
  "inventoryReport": {
    "title": "库存信息 (店端)",
    "description": "展示店端库存总数、可用库存数、锁定库存数、残次品数和待收数等信息的报表页面。",
    "partName": "零件名称",
    "partNamePlaceholder": "请输入零件名称",
    "partNumber": "零件编号",
    "partNumberPlaceholder": "请输入零件编号",
    "supplierName": "供应商名称",
    "supplierNamePlaceholder": "请输入供应商名称",
    "inventoryStatus": "库存状态",
    "inventoryStatusPlaceholder": "请选择库存状态",
    "statusNormal": "正常",
    "statusBelowSafety": "低于安全库存",
    "serialNumber": "序号",
    "totalInventory": "库存总数",
    "availableInventory": "可用库存数",
    "lockedInventory": "锁定库存数",
    "defectiveProducts": "残次品数",
    "pendingReceipt": "待收数",
    "safetyStock": "安全库存数"
  },
  "inventoryReportHQ": {
    "title": "库存报表HQ",
    "description": "展示库存总数、可用库存数、锁定库存数、残次品数和待收数等信息的报表页面。",
    "partName": "零件名称",
    "partNamePlaceholder": "请输入零件名称",
    "partNumber": "零件编号",
    "partNumberPlaceholder": "请输入零件编号",
    "supplierName": "供应商名称",
    "supplierNamePlaceholder": "请输入供应商名称",
    "storeName": "门店名称",
    "storeNamePlaceholder": "请输入门店名称",
    "inventoryStatus": "库存状态",
    "inventoryStatusPlaceholder": "请选择库存状态",
    "statusNormal": "正常",
    "statusBelowSafety": "低于安全库存",
    "serialNumber": "序号",
    "totalInventory": "库存总数",
    "availableInventory": "可用库存数",
    "lockedInventory": "锁定库存数",
    "defectiveProducts": "残次品数",
    "pendingReceipt": "待收数",
    "safetyStock": "安全库存数"
  },
  "partManagement": {
    "title": "零件管理",
    "partName": "零件名称",
    "partNamePlaceholder": "请输入零件名称",
    "partNumber": "零件编号",
    "partNumberPlaceholder": "请输入零件编号",
    "documentNumber": "单据号",
    "requisitionNumber": "单据号",
    "requisitionNumberPlaceholder": "请输入单据号",
    "documentType": "单据类型",
    "documentStatus": "单据状态",
    "selectDocumentType": "请选择单据类型",
    "documentTypeRequisition": "叫料单",
    "documentTypeScrap": "报损单",
    "documentTypePicking": "拣货单",
    "documentTypePurchaseOrder": "采购单",
    "supplierName": "供应商名称",
    "supplierNamePlaceholder": "可输入筛选下拉框",
    "requisitionDate": "叫料日期",
    "requisitionDateRange": "叫料日期区间",
    "arrivalTimeRange": "到货日期区间",
    "requisitionStatus": "叫料单状态",
    "selectRequisitionStatus": "请选择叫料单状态",
    "statusSubmitted": "已提交",
    "statusApproved": "已审批",
    "statusRejected": "已驳回",
    "statusShipped": "已发货",
    "statusPartialShipped": "部分发货",
    "statusPartialReceived": "部分收货",
    "statusReceived": "已收货",
    "statusVoided": "已作废",
    "statusCancelled": "已作废",
    "inventoryStatus": "库存状态",
    "selectInventoryStatus": "请选择库存状态",
    "statusNormal": "正常",
    "statusBelowSafetyStock": "低于安全库存",
    "newRequisition": "新建叫料清单",
    "newRequisitionForm": {
      "partName": "零件名称",
      "partNumber": "零件号",
      "quantity": "数量",
      "storeName": "门店名称",
      "expectedArrivalTime": "期望收货时间",
      "partNameRequired": "零件名称为必填项",
      "partNumberRequired": "零件号为必填项",
      "quantityRequired": "数量为必填项",
      "expectedArrivalTimeRequired": "期望收货时间为必填项"
    },
    "exportReport": "导出报表",
    "approve": "审批",
    "approvalResult": "审批结果",
    "selectApprovalResult": "请选择审批结果",
    "rejectionReason": "驳回原因",
    "rejectionReasonRequired": "请输入驳回原因",
    "enterRejectionReason": "请输入驳回原因，字数限制1000",
    "requisitionDetails": "叫料单明细",
    "quantity": "数量",
    "unit": "单位",
    "expectedArrivalTime": "预计到货时间",
    "approvalSuccess": "审批成功！",
    "approvalResultRequired": "审批结果必选",
    "partScrap": "零件报损",
    "scrapRecord": "报损记录",
    "ship": "发货",
    "shipParts": "零件发货",
    "shipPartially": "部分发货",
    "shipCompletely": "完整发货",
    "shippedQuantity": "已发货数量",
    "shippingStatus": "发货状态",
    "notShipped": "未发货",
    "partiallyShipped": "部分发货",
    "fullyShipped": "已发货",
    "shipSuccess": "发货成功",
    "shipFailed": "发货失败",
    "viewMaterialOrder": "查看拣货单",
    "workOrderNumber": "工单号",
    "workOrderCreateDate": "工单创建日期",
    "workOrderDetail": "明细",
    "printMaterialOrder": "打印拣货单",
    "workOrderDetailTitle": "工单明细",
    "generateDate": "生成日期",
    "createDate": "创建日期",
    "scrapOrderNumber": "报损单号",
    "scrapDate": "报损日期",
    "scrapStatus": "报损状态",
    "partsListTitle": "零件清单",
    "partCode": "零件代码",
    "completePicking": "完成拣货",
    "confirmCompletePicking": "确认完成工单 {workOrderNumber} 的拣货操作吗？",
    "completePickingSuccess": "工单 {workOrderNumber} 拣货完成！",
    "returnPicking": "退拣",
    "returnPickingTitle": "退拣操作",
    "returnPickingPartsTitle": "退拣零件清单",
    "returnQuantity": "退拣数量",
    "returnPickingConfirmTitle": "确认退拣操作",
    "confirmReturnPicking": "确认将工单 {workOrderNumber} 退回到待拣货状态吗？",
    "confirmReturnPickingWithQuantity": "确认对工单 {workOrderNumber} 执行退拣操作吗？",
    "confirmReturnPickingDetails": "工单 {workOrderNumber} 将退拣以下零件：\n\n{returnDetails}\n\n是否确定完成退拣操作？",
    "pleaseSelectReturnQuantity": "请选择要退拣的数量",
    "returnPickingSuccess": "工单 {workOrderNumber} 已退回到待拣货状态！",
    "workOrderStatus": {
      "label": "工单状态",
      "pendingPick": "待拣货",
      "picked": "已拣货",
      "outOfStock": "缺货"
    },
    "outOfStockCannotPrint": "缺货状态的工单无法打印拣货单",
    "partScrapForm": {
      "partName": "零件名称",
      "partNumber": "零件号",
      "partNamePlaceholder": "可输入筛选下拉框",
      "partNumberPlaceholder": "可输入筛选下拉框",
      "quantity": "数量",
      "scrapReason": "报损原因",
      "scrapSource": "报损来源",
      "selectScrapSource": "请选择报损来源",
      "sourceReceipt": "收货报损",
      "sourceRepair": "维修报损",
      "sourceOther": "其它报损",
      "scrapReasonPlaceholder": "请输入报损原因",
      "scrapImages": "报损图片",
      "scrapImagesRequired": "请上传报损图片",
      "imageUploadTip": "必须上传报损图片，支持jpg、png格式，单张图片不超过5MB，最多上传5张",
      "imageTypeError": "只能上传图片格式的文件！",
      "imageSizeError": "图片大小不能超过5MB！",
      "noImages": "暂无图片",
      "partNameRequired": "请输入零件名称",
      "partNumberRequired": "请输入零件号",
      "quantityRequired": "请输入数量",
      "scrapReasonRequired": "请输入报损原因",
      "scrapSourceRequired": "请选择报损来源",
      "addItemsPrompt": "请先添加报损项目",
      "newScrapOrderCreated": "新报损单创建成功",
      "scrapOrderUpdated": "报损单更新成功"
    },
    "scrapRecordForm": {
      "scrapOrderNumber": "报损单号",
      "scrapOrderNumberPlaceholder": "请输入报损单号",
      "partName": "零件名称",
      "partNamePlaceholder": "可输入筛选下拉框",
      "partNumber": "零件编号",
      "partNumberPlaceholder": "可输入筛选下拉框",
      "scrapDate": "报损日期",
      "scrapSource": "报损来源",
      "selectScrapSource": "请选择报损来源",
      "sourceReceipt": "收货",
      "sourceRepair": "维修",
      "sourceOther": "其它",
      "status": "单据状态",
      "selectStatus": "请选择单据状态",
      "statusSubmitted": "已提交",
      "statusApproved": "通过",
      "statusRejected": "驳回",
      "statusVoided": "作废",
      "confirmVoidOrder": "确定要作废报损单 '{orderNumber}' 吗？",
      "voidOrderSuccess": "报损单作废成功",
      "voidOrderFailed": "报损单作废失败",
      "totalQuantity": "总数量",
      "itemCount": "项目数",
      "orderDetails": "报损单详情",
      "scrapReason": "报损原因详情",
      "scrapReasonDetail": "报损原因详情",
      "scrapReasonText": "报损原因描述",
      "scrapImages": "报损图片",
      "deliveryOrderNumber": "到货单号"
    }
  }
}
