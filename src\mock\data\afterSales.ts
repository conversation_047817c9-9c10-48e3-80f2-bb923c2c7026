// dms-frontend/src/mock/data/afterSales.ts

import type {
  AppointmentListItem,
  ServiceAdvisor,
  Technician
} from '@/types/module';

// 模拟服务顾问数据
export const mockServiceAdvisors: ServiceAdvisor[] = [
  { id: 'SA001', name: '张三', avatar: '' },
  { id: 'SA002', name: '李四', avatar: '' },
  { id: 'SA003', name: '王五', avatar: '' },
  { id: 'SA004', name: '赵六', avatar: '' },
  { id: 'SA005', name: '钱七', avatar: '' }
];

// 模拟技师数据
export const mockTechnicians: Technician[] = [
  { id: 'T001', name: '技师A', avatar: '' },
  { id: 'T002', name: '技师B', avatar: '' },
  { id: 'T003', name: '技师C', avatar: '' },
  { id: 'T004', name: '技师D', avatar: '' },
  { id: 'T005', name: '技师E', avatar: '' }
];

// 模拟预约列表数据
export const mockAppointmentList: AppointmentListItem[] = [
  {
    id: 'APT001',
    vin: 'WBAKF91070FX12345',
    licensePlate: '京A12345',
    appointmentTime: '2024-01-10',
    timeSlot: '09:00-10:00',
    serviceType: 'maintenance',
    status: 'not_arrived',
    // 未到店状态，无服务顾问和技师
    customerName: '张三',
    customerPhone: '13800138001',
    vehicleModel: '奔驰C级',
    model: '奔驰C级',
    variant: 'C200L 运动版',
    color: '曜岩黑金属漆',
    mileage: 25000,
    reservationContactName: '张三',
    reservationContactPhone: '13800138001',
    serviceContactName: '张三',
    serviceContactPhone: '13800138001',
    paymentStatus: 'unpaid',
    paymentAmount: 430,
    paymentOrderNumber: '',
    maintenancePackage: {
      id: 'mp_001',
      code: 'PKG001',
      name: '基础保养套餐',
      price: 899,
      totalAmount: 430,
      standardHours: 2.5,
      items: [
        { id: 'item_001', code: 'SP001', name: '更换机油', quantity: 1, price: 200, standardHours: 0.5, description: '使用全合成机油' },
        { id: 'item_002', code: 'SP002', name: '更换机油滤清器', quantity: 1, price: 80, standardHours: 0.5, description: '原厂滤清器' },
        { id: 'item_003', code: 'SP003', name: '检查轮胎', quantity: 1, price: 50, standardHours: 0.5, description: '检查胎压和磨损' },
        { id: 'item_004', code: 'SP004', name: '检查刹车系统', quantity: 1, price: 100, standardHours: 1.0, description: '检查刹车片和刹车油' }
      ],
      suggestedParts: ['机油5L', '机油滤清器', '空气滤清器']
    },
    customerDescription: '车辆行驶25000公里，需要进行基础保养',
    createdAt: '2024-01-10T08:30:00.000Z',
    updatedAt: '2024-01-12T14:20:00.000Z',
    productionDate: '2022-05-15', // 示例生产日期，用于计算车龄
  },
  {
    id: 'APT002',
    vin: 'WVWZZZ1KZAW123456',
    licensePlate: '京B67890',
    appointmentTime: '2024-01-15',
    timeSlot: '10:00-11:00',
    serviceType: 'repair',
    status: 'arrived',
    serviceAdvisor: mockServiceAdvisors[1],
    technician: mockTechnicians[1],
    customerName: '李四',
    customerPhone: '13800138002',
    vehicleModel: '大众帕萨特',
    model: '大众帕萨特',
    variant: '380TSI 豪华版',
    color: '玛瑙红',
    mileage: 45000,
    reservationContactName: '李四',
    reservationContactPhone: '13800138002',
    serviceContactName: '李四',
    serviceContactPhone: '13800138002',
    inspectionCreated: true,
    customerDescription: '发动机异响，需要检查发动机状况',
    createdAt: '2024-01-11T09:15:00.000Z',
    updatedAt: '2024-01-15T08:45:00.000Z',
    productionDate: '2021-11-20', // 示例生产日期，用于计算车龄
  },
  {
    id: 'APT003',
    vin: 'LGXC16A04GG123456',
    licensePlate: '京C11111',
    appointmentTime: '2024-01-16',
    timeSlot: '14:00-15:00',
    serviceType: 'maintenance',
    status: 'not_arrived',
    // 未到店状态，无服务顾问
    customerName: '王五',
    customerPhone: '13800138003',
    vehicleModel: '现代索纳塔',
    model: '现代索纳塔',
    variant: '第十代 380T 豪华版',
    color: '炫酷银',
    mileage: 30000,
    reservationContactName: '王五',
    reservationContactPhone: '13800138003',
    serviceContactName: '王五',
    serviceContactPhone: '13800138003',
    paymentStatus: 'unpaid',
    paymentAmount: 1150,
    paymentOrderNumber: '',
    maintenancePackage: {
      id: 'mp_002',
      code: 'PKG002',
      name: '标准保养套餐',
      price: 1299,
      totalAmount: 1150,
      standardHours: 3.5,
      items: [
        { id: 'item_005', code: 'SP005', name: '更换机油', quantity: 1, price: 300, standardHours: 0.5, description: '使用全合成机油' },
        { id: 'item_006', code: 'SP006', name: '更换三滤', quantity: 1, price: 250, standardHours: 1.0, description: '机油滤、空气滤、汽油滤' },
        { id: 'item_007', code: 'SP007', name: '检查制动系统', quantity: 1, price: 150, standardHours: 1.0, description: '全面检查制动系统' },
        { id: 'item_008', code: 'SP008', name: '检查悬挂系统', quantity: 1, price: 450, standardHours: 1.0, description: '检查减震器和悬挂' }
      ],
      suggestedParts: ['机油5L', '三滤套装', '制动液']
    },
    customerDescription: '30000公里保养，希望进行全面检查',
    createdAt: '2024-01-12T10:00:00.000Z',
    updatedAt: '2024-01-12T10:00:00.000Z'
  },
  {
    id: 'APT004',
    vin: 'LBEXXBFA2HG123456',
    licensePlate: '京D22222',
    appointmentTime: '2024-01-14',
    timeSlot: '15:00-16:00',
    serviceType: 'repair',
    status: 'no_show',
    // 未履约状态，无服务顾问和技师
    customerName: '赵六',
    customerPhone: '13800138004',
    vehicleModel: '宝马3系',
    model: '宝马3系',
    variant: '325Li M运动套装',
    color: '雪山白',
    mileage: 15000,
    reservationContactName: '赵六',
    reservationContactPhone: '13800138004',
    serviceContactName: '赵六',
    serviceContactPhone: '13800138004',
    customerDescription: '空调制冷效果不好，需要检修空调系统',
    createdAt: '2024-01-09T16:30:00.000Z',
    updatedAt: '2024-01-14T16:30:00.000Z'
  },
  {
    id: 'APT005',
    vin: 'LFPH111H1G1123456',
    licensePlate: '京E33333',
    appointmentTime: '2024-01-17',
    timeSlot: '11:00-12:00',
    serviceType: 'maintenance',
    status: 'pending_payment',
    serviceAdvisor: mockServiceAdvisors[0],
    technician: mockTechnicians[2],
    customerName: '钱七',
    customerPhone: '13800138005',
    vehicleModel: '丰田凯美瑞',
    model: '丰田凯美瑞',
    variant: '2.5L 豪华版',
    color: '珍珠白',
    mileage: 20000,
    reservationContactName: '钱七',
    reservationContactPhone: '13800138005',
    serviceContactName: '钱七',
    serviceContactPhone: '13800138005',
    paymentStatus: 'paid',
    paymentAmount: 1650,
    paymentOrderNumber: 'PAY20240116001',
    maintenancePackage: {
      id: 'mp_003',
      code: 'PKG003',
      name: '深度保养套餐',
      price: 1899,
      totalAmount: 1650,
      standardHours: 5.0,
      items: [
        { id: 'item_009', code: 'SP009', name: '更换机油', quantity: 1, price: 350, standardHours: 0.5, description: '使用全合成机油' },
        { id: 'item_010', code: 'SP010', name: '更换五滤', quantity: 1, price: 500, standardHours: 1.5, description: '机油滤、空气滤、汽油滤、空调滤、变速箱滤' },
        { id: 'item_011', code: 'SP011', name: '清洗节气门', quantity: 1, price: 200, standardHours: 1.0, description: '清洗节气门积碳' },
        { id: 'item_012', code: 'SP012', name: '检查点火系统', quantity: 1, price: 300, standardHours: 1.0, description: '检查火花塞和点火线圈' },
        { id: 'item_013', code: 'SP013', name: '全车检查', quantity: 1, price: 300, standardHours: 1.0, description: '全面检查各系统' }
      ],
      suggestedParts: ['机油5L', '五滤套装', '火花塞', '节气门清洗剂']
    },
    customerDescription: '20000公里深度保养',
    createdAt: '2024-01-13T11:20:00.000Z',
    updatedAt: '2024-01-16T09:30:00.000Z'
  },
  {
    id: 'APT006',
    vin: 'WBABA11000GG12345',
    licensePlate: '京F44444',
    appointmentTime: '2024-01-18',
    timeSlot: '09:00-10:00',
    serviceType: 'maintenance',
    status: 'arrived',
    serviceAdvisor: mockServiceAdvisors[2],
    technician: mockTechnicians[0],
    customerName: '孙八',
    customerPhone: '13800138006',
    vehicleModel: '奥迪A4L',
    model: '奥迪A4L',
    variant: '40 TFSI 时尚型',
    color: '冰川白金属漆',
    mileage: 18000,
    reservationContactName: '孙八',
    reservationContactPhone: '13800138006',
    serviceContactName: '孙八',
    serviceContactPhone: '13800138006',
    paymentStatus: 'paid',
    paymentAmount: 1050,
    paymentOrderNumber: 'PAY20240118001',
    maintenancePackage: {
      id: 'mp_004',
      code: 'PKG004',
      name: '标准保养套餐',
      price: 1199,
      totalAmount: 1050,
      standardHours: 3.0,
      items: [
        { id: 'item_014', code: 'SP014', name: '更换机油', quantity: 1, price: 280, standardHours: 0.5, description: '使用全合成机油' },
        { id: 'item_015', code: 'SP015', name: '更换机油滤清器', quantity: 1, price: 120, standardHours: 0.5, description: '原厂滤清器' },
        { id: 'item_016', code: 'SP016', name: '更换空气滤清器', quantity: 1, price: 150, standardHours: 0.5, description: '高效空气滤清器' },
        { id: 'item_017', code: 'SP017', name: '检查制动系统', quantity: 1, price: 200, standardHours: 1.0, description: '全面检查制动系统' },
        { id: 'item_018', code: 'SP018', name: '检查悬挂系统', quantity: 1, price: 300, standardHours: 0.5, description: '检查减震器状态' }
      ],
      suggestedParts: ['机油5L', '机油滤清器', '空气滤清器']
    },
    customerDescription: '车辆行驶18000公里，进行定期保养检查',
    createdAt: '2024-01-17T08:00:00.000Z',
    updatedAt: '2024-01-18T09:15:00.000Z',
    productionDate: '2021-09-10', // 示例生产日期，用于计算车龄
  },
  {
    id: 'APT007',
    vin: 'LSGD254X7FG123456',
    licensePlate: '京G55555',
    appointmentTime: '2024-01-19',
    timeSlot: '14:00-15:00',
    serviceType: 'repair',
    status: 'arrived',
    serviceAdvisor: mockServiceAdvisors[3],
    technician: mockTechnicians[3],
    customerName: '周九',
    customerPhone: '13800138007',
    vehicleModel: '雷克萨斯ES',
    model: '雷克萨斯ES',
    variant: 'ES 200 精英版',
    color: '珍珠白',
    mileage: 35000,
    reservationContactName: '周九',
    reservationContactPhone: '13800138007',
    serviceContactName: '周九',
    serviceContactPhone: '13800138007',
    customerDescription: '车辆转向时有异响，怀疑转向系统有问题，需要检修',
    createdAt: '2024-01-18T10:30:00.000Z',
    updatedAt: '2024-01-19T14:10:00.000Z'
  }
];

// 导出模拟数据获取函数
export const getMockAppointments = () => mockAppointmentList;
