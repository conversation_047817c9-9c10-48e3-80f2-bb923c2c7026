// src/mock/data/afterSales/appointments.ts

import type { AppointmentListParams, AppointmentPageResponse, AppointmentListItem } from '@/types/afterSales/appointments.d.ts';

// Mock appointment list data
const mockAppointmentList: AppointmentListItem[] = [
  {
    id: 'APT001',
    vin: 'WBAKF91070FX12345',
    licensePlate: '京A12345',
    appointmentTime: '2024-01-10',
    timeSlot: '09:00-10:00',
    serviceType: 'maintenance',
    status: 'not_arrived',
    model: '奔驰C级',
    variant: 'C200L 运动版',
    color: '曜岩黑金属漆',
    mileage: 25000,
    reservationContactName: '张三',
    reservationContactPhone: '13800138001',
    serviceContactName: '张三',
    serviceContactPhone: '13800138001',
    createdAt: '2024-01-08 14:30:00',
    productionDate: '2022-03-15'
  },
  {
    id: 'APT002',
    vin: 'WBAKF91070FX12346',
    licensePlate: '京B67890',
    appointmentTime: '2024-01-10',
    timeSlot: '10:00-11:00',
    serviceType: 'repair',
    status: 'arrived',
    model: '奔驰E级',
    variant: 'E300L 豪华版',
    color: '北极白金属漆',
    mileage: 15000,
    reservationContactName: '李四',
    reservationContactPhone: '13800138002',
    serviceContactName: '王五',
    serviceContactPhone: '13800138003',
    serviceAdvisor: { id: 'SA001', name: '张三' },
    qualityInspectionId: 'QI001',
    createdAt: '2024-01-08 15:45:00',
    customerDescription: '发动机异响，需要检查',
    inspectionCreated: true,
    productionDate: '2023-06-20'
  },
  {
    id: 'APT003',
    vin: 'WBAKF91070FX12347',
    licensePlate: '京C11111',
    appointmentTime: '2024-01-10',
    timeSlot: '11:00-12:00',
    serviceType: 'maintenance',
    status: 'pending_payment',
    model: '奔驰S级',
    variant: 'S450L 尊贵版',
    color: '曜岩黑金属漆',
    mileage: 8000,
    reservationContactName: '赵六',
    reservationContactPhone: '13800138004',
    serviceContactName: '赵六',
    serviceContactPhone: '13800138004',
    serviceAdvisor: { id: 'SA002', name: '李四' },
    createdAt: '2024-01-08 16:20:00',
    productionDate: '2023-12-10'
  },
  {
    id: 'APT004',
    vin: 'WBAKF91070FX12348',
    licensePlate: '京D22222',
    appointmentTime: '2024-01-10',
    timeSlot: '14:00-15:00',
    serviceType: 'maintenance',
    status: 'arrived',
    model: '奔驰GLC',
    variant: 'GLC260L 动感版',
    color: '极地白金属漆',
    mileage: 30000,
    reservationContactName: '钱七',
    reservationContactPhone: '13800138005',
    serviceContactName: '孙八',
    serviceContactPhone: '13800138006',
    serviceAdvisor: { id: 'SA003', name: '王五' },
    createdAt: '2024-01-08 17:10:00',
    customerDescription: '常规保养',
    productionDate: '2021-08-25'
  },
  {
    id: 'APT005',
    vin: 'WBAKF91070FX12349',
    licensePlate: '京E33333',
    appointmentTime: '2024-01-10',
    timeSlot: '15:00-16:00',
    serviceType: 'repair',
    status: 'cancelled',
    model: '奔驰A级',
    variant: 'A200L 运动版',
    color: '山脉灰金属漆',
    mileage: 12000,
    reservationContactName: '周九',
    reservationContactPhone: '13800138007',
    serviceContactName: '周九',
    serviceContactPhone: '13800138007',
    createdAt: '2024-01-08 18:00:00',
    customerDescription: '客户临时取消',
    productionDate: '2023-04-12'
  }
];

export const getAppointmentList = (params: AppointmentListParams): Promise<AppointmentPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockAppointmentList];
      // Apply filters
      if (params.appointmentId) {
        filteredData = filteredData.filter(item => 
          item.id.toLowerCase().includes(params.appointmentId!.toLowerCase())
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item => 
          item.licensePlate.toLowerCase().includes(params.licensePlate!.toLowerCase())
        );
      }

      if (params.reservationPhone) {
        filteredData = filteredData.filter(item => 
          item.reservationContactPhone.includes(params.reservationPhone!)
        );
      }

      if (params.servicePhone) {
        filteredData = filteredData.filter(item => 
          item.serviceContactPhone.includes(params.servicePhone!)
        );
      }

      if (params.status) {
        filteredData = filteredData.filter(item => item.status === params.status);
      }

      if (params.serviceType) {
        filteredData = filteredData.filter(item => item.serviceType === params.serviceType);
      }

      if (params.serviceAdvisorId) {
        filteredData = filteredData.filter(item => 
          item.serviceAdvisor?.id === params.serviceAdvisorId
        );
      }

      if (params.dateRange && params.dateRange.length === 2) {
        const [startDate, endDate] = params.dateRange;
        filteredData = filteredData.filter(item => {
          const itemDate = item.appointmentTime;
          return itemDate >= startDate && itemDate <= endDate;
        });
      }

      // Apply pagination
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};
