// src/mock/data/afterSales/quota.ts

import type { 
  QuotaSearchParams, 
  QuotaPageResponse, 
  QuotaConfig,
  TimeSlot,
  QuotaConfigRequest,
  StoreInfo 
} from '@/types/afterSales/quota.d.ts';

// 门店信息
export const mockStoreInfo: StoreInfo = {
  id: 'store_001',
  name: '北京朝阳店',
  code: 'BJ_CY_001'
};

// 生成动态 Mock 数据
function generateMockQuotaData(): QuotaConfig[] {
  const data: QuotaConfig[] = [];
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = -3; i <= 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    const configDate = date.toISOString().split('T')[0];

    const timeSlotCount = Math.floor(Math.random() * 5) + 2; // 2-6 slots
    let totalQuota = 0;
    
    for (let j = 0; j < timeSlotCount; j++) {
      const quota = Math.floor(Math.random() * 20) + 5; // 5-25 quota
      totalQuota += quota;
    }

    const bookedQuantity = Math.floor(Math.random() * (totalQuota + 1));
    const lastUpdate = new Date(date.getTime() - Math.random() * 86400000)
      .toISOString()
      .slice(0, 16)
      .replace('T', ' ');

    data.push({
      id: i + 4,
      configDate,
      timeSlotCount,
      totalQuota,
      bookedQuantity,
      lastUpdateTime: lastUpdate,
      isExpired: date < today,
    });
  }
  
  return data.sort((a, b) => new Date(a.configDate).getTime() - new Date(b.configDate).getTime());
}

const mockQuotaData = generateMockQuotaData();

// 存储时段配置数据
const existingQuotaData: { [key: string]: TimeSlot[] } = {};

// 初始化时段数据
mockQuotaData.forEach(config => {
  const slots: TimeSlot[] = [];
  for (let i = 0; i < config.timeSlotCount; i++) {
    const startHour = 8 + i;
    const startMin = Math.random() < 0.5 ? 0 : 30;
    const endHour = startHour + 1;
    const endMin = startMin;

    const start = `${String(startHour).padStart(2, '0')}:${String(startMin).padStart(2, '0')}`;
    const end = `${String(endHour).padStart(2, '0')}:${String(endMin).padStart(2, '0')}`;
    const quota = Math.floor(Math.random() * 20) + 5;

    slots.push({ id: i, start, end, quota });
  }
  existingQuotaData[config.configDate] = slots;
});

export const getQuotaList = (params: QuotaSearchParams): Promise<QuotaPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockQuotaData];

      // 日期范围筛选
      if (params.startDate) {
        filteredData = filteredData.filter(item => item.configDate >= params.startDate!);
      }
      if (params.endDate) {
        filteredData = filteredData.filter(item => item.configDate <= params.endDate!);
      }

      // 更新过期状态
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      filteredData = filteredData.map(item => ({
        ...item,
        isExpired: new Date(item.configDate) < today
      }));

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};

export const getQuotaTimeSlots = (date: string): Promise<TimeSlot[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(existingQuotaData[date] || []);
    }, 200);
  });
};

export const saveQuotaConfig = (config: QuotaConfigRequest): Promise<{ success: boolean; message: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 更新存储的时段数据
      existingQuotaData[config.date] = config.timeSlots.map((slot, index) => ({
        ...slot,
        id: index
      }));

      // 更新或添加配额配置
      const existingIndex = mockQuotaData.findIndex(item => item.configDate === config.date);
      const totalQuota = config.timeSlots.reduce((sum, slot) => sum + slot.quota, 0);
      const newConfig: QuotaConfig = {
        id: existingIndex >= 0 ? mockQuotaData[existingIndex].id : Date.now(),
        configDate: config.date,
        timeSlotCount: config.timeSlots.length,
        totalQuota,
        bookedQuantity: existingIndex >= 0 ? mockQuotaData[existingIndex].bookedQuantity : 0,
        lastUpdateTime: new Date().toISOString().slice(0, 16).replace('T', ' '),
        isExpired: false
      };

      if (existingIndex >= 0) {
        mockQuotaData[existingIndex] = newConfig;
      } else {
        mockQuotaData.push(newConfig);
        mockQuotaData.sort((a, b) => new Date(a.configDate).getTime() - new Date(b.configDate).getTime());
      }

      resolve({
        success: true,
        message: '配额配置保存成功'
      });
    }, 500);
  });
};

export const getStoreInfo = (): Promise<StoreInfo> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockStoreInfo);
    }, 100);
  });
};
