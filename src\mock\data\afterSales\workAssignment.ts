// src/mock/data/afterSales/workAssignment.ts

import type { 
  WorkOrderListParams, 
  WorkOrderPageResponse, 
  WorkOrderListItem,
  TechnicianInfo,
  AssignWorkOrderRequest,
  ReassignWorkOrderRequest,
  AssignmentResult,
  TechnicianWorkload,
  WorkAssignmentStatistics,
  WorkOrderStatus,
  WorkOrderType,
  WorkOrderPriority,
  TechnicianStatus
} from '@/types/afterSales/workAssignment.d.ts';

// 生成动态 Mock 数据
function generateMockWorkOrderData(): WorkOrderListItem[] {
  const data: WorkOrderListItem[] = [];
  const customerNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  const advisors = ['李明', '王强', '张伟', '刘洋', '陈华'];
  const technicians = ['陈师傅', '刘师傅', '王师傅', '张师傅', '李师傅'];
  const licensePlates = ['京A12345', '沪B67890', '粤C54321', '浙D98765', '苏E11111'];
  const vehicleModels = ['Model Y 2023', 'Model 3 2022', 'Model X 2024', 'Model S 2023'];
  const statuses: WorkOrderStatus[] = ['pending_assign', 'pending_start', 'in_progress', 'completed', 'cancelled'];
  const types: WorkOrderType[] = ['maintenance', 'repair', 'inspection', 'insurance'];
  const priorities: WorkOrderPriority[] = ['low', 'normal', 'high', 'urgent'];

  for (let i = 1; i <= 100; i++) {
    const createDate = new Date();
    createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 30));
    const updateDate = new Date(createDate.getTime() + Math.random() * 86400000 * 3);

    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const hasAssignedTechnician = status !== 'pending_assign';
    const assignedTechnician = hasAssignedTechnician ? technicians[Math.floor(Math.random() * technicians.length)] : undefined;

    data.push({
      workOrderId: `WO${String(i).padStart(8, '0')}`,
      workOrderNo: `WO${new Date().getFullYear()}${String(i).padStart(6, '0')}`,
      customerName: customerNames[Math.floor(Math.random() * customerNames.length)],
      customerPhone: `138${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
      licensePlate: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleVin: `LFV${Math.random().toString(36).substr(2, 14).toUpperCase()}`,
      status,
      workOrderType: types[Math.floor(Math.random() * types.length)],
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      serviceAdvisorId: `SA${String(Math.floor(Math.random() * 5) + 1).padStart(3, '0')}`,
      serviceAdvisorName: advisors[Math.floor(Math.random() * advisors.length)],
      assignedTechnicianId: hasAssignedTechnician ? `T${String(Math.floor(Math.random() * 5) + 1).padStart(3, '0')}` : undefined,
      assignedTechnicianName: assignedTechnician,
      estimatedDuration: Math.floor(Math.random() * 240) + 60, // 60-300分钟
      estimatedStartTime: hasAssignedTechnician ? new Date(createDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : undefined,
      actualStartTime: status === 'in_progress' || status === 'completed' ? new Date(createDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : undefined,
      actualEndTime: status === 'completed' ? new Date(createDate.getTime() + Math.random() * 86400000 * 2).toISOString().slice(0, 16).replace('T', ' ') : undefined,
      creationTime: createDate.toISOString().slice(0, 16).replace('T', ' '),
      updateTime: updateDate.toISOString().slice(0, 16).replace('T', ' '),
      description: `${types[Math.floor(Math.random() * types.length)] === 'repair' ? '维修' : '保养'}服务`,
      notes: Math.random() > 0.7 ? '客户要求使用原厂配件' : undefined
    });
  }
  
  return data.sort((a, b) => new Date(b.creationTime).getTime() - new Date(a.creationTime).getTime());
}

// 生成技师Mock数据
function generateMockTechnicianData(): TechnicianInfo[] {
  const technicianNames = ['陈师傅', '刘师傅', '王师傅', '张师傅', '李师傅'];
  const specialties = [
    ['发动机维修', '变速箱维修'],
    ['电气系统', '空调系统'],
    ['底盘维修', '刹车系统'],
    ['车身维修', '喷漆'],
    ['轮胎更换', '四轮定位']
  ];
  const statuses: TechnicianStatus[] = ['available', 'busy', 'offline'];

  return technicianNames.map((name, index) => ({
    technicianId: `T${String(index + 1).padStart(3, '0')}`,
    technicianName: name,
    technicianCode: `TECH${String(index + 1).padStart(3, '0')}`,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    skillLevel: Math.floor(Math.random() * 5) + 1,
    specialties: specialties[index],
    currentWorkload: Math.floor(Math.random() * 100),
    maxWorkload: 100,
    workingHours: {
      start: '08:00',
      end: '18:00'
    },
    contactInfo: {
      phone: `139${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
      email: `${name.replace('师傅', '')}@company.com`
    },
    department: '维修部',
    position: '高级技师',
    hireDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().slice(0, 10),
    isActive: true
  }));
}

const mockWorkOrderData = generateMockWorkOrderData();
const mockTechnicianData = generateMockTechnicianData();

export const getWorkOrderList = (params: WorkOrderListParams): Promise<WorkOrderPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockWorkOrderData];

      // 应用筛选条件
      if (params.workOrderId) {
        filteredData = filteredData.filter(item => 
          item.workOrderId.toLowerCase().includes(params.workOrderId!.toLowerCase())
        );
      }

      if (params.workOrderNo) {
        filteredData = filteredData.filter(item => 
          item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
        );
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item => 
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item => 
          item.licensePlate.includes(params.licensePlate!)
        );
      }

      if (params.status) {
        filteredData = filteredData.filter(item => item.status === params.status);
      }

      if (params.workOrderType) {
        filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType);
      }

      if (params.priority) {
        filteredData = filteredData.filter(item => item.priority === params.priority);
      }

      if (params.serviceAdvisorId) {
        filteredData = filteredData.filter(item => item.serviceAdvisorId === params.serviceAdvisorId);
      }

      if (params.assignedTechnicianId) {
        filteredData = filteredData.filter(item => item.assignedTechnicianId === params.assignedTechnicianId);
      }

      if (params.creationTimeStart && params.creationTimeEnd) {
        filteredData = filteredData.filter(item => {
          const itemDate = item.creationTime.split(' ')[0];
          return itemDate >= params.creationTimeStart! && itemDate <= params.creationTimeEnd!;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 300);
  });
};

export const getTechnicianList = (): Promise<TechnicianInfo[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockTechnicianData);
    }, 200);
  });
};

export const assignWorkOrder = (request: AssignWorkOrderRequest): Promise<AssignmentResult> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === request.workOrderId);
      if (index !== -1) {
        const technician = mockTechnicianData.find(t => t.technicianId === request.technicianId);
        if (technician) {
          mockWorkOrderData[index].assignedTechnicianId = request.technicianId;
          mockWorkOrderData[index].assignedTechnicianName = technician.technicianName;
          mockWorkOrderData[index].status = 'pending_start';
          mockWorkOrderData[index].estimatedStartTime = request.estimatedStartTime;
          mockWorkOrderData[index].estimatedDuration = request.estimatedDuration || mockWorkOrderData[index].estimatedDuration;
          mockWorkOrderData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
        }
      }

      resolve({
        success: true,
        message: '工单分配成功',
        workOrderId: request.workOrderId,
        assignedTechnicianId: request.technicianId,
        estimatedStartTime: request.estimatedStartTime
      });
    }, 800);
  });
};

export const reassignWorkOrder = (request: ReassignWorkOrderRequest): Promise<AssignmentResult> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === request.workOrderId);
      if (index !== -1) {
        const technician = mockTechnicianData.find(t => t.technicianId === request.toTechnicianId);
        if (technician) {
          mockWorkOrderData[index].assignedTechnicianId = request.toTechnicianId;
          mockWorkOrderData[index].assignedTechnicianName = technician.technicianName;
          mockWorkOrderData[index].estimatedStartTime = request.estimatedStartTime;
          mockWorkOrderData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
        }
      }

      resolve({
        success: true,
        message: '工单重新分配成功',
        workOrderId: request.workOrderId,
        assignedTechnicianId: request.toTechnicianId,
        estimatedStartTime: request.estimatedStartTime
      });
    }, 800);
  });
};

export const getTechnicianWorkload = (): Promise<TechnicianWorkload[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const workloadData = mockTechnicianData.map(technician => ({
        technicianId: technician.technicianId,
        technicianName: technician.technicianName,
        currentOrders: Math.floor(Math.random() * 5) + 1,
        totalWorkload: Math.floor(Math.random() * 480) + 120, // 120-600分钟
        availableCapacity: Math.floor(Math.random() * 240) + 60, // 60-300分钟
        workloadPercentage: technician.currentWorkload
      }));

      resolve(workloadData);
    }, 300);
  });
};

export const getAssignmentStatistics = (): Promise<WorkAssignmentStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const totalOrders = mockWorkOrderData.length;
      const pendingAssignment = mockWorkOrderData.filter(item => item.status === 'pending_assign').length;
      const assignedOrders = mockWorkOrderData.filter(item => item.status === 'pending_start').length;
      const inProgressOrders = mockWorkOrderData.filter(item => item.status === 'in_progress').length;
      const completedOrders = mockWorkOrderData.filter(item => item.status === 'completed').length;

      resolve({
        totalOrders,
        pendingAssignment,
        assignedOrders,
        inProgressOrders,
        completedOrders,
        averageAssignmentTime: Math.floor(Math.random() * 30) + 15, // 15-45分钟
        technicianUtilization: Math.floor(Math.random() * 40) + 60 // 60-100%
      });
    }, 300);
  });
};
