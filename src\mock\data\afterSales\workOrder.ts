// src/mock/data/afterSales/workOrder.ts

import type { 
  WorkOrderSearchParams, 
  WorkOrderPageResponse, 
  WorkOrderListItem,
  WorkOrderDetail,
  WorkOrderFormData,
  WorkOrderStatus,
  WorkOrderPriority,
  CustomerSource,
  WorkOrderType,
  PaymentStatus
} from '@/types/afterSales/workOrder.d.ts';

// 生成动态 Mock 数据
function generateMockWorkOrderData(): WorkOrderListItem[] {
  const data: WorkOrderListItem[] = [];
  const customerNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  const advisors = ['李明', '王强', '张伟', '刘洋'];
  const technicians = ['陈师傅', '刘师傅', '王师傅', '张师傅'];
  const licensePlates = ['京A12345', '沪B67890', '粤C54321', '浙D98765', '苏E11111'];
  const vehicleModels = ['Model Y 2023', 'Model 3 2022', 'Model X 2024', 'Model S 2023'];
  const statuses: WorkOrderStatus[] = ['draft', 'pending_confirmation', 'confirmed', 'in_progress', 'completed', 'cancelled'];
  const priorities: WorkOrderPriority[] = ['normal', 'urgent'];
  const customerSources: CustomerSource[] = ['appointment', 'walk_in'];
  const workOrderTypes: WorkOrderType[] = ['repair', 'maintenance', 'claim'];
  const paymentStatuses: PaymentStatus[] = ['unpaid', 'partial', 'paid'];

  for (let i = 1; i <= 50; i++) {
    const createDate = new Date();
    createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 60));
    const updateDate = new Date(createDate.getTime() + Math.random() * 86400000 * 7);
    
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const completedAt = status === 'completed' ? 
      new Date(updateDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : undefined;

    const estimatedAmount = Math.floor(Math.random() * 5000) + 500;
    const actualAmount = status === 'completed' ? estimatedAmount + Math.floor(Math.random() * 500) - 250 : 0;

    data.push({
      workOrderId: `WO${String(i).padStart(8, '0')}`,
      workOrderNumber: `WO${new Date().getFullYear()}${String(i).padStart(6, '0')}`,
      status,
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      customerSource: customerSources[Math.floor(Math.random() * customerSources.length)],
      workOrderType: workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)],
      customerName: customerNames[Math.floor(Math.random() * customerNames.length)],
      customerPhone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      licensePlate: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleVin: `LFV${Math.random().toString(36).substr(2, 14).toUpperCase()}`,
      serviceAdvisor: advisors[Math.floor(Math.random() * advisors.length)],
      technician: technicians[Math.floor(Math.random() * technicians.length)],
      estimatedAmount,
      actualAmount,
      paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
      createdAt: createDate.toISOString().slice(0, 16).replace('T', ' '),
      updatedAt: updateDate.toISOString().slice(0, 16).replace('T', ' '),
      completedAt,
      description: `${workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)] === 'repair' ? '维修' : '保养'}服务`,
      notes: Math.random() > 0.7 ? '客户要求使用原厂配件' : undefined
    });
  }
  
  return data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
}

const mockWorkOrderData = generateMockWorkOrderData();

export const getWorkOrderList = (params: WorkOrderSearchParams): Promise<WorkOrderPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockWorkOrderData];

      // 应用筛选条件
      if (params.workOrderNumber) {
        filteredData = filteredData.filter(item => 
          item.workOrderNumber.toLowerCase().includes(params.workOrderNumber!.toLowerCase())
        );
      }

      if (params.status) {
        filteredData = filteredData.filter(item => item.status === params.status);
      }

      if (params.priority) {
        filteredData = filteredData.filter(item => item.priority === params.priority);
      }

      if (params.customerSource) {
        filteredData = filteredData.filter(item => item.customerSource === params.customerSource);
      }

      if (params.workOrderType) {
        filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType);
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item => 
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.customerPhone) {
        filteredData = filteredData.filter(item => 
          item.customerPhone.includes(params.customerPhone!)
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item => 
          item.licensePlate.includes(params.licensePlate!)
        );
      }

      if (params.serviceAdvisor) {
        filteredData = filteredData.filter(item => 
          item.serviceAdvisor.includes(params.serviceAdvisor!)
        );
      }

      if (params.technician) {
        filteredData = filteredData.filter(item => 
          item.technician.includes(params.technician!)
        );
      }

      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter(item => {
          const itemDate = item.createdAt.split(' ')[0];
          return itemDate >= params.createdAtStart! && itemDate <= params.createdAtEnd!;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};

export const getWorkOrderDetail = (workOrderId: string): Promise<WorkOrderDetail> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const workOrder = mockWorkOrderData.find(item => item.workOrderId === workOrderId);
      if (workOrder) {
        const detail: WorkOrderDetail = {
          ...workOrder,
          serviceItems: [
            { id: '1', name: '机油更换', description: '全合成机油', price: 300, quantity: 1, total: 300 },
            { id: '2', name: '机滤更换', description: '原厂机滤', price: 80, quantity: 1, total: 80 }
          ],
          partItems: [
            { id: '1', partNumber: 'P001', partName: '机油滤芯', brand: '原厂', price: 80, quantity: 1, total: 80 },
            { id: '2', partNumber: 'P002', partName: '机油', brand: '美孚1号', price: 300, quantity: 1, total: 300 }
          ],
          laborItems: [
            { id: '1', operation: '机油更换', description: '更换机油和机滤', standardHours: 0.5, actualHours: 0.5, hourlyRate: 120, total: 60, technician: workOrder.technician }
          ],
          technicianNotes: '车辆保养正常，建议下次保养时间：6个月后'
        };
        resolve(detail);
      } else {
        reject(new Error('工单不存在'));
      }
    }, 500);
  });
};

export const createWorkOrder = (data: WorkOrderFormData): Promise<{ success: boolean; workOrderId: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = `WO${String(mockWorkOrderData.length + 1).padStart(8, '0')}`;
      const newNumber = `WO${new Date().getFullYear()}${String(mockWorkOrderData.length + 1).padStart(6, '0')}`;
      const now = new Date().toISOString().slice(0, 16).replace('T', ' ');

      const newWorkOrder: WorkOrderListItem = {
        ...data,
        workOrderId: newId,
        workOrderNumber: newNumber,
        createdAt: now,
        updatedAt: now
      } as WorkOrderListItem;

      mockWorkOrderData.unshift(newWorkOrder);

      resolve({
        success: true,
        workOrderId: newId
      });
    }, 800);
  });
};

export const updateWorkOrder = (workOrderId: string, data: Partial<WorkOrderFormData>): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === workOrderId);
      if (index !== -1) {
        mockWorkOrderData[index] = {
          ...mockWorkOrderData[index],
          ...data,
          updatedAt: new Date().toISOString().slice(0, 16).replace('T', ' ')
        };
      }

      resolve({ success: true });
    }, 800);
  });
};

export const deleteWorkOrder = (workOrderId: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === workOrderId);
      if (index !== -1) {
        mockWorkOrderData.splice(index, 1);
      }

      resolve({ success: true });
    }, 500);
  });
};

export const changeWorkOrderStatus = (workOrderId: string, newStatus: WorkOrderStatus, reason?: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === workOrderId);
      if (index !== -1) {
        mockWorkOrderData[index].status = newStatus;
        mockWorkOrderData[index].updatedAt = new Date().toISOString().slice(0, 16).replace('T', ' ');
        if (newStatus === 'completed') {
          mockWorkOrderData[index].completedAt = new Date().toISOString().slice(0, 16).replace('T', ' ');
        }
      }

      resolve({ success: true });
    }, 500);
  });
};
