import type {
  PendingApprovalListItem,
  CompletedApprovalListItem,
  WorkOrderApprovalListParams,
  ApprovalPageResponse,
  ClaimApprovalDetail,
  CancelApprovalDetail,
  WorkOrderApprovalRequest,
  WorkOrderApprovalResult,
  ExportApprovalParams,
  ApprovalStatistics,
  CustomerApprovalInfo,
  VehicleApprovalInfo,
  ClaimLaborItem,
  ClaimPartsItem,
  WorkOrderApprovalProcess
} from '@/types/afterSales/workOrderApproval';

// 动态生成待审批Mock数据
function generateMockPendingApprovals(): PendingApprovalListItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25; // 25-30条数据
  const mockData: PendingApprovalListItem[] = [];
  
  const approvalTypes = ['claim_approval', 'cancel_approval'] as const;
  const timeoutStatuses = ['normal', 'about_to_timeout', 'timeout'] as const;
  const vehicleModels = ['奔驰E300L', '宝马520Li', '奥迪A6L', '凯迪拉克CT6', '雷克萨斯ES300h'];
  const storeNames = ['北京4S店', '上海4S店', '广州4S店', '深圳4S店', '成都4S店'];
  const levels = ['first_level', 'second_level'] as const;
  
  for (let i = 0; i < dataCount; i++) {
    const submitTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
    const timeoutStatus = timeoutStatuses[Math.floor(Math.random() * timeoutStatuses.length)];
    
    mockData.push({
      id: `pending_${i + 1}`,
      approvalNo: `AP${String(Date.now() + i).slice(-10)}`,
      approvalType: approvalTypes[Math.floor(Math.random() * approvalTypes.length)],
      submitterName: `提交人${i + 1}`,
      submitTime: submitTime.toISOString().slice(0, 19).replace('T', ' '),
      orderNo: `WO${String(Date.now() + i).slice(-8)}`,
      requestReason: `审批原因${i + 1}：${Math.random() > 0.5 ? '车辆故障需要索赔维修' : '客户要求取消工单'}`,
      timeoutStatus,
      remainingTime: timeoutStatus === 'timeout' ? `已超时${Math.floor(Math.random() * 24)}小时` : 
                    timeoutStatus === 'about_to_timeout' ? `${Math.floor(Math.random() * 4) + 1}小时` : undefined,
      customerName: `客户${i + 1}`,
      licensePlate: `京A${String(10000 + i).slice(-5)}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      storeName: storeNames[Math.floor(Math.random() * storeNames.length)],
      currentLevel: levels[Math.floor(Math.random() * levels.length)]
    });
  }
  
  return mockData;
}

// 动态生成已审批Mock数据
function generateMockCompletedApprovals(): CompletedApprovalListItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 20; // 20-25条数据
  const mockData: CompletedApprovalListItem[] = [];
  
  const approvalTypes = ['claim_approval', 'cancel_approval'] as const;
  const approvalResults = ['approved', 'rejected'] as const;
  const vehicleModels = ['奔驰E300L', '宝马520Li', '奥迪A6L', '凯迪拉克CT6', '雷克萨斯ES300h'];
  const storeNames = ['北京4S店', '上海4S店', '广州4S店', '深圳4S店', '成都4S店'];
  const levels = ['first_level', 'second_level'] as const;
  const approvers = ['李经理', '王总监', '张主管', '陈经理', '刘总监'];
  
  for (let i = 0; i < dataCount; i++) {
    const submitTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const approvalTime = new Date(submitTime.getTime() + Math.random() * 48 * 60 * 60 * 1000);
    const approvalResult = approvalResults[Math.floor(Math.random() * approvalResults.length)];
    
    mockData.push({
      id: `completed_${i + 1}`,
      approvalNo: `AP${String(Date.now() - 1000000 + i).slice(-10)}`,
      approvalType: approvalTypes[Math.floor(Math.random() * approvalTypes.length)],
      submitterName: `提交人${i + 1}`,
      submitTime: submitTime.toISOString().slice(0, 19).replace('T', ' '),
      orderNo: `WO${String(Date.now() - 1000000 + i).slice(-8)}`,
      requestReason: `审批原因${i + 1}：${Math.random() > 0.5 ? '车辆故障需要索赔维修' : '客户要求取消工单'}`,
      timeoutStatus: 'normal',
      customerName: `客户${i + 1}`,
      licensePlate: `京A${String(20000 + i).slice(-5)}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      storeName: storeNames[Math.floor(Math.random() * storeNames.length)],
      currentLevel: levels[Math.floor(Math.random() * levels.length)],
      approvalResult,
      approvalRemark: approvalResult === 'approved' ? '符合审批条件，审批通过' : undefined,
      approvalTime: approvalTime.toISOString().slice(0, 19).replace('T', ' '),
      approverName: approvers[Math.floor(Math.random() * approvers.length)]
    });
  }
  
  return mockData;
}

const mockPendingData = generateMockPendingApprovals();
const mockCompletedData = generateMockCompletedApprovals();

/**
 * 获取待审批列表Mock数据
 */
export const getMockPendingApprovalList = (
  params: WorkOrderApprovalListParams
): Promise<ApprovalPageResponse<PendingApprovalListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockPendingData];
      
      if (params.approvalNo) {
        filteredData = filteredData.filter(item => 
          item.approvalNo.toLowerCase().includes(params.approvalNo!.toLowerCase())
        );
      }
      
      if (params.approvalType) {
        filteredData = filteredData.filter(item => 
          item.approvalType === params.approvalType
        );
      }
      
      if (params.submitterName) {
        filteredData = filteredData.filter(item => 
          item.submitterName.includes(params.submitterName!)
        );
      }
      
      if (params.orderNo) {
        filteredData = filteredData.filter(item => 
          item.orderNo.toLowerCase().includes(params.orderNo!.toLowerCase())
        );
      }
      
      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item =>
          item.licensePlate.includes(params.licensePlate!)
        );
      }

      if (params.currentLevel) {
        filteredData = filteredData.filter(item =>
          item.currentLevel === params.currentLevel
        );
      }
      
      if (params.timeoutStatus) {
        filteredData = filteredData.filter(item => 
          item.timeoutStatus === params.timeoutStatus
        );
      }
      
      // 时间范围过滤
      if (params.submitTimeStart) {
        filteredData = filteredData.filter(item => 
          item.submitTime >= params.submitTimeStart!
        );
      }
      
      if (params.submitTimeEnd) {
        filteredData = filteredData.filter(item => 
          item.submitTime <= params.submitTimeEnd!
        );
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 500);
  });
};

/**
 * 获取已审批列表Mock数据
 */
export const getMockCompletedApprovalList = (
  params: WorkOrderApprovalListParams
): Promise<ApprovalPageResponse<CompletedApprovalListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑（与待审批类似）
      let filteredData = [...mockCompletedData];
      
      if (params.approvalNo) {
        filteredData = filteredData.filter(item => 
          item.approvalNo.toLowerCase().includes(params.approvalNo!.toLowerCase())
        );
      }
      
      if (params.approvalType) {
        filteredData = filteredData.filter(item => 
          item.approvalType === params.approvalType
        );
      }
      
      if (params.submitterName) {
        filteredData = filteredData.filter(item => 
          item.submitterName.includes(params.submitterName!)
        );
      }
      
      if (params.orderNo) {
        filteredData = filteredData.filter(item => 
          item.orderNo.toLowerCase().includes(params.orderNo!.toLowerCase())
        );
      }
      
      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item =>
          item.licensePlate.includes(params.licensePlate!)
        );
      }

      if (params.approvalResult) {
        filteredData = filteredData.filter(item =>
          (item as CompletedApprovalListItem).approvalResult === params.approvalResult
        );
      }

      // 审批时间范围过滤
      if (params.approvalTimeStart) {
        filteredData = filteredData.filter(item =>
          (item as CompletedApprovalListItem).approvalTime >= params.approvalTimeStart!
        );
      }

      if (params.approvalTimeEnd) {
        filteredData = filteredData.filter(item =>
          (item as CompletedApprovalListItem).approvalTime <= params.approvalTimeEnd!
        );
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 500);
  });
};

/**
 * 获取索赔审批详情Mock数据
 */
export const getMockClaimApprovalDetail = (approvalNo: string): Promise<ClaimApprovalDetail> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockCustomerInfo: CustomerApprovalInfo = {
        customerId: 'C001',
        customerName: '李四',
        phone: '13812345678',
        senderName: '李四',
        senderPhone: '13812345678'
      };
      
      const mockVehicleInfo: VehicleApprovalInfo = {
        vehicleId: 'V001',
        licensePlate: '京A12345',
        vehicleModel: '奔驰E300L',
        vin: 'WDDHF8JB1CA123456',
        engineNo: 'M274920123456',
        purchaseDate: '2023-06-15',
        warrantyEndDate: '2026-06-15',
        mileage: 15000
      };
      
      const mockClaimLaborList: ClaimLaborItem[] = [
        {
          laborId: 'L001',
          laborCode: 'ENG001',
          laborName: '发动机检查',
          laborHours: 2.5,
          laborRate: 120,
          laborAmount: 300,
          description: '发动机故障诊断'
        }
      ];
      
      const mockClaimPartsList: ClaimPartsItem[] = [
        {
          partId: 'P001',
          partCode: 'ENG-FILTER-001',
          partName: '机油滤清器',
          quantity: 1,
          unitPrice: 45,
          totalAmount: 45,
          supplier: '原厂配件',
          description: '更换机油滤清器'
        }
      ];
      
      const mockApprovalProcessList: WorkOrderApprovalProcess[] = [
        {
          processId: 'PROC001',
          approvalNo,
          approvalLevel: 'first_level',
          approverId: 'U001',
          approverName: '李经理',
          approvalTime: '2024-12-10 10:45:00',
          approvalResult: 'approved',
          approvalRemark: '符合索赔条件，一级审批通过',
          isOvertime: false,
          createTime: '2024-12-10 09:30:00'
        }
      ];
      
      resolve({
        approvalNo,
        approvalType: 'claim_approval',
        submitterName: '张三（工号：E001）',
        submitTime: '2024-12-10 09:30:00',
        orderNo: 'WO202412100001',
        requestReason: '车辆发动机故障，需要更换零件并申请索赔',
        customerInfo: mockCustomerInfo,
        vehicleInfo: mockVehicleInfo,
        claimLaborList: mockClaimLaborList,
        claimPartsList: mockClaimPartsList,
        claimLaborTotal: 300,
        claimPartsTotal: 45,
        claimTotalAmount: 345,
        approvalStatus: 'pending_review',
        currentLevel: 'first_level',
        approvalProcessList: mockApprovalProcessList
      });
    }, 800);
  });
};

/**
 * 获取取消审批详情Mock数据
 */
export const getMockCancelApprovalDetail = (approvalNo: string): Promise<CancelApprovalDetail> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockCustomerInfo: CustomerApprovalInfo = {
        customerId: 'C002',
        customerName: '王五',
        phone: '13987654321',
        senderName: '王五',
        senderPhone: '13987654321'
      };

      const mockVehicleInfo: VehicleApprovalInfo = {
        vehicleId: 'V002',
        licensePlate: '京B67890',
        vehicleModel: '宝马520Li',
        vin: 'WBAJA5C50HG123456',
        engineNo: 'B48B20A123456',
        purchaseDate: '2023-08-20',
        warrantyEndDate: '2026-08-20',
        mileage: 8000
      };

      const mockApprovalProcessList: WorkOrderApprovalProcess[] = [
        {
          processId: 'PROC002',
          approvalNo,
          approvalLevel: 'first_level',
          approverId: 'U002',
          approverName: '王经理',
          approvalTime: '2024-12-10 11:30:00',
          approvalResult: 'approved',
          approvalRemark: '客户要求合理，同意取消工单',
          isOvertime: false,
          createTime: '2024-12-10 10:00:00'
        }
      ];

      resolve({
        approvalNo,
        approvalType: 'cancel_approval',
        submitterName: '赵六（工号：E002）',
        submitTime: '2024-12-10 10:00:00',
        orderNo: 'WO202412100002',
        requestReason: '客户临时有事，要求取消维修工单',
        customerInfo: mockCustomerInfo,
        vehicleInfo: mockVehicleInfo,
        cancelReason: '客户临时有急事需要用车',
        cancelDescription: '客户表示下周再来维修，要求先取消本次工单',
        approvalStatus: 'pending_review',
        currentLevel: 'first_level',
        approvalProcessList: mockApprovalProcessList
      });
    }, 800);
  });
};

/**
 * 提交审批Mock数据
 */
export const submitMockApproval = (data: WorkOrderApprovalRequest): Promise<WorkOrderApprovalResult> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟审批成功
      resolve({
        success: true,
        message: `审批${data.approvalResult === 'approved' ? '通过' : '驳回'}成功`,
        data: {
          approvalNo: data.approvalNo,
          approvalResult: data.approvalResult,
          processTime: new Date().toISOString()
        }
      });
    }, 1000);
  });
};

/**
 * 导出审批数据Mock
 */
export const exportMockApprovalData = (params: ExportApprovalParams): Promise<Blob> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟生成Excel文件
      const csvContent = `审批编号,审批类型,提交人,提交时间,工单编号,客户姓名,车牌号,审批状态
AP1234567890,索赔审批,张三,2024-12-10 09:30:00,WO12345678,李四,京A12345,待审批
AP1234567891,取消审批,赵六,2024-12-10 10:00:00,WO12345679,王五,京B67890,待审批`;

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      resolve(blob);
    }, 2000);
  });
};

/**
 * 获取审批统计Mock数据
 */
export const getMockApprovalStatistics = (): Promise<ApprovalStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        totalPending: mockPendingData.length,
        totalCompleted: mockCompletedData.length,
        approvedCount: mockCompletedData.filter(item => item.approvalResult === 'approved').length,
        rejectedCount: mockCompletedData.filter(item => item.approvalResult === 'rejected').length,
        timeoutCount: mockPendingData.filter(item => item.timeoutStatus === 'timeout').length,
        avgProcessTime: 18.5 // 平均处理时间（小时）
      });
    }, 300);
  });
};
