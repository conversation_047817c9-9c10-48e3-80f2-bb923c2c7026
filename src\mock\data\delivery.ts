import type { DeliveryOrderItem } from '@/types/module.d.ts';

export const mockDeliveryList: DeliveryOrderItem[] = [
  {
    deliveryId: '1',
    deliveryNumber: 'DEL20241201001',
    orderId: 'ORD001',
    orderNumber: 'SO202412010001',
    customerName: '张伟',
    customerPhone: '13800138001',
    customerType: '个人客户',
    idType: '身份证',
    idNumber: '110101199001011234',
    address: '北京市朝阳区建国路88号',
    city: '北京',
    postcode: '100020',
    state: '北京市',
    vin: 'WVWAA71K08W201001',
    model: 'Proton X50',
    variant: 'Premium',
    color: '极地白',
    vehiclePrice: 89800,
    dealerStore: '北京朝阳店',
    salesConsultant: '李明',
    orderStatus: 'pending_delivery',
    orderAmount: 89800,
    paymentMethod: '银行转账',
    orderTime: '2024-11-15T10:30:00Z',
    paymentTime: '2024-11-16T14:20:00Z',
    invoiceTime: '2024-11-18T09:15:00Z',
    deliveryStatus: 'pending_delivery',
    customerConfirmed: false,
    createTime: '2024-11-18T16:30:00Z',
    updateTime: '2024-11-18T16:30:00Z',
    creator: '李明',
    updater: '李明',
    canEdit: true,
    canConfirm: true
  },
  {
    deliveryId: '2',
    deliveryNumber: 'DEL20241201002',
    orderId: 'ORD002',
    orderNumber: 'SO202412010002',
    customerName: '王芳',
    customerPhone: '13800138002',
    customerType: '个人客户',
    idType: '身份证',
    idNumber: '110101199002021234',
    address: '上海市浦东新区陆家嘴环路100号',
    city: '上海',
    postcode: '200120',
    state: '上海市',
    vin: 'WVWAA71K08W201002',
    model: 'Proton X70',
    variant: 'Executive',
    color: '雪山白',
    vehiclePrice: 119800,
    dealerStore: '上海浦东店',
    salesConsultant: '陈华',
    orderStatus: 'pending_delivery',
    orderAmount: 119800,
    paymentMethod: '分期付款',
    orderTime: '2024-11-16T11:00:00Z',
    paymentTime: '2024-11-17T10:30:00Z',
    invoiceTime: '2024-11-19T14:45:00Z',
    deliveryStatus: 'pending_confirm',
    customerConfirmed: false,
    createTime: '2024-11-19T15:20:00Z',
    updateTime: '2024-11-20T09:15:00Z',
    creator: '陈华',
    updater: '陈华',
    canEdit: true,
    canConfirm: true
  },
  {
    deliveryId: '3',
    deliveryNumber: 'DEL20241201003',
    orderId: 'ORD003',
    orderNumber: 'SO202412010003',
    customerName: '刘强',
    customerPhone: '13800138003',
    customerType: '企业客户',
    idType: '营业执照',
    idNumber: '91110000123456789X',
    address: '广州市天河区珠江新城花城大道85号',
    city: '广州',
    postcode: '510623',
    state: '广东省',
    vin: 'WVWAA71K08W201003',
    model: 'Proton Saga',
    variant: 'Standard',
    color: '宝石蓝',
    vehiclePrice: 65800,
    dealerStore: '广州天河店',
    salesConsultant: '张丽',
    orderStatus: 'delivered',
    orderAmount: 65800,
    paymentMethod: '现金支付',
    orderTime: '2024-11-10T09:30:00Z',
    paymentTime: '2024-11-11T16:20:00Z',
    invoiceTime: '2024-11-12T11:15:00Z',
    deliveryStatus: 'delivered',
    customerConfirmed: true,
    confirmationType: 'app',
    customerConfirmTime: '2024-11-25T14:30:00Z',
    deliveryTime: '2024-11-25T14:30:00Z',
    deliveryNotes: '客户验车满意，APP确认完成交车',
    createTime: '2024-11-12T12:30:00Z',
    updateTime: '2024-11-25T14:30:00Z',
    creator: '张丽',
    updater: '张丽',
    canEdit: false,
    canConfirm: false
  },
  {
    deliveryId: '4',
    deliveryNumber: 'DEL20241201004',
    orderId: 'ORD004',
    orderNumber: 'SO202412010004',
    customerName: '赵敏',
    customerPhone: '13800138004',
    customerType: '个人客户',
    idType: '身份证',
    idNumber: '110101199004041234',
    address: '深圳市南山区科技园南区高新南一道6号',
    city: '深圳',
    postcode: '518057',
    state: '广东省',
    vin: 'WVWAA71K08W201004',
    model: 'Proton Persona',
    variant: 'Premium',
    color: '魅力红',
    vehiclePrice: 78800,
    dealerStore: '深圳南山店',
    salesConsultant: '孙伟',
    orderStatus: 'delivered',
    orderAmount: 78800,
    paymentMethod: '信用卡',
    orderTime: '2024-11-08T14:15:00Z',
    paymentTime: '2024-11-09T10:45:00Z',
    invoiceTime: '2024-11-10T16:30:00Z',
    deliveryStatus: 'delivered',
    customerConfirmed: true,
    confirmationType: 'offline',
    customerConfirmTime: '2024-11-22T16:45:00Z',
    deliveryTime: '2024-11-22T16:45:00Z',
    deliveryNotes: '客户现场签字确认，交车流程顺利完成',
    signaturePhoto: '/uploads/signatures/DEL20241201004_signature.jpg',
    createTime: '2024-11-10T17:30:00Z',
    updateTime: '2024-11-22T16:45:00Z',
    creator: '孙伟',
    updater: '孙伟',
    canEdit: false,
    canConfirm: false
  },
  {
    deliveryId: '5',
    deliveryNumber: 'DEL20241201005',
    orderId: 'ORD005',
    orderNumber: 'SO202412010005',
    customerName: '周杰',
    customerPhone: '13800138005',
    customerType: '个人客户',
    idType: '身份证',
    idNumber: '110101199005051234',
    address: '成都市高新区天府大道北段1700号',
    city: '成都',
    postcode: '610041',
    state: '四川省',
    vin: 'WVWAA71K08W201005',
    model: 'Proton Iriz',
    variant: 'Active',
    color: '星空灰',
    vehiclePrice: 52800,
    dealerStore: '成都高新店',
    salesConsultant: '吴敏',
    orderStatus: 'pending_delivery',
    orderAmount: 52800,
    paymentMethod: '银行转账',
    orderTime: '2024-11-20T08:30:00Z',
    paymentTime: '2024-11-21T15:20:00Z',
    invoiceTime: '2024-11-22T10:15:00Z',
    deliveryStatus: 'pending_delivery',
    customerConfirmed: false,
    createTime: '2024-11-22T11:30:00Z',
    updateTime: '2024-11-22T11:30:00Z',
    creator: '吴敏',
    updater: '吴敏',
    canEdit: true,
    canConfirm: true
  }
]; 