import type { OrderStatistics, FactoryOrderListItem, FactoryOrderDetail } from '@/types/module'

// 厂端订单统计管理Mock数据
export const factoryOrderMockData = {
  // 统计概览数据
  statistics: {
    monthlyOrderCount: 2156,
    dailyOrderCount: 72,
    monthlyGrowthRate: 12.5,
    dailyGrowthRate: 8.3,
    topDealers: [
      { dealerName: '吉隆坡中央店', orderCount: 168 },
      { dealerName: '槟城旗舰店', orderCount: 145 },
      { dealerName: '新山商业中心店', orderCount: 132 },
      { dealerName: '怡保市中心店', orderCount: 118 },
      { dealerName: '马六甲历史城店', orderCount: 95 }
    ],
    topVehicles: [
      { model: 'Model A', variant: 'Variant 1', salesCount: 342 },
      { model: 'Model B', variant: 'Variant 2', salesCount: 298 },
      { model: 'Model C', variant: 'Variant 1', salesCount: 256 },
      { model: 'Model A', variant: 'Variant 3', salesCount: 234 },
      { model: 'Model D', variant: 'Variant 1', salesCount: 198 }
    ],
    pendingDeliveryCount: 234,
    lastUpdateTime: '2024-03-15T10:30:00.000Z'
  } as OrderStatistics,

  // 订单列表数据
  orderList: [
    {
      id: '1',
      orderNumber: 'ORD202403150001',
      dealerName: '吉隆坡中央店',
      creationTime: '2024-03-15T09:30:00.000Z',
      ordererName: 'A***',
      ordererPhone: '+6012****6789',
      buyerName: 'L***',
      buyerPhone: '+6013****5432',
      buyerCategory: '个人用户',
      model: 'Model A',
      variant: 'Variant 1',
      color: '珍珠白',
      vin: 'VIN1****7890',
      paymentMethod: '贷款',
      loanApprovalStatus: '已通过',
      orderStatus: '已确认',
      orderApprovalStatus: '已审批',
      paymentStatus: '已付定金',
      insuranceStatus: '已投保',
      jpjRegistrationStatus: '待注册'
    },
    {
      id: '2',
      orderNumber: 'ORD202403150002',
      dealerName: '槟城旗舰店',
      creationTime: '2024-03-15T08:15:00.000Z',
      ordererName: 'C***',
      ordererPhone: '+6014****3456',
      buyerName: 'C***',
      buyerPhone: '+6014****3456',
      buyerCategory: '企业用户',
      model: 'Model B',
      variant: 'Variant 2',
      color: '典雅黑',
      vin: 'VIN2****3456',
      paymentMethod: '全款',
      loanApprovalStatus: '无需审批',
      orderStatus: '生产中',
      orderApprovalStatus: '已审批',
      paymentStatus: '已全款',
      insuranceStatus: '已投保',
      jpjRegistrationStatus: '已注册'
    },
    {
      id: '3',
      orderNumber: 'ORD202403140015',
      dealerName: '新山商业中心店',
      creationTime: '2024-03-14T16:45:00.000Z',
      ordererName: 'W***',
      ordererPhone: '+6015****7890',
      buyerName: 'W***',
      buyerPhone: '+6015****7890',
      buyerCategory: '个人用户',
      model: 'Model C',
      variant: 'Variant 1',
      color: '星河蓝',
      vin: 'VIN3****1234',
      paymentMethod: '贷款',
      loanApprovalStatus: '审批中',
      orderStatus: '待确认',
      orderApprovalStatus: '待审批',
      paymentStatus: '已付定金',
      insuranceStatus: '待投保',
      jpjRegistrationStatus: '待注册'
    },
    {
      id: '4',
      orderNumber: 'ORD202403140008',
      dealerName: '怡保市中心店',
      creationTime: '2024-03-14T11:20:00.000Z',
      ordererName: 'T***',
      ordererPhone: '+6016****2345',
      buyerName: 'H***',
      buyerPhone: '+6017****6789',
      buyerCategory: '个人用户',
      model: 'Model A',
      variant: 'Variant 3',
      color: '炫酷红',
      vin: 'VIN4****5678',
      paymentMethod: '全款',
      loanApprovalStatus: '无需审批',
      orderStatus: '已完成',
      orderApprovalStatus: '已审批',
      paymentStatus: '已全款',
      insuranceStatus: '已投保',
      jpjRegistrationStatus: '已注册'
    },
    {
      id: '5',
      orderNumber: 'ORD202403130025',
      dealerName: '马六甲历史城店',
      creationTime: '2024-03-13T14:30:00.000Z',
      ordererName: 'M***',
      ordererPhone: '+6018****4567',
      buyerName: 'M***',
      buyerPhone: '+6018****4567',
      buyerCategory: '个人用户',
      model: 'Model D',
      variant: 'Variant 1',
      color: '月光银',
      vin: 'VIN5****9012',
      paymentMethod: '贷款',
      loanApprovalStatus: '已通过',
      orderStatus: '待交车',
      orderApprovalStatus: '已审批',
      paymentStatus: '已付尾款',
      insuranceStatus: '已投保',
      jpjRegistrationStatus: '已注册'
    }
  ] as FactoryOrderListItem[],

  // 订单详情数据
  orderDetails: [
    {
      id: '1',
      orderNumber: 'ORD202403150001',
      creationTime: '2024-03-15T09:30:00.000Z',
      
      // 客户信息
      ordererName: 'A***',
      ordererPhone: '+6012****6789',
      buyerName: 'L***',
      buyerPhone: '+6013****5432',
      buyerIdType: 'IC身份证',
      buyerIdNumber: '850123****5678',
      buyerEmail: 'l***@example.com',
      buyerAddress: '***雪兰莪州八打灵再也',
      buyerState: '雪兰莪州',
      buyerCity: '八打灵再也',
      buyerPostcode: '47300',
      
      // 购车门店信息
      dealerRegion: '中央区域',
      dealerCity: '吉隆坡',
      dealerName: '吉隆坡中央店',
      salesConsultant: '张销售',
      
      // 车辆信息
      model: 'Model A',
      variant: 'Variant 1',
      color: '珍珠白',
      salesSubtotal: 65000.00,
      numberPlatesFee: 500.00,
      
      // 选配件信息
      accessories: [
        { category: '外观配件', name: '运动保险杠', unitPrice: 1200.00, quantity: 1, totalPrice: 1200.00 },
        { category: '内饰配件', name: '真皮座椅', unitPrice: 2500.00, quantity: 1, totalPrice: 2500.00 },
        { category: '科技配件', name: '导航系统', unitPrice: 1800.00, quantity: 1, totalPrice: 1800.00 }
      ],
      accessoriesTotalAmount: 5500.00,
      
      // 开票信息
      invoiceType: '个人',
      invoiceName: 'L***',
      invoicePhone: '+6013****5432',
      invoiceAddress: '***雪兰莪州八打灵再也',
      
      // 服务&权益信息
      benefits: [
        { benefitCode: 'WARRANTY001', benefitName: '延长保修', benefitMode: '优惠购买', discountPrice: 500.00, effectiveDate: '2024-03-15', expirationDate: '2027-03-15' },
        { benefitCode: 'SERVICE001', benefitName: '免费保养', benefitMode: '赠送', discountPrice: 800.00, effectiveDate: '2024-03-15', expirationDate: '2025-03-15' }
      ],
      benefitsTotalAmount: 1300.00,
      
      // 付款信息
      paymentMethod: '贷款',
      loanApprovalStatus: '已通过',
      depositAmount: 10000.00,
      loanAmount: 50000.00,
      balanceAmount: 11000.00,
      
      // 保险信息
      insurances: [
        { policyNumber: 'POL2024001', insuranceType: '车辆损失险', insuranceCompany: '太平洋保险', effectiveDate: '2024-03-15', expirationDate: '2025-03-15', insurancePrice: 1200.00 },
        { policyNumber: 'POL2024002', insuranceType: '第三者责任险', insuranceCompany: '太平洋保险', effectiveDate: '2024-03-15', expirationDate: '2025-03-15', insurancePrice: 800.00 }
      ],
      insurancesTotalAmount: 2000.00,
      insuranceNotes: '保险已生效，理赔服务24小时可用',
      
      // OTR费用信息
      otrFees: [
        { ticketNumber: 'ROAD2024001', feeItem: '路税', feePrice: 300.00, effectiveDate: '2024-03-15', expirationDate: '2025-03-15' },
        { ticketNumber: 'REG2024001', feeItem: '注册费', feePrice: 150.00, effectiveDate: '2024-03-15', expirationDate: '2024-03-15' },
        { ticketNumber: 'OWN2024001', feeItem: '所有权索赔费', feePrice: 50.00, effectiveDate: '2024-03-15', expirationDate: '2024-03-15' }
      ],
      otrFeesTotalAmount: 500.00,
      
      // 订单变更记录
      changeRecords: [
        { id: '1', originalContent: '车辆颜色：星河蓝', changedContent: '车辆颜色：珍珠白', operator: '张销售', operationTime: '2024-03-14T15:30:00.000Z' },
        { id: '2', originalContent: '配置：标准版', changedContent: '配置：豪华版', operator: '李经理', operationTime: '2024-03-14T10:15:00.000Z' }
      ],
      
      // 底部价格信息
      vehicleInvoicePrice: 71000.00,
      remainingReceivable: 11000.00
    }
  ] as FactoryOrderDetail[]
} 