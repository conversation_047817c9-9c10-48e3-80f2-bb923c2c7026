import type {
  WholeVehicleCollectionSearchParams,
  WholeVehicleCollectionPageResponse,
  WholeVehicleCollectionItem,
  OrderDetailInfo,
  PaymentRecord,
  AddPaymentRecordForm,
  ApiResponse
} from '@/types/finance/wholeVehicleCollection';

// 动态生成模拟数据（25-30条，便于测试分页）
function generateMockData(): WholeVehicleCollectionItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: WholeVehicleCollectionItem[] = [];

  const orderStatuses = ['已提交', '取消审核中', '取消审核通过', '已取消', '已确认', '待审核', '已审核', '待交车', '已交车'];
  const paymentStatuses = ['待支付定金', '已支付定金', '退款中', '退款完成', '待支付尾款', '已支付尾款'];
  const vehicleModels = ['SAGA', 'PERSONA', 'MYVI', 'ALZA', 'AXIA', 'BEZZA'];
  const colors = ['珍珠白', '宝石蓝', '炫酷红', '星空灰', '钛金银'];
  const stores = ['吉隆坡总店', '新山分店', '槟城分店', '怡保分店', '马六甲分店'];
  const consultants = ['李销售', '王顾问', '陈顾问', '张经理', '刘专员'];

  for (let i = 0; i < dataCount; i++) {
    const vehicleSalesPrice = Math.floor(Math.random() * 50000) + 40000;
    const insuranceAmount = Math.floor(Math.random() * 3000) + 2000;
    const otrAmount = Math.floor(Math.random() * 5000) + 3000;
    const discountAmount = Math.floor(Math.random() * 5000);
    const totalAmount = vehicleSalesPrice + insuranceAmount + otrAmount - discountAmount;
    const paidAmount = Math.floor(Math.random() * totalAmount);
    const unpaidAmount = totalAmount - paidAmount;
    const loanAmount = Math.random() > 0.5 ? Math.floor(Math.random() * 40000) + 20000 : 0;

    const createDate = new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000);
    const updateDate = new Date(createDate.getTime() + Math.floor(Math.random() * 7) * 24 * 60 * 60 * 1000);

    mockData.push({
      orderId: `ORD${String(i + 1).padStart(6, '0')}`,
      orderNumber: `ORD${String(Math.floor(Math.random() * 900000) + 100000)}`,
      buyerName: `客户${i + 1}`,
      buyerPhone: `01${String(Math.floor(Math.random() * 90000000) + 10000000)}`,
      dealerStoreName: stores[Math.floor(Math.random() * stores.length)],
      salesConsultantName: consultants[Math.floor(Math.random() * consultants.length)],
      vin: `MHKA1CA0XL${String(Math.floor(Math.random() * 900000) + 100000)}`,
      model: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      variant: Math.random() > 0.5 ? 'Standard' : 'Premium',
      color: colors[Math.floor(Math.random() * colors.length)],
      orderCreateTime: createDate.toISOString().replace('T', ' ').substring(0, 19),
      orderStatus: orderStatuses[Math.floor(Math.random() * orderStatuses.length)],
      paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
      vehicleSalesPrice,
      insuranceAmount,
      otrAmount,
      discountAmount,
      totalAmount,
      paidAmount,
      unpaidAmount,
      loanAmount,
      canInvoice: Math.random() > 0.3,
      invoiceTime: Math.random() > 0.5 ? updateDate.toISOString().replace('T', ' ').substring(0, 19) : undefined,
      invoiceNumber: Math.random() > 0.5 ? `INV${String(Math.floor(Math.random() * 900000) + 100000)}` : undefined,
      createTime: createDate.toISOString().replace('T', ' ').substring(0, 19),
      updateTime: updateDate.toISOString().replace('T', ' ').substring(0, 19)
    });
  }

  return mockData;
}

const mockData = generateMockData();

export const getWholeVehicleCollectionListMock = (
  params: WholeVehicleCollectionSearchParams
): Promise<ApiResponse<WholeVehicleCollectionPageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNumber.toLowerCase().includes(params.orderNumber!.toLowerCase())
        );
      }

      if (params.buyerName) {
        filteredData = filteredData.filter(item =>
          item.buyerName.includes(params.buyerName!)
        );
      }

      if (params.buyerPhone) {
        filteredData = filteredData.filter(item =>
          item.buyerPhone.includes(params.buyerPhone!)
        );
      }

      if (params.orderStatus) {
        filteredData = filteredData.filter(item =>
          item.orderStatus === params.orderStatus
        );
      }

      if (params.paymentStatus) {
        filteredData = filteredData.filter(item =>
          item.paymentStatus === params.paymentStatus
        );
      }

      if (params.canInvoice !== undefined) {
        filteredData = filteredData.filter(item =>
          item.canInvoice === params.canInvoice
        );
      }

      if (params.startDate && params.endDate) {
        filteredData = filteredData.filter(item => {
          const createTime = new Date(item.orderCreateTime);
          const start = new Date(params.startDate!);
          const end = new Date(params.endDate!);
          return createTime >= start && createTime <= end;
        });
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        code: '200',
        message: '获取成功',
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        },
        timestamp: Date.now()
      });
    }, 500);
  });
};

// 获取订单详情Mock
export const getOrderDetailMock = (orderId: string): Promise<ApiResponse<OrderDetailInfo>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const orderItem = mockData.find(item => item.orderId === orderId);
      if (!orderItem) {
        resolve({
          code: '404',
          message: '订单不存在',
          result: {} as OrderDetailInfo
        });
        return;
      }

      // 生成收退款记录
      const paymentRecords: PaymentRecord[] = [];
      const recordCount = Math.floor(Math.random() * 3) + 1;

      for (let i = 0; i < recordCount; i++) {
        const isRefund = Math.random() > 0.7;
        paymentRecords.push({
          paymentRecordId: `PAY${String(i + 1).padStart(6, '0')}`,
          paymentRecordNumber: `PAY${String(Math.floor(Math.random() * 900000) + 100000)}`,
          orderId: orderItem.orderId,
          businessType: isRefund ? '退款' : '收款',
          transactionNumber: `TXN${String(Math.floor(Math.random() * 900000) + 100000)}`,
          channel: Math.random() > 0.5 ? '银行转账' : '在线支付',
          amount: Math.floor(Math.random() * 10000) + 1000,
          paymentType: Math.random() > 0.5 ? '定金' : '尾款',
          arrivalTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)
            .toISOString().replace('T', ' ').substring(0, 19),
          remark: i === 0 ? '首次付款' : undefined,
          dataSource: '系统录入',
          isDeletable: Math.random() > 0.5,
          createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
          creator: '系统管理员'
        });
      }

      const orderDetail: OrderDetailInfo = {
        // 订单基本信息
        orderId: orderItem.orderId,
        orderNumber: orderItem.orderNumber,
        orderCreateTime: orderItem.orderCreateTime,
        orderStatus: orderItem.orderStatus,
        paymentStatus: orderItem.paymentStatus,
        paymentMethod: Math.random() > 0.5 ? '银行转账' : '在线支付',
        loanAmount: orderItem.loanAmount,
        canInvoice: orderItem.canInvoice,
        invoiceTime: orderItem.invoiceTime,
        invoiceNumber: orderItem.invoiceNumber,

        // 下单人信息
        ordererName: orderItem.buyerName,
        ordererPhone: orderItem.buyerPhone,

        // 购车人信息
        buyerName: orderItem.buyerName,
        buyerPhone: orderItem.buyerPhone,
        buyerIdType: 'IC',
        buyerIdNumber: `${Math.floor(Math.random() * 900000) + 100000}-${Math.floor(Math.random() * 90) + 10}-${Math.floor(Math.random() * 9000) + 1000}`,
        buyerEmail: `${orderItem.buyerName.toLowerCase()}@example.com`,
        buyerState: 'Selangor',
        buyerCity: 'Kuala Lumpur',
        buyerPostcode: String(Math.floor(Math.random() * 90000) + 10000),
        buyerAddress: `No. ${Math.floor(Math.random() * 999) + 1}, Jalan Example`,

        // 经销商信息
        dealerRegion: 'Central',
        dealerCity: 'Kuala Lumpur',
        dealerStoreName: orderItem.dealerStoreName,

        // 销售顾问信息
        salesConsultantName: orderItem.salesConsultantName,
        salesConsultantPhone: `01${String(Math.floor(Math.random() * 90000000) + 10000000)}`,
        salesConsultantEmail: `${orderItem.salesConsultantName.toLowerCase()}@dealer.com`,

        // 车辆信息
        model: orderItem.model,
        variant: orderItem.variant,
        color: orderItem.color,
        vin: orderItem.vin,
        warehouseName: '中央仓库',
        productionDate: new Date(Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000)
          .toISOString().substring(0, 10),
        options: Math.random() > 0.5 ? [
          { name: '导航系统', price: 2000 },
          { name: '倒车影像', price: 1500 }
        ] : undefined,

        // 价格信息
        salesSubtotal: orderItem.vehicleSalesPrice * 0.9,
        consumptionTax: orderItem.vehicleSalesPrice * 0.05,
        salesTax: orderItem.vehicleSalesPrice * 0.05,
        numberPlatesFee: 500,
        accessoriesTotalAmount: 3500,
        vehicleSalesPrice: orderItem.vehicleSalesPrice,
        insuranceAmount: orderItem.insuranceAmount,
        otrAmount: orderItem.otrAmount,
        discountAmount: orderItem.discountAmount,
        totalAmount: orderItem.totalAmount,
        paidAmount: orderItem.paidAmount,
        unpaidAmount: orderItem.unpaidAmount,

        // 收退款记录
        paymentRecords
      };

      resolve({
        code: '200',
        message: '获取成功',
        result: orderDetail,
        timestamp: Date.now()
      });
    }, 300);
  });
};

// 添加收退款记录Mock
export const addPaymentRecordMock = (
  orderId: string,
  record: AddPaymentRecordForm
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟验证逻辑
      if (!orderId || !record.transactionNumber || !record.amount) {
        resolve({
          code: '400',
          message: '参数不完整',
          result: false
        });
        return;
      }

      resolve({
        code: '200',
        message: '添加成功',
        result: true,
        timestamp: Date.now()
      });
    }, 800);
  });
};

// 删除收退款记录Mock
export const deletePaymentRecordMock = (recordId: string): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '200',
        message: '删除成功',
        result: true,
        timestamp: Date.now()
      });
    }, 500);
  });
};
