import Mock from 'mockjs';

const generatePartManagementHQData = (query: any = {}) => {
  const { page = 1, pageSize = 10, requisitionStatus = 'submitted' } = query;
  const total = 50;
  const list = [];

  for (let i = 0; i < pageSize; i++) {
    list.push(Mock.mock({
      id: `@guid()`,
      requisitionNumber: `JL${Mock.Random.string('number', 8)}`,
      purchaseOrderNumber: Mock.Random.boolean() ? `CG${Mock.Random.string('number', 8)}` : null,
      requisitionDate: Mock.Random.date('yyyy-MM-dd'),
      requisitionStatus: requisitionStatus === 'submitted' ? 'submitted' : Mock.Random.pick(['submitted', 'approved', 'rejected', 'shipped', 'received', 'cancelled']),
      partName: `零件名称${Mock.Random.integer(1, 100)}`,
      partNumber: `PN${Mock.Random.string('number', 6)}`,
      quantity: Mock.Random.integer(1, 50),
      unit: Mock.Random.pick(['个', '套', '件']),
      expectedArrivalTime: Mock.Random.date('yyyy-MM-dd'),
      supplierName: `供应商${Mock.Random.integer(1, 20)}`,
      remark: Mock.Random.cparagraph(1, 3),
      detailList: Array.from({ length: Mock.Random.integer(1, 5) }).map(() => ({
        id: `@guid()`,
        partName: `详情零件名称${Mock.Random.integer(1, 100)}`,
        partNumber: `详情PN${Mock.Random.string('number', 6)}`,
        quantity: Mock.Random.integer(1, 50),
        unit: Mock.Random.pick(['个', '套', '件']),
        requisitionStatus: Mock.Random.pick(['submitted', 'approved', 'rejected', 'shipped', 'received', 'cancelled']),
        requisitionDate: Mock.Random.date('yyyy-MM-dd'),
        expectedArrivalTime: Mock.Random.date('yyyy-MM-dd'),
        supplierName: `详情供应商${Mock.Random.integer(1, 20)}`,
      }))
    }));
  }

  // Filter by requisitionStatus if specified in query, but ensure 'submitted' is default if not specified
  const filteredList = list.filter((item: any) => {
    if (query.requisitionStatus && query.requisitionStatus !== '') {
      return item.requisitionStatus === query.requisitionStatus;
    } else if (requisitionStatus === 'submitted') {
      return item.requisitionStatus === 'submitted';
    }
    return true;
  });

  return {
    data: filteredList.slice((page - 1) * pageSize, page * pageSize),
    total: filteredList.length
  };
};

export const fetchPartManagementHQData = (query: any) => {
  return generatePartManagementHQData(query);
};
