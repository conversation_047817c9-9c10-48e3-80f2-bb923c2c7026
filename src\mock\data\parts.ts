import type { PaginationResponse, PartsSearchParameters, PartsListItem, PartsDetail, PartsDetailItem, ApprovePartsPayload } from '@/types/module.d';

const mockPartsData: PartsListItem[] = [
  {
    id: '1',
    serialNumber: 1,
    requisitionNumber: 'RQ20230101001',
    purchaseOrderNumber: 'PO20230105001',
    requisitionDate: '2023-01-01',
    requisitionStatus: 'submitted',
  },
  {
    id: '2',
    serialNumber: 2,
    requisitionNumber: 'RQ20230102002',
    purchaseOrderNumber: '',
    requisitionDate: '2023-01-02',
    requisitionStatus: 'submitted',
  },
  {
    id: '3',
    serialNumber: 3,
    requisitionNumber: 'RQ20230103003',
    purchaseOrderNumber: 'PO20230107002',
    requisitionDate: '2023-01-03',
    requisitionStatus: 'approved',
  },
  {
    id: '4',
    serialNumber: 4,
    requisitionNumber: 'RQ20230104004',
    purchaseOrderNumber: 'PO20230108003',
    requisitionDate: '2023-01-04',
    requisitionStatus: 'rejected',
  },
  {
    id: '5',
    serialNumber: 5,
    requisitionNumber: 'RQ20230105005',
    purchaseOrderNumber: 'PO20230109004',
    requisitionDate: '2023-01-05',
    requisitionStatus: 'shipped',
  },
  {
    id: '6',
    serialNumber: 6,
    requisitionNumber: 'RQ20230106006',
    purchaseOrderNumber: 'PO20230110005',
    requisitionDate: '2023-01-06',
    requisitionStatus: 'received',
  },
  {
    id: '7',
    serialNumber: 7,
    requisitionNumber: 'RQ20230107007',
    purchaseOrderNumber: 'PO20230111006',
    requisitionDate: '2023-01-07',
    requisitionStatus: 'voided',
  },
  {
    id: '8',
    serialNumber: 8,
    requisitionNumber: 'RQ20230108008',
    purchaseOrderNumber: 'PO20230112007',
    requisitionDate: '2023-01-08',
    requisitionStatus: 'submitted',
  },
  {
    id: '9',
    serialNumber: 9,
    requisitionNumber: 'RQ20230109009',
    purchaseOrderNumber: 'PO20230113008',
    requisitionDate: '2023-01-09',
    requisitionStatus: 'approved',
  },
  {
    id: '10',
    serialNumber: 10,
    requisitionNumber: 'RQ20230110010',
    purchaseOrderNumber: 'PO20230114009',
    requisitionDate: '2023-01-10',
    requisitionStatus: 'submitted',
  },
];

const mockDetailData: PartsDetail[] = [
  {
    id: '1',
    requisitionNumber: 'RQ20230101001',
    purchaseOrderNumber: 'PO20230105001',
    requisitionDate: '2023-01-01',
    items: [
      {
        id: '101',
        partName: '轮胎',
        partNumber: 'T-001',
        quantity: 4,
        unit: '个',
        requisitionStatus: 'submitted',
        requisitionDate: '2023-01-01',
        expectedArrivalTime: '2023-01-10',
        supplierName: '米其林',
      },
      {
        id: '102',
        partName: '刹车片',
        partNumber: 'B-001',
        quantity: 2,
        unit: '套',
        requisitionStatus: 'submitted',
        requisitionDate: '2023-01-01',
        expectedArrivalTime: '2023-01-10',
        supplierName: '博世',
      },
    ],
  },
  {
    id: '2',
    requisitionNumber: 'RQ20230102002',
    purchaseOrderNumber: '',
    requisitionDate: '2023-01-02',
    items: [
      {
        id: '201',
        partName: '机油滤清器',
        partNumber: 'F-001',
        quantity: 1,
        unit: '个',
        requisitionStatus: 'submitted',
        requisitionDate: '2023-01-02',
        expectedArrivalTime: '2023-01-12',
        supplierName: '曼牌',
      },
    ],
  },
  {
    id: '3',
    requisitionNumber: 'RQ20230103003',
    purchaseOrderNumber: 'PO20230107002',
    requisitionDate: '2023-01-03',
    items: [
      {
        id: '301',
        partName: '火花塞',
        partNumber: 'S-001',
        quantity: 4,
        unit: '个',
        requisitionStatus: 'approved',
        requisitionDate: '2023-01-03',
        expectedArrivalTime: '2023-01-13',
        supplierName: 'NGK',
      },
    ],
  },
];

export const mockPartsList = (params: PartsSearchParameters): Promise<PaginationResponse<PartsListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredList = [...mockPartsData];

      if (params.requisitionStatus) {
        filteredList = filteredList.filter(item => item.requisitionStatus === params.requisitionStatus);
      } else {
        // 默认显示叫料单状态为已提交的单据
        filteredList = filteredList.filter(item => item.requisitionStatus === 'submitted');
      }

      if (params.partName) {
        filteredList = filteredList.filter(item => 
          mockDetailData.some(detail => 
            detail.requisitionNumber === item.requisitionNumber && 
            detail.items.some(detailItem => detailItem.partName.includes(params.partName || ''))
          )
        );
      }
      if (params.partNumber) {
        filteredList = filteredList.filter(item => 
          mockDetailData.some(detail => 
            detail.requisitionNumber === item.requisitionNumber && 
            detail.items.some(detailItem => detailItem.partNumber.includes(params.partNumber || ''))
          )
        );
      }
      if (params.requisitionNumber) {
        filteredList = filteredList.filter(item => item.requisitionNumber.includes(params.requisitionNumber));
      }
      if (params.purchaseOrderNumber) {
        filteredList = filteredList.filter(item => item.purchaseOrderNumber.includes(params.purchaseOrderNumber));
      }
      if (params.supplierName) {
        filteredList = filteredList.filter(item => 
          mockDetailData.some(detail => 
            detail.requisitionNumber === item.requisitionNumber && 
            detail.items.some(detailItem => detailItem.supplierName.includes(params.supplierName || ''))
          )
        );
      }

      if (params.requisitionDateRange && params.requisitionDateRange.length === 2) {
        const [start, end] = params.requisitionDateRange;
        filteredList = filteredList.filter(item => {
          const itemDate = new Date(item.requisitionDate);
          const startDate = new Date(start);
          const endDate = new Date(end);
          return itemDate >= startDate && itemDate <= endDate;
        });
      }

      if (params.arrivalDateRange && params.arrivalDateRange.length === 2) {
        const [start, end] = params.arrivalDateRange;
        filteredList = filteredList.filter(item => 
          mockDetailData.some(detail => 
            detail.requisitionNumber === item.requisitionNumber && 
            detail.items.some(detailItem => {
              const itemDate = new Date(detailItem.expectedArrivalTime);
              const startDate = new Date(start);
              const endDate = new Date(end);
              return itemDate >= startDate && itemDate <= endDate;
            })
          )
        );
      }

      if (params.stockStatus) {
        // For simplicity, stock status is mocked based on part name. In a real scenario, this would come from backend.
        filteredList = filteredList.filter(item => {
          const detail = mockDetailData.find(d => d.requisitionNumber === item.requisitionNumber);
          if (detail && detail.items.length > 0) {
            // Example: If a part name is '刹车片', and stock status is 'below_safe', mock it as below safe.
            if (params.stockStatus === 'below_safe' && detail.items.some(di => di.partName === '刹车片')) {
              return true;
            } else if (params.stockStatus === 'normal' && !detail.items.some(di => di.partName === '刹车片')) {
              return true;
            }
            return false;
          }
          return false;
        });
      }

      const { page = 1, pageSize = 10 } = params;
      const total = filteredList.length;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const list = filteredList.slice(startIndex, endIndex);

      resolve({
        list,
        total,
        page,
        pageSize,
      });
    }, 500);
  });
};

export const mockPartsDetail = (id: string | number): Promise<PartsDetail> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const detail = mockDetailData.find(d => d.id === id);
      if (detail) {
        resolve(detail);
      } else {
        reject(new Error('Part detail not found'));
      }
    }, 500);
  });
};

export const mockApproveParts = (payload: ApprovePartsPayload): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const partIndex = mockPartsData.findIndex(item => item.id === payload.id);
      if (partIndex !== -1) {
        mockPartsData[partIndex].requisitionStatus = payload.approvalResult;
        // Optionally store rejection reason if needed for mock data display
        resolve();
      } else {
        reject(new Error('Parts item not found for approval'));
      }
    }, 500);
  });
}; 