import type {
  PartsReceiptListItem,
  PartsReceiptSearchParams,
  PaginationResponse,
  ReceiptItem,
  GenerateReceiptOrderSearchParams,
  GenerateReceiptOrderListItem,
  InvalidateReceiptOrderSearchParams,
  InvalidateReceiptOrderListItem,
  ReceiptOrderDetailItem
} from '@/types/partsReceipt';

// 模拟数据存储
let mockPartsReceiptList: PartsReceiptListItem[] = [
  {
    id: '1',
    receiptOrderNumber: 'SH20230001',
    partName: '机油滤清器',
    partNumber: 'A123B456',
    partQuantity: 10,
    unit: '个',
    supplierName: '供应商A',
    requisitionNumber: 'JL2023001',
    deliveryOrderNumber: 'DH20230001',
    purchaseOrderNumber: 'CG2023001',
  },
  {
    id: '2',
    receiptOrderNumber: 'SH20230002',
    partName: '刹车片',
    partNumber: 'B789C012',
    partQuantity: 20,
    unit: '套',
    supplierName: '供应商B',
    requisitionNumber: 'JL2023002',
    deliveryOrderNumber: 'DH20230002',
    purchaseOrderNumber: 'CG2023002',
  },
  {
    id: '3',
    receiptOrderNumber: 'SH20230003',
    partName: '火花塞',
    partNumber: 'D345E678',
    partQuantity: 50,
    unit: '个',
    supplierName: '供应商C',
    requisitionNumber: 'JL2023003',
    deliveryOrderNumber: 'DH20230003',
    purchaseOrderNumber: 'CG2023003',
  },
  {
    id: '4',
    receiptOrderNumber: 'SH20230004',
    partName: '空气滤清器',
    partNumber: 'F901G234',
    partQuantity: 15,
    unit: '个',
    supplierName: '供应商A',
    requisitionNumber: 'JL2023004',
    deliveryOrderNumber: 'DH20230004',
    purchaseOrderNumber: 'CG2023004',
  },
  {
    id: '5',
    receiptOrderNumber: 'SH20230005',
    partName: '轮胎',
    partNumber: 'H567I890',
    partQuantity: 4,
    unit: '条',
    supplierName: '供应商B',
    requisitionNumber: 'JL2023005',
    deliveryOrderNumber: 'DH20230005',
    purchaseOrderNumber: 'CG2023005',
  },
];

const mockGeneratedReceiptOrders: GenerateReceiptOrderListItem[] = [
  {
    id: 'GR1',
    partName: '机油滤清器',
    partNumber: 'A123B456',
    quantity: 5,
    unit: '个',
    supplierName: '供应商A',
    receiptTime: '2023-10-26',
    receiptOrderNumber: 'SHD20230001',
    purchaseOrderNumber: 'CG2023001',
    requisitionNumber: 'JL2023001',
  },
  {
    id: 'GR2',
    partName: '刹车片',
    partNumber: 'B789C012',
    quantity: 10,
    unit: '套',
    supplierName: '供应商B',
    receiptTime: '2023-10-25',
    receiptOrderNumber: 'SHD20230002',
    purchaseOrderNumber: 'CG2023002',
    requisitionNumber: 'JL2023002',
  },
];

const mockInvalidateReceiptOrders: InvalidateReceiptOrderListItem[] = [
  {
    id: 'IR1',
    receiptOrderNumber: 'SHD20230001',
    generationDate: '2023-10-26',
    receiptOrderStatus: 'effective',
  },
  {
    id: 'IR2',
    receiptOrderNumber: 'SHD20230002',
    generationDate: '2023-10-25',
    receiptOrderStatus: 'effective',
  },
  {
    id: 'IR3',
    receiptOrderNumber: 'SHD20230003',
    generationDate: '2023-10-24',
    receiptOrderStatus: 'invalidated',
  },
];

// 模拟 API 延迟
const mockDelay = (ms: number) => new Promise(res => setTimeout(res, ms));

/**
 * 模拟获取零件收货列表 API
 */
export const mockGetPartsReceiptList = async (params: PartsReceiptSearchParams): Promise<PaginationResponse<PartsReceiptListItem>> => {
  await mockDelay(500);
  let filteredList = mockPartsReceiptList;

  if (params.partName) {
    filteredList = filteredList.filter(item => item.partName.includes(params.partName));
  }
  if (params.partNumber) {
    filteredList = filteredList.filter(item => item.partNumber.includes(params.partNumber));
  }
  if (params.supplierName) {
    filteredList = filteredList.filter(item => item.supplierName.includes(params.supplierName));
  }
  if (params.requisitionNumber) {
    filteredList = filteredList.filter(item => item.requisitionNumber.includes(params.requisitionNumber));
  }
  if (params.purchaseOrderNumber) {
    filteredList = filteredList.filter(item => item.purchaseOrderNumber.includes(params.purchaseOrderNumber));
  }
  if (params.deliveryOrderNumber) {
    filteredList = filteredList.filter(item => item.deliveryOrderNumber.includes(params.deliveryOrderNumber));
  }

  const page = (params as any).page || 1;
  const pageSize = (params as any).pageSize || 10;
  const total = filteredList.length;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const data = filteredList.slice(startIndex, endIndex);

  return {
    data,
    total,
    page,
    pageSize,
  };
};

/**
 * 模拟执行收货操作 API
 */
export const mockReceiveParts = async (items: ReceiptItem[]): Promise<any> => {
  await mockDelay(500);
  items.forEach(receivedItem => {
    // 从待收货列表中移除已收货的项
    mockPartsReceiptList = mockPartsReceiptList.filter(item => item.id !== receivedItem.id);

    // 可以添加逻辑来处理破损数量和原因，例如记录到另一个模拟列表中
    if (receivedItem.damagedQuantity > 0) {
      console.log(`零件 ${receivedItem.partName} 破损 ${receivedItem.damagedQuantity} 个，原因: ${receivedItem.damageReason}`);
    }

    // 将收货的零件添加到已生成收货单的模拟数据中（简化处理，实际可能需要更复杂的逻辑）
    mockGeneratedReceiptOrders.push({
      id: `GR${mockGeneratedReceiptOrders.length + 1}`,
      partName: receivedItem.partName,
      partNumber: receivedItem.partNumber,
      quantity: receivedItem.receivedQuantity,
      unit: receivedItem.unit,
      supplierName: receivedItem.supplierName,
      receiptTime: new Date().toISOString().split('T')[0], // 当前日期
      receiptOrderNumber: `SHD${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`, // 随机生成收货单号
      purchaseOrderNumber: receivedItem.purchaseOrderNumber,
      requisitionNumber: receivedItem.requisitionNumber,
    });
  });
  return { code: 0, message: '收货成功' };
};

/**
 * 模拟生成收货单 API
 */
export const mockGenerateReceiptOrder = async (items: GenerateReceiptOrderListItem[]): Promise<any> => {
  await mockDelay(500);
  items.forEach(item => {
    // 假设这些项是已经收货的，现在只是生成打印单据
    // 在实际应用中，这里可能会根据已收货的明细创建新的收货单记录，并分配新的收货单号
    const existingItemIndex = mockGeneratedReceiptOrders.findIndex(order => order.id === item.id);
    if (existingItemIndex !== -1) {
        // 假设生成收货单后，这些记录就从"可生成收货单"列表中消失
        // 这里只是模拟，直接从 mockGeneratedReceiptOrders 中删除或标记为已打印
        mockGeneratedReceiptOrders.splice(existingItemIndex, 1);
    }
    // 也可以将这些已生成的收货单添加到另一个已打印收货单的列表
    mockInvalidateReceiptOrders.push({
        id: `IR${mockInvalidateReceiptOrders.length + 1}`,
        receiptOrderNumber: `SHD${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        generationDate: new Date().toISOString().split('T')[0],
        receiptOrderStatus: 'effective',
    });
  });
  return { code: 0, message: '生成收货单成功' };
};

/**
 * 模拟作废收货单 API
 */
export const mockInvalidateReceiptOrder = async (receiptOrderNumber: string): Promise<any> => {
  await mockDelay(500);
  const index = mockInvalidateReceiptOrders.findIndex(item => item.receiptOrderNumber === receiptOrderNumber);
  if (index !== -1) {
    mockInvalidateReceiptOrders[index].receiptOrderStatus = 'invalidated';
    return { code: 0, message: '作废收货单成功' };
  }
  return { code: 1, message: '收货单不存在或已作废' };
};

/**
 * 模拟获取收货单明细 API
 */
export const mockGetReceiptOrderDetail = async (receiptOrderNumber: string): Promise<PaginationResponse<ReceiptOrderDetailItem>> => {
  await mockDelay(500);
  // 假设根据收货单号返回对应的明细，这里简化为返回一个固定列表
  const detailList: ReceiptOrderDetailItem[] = [
    {
      id: 'D1',
      partName: '机油滤清器',
      partNumber: 'A123B456',
      quantity: 5,
      unit: '个',
      supplierName: '供应商A',
      receiptTime: '2023-10-26',
    },
    {
      id: 'D2',
      partName: '螺丝',
      partNumber: 'X123Y456',
      quantity: 20,
      unit: '个',
      supplierName: '供应商A',
      receiptTime: '2023-10-26',
    },
  ];
  return {
    data: detailList,
    total: detailList.length,
    page: 1,
    pageSize: detailList.length,
  };
}; 