import type { PartArchiveItem, PartArchiveSearchParams, PartArchivePageResponse } from '@/types/parts/archives';

// 零件名称库
const partNames = [
  '刹车片', '机油滤清器', '火花塞', '雨刮器', '空气滤芯',
  '机油', '防冻液', '刹车油', '变速箱油', '转向助力油',
  '前减震器', '后减震器', '轮胎', '轮毂', '刹车盘',
  '电瓶', '发电机', '起动机', '空调滤芯', '燃油滤清器',
  '正时皮带', '发动机皮带', '离合器片', '水泵', '节温器'
];

// 单位库
const units = ['套', '个', '支', '对', '瓶', '升', '条', '只', '片', '包'];

// 供应商名称库
const supplierNames = [
  '博世汽配', '德尔福配件', '天合汽车', '法雷奥集团', '电装公司',
  '大陆集团', '采埃孚', '麦格纳', '爱信精机', '马勒配件'
];

// 生成随机数据
function generateMockData(): PartArchiveItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25; // 生成25-30条数据
  const mockData: PartArchiveItem[] = [];
  
  for (let i = 0; i < dataCount; i++) {
    const partIndex = i % partNames.length;
    const supplierIndex = Math.floor(Math.random() * supplierNames.length);
    
    mockData.push({
      partName: partNames[partIndex],
      partNumber: `PN${String(i + 1).padStart(4, '0')}`, // PN0001, PN0002...
      unit: units[Math.floor(Math.random() * units.length)],
      supplierName: supplierNames[supplierIndex],
      purchasePrice: Math.round((Math.random() * 500 + 20) * 100) / 100 // 20-520之间的随机价格
    });
  }
  
  return mockData;
}

// 生成模拟数据
const mockData: PartArchiveItem[] = generateMockData();

/**
 * 获取零件档案列表
 * @param params 搜索参数
 */
export const getPartArchiveList = (params: PartArchiveSearchParams): Promise<PartArchivePageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤
      let filteredData = [...mockData];
      
      if (params.partName) {
        filteredData = filteredData.filter(item => 
          item.partName.includes(params.partName!)
        );
      }
      
      if (params.partNumber) {
        filteredData = filteredData.filter(item => 
          item.partNumber.includes(params.partNumber!)
        );
      }
      
      if (params.supplierName) {
        filteredData = filteredData.filter(item => 
          item.supplierName.includes(params.supplierName!)
        );
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      const paginatedData = filteredData.slice(start, end);
      
      resolve({
        list: paginatedData,
        total: filteredData.length
      });
    }, 500); // 模拟API延迟
  });
};