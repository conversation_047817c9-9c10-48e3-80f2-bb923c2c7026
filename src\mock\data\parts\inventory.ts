
import type {
  InventoryDashboard,
  InventorySearchParams,
  InventoryListResponse,
  InventoryItem,
  StockStatus,
  InventoryDetail,
  InventoryTrend,
  InventoryAdjustParams,
  InventoryAdjustResult,
  BatchReplenishmentParams,
  BatchReplenishmentResult,
} from '@/types/parts/inventory';

const stockStatusOptions: StockStatus[] = ['NORMAL', 'WARNING', 'SHORTAGE', 'OVERSTOCKED'];

// 简单的模拟数据生成工具
const mockUtils = {
  randomInt: (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min,
  randomChoice: <T>(arr: T[]): T => arr[Math.floor(Math.random() * arr.length)],
  randomString: (length: number) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
  },
  randomPrice: (min: number, max: number) => {
    return parseFloat((Math.random() * (max - min) + min).toFixed(2));
  },
  randomDate: (daysAgo: number) => {
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * daysAgo));
    return date.toISOString();
  }
};

const partNames = [
  '火花塞', '机油滤清器', '刹车片前', '刹车片后', '空气滤芯', '雨刮片',
  '机油', '防冻液', '刹车油', '变速箱油', '轮胎', '电瓶',
  '点火线圈', '氧传感器', '节气门', '水泵', '燃油泵', '发电机'
];

const brands = ['博世', '曼牌', '天合', '马勒', '美孚', '嘉实多', '米其林', '普利司通'];

const warehouses = ['主仓库', '备件仓', '展示仓'];
const locations = ['A', 'B', 'C'];

const names = ['张库管', '李管理', '王仓管', '刘主管', '陈经理'];

const mockInventoryItems: InventoryItem[] = Array.from({ length: 1958 }, (_, i) => {
  const safetyStock = mockUtils.randomInt(5, 20);
  const maximumStock = safetyStock * 4;
  const currentStock = mockUtils.randomInt(0, maximumStock + 10);
  let stockStatus: StockStatus;

  if (currentStock === 0) {
    stockStatus = 'SHORTAGE';
  } else if (currentStock <= safetyStock) {
    stockStatus = 'WARNING';
  } else if (currentStock > maximumStock) {
    stockStatus = 'OVERSTOCKED';
  } else {
    stockStatus = 'NORMAL';
  }

  const availableStock = currentStock > 0 ? mockUtils.randomInt(0, currentStock) : 0;
  const occupiedStock = currentStock > availableStock ? currentStock - availableStock : 0;

  return {
    inventoryId: 1000 + i,
    stockStatus,
    partCode: `P${mockUtils.randomString(8)}`,
    partName: mockUtils.randomChoice(partNames),
    brand: mockUtils.randomChoice(brands),
    specification: `规格${mockUtils.randomString(4)}`,
    currentStock,
    occupiedStock,
    damagedStock: mockUtils.randomInt(0, 5),
    safetyStock,
    availableStock,
    warehouseName: mockUtils.randomChoice(warehouses),
    lastCheckTime: mockUtils.randomDate(30).split('T')[0], // 格式化为 YYYY-MM-DD
  };
});

export const getMockInventoryDashboard = async (params: { storeId: number }): Promise<InventoryDashboard> => {
  await new Promise(resolve => setTimeout(resolve, mockUtils.randomInt(100, 400)));
  return {
    totalSkuCount: 2458,
    shortageCount: mockInventoryItems.filter(p => p.stockStatus === 'SHORTAGE').length,
    warningCount: mockInventoryItems.filter(p => p.stockStatus === 'WARNING').length,
    occupiedValue: 125000.00,
    trends: {
      skuGrowthRate: 5.2,
      trendDirection: 'UP',
    },
  };
};

export const getMockInventoryList = async (params: InventorySearchParams): Promise<InventoryListResponse> => {
  await new Promise(resolve => setTimeout(resolve, mockUtils.randomInt(300, 800)));
  
  const { page, size, category, partCode, partName, stockStatus } = params;
  
  let filteredData = mockInventoryItems;

  if (partCode) {
    filteredData = filteredData.filter(item => item.partCode.includes(partCode));
  }
  if (partName) {
    filteredData = filteredData.filter(item => item.partName.toLowerCase().includes(partName.toLowerCase()));
  }
  if (stockStatus && stockStatus.length > 0) {
    filteredData = filteredData.filter(item => stockStatus.includes(item.stockStatus));
  }

  const totalCount = filteredData.length;
  const start = (page - 1) * size;
  const end = start + size;
  const data = filteredData.slice(start, end);

  return {
    data,
    pagination: {
      currentPage: page,
      pageSize: size,
      totalCount,
      totalPages: Math.ceil(totalCount / size),
    },
  };
};

export const getMockInventoryDetail = async (params: { inventoryId: number }): Promise<InventoryDetail> => {
  await new Promise(resolve => setTimeout(resolve, mockUtils.randomInt(200, 500)));
  const item = mockInventoryItems.find(i => i.inventoryId === params.inventoryId);
  
  return {
    partInfo: {
      partCode: item?.partCode || 'P-UNKNOWN',
      partName: item?.partName || 'Unknown Part',
      specification: item?.specification || `规格${mockUtils.randomString(4)}`,
      unit: '件',
      retailPrice: mockUtils.randomPrice(100, 500),
      purchasePrice: mockUtils.randomPrice(50, 300),
    },
    inventoryInfo: {
      currentStock: item?.currentStock || 0,
      availableStock: item?.availableStock || 0,
      occupiedStock: item?.occupiedStock || 0,
      damagedStock: item?.damagedStock || 0,
      safetyStock: item?.safetyStock || 5,
      maximumStock: (item?.safetyStock || 5) * 4,
      stockStatus: item?.stockStatus || 'NORMAL',
      shelfNumber: `A${mockUtils.randomInt(1, 10)}`,
      lastCheckTime: mockUtils.randomDate(30),
      checkPerson: mockUtils.randomChoice(names),
    },
  };
};

export const getMockInventoryTrend = async (params: { inventoryId: number, days?: number }): Promise<InventoryTrend> => {
  await new Promise(resolve => setTimeout(resolve, mockUtils.randomInt(400, 900)));
  const days = params.days || 30;
  const item = mockInventoryItems.find(i => i.inventoryId === params.inventoryId);
  const trendData = Array.from({ length: days }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - i);
    return {
      date: date.toISOString().split('T')[0],
      stock: mockUtils.randomInt((item?.safetyStock || 5) - 2, (item?.safetyStock || 5) * 4),
    };
  }).reverse();

  return {
    safetyStockLine: item?.safetyStock || 8,
    trendData,
  };
};

export const mockAdjustInventory = async (params: InventoryAdjustParams): Promise<InventoryAdjustResult> => {
  await new Promise(resolve => setTimeout(resolve, mockUtils.randomInt(300, 600)));
  const item = mockInventoryItems.find(i => i.inventoryId === params.inventoryId);
  if (!item) throw new Error('Inventory item not found');

  const beforeStock = item.currentStock;
  let afterStock = beforeStock;

  switch (params.adjustType) {
    case 'INCREASE':
      afterStock += params.quantity;
      break;
    case 'DECREASE':
      afterStock -= params.quantity;
      break;
    case 'SET_TO':
      afterStock = params.quantity;
      break;
  }
  item.currentStock = afterStock;

  return {
    adjustmentId: mockUtils.randomInt(2000, 3000),
    beforeStock,
    afterStock,
  };
};

export const mockBatchCreateReplenishment = async (params: BatchReplenishmentParams): Promise<BatchReplenishmentResult> => {
  await new Promise(resolve => setTimeout(resolve, mockUtils.randomInt(500, 1000)));
  return {
    orderNo: `REP${new Date().getFullYear()}${mockUtils.randomString(8)}`,
    totalItems: params.inventoryItems.length,
  };
};
