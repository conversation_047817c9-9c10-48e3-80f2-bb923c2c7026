// 主机厂端采购管理Mock数据

import type {
  OemDashboard,
  OemPurchaseOrder,
  OemPurchaseOrderItem,
  OemOrderDetail,
  CarrierInfo,
  InventoryCheckResult,
  OrderStatistics,
  OemOrderListParams,
  OemApprovalParams,
  OemShipmentParams,
  BatchApprovalParams,
  BatchShipmentParams,
  PaginationResponse,
  ShipmentInfo,
  OrderStatusHistory
} from '@/types/parts/purchase-oem';

// 主机厂仪表盘数据
export const mockOemDashboard: OemDashboard = {
  pendingApprovalCount: 15,
  approvedTodayCount: 32,
  pendingShipmentCount: 8,
  last7DaysTotalAmount: 1250000.00
};

// 主机厂待处理订单列表
export const mockOemOrders: OemPurchaseOrder[] = [
  {
    orderId: 1002,
    orderNo: 'PO20250728002',
    dealerId: 2001,
    dealerName: 'A经销商-北京总店',
    dealerContact: '王经理',
    dealerPhone: '138-0000-0001',
    warehouseId: 3001,
    warehouseName: '主仓库',
    status: 'PENDING_APPROVAL',
    totalAmount: 8200.00,
    remarks: '部分配件急用，希望能优先处理，谢谢！',
    creatorId: 5001,
    creatorName: '王经理',
    createdAt: '2025-07-28 08:15:00',
    priority: 'HIGH'
  },
  {
    orderId: 1006,
    orderNo: 'PO20250728004',
    dealerId: 2002,
    dealerName: 'B经销商-上海分店',
    dealerContact: '李经理',
    dealerPhone: '138-0000-0002',
    warehouseId: 3003,
    warehouseName: '上海仓库',
    status: 'PENDING_APPROVAL',
    totalAmount: 15000.00,
    remarks: '常规补货申请',
    creatorId: 5002,
    creatorName: '李经理',
    createdAt: '2025-07-28 09:30:00',
    priority: 'NORMAL'
  },
  {
    orderId: 1007,
    orderNo: 'PO20250727003',
    dealerId: 2003,
    dealerName: 'C经销商-广州总店',
    dealerContact: '张经理',
    dealerPhone: '138-0000-0003',
    warehouseId: 3004,
    warehouseName: '广州仓库',
    status: 'PENDING_SHIPMENT',
    totalAmount: 3150.00,
    remarks: '紧急维修用配件',
    creatorId: 5003,
    creatorName: '张经理',
    createdAt: '2025-07-27 16:45:00',
    auditorId: 9001,
    auditorName: '陈经理',
    auditedAt: '2025-07-27 17:30:00',
    auditRemarks: '审核通过，优先发货',
    priority: 'URGENT'
  },
  {
    orderId: 1008,
    orderNo: 'PO20250727002',
    dealerId: 2004,
    dealerName: 'D经销商-深圳分店',
    dealerContact: '赵经理',
    dealerPhone: '138-0000-0004',
    warehouseId: 3005,
    warehouseName: '深圳仓库',
    status: 'PARTIALLY_SHIPPED',
    totalAmount: 5600.00,
    remarks: '月度常规补货',
    creatorId: 5004,
    creatorName: '赵经理',
    createdAt: '2025-07-27 10:20:00',
    auditorId: 9001,
    auditorName: '陈经理',
    auditedAt: '2025-07-27 11:15:00',
    auditRemarks: '审核通过',
    priority: 'NORMAL'
  },
  {
    orderId: 1009,
    orderNo: 'PO20250726003',
    dealerId: 2005,
    dealerName: 'E经销商-成都总店',
    dealerContact: '钱经理',
    dealerPhone: '138-0000-0005',
    warehouseId: 3006,
    warehouseName: '成都仓库',
    status: 'SHIPPED_ALL',
    totalAmount: 12800.00,
    remarks: '展厅配件补充',
    creatorId: 5005,
    creatorName: '钱经理',
    createdAt: '2025-07-26 14:30:00',
    auditorId: 9002,
    auditorName: '林经理',
    auditedAt: '2025-07-26 15:45:00',
    auditRemarks: '审核通过',
    priority: 'LOW'
  }
];

// 主机厂订单明细
export const mockOemOrderItems: Record<number, OemPurchaseOrderItem[]> = {
  1002: [
    {
      itemId: 2001,
      purchaseOrderId: 1002,
      partId: 'P10001',
      partCode: 'P10001',
      partName: '火花塞',
      brand: 'NGK',
      specification: 'BKR6E',
      unit: '个',
      purchasePrice: 45.00,
      orderQuantity: 100,
      shippedQuantity: 0,
      factoryStock: 5000,
      availableStock: 4800,
      status: 'SUFFICIENT'
    },
    {
      itemId: 2002,
      purchaseOrderId: 1002,
      partId: 'P10002',
      partCode: 'P10002',
      partName: '机油滤清器',
      brand: '博世',
      specification: 'F026407006',
      unit: '个',
      purchasePrice: 80.00,
      orderQuantity: 50,
      shippedQuantity: 0,
      factoryStock: 20,
      availableStock: 20,
      status: 'INSUFFICIENT'
    }
  ],
  1007: [
    {
      itemId: 2003,
      purchaseOrderId: 1007,
      partId: 'P10003',
      partCode: 'P10003',
      partName: '刹车片前',
      brand: '布雷博',
      specification: 'P85075',
      unit: '套',
      purchasePrice: 200.00,
      orderQuantity: 15,
      shippedQuantity: 0,
      factoryStock: 300,
      availableStock: 285,
      status: 'SUFFICIENT'
    },
    {
      itemId: 2004,
      purchaseOrderId: 1007,
      partId: 'P10008',
      partCode: 'P10008',
      partName: '雨刮片',
      brand: '博世',
      specification: 'A380H',
      unit: '对',
      purchasePrice: 85.00,
      orderQuantity: 10,
      shippedQuantity: 0,
      factoryStock: 450,
      availableStock: 440,
      status: 'SUFFICIENT'
    }
  ]
};

// 订单详情（完整信息）
export const mockOemOrderDetails: Record<number, OemOrderDetail> = {
  1002: {
    orderInfo: mockOemOrders[0],
    items: mockOemOrderItems[1002],
    statusHistory: [
      {
        id: 1,
        orderId: 1002,
        fromStatus: '',
        toStatus: 'DRAFT',
        operatorId: 5001,
        operatorName: '王经理',
        operatedAt: '2025-07-28 08:10:00',
        remarks: '创建订单'
      },
      {
        id: 2,
        orderId: 1002,
        fromStatus: 'DRAFT',
        toStatus: 'PENDING_APPROVAL',
        operatorId: 5001,
        operatorName: '王经理',
        operatedAt: '2025-07-28 08:15:00',
        remarks: '提交审核'
      }
    ]
  }
};

// 承运商信息
export const mockCarriers: CarrierInfo[] = [
  {
    carrierId: 'SF',
    carrierName: '顺丰速运',
    contactPhone: '************',
    serviceArea: ['全国']
  },
  {
    carrierId: 'YTO',
    carrierName: '圆通速递',
    contactPhone: '************',
    serviceArea: ['全国']
  },
  {
    carrierId: 'ZTO',
    carrierName: '中通快递',
    contactPhone: '************',
    serviceArea: ['全国']
  },
  {
    carrierId: 'STO',
    carrierName: '申通快递',
    contactPhone: '************',
    serviceArea: ['全国']
  },
  {
    carrierId: 'YD',
    carrierName: '韵达速递',
    contactPhone: '************',
    serviceArea: ['全国']
  },
  {
    carrierId: 'HTKY',
    carrierName: '百世快递',
    contactPhone: '************',
    serviceArea: ['全国']
  }
];

// 库存检查结果
export const mockInventoryResults: InventoryCheckResult[] = [
  {
    partId: 'P10001',
    partCode: 'P10001',
    partName: '火花塞',
    factoryStock: 5000,
    reservedStock: 200,
    availableStock: 4800,
    status: 'SUFFICIENT'
  },
  {
    partId: 'P10002',
    partCode: 'P10002',
    partName: '机油滤清器',
    factoryStock: 20,
    reservedStock: 0,
    availableStock: 20,
    status: 'INSUFFICIENT'
  },
  {
    partId: 'P10003',
    partCode: 'P10003',
    partName: '刹车片前',
    factoryStock: 300,
    reservedStock: 15,
    availableStock: 285,
    status: 'SUFFICIENT'
  },
  {
    partId: 'P10005',
    partCode: 'P10005',
    partName: '燃油滤清器',
    factoryStock: 0,
    reservedStock: 0,
    availableStock: 0,
    status: 'OUT_OF_STOCK'
  }
];

// 经销商列表
export const mockDealers = [
  {
    dealerId: 2001,
    dealerName: 'A经销商-北京总店',
    dealerCode: 'BJ001',
    regionName: '华北大区',
    contactPerson: '王经理',
    contactPhone: '138-0000-0001',
    status: 'ACTIVE'
  },
  {
    dealerId: 2002,
    dealerName: 'B经销商-上海分店',
    dealerCode: 'SH001',
    regionName: '华东大区',
    contactPerson: '李经理',
    contactPhone: '138-0000-0002',
    status: 'ACTIVE'
  },
  {
    dealerId: 2003,
    dealerName: 'C经销商-广州总店',
    dealerCode: 'GZ001',
    regionName: '华南大区',
    contactPerson: '张经理',
    contactPhone: '138-0000-0003',
    status: 'ACTIVE'
  },
  {
    dealerId: 2004,
    dealerName: 'D经销商-深圳分店',
    dealerCode: 'SZ001',
    regionName: '华南大区',
    contactPerson: '赵经理',
    contactPhone: '138-0000-0004',
    status: 'ACTIVE'
  },
  {
    dealerId: 2005,
    dealerName: 'E经销商-成都总店',
    dealerCode: 'CD001',
    regionName: '西南大区',
    contactPerson: '钱经理',
    contactPhone: '138-0000-0005',
    status: 'ACTIVE'
  }
];

// 统计数据
export const mockOrderStatistics: OrderStatistics = {
  totalOrders: 156,
  totalAmount: 2800000.00,
  averageAmount: 17948.72,
  byStatus: {
    'PENDING_APPROVAL': 15,
    'APPROVED': 32,
    'REJECTED': 5,
    'PENDING_SHIPMENT': 8,
    'PARTIALLY_SHIPPED': 12,
    'SHIPPED_ALL': 30,
    'CANCELLED': 2
  },
  byPriority: {
    'LOW': 45,
    'NORMAL': 85,
    'HIGH': 20,
    'URGENT': 6
  },
  trends: {
    daily: [
      { date: '2025-07-22', count: 8, amount: 145000 },
      { date: '2025-07-23', count: 12, amount: 198000 },
      { date: '2025-07-24', count: 15, amount: 267000 },
      { date: '2025-07-25', count: 18, amount: 324000 },
      { date: '2025-07-26', count: 22, amount: 389000 },
      { date: '2025-07-27', count: 16, amount: 285000 },
      { date: '2025-07-28', count: 19, amount: 342000 }
    ],
    weekly: [
      { date: '2025-W29', count: 65, amount: 1150000 },
      { date: '2025-W30', count: 91, amount: 1650000 }
    ],
    monthly: [
      { date: '2025-06', count: 234, amount: 4200000 },
      { date: '2025-07', count: 156, amount: 2800000 }
    ]
  }
};

// 热门配件数据
export const mockPopularParts = [
  {
    partId: 'P10001',
    partCode: 'P10001',
    partName: '火花塞',
    totalQuantity: 2500,
    totalAmount: 112500,
    orderCount: 45,
    dealerCount: 25
  },
  {
    partId: 'P10008',
    partCode: 'P10008',
    partName: '雨刮片',
    totalQuantity: 1800,
    totalAmount: 153000,
    orderCount: 38,
    dealerCount: 22
  },
  {
    partId: 'P10002',
    partCode: 'P10002',
    partName: '机油滤清器',
    totalQuantity: 1200,
    totalAmount: 96000,
    orderCount: 32,
    dealerCount: 18
  },
  {
    partId: 'P10003',
    partCode: 'P10003',
    partName: '刹车片前',
    totalQuantity: 450,
    totalAmount: 90000,
    orderCount: 28,
    dealerCount: 16
  },
  {
    partId: 'P10004',
    partCode: 'P10004',
    partName: '空气滤清器',
    totalQuantity: 800,
    totalAmount: 76000,
    orderCount: 24,
    dealerCount: 14
  }
];

// 经销商分析数据
export const mockDealerAnalysis = [
  {
    dealerId: 2001,
    dealerName: 'A经销商-北京总店',
    totalOrders: 28,
    totalAmount: 485000,
    averageAmount: 17321.43,
    approvalRate: 96.4
  },
  {
    dealerId: 2003,
    dealerName: 'C经销商-广州总店',
    totalOrders: 24,
    totalAmount: 425000,
    averageAmount: 17708.33,
    approvalRate: 95.8
  },
  {
    dealerId: 2002,
    dealerName: 'B经销商-上海分店',
    totalOrders: 22,
    totalAmount: 398000,
    averageAmount: 18090.91,
    approvalRate: 95.5
  },
  {
    dealerId: 2005,
    dealerName: 'E经销商-成都总店',
    totalOrders: 20,
    totalAmount: 345000,
    averageAmount: 17250.00,
    approvalRate: 95.0
  },
  {
    dealerId: 2004,
    dealerName: 'D经销商-深圳分店',
    totalOrders: 18,
    totalAmount: 298000,
    averageAmount: 16555.56,
    approvalRate: 94.4
  }
];

/**
 * Mock API 函数
 */

/**
 * 获取采购订单列表Mock数据
 */
export function getMockOemOrderList(params: OemOrderListParams): Promise<PaginationResponse<OemPurchaseOrder>> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredOrders = [...mockOemOrders]
      
      // 按状态筛选
      if (params.status && params.status.length > 0) {
        filteredOrders = filteredOrders.filter(order => params.status!.includes(order.status))
      }
      
      // 按经销商筛选
      if (params.dealerId) {
        filteredOrders = filteredOrders.filter(order => order.dealerId === params.dealerId)
      }
      
      // 按优先级筛选
      if (params.priority) {
        filteredOrders = filteredOrders.filter(order => order.priority === params.priority)
      }
      
      // 按关键词搜索
      if (params.query) {
        filteredOrders = filteredOrders.filter(order => 
          order.orderNo.toLowerCase().includes(params.query!.toLowerCase()) ||
          order.dealerName.toLowerCase().includes(params.query!.toLowerCase())
        )
      }
      
      // 分页
      const page = params.page || 1
      const size = params.size || 20
      const start = (page - 1) * size
      const end = start + size
      const paginatedOrders = filteredOrders.slice(start, end)
      
      resolve({
        data: paginatedOrders,
        pagination: {
          currentPage: page,
          pageSize: size,
          totalCount: filteredOrders.length,
          totalPages: Math.ceil(filteredOrders.length / size)
        }
      })
    }, 300)
  })
}

/**
 * 获取采购订单详情Mock数据
 */
export function getMockOemOrderDetail(orderId: number): Promise<OemOrderDetail> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const order = mockOemOrders.find(o => o.orderId === orderId)
      if (order) {
        const items = mockOemOrderItems[orderId] || []
        const statusHistory = mockOemOrderDetails[orderId]?.statusHistory || []
        
        resolve({
          orderInfo: order,
          items,
          statusHistory
        })
      } else {
        reject(new Error('订单不存在'))
      }
    }, 200)
  })
}

/**
 * 审批订单Mock
 */
export function getMockApproveOrder(params: OemApprovalParams): Promise<void> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const order = mockOemOrders.find(o => o.orderId === params.orderId)
      if (order) {
        if (params.isApproved) {
          order.status = 'PENDING_SHIPMENT'
        } else {
          order.status = 'REJECTED'
        }
        order.auditorId = params.auditorId
        order.auditorName = '审核员'
        order.auditedAt = new Date().toISOString().slice(0, 19).replace('T', ' ')
        order.auditRemarks = params.remarks || ''
        resolve()
      } else {
        reject(new Error('订单不存在'))
      }
    }, 500)
  })
}

/**
 * 发货处理Mock
 */
export function getMockShipOrder(params: OemShipmentParams): Promise<void> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const order = mockOemOrders.find(o => o.orderId === params.orderId)
      if (order) {
        // 检查是否全部发货
        const items = mockOemOrderItems[params.orderId] || []
        let allShipped = true
        
        params.items.forEach(shipItem => {
          const item = items.find(i => i.partId === shipItem.partId)
          if (item) {
            item.shippedQuantity += shipItem.shippedQuantity
            if (item.shippedQuantity < item.orderQuantity) {
              allShipped = false
            }
          }
        })
        
        order.status = allShipped ? 'SHIPPED_ALL' : 'PARTIALLY_SHIPPED'
        resolve()
      } else {
        reject(new Error('订单不存在'))
      }
    }, 500)
  })
}

/**
 * 批量审批Mock
 */
export function getMockBatchApprove(params: BatchApprovalParams): Promise<{
  successCount: number
  failCount: number
  failedOrders?: Array<{ orderId: number; reason: string }>
}> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let successCount = 0
      const failedOrders: Array<{ orderId: number; reason: string }> = []
      
      params.orderIds.forEach(orderId => {
        const order = mockOemOrders.find(o => o.orderId === orderId)
        if (order && order.status === 'PENDING_APPROVAL') {
          if (params.isApproved) {
            order.status = 'PENDING_SHIPMENT'
          } else {
            order.status = 'REJECTED'
          }
          order.auditorId = params.auditorId
          order.auditorName = '审核员'
          order.auditedAt = new Date().toISOString().slice(0, 19).replace('T', ' ')
          order.auditRemarks = params.remarks || ''
          successCount++
        } else {
          failedOrders.push({
            orderId,
            reason: order ? '订单状态不允许审批' : '订单不存在'
          })
        }
      })
      
      resolve({
        successCount,
        failCount: failedOrders.length,
        failedOrders: failedOrders.length > 0 ? failedOrders : undefined
      })
    }, 800)
  })
}

/**
 * 批量发货Mock
 */
export function getMockBatchShip(params: BatchShipmentParams): Promise<{
  successCount: number
  failCount: number
  failedOrders?: Array<{ orderId: number; reason: string }>
}> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let successCount = 0
      const failedOrders: Array<{ orderId: number; reason: string }> = []
      
      params.orderIds.forEach(orderId => {
        const order = mockOemOrders.find(o => o.orderId === orderId)
        if (order && (order.status === 'PENDING_SHIPMENT' || order.status === 'PARTIALLY_SHIPPED')) {
          order.status = 'SHIPPED_ALL'
          successCount++
        } else {
          failedOrders.push({
            orderId,
            reason: order ? '订单状态不允许发货' : '订单不存在'
          })
        }
      })
      
      resolve({
        successCount,
        failCount: failedOrders.length,
        failedOrders: failedOrders.length > 0 ? failedOrders : undefined
      })
    }, 800)
  })
}

/**
 * 获取统计信息Mock数据
 */
export function getMockOemStatistics(): Promise<OemDashboard> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockOemDashboard)
    }, 200)
  })
}

/**
 * 获取承运商列表Mock数据
 */
export function getMockCarriers(): Promise<CarrierInfo[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockCarriers)
    }, 100)
  })
}

// 订单备注
export const mockOrderNotes = [
  {
    noteId: 1,
    orderId: 1002,
    content: '客户反馈急需火花塞，建议优先处理',
    isInternal: false,
    createdBy: '王经理',
    createdAt: '2025-07-28 08:20:00'
  },
  {
    noteId: 2,
    orderId: 1002,
    content: '机油滤清器库存不足，需要协调其他仓库调货',
    isInternal: true,
    createdBy: '陈经理',
    createdAt: '2025-07-28 10:15:00'
  }
];