import type { 
  QualityCheckListItem, 
  QualityCheckDetail, 
  QualityCheckStatus,
  QualityCheckItemType,
  QualityCheckResult,
  WorkOrderType 
} from '@/types/module'

// 质检状态映射
const statusMap: Record<QualityCheckStatus, string> = {
  'pending_check': '待质检',
  'checking': '质检中', 
  'pending_review': '质检待审批',
  'passed': '质检通过',
  'rework': '返工'
}

// 工单类型映射
const workOrderTypeMap: Record<WorkOrderType, string> = {
  'maintenance': '保养',
  'repair': '维修',
  'insurance': '保险'
}

// 生成质检单列表Mock数据
export const generateQualityCheckList = (count: number = 20): QualityCheckListItem[] => {
  const statuses: QualityCheckStatus[] = ['pending_check', 'checking', 'pending_review', 'passed', 'rework']
  const workOrderTypes: WorkOrderType[] = ['maintenance', 'repair', 'insurance']
  const vehicleModels = ['Model S', 'Model X', 'Model 3', 'Model Y']
  const technicianNames = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅']
  const customerNames = ['张三', '李四', '王五', '赵六', '陈七', '刘八', '孙九', '周十']
  const plateNumbers = ['京A12345', '京B67890', '沪A11111', '粤A22222', '川A33333']

  return Array.from({ length: count }, (_, index) => {
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const workOrderType = workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)]
    const technicianName = technicianNames[Math.floor(Math.random() * technicianNames.length)]
    const customerName = customerNames[Math.floor(Math.random() * customerNames.length)]
    const plateNumber = plateNumbers[Math.floor(Math.random() * plateNumbers.length)]
    const vehicleModel = vehicleModels[Math.floor(Math.random() * vehicleModels.length)]
    
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    const estimatedHours = Math.floor(Math.random() * 8) + 1
    const actualHours = status === 'passed' || status === 'rework' ? Math.floor(Math.random() * 8) + 1 : undefined

    return {
      id: `qc-${index + 1}`,
      qualityCheckNo: `QC${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(index + 1).padStart(3, '0')}`,
      status,
      workOrderNo: `WO${String(index + 1).padStart(6, '0')}`,
      workOrderType,
      isClaimRelated: Math.random() > 0.7,
      isOutsourceRelated: Math.random() > 0.8,
      serviceCustomerName: customerName,
      serviceCustomerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      plateNumber,
      vehicleModel,
      vehicleConfig: '标准版',
      vehicleColor: ['白色', '黑色', '银色', '红色'][Math.floor(Math.random() * 4)],
      technicianName,
      startTime: status !== 'pending_check' ? createTime : undefined,
      finishTime: (status === 'passed' || status === 'rework') ? new Date(new Date(createTime).getTime() + estimatedHours * 60 * 60 * 1000).toISOString() : undefined,
      estimatedHours,
      actualHours,
      reworkReason: status === 'rework' ? '质检不合格，需要重新处理' : undefined,
      reworkRequirement: status === 'rework' ? '请检查制动系统相关项目' : undefined,
      createTime,
      updateTime: createTime
    }
  })
}

// 生成质检详情Mock数据
export const generateQualityCheckDetail = (id: string): QualityCheckDetail => {
  const mockListItem = generateQualityCheckList(1)[0]
  mockListItem.id = id

  // 质检项目数据
  const checkItems = [
    // 制动系统检查
    {
      id: 'item-1',
      qualityCheckId: id,
      categoryCode: 'BRAKE_SYSTEM',
      categoryName: '制动系统检查',
      itemCode: 'BRAKE_PEDAL_TRAVEL',
      itemName: '制动踏板行程',
      itemType: 'NUMERIC' as QualityCheckItemType,
      numericValue: 120,
      standardValue: '≤150mm',
      unit: 'mm',
      isRequired: true,
      sortOrder: 1,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    {
      id: 'item-2',
      qualityCheckId: id,
      categoryCode: 'BRAKE_SYSTEM',
      categoryName: '制动系统检查',
      itemCode: 'BRAKE_FLUID_LEVEL',
      itemName: '制动液液位',
      itemType: 'BOOLEAN' as QualityCheckItemType,
      checkResult: 'PASS' as QualityCheckResult,
      standardValue: '在MIN-MAX之间',
      isRequired: true,
      sortOrder: 2,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    // 转向系统检查
    {
      id: 'item-3',
      qualityCheckId: id,
      categoryCode: 'STEERING_SYSTEM',
      categoryName: '转向系统检查',
      itemCode: 'STEERING_WHEEL_FREE_PLAY',
      itemName: '方向盘自由转动量',
      itemType: 'NUMERIC' as QualityCheckItemType,
      numericValue: 8,
      standardValue: '≤15°',
      unit: '°',
      isRequired: true,
      sortOrder: 3,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    // 轮胎检查
    {
      id: 'item-4',
      qualityCheckId: id,
      categoryCode: 'TIRE_INSPECTION',
      categoryName: '轮胎检查',
      itemCode: 'TIRE_PRESSURE',
      itemName: '轮胎气压',
      itemType: 'NUMERIC' as QualityCheckItemType,
      numericValue: 2.3,
      standardValue: '2.2-2.5bar',
      unit: 'bar',
      isRequired: true,
      sortOrder: 4,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    {
      id: 'item-5',
      qualityCheckId: id,
      categoryCode: 'TIRE_INSPECTION',
      categoryName: '轮胎检查',
      itemCode: 'TIRE_WEAR',
      itemName: '轮胎磨损情况',
      itemType: 'TEXT' as QualityCheckItemType,
      textValue: '正常磨损，无异常',
      standardValue: '无异常磨损',
      isRequired: true,
      sortOrder: 5,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
  ]

  // 工时详情数据
  const laborHourDetails = [
    {
      id: 'labor-1',
      workOrderNo: mockListItem.workOrderNo,
      laborCode: 'L001',
      laborName: '制动系统检查',
      laborType: 'maintenance' as 'maintenance' | 'repair' | 'claim',
      isOutsource: false,
      isAdditional: false,
      standardHours: 1.5,
      actualHours: 1.8,
      unitPrice: 80
    },
    {
      id: 'labor-2',
      workOrderNo: mockListItem.workOrderNo,
      laborCode: 'L002',
      laborName: '转向系统检查',
      laborType: 'maintenance' as 'maintenance' | 'repair' | 'claim',
      isOutsource: false,
      isAdditional: false,
      standardHours: 1.0,
      actualHours: 1.2,
      unitPrice: 80
    }
  ]

  // 零件详情数据
  const partsDetails = [
    {
      id: 'part-1',
      workOrderNo: mockListItem.workOrderNo,
      partCode: 'P001',
      partName: '制动液',
      partType: 'maintenance' as 'maintenance' | 'repair' | 'claim',
      isAdditional: false,
      quantity: 1,
      unitPrice: 150,
      totalAmount: 150
    },
    {
      id: 'part-2',
      workOrderNo: mockListItem.workOrderNo,
      partCode: 'P002',
      partName: '机油滤清器',
      partType: 'maintenance' as 'maintenance' | 'repair' | 'claim',
      isAdditional: false,
      quantity: 1,
      unitPrice: 80,
      totalAmount: 80
    }
  ]

  // 操作日志数据
  const operationLogs = [
    {
      id: 'log-1',
      qualityCheckId: id,
      qualityCheckNo: mockListItem.qualityCheckNo,
      operationType: 'edit' as 'edit' | 'submit_review' | 'approve_pass' | 'rework',
      operatorId: 'tech-001',
      operatorName: mockListItem.technicianName,
      operatorRole: 'technician' as 'technician' | 'technician_manager' | 'system',
      operationContent: '开始质检作业',
      beforeStatus: 'pending_check',
      afterStatus: 'checking',
      operationTime: mockListItem.createTime,
      ipAddress: '*************'
    }
  ]

  return {
    qualityCheck: {
      ...mockListItem,
      id,
      qualityCheckNo: mockListItem.qualityCheckNo,
      workOrderNo: mockListItem.workOrderNo,
      workOrderType: mockListItem.workOrderType,
      status: mockListItem.status,
      technicianId: 'tech-001',
      technicianName: mockListItem.technicianName,
      storeId: 'store-001',
      storeName: '北京体验中心',
      isClaimRelated: mockListItem.isClaimRelated,
      isOutsourceRelated: mockListItem.isOutsourceRelated,
      startTime: mockListItem.startTime,
      finishTime: mockListItem.finishTime,
      estimatedHours: mockListItem.estimatedHours,
      actualHours: mockListItem.actualHours,
      submitTime: mockListItem.status !== 'pending_check' && mockListItem.status !== 'checking' ? mockListItem.createTime : undefined,
      auditTime: mockListItem.status === 'passed' || mockListItem.status === 'rework' ? mockListItem.updateTime : undefined,
      auditResult: mockListItem.status === 'passed' ? 'passed' : mockListItem.status === 'rework' ? 'rework' : undefined,
      auditorId: mockListItem.status === 'passed' || mockListItem.status === 'rework' ? 'manager-001' : undefined,
      auditorName: mockListItem.status === 'passed' || mockListItem.status === 'rework' ? '李经理' : undefined,
      reworkReason: mockListItem.reworkReason,
      reworkRequirement: mockListItem.reworkRequirement,
      auditRemark: mockListItem.status === 'passed' ? '质检通过，符合标准' : undefined,
      createTime: mockListItem.createTime,
      updateTime: mockListItem.updateTime
    },
    customerVehicleInfo: {
      serviceCustomerName: mockListItem.serviceCustomerName,
      serviceCustomerPhone: mockListItem.serviceCustomerPhone,
      plateNumber: mockListItem.plateNumber,
      vin: `LRWXXXXXXXS${String(Math.floor(Math.random() * 100000)).padStart(6, '0')}`,
      vehicleModel: mockListItem.vehicleModel,
      vehicleConfig: mockListItem.vehicleConfig,
      vehicleColor: mockListItem.vehicleColor,
      mileage: Math.floor(Math.random() * 100000),
      vehicleAge: Math.floor(Math.random() * 60) + 1
    },
    laborHourDetails,
    partsDetails,
    checkItems,
    operationLogs
  }
}

// 质检项目模板数据
export const getQualityCheckTemplateData = (workOrderType: WorkOrderType) => {
  const baseItems = [
    // 制动系统
    {
      categoryCode: 'BRAKE_SYSTEM',
      categoryName: '制动系统检查',
      items: [
        {
          itemCode: 'BRAKE_PEDAL_TRAVEL',
          itemName: '制动踏板行程',
          itemType: 'NUMERIC' as QualityCheckItemType,
          standardValue: '≤150mm',
          unit: 'mm',
          isRequired: true
        },
        {
          itemCode: 'BRAKE_FLUID_LEVEL',
          itemName: '制动液液位',
          itemType: 'BOOLEAN' as QualityCheckItemType,
          standardValue: '在MIN-MAX之间',
          isRequired: true
        },
        {
          itemCode: 'BRAKE_DISC_THICKNESS',
          itemName: '制动盘厚度',
          itemType: 'NUMERIC' as QualityCheckItemType,
          standardValue: '≥20mm',
          unit: 'mm',
          isRequired: true
        }
      ]
    },
    // 转向系统
    {
      categoryCode: 'STEERING_SYSTEM',
      categoryName: '转向系统检查',
      items: [
        {
          itemCode: 'STEERING_WHEEL_FREE_PLAY',
          itemName: '方向盘自由转动量',
          itemType: 'NUMERIC' as QualityCheckItemType,
          standardValue: '≤15°',
          unit: '°',
          isRequired: true
        },
        {
          itemCode: 'STEERING_FLUID_LEVEL',
          itemName: '转向助力液液位',
          itemType: 'BOOLEAN' as QualityCheckItemType,
          standardValue: '正常',
          isRequired: true
        }
      ]
    },
    // 轮胎检查
    {
      categoryCode: 'TIRE_INSPECTION',
      categoryName: '轮胎检查',
      items: [
        {
          itemCode: 'TIRE_PRESSURE',
          itemName: '轮胎气压',
          itemType: 'NUMERIC' as QualityCheckItemType,
          standardValue: '2.2-2.5bar',
          unit: 'bar',
          isRequired: true
        },
        {
          itemCode: 'TIRE_WEAR',
          itemName: '轮胎磨损情况',
          itemType: 'TEXT' as QualityCheckItemType,
          standardValue: '无异常磨损',
          isRequired: true
        }
      ]
    }
  ]

  // 根据工单类型添加特定检查项目
  if (workOrderType === 'maintenance') {
    baseItems.push({
      categoryCode: 'ENGINE_PERFORMANCE',
      categoryName: '发动机性能检查',
      items: [
        {
          itemCode: 'ENGINE_OIL_LEVEL',
          itemName: '机油液位',
          itemType: 'BOOLEAN' as QualityCheckItemType,
          standardValue: '正常',
          isRequired: true
        },
        {
          itemCode: 'ENGINE_IDLE_SPEED',
          itemName: '怠速转速',
          itemType: 'NUMERIC' as QualityCheckItemType,
          standardValue: '750-850rpm',
          unit: 'rpm',
          isRequired: true
        }
      ]
    })
  }

  if (workOrderType === 'repair') {
    baseItems.push({
      categoryCode: 'REPAIR_QUALITY',
      categoryName: '维修质量检查',
      items: [
        {
          itemCode: 'REPAIR_COMPLETION',
          itemName: '维修完成度',
          itemType: 'BOOLEAN' as QualityCheckItemType,
          standardValue: '完全修复',
          isRequired: true
        },
        {
          itemCode: 'REPAIR_APPEARANCE',
          itemName: '维修外观',
          itemType: 'TEXT' as QualityCheckItemType,
          standardValue: '外观整洁，无损伤',
          isRequired: true
        }
      ]
    })
  }

  return baseItems
} 