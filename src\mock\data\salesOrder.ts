import type {
  SalesOrderListItem,
  SalesOrderDetail,
  AvailableRight
} from '@/types/module.d'

// 销售订单列表Mock数据
export const mockSalesOrderList: SalesOrderListItem[] = [
  {
    id: '1',
    orderNo: 'KL2023120401',
    createTime: '2023-12-04 10:30:00',
    ordererName: '李销售',
    ordererPhone: '012-3456789',
    buyerName: '张三',
    buyerPhone: '138****1234',
    customerType: 'individual',
    model: 'MYVI',
    variant: '1.5 Premium X',
    color: '白色',
    vin: 'LVSGCQ****0001',
    paymentMethod: 'installment',
    totalAmount: 68098.00,
    loanApprovalStatus: 'approved',
    orderStatus: 'confirmed',
    approvalStatus: 'approved',
    paymentStatus: 'deposit_paid',
    insuranceStatus: 'insured',
    jpjRegistrationStatus: 'registered'
  },
  {
    id: '2',
    orderNo: 'KL2023120402',
    createTime: '2023-12-04 14:15:00',
    ordererName: '李销售',
    ordererPhone: '012-3456789',
    buyerName: '王丽',
    buyerPhone: '135****5678',
    customerType: 'individual',
    model: 'AXIA',
    variant: '1.0 Standard G',
    color: '红色',
    vin: 'LVSGCQ****0002',
    paymentMethod: 'full_payment',
    totalAmount: 42598.00,
    orderStatus: 'pending_delivery',
    approvalStatus: 'approved',
    paymentStatus: 'balance_paid',
    insuranceStatus: 'insured',
    jpjRegistrationStatus: 'registered'
  },
  {
    id: '3',
    orderNo: 'KL2023120403',
    createTime: '2023-12-04 16:45:00',
    ordererName: '赵顾问',
    ordererPhone: '012-7890123',
    buyerName: '刘明',
    buyerPhone: '136****9012',
    customerType: 'company',
    model: 'BEZZA',
    variant: '1.3 Premium X',
    color: '蓝色',
    vin: 'LVSGCQ****0003',
    paymentMethod: 'installment',
    totalAmount: 55789.00,
    loanApprovalStatus: 'pending_review',
    orderStatus: 'submitted',
    approvalStatus: 'pending_approval',
    paymentStatus: 'deposit_paid',
    insuranceStatus: 'not_insured',
    jpjRegistrationStatus: 'pending_registration'
  },
  {
    id: '4',
    orderNo: 'KL2023120404',
    createTime: '2023-12-03 09:20:00',
    ordererName: '陈销售',
    ordererPhone: '012-4567890',
    buyerName: '周雅',
    buyerPhone: '137****3456',
    customerType: 'individual',
    model: 'MYVI',
    variant: '1.3 Standard G',
    color: '银色',
    vin: 'LVSGCQ****0004',
    paymentMethod: 'full_payment',
    totalAmount: 61234.00,
    orderStatus: 'delivered',
    approvalStatus: 'approved',
    paymentStatus: 'balance_paid',
    insuranceStatus: 'insured',
    jpjRegistrationStatus: 'registered'
  },
  {
    id: '5',
    orderNo: 'KL2023120405',
    createTime: '2023-12-02 11:10:00',
    ordererName: '孙顾问',
    ordererPhone: '012-5678901',
    buyerName: '吴强',
    buyerPhone: '138****7890',
    customerType: 'individual',
    model: 'AXIA',
    variant: '1.0 Advance',
    color: '黑色',
    vin: '',
    paymentMethod: 'installment',
    totalAmount: 46890.00,
    loanApprovalStatus: 'rejected',
    orderStatus: 'cancel_pending',
    approvalStatus: 'pending_approval',
    paymentStatus: 'deposit_paid',
    insuranceStatus: 'not_insured',
    jpjRegistrationStatus: 'pending_registration'
  }
]

// 销售订单详情Mock数据
export const mockSalesOrderDetail: SalesOrderDetail = {
  // 订单基本信息
  id: '1',
  orderNo: 'KL2023120401',
  createTime: '2023-12-04 10:30:00',

  // 客户信息
  ordererName: '李销售',
  ordererPhone: '012-3456789',
  buyerName: '张三',
  buyerPhone: '138****1234',
  buyerIdType: 'ID',
  buyerIdNumber: '440301********123X',
  buyerEmail: 'user***@example.com',
  buyerAddress: '广东省深圳市南山区***',
  buyerState: '广东省',
  buyerCity: '深圳市',
  buyerPostcode: '518000',
  customerType: 'individual',

  // 门店信息
  storeRegion: '华南地区',
  storeCity: '吉隆坡',
  storeName: '吉隆坡门店',
  salesConsultantName: '李销售',

  // 车辆信息
  model: 'MYVI',
  variant: '1.5 Premium X',
  color: '白色',
  salesSubtotal: 65000.00,
  numberPlatesFee: 98.00,
  vin: 'LVSGCQ****0001',

  // 选配件信息
  accessories: [
    {
      id: 'acc001',
      category: '电子设备',
      accessoryName: '倒车雷达',
      unitPrice: 500.00,
      quantity: 1,
      totalPrice: 500.00
    },
    {
      id: 'acc002',
      category: '外观配件',
      accessoryName: '车身贴膜',
      unitPrice: 800.00,
      quantity: 1,
      totalPrice: 800.00
    }
  ],
  accessoriesTotalAmount: 1300.00,

  // 开票信息
  invoicingType: '个人',
  invoicingName: '张三',
  invoicingPhone: '138****1234',
  invoicingAddress: '广东省深圳市南山区***',

  // 权益信息
  rights: [
    {
      id: 'right001',
      rightCode: 'FIRST_BUY',
      rightName: '首次购车优惠',
      rightMode: '折扣',
      discountAmount: 2000.00,
      effectiveDate: '2023-12-01',
      expiryDate: '2024-12-01'
    }
  ],
  rightsDiscountAmount: 2000.00,

  // 支付信息
  paymentMethod: 'installment',
  loanApprovalStatus: 'approved',
  depositAmount: 1000.00,
  loanAmount: 50000.00,
  balanceAmount: 17098.00,

  // 保险信息
  insuranceList: [
    {
      id: 'ins001',
      policyNumber: 'POL2023120401',
      insuranceType: '全险',
      insuranceCompany: '大众保险',
      effectiveDate: '2023-12-04',
      expiryDate: '2024-12-04',
      insurancePrice: 3500.00
    }
  ],
  insuranceTotalAmount: 3500.00,
  insuranceNotes: '已投保完成',

  // OTR费用信息
  otrFees: [
    {
      id: 'otr001',
      ticketNumber: 'TKT2023120401001',
      feeItem: '路税',
      feePrice: 90.00,
      effectiveDate: '2023-12-04',
      expiryDate: '2024-12-04'
    },
    {
      id: 'otr002',
      ticketNumber: 'TKT2023120401002',
      feeItem: '注册/过户费',
      feePrice: 150.00,
      effectiveDate: '2023-12-04',
      expiryDate: '2024-12-04'
    },
    {
      id: 'otr003',
      ticketNumber: 'TKT2023120401003',
      feeItem: '所有权索赔费',
      feePrice: 50.00,
      effectiveDate: '2023-12-04',
      expiryDate: '2024-12-04'
    },
    {
      id: 'otr004',
      ticketNumber: 'TKT2023120401004',
      feeItem: '咨询费',
      feePrice: 30.00,
      effectiveDate: '2023-12-04',
      expiryDate: '2024-12-04'
    }
  ],
  otrFeesTotalAmount: 320.00,

  // 订单变更记录
  changeRecords: [
    {
      id: 'change001',
      originalContent: '车辆颜色：红色',
      changedContent: '车辆颜色：白色',
      operator: '李销售',
      operationTime: '2023-12-04 11:15:00'
    }
  ],

  // 状态信息
  orderStatus: 'confirmed',
  approvalStatus: 'approved',
  paymentStatus: 'deposit_paid',
  insuranceStatus: 'insured',
  jpjRegistrationStatus: 'registered',

  // 计算字段
  totalInvoiceAmount: 68098.00,
  remainingAmount: 17098.00
}

// 可选权益Mock数据
export const mockAvailableRights: AvailableRight[] = [
  {
    id: 'right001',
    rightCode: 'FIRST_BUY',
    rightName: '首次购车优惠',
    rightMode: '折扣',
    discountAmount: 2000.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  },
  {
    id: 'right002',
    rightCode: 'LOYALTY',
    rightName: '忠诚客户优惠',
    rightMode: '现金减免',
    discountAmount: 1500.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  },
  {
    id: 'right003',
    rightCode: 'TRADE_IN',
    rightName: '置换补贴',
    rightMode: '折扣',
    discountAmount: 3000.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  },
  {
    id: 'right004',
    rightCode: 'CASH_DISCOUNT',
    rightName: '现金优惠',
    rightMode: '现金减免',
    discountAmount: 1000.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  },
  {
    id: 'right005',
    rightCode: 'INSURANCE_DISCOUNT',
    rightName: '保险优惠',
    rightMode: '折扣',
    discountAmount: 800.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  },
  {
    id: 'right006',
    rightCode: 'VIP_MEMBER',
    rightName: 'VIP会员专享',
    rightMode: '现金减免',
    discountAmount: 2500.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  },
  {
    id: 'right007',
    rightCode: 'FESTIVAL',
    rightName: '节日促销',
    rightMode: '折扣',
    discountAmount: 1200.00,
    effectiveDate: '2023-12-01',
    expiryDate: '2024-01-31'
  },
  {
    id: 'right008',
    rightCode: 'REFERRAL',
    rightName: '推荐奖励',
    rightMode: '现金减免',
    discountAmount: 800.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  },
  {
    id: 'right009',
    rightCode: 'STUDENT',
    rightName: '学生优惠',
    rightMode: '折扣',
    discountAmount: 1500.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  },
  {
    id: 'right010',
    rightCode: 'ELDERLY',
    rightName: '长者优惠',
    rightMode: '现金减免',
    discountAmount: 1000.00,
    effectiveDate: '2023-01-01',
    expiryDate: '2024-12-31'
  }
]
