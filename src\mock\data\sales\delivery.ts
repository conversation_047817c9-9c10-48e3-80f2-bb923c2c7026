// 交付管理Mock数据模块
// 支持标准MyBatisPlus分页和搜索功能

import type {
  DeliverySearchParams,
  DeliveryPageResponse,
  DeliveryListItem
} from '@/types/sales/delivery';

// 动态生成Mock数据（25-30条，便于测试分页）
function generateMockDeliveryData(): DeliveryListItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: DeliveryListItem[] = [];

  const orderStatuses = ['normal', 'pending_delivery', 'delivered', 'cancelled'];
  const deliveryStatuses = ['pending_delivery', 'pending_confirm', 'delivered'];
  const confirmationTypes = ['app', 'offline'];
  const dealerStores = ['store_001', 'store_002', 'store_003', 'store_004'];
  const models = ['Myvi', 'Axia', 'Bezza', 'Aruz', 'Alza'];
  const variants = ['Standard', 'Premium', 'Advance'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];

  for (let i = 0; i < dataCount; i++) {
    const currentDate = new Date();
    const randomDaysAgo = Math.floor(Math.random() * 90);
    const baseDate = new Date(currentDate.getTime() - randomDaysAgo * 24 * 60 * 60 * 1000);
    
    mockData.push({
      deliveryId: `delivery_${String(i + 1).padStart(3, '0')}`,
      deliveryNumber: `DEL${currentDate.getFullYear()}${String(currentDate.getMonth() + 1).padStart(2, '0')}${String(i + 1).padStart(4, '0')}`,
      orderId: `order_${String(i + 1).padStart(3, '0')}`,
      orderNumber: `SO${currentDate.getFullYear()}${String(i + 1).padStart(6, '0')}`,
      customerName: `客户${String(i + 1).padStart(2, '0')}`,
      customerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      vin: `VIN${String(i + 1).padStart(14, '0')}`,
      model: models[Math.floor(Math.random() * models.length)],
      variant: variants[Math.floor(Math.random() * variants.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      dealerStore: dealerStores[Math.floor(Math.random() * dealerStores.length)],
      salesConsultant: `顾问${String(i + 1).padStart(2, '0')}`,
      orderStatus: orderStatuses[Math.floor(Math.random() * orderStatuses.length)],
      deliveryStatus: deliveryStatuses[Math.floor(Math.random() * deliveryStatuses.length)],
      customerConfirmed: Math.random() > 0.3,
      confirmationType: Math.random() > 0.5 ? confirmationTypes[Math.floor(Math.random() * confirmationTypes.length)] : undefined,
      invoiceTime: new Date(baseDate.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      deliveryTime: Math.random() > 0.4 ? new Date(baseDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
      customerConfirmTime: Math.random() > 0.5 ? new Date(baseDate.getTime() + Math.random() * 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
      deliveryNotes: Math.random() > 0.6 ? `交付备注${i + 1}` : undefined,
      signaturePhoto: Math.random() > 0.7 ? `https://example.com/signature${i + 1}.jpg` : undefined,
      createTime: baseDate.toISOString(),
      updateTime: new Date(baseDate.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString(),
      creator: `user_${Math.floor(Math.random() * 10) + 1}`,
      updater: `user_${Math.floor(Math.random() * 10) + 1}`
    });
  }

  return mockData;
}

const mockData = generateMockDeliveryData();

// 标准MyBatisPlus分页Mock接口
export const getDeliveryListMock = (
  params: DeliverySearchParams
): Promise<DeliveryPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      // 交付单号搜索
      if (params.deliveryNumber) {
        filteredData = filteredData.filter(item =>
          item.deliveryNumber.toLowerCase().includes(params.deliveryNumber!.toLowerCase())
        );
      }

      // 订单编号搜索
      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNumber.toLowerCase().includes(params.orderNumber!.toLowerCase())
        );
      }

      // 客户姓名搜索
      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.includes(params.customerName!)
        );
      }

      // 客户电话搜索
      if (params.customerPhone) {
        filteredData = filteredData.filter(item =>
          item.customerPhone.includes(params.customerPhone!)
        );
      }

      // VIN搜索
      if (params.vin) {
        filteredData = filteredData.filter(item =>
          item.vin.toLowerCase().includes(params.vin!.toLowerCase())
        );
      }

      // 订单状态筛选
      if (params.orderStatus) {
        filteredData = filteredData.filter(item =>
          item.orderStatus === params.orderStatus
        );
      }

      // 交付状态筛选
      if (params.deliveryStatus) {
        filteredData = filteredData.filter(item =>
          item.deliveryStatus === params.deliveryStatus
        );
      }

      // 经销商门店筛选
      if (params.dealerStore) {
        filteredData = filteredData.filter(item =>
          item.dealerStore === params.dealerStore
        );
      }

      // 客户确认筛选
      if (params.customerConfirmed !== undefined) {
        filteredData = filteredData.filter(item =>
          item.customerConfirmed === params.customerConfirmed
        );
      }

      // 确认方式筛选
      if (params.confirmationType) {
        filteredData = filteredData.filter(item =>
          item.confirmationType === params.confirmationType
        );
      }

      // 交付时间范围筛选
      if (params.deliveryTimeRange && params.deliveryTimeRange.length === 2) {
        const [startDate, endDate] = params.deliveryTimeRange;
        filteredData = filteredData.filter(item => {
          if (!item.deliveryTime) return false;
          return item.deliveryTime >= startDate && item.deliveryTime <= endDate;
        });
      }

      // 客户确认时间范围筛选
      if (params.customerConfirmTimeRange && params.customerConfirmTimeRange.length === 2) {
        const [startDate, endDate] = params.customerConfirmTimeRange;
        filteredData = filteredData.filter(item => {
          if (!item.customerConfirmTime) return false;
          return item.customerConfirmTime >= startDate && item.customerConfirmTime <= endDate;
        });
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        records: filteredData.slice(start, end),
        total,
        pageNum,
        pageSize,
        pages
      });
    }, 500);
  });
};

// 获取交付详情Mock接口
export const getDeliveryDetailMock = (deliveryNumber: string): Promise<DeliveryListItem | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.deliveryNumber === deliveryNumber);
      resolve(item || null);
    }, 300);
  });
};
