import type {
  FactoryOrderSearchParams,
  FactoryOrderPageResponse,
  FactoryOrderDetail,
  FactoryOrderStatistics,
  ExportFactoryOrderParams,
  ExportFactoryOrderResponse
} from '@/types/sales/factoryOrderManagement';

// Mock数据生成
const generateMockFactoryOrders = (count: number) => {
  const orders = [];
  const dealers = ['01010001', '01010002', '01010003', '01010004']; // 门店编码
  const models = ['01020001', '01020002', '01020003']; // 车型编码
  const variants = ['01030001', '01030002', '01030003']; // 配置编码
  const orderStatuses = ['01040001', '01040002', '01040003', '01040004']; // 订单状态编码
  const approvalStatuses = ['01050001', '01050002', '01050003']; // 审批状态编码
  const paymentStatuses = ['01060001', '01060002', '01060003', '01060004']; // 支付状态编码
  const loanStatuses = ['01070001', '01070002', '01070003']; // 贷款状态编码
  const insuranceStatuses = ['01080001', '01080002', '01080003']; // 保险状态编码
  const jpjStatuses = ['01090001', '01090002', '01090003']; // JPJ注册状态编码

  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];
  const paymentMethods = ['现金', '银行转账', '信用卡', '分期付款'];
  const customerTypes = ['个人', '企业'];

  for (let i = 1; i <= count; i++) {
    const totalAmount = Math.floor(Math.random() * 100000) + 50000;
    const paidAmount = Math.floor(Math.random() * totalAmount);
    const remainingAmount = totalAmount - paidAmount;

    orders.push({
      id: `FO${String(i).padStart(6, '0')}`,
      orderNo: `FO${String(i).padStart(6, '0')}`,
      storeName: dealers[Math.floor(Math.random() * dealers.length)],
      model: models[Math.floor(Math.random() * models.length)],
      variant: variants[Math.floor(Math.random() * variants.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      orderStatus: orderStatuses[Math.floor(Math.random() * orderStatuses.length)],
      approvalStatus: approvalStatuses[Math.floor(Math.random() * approvalStatuses.length)],
      paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
      loanStatus: loanStatuses[Math.floor(Math.random() * loanStatuses.length)],
      insuranceStatus: insuranceStatuses[Math.floor(Math.random() * insuranceStatuses.length)],
      jpjRegistrationStatus: jpjStatuses[Math.floor(Math.random() * jpjStatuses.length)],
      orderDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      deliveryDate: Math.random() > 0.5 ? new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
      totalAmount,
      paidAmount,
      remainingAmount,
      customerName: `客户${i}`,
      customerPhone: `+60${Math.floor(Math.random() * 1000000000)}`,
      customerType: customerTypes[Math.floor(Math.random() * customerTypes.length)],
      salesConsultant: `顾问${Math.floor(Math.random() * 10) + 1}`,
      remarks: Math.random() > 0.7 ? `备注信息${i}` : undefined,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updateTime: new Date().toISOString(),
      vin: `VIN${String(i).padStart(10, '0')}`,
      paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)]
    });
  }
  return orders;
};

const mockOrders = generateMockFactoryOrders(100);

// 获取工厂订单列表
export const getFactoryOrderList = (params: FactoryOrderSearchParams): Promise<FactoryOrderPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockOrders];

      // 搜索过滤
      if (params.dealerName) {
        filteredData = filteredData.filter(item => item.storeName === params.dealerName);
      }
      if (params.model) {
        filteredData = filteredData.filter(item => item.model === params.model);
      }
      if (params.variant) {
        filteredData = filteredData.filter(item => item.variant === params.variant);
      }
      if (params.orderStatus) {
        filteredData = filteredData.filter(item => item.orderStatus === params.orderStatus);
      }
      if (params.approvalStatus) {
        filteredData = filteredData.filter(item => item.approvalStatus === params.approvalStatus);
      }
      if (params.paymentStatus) {
        filteredData = filteredData.filter(item => item.paymentStatus === params.paymentStatus);
      }
      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNo.toLowerCase().includes(params.orderNumber!.toLowerCase())
        );
      }
      if (params.orderDateStart && params.orderDateEnd) {
        filteredData = filteredData.filter(item => {
          const orderDate = new Date(item.orderDate);
          return orderDate >= new Date(params.orderDateStart!) && orderDate <= new Date(params.orderDateEnd!);
        });
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 10;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        }
      });
    }, 500);
  });
};

// 获取工厂订单详情
export const getFactoryOrderDetail = (orderNumber: string): Promise<{ result: FactoryOrderDetail }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const order = mockOrders.find(item => item.orderNo === orderNumber);
      if (order) {
        resolve({
          result: {
            ...order,
            customerInfo: {
              name: order.customerName,
              phone: order.customerPhone,
              email: `${order.customerName.toLowerCase()}@example.com`,
              address: '马来西亚吉隆坡',
              icNumber: '123456-78-9012',
              customerType: order.customerType
            },
            vehicleInfo: {
              model: order.model,
              variant: order.variant,
              color: order.color,
              vin: order.vin,
              engineNumber: `ENG${order.id}`,
              chassisNumber: `CHS${order.id}`
            },
            paymentInfo: {
              totalAmount: order.totalAmount,
              paidAmount: order.paidAmount,
              remainingAmount: order.remainingAmount,
              paymentMethod: order.paymentMethod || '银行转账',
              paymentRecords: []
            },
            loanInfo: {
              loanAmount: Math.floor(order.totalAmount * 0.8),
              loanBank: '马来亚银行',
              loanStatus: order.loanStatus,
              approvalDate: order.createTime
            },
            insuranceInfo: {
              insuranceCompany: '马来西亚保险公司',
              insuranceType: '全险',
              insuranceAmount: Math.floor(order.totalAmount * 0.1),
              insuranceStatus: order.insuranceStatus
            },
            jpjInfo: {
              registrationNumber: `JPJ${order.id}`,
              registrationStatus: order.jpjRegistrationStatus,
              registrationDate: order.deliveryDate
            }
          } as FactoryOrderDetail
        });
      }
    }, 300);
  });
};

// 获取工厂订单统计
export const getFactoryOrderStatistics = (): Promise<{ result: FactoryOrderStatistics }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        result: {
          monthlyOrderCount: mockOrders.length,
          dailyOrderCount: Math.floor(Math.random() * 20) + 5,
          monthlyGrowthRate: Math.floor(Math.random() * 20) + 5,
          dailyGrowthRate: Math.floor(Math.random() * 10) + 2,
          topStoreName: '吉隆坡中央店',
          topStoreOrderCount: Math.floor(Math.random() * 50) + 20,
          topVehicleModel: 'Model A',
          topVehicleOrderCount: Math.floor(Math.random() * 30) + 15,
          pendingDeliveryCount: mockOrders.filter(o => o.orderStatus === '01040002').length,
          lastUpdateTime: new Date().toISOString(),
          topDealers: [
            { name: '吉隆坡中央店', count: 45 },
            { name: '槟城旗舰店', count: 38 },
            { name: '新山分店', count: 32 }
          ],
          topVehicles: [
            { model: 'Model A', count: 28 },
            { model: 'Model B', count: 22 },
            { model: 'Model C', count: 18 }
          ]
        }
      });
    }, 300);
  });
};

// 导出工厂订单数据
export const exportFactoryOrderData = (params: ExportFactoryOrderParams): Promise<ExportFactoryOrderResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        downloadUrl: `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,mock-excel-data`,
        fileName: `factory-orders-${Date.now()}.xlsx`
      });
    }, 1000);
  });
};

// 刷新统计数据
export const refreshStatistics = (): Promise<FactoryOrderStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        monthlyOrderCount: mockOrders.length + Math.floor(Math.random() * 10),
        dailyOrderCount: Math.floor(Math.random() * 20) + 5,
        monthlyGrowthRate: Math.floor(Math.random() * 20) + 5,
        dailyGrowthRate: Math.floor(Math.random() * 10) + 2,
        topStoreName: '吉隆坡中央店',
        topStoreOrderCount: Math.floor(Math.random() * 50) + 20,
        topVehicleModel: 'Model A',
        topVehicleOrderCount: Math.floor(Math.random() * 30) + 15,
        pendingDeliveryCount: mockOrders.filter(o => o.orderStatus === '01040002').length,
        lastUpdateTime: new Date().toISOString(),
        topDealers: [
          { name: '吉隆坡中央店', count: 45 },
          { name: '槟城旗舰店', count: 38 },
          { name: '新山分店', count: 32 }
        ],
        topVehicles: [
          { model: 'Model A', count: 28 },
          { model: 'Model B', count: 22 },
          { model: 'Model C', count: 18 }
        ]
      });
    }, 800);
  });
};
