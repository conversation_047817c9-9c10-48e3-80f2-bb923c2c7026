import type {
  FactoryProspectSearchParams,
  FactoryProspectPageResponse,
  FactoryProspectStatisticsResponse,
  FactoryProspectDetailResponse,
  FactoryProspectListItem,
  FactoryProspectDetail,
  CustomerBasicInfoResponse,
  StoreAssociationsResponse,
  FollowUpRecordsResponse,
  TestDriveRecordsResponse,
  DefeatRecordsResponse,
  ChangeHistoryResponse,
  PerformanceAnalysisResponse
} from '@/types/sales/factoryProspect';

// Mock数据生成
const generateMockFactoryProspects = (count: number): FactoryProspectListItem[] => {
  const prospects: FactoryProspectListItem[] = [];
  const stores = ['01010001', '01010002', '01010003', '01010004'];
  const intentLevels = ['01160001', '01160002', '01160003', '01160004']; // H, A, B, C级
  const statuses = ['01380001', '01380002', '01380003', '01380004']; // 新建, 跟进中, 已成交, 无意向
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];

  for (let i = 1; i <= count; i++) {
    prospects.push({
      globalCustomerId: `GC${String(i).padStart(6, '0')}`,
      leadId: `L${String(i).padStart(8, '0')}`,
      customerName: names[Math.floor(Math.random() * names.length)] + i,
      phoneNumber: `+60${Math.floor(Math.random() * 1000000000).toString().padStart(9, '0')}`,
      associatedStoreCount: Math.floor(Math.random() * 3) + 1,
      registrationTime: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
      prospectStatus: statuses[Math.floor(Math.random() * statuses.length)],
      lastFollowUpTime: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString() : undefined,
      createTime: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
      updateTime: new Date().toISOString()
    });
  }
  return prospects;
};

// 生成25-30条Mock数据
const mockDataCount = Math.floor(Math.random() * 6) + 25;
const mockProspects = generateMockFactoryProspects(mockDataCount);

// 获取厂端潜客列表
export const getFactoryProspectList = (params: FactoryProspectSearchParams): Promise<FactoryProspectPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockProspects];

      // 搜索过滤
      if (params.leadId) {
        filteredData = filteredData.filter(item =>
          item.leadId.toLowerCase().includes(params.leadId!.toLowerCase())
        );
      }

      if (params.storeId) {
        // 简化处理，实际应该根据门店关联记录过滤
        filteredData = filteredData.filter(item => item.associatedStoreCount > 0);
      }

      if (params.viewType && params.viewType !== 'all') {
        switch (params.viewType) {
          case 'cross_store':
            filteredData = filteredData.filter(item => item.associatedStoreCount > 1);
            break;
          case 'defeated':
            filteredData = filteredData.filter(item => item.prospectStatus === '01380004');
            break;
          case 'converted':
            filteredData = filteredData.filter(item => item.prospectStatus === '01380003');
            break;
        }
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        }
      });
    }, 500);
  });
};

// 获取厂端潜客统计
export const getFactoryProspectStatistics = (params: Partial<FactoryProspectSearchParams>): Promise<FactoryProspectStatisticsResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const hLevelCount = mockProspects.filter(p => p.currentIntentLevel === '01160001').length;
      const convertedCount = mockProspects.filter(p => p.prospectStatus === '01380003').length;
      const crossStoreCount = mockProspects.filter(p => p.associatedStoreCount > 1).length;

      resolve({
        result: {
          totalLeadCount: mockProspects.length,
          hLevelProspectCount: hLevelCount,
          monthlyDealProspectCount: convertedCount,  // 原字段名修改
          crossStoreCustomerCount: crossStoreCount,
          totalLeadCountVsLastMonth: Math.floor(Math.random() * 20) - 10,
          hLevelProspectCountVsLastMonth: Math.floor(Math.random() * 10) - 5,
          monthlyConversionRate: ((convertedCount / mockProspects.length) * 100).toFixed(1),
          crossStoreCustomerRatio: ((crossStoreCount / mockProspects.length) * 100).toFixed(1)
        }
      });
    }, 300);
  });
};

// 获取厂端潜客详情
export const getFactoryProspectDetail = (globalCustomerId: string): Promise<FactoryProspectDetailResponse> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const prospect = mockProspects.find(item => item.globalCustomerId === globalCustomerId);
      if (prospect) {
        const detail: FactoryProspectDetail = {
          ...prospect,
          customerInfo: {
            name: prospect.customerName,
            phone: prospect.phoneNumber,
            email: `${prospect.customerName.toLowerCase()}@example.com`,
            idType: 'IC',
            idNumber: '123456-78-9012',
            registerTime: prospect.registrationTime,
            registerSource: '官网注册',
            currentStatus: prospect.prospectStatus
          },
          storeAssociations: {
            associatedStoreCount: prospect.associatedStoreCount,
            storeRecords: []
          },
          followUpRecords: [],
          testDriveRecords: [],
          defeatRecords: [],
          changeHistory: []
        };

        resolve({
          result: detail
        });
      } else {
        reject(new Error('潜客不存在'));
      }
    }, 300);
  });
};

// 导出厂端潜客数据（返回Blob）
export const exportFactoryProspectData = (params: FactoryProspectSearchParams): Promise<Blob> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟Excel文件内容
      const csvContent = 'data:text/csv;charset=utf-8,潜客ID,潜客姓名,手机号,关联门店数,潜客级别,潜客状态\n' +
        mockProspects.map(item =>
          `${item.leadId},${item.customerName},${item.phoneNumber},${item.associatedStoreCount},${item.currentIntentLevel},${item.prospectStatus}`
        ).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      resolve(blob);
    }, 1000);
  });
};

// 获取客户基本信息
export const getCustomerBasicInfo = (params: { globalCustomerId: string }): Promise<CustomerBasicInfoResponse> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const prospect = mockProspects.find(item => item.globalCustomerId === params.globalCustomerId);
      if (prospect) {
        resolve({
          globalCustomerId: prospect.globalCustomerId,
          customerName: prospect.customerName,
          customerPhone: prospect.phoneNumber,
          idType: 'IC',
          idNumber: '123456-78-9012',
          email: `${prospect.customerName.toLowerCase()}@example.com`,
          registrationTime: prospect.registrationTime,
          sourceChannel: '官网注册',
          currentStatus: prospect.prospectStatus
        });
      } else {
        reject(new Error('客户不存在'));
      }
    }, 300);
  });
};

// 获取门店关联信息
export const getStoreAssociations = (params: { globalCustomerId: string }): Promise<StoreAssociationsResponse> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const prospect = mockProspects.find(item => item.globalCustomerId === params.globalCustomerId);
      if (prospect) {
        const storeRecords = Array.from({ length: prospect.associatedStoreCount }, (_, i) => ({
          storeId: `0101000${i + 1}`,
          storeName: `门店${i + 1}`,
          leadAssociationTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          associationReason: '客户主动咨询',
          currentSalesAdvisor: `顾问${i + 1}`,
          currentIntentLevel: prospect.currentIntentLevel,
          lastFollowUpTime: prospect.lastFollowUpTime || ''
        }));

        resolve({
          associatedStoreCount: prospect.associatedStoreCount,
          storeAssociationRecords: storeRecords
        });
      } else {
        reject(new Error('客户不存在'));
      }
    }, 300);
  });
};

// 获取跟进记录
export const getFollowUpRecords = (params: { storeProspectId: string }): Promise<FollowUpRecordsResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const recordCount = Math.floor(Math.random() * 10) + 1;
      const records = Array.from({ length: recordCount }, (_, i) => ({
        id: `F${String(i + 1).padStart(6, '0')}`,
        storeName: `门店${Math.floor(Math.random() * 3) + 1}`,
        salesAdvisor: `顾问${Math.floor(Math.random() * 5) + 1}`,
        followUpTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        followUpMethod: ['电话', '微信', '面谈', '短信'][Math.floor(Math.random() * 4)],
        followUpContent: `跟进内容${i + 1}`,
        intentLevel: ['H级', 'A级', 'B级', 'C级'][Math.floor(Math.random() * 4)],
        nextFollowUpTime: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      }));

      resolve({
        totalCount: recordCount,
        earliestTime: records.length > 0 ? records[records.length - 1].followUpTime : '',
        latestTime: records.length > 0 ? records[0].followUpTime : '',
        followUpRecords: records
      });
    }, 300);
  });
};

// 获取试驾记录
export const getTestDriveRecords = (params: { storeProspectId: string }): Promise<TestDriveRecordsResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const recordCount = Math.floor(Math.random() * 5) + 1;
      const records = Array.from({ length: recordCount }, (_, i) => ({
        id: `T${String(i + 1).padStart(6, '0')}`,
        storeName: `门店${Math.floor(Math.random() * 3) + 1}`,
        salesAdvisor: `顾问${Math.floor(Math.random() * 5) + 1}`,
        testDriveTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        vehicleModel: ['Myvi', 'Axia', 'Bezza', 'Aruz'][Math.floor(Math.random() * 4)],
        duration: Math.floor(Math.random() * 60) + 15,
        customerFeedback: `试驾反馈${i + 1}`
      }));

      resolve({
        totalCount: recordCount,
        testDriveRecords: records
      });
    }, 300);
  });
};

// 获取战败记录
export const getDefeatRecords = (params: { storeProspectId: string }): Promise<DefeatRecordsResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const recordCount = Math.floor(Math.random() * 3);
      const records = Array.from({ length: recordCount }, (_, i) => ({
        id: `D${String(i + 1).padStart(6, '0')}`,
        storeName: `门店${Math.floor(Math.random() * 3) + 1}`,
        salesAdvisor: `顾问${Math.floor(Math.random() * 5) + 1}`,
        defeatTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        defeatReason: ['价格因素', '竞品选择', '暂不购买', '其他原因'][Math.floor(Math.random() * 4)],
        competingBrand: ['Toyota', 'Honda', 'Nissan', 'Proton'][Math.floor(Math.random() * 4)],
        competingModel: ['Vios', 'City', 'Almera', 'Saga'][Math.floor(Math.random() * 4)],
        approvalStatus: ['已批准', '已拒绝', '待审核'][Math.floor(Math.random() * 3)]
      }));

      resolve({
        totalCount: recordCount,
        defeatRecords: records
      });
    }, 300);
  });
};

// 获取变更历史
export const getChangeHistory = (params: { storeProspectId: string }): Promise<ChangeHistoryResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const recordCount = Math.floor(Math.random() * 8) + 2;
      const records = Array.from({ length: recordCount }, (_, i) => ({
        id: `C${String(i + 1).padStart(6, '0')}`,
        changedTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        changedBy: `操作员${Math.floor(Math.random() * 5) + 1}`,
        changedField: ['意向级别', '跟进状态', '联系方式', '备注信息'][Math.floor(Math.random() * 4)],
        oldValue: `旧值${i + 1}`,
        newValue: `新值${i + 1}`,
        changeReason: `变更原因${i + 1}`
      }));

      resolve({
        totalCount: recordCount,
        changeHistoryRecords: records
      });
    }, 300);
  });
};

// 获取绩效分析
export const getPerformanceAnalysis = (params: { globalCustomerId: string }): Promise<PerformanceAnalysisResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const prospect = mockProspects.find(item => item.globalCustomerId === params.globalCustomerId);
      const followUpCount = Math.floor(Math.random() * 20) + 5;
      const storeCount = prospect?.associatedStoreCount || 1;

      resolve({
        totalFollowUpCount: followUpCount,
        totalAssociatedStoreCount: storeCount,
        mostActiveStoreName: `门店${Math.floor(Math.random() * 3) + 1}`,
        mostActiveStoreFollowUpCount: Math.floor(followUpCount * 0.6)
      });
    }, 300);
  });
};
