import type {
  SalesOrderSearchParams,
  SalesOrderListResponse,
  SalesOrderDetail,
  AvailableRight,
  RightSearchParams,
  PaginationResponse
} from '@/types/sales/orders';

// 动态生成订单数据
function generateMockOrders(count: number) {
  const orders = [];
  const orderStatuses = ['submitted', 'confirmed', 'pending_delivery', 'completed', 'canceled'];
  const buyerTypes = ['individual', 'company'];
  const paymentMethods = ['full_payment', 'installment'];
  const approvalStatuses = ['pending_approval', 'approved', 'rejected'];
  const paymentStatuses = ['pending_deposit', 'fully_paid', 'refund_completed'];
  const insuranceStatuses = ['not_insured', 'pending', 'insured'];
  const jpjRegistrationStatuses = ['pending_registration', 'registering', 'registered', 'registration_failed'];

  const models = ['Myvi', 'Ativa', 'Alza', 'Bezza', 'Axia'];
  const variants = {
    Myvi: ['1.3L Standard', '1.5L Premium', '1.5L Advance'],
    Ativa: ['1.0L Standard', '1.0L Premium', '1.0L Advance'],
    Alza: ['1.5L Standard', '1.5L Premium', '1.5L Advance'],
    Bezza: ['1.0L Standard', '1.3L Premium'],
    Axia: ['1.0L Standard', '1.0L Premium', '1.0L Advance']
  };
  const colors = ['白色', '黑色', '银色', '红色', '蓝色', '灰色', '金色'];

  for (let i = 0; i < count; i++) {
    const model = models[Math.floor(Math.random() * models.length)];
    const variant = variants[model][Math.floor(Math.random() * variants[model].length)];

    orders.push({
      id: `order_${i + 1}`,
      orderNo: `SO${String(i + 1).padStart(8, '0')}`,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      customerName: `客户${i + 1}`,
      customerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      buyerName: `购车人${i + 1}`,
      buyerPhone: `139${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      buyerType: buyerTypes[Math.floor(Math.random() * buyerTypes.length)],
      model: model,
      variant: variant,
      color: colors[Math.floor(Math.random() * colors.length)],
      vin: `VIN${String(Math.floor(Math.random() * 10000000000000000)).padStart(17, '0')}`,
      paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
      orderStatus: orderStatuses[Math.floor(Math.random() * orderStatuses.length)],
      approvalStatus: approvalStatuses[Math.floor(Math.random() * approvalStatuses.length)],
      paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
      insuranceStatus: insuranceStatuses[Math.floor(Math.random() * insuranceStatuses.length)],
      jpjRegistrationStatus: jpjRegistrationStatuses[Math.floor(Math.random() * jpjRegistrationStatuses.length)],
      totalAmount: Math.floor(Math.random() * 100000) + 50000
    });
  }
  return orders;
}

// 生成订单详情
export function generateMockOrderDetail(orderNo: string): SalesOrderDetail {
  const orders = generateMockOrders(1);
  const order = orders[0];

  return {
    id: order.id,
    orderNo: orderNo,
    createTime: order.createTime,
    updateTime: new Date().toISOString(),

    // 扁平化客户信息
    customerName: order.customerName,
    customerPhone: order.customerPhone,
    buyerName: order.buyerName,
    buyerPhone: order.buyerPhone,
    buyerType: order.buyerType,
    idType: '身份证',
    idNumber: `IC${String(Math.floor(Math.random() * 1000000000000)).padStart(12, '0')}`,
    email: `buyer${Math.floor(Math.random() * 1000)}@example.com`,
    address: `地址${Math.floor(Math.random() * 1000)}`,
    state: ['吉隆坡', '雪兰莪', '柔佛', '槟城'][Math.floor(Math.random() * 4)],
    city: ['吉隆坡市', '八打灵再也', '新山', '乔治市'][Math.floor(Math.random() * 4)],
    zipCode: String(Math.floor(Math.random() * 90000) + 10000),
    customerType: order.buyerType,

    // 门店信息
    storeRegion: ['中央区', '南区', '北区', '东区'][Math.floor(Math.random() * 4)],
    dealerCity: ['吉隆坡', '新山', '槟城', '关丹'][Math.floor(Math.random() * 4)],
    dealerName: `Perodua ${['KL', 'JB', 'PG', 'KT'][Math.floor(Math.random() * 4)]} 展厅`,
    salesConsultantName: `销售顾问${Math.floor(Math.random() * 100)}`,

    // 车辆信息
    model: order.model,
    variant: order.variant,
    color: order.color,
    vin: order.vin,
    vehiclePrice: order.totalAmount * 0.8,
    numberPlatesFee: 500,

    // 配件信息
    accessories: [
      { name: '脚垫', price: 200, quantity: 1 },
      { name: '座椅套', price: 300, quantity: 1 }
    ],
    totalAccessoryAmount: 500,

    // 开票信息
    invoiceType: '个人',
    invoiceName: order.buyerName,
    invoicePhone: order.buyerPhone,
    invoiceAddress: `地址${Math.floor(Math.random() * 1000)}`,

    // 权益信息
    rights: [
      { name: '延保服务', discount: 500 },
      { name: '免费保养', discount: 300 }
    ],
    totalRightsDiscountAmount: 800,

    // 支付信息
    paymentMethod: order.paymentMethod,
    totalAmount: order.totalAmount,
    depositAmount: Math.floor(order.totalAmount * 0.1),
    balanceAmount: order.totalAmount - Math.floor(order.totalAmount * 0.1),
    loanAmount: order.paymentMethod === 'installment' ? order.totalAmount - Math.floor(order.totalAmount * 0.1) : undefined,
    loanTerm: order.paymentMethod === 'installment' ? Math.floor(Math.random() * 4 + 3) * 12 : undefined,
    loanApprovalStatus: order.paymentMethod === 'installment' ? ['pending', 'approved', 'rejected'][Math.floor(Math.random() * 3)] : undefined,

    // 保险信息
    policies: [
      { name: '车损险', premium: 2000 },
      { name: '第三者责任险', premium: 1500 }
    ],
    totalInsuranceAmount: 3500,

    // OTR费用信息
    otrFees: [
      { name: '路税', amount: 300 },
      { name: '保险', amount: 3500 }
    ],
    totalOtrAmount: 3800,

    // 变更记录
    changeRecords: [
      {
        changeTime: new Date().toISOString(),
        changeType: '颜色变更',
        originalValue: '白色',
        newValue: '黑色',
        operator: '销售顾问'
      }
    ],

    // 计算字段
    totalInvoicePrice: order.totalAmount + 3800,
    remainingAmount: order.totalAmount - Math.floor(order.totalAmount * 0.1),

    // 状态信息
    orderStatus: order.orderStatus,
    approvalStatus: order.approvalStatus,
    paymentStatus: order.paymentStatus,
    insuranceStatus: order.insuranceStatus,
    jpjRegistrationStatus: order.jpjRegistrationStatus,

    // 其他信息
    selectedRights: ['延保服务', '免费保养'],
    remarks: '客户要求尽快交车',
    attachments: ['contract.pdf', 'id_copy.jpg']
  };
}

// 获取订单列表
export const getOrdersList = (params: SalesOrderSearchParams): Promise<SalesOrderListResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const allOrders = generateMockOrders(28); // 生成28条数据用于分页测试

      // 搜索过滤逻辑
      let filteredOrders = [...allOrders];

      if (params.orderNumber) {
        filteredOrders = filteredOrders.filter(order =>
          order.orderNo.toLowerCase().includes(params.orderNumber.toLowerCase())
        );
      }

      if (params.buyerName) {
        filteredOrders = filteredOrders.filter(order =>
          order.buyerName.toLowerCase().includes(params.buyerName.toLowerCase())
        );
      }

      if (params.buyerPhone) {
        filteredOrders = filteredOrders.filter(order =>
          order.buyerPhone.includes(params.buyerPhone)
        );
      }

      if (params.buyerType) {
        filteredOrders = filteredOrders.filter(order =>
          order.buyerType === params.buyerType
        );
      }

      if (params.orderStatus) {
        filteredOrders = filteredOrders.filter(order =>
          order.orderStatus === params.orderStatus
        );
      }

      if (params.approvalStatus) {
        filteredOrders = filteredOrders.filter(order =>
          order.approvalStatus === params.approvalStatus
        );
      }

      if (params.paymentStatus) {
        filteredOrders = filteredOrders.filter(order =>
          order.paymentStatus === params.paymentStatus
        );
      }

      if (params.insuranceStatus) {
        filteredOrders = filteredOrders.filter(order =>
          order.insuranceStatus === params.insuranceStatus
        );
      }

      if (params.jpjRegistrationStatus) {
        filteredOrders = filteredOrders.filter(order =>
          order.jpjRegistrationStatus === params.jpjRegistrationStatus
        );
      }

      if (params.createTimeStart) {
        filteredOrders = filteredOrders.filter(order =>
          new Date(order.createTime) >= new Date(params.createTimeStart)
        );
      }

      if (params.createTimeEnd) {
        filteredOrders = filteredOrders.filter(order =>
          new Date(order.createTime) <= new Date(params.createTimeEnd)
        );
      }

      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 10;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        records: filteredOrders.slice(start, end),
        total: filteredOrders.length,
        pageNum,
        pageSize,
        pages: Math.ceil(filteredOrders.length / pageSize)
      });
    }, 500);
  });
};

// 获取订单详情
export const getOrderDetail = (orderNo: string): Promise<SalesOrderDetail> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(generateMockOrderDetail(orderNo));
    }, 300);
  });
};

// 生成权益数据
function generateMockRights(count: number): AvailableRight[] {
  const rights = [];
  const rightTypes = ['延保服务', '免费保养', '道路救援', '保险优惠', '装饰套餐'];
  const discountTypes: ('fixed' | 'percentage')[] = ['fixed', 'percentage'];

  for (let i = 1; i <= count; i++) {
    const rightType = rightTypes[Math.floor(Math.random() * rightTypes.length)];
    const discountType = discountTypes[Math.floor(Math.random() * discountTypes.length)];

    rights.push({
      id: `right_${i}`,
      rightCode: `R${String(i).padStart(4, '0')}`,
      rightName: `${rightType}${i}`,
      description: `${rightType}详细说明`,
      discountAmount: discountType === 'fixed' ? Math.floor(Math.random() * 1000) + 100 : Math.floor(Math.random() * 20) + 5,
      discountType,
      validityPeriod: `${Math.floor(Math.random() * 12) + 1}个月`,
      status: Math.random() > 0.2 ? 'active' : 'inactive'
    });
  }

  return rights;
}

// 获取可用权益列表
export const getAvailableRights = (params: RightSearchParams): Promise<PaginationResponse<AvailableRight>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const allRights = generateMockRights(50);
      let filteredRights = allRights;

      // 应用搜索过滤
      if (params.rightCode) {
        filteredRights = filteredRights.filter(right =>
          right.rightCode.toLowerCase().includes(params.rightCode!.toLowerCase())
        );
      }

      if (params.rightName) {
        filteredRights = filteredRights.filter(right =>
          right.rightName.toLowerCase().includes(params.rightName!.toLowerCase())
        );
      }

      if (params.status) {
        filteredRights = filteredRights.filter(right => right.status === params.status);
      }

      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 10;
      const total = filteredRights.length;
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const records = filteredRights.slice(startIndex, endIndex);

      resolve({
        records,
        total,
        pageNum,
        pageSize,
        pages: Math.ceil(total / pageSize)
      });
    }, 300);
  });
};
