import type {
  ProspectSearchParams,
  ProspectPageResponse,
  ProspectItem,
  CreateProspectRequest,
  CreateFollowUpRequest,
  FollowUpRecord,
  AssignAdvisorRequest,
  MarkNoIntentionRequest,
  ProspectDetailInfo,
  ExistingLeadInfo,
  SearchExistingLeadRequest
} from '@/types/sales/prospects';

// 动态生成模拟潜客数据
function generateMockProspects(): ProspectItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25; // 25-30条数据
  const mockData: ProspectItem[] = [];

  const sourceChannels = ['01050001', '01050002', '01050003', '01050004'];
  const intentLevels = ['01160001', '01160002', '01160003', '01160004'];
  const statuses = ['01380001', '01380002', '01380003', '01380004'];
  const models = ['Myvi', 'Axia', 'Bezza', '<PERSON><PERSON>z', '<PERSON>za'];
  const variants = ['1.3L Standard', '1.3L Premium', '1.0L Entry', '1.5L Advance'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];
  const names = ['张三', '李四', '王五', '赵六', '刘七', '陈八', '杨九', '黄十'];

  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      id: `PROSPECT${String(i + 1).padStart(6, '0')}`,
      name: names[Math.floor(Math.random() * names.length)] + (i > 7 ? i - 7 : ''),
      phoneNumber: `1${String(Math.floor(Math.random() * 900000000) + 100000000)}`,
      sourceChannel: sourceChannels[Math.floor(Math.random() * sourceChannels.length)],
      currentIntentLevel: intentLevels[Math.floor(Math.random() * intentLevels.length)],
      prospectStatus: statuses[Math.floor(Math.random() * statuses.length)],
      intentModel: models[Math.floor(Math.random() * models.length)],
      intentVariant: variants[Math.floor(Math.random() * variants.length)],
      intentColor: colors[Math.floor(Math.random() * colors.length)],
      currentSalesAdvisorId: `SA${String(Math.floor(Math.random() * 50) + 1).padStart(3, '0')}`,
      currentSalesAdvisorName: `顾问${Math.floor(Math.random() * 50) + 1}`,
      leadAssociationTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      lastFollowUpTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      nextFollowUpTime: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    });
  }

  return mockData;
}

const mockProspects = generateMockProspects();

// 获取潜客列表
export const getProspectList = (params: ProspectSearchParams): Promise<ProspectPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockProspects];

      // 搜索过滤
      if (params.prospectId) {
        filteredData = filteredData.filter(item =>
          item.id.toLowerCase().includes(params.prospectId!.toLowerCase())
        );
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.name.includes(params.customerName!)
        );
      }

      if (params.customerPhone) {
        filteredData = filteredData.filter(item =>
          item.phoneNumber.includes(params.customerPhone!)
        );
      }

      if (params.sourceChannel) {
        filteredData = filteredData.filter(item =>
          item.sourceChannel === params.sourceChannel
        );
      }

      if (params.customerLevel) {
        filteredData = filteredData.filter(item =>
          item.currentIntentLevel === params.customerLevel
        );
      }

      if (params.customerStatus) {
        filteredData = filteredData.filter(item =>
          item.prospectStatus === params.customerStatus
        );
      }

      // 今日筛选
      if (params.isToday) {
        const today = new Date().toDateString();
        filteredData = filteredData.filter(item =>
          new Date(item.leadAssociationTime!).toDateString() === today
        );
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length
      });
    }, 500);
  });
};

// 获取潜客详情
export const getProspectDetail = (id: string): Promise<ProspectDetailInfo> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const prospect = mockProspects.find(item => item.id === id);
      if (prospect) {
        const detailInfo: ProspectDetailInfo = {
          prospectBaseInfo: {
            ...prospect,
            globalCustomerId: `GC${prospect.id.slice(-6)}`,
            email: `${prospect.name.toLowerCase()}@example.com`,
            region: '槟城',
            address: '槟城市区某某路123号',
            idType: '身份证',
            idNumber: `320123199${String(Math.floor(Math.random() * 10)).padStart(1, '0')}${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`
          },
          testDriveRecords: [
            {
              testDriveRecordId: `TD${prospect.id.slice(-3)}`,
              driverName: prospect.name,
              phoneNumber: prospect.phoneNumber,
              testDriveModel: `${prospect.intentModel} ${prospect.intentVariant}`,
              testDriveTime: new Date(Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000).toISOString(),
              testDriveFeedback: '驾驶体验良好，对动力表现满意'
            }
          ],
          followUpRecords: [
            {
              id: `FU${prospect.id.slice(-3)}001`,
              prospectId: prospect.id,
              followUpType: '电话',
              followUpTime: prospect.lastFollowUpTime!,
              nextFollowUpTime: prospect.nextFollowUpTime,
              followUpContent: '客户对车型很感兴趣，约定下周到店看车',
              followUpResult: '意向良好',
              intentLevelAfterFollowUp: prospect.currentIntentLevel,
              salesAdvisorName: prospect.currentSalesAdvisorName
            }
          ],
          defeatApplications: [],
          changeLogs: [
            {
              changeLogId: `CL${prospect.id.slice(-3)}001`,
              changeTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
              changeType: '更新意向级别',
              operatorName: prospect.currentSalesAdvisorName!,
              originalValue: 'A级',
              newValue: prospect.currentIntentLevel === '01160001' ? 'H级' : 'A级',
              changeReason: '客户表现出更强购买意向'
            }
          ],
          performanceAnalysis: {
            totalFollowUpCount: Math.floor(Math.random() * 10) + 5,
            mostActiveStore: '槟城中心店'
          }
        };

        resolve(detailInfo);
      } else {
        reject(new Error('潜客不存在'));
      }
    }, 300);
  });
};

// 新增潜客
export const createProspect = (data: CreateProspectRequest): Promise<ProspectItem> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newProspect: ProspectItem = {
        id: `PROSPECT${String(mockProspects.length + 1).padStart(6, '0')}`,
        name: data.name,
        phoneNumber: data.phoneNumber,
        sourceChannel: data.sourceChannel,
        currentIntentLevel: data.intentLevel,
        prospectStatus: '01380001', // 新建状态
        intentModel: data.intentModel,
        intentVariant: data.intentVariant,
        intentColor: data.intentColor,
        currentSalesAdvisorId: data.salesAdvisorId,
        currentSalesAdvisorName: data.salesAdvisorId ? `顾问${data.salesAdvisorId.slice(-2)}` : undefined,
        leadAssociationTime: new Date().toISOString()
      };

      mockProspects.push(newProspect);
      resolve(newProspect);
    }, 500);
  });
};

// 添加跟进记录
export const addFollowUpRecord = (data: CreateFollowUpRequest): Promise<FollowUpRecord> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 更新潜客的跟进时间
      const prospect = mockProspects.find(p => p.id === data.prospectId);
      if (prospect) {
        prospect.lastFollowUpTime = data.followUpTime;
        prospect.nextFollowUpTime = data.nextFollowUpTime;
        if (data.intentLevelAfterFollowUp) {
          prospect.currentIntentLevel = data.intentLevelAfterFollowUp;
        }
      }

      const followUpRecord: FollowUpRecord = {
        id: `FU${Date.now()}`,
        prospectId: data.prospectId,
        followUpType: data.followUpType,
        followUpTime: data.followUpTime,
        nextFollowUpTime: data.nextFollowUpTime,
        followUpContent: data.followUpContent,
        followUpResult: data.followUpResult,
        intentLevelAfterFollowUp: data.intentLevelAfterFollowUp,
        salesAdvisorName: prospect?.currentSalesAdvisorName
      };

      resolve(followUpRecord);
    }, 300);
  });
};

// 分配顾问
export const assignAdvisor = (data: AssignAdvisorRequest): Promise<boolean> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const prospect = mockProspects.find(p => p.id === data.prospectId);
      if (prospect) {
        prospect.currentSalesAdvisorId = data.newAdvisorId;
        prospect.currentSalesAdvisorName = `顾问${data.newAdvisorId.slice(-2)}`;
      }

      resolve(true);
    }, 300);
  });
};

// 标记无意向
export const markNoIntention = (data: MarkNoIntentionRequest): Promise<boolean> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const prospect = mockProspects.find(p => p.id === data.prospectId);
      if (prospect) {
        prospect.prospectStatus = '01380004'; // 无意向状态
      }

      resolve(true);
    }, 300);
  });
};

// 搜索现有线索
export const searchExistingLead = (params: SearchExistingLeadRequest): Promise<ExistingLeadInfo | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟搜索逻辑
      const existingProspect = mockProspects.find(p =>
        (params.customerName && p.name.includes(params.customerName)) ||
        (params.customerPhone && p.phoneNumber.includes(params.customerPhone))
      );

      if (existingProspect) {
        const existingLead: ExistingLeadInfo = {
          id: parseInt(existingProspect.id.slice(-6)),
          customerName: existingProspect.name,
          customerPhone: existingProspect.phoneNumber,
          email: `${existingProspect.name.toLowerCase()}@example.com`,
          sourceChannel: existingProspect.sourceChannel
        };
        resolve(existingLead);
      } else {
        resolve(null);
      }
    }, 300);
  });
};
