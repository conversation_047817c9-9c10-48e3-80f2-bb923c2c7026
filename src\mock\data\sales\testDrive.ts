import type { TestDriveRecord, GetTestDriveListRequest, PageResult } from '@/types/sales/testDrive'

// Mock 试驾记录数据生成器
export const generateMockTestDriveRecords = (count: number = 25): TestDriveRecord[] => {
  const records: TestDriveRecord[] = []
  
  const customerNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const phoneNumbers = ['13800138001', '13800138002', '13800138003', '13800138004', '13800138005', '13800138006', '13800138007', '13800138008']
  const models = ['AXIA', 'BEZZA', 'ALZA', 'ARUZ']
  const variants = ['E', 'G', 'AV']
  const sources = ['网络推广', '朋友推荐', '门店咨询', '电话咨询', '展会']
  const consultantNames = ['销售顾问A', '销售顾问B', '销售顾问C', '销售顾问D']
  
  for (let i = 0; i < count; i++) {
    const customerName = customerNames[i % customerNames.length]
    const customerPhone = phoneNumbers[i % phoneNumbers.length]
    const model = models[i % models.length]
    const variant = variants[i % variants.length]
    const source = sources[i % sources.length]
    const consultantName = consultantNames[i % consultantNames.length]
    
    // 生成时间戳
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // 最近30天内
    const startTime = new Date(createTime.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000) // 创建后7天内
    const endTime = new Date(startTime.getTime() + Math.random() * 4 * 60 * 60 * 1000) // 开始后4小时内
    
    // 生成里程数
    const startMileage = Math.floor(Math.random() * 1000) + 50000
    const endMileage = startMileage + Math.floor(Math.random() * 50) + 10
    
    const record: TestDriveRecord = {
      id: `td_${Date.now()}_${i}`,
      testDriveNo: `TD${String(Date.now()).slice(-8)}${String(i).padStart(3, '0')}`,
      customerId: i + 1000,
      
      // 潜客信息
      customerName,
      customerPhone,
      customerIdType: i % 2 === 0 ? '身份证' : '护照',
      customerIdNumber: i % 2 === 0 ? `${Math.floor(Math.random() * 900000000) + 100000000}X` : `P${Math.floor(Math.random() * 9000000) + 1000000}`,
      source,
      email: `${customerName.toLowerCase()}@example.com`,
      region: i % 4 === 0 ? '广东省深圳市' : i % 4 === 1 ? '广东省广州市' : i % 4 === 2 ? '上海市' : '北京市',
      address: `${i % 4 === 0 ? '深圳市南山区' : i % 4 === 1 ? '广州市天河区' : i % 4 === 2 ? '上海市浦东新区' : '北京市朝阳区'}某某街道${i + 1}号`,
      
      // 试驾人信息（可能与潜客不同）
      driverName: i % 3 === 0 ? customerName : `${customerName}的朋友`,
      driverPhone: i % 3 === 0 ? customerPhone : `1380013${String(8001 + i).slice(-4)}`,
      driverIdType: i % 2 === 0 ? 1 : 2, // 1-身份证，2-护照
      driverIdNumber: i % 2 === 0 ? `${Math.floor(Math.random() * 900000000) + 100000000}X` : `P${Math.floor(Math.random() * 9000000) + 1000000}`,
      driverLicenseNumber: `粤B${Math.floor(Math.random() * 900000) + 100000}`,
      
      // 试驾详情
      model,
      variant,
      startMileage,
      endMileage,
      mileage: endMileage - startMileage,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      feedback: i % 4 === 0 ? '车辆性能良好，驾驶体验不错' : i % 4 === 1 ? '车辆操控灵活，乘坐舒适' : i % 4 === 2 ? '动力充沛，配置丰富' : '整体满意，考虑购买',
      
      // 销售和门店信息
      consultantId: (i % 4) + 1,
      consultantName,
      storeId: (i % 3) + 1,
      storeName: i % 3 === 0 ? '深圳旗舰店' : i % 3 === 1 ? '广州体验店' : '上海展示厅',
      storeRegion: i % 3 === 0 ? '华南' : i % 3 === 1 ? '华南' : '华东',
      
      // 时间戳和标志
      createTime: createTime.toISOString(),
      updateTime: createTime.toISOString(),
      editable: Math.random() > 0.2 // 80%的记录可编辑
    }
    
    records.push(record)
  }
  
  return records
}

// Mock API 响应生成器
export const mockTestDriveListResponse = (params: GetTestDriveListRequest): PageResult<TestDriveRecord> => {
  const allRecords = generateMockTestDriveRecords(30)
  
  // 应用筛选条件
  const filteredRecords = allRecords.filter(record => {
    if (params.customerName && !record.customerName.includes(params.customerName)) {
      return false
    }
    if (params.customerPhone && !record.customerPhone.includes(params.customerPhone)) {
      return false
    }
    if (params.model && record.model !== params.model) {
      return false
    }
    if (params.variant && record.variant !== params.variant) {
      return false
    }
    if (params.startTimeBegin && new Date(record.startTime) < new Date(params.startTimeBegin)) {
      return false
    }
    if (params.startTimeEnd && new Date(record.startTime) > new Date(params.startTimeEnd)) {
      return false
    }
    return true
  })
  
  // 应用分页
  const total = filteredRecords.length
  const pageSize = params.pageSize || 20
  const pageNum = params.pageNum || 1
  const startIndex = (pageNum - 1) * pageSize
  const endIndex = startIndex + pageSize
  const records = filteredRecords.slice(startIndex, endIndex)
  
  return {
    total,
    pages: Math.ceil(total / pageSize),
    current: pageNum,
    size: pageSize,
    records
  }
}

// Mock 试驾详情数据
export const mockTestDriveDetail = (testDriveNo: string): TestDriveRecord | null => {
  const allRecords = generateMockTestDriveRecords(30)
  return allRecords.find(record => record.testDriveNo === testDriveNo) || null
}

// Mock 潜客搜索数据
export const mockLeadSearchResults = (params: { customerName?: string; customerPhone?: string }) => {
  const leads = [
    {
      id: 1,
      name: '张三',
      phone: '13800138001',
      idType: '身份证',
      idNumber: '440300199001011234',
      email: '<EMAIL>',
      region: '广东省深圳市',
      address: '深圳市南山区科技园',
      source: '网络推广'
    },
    {
      id: 2,
      name: '李四',
      phone: '13800138002',
      idType: '身份证',
      idNumber: '440300199002022345',
      email: '<EMAIL>',
      region: '广东省广州市',
      address: '广州市天河区珠江新城',
      source: '朋友推荐'
    },
    {
      id: 3,
      name: '王五',
      phone: '13800138003',
      idType: '护照',
      idNumber: '********',
      email: '<EMAIL>',
      region: '上海市',
      address: '上海市浦东新区陆家嘴',
      source: '门店咨询'
    }
  ]
  
  return leads.filter(lead => {
    if (params.customerName && !lead.name.includes(params.customerName)) {
      return false
    }
    if (params.customerPhone && !lead.phone.includes(params.customerPhone)) {
      return false
    }
    return true
  })
}

// 导出所有Mock数据生成器
export const testDriveMockData = {
  generateMockTestDriveRecords,
  mockTestDriveListResponse,
  mockTestDriveDetail,
  mockLeadSearchResults
}