import type { VehicleModelSearchParams, VehicleModelPageResponse, SyncLogItem, SyncResponse } from '@/types/sales/vehicleModel';

// 动态生成模拟数据（25-30条）
function generateMockData() {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData = [];

  const models = ['MYVI', 'ALZA', 'AXIA', 'BEZZA'];
  const variants = {
    'MYVI': ['1.5L H CVT', '1.3L G MT', '1.3L G CVT'],
    'ALZA': ['1.5L AV CVT', '1.5L H CVT'],
    'AXIA': ['1.0L G MT', '1.0L G CVT'],
    'BEZZA': ['1.3L X CVT', '1.0L G MT']
  };
  const colors = ['GRANITE GREY', 'ELECTRIC BLUE', 'GLITTERING SILVER', 'IVORY WHITE'];

  for (let i = 0; i < dataCount; i++) {
    const model = models[Math.floor(Math.random() * models.length)];
    const variantList = variants[model];
    const variant = variantList[Math.floor(Math.random() * variantList.length)];
    const color = colors[Math.floor(Math.random() * colors.length)];

    mockData.push({
      id: `${i + 1}`,
      model,
      variantName: variant,
      variantCode: `BD${Math.floor(Math.random() * 9)}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
      colourName: color,
      colourCode: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(Math.random() * 99)}(M)`,
      fmrid: `C${6500 + i}`,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
    });
  }

  return mockData;
}

const mockData = generateMockData();

// ✅ 标准MyBatisPlus分页响应
export const getMockVehicleModelList = (params: VehicleModelSearchParams): Promise<VehicleModelPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.model) {
        filteredData = filteredData.filter(item =>
          item.model.toLowerCase().includes(params.model!.toLowerCase())
        );
      }

      if (params.variantName) {
        filteredData = filteredData.filter(item =>
          item.variantName.toLowerCase().includes(params.variantName!.toLowerCase())
        );
      }

      if (params.colourName) {
        filteredData = filteredData.filter(item =>
          item.colourName.toLowerCase().includes(params.colourName!.toLowerCase())
        );
      }

      if (params.fmrid) {
        filteredData = filteredData.filter(item =>
          item.fmrid.toLowerCase().includes(params.fmrid!.toLowerCase())
        );
      }

      // ✅ 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        records: filteredData.slice(start, end),  // ✅ 标准MyBatisPlus响应
        total: filteredData.length,
        pageNum,
        pageSize,
        pages: Math.ceil(filteredData.length / pageSize)
      });
    }, 500);
  });
};

// 同步数据Mock
export const getMockSyncResponse = (): Promise<SyncResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true, message: '数据同步成功' });
    }, 2000);
  });
};

// 同步日志Mock
export const getMockSyncLogs = (): Promise<SyncLogItem[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        {
          id: '1',
          syncTime: '2024-01-15 10:30:00',
          status: 'success',
          recordCount: 150
        },
        {
          id: '2',
          syncTime: '2024-01-14 10:30:00',
          status: 'failed',
          recordCount: 0,
          errorMessage: 'Connection timeout'
        },
        {
          id: '3',
          syncTime: '2024-01-13 10:30:00',
          status: 'success',
          recordCount: 142
        }
      ]);
    }, 300);
  });
};
