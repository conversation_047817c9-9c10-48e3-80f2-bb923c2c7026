import type { VehicleQuerySearchParams, VehicleQueryPageResponse } from '@/types/sales/vehicleQuery';

// 动态生成模拟数据
function generateMockData() {
  const dataCount = Math.floor(Math.random() * 6) + 25; // 25-30条数据
  const mockData = [];

  // 使用数据字典编码
  const stockStatusOptions = ['01200001001', '01200001002', '01200001003', '01200001004']; // 在库、配车、在途、调拨
  const lockStatusOptions = ['01200002001', '01200002002']; // 已锁定、未锁定
  const invoiceStatusOptions = ['01200003001', '01200003002']; // 已开票、未开票
  const deliveryStatusOptions = ['01200004001', '01200004002']; // 已交车、未交车

  const models = ['Axia', 'Bezza', 'Myvi', 'Alza', 'Aruz'];
  const variants = ['1.0 Standard', '1.3 Premium', '1.5 Advance', '1.0 SE', '1.3 AV'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色', '灰色'];
  const warehouses = ['主仓库', '分仓库A', '分仓库B', '分仓库C'];

  for (let i = 0; i < dataCount; i++) {
    const baseDate = new Date();
    const randomDays = Math.floor(Math.random() * 365);
    
    mockData.push({
      id: `VQ${String(i + 1).padStart(6, '0')}`,
      factoryOrderNo: `FO${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
      vin: `VIN${String(Math.floor(Math.random() * 9999999999999999)).padStart(17, '0')}`,
      model: models[Math.floor(Math.random() * models.length)],
      variant: variants[Math.floor(Math.random() * variants.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      fmrId: `FMR${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
      warehouseName: warehouses[Math.floor(Math.random() * warehouses.length)],
      stockStatus: stockStatusOptions[Math.floor(Math.random() * stockStatusOptions.length)],
      lockStatus: lockStatusOptions[Math.floor(Math.random() * lockStatusOptions.length)],
      invoiceStatus: invoiceStatusOptions[Math.floor(Math.random() * invoiceStatusOptions.length)],
      deliveryStatus: deliveryStatusOptions[Math.floor(Math.random() * deliveryStatusOptions.length)],
      invoiceDate: new Date(baseDate.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      deliveryDate: new Date(baseDate.getTime() - Math.random() * 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      storageDate: new Date(baseDate.getTime() - Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      productionDate: new Date(baseDate.getTime() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    });
  }

  return mockData;
}

const mockData = generateMockData();

export const getVehicleQueryList = (params: VehicleQuerySearchParams): Promise<VehicleQueryPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      // VIN搜索
      if (params.vin) {
        filteredData = filteredData.filter(item =>
          item.vin.toLowerCase().includes(params.vin!.toLowerCase())
        );
      }

      // 工厂订单号搜索
      if (params.factoryOrderNo) {
        filteredData = filteredData.filter(item =>
          item.factoryOrderNo.toLowerCase().includes(params.factoryOrderNo!.toLowerCase())
        );
      }

      // FMR ID搜索
      if (params.fmrId) {
        filteredData = filteredData.filter(item =>
          item.fmrId.toLowerCase().includes(params.fmrId!.toLowerCase())
        );
      }

      // 仓库名称筛选
      if (params.warehouseName) {
        filteredData = filteredData.filter(item =>
          item.warehouseName === params.warehouseName
        );
      }

      // 车型筛选
      if (params.model) {
        filteredData = filteredData.filter(item =>
          item.model === params.model
        );
      }

      // 配置筛选
      if (params.variant) {
        filteredData = filteredData.filter(item =>
          item.variant === params.variant
        );
      }

      // 颜色筛选
      if (params.color) {
        filteredData = filteredData.filter(item =>
          item.color === params.color
        );
      }

      // 状态筛选
      if (params.stockStatus) {
        filteredData = filteredData.filter(item =>
          item.stockStatus === params.stockStatus
        );
      }

      if (params.lockStatus) {
        filteredData = filteredData.filter(item =>
          item.lockStatus === params.lockStatus
        );
      }

      if (params.invoiceStatus) {
        filteredData = filteredData.filter(item =>
          item.invoiceStatus === params.invoiceStatus
        );
      }

      if (params.deliveryStatus) {
        filteredData = filteredData.filter(item =>
          item.deliveryStatus === params.deliveryStatus
        );
      }

      // 日期范围筛选
      if (params.invoiceDateStart && params.invoiceDateEnd) {
        filteredData = filteredData.filter(item =>
          item.invoiceDate >= params.invoiceDateStart! && item.invoiceDate <= params.invoiceDateEnd!
        );
      }

      if (params.storageDateStart && params.storageDateEnd) {
        filteredData = filteredData.filter(item =>
          item.storageDate >= params.storageDateStart! && item.storageDate <= params.storageDateEnd!
        );
      }

      if (params.productionDateStart && params.productionDateEnd) {
        filteredData = filteredData.filter(item =>
          item.productionDate >= params.productionDateStart! && item.productionDate <= params.productionDateEnd!
        );
      }

      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 10;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        }
      });
    }, 500); // 模拟网络延迟
  });
};
