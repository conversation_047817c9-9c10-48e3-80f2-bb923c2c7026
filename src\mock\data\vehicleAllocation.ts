import type { 
  VehicleAllocationOrderItem, 
  AvailableVehicle, 
  AllocationRecord, 
  OrderAllocationDetail,
  OrderAllocationTimelineItem,
  AllocationStatistics,
  AllocationStatus,
  OrderStatus,
  StockStatus,
  OperationType,
  ProcessResult
} from '@/types/vehicleAllocation';

// 模拟车辆配车订单数据
export const mockVehicleAllocationOrders: VehicleAllocationOrderItem[] = [
  {
    id: 1,
    orderNumber: 'ORD2024001',
    customerName: '张三',
    customerPhone: '13800138001',
    store: '北京门店',
    salesConsultant: '李销售',
    model: 'Model 3',
    variant: 'Long Range',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024001',
    vin: 'LRW3E7FS1EC123456',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-15 10:30:00',
  },
  {
    id: 2,
    orderNumber: 'ORD2024002',
    customerName: '李四',
    customerPhone: '13800138002',
    store: '上海门店',
    salesConsultant: '王销售',
    model: 'Model Y',
    variant: 'Performance',
    color: '深海蓝',
    factoryOrderNumber: 'FON2024002',
    vin: 'LRW3E7FS2EC234567',
    allocationStatus: 'allocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-16 14:20:00',
    allocationTime: '2024-01-17 09:15:00',
  },
  {
    id: 3,
    orderNumber: 'ORD2024003',
    customerName: '王五',
    customerPhone: '13800138003',
    store: '北京门店',
    salesConsultant: '赵销售',
    model: 'Model S',
    variant: 'Plaid',
    color: '曜石黑',
    factoryOrderNumber: 'FON2024003',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-17 16:45:00',
  },
  {
    id: 4,
    orderNumber: 'ORD2024004',
    customerName: '赵六',
    customerPhone: '13800138004',
    store: '深圳门店',
    salesConsultant: '孙销售',
    model: 'Model X',
    variant: 'Plaid',
    color: '中国红',
    factoryOrderNumber: 'FON2024004',
    vin: 'LRW3E7FS4EC456789',
    allocationStatus: 'allocated',
    orderStatus: 'ready_delivery',
    orderCreateTime: '2024-01-18 11:20:00',
    allocationTime: '2024-01-19 13:30:00',
  },
  {
    id: 5,
    orderNumber: 'ORD2024005',
    customerName: '孙七',
    customerPhone: '13800138005',
    store: '广州门店',
    salesConsultant: '周销售',
    model: 'Model 3',
    variant: 'Standard',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024005',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'cancel_review',
    orderCreateTime: '2024-01-19 08:10:00',
  },
  {
    id: 6,
    orderNumber: 'ORD2024006',
    customerName: '周八',
    customerPhone: '13800138006',
    store: '成都门店',
    salesConsultant: '吴销售',
    model: 'Model Y',
    variant: 'Long Range',
    color: '冷光银',
    factoryOrderNumber: 'FON2024006',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-20 11:00:00',
  },
  {
    id: 7,
    orderNumber: 'ORD2024007',
    customerName: '吴九',
    customerPhone: '13800138007',
    store: '北京门店',
    salesConsultant: '李销售',
    model: 'Model 3',
    variant: 'Performance',
    color: '深海蓝',
    factoryOrderNumber: 'FON2024007',
    vin: 'LRW3E7FS7EC789012',
    allocationStatus: 'allocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-21 13:20:00',
    allocationTime: '2024-01-22 10:00:00',
  },
  {
    id: 8,
    orderNumber: 'ORD2024008',
    customerName: '郑十',
    customerPhone: '13800138008',
    store: '上海门店',
    salesConsultant: '王销售',
    model: 'Model S',
    variant: 'Long Range',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024008',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-22 15:00:00',
  },
  {
    id: 9,
    orderNumber: 'ORD2024009',
    customerName: '冯十一',
    customerPhone: '13800138009',
    store: '深圳门店',
    salesConsultant: '孙销售',
    model: 'Model X',
    variant: 'Long Range',
    color: '冷光银',
    factoryOrderNumber: 'FON2024009',
    vin: 'LRW3E7FS9EC901234',
    allocationStatus: 'allocated',
    orderStatus: 'ready_delivery',
    orderCreateTime: '2024-01-23 09:45:00',
    allocationTime: '2024-01-24 11:30:00',
  },
  {
    id: 10,
    orderNumber: 'ORD2024010',
    customerName: '陈十二',
    customerPhone: '13800138010',
    store: '广州门店',
    salesConsultant: '周销售',
    model: 'Model 3',
    variant: 'Long Range',
    color: '中国红',
    factoryOrderNumber: 'FON2024010',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'delivered',
    orderCreateTime: '2024-01-24 14:00:00',
  },
  {
    id: 11,
    orderNumber: 'ORD2024011',
    customerName: '褚十三',
    customerPhone: '13800138011',
    store: '北京门店',
    salesConsultant: '赵销售',
    model: 'Model Y',
    variant: 'Standard',
    color: '曜石黑',
    factoryOrderNumber: 'FON2024011',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'cancelled',
    orderCreateTime: '2024-01-25 10:10:00',
  },
  {
    id: 12,
    orderNumber: 'ORD2024012',
    customerName: '卫十四',
    customerPhone: '13800138012',
    store: '成都门店',
    salesConsultant: '吴销售',
    model: 'Model S',
    variant: 'Plaid',
    color: '深海蓝',
    factoryOrderNumber: 'FON2024012',
    vin: 'LRW3E7FSCECD12345',
    allocationStatus: 'allocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-26 16:30:00',
    allocationTime: '2024-01-27 09:00:00',
  },
  {
    id: 13,
    orderNumber: 'ORD2024013',
    customerName: '蒋十五',
    customerPhone: '13800138013',
    store: '上海门店',
    salesConsultant: '王销售',
    model: 'Model X',
    variant: 'Plaid',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024013',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-27 11:40:00',
  },
  {
    id: 14,
    orderNumber: 'ORD2024014',
    customerName: '沈十六',
    customerPhone: '13800138014',
    store: '深圳门店',
    salesConsultant: '孙销售',
    model: 'Model 3',
    variant: 'Performance',
    color: '冷光银',
    factoryOrderNumber: 'FON2024014',
    vin: 'LRW3E7FSEECF23456',
    allocationStatus: 'allocated',
    orderStatus: 'ready_delivery',
    orderCreateTime: '2024-01-28 14:50:00',
    allocationTime: '2024-01-29 14:00:00',
  },
  {
    id: 15,
    orderNumber: 'ORD2024015',
    customerName: '韩十七',
    customerPhone: '13800138015',
    store: '广州门店',
    salesConsultant: '周销售',
    model: 'Model Y',
    variant: 'Long Range',
    color: '中国红',
    factoryOrderNumber: 'FON2024015',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'cancel_approved',
    orderCreateTime: '2024-01-29 09:30:00',
  },
  {
    id: 16,
    orderNumber: 'ORD2024016',
    customerName: '杨十八',
    customerPhone: '13800138016',
    store: '北京门店',
    salesConsultant: '李销售',
    model: 'Model 3',
    variant: 'Long Range',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024016',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-02-01 10:00:00',
  },
  {
    id: 17,
    orderNumber: 'ORD2024017',
    customerName: '朱十九',
    customerPhone: '13800138017',
    store: '上海门店',
    salesConsultant: '王销售',
    model: 'Model Y',
    variant: 'Performance',
    color: '深海蓝',
    factoryOrderNumber: 'FON2024017',
    vin: 'LRW3E7FSHECH34567',
    allocationStatus: 'allocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-02-02 11:00:00',
    allocationTime: '2024-02-03 10:30:00',
  },
  {
    id: 18,
    orderNumber: 'ORD2024018',
    customerName: '秦二十',
    customerPhone: '13800138018',
    store: '成都门店',
    salesConsultant: '吴销售',
    model: 'Model S',
    variant: 'Plaid',
    color: '曜石黑',
    factoryOrderNumber: 'FON2024018',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-02-03 14:00:00',
  },
  {
    id: 19,
    orderNumber: 'ORD2024019',
    customerName: '尤二十一',
    customerPhone: '13800138019',
    store: '深圳门店',
    salesConsultant: '孙销售',
    model: 'Model X',
    variant: 'Plaid',
    color: '中国红',
    factoryOrderNumber: 'FON2024019',
    vin: 'LRW3E7FSJECJ45678',
    allocationStatus: 'allocated',
    orderStatus: 'ready_delivery',
    orderCreateTime: '2024-02-04 15:00:00',
    allocationTime: '2024-02-05 11:00:00',
  },
  {
    id: 20,
    orderNumber: 'ORD2024020',
    customerName: '许二十二',
    customerPhone: '13800138020',
    store: '广州门店',
    salesConsultant: '周销售',
    model: 'Model 3',
    variant: 'Standard',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024020',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-02-05 16:00:00',
  }
];

// 模拟可配车辆数据
export const mockAvailableVehicles: AvailableVehicle[] = [
  {
    id: 1,
    vin: 'LRW3E7FS1EC111111',
    factoryOrderNumber: 'FON2024101',
    model: 'Model 3',
    variant: 'Long Range',
    color: '珍珠白',
    warehouseName: '北京仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-10 10:00:00',
  },
  {
    id: 2,
    vin: 'LRW3E7FS2EC222222',
    factoryOrderNumber: 'FON2024102',
    model: 'Model Y',
    variant: 'Performance',
    color: '深海蓝',
    warehouseName: '上海仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-11 14:30:00',
  },
  {
    id: 3,
    vin: 'LRW3E7FS3EC333333',
    factoryOrderNumber: 'FON2024103',
    model: 'Model S',
    variant: 'Plaid',
    color: '曜石黑',
    warehouseName: '北京仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-12 09:15:00',
  },
  {
    id: 4,
    vin: 'LRW3E7FS4EC444444',
    factoryOrderNumber: 'FON2024104',
    model: 'Model X',
    variant: 'Plaid',
    color: '中国红',
    warehouseName: '深圳仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-13 16:20:00',
  },
  {
    id: 5,
    vin: 'LRW3E7FS5EC555555',
    factoryOrderNumber: 'FON2024105',
    model: 'Model 3',
    variant: 'Standard',
    color: '珍珠白',
    warehouseName: '广州仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-14 12:40:00',
  },
  {
    id: 6,
    vin: 'LRW3E7FS6EC666666',
    factoryOrderNumber: 'FON2024106',
    model: 'Model Y',
    variant: 'Long Range',
    color: '冷光银',
    warehouseName: '成都仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-15 10:00:00',
  },
  {
    id: 7,
    vin: 'LRW3E7FS7EC789012',
    factoryOrderNumber: 'FON2024007',
    model: 'Model 3',
    variant: 'Performance',
    color: '深海蓝',
    warehouseName: '北京仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-20 11:00:00',
  },
  {
    id: 8,
    vin: 'LRW3E7FS8EC890123',
    factoryOrderNumber: 'FON2024108',
    model: 'Model S',
    variant: 'Long Range',
    color: '珍珠白',
    warehouseName: '上海仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-21 12:00:00',
  },
  {
    id: 9,
    vin: 'LRW3E7FS9EC901234',
    factoryOrderNumber: 'FON2024009',
    model: 'Model X',
    variant: 'Long Range',
    color: '冷光银',
    warehouseName: '深圳仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-22 13:00:00',
  },
  {
    id: 12,
    vin: 'LRW3E7FSCECD12345',
    factoryOrderNumber: 'FON2024012',
    model: 'Model S',
    variant: 'Plaid',
    color: '深海蓝',
    warehouseName: '成都仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-25 14:00:00',
  },
  {
    id: 14,
    vin: 'LRW3E7FSEECF23456',
    factoryOrderNumber: 'FON2024014',
    model: 'Model 3',
    variant: 'Performance',
    color: '冷光银',
    warehouseName: '深圳仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-27 15:00:00',
  },
  {
    id: 17,
    vin: 'LRW3E7FSHECH34567',
    factoryOrderNumber: 'FON2024017',
    model: 'Model Y',
    variant: 'Performance',
    color: '深海蓝',
    warehouseName: '上海仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-02-01 16:00:00',
  },
  {
    id: 19,
    vin: 'LRW3E7FSJECJ45678',
    factoryOrderNumber: 'FON2024019',
    model: 'Model X',
    variant: 'Plaid',
    color: '中国红',
    warehouseName: '深圳仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-02-03 17:00:00',
  }
];

// 模拟配车记录数据
export const mockAllocationRecords: AllocationRecord[] = [
  {
    id: 1,
    recordNumber: 'ARN2024001',
    orderNumber: 'ORD2024002',
    customerName: '李四',
    vin: 'LRW3E7FS2EC234567',
    operationType: 'allocate',
    operator: '配车管理员',
    operationTime: '2024-01-17 09:15:00',
    processResult: 'success',
    remarks: '正常配车',
    operationDetails: '为订单ORD2024002成功配车，VIN: LRW3E7FS2EC234567，车辆信息：Model Y Performance 深海蓝',
  },
  {
    id: 2,
    recordNumber: 'ARN2024002',
    orderNumber: 'ORD2024004',
    customerName: '赵六',
    vin: 'LRW3E7FS4EC456789',
    operationType: 'allocate',
    operator: '配车管理员',
    operationTime: '2024-01-19 13:30:00',
    processResult: 'success',
    remarks: '客户指定车辆',
    operationDetails: '为订单ORD2024004成功配车，VIN: LRW3E7FS4EC456789，车辆信息：Model X Plaid 中国红',
  },
  {
    id: 3,
    recordNumber: 'ARN2024003',
    orderNumber: 'ORD2024001',
    customerName: '张三',
    vin: 'LRW3E7FS1EC111111',
    operationType: 'allocate',
    operator: '配车管理员',
    operationTime: '2024-01-20 10:45:00',
    processResult: 'failed',
    remarks: '车辆配置不匹配',
    operationDetails: '尝试为订单ORD2024001配车失败，原因：车辆配置不匹配订单要求',
  },
  {
    id: 4,
    recordNumber: 'ARN2024004',
    orderNumber: 'ORD2024005',
    customerName: '孙七',
    vin: 'LRW3E7FS5EC555555',
    operationType: 'system_auto_cancel',
    operator: '系统',
    operationTime: '2024-01-21 08:20:00',
    processResult: 'success',
    remarks: '订单取消审批通过',
    operationDetails: '系统自动取消配车，原因：订单状态变更为取消审批通过',
  },
  {
    id: 5,
    recordNumber: 'ARN2024005',
    orderNumber: 'ORD2024006',
    customerName: '周八',
    vin: 'LRW3E7FS6EC666666',
    operationType: 'allocate',
    operator: '配车管理员',
    operationTime: '2024-01-20 11:00:00',
    processResult: 'success',
    remarks: '正常配车',
    operationDetails: '为订单ORD2024006成功配车，VIN: LRW3E7FS6EC666666',
  },
  {
    id: 6,
    recordNumber: 'ARN2024006',
    orderNumber: 'ORD2024007',
    customerName: '吴九',
    vin: 'LRW3E7FS7EC789012',
    operationType: 'allocate',
    operator: '配车管理员',
    operationTime: '2024-01-22 10:00:00',
    processResult: 'success',
    remarks: '正常配车',
    operationDetails: '为订单ORD2024007成功配车，VIN: LRW3E7FS7EC789012',
  },
  {
    id: 7,
    recordNumber: 'ARN2024007',
    orderNumber: 'ORD2024008',
    customerName: '郑十',
    vin: 'LRW3E7FS8EC890123',
    operationType: 'allocate',
    operator: '配车管理员',
    operationTime: '2024-01-22 15:00:00',
    processResult: 'success',
    remarks: '正常配车',
    operationDetails: '为订单ORD2024008成功配车，VIN: LRW3E7FS8EC890123',
  },
  {
    id: 8,
    recordNumber: 'ARN2024008',
    orderNumber: 'ORD2024009',
    customerName: '冯十一',
    vin: 'LRW3E7FS9EC901234',
    operationType: 'allocate',
    operator: '配车管理员',
    operationTime: '2024-01-24 11:30:00',
    processResult: 'success',
    remarks: '正常配车',
    operationDetails: '为订单ORD2024009成功配车，VIN: LRW3E7FS9EC901234',
  },
];

// 模拟订单配车详情数据
export const mockOrderAllocationDetails: Record<string, OrderAllocationDetail> = {
  'ORD2024001': {
    orderNumber: 'ORD2024001',
    ordererName: '张三',
    ordererPhone: '13800138001',
    customerType: '个人客户',
    model: 'Model 3',
    variant: 'Long Range',
    color: '珍珠白',
    salesConsultant: '李销售',
    store: '北京门店',
    orderCreateTime: '2024-01-15 10:30:00',
    orderStatus: 'confirmed',
    allocationStatus: 'unallocated',
  },
  'ORD2024002': {
    orderNumber: 'ORD2024002',
    ordererName: '李四',
    ordererPhone: '13800138002',
    customerType: '个人客户',
    model: 'Model Y',
    variant: 'Performance',
    color: '深海蓝',
    salesConsultant: '王销售',
    store: '上海门店',
    orderCreateTime: '2024-01-16 14:20:00',
    orderStatus: 'confirmed',
    allocationStatus: 'allocated',
  },
};

// 模拟订单配车历史时间轴数据
export const mockOrderAllocationTimelines: Record<string, OrderAllocationTimelineItem[]> = {
  'ORD2024002': [
    {
      id: 1,
      operationType: 'allocate',
      operator: '配车管理员',
      operationTime: '2024-01-17 09:15:00',
      vin: 'LRW3E7FS2EC234567',
      warehouseName: '上海仓库',
      processResult: 'success',
      remarks: '正常配车',
      operationDetails: '为订单ORD2024002成功配车，VIN: LRW3E7FS2EC234567，车辆信息：Model Y Performance 深海蓝',
      isSystemOperation: false,
    },
    {
      id: 2,
      operationType: 'view_detail',
      operator: '李销售',
      operationTime: '2024-01-17 14:30:00',
      processResult: 'success',
      remarks: '查看配车详情',
      operationDetails: '销售顾问查看订单配车详情',
      isSystemOperation: false,
    },
  ],
  'ORD2024004': [
    {
      id: 1,
      operationType: 'allocate',
      operator: '配车管理员',
      operationTime: '2024-01-19 13:30:00',
      vin: 'LRW3E7FS4EC456789',
      warehouseName: '深圳仓库',
      processResult: 'success',
      remarks: '客户指定车辆',
      operationDetails: '为订单ORD2024004成功配车，VIN: LRW3E7FS4EC456789，车辆信息：Model X Plaid 中国红',
      isSystemOperation: false,
    },
  ],
  'ORD2024007': [
    {
      id: 1,
      operationType: 'allocate',
      operator: '配车管理员',
      operationTime: '2024-01-22 10:00:00',
      vin: 'LRW3E7FS7EC789012',
      warehouseName: '北京仓库',
      processResult: 'success',
      remarks: '正常配车',
      operationDetails: '为订单ORD2024007成功配车，VIN: LRW3E7FS7EC789012',
      isSystemOperation: false,
    }
  ],
  'ORD2024009': [
    {
      id: 1,
      operationType: 'allocate',
      operator: '配车管理员',
      operationTime: '2024-01-24 11:30:00',
      vin: 'LRW3E7FS9EC901234',
      warehouseName: '深圳仓库',
      processResult: 'success',
      remarks: '正常配车',
      operationDetails: '为订单ORD2024009成功配车，VIN: LRW3E7FS9EC901234',
      isSystemOperation: false,
    }
  ]
};

// 模拟配车统计数据
export const mockAllocationStatistics: AllocationStatistics = {
  totalAllocations: 156,
  totalCancellations: 12,
  successRate: 92.3,
  averageAllocationTime: 4.5,
};

// 门店选项
export const mockStoreOptions = [
  { label: '北京门店', value: '北京门店' },
  { label: '上海门店', value: '上海门店' },
  { label: '深圳门店', value: '深圳门店' },
  { label: '广州门店', value: '广州门店' },
];

// 销售顾问选项（按门店分组）
export const mockSalesConsultantOptions: Record<string, Array<{label: string, value: string}>> = {
  '北京门店': [
    { label: '李销售', value: '李销售' },
    { label: '赵销售', value: '赵销售' },
  ],
  '上海门店': [
    { label: '王销售', value: '王销售' },
  ],
  '深圳门店': [
    { label: '孙销售', value: '孙销售' },
  ],
  '广州门店': [
    { label: '周销售', value: '周销售' },
  ],
};

// 配车状态选项
export const mockAllocationStatusOptions = [
  { label: '已配车', value: 'allocated' },
  { label: '未配车', value: 'unallocated' },
];

// 订单状态选项
export const mockOrderStatusOptions = [
  { label: '已提交', value: 'submitted' },
  { label: '已确认', value: 'confirmed' },
  { label: '取消审核中', value: 'cancel_review' },
  { label: '取消审核通过', value: 'cancel_approved' },
  { label: '已取消', value: 'cancelled' },
  { label: '待交车', value: 'ready_delivery' },
  { label: '已交车', value: 'delivered' },
];

// 操作类型选项
export const mockOperationTypeOptions = [
  { label: '配车', value: 'allocate' },
  { label: '取消配车', value: 'cancel_allocate' },
  { label: '查看详情', value: 'view_detail' },
  { label: '系统自动取消', value: 'system_auto_cancel' },
]; 