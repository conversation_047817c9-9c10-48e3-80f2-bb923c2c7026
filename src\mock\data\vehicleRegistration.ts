import type { VehicleRegistrationListItem, VehicleRegistrationDetail } from '@/types/vehicleRegistration.d';

// 模拟车辆登记列表数据
export const mockVehicleRegistrationList: VehicleRegistrationListItem[] = [
  {
    id: '1',
    orderNumber: 'ORD001',
    customerName: '张三',
    customerPhone: '13800138001',
    vin: 'WVWZZZ1JZ3W386752',
    vehicleModel: 'MYVI 1.5L',
    vehicleColor: '蓝色',
    insuranceStatus: 'insured',
    insuranceCompany: '太平洋保险',
    registrationStatus: 'pending',
    pushTime: '',
    registrationFee: 0,
    salesAdvisor: '张顾问'
  },
  {
    id: '2',
    orderNumber: 'ORD002',
    customerName: '李四',
    customerPhone: '13800138002',
    vin: 'WVWZZZ1JZ3W386753',
    vehicleModel: 'AXIA 1.0L',
    vehicleColor: '红色',
    insuranceStatus: 'insured',
    insuranceCompany: '平安保险',
    registrationStatus: 'processing',
    pushTime: '2024-01-15 10:30:00',
    registrationFee: 0,
    salesAdvisor: '李顾问'
  },
  {
    id: '3',
    orderNumber: 'ORD003',
    customerName: '王五',
    customerPhone: '13800138003',
    vin: 'WVWZZZ1JZ3W386754',
    vehicleModel: 'BEZZA 1.3L',
    vehicleColor: '白色',
    insuranceStatus: 'insured',
    insuranceCompany: '中国人寿',
    registrationStatus: 'success',
    pushTime: '2024-01-15 09:00:00',
    registrationFee: 320.00,
    salesAdvisor: '王顾问'
  },
  {
    id: '4',
    orderNumber: 'ORD004',
    customerName: '赵六',
    customerPhone: '13800138004',
    vin: 'WVWZZZ1JZ3W386755',
    vehicleModel: 'VIVA 1.0L',
    vehicleColor: '银色',
    insuranceStatus: 'insured',
    insuranceCompany: '泰康保险',
    registrationStatus: 'failed',
    pushTime: '2024-01-15 11:00:00',
    registrationFee: 0,
    salesAdvisor: '赵顾问'
  },
  {
    id: '5',
    orderNumber: 'ORD005',
    customerName: '孙七',
    customerPhone: '13800138005',
    vin: 'WVWZZZ1JZ3W386756',
    vehicleModel: 'ATIVA 1.5L',
    vehicleColor: '黑色',
    insuranceStatus: 'insured',
    insuranceCompany: '新华保险',
    registrationStatus: 'pending',
    pushTime: '',
    registrationFee: 0,
    salesAdvisor: '孙顾问'
  }
];

// 模拟车辆登记详情数据
export const mockVehicleRegistrationDetails: { [key: string]: VehicleRegistrationDetail } = {
  'ORD001': {
    orderInfo: {
      orderNumber: 'ORD001',
      orderStatus: '已确认',
      pushTime: '',
      createTime: '2024-01-10 14:30:00',
      salesAdvisor: '张顾问'
    },
    customerInfo: {
      name: '张三',
      idType: '身份证',
      idNumber: '330102199001011234',
      phone: '13800138001',
      email: '<EMAIL>',
      address: '杭州市西湖区文三路138号',
      city: '杭州市',
      state: '浙江省',
      postcode: '310013'
    },
    vehicleInfo: {
      vin: 'WVWZZZ1JZ3W386752',
      model: 'MYVI 1.5L',
      color: '蓝色',
      engineNumber: 'EN123456789',
      modelCode: 'BD5VZ',
      variant: '1.5L CVT',
      productionYear: '2024',
      manufactureDate: '2024-01-05'
    },
    insuranceInfo: {
      status: '已投保',
      company: '太平洋保险',
      policyNumber: 'POL2024001234',
      period: '1年',
      date: '2024-01-12',
      fee: 2500.00
    },
    jpjInfo: {
      status: '待登记',
      certificateNumber: '',
      pushTime: '',
      completionTime: '',
      operator: '',
      failureReason: ''
    },
    feeDetails: [],
    operationLogs: [
      {
        operationTime: '2024-01-10 14:30:00',
        operationType: '订单创建',
        operator: '系统',
        result: '成功',
        remark: '订单创建成功'
      }
    ]
  },
  'ORD003': {
    orderInfo: {
      orderNumber: 'ORD003',
      orderStatus: '已确认',
      pushTime: '2024-01-15 09:00:00',
      createTime: '2024-01-12 16:20:00',
      salesAdvisor: '王顾问'
    },
    customerInfo: {
      name: '王五',
      idType: '身份证',
      idNumber: '330102199002021234',
      phone: '13800138003',
      email: '<EMAIL>',
      address: '杭州市下城区延安路188号',
      city: '杭州市',
      state: '浙江省',
      postcode: '310006'
    },
    vehicleInfo: {
      vin: 'WVWZZZ1JZ3W386754',
      model: 'BEZZA 1.3L',
      color: '白色',
      engineNumber: 'EN123456791',
      modelCode: 'BD7VZ',
      variant: '1.3L Auto',
      productionYear: '2024',
      manufactureDate: '2024-01-08'
    },
    insuranceInfo: {
      status: '已投保',
      company: '中国人寿',
      policyNumber: 'POL2024003456',
      period: '1年',
      date: '2024-01-13',
      fee: 2800.00
    },
    jpjInfo: {
      status: '登记成功',
      certificateNumber: 'REG2024001234',
      pushTime: '2024-01-15 09:00:00',
      completionTime: '2024-01-15 15:20:00',
      operator: '李登记员',
      failureReason: ''
    },
    feeDetails: [
      {
        feeType: 'Road Tax',
        feeTypeDisplay: '路税',
        amount: 90.00
      },
      {
        feeType: 'Registration Fee',
        feeTypeDisplay: '注册/过户费',
        amount: 150.00
      },
      {
        feeType: 'HIP/Ownership Claim',
        feeTypeDisplay: '所有权索赔费',
        amount: 50.00
      },
      {
        feeType: 'Interchange Fee',
        feeTypeDisplay: '咨询费',
        amount: 30.00
      }
    ],
    operationLogs: [
      {
        operationTime: '2024-01-12 16:20:00',
        operationType: '订单创建',
        operator: '系统',
        result: '成功',
        remark: '订单创建成功'
      },
      {
        operationTime: '2024-01-15 09:00:00',
        operationType: '推送登记',
        operator: '李登记员',
        result: '成功',
        remark: '推送至JPJ系统'
      },
      {
        operationTime: '2024-01-15 15:20:00',
        operationType: '接收回传',
        operator: '系统',
        result: '成功',
        remark: 'JPJ系统回传登记成功'
      }
    ]
  },
  'ORD004': {
    orderInfo: {
      orderNumber: 'ORD004',
      orderStatus: '已确认',
      pushTime: '2024-01-15 11:00:00',
      createTime: '2024-01-13 10:15:00',
      salesAdvisor: '赵顾问'
    },
    customerInfo: {
      name: '赵六',
      idType: '身份证',
      idNumber: '330102199003031234',
      phone: '13800138004',
      email: '<EMAIL>',
      address: '杭州市江干区庆春路258号',
      city: '杭州市',
      state: '浙江省',
      postcode: '310020'
    },
    vehicleInfo: {
      vin: 'WVWZZZ1JZ3W386755',
      model: 'VIVA 1.0L',
      color: '银色',
      engineNumber: 'EN123456792',
      modelCode: 'BD3VZ',
      variant: '1.0L Manual',
      productionYear: '2024',
      manufactureDate: '2024-01-10'
    },
    insuranceInfo: {
      status: '已投保',
      company: '泰康保险',
      policyNumber: 'POL2024004567',
      period: '1年',
      date: '2024-01-14',
      fee: 2300.00
    },
    jpjInfo: {
      status: '登记失败',
      certificateNumber: '',
      pushTime: '2024-01-15 11:00:00',
      completionTime: '2024-01-15 14:30:00',
      operator: '李登记员',
      failureReason: '购车人身份证信息验证失败，请核实身份证号码是否正确'
    },
    feeDetails: [],
    operationLogs: [
      {
        operationTime: '2024-01-13 10:15:00',
        operationType: '订单创建',
        operator: '系统',
        result: '成功',
        remark: '订单创建成功'
      },
      {
        operationTime: '2024-01-15 11:00:00',
        operationType: '推送登记',
        operator: '李登记员',
        result: '成功',
        remark: '推送至JPJ系统'
      },
      {
        operationTime: '2024-01-15 14:30:00',
        operationType: '接收回传',
        operator: '系统',
        result: '失败',
        remark: 'JPJ系统回传登记失败'
      }
    ]
  }
};

// 销售顾问列表
export const mockSalesAdvisors = [
  { value: '张顾问', label: '张顾问' },
  { value: '李顾问', label: '李顾问' },
  { value: '王顾问', label: '王顾问' },
  { value: '赵顾问', label: '赵顾问' },
  { value: '孙顾问', label: '孙顾问' }
]; 