// 工单审批Mock数据
import type {
  PendingApprovalListItem,
  CompletedApprovalListItem,
  ClaimApprovalDetail,
  CancelApprovalDetail,
  WorkOrderClaimContent,
  WorkOrderApprovalProcess,
  CustomerApprovalInfo,
  VehicleApprovalInfo,
  ApprovalOperationLog
} from '@/types/module'

// 待审批列表Mock数据
export const mockPendingApprovals: PendingApprovalListItem[] = [
  {
    id: '1',
    approvalNo: 'AP001202412100001',
    approvalType: 'claim_approval',
    submitterName: '张三',
    submitTime: '2024-12-10 09:30:00',
    orderNo: 'WO202412100001',
    requestReason: '车辆发动机故障，需要更换零件并申请索赔',
    timeoutStatus: 'about_to_timeout',
    remainingTime: '2小时',
    customerName: '李四',
    licensePlate: '京A12345',
    vehicleModel: '奔驰E300L',
    storeName: '北京4S店',
    currentLevel: 'first_level'
  },
  {
    id: '2',
    approvalNo: 'AP001202412100002',
    approvalType: 'cancel_approval',
    submitterName: '王五',
    submitTime: '2024-12-10 14:15:00',
    orderNo: 'WO202412100002',
    requestReason: '客户临时取消维修需求',
    timeoutStatus: 'normal',
    customerName: '赵六',
    licensePlate: '京B67890',
    vehicleModel: '奥迪A6L',
    storeName: '北京4S店',
    currentLevel: 'first_level'
  },
  {
    id: '3',
    approvalNo: 'AP001202412100003',
    approvalType: 'claim_approval',
    submitterName: '陈七',
    submitTime: '2024-12-09 16:45:00',
    orderNo: 'WO202412090003',
    requestReason: '变速箱异响，需要索赔维修',
    timeoutStatus: 'timeout',
    remainingTime: '已超时8小时',
    customerName: '孙八',
    licensePlate: '沪C11111',
    vehicleModel: 'BMW 520Li',
    storeName: '上海4S店',
    currentLevel: 'second_level'
  },
  {
    id: '4',
    approvalNo: 'AP002202412100004',
    approvalType: 'claim_approval',
    submitterName: '刘九',
    submitTime: '2024-12-10 11:20:00',
    orderNo: 'WO202412100004',
    requestReason: '空调压缩机故障，申请质保索赔',
    timeoutStatus: 'normal',
    customerName: '周十',
    licensePlate: '粤A88888',
    vehicleModel: '凯迪拉克CT6',
    storeName: '广州4S店',
    currentLevel: 'first_level'
  },
  {
    id: '5',
    approvalNo: 'AP003202412100005',
    approvalType: 'cancel_approval',
    submitterName: '吴十一',
    submitTime: '2024-12-10 16:30:00',
    orderNo: 'WO202412100005',
    requestReason: '客户要求延期维修',
    timeoutStatus: 'normal',
    customerName: '郑十二',
    licensePlate: '川A99999',
    vehicleModel: '雷克萨斯ES300h',
    storeName: '成都4S店',
    currentLevel: 'first_level'
  }
]

// 已审批列表Mock数据
export const mockCompletedApprovals: CompletedApprovalListItem[] = [
  {
    id: '6',
    approvalNo: 'AP001202412090001',
    approvalType: 'claim_approval',
    submitterName: '刘九',
    submitTime: '2024-12-09 10:00:00',
    orderNo: 'WO202412090001',
    requestReason: '空调系统故障索赔',
    approvalResult: 'approved',
    approvalRemark: '符合索赔条件，审批通过',
    approvalTime: '2024-12-09 15:30:00',
    approverName: '李经理',
    customerName: '周十',
    licensePlate: '粤A88888',
    vehicleModel: '凯迪拉克CT6',
    storeName: '广州4S店'
  },
  {
    id: '7',
    approvalNo: 'AP001202412090002',
    approvalType: 'cancel_approval',
    submitterName: '吴十一',
    submitTime: '2024-12-09 11:30:00',
    orderNo: 'WO202412090002',
    requestReason: '工单取消申请',
    approvalResult: 'rejected',
    approvalRemark: '工单已开始施工，不允许取消',
    approvalTime: '2024-12-09 13:45:00',
    approverName: '张经理',
    customerName: '郑十二',
    licensePlate: '川A99999',
    vehicleModel: '雷克萨斯ES300h',
    storeName: '成都4S店'
  },
  {
    id: '8',
    approvalNo: 'AP002202412080001',
    approvalType: 'claim_approval',
    submitterName: '马十三',
    submitTime: '2024-12-08 14:20:00',
    orderNo: 'WO202412080001',
    requestReason: '发动机漏油，申请质保维修',
    approvalResult: 'approved',
    approvalRemark: '在质保期内，符合索赔条件',
    approvalTime: '2024-12-08 18:15:00',
    approverName: '王总监',
    customerName: '冯十四',
    licensePlate: '津B12345',
    vehicleModel: '沃尔沃XC90',
    storeName: '天津4S店'
  }
]

// 客户信息Mock数据
const mockCustomerInfo: CustomerApprovalInfo = {
  customerId: 'C001',
  customerName: '李四',
  phone: '13812345678',
  senderName: '李四',
  senderPhone: '13812345678'
}

// 车辆信息Mock数据
const mockVehicleInfo: VehicleApprovalInfo = {
  vehicleId: 'V001',
  licensePlate: '京A12345',
  vin: 'WDDNG7EB1KA123456',
  model: '奔驰E300L',
  configuration: '豪华型',
  color: '极地白',
  saleTime: '2022-06-15',
  mileage: 25000,
  vehicleAge: 30,
  serviceTime: '2024-12-10 08:00:00'
}

// 索赔工时Mock数据
const mockClaimLaborList: WorkOrderClaimContent[] = [
  {
    claimId: 'CL001',
    orderId: 'WO202412100001',
    claimType: 'labor_claim',
    itemCode: 'L001',
    itemName: '发动机检修',
    quantity: 2.5,
    unitPrice: 120,
    claimAmount: 300,
    createTime: '2024-12-10 09:00:00'
  },
  {
    claimId: 'CL002',
    orderId: 'WO202412100001',
    claimType: 'labor_claim',
    itemCode: 'L002',
    itemName: '零件更换',
    quantity: 1.0,
    unitPrice: 120,
    claimAmount: 120,
    createTime: '2024-12-10 09:00:00'
  }
]

// 索赔零件Mock数据
const mockClaimPartsList: WorkOrderClaimContent[] = [
  {
    claimId: 'CP001',
    orderId: 'WO202412100001',
    claimType: 'parts_claim',
    itemCode: 'P001',
    itemName: '发动机皮带',
    quantity: 1,
    unitPrice: 280,
    claimAmount: 280,
    createTime: '2024-12-10 09:00:00'
  },
  {
    claimId: 'CP002',
    orderId: 'WO202412100001',
    claimType: 'parts_claim',
    itemCode: 'P002',
    itemName: '机油滤清器',
    quantity: 1,
    unitPrice: 65,
    claimAmount: 65,
    createTime: '2024-12-10 09:00:00'
  }
]

// 审批过程Mock数据
const mockApprovalProcessList: WorkOrderApprovalProcess[] = [
  {
    processId: 'PROC001',
    approvalNo: 'AP001202412100001',
    approvalLevel: 'first_level',
    approverId: 'U001',
    approverName: '李经理',
    approvalTime: '2024-12-10 10:45:00',
    approvalResult: 'approved',
    approvalRemark: '符合索赔条件，一级审批通过',
    isOvertime: false,
    createTime: '2024-12-10 09:30:00'
  }
]

// 索赔审批详情Mock数据
export const mockClaimApprovalDetail: ClaimApprovalDetail = {
  approvalNo: 'AP001202412100001',
  approvalType: 'claim_approval',
  submitterName: '张三（工号：E001）',
  submitTime: '2024-12-10 09:30:00',
  orderNo: 'WO202412100001',
  requestReason: '车辆发动机故障，需要更换零件并申请索赔',
  customerInfo: mockCustomerInfo,
  vehicleInfo: mockVehicleInfo,
  claimLaborList: mockClaimLaborList,
  claimPartsList: mockClaimPartsList,
  claimLaborTotal: 420,
  claimPartsTotal: 345,
  claimTotalAmount: 765,
  approvalStatus: 'pending_review',
  currentLevel: 'first_level',
  approvalProcessList: mockApprovalProcessList
}

// 取消审批详情Mock数据
export const mockCancelApprovalDetail: CancelApprovalDetail = {
  approvalNo: 'AP001202412100002',
  approvalType: 'cancel_approval',
  submitterName: '王五（工号：E002）',
  submitTime: '2024-12-10 14:15:00',
  orderNo: 'WO202412100002',
  cancelReason: '客户临时取消维修需求',
  customerInfo: {
    customerId: 'C002',
    customerName: '赵六',
    phone: '13987654321',
    senderName: '赵六',
    senderPhone: '13987654321'
  },
  vehicleInfo: {
    vehicleId: 'V002',
    licensePlate: '京B67890',
    vin: 'WAUENF8E5KA789012',
    model: '奥迪A6L',
    configuration: '技术型',
    color: '星空蓝',
    saleTime: '2023-03-20',
    mileage: 15000,
    vehicleAge: 21,
    serviceTime: '2024-12-10 13:00:00'
  },
  currentOrderStatus: 'pending_assign',
  estimatedStartTime: '2024-12-11 09:00:00',
  assignedTechnicianName: '技师A',
  approvalStatus: 'pending_review',
  approvalProcessList: []
}

// 操作日志Mock数据
export const mockOperationLogs: ApprovalOperationLog[] = [
  {
    logId: 'LOG001',
    operationType: '提交索赔审批',
    operator: '张三',
    operationTime: '2024-12-10 09:30:00',
    operationRemark: '提交索赔审批申请'
  },
  {
    logId: 'LOG002',
    operationType: '一级审批通过',
    operator: '李经理',
    operationTime: '2024-12-10 10:45:00',
    operationRemark: '符合索赔条件，审批通过'
  },
  {
    logId: 'LOG003',
    operationType: '二级审批待处理',
    operator: '系统',
    operationTime: '2024-12-10 10:46:00',
    operationRemark: '自动分配给厂端管理人员进行二级审批'
  }
]

// 导出默认数据
export default {
  pendingApprovals: mockPendingApprovals,
  completedApprovals: mockCompletedApprovals,
  claimApprovalDetails: [mockClaimApprovalDetail],
  cancelApprovalDetails: [mockCancelApprovalDetail],
  operationLogs: mockOperationLogs
}
