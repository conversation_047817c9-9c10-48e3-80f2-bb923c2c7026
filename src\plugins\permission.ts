import type { App, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * 权限指令
 * 用法：
 * v-permission="'system:user:list'" - 单个权限
 * v-permission="['system:user:list', 'system:user:view']" - 多个权限（满足其一即可）
 */
export const permission = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (value) {
      const hasPermission = Array.isArray(value)
        ? value.some(permission => authStore.hasPermission(permission))
        : authStore.hasPermission(value)

      if (!hasPermission) {
        el.parentNode?.removeChild(el)
      }
    }
  }
}

/**
 * 角色指令
 * 用法：
 * v-role="'admin'" - 单个角色
 * v-role="['admin', 'manager']" - 多个角色（满足其一即可）
 */
export const role = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (value) {
      const hasRole = Array.isArray(value)
        ? value.some(role => authStore.hasRole(role))
        : authStore.hasRole(value)

      if (!hasRole) {
        el.parentNode?.removeChild(el)
      }
    }
  }
}

/**
 * 菜单权限指令
 * 用法：
 * v-menu="'system:user'" - 检查是否有对应菜单权限
 */
export const menu = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (value) {
      const hasMenu = authStore.hasMenu(value)

      if (!hasMenu) {
        el.parentNode?.removeChild(el)
      }
    }
  }
}

export default {
  install(app: App) {
    app.directive('permission', permission)
    app.directive('role', role)
    app.directive('menu', menu)
  }
}
