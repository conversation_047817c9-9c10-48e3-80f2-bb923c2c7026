import type { RouteRecordRaw } from 'vue-router'

// 基础模块路由配置
export const baseRoutes: RouteRecordRaw[] = [
  // 门店管理
  {
    path: '/base/store',
    name: 'base-store',
    component: () => import('@/views/base/store/StoreView.vue'),
    meta: {
      title: 'menu.store',
      icon: 'OfficeBuilding',
      requiresAuth: true,
      permissions: ['base:store:view']
    }
  }
]

export default baseRoutes
