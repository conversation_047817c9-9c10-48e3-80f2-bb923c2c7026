import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login as loginApi, logout as logoutApi, getUserInfo as getUserInfoApi, type LoginRequest, type UserInfo } from '@/api/modules/auth'
import UserStorage from '@/utils/user-storage'

export const useAuthStore = defineStore('auth', () => {
  // 获取路由实例用于页面跳转
  const router = useRouter()

  // 状态
  const token = ref<string | null>(UserStorage.getToken())
  const refreshToken = ref<string | null>(UserStorage.getRefreshToken())
  const userInfo = ref<UserInfo | null>(UserStorage.getUserInfo())
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRoles = computed(() => userInfo.value?.roles || [])
  const userPermissions = computed(() => userInfo.value?.permissions || [])

  // 设置Token
  const setToken = (newToken: string, newRefreshToken?: string) => {
    console.log('设置Token:', newToken)
    console.log('设置RefreshToken:', newRefreshToken)

    token.value = newToken
    UserStorage.setToken(newToken)

    if (newRefreshToken) {
      refreshToken.value = newRefreshToken
      UserStorage.setRefreshToken(newRefreshToken)
    }

    console.log('Token已保存到localStorage')
  }

  // 清除Token
  const clearToken = () => {
    token.value = null
    refreshToken.value = null
    userInfo.value = null
    UserStorage.clearAll()
  }

  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    UserStorage.setUserInfo(info)
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      loading.value = true

      const response = await loginApi(loginData)

      if (response.code === '200' || response.code === 200) {

        const { accessToken: newToken, refreshToken: newRefreshToken, userInfo: user } = response.result

        // 保存token和用户信息
        setToken(newToken, newRefreshToken)
        setUserInfo(user)

        ElMessage.success('登录成功')
        return Promise.resolve(response)
      } else {
        console.error('登录失败，响应码:', response.code, '消息:', response.message)
        ElMessage.error(response.message || '登录失败')
        return Promise.reject(new Error(response.message || '登录失败'))
      }
    } catch (error: unknown) {
      console.error('登录异常:', error)
      const errorMessage = error instanceof Error ? error.message : '登录失败'
      ElMessage.error(errorMessage)
      return Promise.reject(error)
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      loading.value = true
      await logoutApi()
      clearToken()
      ElMessage.success('退出成功')
      router.push({ name: 'login' }) // 退出成功后跳转到登录页
    } catch (error) {
      console.error('退出登录失败:', error)
      // 即使退出登录接口失败，也要清除本地token
      clearToken()
      router.push({ name: 'login' }) // 即使失败也要尝试跳转到登录页
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      if (!token.value) {
        return Promise.reject(new Error('未登录'))
      }

      const response = await getUserInfoApi()
      if (response.code === '200' || response.code === 200) {
        setUserInfo(response.result)
        return Promise.resolve(response.result)
      } else {
        // 获取用户信息失败，清除token
        clearToken()
        return Promise.reject(new Error(response.message || '获取用户信息失败'))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      clearToken()
      return Promise.reject(error)
    }
  }

  // 检查权限
  const hasPermission = (permission: string) => {
    if (!userInfo.value) return false

    // 如果有超级管理员权限，直接返回true
    if (userPermissions.value.includes('*:*:*')) return true

    // 检查具体权限
    return userPermissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role: string) => {
    if (!userInfo.value) return false
    return userRoles.value.includes(role)
  }

  // 检查菜单权限
  const hasMenu = (menuCode: string) => {
    if (!userInfo.value || !userInfo.value.menus) return false
    return userInfo.value.menus.some(menu => menu.menuCode === menuCode)
  }

  // 初始化认证状态（页面刷新时调用）
  const initAuth = async () => {
    // 从localStorage加载用户信息
    const storedUserInfo = UserStorage.getUserInfo()
    if (storedUserInfo) {
      userInfo.value = storedUserInfo
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    if (token.value && !userInfo.value) {
      try {
        await getUserInfo()
      } catch (error) {
        console.error('初始化认证状态失败:', error)
        clearToken()
      }
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    userInfo,
    loading,

    // 计算属性
    isLoggedIn,
    userRoles,
    userPermissions,

    // 方法
    setToken,
    clearToken,
    setUserInfo,
    login,
    logout,
    getUserInfo,
    hasPermission,
    hasRole,
    hasMenu,
    initAuth
  }
})
