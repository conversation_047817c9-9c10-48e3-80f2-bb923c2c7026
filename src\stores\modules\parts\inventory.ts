
import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import type {
  InventoryDashboard,
  InventoryItem,
  InventorySearchParams,
  InventoryDetail,
} from '@/types/parts/inventory';
import {
  getInventoryDashboard,
  getInventoryList,
  getInventoryDetail as fetchDetailApi,
} from '@/api/modules/parts/inventory';
import { useI18n } from 'vue-i18n';

export const useInventoryStore = defineStore('parts-inventory', () => {
  const { t } = useI18n();

  // State
  const dashboardData = ref<InventoryDashboard | null>(null);
  const inventoryList = ref<InventoryItem[]>([]);
  const currentDetail = ref<InventoryDetail | null>(null);
  const loading = reactive({
    dashboard: false,
    list: false,
    detail: false,
    trend: false,
  });
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
  });

  // Actions
  const fetchDashboard = async (storeId: number) => {
    loading.dashboard = true;
    try {
      dashboardData.value = await getInventoryDashboard({ storeId });
    } catch (error) {
      ElMessage.error(t('common.loadFailed'));
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      loading.dashboard = false;
    }
  };

  const fetchInventoryList = async (params: { storeId: number } & Partial<Omit<InventorySearchParams, 'page' | 'size' | 'storeId'>>) => {
    loading.list = true;
    try {
      const searchParams: InventorySearchParams = {
        ...params,
        page: pagination.page,
        size: pagination.pageSize,
      };
      const response = await getInventoryList(searchParams);
      inventoryList.value = response.data;
      pagination.total = response.pagination.totalCount;
    } catch (error) {
      ElMessage.error(t('common.loadFailed'));
      console.error('Failed to fetch inventory list:', error);
    } finally {
      loading.list = false;
    }
  };

  const fetchInventoryDetail = async (inventoryId: number) => {
    loading.detail = true;
    try {
      currentDetail.value = await fetchDetailApi({ inventoryId });
    } catch (error) {
      ElMessage.error(t('common.loadFailed'));
      console.error(`Failed to fetch inventory detail for id ${inventoryId}:`, error);
    } finally {
      loading.detail = false;
    }
  };

  const setPage = (page: number) => {
    pagination.page = page;
  };

  const setPageSize = (pageSize: number) => {
    pagination.pageSize = pageSize;
  };

  return {
    // State
    dashboardData,
    inventoryList,
    currentDetail,
    loading,
    pagination,
    // Actions
    fetchDashboard,
    fetchInventoryList,
    fetchInventoryDetail,
    setPage,
    setPageSize,
  };
});
