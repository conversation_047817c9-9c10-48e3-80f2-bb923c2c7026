// 经销商端采购管理状态管理

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import {
  getDealerDashboard,
  getDealerOrderList,
  getDealerOrderDetail,
  saveDealerOrder,
  submitDealerOrder,
  cancelDealerOrder,
  deleteDealerOrder,
  getPartsForSelection,
  getPartInventory,
  getDealerReceiptInfo,
  confirmDealerReceipt,
  getDealerWarehouses,
  getWarehouseLocations,
  getOrderStatusHistory
} from '@/api/modules/parts/purchase-dealer';
import type {
  DealerDashboard,
  PurchaseOrder,
  PurchaseOrderItem,
  ShipmentOrder,
  PartForSelection,
  PurchaseOrderForm,
  ReceiptForm,
  DealerOrderListParams,
  PurchaseOrderStatus
} from '@/types/parts/purchase-dealer';
import { ElMessage } from 'element-plus';

export const usePurchaseDealerStore = defineStore('purchase-dealer', () => {
  // 状态数据
  const dashboardData = ref<DealerDashboard | null>(null);
  const orderList = ref<PurchaseOrder[]>([]);
  const currentOrder = ref<PurchaseOrder | null>(null);
  const currentOrderItems = ref<PurchaseOrderItem[]>([]);
  const shipmentList = ref<ShipmentOrder[]>([]);
  const partsList = ref<PartForSelection[]>([]);
  const warehouseList = ref<any[]>([]);
  const locationList = ref<any[]>([]);
  const statusHistory = ref<any[]>([]);

  // 分页信息
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });

  // 加载状态
  const loading = ref({
    dashboard: false,
    list: false,
    detail: false,
    save: false,
    parts: false,
    receipt: false
  });

  // 筛选条件
  const filters = ref({
    query: '',
    status: [] as PurchaseOrderStatus[],
    dateRange: [] as string[]
  });

  // Getters
  const statusCounts = computed(() => {
    if (!dashboardData.value) return {};
    return {
      pendingApproval: dashboardData.value.pendingApprovalCount,
      inTransit: dashboardData.value.inTransitCount,
      pendingReceipt: dashboardData.value.pendingReceiptCount
    };
  });

  const filteredOrders = computed(() => {
    let filtered = [...orderList.value];
    
    if (filters.value.query) {
      const query = filters.value.query.toLowerCase();
      filtered = filtered.filter(order => 
        order.orderNo.toLowerCase().includes(query) ||
        order.dealerName.toLowerCase().includes(query)
      );
    }
    
    if (filters.value.status.length > 0) {
      filtered = filtered.filter(order => 
        filters.value.status.includes(order.status)
      );
    }
    
    return filtered;
  });

  // Actions

  /**
   * 获取仪表盘数据
   */
  const fetchDashboard = async (dealerId: number) => {
    loading.value.dashboard = true;
    try {
      const response = await getDealerDashboard(dealerId);
      dashboardData.value = response.data;
    } catch (error) {
      console.error('Failed to fetch dashboard:', error);
      ElMessage.error('获取仪表盘数据失败');
    } finally {
      loading.value.dashboard = false;
    }
  };

  /**
   * 获取订单列表
   */
  const fetchOrderList = async (params: DealerOrderListParams) => {
    loading.value.list = true;
    try {
      const response = await getDealerOrderList(params);
      orderList.value = response.data.data;
      pagination.value = {
        page: response.data.pagination.currentPage,
        pageSize: response.data.pagination.pageSize,
        total: response.data.pagination.totalCount,
        totalPages: response.data.pagination.totalPages
      };
    } catch (error) {
      console.error('Failed to fetch order list:', error);
      ElMessage.error('获取订单列表失败');
    } finally {
      loading.value.list = false;
    }
  };

  /**
   * 获取订单详情
   */
  const fetchOrderDetail = async (orderId: number) => {
    loading.value.detail = true;
    try {
      const response = await getDealerOrderDetail(orderId);
      currentOrder.value = response.data.order;
      currentOrderItems.value = response.data.items;
    } catch (error) {
      console.error('Failed to fetch order detail:', error);
      ElMessage.error('获取订单详情失败');
    } finally {
      loading.value.detail = false;
    }
  };

  /**
   * 保存订单（草稿或更新）
   */
  const saveOrder = async (formData: PurchaseOrderForm) => {
    loading.value.save = true;
    try {
      const params = {
        orderId: formData.orderId,
        dealerId: 1001, // 从用户信息获取
        warehouseId: formData.warehouseId,
        remarks: formData.remarks,
        items: formData.items.map(item => ({
          partId: item.partId,
          quantity: item.orderQuantity
        }))
      };
      
      const response = await saveDealerOrder(params);
      ElMessage.success(formData.orderId ? '订单更新成功' : '订单保存成功');
      return response.data.orderId;
    } catch (error) {
      console.error('Failed to save order:', error);
      ElMessage.error('保存订单失败');
      throw error;
    } finally {
      loading.value.save = false;
    }
  };

  /**
   * 提交订单审核
   */
  const submitOrder = async (orderId: number) => {
    try {
      await submitDealerOrder({ orderId });
      ElMessage.success('订单已提交审核');
      
      // 更新本地订单状态
      const order = orderList.value.find(o => o.orderId === orderId);
      if (order) {
        order.status = 'PENDING_APPROVAL';
      }
    } catch (error) {
      console.error('Failed to submit order:', error);
      ElMessage.error('提交订单失败');
      throw error;
    }
  };

  /**
   * 取消订单
   */
  const cancelOrder = async (orderId: number) => {
    try {
      await cancelDealerOrder({ orderId });
      ElMessage.success('订单已取消');
      
      // 更新本地订单状态
      const order = orderList.value.find(o => o.orderId === orderId);
      if (order) {
        order.status = 'CANCELLED';
      }
    } catch (error) {
      console.error('Failed to cancel order:', error);
      ElMessage.error('取消订单失败');
      throw error;
    }
  };

  /**
   * 删除草稿订单
   */
  const deleteOrder = async (orderId: number) => {
    try {
      await deleteDealerOrder(orderId);
      ElMessage.success('订单已删除');
      
      // 从本地列表移除
      const index = orderList.value.findIndex(o => o.orderId === orderId);
      if (index > -1) {
        orderList.value.splice(index, 1);
      }
    } catch (error) {
      console.error('Failed to delete order:', error);
      ElMessage.error('删除订单失败');
      throw error;
    }
  };

  /**
   * 获取配件选择列表
   */
  const fetchPartsForSelection = async (params: {
    dealerId: number;
    page: number;
    size: number;
    query?: string;
    categoryId?: string;
  }) => {
    loading.value.parts = true;
    try {
      const response = await getPartsForSelection(params);
      partsList.value = response.data.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch parts:', error);
      ElMessage.error('获取配件列表失败');
      throw error;
    } finally {
      loading.value.parts = false;
    }
  };

  /**
   * 获取配件库存信息
   */
  const fetchPartInventory = async (dealerId: number, partIds: string[]) => {
    try {
      const response = await getPartInventory({ dealerId, partIds });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch part inventory:', error);
      throw error;
    }
  };

  /**
   * 获取收货信息
   */
  const fetchReceiptInfo = async (orderId: number) => {
    loading.value.receipt = true;
    try {
      const response = await getDealerReceiptInfo({ orderId });
      shipmentList.value = response.data.shipments;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch receipt info:', error);
      ElMessage.error('获取收货信息失败');
      throw error;
    } finally {
      loading.value.receipt = false;
    }
  };

  /**
   * 确认收货
   */
  const confirmReceipt = async (receiptData: ReceiptForm) => {
    try {
      const params = {
        shipmentId: receiptData.shipmentId,
        receiptDate: receiptData.receiptDate,
        handler: receiptData.handler,
        items: receiptData.items.map(item => ({
          partId: item.partId,
          receivedQuantity: item.receivedQuantity,
          locationId: item.locationId
        }))
      };
      
      await confirmDealerReceipt(params);
      ElMessage.success('收货确认成功');
    } catch (error) {
      console.error('Failed to confirm receipt:', error);
      ElMessage.error('收货确认失败');
      throw error;
    }
  };

  /**
   * 获取仓库列表
   */
  const fetchWarehouses = async (dealerId: number) => {
    try {
      const response = await getDealerWarehouses(dealerId);
      warehouseList.value = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch warehouses:', error);
      throw error;
    }
  };

  /**
   * 获取库位列表
   */
  const fetchLocations = async (warehouseId: number) => {
    try {
      const response = await getWarehouseLocations(warehouseId);
      locationList.value = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch locations:', error);
      throw error;
    }
  };

  /**
   * 获取订单状态历史
   */
  const fetchStatusHistory = async (orderId: number) => {
    try {
      const response = await getOrderStatusHistory(orderId);
      statusHistory.value = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch status history:', error);
      throw error;
    }
  };

  /**
   * 设置筛选条件
   */
  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    filters.value = { ...filters.value, ...newFilters };
  };

  /**
   * 重置筛选条件
   */
  const resetFilters = () => {
    filters.value = {
      query: '',
      status: [],
      dateRange: []
    };
  };

  /**
   * 设置分页
   */
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.page = page;
    pagination.value.pageSize = pageSize;
  };

  /**
   * 清空当前订单
   */
  const clearCurrentOrder = () => {
    currentOrder.value = null;
    currentOrderItems.value = [];
  };

  return {
    // 状态
    dashboardData,
    orderList,
    currentOrder,
    currentOrderItems,
    shipmentList,
    partsList,
    warehouseList,
    locationList,
    statusHistory,
    pagination,
    loading,
    filters,
    
    // 计算属性
    statusCounts,
    filteredOrders,
    
    // 方法
    fetchDashboard,
    fetchOrderList,
    fetchOrderDetail,
    saveOrder,
    submitOrder,
    cancelOrder,
    deleteOrder,
    fetchPartsForSelection,
    fetchPartInventory,
    fetchReceiptInfo,
    confirmReceipt,
    fetchWarehouses,
    fetchLocations,
    fetchStatusHistory,
    setFilters,
    resetFilters,
    setPagination,
    clearCurrentOrder
  };
});