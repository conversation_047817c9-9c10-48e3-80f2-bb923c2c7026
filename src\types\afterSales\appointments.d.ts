// src/types/afterSales/appointments.d.ts

export interface MaintenanceItem {
  code: string;
  name: string;
  quantity: number;
  price: number;
}

export interface AppointmentListItem {
  id: string;
  licensePlate: string;
  reservationContactName: string;
  reservationContactPhone: string;
  serviceContactName: string;
  serviceContactPhone: string;
  appointmentTime: string;
  timeSlot: string;
  serviceType: 'maintenance' | 'repair';
  status: 'arrived' | 'not_arrived' | 'cancelled' | 'pending_payment';
  serviceAdvisor?: { id: string; name: string };
  qualityInspectionId?: string;
  createdAt: string;
  inspectionCreated?: boolean;
  customerDescription?: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  mileage?: number;
  productionDate?: string;
}

export interface AppointmentDetail extends AppointmentListItem {
  store: {
    id: string;
    name: string;
    address: string;
  };
  paymentStatus?: 'paid' | 'unpaid' | 'refunded';
  paymentAmount?: number;
  paymentOrderNumber?: string;
  maintenancePackage?: {
    items: MaintenanceItem[];
    totalAmount: number;
  };
}

export interface AppointmentListParams {
  appointmentId?: string;
  licensePlate?: string;
  reservationPhone?: string;
  servicePhone?: string;
  dateRange?: [string, string];
  status?: string;
  serviceType?: string;
  serviceAdvisorId?: string;
  technicianId?: string;
  page?: number;
  pageSize?: number;
}

export interface AppointmentPageResponse {
  list: AppointmentListItem[];
  total: number;
}
