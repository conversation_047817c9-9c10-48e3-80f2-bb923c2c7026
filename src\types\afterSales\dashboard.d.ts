// src/types/afterSales/dashboard.d.ts

export interface DashboardAppointmentItem {
  plateNumber: string;
  appointmentDate: string;
  timeSlot: string;
  serviceType: 'maintenance' | 'repair';
  status: 'arrived' | 'notArrived' | 'notFulfilled';
}

export interface DashboardStats {
  totalAppointments: number;
  arrivedCount: number;
  notArrivedCount: number;
  notFulfilledCount: number;
  arrivalRate: number;
  tomorrowCount: number;
}

export interface DashboardSearchParams {
  date?: string;
  filterType?: 'all' | 'notArrived' | 'tomorrow';
}

export interface DashboardResponse {
  appointments: DashboardAppointmentItem[];
  stats: DashboardStats;
}
