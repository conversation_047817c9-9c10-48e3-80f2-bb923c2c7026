// 车间派工相关类型定义

// 基础类型定义
export type WorkOrderPriority = 'urgent' | 'normal';
export type WorkOrderType = 'repair' | 'maintenance' | 'insurance';
export type WorkOrderStatus = 'pendingAssignment' | 'pendingStart' | 'inProgress' | 'paused' | 'completed' | 'cancelled' | 'pendingQualityInspection';
export type AssignmentStatus = 'pending' | 'assigned';
export type CustomerSource = 'appointment' | 'walkIn';
export type TechnicianStatus = 'available' | 'busy' | 'offline';

// 工单列表项接口
export interface DispatchListItem {
  id: number;
  workOrderNo: string;
  priority: WorkOrderPriority;
  workOrderType: WorkOrderType;
  workOrderStatus: WorkOrderStatus;
  creationTime: string;
  customerSource: CustomerSource;
  repairmanName: string;
  repairmanPhone: string;
  licensePlateNumber: string;
  vehicleModel: string;
  configuration: string;
  color: string;
  vehicleAge: number;
  mileage: number;
  serviceAdvisor: string;
  assignmentStatus: AssignmentStatus;
  technician: string;
  estimatedWorkHours: number;
  estimatedStartTime: string;
  estimatedFinishTime: string;
  actualStartTime: string;
  actualFinishTime: string;
  isPaused: boolean;
  pauseReason?: string;
  completionTime?: string;
  qualityInspector?: string;
  qualityInspectionTime?: string;
  notes?: string;
}

// 搜索参数接口
export interface DispatchSearchParams {
  page: number;
  pageSize: number;
  workOrderNo?: string;
  priority?: WorkOrderPriority;
  workOrderType?: WorkOrderType;
  assignmentStatus?: AssignmentStatus;
  workOrderStatus?: WorkOrderStatus;
  creationTimeStart?: string;
  creationTimeEnd?: string;
  customerSource?: CustomerSource;
  repairmanName?: string;
  licensePlateNumber?: string;
  serviceAdvisor?: string;
  technician?: string;
}

// 技师信息接口
export interface TechnicianInfo {
  id: string;
  name: string;
  department: string;
  status: TechnicianStatus;
  currentWorkload: number;
  maxWorkload: number;
  skills: string[];
  workingHours: {
    start: string;
    end: string;
  };
  currentWorkOrders: string[];
  efficiency: number;
  experience: number;
}

// 分配表单接口
export interface AssignmentFormData {
  workOrderNo: string;
  technicianId: string;
  estimatedStartTime: string;
  estimatedFinishTime: string;
  estimatedWorkHours: number;
  notes: string;
}

// 重新分配表单接口
export interface ReassignmentFormData {
  workOrderNo: string;
  originalTechnician: string;
  newTechnicianId: string;
  estimatedStartTime: string;
  estimatedFinishTime: string;
  estimatedWorkHours: number;
  reason: string;
  notes: string;
}

// 工单详情接口
export interface WorkOrderDetail {
  id: number;
  workOrderNo: string;
  priority: WorkOrderPriority;
  workOrderType: WorkOrderType;
  workOrderStatus: WorkOrderStatus;
  creationTime: string;
  customerInfo: {
    name: string;
    phone: string;
    source: CustomerSource;
  };
  vehicleInfo: {
    licensePlateNumber: string;
    model: string;
    configuration: string;
    color: string;
    age: number;
    mileage: number;
  };
  serviceInfo: {
    advisor: string;
    description: string;
    symptoms: string[];
    diagnosis: string;
  };
  assignmentInfo: {
    status: AssignmentStatus;
    technician: string;
    estimatedWorkHours: number;
    estimatedStartTime: string;
    estimatedFinishTime: string;
    actualStartTime: string;
    actualFinishTime: string;
    notes: string;
  };
  progressInfo: {
    isPaused: boolean;
    pauseReason?: string;
    completionTime?: string;
    qualityInspector?: string;
    qualityInspectionTime?: string;
    workLog: WorkLogEntry[];
  };
}

// 工作日志条目接口
export interface WorkLogEntry {
  id: string;
  timestamp: string;
  operator: string;
  action: string;
  description: string;
  attachments?: string[];
}

// 分页响应接口
export interface PaginationResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

// 统计信息接口
export interface DispatchStatistics {
  totalWorkOrders: number;
  pendingAssignment: number;
  inProgress: number;
  completed: number;
  paused: number;
  averageCompletionTime: number;
  technicianUtilization: number;
}

// 技师工作负载接口
export interface TechnicianWorkload {
  technicianId: string;
  technicianName: string;
  currentWorkOrders: number;
  maxCapacity: number;
  utilizationRate: number;
  estimatedAvailableTime: string;
}

// 派工选项接口
export interface DispatchOption {
  label: string;
  value: string;
}

// 批量操作接口
export interface BatchOperationData {
  workOrderNos: string[];
  operation: 'assign' | 'reassign' | 'pause' | 'resume' | 'complete';
  technicianId?: string;
  reason?: string;
  notes?: string;
}

// 导出参数接口
export interface ExportDispatchParams extends DispatchSearchParams {
  exportType: 'all' | 'selected';
  exportFormat: 'excel' | 'pdf';
  selectedIds?: number[];
}
