// src/types/afterSales/inspection.d.ts

// 环检单状态枚举
export type InspectionStatus =
  | 'pending'         // 待处理
  | 'in_progress'     // 检查中
  | 'pending_confirm' // 待客户确认
  | 'confirmed';      // 已确认

// 登记类型枚举
export type RegisterType = 'appointment' | 'walk_in'; // 预约 | 到店

// 服务类型枚举
export type ServiceType = 'maintenance' | 'repair'; // 保养 | 维修

// 环检单列表项接口
export interface InspectionListItem {
  inspectionNo: string;
  inspectionStatus: InspectionStatus;
  repairmanName: string;
  repairmanPhone: string;
  licensePlateNo: string;
  vehicleModel: string;
  vehicleConfig: string;
  color: string;
  mileage: number;
  vehicleAge: number;
  serviceAdvisor: string;
  technician: string;
  registerType: RegisterType;
  serviceType: ServiceType;
  customerConfirmTime: string;
  createTime: string;
  updateTime: string;
  inspectionContent?: any;
}

// 环检单详情接口
export interface InspectionDetail extends InspectionListItem {
  inspectionContent: any; // 检查内容详情
}

// 环检单搜索参数接口
export interface InspectionSearchParams {
  inspectionNo?: string;
  inspectionStatus?: string;
  licensePlateNo?: string;
  repairmanName?: string;
  technician?: string;
  repairmanPhone?: string;
  createTimeRange?: [string, string] | null;
  page?: number;
  pageSize?: number;
}

// 环检单分页响应接口
export interface InspectionPageResponse {
  list: InspectionListItem[];
  total: number;
}

// 分配技师表单接口
export interface AssignTechnicianForm {
  inspectionNo: string;
  licensePlateNo: string;
  repairmanName: string;
  registerType: string;
  serviceType: string;
  technicianId: string;
}

// 客户确认表单接口
export interface CustomerConfirmForm {
  inspectionNo: string;
  confirmTime: string;
  customerSignature?: string;
  remarks?: string;
}
