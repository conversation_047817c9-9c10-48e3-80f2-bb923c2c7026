// 质量管理相关类型定义

// 基础类型定义
export type QualityCheckStatus = 'pending_check' | 'checking' | 'pending_review' | 'passed' | 'rework';
export type QualityCheckAuditResult = 'passed' | 'rework';
export type QualityCheckItemType = 'BOOLEAN' | 'NUMERIC' | 'TEXT';
export type QualityCheckResult = 'PASS' | 'FAIL';
export type WorkOrderType = 'maintenance' | 'repair' | 'insurance';

// 质检单列表项接口
export interface QualityCheckListItem {
  id: string;
  qualityCheckNo: string;
  status: QualityCheckStatus;
  workOrderNo: string;
  workOrderType: WorkOrderType;
  isClaimRelated: boolean;
  isOutsourceRelated: boolean;
  serviceCustomerName: string;
  serviceCustomerPhone: string;
  plateNumber: string;
  vehicleModel: string;
  vehicleConfig: string;
  vehicleColor: string;
  technicianName: string;
  startTime?: string;
  finishTime?: string;
  estimatedHours?: number;
  actualHours?: number;
  reworkReason?: string;
  reworkRequirement?: string;
  createTime: string;
  updateTime: string;
}

// 搜索参数接口
export interface QualityCheckSearchParams {
  page: number;
  pageSize: number;
  workOrderNo?: string;
  qualityCheckNo?: string;
  status?: QualityCheckStatus[];
  workOrderType?: WorkOrderType;
  technicianName?: string;
  plateNumber?: string;
  serviceCustomerName?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  isClaimRelated?: boolean;
  isOutsourceRelated?: boolean;
}

// 质检项目接口
export interface QualityCheckItem {
  id: string;
  qualityCheckId: string;
  categoryCode: string;
  categoryName: string;
  itemCode: string;
  itemName: string;
  itemType: QualityCheckItemType;
  checkResult?: QualityCheckResult;
  numericValue?: number;
  textValue?: string;
  standardValue?: string;
  unit?: string;
  isRequired: boolean;
  sortOrder: number;
  createTime: string;
  updateTime: string;
}

// 质检项目表单接口
export interface QualityCheckItemForm {
  id?: string;
  categoryCode: string;
  categoryName: string;
  itemCode: string;
  itemName: string;
  itemType: QualityCheckItemType;
  checkResult?: QualityCheckResult;
  numericValue?: number;
  textValue?: string;
  standardValue?: string;
  unit?: string;
  isRequired: boolean;
  sortOrder: number;
}

// 工单信息接口
export interface WorkOrderInfo {
  workOrderNo: string;
  workOrderType: WorkOrderType;
  description: string;
  symptoms: string[];
  diagnosis: string;
  serviceAdvisor: string;
  estimatedHours: number;
  actualHours?: number;
}

// 车辆信息接口
export interface VehicleInfo {
  plateNumber: string;
  vehicleModel: string;
  vehicleConfig: string;
  vehicleColor: string;
  vin: string;
  mileage: number;
  vehicleAge: number;
}

// 服务信息接口
export interface ServiceInfo {
  serviceCustomerName: string;
  serviceCustomerPhone: string;
  serviceType: string;
  serviceDate: string;
  serviceLocation: string;
  serviceNotes?: string;
}

// 质检单基础信息接口
export interface QualityCheckOrder {
  id: string;
  qualityCheckNo: string;
  workOrderNo: string;
  workOrderType: WorkOrderType;
  status: QualityCheckStatus;
  technicianId: string;
  technicianName: string;
  storeId: string;
  storeName: string;
  isClaimRelated: boolean;
  isOutsourceRelated: boolean;
  startTime?: string;
  finishTime?: string;
  estimatedHours?: number;
  actualHours?: number;
  submitTime?: string;
  auditTime?: string;
  auditResult?: QualityCheckAuditResult;
  auditorId?: string;
  auditorName?: string;
  reworkReason?: string;
  reworkRequirement?: string;
  auditRemark?: string;
  createTime: string;
  updateTime: string;
}

// 质检详情接口
export interface QualityCheckDetail {
  qualityCheck: QualityCheckOrder;
  checkItems: QualityCheckItem[];
  workOrderInfo: WorkOrderInfo;
  vehicleInfo: VehicleInfo;
  serviceInfo: ServiceInfo;
}

// 质检提交表单接口
export interface QualityCheckSubmitForm {
  qualityCheckId: string;
  actualHours?: number;
  remarks?: string;
  checkItems: QualityCheckItemForm[];
  isDraft?: boolean;
}

// 质检审核表单接口
export interface QualityCheckAuditForm {
  qualityCheckId: string;
  auditResult: QualityCheckAuditResult;
  reworkReason?: string;
  reworkRequirement?: string;
  auditRemark?: string;
}

// 返工处理表单接口
export interface QualityCheckReworkForm {
  qualityCheckId: string;
  reworkStartTime?: string;
  reworkFinishTime?: string;
  reworkNotes?: string;
}

// 统计信息接口
export interface QualityCheckStatistics {
  todayTotal: number;
  todayPassed: number;
  todayFailed: number;
  weekTotal: number;
  weekPassed: number;
  weekFailed: number;
  passRate: number;
  avgProcessTime: number;
  reworkRate: number;
  monthlyTrend: MonthlyTrendItem[];
}

// 月度趋势项接口
export interface MonthlyTrendItem {
  month: string;
  total: number;
  passed: number;
  failed: number;
  passRate: number;
}

// 质检模板接口
export interface QualityCheckTemplate {
  categoryCode: string;
  categoryName: string;
  items: QualityCheckTemplateItem[];
}

// 质检模板项接口
export interface QualityCheckTemplateItem {
  itemCode: string;
  itemName: string;
  itemType: QualityCheckItemType;
  standardValue?: string;
  unit?: string;
  isRequired: boolean;
  sortOrder: number;
}

// 分页响应接口
export interface PaginationResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

// 质检选项接口
export interface QualityCheckOption {
  label: string;
  value: string;
}

// 导出参数接口
export interface ExportQualityCheckParams extends QualityCheckSearchParams {
  exportType: 'all' | 'selected';
  exportFormat: 'excel' | 'pdf';
  selectedIds?: string[];
}

// 批量操作接口
export interface BatchQualityCheckOperation {
  qualityCheckIds: string[];
  operation: 'audit' | 'submit' | 'rework';
  auditResult?: QualityCheckAuditResult;
  reworkReason?: string;
  auditRemark?: string;
}
