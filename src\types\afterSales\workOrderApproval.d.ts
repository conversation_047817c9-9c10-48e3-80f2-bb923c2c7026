// 工单审批相关类型定义

// 基础类型定义
export type WorkOrderApprovalType = 'claim_approval' | 'cancel_approval'; // 索赔审批 | 取消工单审批
export type ApprovalStatus = 'pending_review' | 'reviewed'; // 待审核 | 已审核
export type ApprovalResult = 'approved' | 'rejected'; // 审批通过 | 审批驳回
export type ApprovalLevel = 'first_level' | 'second_level'; // 一级审批 | 二级审批
export type TimeoutStatus = 'normal' | 'about_to_timeout' | 'timeout'; // 正常 | 即将超时 | 已超时
export type ApprovalUserRole = 'service_advisor' | 'technician_manager' | 'factory_manager'; // 服务顾问 | 技师经理 | 厂端管理人员
export type ClaimType = 'labor_claim' | 'parts_claim'; // 工时索赔 | 零件索赔

// 待审批列表项接口
export interface PendingApprovalListItem {
  id: string;
  approvalNo: string;
  approvalType: WorkOrderApprovalType;
  submitterName: string;
  submitTime: string;
  orderNo: string;
  requestReason: string;
  timeoutStatus: TimeoutStatus;
  remainingTime?: string;
  customerName: string;
  licensePlate: string;
  vehicleModel: string;
  storeName: string;
  currentLevel: ApprovalLevel;
}

// 已审批列表项接口
export interface CompletedApprovalListItem extends PendingApprovalListItem {
  approvalResult: ApprovalResult;
  approvalRemark?: string;
  approvalTime: string;
  approverName: string;
}

// 搜索参数接口
export interface WorkOrderApprovalListParams {
  page: number;
  pageSize: number;
  approvalNo?: string;
  approvalType?: WorkOrderApprovalType;
  submitterName?: string;
  orderNo?: string;
  customerName?: string;
  licensePlate?: string;
  timeoutStatus?: TimeoutStatus;
  currentLevel?: ApprovalLevel;
  submitTimeStart?: string;
  submitTimeEnd?: string;
  approvalResult?: ApprovalResult;
  approvalTimeStart?: string;
  approvalTimeEnd?: string;
}

// 分页响应接口
export interface ApprovalPageResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 客户信息接口
export interface CustomerApprovalInfo {
  customerId: string;
  customerName: string;
  phone: string;
  senderName: string;
  senderPhone: string;
}

// 车辆信息接口
export interface VehicleApprovalInfo {
  vehicleId: string;
  licensePlate: string;
  vehicleModel: string;
  vin: string;
  engineNo: string;
  purchaseDate: string;
  warrantyEndDate: string;
  mileage: number;
}

// 索赔工时项接口
export interface ClaimLaborItem {
  laborId: string;
  laborCode: string;
  laborName: string;
  laborHours: number;
  laborRate: number;
  laborAmount: number;
  description?: string;
}

// 索赔零件项接口
export interface ClaimPartsItem {
  partId: string;
  partCode: string;
  partName: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  supplier?: string;
  description?: string;
}

// 工单索赔内容接口
export interface WorkOrderClaimContent {
  claimType: ClaimType;
  claimReason: string;
  claimDescription?: string;
  claimAmount: number;
  attachments?: string[];
}

// 审批过程接口
export interface WorkOrderApprovalProcess {
  processId: string;
  approvalNo: string;
  approvalLevel: ApprovalLevel;
  approverId: string;
  approverName: string;
  approvalTime: string;
  approvalResult: ApprovalResult;
  approvalRemark?: string;
  isOvertime: boolean;
  createTime: string;
}

// 操作日志接口
export interface ApprovalOperationLog {
  logId: string;
  approvalNo: string;
  operatorId: string;
  operatorName: string;
  operationType: 'submit' | 'approve' | 'reject' | 'withdraw' | 'timeout_reminder';
  operationTime: string;
  operationDescription: string;
  operationDetails?: any;
}

// 索赔审批详情接口
export interface ClaimApprovalDetail {
  approvalNo: string;
  approvalType: 'claim_approval';
  submitterName: string;
  submitTime: string;
  orderNo: string;
  requestReason: string;
  customerInfo: CustomerApprovalInfo;
  vehicleInfo: VehicleApprovalInfo;
  claimLaborList: ClaimLaborItem[];
  claimPartsList: ClaimPartsItem[];
  claimLaborTotal: number;
  claimPartsTotal: number;
  claimTotalAmount: number;
  approvalStatus: ApprovalStatus;
  currentLevel: ApprovalLevel;
  approvalProcessList: WorkOrderApprovalProcess[];
}

// 取消审批详情接口
export interface CancelApprovalDetail {
  approvalNo: string;
  approvalType: 'cancel_approval';
  submitterName: string;
  submitTime: string;
  orderNo: string;
  requestReason: string;
  customerInfo: CustomerApprovalInfo;
  vehicleInfo: VehicleApprovalInfo;
  cancelReason: string;
  cancelDescription?: string;
  approvalStatus: ApprovalStatus;
  currentLevel: ApprovalLevel;
  approvalProcessList: WorkOrderApprovalProcess[];
}

// 审批请求接口
export interface WorkOrderApprovalRequest {
  approvalNo: string;
  approvalResult: ApprovalResult;
  approvalRemark?: string;
  rejectionReason?: string;
}

// 审批响应接口
export interface WorkOrderApprovalResult {
  success: boolean;
  message?: string;
  data?: any;
}

// 导出参数接口
export interface ExportApprovalParams extends WorkOrderApprovalListParams {
  exportType: 'pending' | 'completed' | 'all';
  exportFormat: 'excel' | 'pdf';
}

// 审批统计接口
export interface ApprovalStatistics {
  totalPending: number;
  totalCompleted: number;
  approvedCount: number;
  rejectedCount: number;
  timeoutCount: number;
  avgProcessTime: number;
}

// 审批选项接口
export interface ApprovalOption {
  label: string;
  value: string;
}

// 审批表单数据接口
export interface ApprovalFormData {
  approvalResult: ApprovalResult;
  approvalRemark?: string;
  rejectionReason?: string;
  attachments?: string[];
}
