// 审批系统类型定义

// 审批类型枚举
export type ApprovalType = 'claim' | 'cancel'

// 审批状态枚举
export type ApprovalStatus = 'pending' | 'level1_approved' | 'level2_approved' | 'rejected' | 'completed'

// 审批结果枚举
export type ApprovalResult = 'pending' | 'approved' | 'rejected'

// 用户角色枚举
export type UserRole = 'technician_manager' | 'factory_manager' | 'service_advisor'

// 基础审批请求接口
export interface ApprovalRequest {
  id: string
  approvalNo: string
  approvalType: ApprovalType
  orderNo: string
  submitter: string
  submitterNo: string
  submitTime: string
  approvalStatus: ApprovalStatus
  requestReason: string
  customerName?: string
  customerPhone?: string
  senderName?: string
  senderPhone?: string
  licensePlate?: string
  vin?: string
  vehicleModel?: string
  vehicleColor?: string
  mileage?: number
  vehicleAge?: number
  repairTime?: string
  claimLaborData?: ClaimLaborItem[]
  claimPartsData?: ClaimPartItem[]
  createdAt: string
  updatedAt: string
  approvalResult?: ApprovalResult
}

// 审批过程记录接口
export interface ApprovalProcess {
  id: string
  approvalNo: string
  level: number
  approver: string
  approverNo: string
  approvalTime: string
  approvalResult: ApprovalResult
  approvalRemark?: string
  isOvertime: boolean
}

// 索赔工时项目接口
export interface ClaimLaborItem {
  laborCode: string
  laborName: string
  standardHours: number
  hourlyRate: number
  claimAmount: number
}

// 索赔零件项目接口
export interface ClaimPartItem {
  partCode: string
  partName: string
  quantity: number
  unitPrice: number
  claimAmount: number
}

// 门店信息接口
export interface StoreInfo {
  id: string
  name: string
  code: string
}

// 审批表单数据接口
export interface ApprovalFormData {
  approvalResult: ApprovalResult | ''
  approvalRemark: string
}

// 搜索参数接口
export interface ApprovalSearchParams {
  approvalNo?: string
  approvalType?: ApprovalType | ''
  approvalStatus?: ApprovalStatus | ''
  submitTimeRange?: [string, string]
  submitter?: string
  orderNo?: string
  storeId?: string
  customerInfo?: string
}

// 审批统计数据接口
export interface ApprovalStatistics {
  pendingCount: number
  approvedCount: number
  rejectedCount: number
  overtimeCount: number
} 