// 门店管理相关类型定义

// MyBatisPlus分页参数
export interface PageParams {
  current?: number;    // 当前页码，从1开始
  size?: number;       // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  current: number;     // 当前页码
  size: number;        // 每页条数
  pages: number;       // 总页数
}

// 门店信息
export interface StoreItem {
  id: string;
  storeCode: string;
  storeName: string;
  storeShortName?: string;  // 门店简称
  storeType: '02010001' | '02010002';
  storeProperties: string[];
  storeStatus: string;
  manager?: string;
  contactPerson?: string;
  contactPhone?: string;
  continent?: string;       // 洲
  city?: string;
  postalCode?: string;      // 邮政编码
  detailAddress?: string;
  parentId?: string;
  children?: StoreItem[];
  remark?: string;
  createTime: string;
}

// 门店搜索参数（继承分页参数）
export interface StoreSearchParams extends PageParams {
  storeName?: string;
  storeCode?: string;
  storeStatus?: string;
}

// 门店分页响应
export interface StorePageResponse extends PageResponse<StoreItem> {}

// 新增门店请求
export interface CreateStoreRequest {
  storeCode: string;
  storeName: string;
  storeShortName?: string;
  storeType: '02010001' | '02010002';
  storeProperties: string[];
  storeStatus: string;
  manager?: string;
  contactPerson?: string;
  contactPhone?: string;
  continent?: string;
  city?: string;
  postalCode?: string;
  detailAddress?: string;
  parentId?: string;
  remark?: string;
}

// 更新门店请求
export interface UpdateStoreRequest extends CreateStoreRequest {
  id: string;
}

// API响应格式
export interface ApiResponse<T> {
  code: number | string;
  message: string;
  result: T;
  success: boolean;
  timestamp: number;
  traceId: string;
}
