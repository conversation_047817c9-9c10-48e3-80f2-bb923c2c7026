// 通用分页参数接口
export interface PaginationParams {
  page: number;
  size: number;
}

// 通用分页响应接口
export interface PaginationResult<T> {
  list: T[];
  total: number;
  page?: number;
  size?: number;
}

// API响应基础结构
export interface ApiResult<T> {
  code: number;
  message: string;
  result: T;
  success: boolean;
}

// API分页响应结构
export interface ApiPageResult<T> {
  code: number;
  message: string;
  result: {
    records: T[];
    total: number;
    size: number;
    current: number;
    pages: number;
  };
  success: boolean;
}