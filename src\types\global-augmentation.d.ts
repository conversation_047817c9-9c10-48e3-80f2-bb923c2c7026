/**
 * 全局类型增强，为组件中使用的字段提供类型定义
 */

// 为所有对象类型添加索引签名，允许访问任意属性
declare global {
  interface Object {
    [key: string]: unknown;
  }
}

// 扩展现有类型
declare module 'vue' {
  interface ComponentCustomProperties {
    [key: string]: unknown;
  }
}

// 扩展API响应类型
declare module '@/types/module' {
  interface PaginationResponse<T> {
    records?: T[];
    result?: {
      records: T[];
      total: number;
      current?: number;
      size?: number;
      pages?: number;
    };
    data?: T[];
    total?: number;
    current?: number;
    size?: number;
    pages?: number;
  }

  // 扩展销售订单参数
  interface SalesOrderListParams {
    // 允许使用任意字段
    [key: string]: unknown;
  }

  // 扩展潜客相关类型
  interface ProspectBaseInfo {
    // 兼容字段
    name?: string;
    phoneNumber?: string;
    customerName?: string;
    customerPhone?: string;
    storeProspectId?: number;
    prospectId?: number;
    defeatDetails?: string;
    defeatDescription?: string;
  }

  // 扩展审批相关类型
  interface ApprovalListItem {
    // 兼容字段
    approvalNo?: string;
    submitterName?: string;
    submitTime?: string;
    orderNo?: string;
    reason?: string;
    comment?: string;
  }

  // 扩展车辆登记相关类型
  interface VehicleRegistrationDetail {
    // 兼容字段
    jpjInfo?: {
      status?: string;
      certificateNumber?: string;
      pushTime?: string;
      completionTime?: string;
      operator?: string;
      failureReason?: string;
    };
    operationLogs?: unknown[];
  }

  // 扩展保险信息类型
  interface InsuranceInfo {
    status: string;
    company: string;
    policyNumber: string;
    period: string;
    amount: number;
    // 兼容字段
    date?: string;
    fee?: number;
  }

  // 扩展权益类型
  interface AvailableRight {
    id: string;
    rightCode: string;
    rightName: string;
    rightMode: string;
    discountAmount: number;
    effectiveDate: string;
    expiryDate: string;
    // 兼容字段
    discountPrice?: number;
  }

  // 扩展发票类型
  interface Invoice {
    // 兼容字段
    invoiceCompany?: string;
    engineDisplacement?: string;
    loanPeriod?: string;
    issueDate?: string;
    vehicleSalesPrice?: number;
    accessoryAmount?: number;
    otrAmount?: number;
  }

  // 扩展检查表单类型
  interface InspectionFormDetail {
    // 兼容字段
    reserveName?: string;
    reservePhone?: string;
    remark?: string;
    vin?: string;
    modelConfig?: string;
    servicePackageName?: string;
    customerConfirmImage?: string;
  }

  // 扩展支付记录类型
  interface PaymentRecord {
    // 兼容字段
    recordId?: string;
    recordType?: string;
    paymentMethod?: string;
    paymentDate?: string;
    handler?: string;
  }

  // 扩展订单类型
  interface SalesOrderDetail {
    // 兼容字段
    buyerType?: string;
    buyerIdCard?: string;
    buyerGender?: string;
    engineNumber?: string;
    yearOfManufacture?: string;
  }
}

export {};
