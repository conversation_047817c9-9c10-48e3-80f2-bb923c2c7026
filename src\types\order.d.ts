// 订单管理相关类型定义

// 分页响应接口
export interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
}

// 订单状态枚举
export type OrderStatus = 
  | 'pending_deposit' // 待支付定金
  | 'confirmed' // 已确认
  | 'cancelled' // 已取消
  | 'pending_delivery' // 待交车
  | 'completed' // 已完成

// 支付状态枚举
export type PaymentStatus = 
  | 'pending_deposit' // 待支付定金
  | 'deposit_paid' // 定金已付
  | 'pending_final' // 待支付尾款
  | 'fully_paid' // 已全款

// 支付方式枚举
export type PaymentMethod = 'full_payment' | 'installment'

// 审核状态枚举
export type ApprovalStatus = 
  | 'pending' // 待审批
  | 'approved' // 已审批-通过
  | 'rejected' // 已审批-驳回

// 保险状态枚举
export type InsuranceStatus = 
  | 'pending' // 待投保
  | 'completed' // 已投保
  | 'failed' // 投保失败

// JPJ注册状态枚举
export type JpjRegistrationStatus = 
  | 'pending' // 待登记
  | 'completed' // 已登记
  | 'failed' // 登记失败

// 订单选配件
export interface OrderAccessory {
  id: string
  accessoryCode: string
  accessoryName: string
  category: string
  price: number
  quantity: number
  totalPrice: number
}

// 订单权益
export interface OrderRight {
  id: string
  rightCode: string
  rightName: string
  mode: string
  discountAmount: number
  effectiveDate: string
  expiryDate: string
}

// 订单保险信息
export interface OrderInsurance {
  id: string
  policyNumber: string
  insuranceType: string
  company: string
  amount: number
  effectiveDate: string
  expiryDate: string
  status: InsuranceStatus
  notes?: string
}

// OTR费用信息
export interface OtrFee {
  id: string
  ticketNumber: string
  feeItem: string
  feePrice: number
  effectiveDate: string
  expiryDate: string
}

// 订单基本信息
export interface Order {
  id: string
  orderNumber: string
  customerId: string
  customerName: string
  customerPhone: string
  buyerName: string
  buyerPhone: string
  buyerIdType: string
  buyerIdNumber: string
  buyerEmail: string
  buyerAddress: string
  buyerState: string
  buyerCity: string
  buyerPostalCode: string
  buyerType: string
  storeId: string
  storeName: string
  storeState: string
  storeCity: string
  salesConsultantId: string
  salesConsultantName: string
  model: string
  variant: string
  color: string
  vin: string
  salesSubtotal: number
  numberPlatesFee: number
  totalAmount: number
  deposit: number
  finalPayment: number
  loanAmount?: number
  loanTerm?: number
  paymentMethod: PaymentMethod
  loanApprovalStatus?: string
  orderStatus: OrderStatus
  paymentStatus: PaymentStatus
  approvalStatus: ApprovalStatus
  insuranceStatus: InsuranceStatus
  jpjRegistrationStatus: JpjRegistrationStatus
  accessories: OrderAccessory[]
  rights: OrderRight[]
  insurance?: OrderInsurance
  otrFees: OtrFee[]
  createTime: string
  updateTime: string
  // 开票信息
  invoicingType: string
  invoicingName: string
  invoicingPhone: string
  invoicingAddress: string
}

// 订单列表项（用于表格显示）
export interface OrderListItem {
  id: string
  orderNumber: string
  storeName: string
  createTime: string
  customerName: string
  customerPhone: string
  buyerName: string
  buyerPhone: string
  buyerType: string
  model: string
  variant: string
  color: string
  vin: string
  paymentMethod: PaymentMethod
  loanApprovalStatus?: string
  orderStatus: OrderStatus
  approvalStatus: ApprovalStatus
  paymentStatus: PaymentStatus
  insuranceStatus: InsuranceStatus
  jpjRegistrationStatus: JpjRegistrationStatus
}

// 订单查询参数
export interface OrderListParams {
  page?: number
  pageSize?: number
  storeFilter?: string
  modelFilter?: string
  variantFilter?: string
  orderStatus?: OrderStatus
  paymentStatus?: PaymentStatus
  orderNumber?: string
  dateRange?: [string, string]
}

// 订单统计信息
export interface OrderStats {
  monthlyOrderCount: number
  monthlyOrderGrowth: number
  todayOrderCount: number
  todayOrderGrowth: number
  topStore: string
  topStoreOrderCount: number
  hotModel: string
  hotModelOrderCount: number
  pendingDeliveryCount: number
}

// 审核申请类型
export type ApprovalType = 
  | 'order_cancel' // 订单取消
  | 'color_change' // 车辆颜色修改
  | 'buyer_info_change' // 购车人信息修改

// 审核记录
export interface ApprovalRecord {
  id: string
  approvalNumber: string
  orderId: string
  orderNumber: string
  applicantId: string
  applicantName: string
  approvalType: ApprovalType
  applicationReason: string
  approvalLevel: 'first' | 'final'
  approverId?: string
  approverName?: string
  approvalResult: ApprovalStatus
  rejectionReason?: string
  applicationTime: string
  approvalTime?: string
  // 变更内容（用于颜色修改和购车人信息修改）
  changeDetails?: {
    before: any
    after: any
  }
}

// 审核列表查询参数
export interface ApprovalListParams {
  page?: number
  pageSize?: number
  approvalType?: ApprovalType
  orderNumber?: string
  applicantName?: string
  approvalStatus?: ApprovalStatus
  dateRange?: [string, string]
}

// 权益选项（用于权益选择模态框）
export interface RightOption {
  rightCode: string
  rightName: string
  mode: string
  discountAmount: number
  effectiveDate: string
  expiryDate: string
  available: boolean
}

// 权益查询参数
export interface RightListParams {
  rightCode?: string
  rightName?: string
} 