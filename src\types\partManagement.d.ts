// 配件管理相关类型定义

// 详情项接口
export interface DetailItem {
  partName: string;
  partNumber: string;
  quantity: number;
  unit: string;
  requisitionStatus: 'submitted' | 'approved' | 'rejected' | 'shipped' | 'partialReceived' | 'received' | 'cancelled' | 'voided';
  requisitionDate: string;
  expectedArrivalTime: string;
  supplierName: string;
}

// 配件管理项接口
export interface PartManagementItem {
  id: string;
  requisitionNumber: string;
  purchaseOrderNumber: string;
  requisitionDate: string;
  requisitionStatus: 'submitted' | 'approved' | 'rejected' | 'shipped' | 'partialReceived' | 'received' | 'cancelled' | 'voided';
  partName: string;
  partNumber: string;
  supplierName: string;
  inventoryStatus: 'normal' | 'belowSafetyStock';
  items?: DetailItem[];
  rejectionReason?: string;
}

// 报损记录项接口
export interface ScrapRecordItem {
  id: string;
  partName: string;
  partNumber: string;
  scrapQuantity: number;
  scrapDate: string;
  scrapSource: 'receipt' | 'repair' | 'other';
  receiptTime: string;
  scrapReason: string;
}

// 配件管理查询参数
export interface PartManagementParams {
  page?: number;
  pageSize?: number;
  partName?: string;
  partNumber?: string;
  requisitionNumber?: string;
  purchaseOrderNumber?: string;
  supplierName?: string;
  requisitionStatus?: string;
  inventoryStatus?: string;
}

// 报损记录查询参数
export interface ScrapRecordParams {
  page?: number;
  pageSize?: number;
  partName?: string;
  partNumber?: string;
  scrapSource?: string;
  receiptTime?: string;
}
