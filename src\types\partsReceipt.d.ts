declare namespace PartsReceipt {
  interface PartsReceiptListItem {
    id: string;
    receiptOrderNumber: string;
    partName: string;
    partNumber: string;
    partQuantity: number;
    unit: string;
    supplierName: string;
    requisitionNumber: string;
    deliveryOrderNumber: string;
    purchaseOrderNumber: string;
    // 添加其他需要的字段
  }

  interface PartsReceiptSearchParams {
    partName: string;
    partNumber: string;
    supplierName: string;
    requisitionNumber: string;
    purchaseOrderNumber: string;
    deliveryOrderNumber: string;
  }

  interface PaginationResponse<T> {
    data: T[];
    total: number;
    page: number;
    pageSize: number;
  }

  interface ReceiptItem extends PartsReceiptListItem {
    receivedQuantity: number;
    damagedQuantity: number;
    damageReason: string;
  }

  interface GenerateReceiptOrderSearchParams {
    partName: string;
    partNumber: string;
    supplierName: string;
    receiptTime: string;
    requisitionNumber: string;
    purchaseOrderNumber: string;
  }

  interface GenerateReceiptOrderListItem {
    id: string;
    partName: string;
    partNumber: string;
    quantity: number;
    unit: string;
    supplierName: string;
    receiptTime: string;
    receiptOrderNumber: string;
    purchaseOrderNumber: string;
    requisitionNumber: string;
  }

  interface InvalidateReceiptOrderSearchParams {
    receiptOrderNumber: string;
    receiptOrderStatus: string;
    partName: string;
    partNumber: string;
    supplierName: string;
    receiptTime: string;
    requisitionNumber: string;
    purchaseOrderNumber: string;
  }

  interface InvalidateReceiptOrderListItem {
    id: string;
    receiptOrderNumber: string;
    generationDate: string;
    receiptOrderStatus: string;
    canInvalidate?: boolean; // 用于前端控制是否可作废
  }

  interface ReceiptOrderDetailItem {
    id: string;
    partName: string;
    partNumber: string;
    quantity: number;
    unit: string;
    supplierName: string;
    receiptTime: string;
  }
}

// 导出命名空间中的接口，以便在其他文件中使用
declare type PartsReceiptListItem = PartsReceipt.PartsReceiptListItem;
declare type PartsReceiptSearchParams = PartsReceipt.PartsReceiptSearchParams;
declare type PaginationResponse<T> = PartsReceipt.PaginationResponse<T>;
declare type ReceiptItem = PartsReceipt.ReceiptItem;
declare type GenerateReceiptOrderSearchParams = PartsReceipt.GenerateReceiptOrderSearchParams;
declare type GenerateReceiptOrderListItem = PartsReceipt.GenerateReceiptOrderListItem;
declare type InvalidateReceiptOrderSearchParams = PartsReceipt.InvalidateReceiptOrderSearchParams;
declare type InvalidateReceiptOrderListItem = PartsReceipt.InvalidateReceiptOrderListItem;
declare type ReceiptOrderDetailItem = PartsReceipt.ReceiptOrderDetailItem; 