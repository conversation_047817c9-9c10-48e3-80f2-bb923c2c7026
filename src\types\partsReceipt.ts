export interface PartsReceiptListItem {
  id: number;
  partName: string;
  partNumber: string;
  supplierName: string;
  requisitionNumber: string;
  purchaseOrderNumber: string;
  deliveryOrderNumber: string;
  partQuantity: number;
  unit: string;
}

export interface PartsReceiptSearchParams {
  partName?: string;
  partNumber?: string;
  supplierName?: string;
  requisitionNumber?: string;
  purchaseOrderNumber?: string;
  deliveryOrderNumber?: string;
}

export interface PaginationResponse<T> {
  data: T[];
  total: number;
}

export interface ReceiptItem {
  partName: string;
  partNumber: string;
  partQuantity: number;
  receivedQuantity: number;
  damagedQuantity: number;
  damageReason: string;
}

export interface GenerateReceiptOrderSearchParams {
  partName?: string;
  partNumber?: string;
  supplierName?: string;
  receiptTime?: string;
  requisitionNumber?: string;
  purchaseOrderNumber?: string;
}

export interface GenerateReceiptOrderListItem {
  id: number;
  partName: string;
  partNumber: string;
  quantity: number;
  unit: string;
  supplierName: string;
  receiptTime: string;
  receiptOrderNumber: string;
  purchaseOrderNumber: string;
  requisitionNumber: string;
}

export interface InvalidateReceiptOrderSearchParams {
  receiptOrderNumber?: string;
  receiptOrderStatus?: string;
  partName?: string;
  partNumber?: string;
  supplierName?: string;
  receiptTime?: string;
  requisitionNumber?: string;
  purchaseOrderNumber?: string;
}

export interface InvalidateReceiptOrderListItem {
  receiptOrderNumber: string;
  generationDate: string;
  receiptOrderStatus: string;
  canInvalidate?: boolean;
}

export interface ReceiptOrderDetailItem {
  partName: string;
  partNumber: string;
  quantity: number;
  unit: string;
  supplierName: string;
  receiptTime: string;
} 