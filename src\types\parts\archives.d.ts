// 零件档案相关类型定义

// 零件档案项接口
export interface PartArchiveItem {
  partName: string;
  partNumber: string;
  unit: string;
  supplierName: string;
  purchasePrice: number;
}

// 零件档案搜索参数
export interface PartArchiveSearchParams {
  partName?: string;
  partNumber?: string;
  supplierName?: string;
  page?: number;
  pageSize?: number;
}

// 零件档案分页响应
export interface PartArchivePageResponse {
  list: PartArchiveItem[];
  total: number;
}