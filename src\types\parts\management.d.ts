// 零件管理相关类型定义

// ===== 基础类型定义 =====

// 单据类型枚举
export type DocumentType = 'requisition' | 'scrap' | 'picking';

// 叫料单状态枚举
export type RequisitionStatus = 'submitted' | 'approved' | 'rejected' | 'shipped' | 'partialShipped' | 'partialReceived' | 'received' | 'cancelled' | 'voided';

// 工单状态枚举
export type WorkOrderStatus = 'pending' | 'pending_pick' | 'picked' | 'outOfStock' | 'closed';

// 库存状态枚举
export type InventoryStatus = 'normal' | 'belowSafetyStock';

// 报损来源枚举
export type ScrapSource = 'receipt' | 'repair' | 'other';

// ===== 详情项接口 =====

// 详情项接口（从原partManagement.d.ts迁移）
export interface DetailItem {
  partName: string;
  partNumber: string;
  quantity: number;
  unit: string;
  requisitionStatus: RequisitionStatus;
  requisitionDate: string;
  expectedArrivalTime: string;
  supplierName: string;
}

// 工单零件项
export interface WorkOrderPart {
  partName: string;
  partCode: string;
  quantity: number;
}

// 退拣零件项
export interface ReturnPickingPart {
  partName: string;
  partCode: string;
  quantity: number;
  returnQuantity: number;
}

// ===== 主要数据接口 =====

// 配件管理项接口（从原partManagement.d.ts迁移并增强）
export interface PartManagementItem {
  id?: string;
  requisitionNumber: string;
  purchaseOrderNumber?: string;
  requisitionDate: string;
  requisitionStatus: RequisitionStatus;
  partName?: string;
  partNumber?: string;
  supplierName?: string;
  inventoryStatus?: InventoryStatus;
  documentType?: DocumentType;
  items?: DetailItem[];
  rejectionReason?: string;
  [key: string]: any;
}

// 报损记录项接口（从原partManagement.d.ts迁移）
export interface ScrapRecordItem {
  id: string;
  partName: string;
  partNumber: string;
  scrapQuantity: number;
  scrapDate: string;
  scrapSource: ScrapSource;
  receiptTime: string;
  scrapReason: string;
}

// 物料单项接口（从页面内提取）
export interface MaterialOrderItem {
  workOrderNumber: string;
  createDate: string;
  workOrderStatus: WorkOrderStatus;
  customerName: string;
  vehicleModel: string;
  serviceType: string;
}

// 工单详情接口（从页面内提取）
export interface WorkOrderDetail {
  workOrderNumber: string;
  generateDate: string;
  parts: WorkOrderPart[];
}

// 退拣详情接口（从页面内提取）
export interface ReturnPickingDetail {
  workOrderNumber: string;
  generateDate: string;
  parts: ReturnPickingPart[];
}

// 统一表格数据类型（从页面内提取）
export interface UnifiedTableItem {
  id?: string;
  requisitionNumber: string;
  purchaseOrderNumber?: string;
  requisitionDate: string;
  requisitionStatus: string;
  partName?: string;
  partNumber?: string;
  supplierName?: string;
  scrapSource?: string;
  documentType?: string;
  scrapQuantity?: number;
  scrapReason?: string;
  scrapImages?: string[];
  rejectionReason?: string;
  items?: any[];
  workOrderData?: any;
  [key: string]: any;
}

// ===== 查询参数接口 =====

// 配件管理查询参数（从原partManagement.d.ts迁移并增强）
export interface PartManagementParams {
  page?: number;
  pageSize?: number;
  partName?: string;
  partNumber?: string;
  requisitionNumber?: string;
  purchaseOrderNumber?: string;
  supplierName?: string;
  requisitionStatus?: string;
  inventoryStatus?: string;
}

// 报损记录查询参数（从原partManagement.d.ts迁移）
export interface ScrapRecordParams {
  page?: number;
  pageSize?: number;
  partName?: string;
  partNumber?: string;
  scrapSource?: string;
  receiptTime?: string;
}

// 统一搜索参数接口
export interface UnifiedSearchParams {
  documentType: DocumentType;
  page?: number;
  pageSize?: number;
  partName?: string;
  partNumber?: string;
  requisitionNumber?: string;
  supplierName?: string;
  requisitionDateRange?: string[];
  requisitionStatus?: string;
  inventoryStatus?: string;
}

// ===== API响应接口 =====

// 分页响应接口
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
}

// 零件管理页面响应接口
export interface PartManagementPageResponse extends PaginatedResponse<UnifiedTableItem> {}

// 工单列表响应接口
export interface MaterialOrderResponse extends PaginatedResponse<MaterialOrderItem> {}

// ===== 表单数据接口 =====

// 查询表单接口
export interface QueryForm {
  partName: string;
  partNumber: string;
  requisitionNumber: string;
  documentType: DocumentType;
  supplierName: string;
  requisitionDateRange: string[];
  requisitionStatus: string;
  inventoryStatus: string;
}

// 详情数据接口
export interface DetailData {
  requisitionNumber: string;
  purchaseOrderNumber: string;
  requisitionDate: string;
  requisitionStatus: string;
  rejectionReason: string;
  items: DetailItem[];
}

// ===== 状态映射接口 =====

// 状态标签映射
export interface StatusLabelMapping {
  [key: string]: string;
}

// 状态标签类型映射
export interface StatusTagTypeMapping {
  [key: string]: 'success' | 'warning' | 'danger' | 'info' | '';
}

// ===== 操作相关接口 =====

// 退拣操作项
export interface ReturnPickingItem {
  partName: string;
  partCode: string;
  returnQuantity: number;
}

// 批量操作参数
export interface BatchOperationParams {
  ids: string[];
  operation: 'void' | 'approve' | 'reject';
  reason?: string;
}

// ===== 工单数据缓存接口 =====

// 工单数据存储
export interface WorkOrderDataStore {
  [workOrderNumber: string]: WorkOrderDetail;
}

// ===== 表单和操作相关接口 =====

// 叫料单创建/更新数据
export interface RequisitionFormData {
  partName: string;
  partNumber: string;
  quantity: number;
  unit: string;
  supplierName: string;
  expectedArrivalTime: string;
  remarks?: string;
}

// 报损单创建/更新数据
export interface ScrapRecordFormData {
  partName: string;
  partNumber: string;
  scrapQuantity: number;
  scrapSource: ScrapSource;
  scrapReason: string;
  scrapImages?: string[];
  remarks?: string;
}

// 物料单筛选参数
export interface MaterialOrderFilters {
  workOrderNumber?: string;
  workOrderStatus?: string;
  createDateRange?: string[];
  page?: number;
  pageSize?: number;
}