// 经销商端采购管理类型定义

// 经销商门店信息
export interface DealerStoreInfo {
  dealerId: string;
  dealerName: string;
  storeName: string;
  storeCode?: string;
  region?: string;
  contactPerson?: string;
  contactPhone?: string;
}

// 采购订单状态枚举
export type PurchaseOrderStatus = 
  | 'draft'                 // 草稿
  | 'submitted'             // 已提交
  | 'approved'              // 已审批
  | 'rejected'              // 已驳回
  | 'shipped'               // 已发货
  | 'partiallyShipped'      // 部分发货
  | 'partiallyReceived'     // 部分收货
  | 'received'              // 已收货
  | 'cancelled';            // 已作废

// 采购订单基础信息
export interface PurchaseOrder {
  id: string;
  orderNo: string;
  dealerId?: number;
  dealerName?: string;
  dealerStoreInfo?: DealerStoreInfo;  // 门店信息
  warehouseId: number;
  warehouseName: string;
  expectedDeliveryDate?: string;      // 期望到货日期
  status: PurchaseOrderStatus;
  totalAmount: number;
  itemCount: number;
  remark?: string;
  rejectionReason?: string;      // 驳回原因
  createTime: string;
  updateTime?: string;
  shipTime?: string;          // 发货日期
  expectedDelivery?: string;  // 预计交期
  items: PurchaseOrderItem[];
}

// 零件类型枚举
export type PartType = 'ORIGINAL' | 'NON_ORIGINAL';

// 采购订单明细
export interface PurchaseOrderItem {
  id: string;
  partId: string;
  partCode: string;
  partName: string;
  partType: PartType;        // 零件类型：原厂/非原厂
  brand?: string;
  specification?: string;
  unit: string;
  unitPrice: number;
  quantity: number;
  shippedQuantity?: number;
  receivedQuantity?: number;
  amount: number;
  currentStock?: number;     // 当前库存（用于显示）
  safetyStock?: number;      // 安全库存（用于显示）
}

// 发货单信息
export interface ShipmentOrder {
  shipmentId: number;
  shipmentNo: string;
  purchaseOrderId: number;
  shippingDate: string;
  carrier: string;
  trackingNumber: string;
  remarks?: string;
  status: 'SHIPPED' | 'IN_TRANSIT' | 'DELIVERED' | 'RECEIVED';
  items: ShipmentOrderItem[];
}

// 发货单明细
export interface ShipmentOrderItem {
  itemId: number;
  shipmentOrderId: number;
  purchaseOrderItemId: number;
  partId: string;
  partCode: string;
  partName: string;
  unit: string;
  orderedQuantity: number;
  shippedQuantity: number;
  receivedQuantity: number;
}

// 经销商仪表盘数据
export interface DealerDashboardStats {
  pendingApprovalCount: number;      // 待审核
  inTransitCount: number;            // 在途订单
  pendingReceiptCount: number;       // 待收货
  monthlyTotalAmount: number;        // 本月申请总额
}

// 配件选择器中的配件信息
export interface PartForSelection {
  partId: string;
  partCode: string;
  partName: string;
  partType: PartType;        // 零件类型：原厂/非原厂
  brand: string;
  specification: string;
  unit: string;
  purchasePrice: number;
  currentStock: number;
  safetyStock: number;
  availableStock: number;
}

// 创建/编辑采购订单的表单数据
export interface PurchaseOrderForm {
  orderId?: number;
  dealerStoreInfo?: DealerStoreInfo;  // 门店信息
  warehouseId: number;
  expectedDeliveryDate?: string;      // 期望到货日期
  remarks?: string;
  items: PurchaseOrderFormItem[];
}

export interface PurchaseOrderFormItem {
  id: string; // Add id
  partId: string;
  partCode: string;
  partName: string;
  partType: PartType;        // 零件类型：原厂/非原厂
  brand: string;
  specification: string;
  unit: string;
  unitPrice: number; // Use unitPrice instead of purchasePrice
  quantity: number; // Use quantity instead of orderQuantity
  amount: number;
  currentStock: number;
  safetyStock: number;
}

// 收货表单数据
export interface ReceiptForm {
  shipmentId: number;
  receiptDate: string;
  handler: string;
  items: ReceiptFormItem[];
}

export interface ReceiptFormItem {
  partId: string;
  partCode: string;
  partName: string;
  unit: string;
  orderedQuantity: number;
  shippedQuantity: number;
  receivedQuantity: number;
  locationId: string;
  locationName?: string;
}

// API请求参数类型
export interface PurchaseOrderListQuery {
  page?: number;
  size?: number;
  orderNo?: string;
  status?: PurchaseOrderStatus;
  warehouseId?: number;
}

export interface CreatePurchaseOrderRequest {
  warehouseId: number;
  expectedDeliveryDate?: string;      // 期望到货日期
  remark?: string;
  items: {
    partId: string;
    partCode: string;
    partName: string;
    partType: PartType;      // 零件类型：原厂/非原厂
    brand?: string;
    specification?: string;
    unit: string;
    unitPrice: number;
    quantity: number;
    amount: number;
  }[];
}

export interface UpdatePurchaseOrderRequest {
  warehouseId?: number;
  expectedDeliveryDate?: string;      // 期望到货日期
  remark?: string;
  items?: {
    id?: string;
    partId: string;
    partCode: string;
    partName: string;
    partType: PartType;      // 零件类型：原厂/非原厂
    brand?: string;
    specification?: string;
    unit: string;
    unitPrice: number;
    quantity: number;
    amount: number;
  }[];
}

export interface ReceiptRequest {
  shipmentId: string;
  warehouseId: number;
  receiptDate: string;
  handler: string;
  items: {
    partId: string;
    receivedQuantity: number;
    locationId: string;
  }[];
}

// 提交订单响应类型（支持拆分为多个订单）
export interface SubmitOrderResponse {
  message: string;
  data: {
    orderId: string;
    orderNo: string;
    partType: PartType;
  }[];
}

// 收货状态枚举
export type ReceiptStatus = 
  | 'NORMAL'       // 正常
  | 'SHORTAGE'     // 短缺
  | 'DAMAGE'       // 损坏
  | 'REJECTED'     // 拒收
  | 'PENDING'      // 待收货
  | 'URGENT';      // 紧急待收货

// 收货异常类型枚举
export type ReceiptAbnormalType = 
  | 'QUANTITY_SHORTAGE'   // 数量不足
  | 'QUALITY_DAMAGE'      // 质量损坏
  | 'SPECIFICATION_ERROR' // 规格错误
  | 'PACKAGING_DAMAGE'    // 包装损坏
  | 'OTHER';              // 其他

// 收货单信息
export interface ReceiptOrder {
  id: string;
  receiptNo: string;
  purchaseOrderId: string;
  purchaseOrderNo: string;
  shipmentId: string;
  shipmentNo: string;
  supplierId: string;
  supplierName: string;
  expectedDate: string;
  actualReceiptDate?: string;
  status: ReceiptStatus;
  totalItems: number;
  receivedItems: number;
  abnormalItems: number;
  handler?: string;
  remark?: string;
  createTime: string;
  updateTime?: string;
  items: ReceiptOrderItem[];
}

// 收货单明细
export interface ReceiptOrderItem {
  id: string;
  receiptOrderId: string;
  partId: string;
  partCode: string;
  partName: string;
  unit: string;
  orderedQuantity: number;
  shippedQuantity: number;
  actualReceiptQuantity: number;
  status: ReceiptStatus;
  abnormalType?: ReceiptAbnormalType;
  abnormalReason?: string;
  locationId?: string;
  locationName?: string;
  batchNo?: string;
  unitPrice: number;
  amount: number;
}

// 收货统计信息
export interface ReceiptStatistics {
  totalOrders: number;
  pendingReceipt: number;
  completedReceipt: number;
  abnormalReceipt: number;
  totalAmount: number;
  receivedAmount: number;
  receivedRate: number;
  onTimeRate: number;
}

// 收货确认请求
export interface ReceiptConfirmRequest {
  receiptOrderId: string;
  receiptDate: string;
  handler: string;
  warehouseId: string;
  items: {
    itemId: string;
    actualReceiptQuantity: number;
    status: ReceiptStatus;
    abnormalType?: ReceiptAbnormalType;
    abnormalReason?: string;
    locationId?: string;
    batchNo?: string;
  }[];
  remark?: string;
}

// 扩展采购订单接口
export interface PurchaseOrderExtended extends PurchaseOrder {
  // 收货相关信息
  receiptProgress?: {
    totalQuantity: number;
    receivedQuantity: number;
    receivedRate: number;
    inTransitQuantity: number;
    abnormalQuantity: number;
  };
  receiptOrders?: ReceiptOrder[];
  lastReceiptDate?: string;
  nextExpectedDate?: string;
}