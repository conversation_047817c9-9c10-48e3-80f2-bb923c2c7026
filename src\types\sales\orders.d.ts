// 基础类型定义
export type BuyerType = 'individual' | 'company';
export type PaymentMethod = 'full_payment' | 'installment';
export type LoanApprovalStatus = 'pending_review' | 'approved' | 'rejected';
export type SalesOrderStatus = 'submitted' | 'confirmed' | 'pending_delivery' | 'completed' | 'canceled';
export type SalesOrderApprovalStatus = 'pending_approval' | 'approved' | 'rejected';
export type SalesOrderPaymentStatus = 'pending_deposit' | 'fully_paid' | 'refund_completed';
export type InsuranceStatus = 'not_insured' | 'pending' | 'insured';
export type JPJRegistrationStatus = 'pending_registration' | 'registering' | 'registered' | 'registration_failed';

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
  total?: number;      // 总条数（响应返回）
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 销售订单搜索参数（继承分页参数）
export interface SalesOrderSearchParams extends PageParams {
  orderNumber?: string;
  buyerName?: string;
  customerPhone?: string;
  buyerType?: string;
  model?: string;
  orderStatus?: string;
  approvalStatus?: string;
  paymentStatus?: string;
  insuranceStatus?: string;
  loanApprovalStatus?: string;
  jpjRegistrationStatus?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
}

// 销售订单列表项
export interface SalesOrderListItem {
  id: string;
  orderNo: string;
  createTime: string;
  customerName: string;
  customerPhone: string;
  buyerName: string;
  buyerPhone: string;
  buyerType: string;
  model: string;
  variant: string;
  color: string;
  vin: string;
  paymentMethod: string;
  orderStatus: string;
  approvalStatus: string;
  paymentStatus: string;
  insuranceStatus: string;
  jpjRegistrationStatus: string;
  totalAmount: number;
}

// 销售订单列表响应
export interface SalesOrderListResponse extends PageResponse<SalesOrderListItem> {}

// 客户信息
export interface CustomerInfo {
  id: string;
  name: string;
  phone: string;
  email?: string;
  idCard?: string;
  address?: string;
}

// 车辆信息
export interface VehicleInfo {
  model: string;
  variant: string;
  color: string;
  vin: string;
  engineNo?: string;
  chassisNo?: string;
  registrationNo?: string;
}

// 支付信息
export interface PaymentInfo {
  paymentMethod: string;
  totalAmount: number;
  depositAmount: number;
  finalPaymentAmount: number;
  paidAmount: number;
  pendingAmount: number;
  paymentStatus: string;
  loanAmount?: number;
  loanTerm?: number;
  loanApprovalStatus?: string;
}

// 保险信息
export interface InsuranceInfo {
  insuranceStatus: string;
  insuranceCompany?: string;
  insurancePolicyNo?: string;
  insuranceStartDate?: string;
  insuranceEndDate?: string;
  insuranceNotes?: string;
}

// JPJ注册信息
export interface JPJRegistrationInfo {
  jpjRegistrationStatus: string;
  registrationDate?: string;
  registrationRemarks?: string;
}

// 销售订单详情 - 兼容原有扁平化结构
export interface SalesOrderDetail {
  id: string;
  orderNo: string;
  createTime: string;
  updateTime: string;

  // 客户信息 (扁平化)
  customerName: string;
  customerPhone: string;
  buyerName: string;
  buyerPhone: string;
  buyerType: string;
  idType?: string;
  idNumber?: string;
  email?: string;
  address?: string;
  state?: string;
  city?: string;
  zipCode?: string;
  customerType?: string;

  // 门店信息
  storeRegion?: string;
  dealerCity?: string;
  dealerName?: string;
  salesConsultantName?: string;

  // 车辆信息
  model: string;
  variant: string;
  color: string;
  vin: string;
  vehiclePrice?: number;
  numberPlatesFee?: number;

  // 配件信息
  accessories?: any[];
  totalAccessoryAmount?: number;

  // 开票信息
  invoiceType?: string;
  invoiceName?: string;
  invoicePhone?: string;
  invoiceAddress?: string;

  // 权益信息
  rights?: any[];
  totalRightsDiscountAmount?: number;

  // 支付信息
  paymentMethod: string;
  totalAmount: number;
  depositAmount?: number;
  balanceAmount?: number;
  loanAmount?: number;
  loanTerm?: number;
  loanApprovalStatus?: string;

  // 保险信息
  policies?: any[];
  totalInsuranceAmount?: number;

  // OTR费用信息
  otrFees?: any[];
  totalOtrAmount?: number;

  // 变更记录
  changeRecords?: any[];

  // 计算字段
  totalInvoicePrice?: number;
  remainingAmount?: number;

  // 状态信息
  orderStatus: string;
  approvalStatus: string;
  paymentStatus?: string;
  insuranceStatus?: string;
  jpjRegistrationStatus?: string;

  // 其他信息
  selectedRights?: string[];
  remarks?: string;
  attachments?: string[];

  // 嵌套结构 (可选，用于新的API)
  customer?: CustomerInfo;
  buyer?: CustomerInfo;
  vehicle?: VehicleInfo;
  payment?: PaymentInfo;
  insurance?: InsuranceInfo;
  jpjRegistration?: JPJRegistrationInfo;
}

// 保存订单请求
export interface SaveOrderRequest {
  orderNo: string;
  color?: string;
  selectedRights?: string[];
  paymentMethod?: string;
  loanAmount?: number;
  loanTerm?: number;
  loanApprovalStatus?: string;
  insuranceNotes?: string;
  remarks?: string;
  attachments?: string[];
}

// 权益相关类型
export interface AvailableRight {
  id: string;
  rightCode: string;
  rightName: string;
  description?: string;
  discountAmount?: number;
  discountType?: 'fixed' | 'percentage';
  validityPeriod?: string;
  status: 'active' | 'inactive';
}

export interface SalesOrderRight {
  id: string;
  rightCode: string;
  rightName: string;
  discountAmount: number;
  discountType: 'fixed' | 'percentage';
  appliedAmount: number;
}

export interface RightSearchParams {
  pageNum?: number;
  pageSize?: number;
  rightCode?: string;
  rightName?: string;
  status?: string;
}

export interface PaginationResponse<T> {
  records: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}
