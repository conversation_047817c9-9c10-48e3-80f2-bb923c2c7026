export interface TestDriveRecord {
  // --- Core IDs ---
  id: string; // Frontend-only key
  testDriveNo: string; // Replaces testDriveId
  customerId?: number;

  // --- Prospect/Customer Info ---
  customerName: string; // Replaces prospectName & part of driverName in list
  customerPhone: string; // Replaces prospectPhone & part of driverPhone in list
  idType?: string | number; // Replaces prospectIdType
  idNumber?: string; // Replaces prospectIdNumber
  source?: string; // Replaces prospectSource
  email?: string; // Replaces prospectEmail
  region?: string; // Replaces prospectRegion
  address?: string; // Replaces prospectAddress
  sourceChannel?: string | number;

  // --- Driver Info (can be different from customer) ---
  driverName: string;
  driverPhone: string;
  driverIdType?: number | string | null;
  driverIdNumber: string;
  driverLicenseNumber: string;

  // --- Test Drive Details ---
  model: number | string | null; // Replaces testDriveModel
  variant: number | string | null; // Replaces testDriveVariant
  startMileage: number | null;
  endMileage: number | null;
  mileage?: number; // From list response
  startTime: string | number | null;
  endTime: string | number | null;
  feedback?: string; // Replaces testDriveFeedback

  // --- Sales & Store Info ---
  consultantId?: number;
  consultantName: number | string | null; // Replaces salesAdvisorName
  storeId?: number;
  storeName?: string; // From list response
  storeRegion?: string;

  // --- Timestamps & Flags ---
  createTime: string; // Replaces entryTime
  updateTime?: string;
  editable?: boolean; // From list response
}

export interface GetTestDriveListRequest {
  testDriveNo?: string;
  customerName?: string;
  customerPhone?: string;
  model?: string;
  variant?: string;
  startTimeBegin?: string;
  startTimeEnd?: string;
  storeIds?: number[];
  regionCodes?: string[];
  pageNum: number;
  pageSize: number;
}

// 创建试驾单的数据传输对象 (DTO)
export interface CreateTestDriveDto {
  customerId?: number;
  customerName: string;
  customerPhone: string;
  customerIdType?: string | number;
  customerIdNumber?: string;
  source?: string;
  email?: string;
  region?: string;
  address?: string;
  driverName: string;
  driverPhone: string;
  driverIdType?: number | string | null;
  driverIdNumber: string;
  driverLicenseNumber: string;
  model: number | string | null;
  variant: number | string | null;
  startMileage: number | null;
  endMileage: number | null;
  startTime: string | number | null;
  endTime: string | number | null;
  feedback?: string;
  consultantId?: number;
  consultantName: number | string | null;
  storeId?: number;
}

// 更新试驾单的数据传输对象 (DTO)
export interface UpdateTestDriveDto extends CreateTestDriveDto {
  id: string;
  testDriveNo: string;
}

// 导出请求参数
export type ExportTestDriveListRequest = Omit<GetTestDriveListRequest, 'pageNum' | 'pageSize'>

// 潜客搜索相关类型
export interface Lead {
  id: number;
  name: string;
  phone: string;
  idType?: string | number;
  idNumber?: string;
  email?: string;
  region?: string;
  address?: string;
  source?: string;
}

export interface SearchLeadsRequest {
  customerName?: string;
  customerPhone?: string;
  searchType?: number;
}

// 分页响应类型
export interface PageResult<T> {
  total: number;
  pages: number;
  current: number;
  size: number;
  records: T[];
}

// 统一API响应类型
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  code: string;
  traceId: string;
  result: T;
  timestamp: number;
}
