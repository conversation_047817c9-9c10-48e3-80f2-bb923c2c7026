export interface VehicleListItem {
  factoryOrderNo: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  fmrId: string;
  warehouseName: string;
  stockStatus: 'inStock' | 'inTransit' | 'allocated' | 'transferred';
  lockStatus: 'locked' | 'unlocked';
  invoiceStatus: 'invoiced' | 'notInvoiced';
  invoiceDate: string;
  deliveryStatus: 'delivered' | 'notDelivered';
  deliveryDate: string;
  storageDate: string;
  productionDate: string;
}

export interface VehicleSearchParams {
  vin?: string;
  factoryOrderNo?: string;
  warehouseName?: string;
  model?: string;
  variant?: string;
  color?: string;
  fmrId?: string;
  lockStatus?: 'locked' | 'unlocked';
  invoiceStatus?: 'invoiced' | 'notInvoiced';
  invoiceDateStart?: string;
  invoiceDateEnd?: string;
  storageDateStart?: string;
  storageDateEnd?: string;
  productionDateStart?: string;
  productionDateEnd?: string;
}

export interface VehicleDetail extends VehicleListItem {}

export interface PaginationResponse<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

export interface VehicleConfiguration {
  model: string;
  variants: VehicleVariant[];
}

export interface VehicleVariant {
  variant: string;
  colorOptions: string[];
}

export interface WarehouseInfo {
  warehouseName: string;
  warehouseCode: string;
  warehouseLocation: string;
  warehouseStatus: string;
} 