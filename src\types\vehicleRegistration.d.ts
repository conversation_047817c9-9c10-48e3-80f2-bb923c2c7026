// 车辆登记相关类型定义

// 车辆登记列表查询参数
export interface VehicleRegistrationListParams {
  orderNumber?: string; // 订单编号
  customerName?: string; // 购车人姓名
  customerPhone?: string; // 购车人手机号
  registrationStatus?: string; // 登记状态
  vin?: string; // VIN码
  insuranceStatus?: string; // 保险状态
  pushTimeStart?: string; // 推送时间开始
  pushTimeEnd?: string; // 推送时间结束
  salesAdvisor?: string; // 销售顾问
  pageNumber?: number; // 页码
  pageSize?: number; // 每页数量
}

// 车辆登记列表项
export interface VehicleRegistrationListItem {
  id: string; // 序号
  orderNumber: string; // 订单编号
  customerName: string; // 购车人姓名
  customerPhone: string; // 购车人手机号
  vin: string; // VIN码
  vehicleModel: string; // 车型
  vehicleColor: string; // 颜色
  insuranceStatus: string; // 保险状态
  insuranceCompany: string; // 保险公司
  registrationStatus: string; // 登记状态
  pushTime?: string; // 推送时间
  registrationFee?: number; // 登记费用
  salesAdvisor: string; // 销售顾问
}

// 车辆登记详情
export interface VehicleRegistrationDetail {
  // 订单基本信息
  orderInfo: {
    orderNumber: string; // 订单编号
    orderStatus: string; // 订单状态
    lastPushTime?: string; // 推送时间
    createdAt: string; // 订单创建时间
    salesAdvisor: string; // 销售顾问
  };

  // 购车人信息
  customerInfo: {
    name: string; // 姓名
    idType: string; // 身份证件类型
    idNumber: string; // 身份证号
    phone: string; // 手机号码
    email?: string; // 邮箱地址
    address: string; // 联系地址
    city: string; // 城市
    state: string; // 州/省
    postcode: string; // 邮政编码
  };

  // 车辆信息
  vehicleInfo: {
    vin: string; // VIN码
    model: string; // 车型信息
    color: string; // 车身颜色
    engineNumber: string; // 发动机号
    modelCode: string; // 车型代码
    variant: string; // 配置信息
    productionYear: string; // 生产年份
    manufactureDate: string; // 出厂日期
  };

  // 保险信息
  insuranceInfo: {
    status: string; // 保险状态
    company: string; // 保险公司
    policyNumber: string; // 保单号
    period: string; // 保险期限
    date: string; // 投保日期
    fee: number; // 保险费用
  };

  // JPJ登记信息
  jpjInfo: {
    status: string; // JPJ注册状态
    certificateNumber?: string; // 登记证书号
    pushTime?: string; // 推送时间
    completionTime?: string; // 登记完成时间
    operator?: string; // 推送操作人
    failureReason?: string; // 失败原因
  };

  // 登记费用明细
  feeDetails?: Array<{
    feeType: string; // 费用类型
    feeTypeDisplay: string; // 费用类型显示名称
    amount: number; // 费用金额
  }>;

  // 操作日志
  operationLogs: Array<{
    operationTime: string; // 操作时间
    operationType: string; // 操作类型
    operator: string; // 操作人员
    result: string; // 操作结果
    remark?: string; // 备注
  }>;
}

// 推送登记请求参数
export interface PushRegistrationParams {
  orderNumber: string; // 订单编号
  customerName: string; // 购车人姓名
  vin: string; // VIN码
  vehicleModel: string; // 车型
}

// 分页响应格式
export interface PaginationResponse<T> {
  data: T[];
  total: number;
  pageNumber: number;
  pageSize: number;
}

// 用户角色类型
export type UserRole = 'vehicle_registration_officer' | 'sales_consultant' | 'sales_manager';

// 登记状态类型
export type RegistrationStatus = 'pending' | 'processing' | 'success' | 'failed';

// 保险状态类型
export type InsuranceStatus = 'insured' | 'not_insured';
