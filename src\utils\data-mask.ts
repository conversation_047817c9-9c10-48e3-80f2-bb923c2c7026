// 数据脱敏工具类

/**
 * 手机号脱敏
 * @param phone 手机号
 * @returns 脱敏后的手机号
 */
export const maskPhone = (phone: string): string => {
  if (!phone) return ''
  if (phone.length < 7) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 身份证号脱敏
 * @param idNumber 身份证号
 * @returns 脱敏后的身份证号
 */
export const maskIdNumber = (idNumber: string): string => {
  if (!idNumber) return ''
  if (idNumber.length < 8) return idNumber
  const start = idNumber.substring(0, 4)
  const end = idNumber.substring(idNumber.length - 4)
  const middle = '*'.repeat(idNumber.length - 8)
  return start + middle + end
}

/**
 * 姓名脱敏
 * @param name 姓名
 * @returns 脱敏后的姓名
 */
export const maskName = (name: string): string => {
  if (!name) return ''
  if (name.length === 1) return name
  if (name.length === 2) return name[0] + '*'
  return name[0] + '*'.repeat(name.length - 2) + name[name.length - 1]
}
