<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('appointmentDashboard.pageTitle') }}</h1>

    <!-- Statistics Cards -->
    <el-row :gutter="20" class="mb-20 stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-text">
              <div class="stat-label">{{ t('appointmentDashboard.stats.totalAppointments') }}</div>
              <div class="stat-value">{{ stats.totalAppointments }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-text">
              <div class="stat-label">{{ t('appointmentDashboard.stats.arrivedCount') }}</div>
              <div class="stat-value">{{ stats.arrivedCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-text">
              <div class="stat-label">{{ t('appointmentDashboard.stats.notArrivedCount') }}</div>
              <div class="stat-value">{{ stats.notArrivedCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-text">
              <div class="stat-label">{{ t('appointmentDashboard.stats.arrivalRate') }}</div>
              <div class="stat-value">{{ stats.arrivalRate }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Filter Buttons -->
    <el-row class="mb-20">
      <el-button
        :type="activeFilter === 'all' ? 'primary' : 'default'"
        @click="handleFilterChange('all')"
      >
        {{ t('appointmentDashboard.filters.all') }}
      </el-button>
      <el-button
        :type="activeFilter === 'notArrived' ? 'primary' : 'default'"
        @click="handleFilterChange('notArrived')"
      >
        {{ t('appointmentDashboard.filters.notArrived') }}
      </el-button>
      <el-button
        :type="activeFilter === 'tomorrow' ? 'primary' : 'default'"
        @click="handleFilterChange('tomorrow')"
      >
        {{ t('appointmentDashboard.filters.tomorrow') }}
      </el-button>
    </el-row>

    <!-- Data Table -->
    <el-card class="table-card">
      <el-table :data="filteredAppointments" style="width: 100%" stripe>
        <el-table-column prop="plateNumber" :label="t('appointmentDashboard.table.plateNumber')" />
        <el-table-column prop="appointmentDate" :label="t('appointmentDashboard.table.appointmentDate')" />
        <el-table-column prop="timeSlot" :label="t('appointmentDashboard.table.timeSlot')" />
        <el-table-column prop="serviceType" :label="t('appointmentDashboard.table.serviceType')">
          <template #default="{ row }">
            <el-tag :type="getServiceTypeTag(row.serviceType)">
              {{ getServiceTypeText(row.serviceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="t('appointmentDashboard.table.status')">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- Empty State -->
      <div v-if="filteredAppointments.length === 0" class="empty-state">
        <p>{{ t('appointmentDashboard.emptyState') }}</p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ElButton, ElCard, ElCol, ElRow, ElTable, ElTableColumn, ElTag } from 'element-plus';
import { ref, computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';

const { t } = useModuleI18n('afterSales');

// 当前选中的筛选类型
const activeFilter = ref('all');

// Mock数据
const mockAppointments = ref([
  {
    plateNumber: '粤A12345',
    appointmentDate: '2024-01-15',
    timeSlot: '09:00-10:00',
    serviceType: 'maintenance',
    status: 'arrived'
  },
  {
    plateNumber: '京B67890',
    appointmentDate: '2024-01-15',
    timeSlot: '10:00-11:00',
    serviceType: 'repair',
    status: 'notArrived'
  },
  {
    plateNumber: '沪C11111',
    appointmentDate: '2024-01-15',
    timeSlot: '11:00-12:00',
    serviceType: 'maintenance',
    status: 'arrived'
  },
  {
    plateNumber: '深D22222',
    appointmentDate: '2024-01-15',
    timeSlot: '14:00-15:00',
    serviceType: 'repair',
    status: 'notFulfilled'
  },
  {
    plateNumber: '浙E33333',
    appointmentDate: '2024-01-15',
    timeSlot: '15:00-16:00',
    serviceType: 'maintenance',
    status: 'arrived'
  },
  {
    plateNumber: '苏F44444',
    appointmentDate: '2024-01-15',
    timeSlot: '16:00-17:00',
    serviceType: 'repair',
    status: 'notArrived'
  }
]);

// 统计数据
const stats = computed(() => {
  const today = '2024-01-15';
  const todayAppointments = mockAppointments.value.filter(item => item.appointmentDate === today);
  const totalAppointments = todayAppointments.length;
  const arrivedCount = todayAppointments.filter(item => item.status === 'arrived').length;
  const notArrivedCount = todayAppointments.filter(item => item.status === 'notArrived').length;
  const notFulfilledCount = todayAppointments.filter(item => item.status === 'notFulfilled').length;
  const arrivalRate = totalAppointments > 0 ? Math.round((arrivedCount / totalAppointments) * 100) : 0;

  const tomorrow = '2024-01-16';
  const tomorrowCount = mockAppointments.value.filter(item => item.appointmentDate === tomorrow).length;

  return {
    totalAppointments,
    arrivedCount,
    notArrivedCount,
    notFulfilledCount,
    arrivalRate,
    tomorrowCount
  };
});

// 根据筛选条件过滤数据
const filteredAppointments = computed(() => {
  const today = '2024-01-15';
  const tomorrow = '2024-01-16';

  switch (activeFilter.value) {
    case 'notArrived':
      return mockAppointments.value.filter(item =>
        item.appointmentDate === today && item.status === 'notArrived'
      );
    case 'tomorrow':
      return mockAppointments.value.filter(item => item.appointmentDate === tomorrow);
    default:
      return mockAppointments.value.filter(item => item.appointmentDate === today);
  }
});

// 处理筛选按钮点击
const handleFilterChange = (filterType: string) => {
  activeFilter.value = filterType;
};

// 获取服务类型标签样式
const getServiceTypeTag = (serviceType: string) => {
  return serviceType === 'maintenance' ? 'success' : 'warning';
};

// 获取服务类型显示文本
const getServiceTypeText = (serviceType: string) => {
  return t(`appointmentDashboard.serviceTypes.${serviceType}`);
};

// 获取状态标签样式
const getStatusTag = (status: string) => {
  switch (status) {
    case 'arrived':
      return 'success';
    case 'notArrived':
      return 'info';
    case 'notFulfilled':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取状态显示文本
const getStatusText = (status: string) => {
  return t(`appointmentDashboard.status.${status}`);
};
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
}
.stats-cards {
  margin-bottom: 20px;
}
.stat-card {
  text-align: center;
}
.stat-content {
  padding: 20px;
}
.stat-label {
  color: #666;
  margin-bottom: 8px;
}
.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #333;
}
.filter-card {
  margin-bottom: 20px;
}
.filter-buttons-left {
  display: flex;
  gap: 12px;
}
.filter-buttons-right {
  display: flex;
  justify-content: flex-end;
}
.mb-20 {
  margin-bottom: 20px;
}
.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}
.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transition: box-shadow 0.3s ease;
}
.filter-buttons-left .el-button {
  margin-right: 12px;
}
.filter-buttons-left .el-button:last-child {
  margin-right: 0;
}
</style>
