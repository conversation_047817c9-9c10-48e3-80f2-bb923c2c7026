<script setup lang="ts">
// 从售后模块的 API 导入相关功能函数
import { getServiceAdvisors, getTechnicians } from '@/api/modules/afterSales';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { getServiceAdvisorList } from '@/api/modules/masterData';
// 导入售后预约相关的 TypeScript 类型定义
import type {
  AppointmentListItem,
  AppointmentDetail,
  AppointmentListParams,
  ServiceAdvisor,
  Technician
} from '@/types/module.d.ts';
// 从 Element Plus 导入所需的图标
import { Search, View, Document } from '@element-plus/icons-vue';
// 从 Element Plus 导入所需的 UI 组件
import {
  ElButton,
  ElCard,
  ElCol,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElPagination,
  ElRow,
  ElSelect,
  ElTable,
  ElTableColumn,
  ElTag,
  ElDatePicker,
  ElDescriptions,
  ElDescriptionsItem
} from 'element-plus';
// 从 Vue 导入响应式和生命周期钩子函数
import { computed, onMounted, ref } from 'vue';

// 为保养包项定义一个具体的类型
interface MaintenanceItem {
  code: string;
  name: string;
  quantity: number;
  price: number;
}

// 定义临时的本地扩展类型以解决 Linter 错误
interface DisplayAppointmentListItem extends AppointmentListItem {
  maintenancePackage?: { items: MaintenanceItem[]; totalAmount: number };
  paymentStatus?: 'paid' | 'unpaid' | 'refunded';
  paymentAmount?: number;
  paymentOrderNumber?: string;
}
interface DisplayAppointmentDetail extends AppointmentDetail {
  maintenancePackage?: { items: MaintenanceItem[]; totalAmount: number };
  paymentStatus?: 'paid' | 'unpaid' | 'refunded';
  paymentAmount?: number;
  paymentOrderNumber?: string;
}

// 获取国际化函数 t 和当前语言环境 locale
const { t, tc } = useModuleI18n('afterSales');

// 计算车龄（月）
const calculateVehicleAgeInMonths = (productionDate: string | undefined): string => {
  if (!productionDate) {
    return tc('unknown');
  }
  const production = new Date(productionDate);
  const now = new Date();
  const years = now.getFullYear() - production.getFullYear();
  const months = now.getMonth() - production.getMonth();
  const totalMonths = years * 12 + months;
  return totalMonths >= 0 ? `${totalMonths} ${tc('months')}` : tc('unknown');
};

// 定义响应式变量来存储表格数据和相关状态
const appointments = ref<DisplayAppointmentListItem[]>([]); // 预约列表数据
const loading = ref(true); // 表格加载状态
const total = ref(0); // 数据总数，用于分页
const currentPage = ref(1); // 当前页码
const pageSize = ref(10); // 每页显示条数

// 查询表单数据，用于筛选预约列表
const searchParams = ref<AppointmentListParams>({
  appointmentId: '', // 预约单号
  licensePlate: '', // 车牌号
  reservationPhone: '', // 预约人手机号
  servicePhone: '', // 送修人手机号
  dateRange: undefined, // 创建时间范围
  status: undefined, // 状态
  serviceType: undefined, // 维修类型
  serviceAdvisorId: undefined, // 服务顾问
  technicianId: undefined // 技师
});

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.APPOINTMENT_STATUS,
  DICTIONARY_TYPES.WORK_ORDER_TYPE
]);

// 服务顾问数据 (主数据)
const serviceAdvisorOptions = ref([]);
const masterDataLoading = ref(false);

// 服务顾问和技师列表 (保留原有的)
const serviceAdvisors = ref<ServiceAdvisor[]>([]);
const technicians = ref<Technician[]>([]);

// 详情对话框相关的响应式变量
const detailDialogVisible = ref(false);
const currentAppointmentDetail = ref<DisplayAppointmentDetail | null>(null);
const detailLoading = ref(false);

// 获取字典选项的计算属性
const appointmentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPOINTMENT_STATUS));
const serviceTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.WORK_ORDER_TYPE));

// 加载服务顾问数据
const loadServiceAdvisors = async () => {
  masterDataLoading.value = true;
  try {
    serviceAdvisorOptions.value = await getServiceAdvisorList();
  } catch (error) {
    console.error('获取服务顾问数据失败:', error);
    // ElMessage.error('获取服务顾问数据失败'); // 需要导入ElMessage
  } finally {
    masterDataLoading.value = false;
  }
};

// 计算属性：当前日期（用于默认筛选）
const todayDate = computed(() => {
  const today = new Date();
  return today.toISOString().split('T')[0];
});

// 异步函数：获取预约列表数据
const fetchAppointments = async () => {
  loading.value = true;
  try {
    // 使用mock数据
    const { mockAppointmentList } = await import('@/mock/data/afterSales');
    setTimeout(() => {
      appointments.value = mockAppointmentList;
      total.value = appointments.value.length;
      loading.value = false;
    }, 300);
  } catch {
    console.error('Failed to fetch appointments:');
    ElMessage.error(tc('operationFailed'));
    loading.value = false;
  }
};

// 异步函数：获取服务顾问和技师列表
const fetchStaffList = async () => {
  try {
    const [advisors, techs] = await Promise.all([getServiceAdvisors(), getTechnicians()]);
    serviceAdvisors.value = advisors;
    technicians.value = techs;
  } catch {
    console.error('Failed to fetch staff list:');
  }
};

// 事件处理函数：搜索按钮点击
const handleSearch = () => {
  currentPage.value = 1;
  fetchAppointments();
};

// 事件处理函数：重置搜索表单
const resetSearch = () => {
  searchParams.value = {
    appointmentId: '',
    licensePlate: '',
    reservationPhone: '',
    servicePhone: '',
    dateRange: undefined,
    status: undefined,
    serviceType: undefined,
    serviceAdvisorId: undefined,
    technicianId: undefined
  };
  currentPage.value = 1;
  fetchAppointments();
};

// 事件处理函数：分页页码改变
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchAppointments();
};

// 事件处理函数：分页每页数量改变
const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchAppointments();
};

// 事件处理函数：查看详情
const handleViewDetail = async (row: AppointmentListItem) => {
  detailLoading.value = true;
  detailDialogVisible.value = true;
  try {
    // 模拟详情数据
    setTimeout(() => {
      const detail: DisplayAppointmentDetail = {
        ...row,
        store: {
          id: 'store_001',
          name: '北京朝阳店',
          address: '北京市朝阳区工体北路甲6号'
        },
        paymentStatus: row.status === 'pending_payment' ? 'unpaid' : 'paid',
        paymentAmount: 1500,
        paymentOrderNumber: 'PAY_' + Date.now(),
        maintenancePackage: {
          items: [
            { code: 'SVC001', name: t('mock.maintenancePackageName'), quantity: 1, price: 800 },
            { code: 'PART001', name: t('mock.oilFilter'), quantity: 1, price: 150 }
          ],
          totalAmount: 950
        }
      };
      currentAppointmentDetail.value = detail;
      detailLoading.value = false;
    }, 300);
  } catch {
    ElMessage.error(tc('operationFailed'));
    detailDialogVisible.value = false;
    detailLoading.value = false;
  }
};

// 环检单确认对话框相关的响应式变量
const inspectionConfirmDialogVisible = ref(false);
const currentInspectionAppointment = ref<DisplayAppointmentListItem | null>(null);
const inspectionConfirmLoading = ref(false);
const editableServiceContact = ref({ name: '', phone: '' });

// 事件处理函数：创建环检单
const handleCreateQualityInspection = async (row: AppointmentListItem) => {
  // 增强行数据以包含对话框所需的额外信息
  const inspectionAppointmentData: DisplayAppointmentListItem = {
    ...row,
    paymentStatus: row.status === 'pending_payment' ? 'unpaid' : 'paid',
    paymentAmount: 1500,
    paymentOrderNumber: `PAY_${Date.now()}`,
    maintenancePackage: {
      items: [
        { code: 'SVC001', name: t('mock.maintenancePackageName'), quantity: 1, price: 800 },
        { code: 'PART001', name: t('mock.oilFilter'), quantity: 1, price: 150 }
      ],
      totalAmount: 950
    }
  };
  currentInspectionAppointment.value = inspectionAppointmentData;

  // 初始化可编辑的送修人信息
  editableServiceContact.value = {
    name: row.serviceContactName,
    phone: row.serviceContactPhone
  };
  inspectionConfirmDialogVisible.value = true;
};

// 确认创建环检单
const confirmCreateQualityInspection = async () => {
  if (!currentInspectionAppointment.value) return;

  inspectionConfirmLoading.value = true;
  try {
    // 模拟创建环检单的过程
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 创建环检单成功后，为当前预约分配服务顾问并标记为已创建环检单
    const appointmentIndex = appointments.value.findIndex(
      (app) => app.id === currentInspectionAppointment.value!.id
    );
    if (appointmentIndex !== -1) {
      appointments.value[appointmentIndex].serviceAdvisor = { id: 'SA001', name: '张三' };
      appointments.value[appointmentIndex].inspectionCreated = true;
      // 更新送修人信息
      appointments.value[appointmentIndex].serviceContactName = editableServiceContact.value.name;
      appointments.value[appointmentIndex].serviceContactPhone = editableServiceContact.value.phone;
    }

    ElMessage.success(t('messages.inspectionCreatedSuccess'));
    inspectionConfirmDialogVisible.value = false;
  } catch {
    ElMessage.error(t('messages.inspectionCreatedFailed'));
  } finally {
    inspectionConfirmLoading.value = false;
  }
};

// 取消创建环检单
const cancelCreateQualityInspection = () => {
  inspectionConfirmDialogVisible.value = false;
  currentInspectionAppointment.value = null;
};

// 格式化显示方法 - 使用字典接口
const formatAppointmentStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.APPOINTMENT_STATUS, status) || status;
const formatServiceType = (type: string) => getNameByCode(DICTIONARY_TYPES.WORK_ORDER_TYPE, type) || type;

// 格式化价格显示
const formatPrice = (price: number) => {
  return `¥${price.toLocaleString()}`;
};

// 生命周期钩子：组件挂载后执行
onMounted(() => {
  // 设置默认日期范围为今天
  searchParams.value.dateRange = [todayDate.value, todayDate.value];
  fetchAppointments();
  fetchStaffList(); // 保留原有的获取方法
  loadServiceAdvisors(); // 新增主数据API获取方法
});
</script>

<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('appointmentManagement') }}</h1>

    <!-- 搜索区域卡片 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('labels.appointmentId')">
              <el-input
                v-model="searchParams.appointmentId"
                :placeholder="t('placeholders.appointmentId')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.licensePlate')">
              <el-input
                v-model="searchParams.licensePlate"
                :placeholder="t('placeholders.licensePlate')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.reservationPhone')">
              <el-input
                v-model="searchParams.reservationPhone"
                :placeholder="t('placeholders.reservationPhone')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.servicePhone')">
              <el-input
                v-model="searchParams.servicePhone"
                :placeholder="t('placeholders.servicePhone')"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('labels.creationTime')">
              <el-date-picker
                v-model="searchParams.dateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.status')">
              <el-select
                v-model="searchParams.status"
                :placeholder="t('placeholders.selectStatus')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="item in appointmentStatusOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.serviceType')">
              <el-select
                v-model="searchParams.serviceType"
                :placeholder="t('placeholders.selectServiceType')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="item in serviceTypeOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.serviceAdvisor')">
              <el-select
                v-model="searchParams.serviceAdvisorId"
                :placeholder="t('placeholders.selectServiceAdvisor')"
                clearable
                style="width: 100%"
                :loading="masterDataLoading"
              >
                <el-option
                  v-for="item in serviceAdvisorOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="buttons-col text-right">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ tc('search') }}
            </el-button>
            <el-button @click="resetSearch">
              {{ tc('reset') }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 新增操作区域卡片 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20">
        <el-col :span="12">
          <span class="filter-summary">
            {{ t('labels.totalRecords', { total }) }}
          </span>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" :icon="Document">
            {{ t('exportExcel') }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表格区域卡片 -->
    <el-card class="table-card">
      <el-table :data="appointments" v-loading="loading" style="width: 100%">
        <el-table-column :label="t('headers.appointmentId')" min-width="140">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="licensePlate"
          :label="t('headers.licensePlate')"
          min-width="100"
        />
        <el-table-column
          prop="reservationContactName"
          :label="t('headers.reservationContactName')"
          min-width="100"
        />
        <el-table-column
          prop="reservationContactPhone"
          :label="t('headers.reservationContactPhone')"
          min-width="120"
        />
        <el-table-column
          prop="serviceContactName"
          :label="t('headers.serviceContactName')"
          min-width="100"
        />
        <el-table-column
          prop="serviceContactPhone"
          :label="t('headers.serviceContactPhone')"
          min-width="120"
        />
        <el-table-column :label="t('headers.appointmentDate')" min-width="100">
          <template #default="{ row }">
            {{ row.appointmentTime }}
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.timeSlot')" min-width="120">
          <template #default="{ row }">
            {{ row.timeSlot }}
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.serviceType')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.serviceType === 'maintenance' ? 'success' : 'warning'">
              {{ t(`serviceTypes.${row.serviceType}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.status')" min-width="100">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === 'arrived'
                  ? 'success'
                  : row.status === 'not_arrived'
                    ? 'info'
                    : row.status === 'cancelled'
                      ? 'info'
                      : row.status === 'pending_payment'
                        ? 'warning'
                        : 'danger'
              "
            >
              {{ t(`statuses.${row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.serviceAdvisor')" min-width="100">
          <template #default="{ row }">
            {{ row.serviceAdvisor?.name || t('labels.unassigned') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.qualityInspectionId')" min-width="140">
          <template #default="{ row }">
            {{ row.qualityInspectionId || t('labels.notGenerated') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.creationTime')" min-width="150">
          <template #default="{ row }">
            {{ row.createdAt }}
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="View" link @click="handleViewDetail(row)">
              {{ tc('detail') }}
            </el-button>
            <el-button
              v-if="row.status === 'arrived' && !row.inspectionCreated"
              type="success"
              :icon="Document"
              link
              @click="handleCreateQualityInspection(row)"
            >
              {{ t('createInspection') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-card>

    <!-- 预约详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="t('appointmentDetail') + (currentAppointmentDetail ? ' - ' + currentAppointmentDetail.id : '')"
      width="800px"
      class="appointment-detail-dialog"
    >
      <div v-if="currentAppointmentDetail" v-loading="detailLoading">
        <!-- 预约信息 -->
        <el-descriptions :title="t('titles.appointmentInfo')" :column="2" border>
          <el-descriptions-item :label="t('labels.appointmentId')">
            {{ currentAppointmentDetail.id }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.status')">
            <el-tag
              :type="
                currentAppointmentDetail.status === 'arrived'
                  ? 'success'
                  : currentAppointmentDetail.status === 'not_arrived'
                    ? 'info'
                    : currentAppointmentDetail.status === 'cancelled'
                      ? 'info'
                      : 'danger'
              "
            >
              {{ t(`statuses.${currentAppointmentDetail.status}`) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.appointmentTime')">
            {{ currentAppointmentDetail.appointmentTime }} {{ currentAppointmentDetail.timeSlot }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.serviceType')">
            <el-tag :type="currentAppointmentDetail.serviceType === 'maintenance' ? 'success' : 'warning'">
              {{ t(`serviceTypes.${currentAppointmentDetail.serviceType}`) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="currentAppointmentDetail.customerDescription"
            :label="t('labels.customerDescription')"
            :span="2"
          >
            {{ currentAppointmentDetail.customerDescription }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 客户信息 -->
        <el-descriptions :title="t('titles.customerInfo')" :column="2" border class="mt-20">
          <el-descriptions-item :label="t('labels.reservationContactName')">
            {{ currentAppointmentDetail.reservationContactName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.reservationContactPhone')">
            {{ currentAppointmentDetail.reservationContactPhone }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.serviceContactName')">
            {{ currentAppointmentDetail.serviceContactName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.serviceContactPhone')">
            {{ currentAppointmentDetail.serviceContactPhone }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 车辆信息 -->
        <el-descriptions :title="t('titles.vehicleInfo')" :column="2" border class="mt-20">
          <el-descriptions-item :label="t('labels.licensePlate')">
            {{ currentAppointmentDetail.licensePlate }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.vin')">
            {{ currentAppointmentDetail.vin }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.model')">
            {{ currentAppointmentDetail.model }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.variant')">
            {{ currentAppointmentDetail.variant }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.color')">
            {{ currentAppointmentDetail.color }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.mileage')">
            {{
              currentAppointmentDetail.mileage
                ? currentAppointmentDetail.mileage + 'km'
                : tc('unknown')
            }}
          </el-descriptions-item>
          <el-descriptions-item :label="tc('vehicleAge')">
            {{ calculateVehicleAgeInMonths(currentAppointmentDetail.productionDate) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 预约信息 -->
        <el-descriptions :title="t('titles.appointmentBookingInfo')" :column="2" border class="mt-20">
          <el-descriptions-item :label="t('labels.store')">
            {{ currentAppointmentDetail.store.name }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.serviceAdvisor')">
            {{ currentAppointmentDetail.serviceAdvisor?.name || t('labels.unassigned') }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 服务内容 - 保养类型显示 -->
        <div v-if="currentAppointmentDetail.serviceType === 'maintenance'" class="mt-20">
          <h4>{{ t('titles.serviceContent') }}</h4>
          <div v-if="currentAppointmentDetail.maintenancePackage" class="service-package">
            <!-- 保养服务包列表 -->
            <el-table :data="currentAppointmentDetail.maintenancePackage.items" border style="width: 100%">
              <el-table-column prop="code" :label="t('headers.servicePackageCode')" width="120" />
              <el-table-column prop="name" :label="t('headers.servicePackageName')" />
              <el-table-column prop="quantity" :label="t('headers.quantity')" width="80" />
              <el-table-column prop="price" :label="t('headers.price')" width="100">
                <template #default="scope">
                  {{ formatPrice(scope.row.price) }}
                </template>
              </el-table-column>
            </el-table>

            <!-- 总金额行 -->
            <div class="total-amount mt-10">
              <el-descriptions :column="1" border>
                <el-descriptions-item :label="t('labels.totalAmount')">
                  <strong style="color: #409eff; font-size: 16px">
                    {{ formatPrice(currentAppointmentDetail.maintenancePackage.totalAmount) }}
                  </strong>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>

        <!-- 支付信息 - 保养类型显示 -->
        <el-descriptions
          v-if="
            currentAppointmentDetail.paymentStatus &&
            currentAppointmentDetail.serviceType === 'maintenance'
          "
          :title="t('titles.paymentInfo')"
          :column="2"
          border
          class="mt-20"
        >
          <el-descriptions-item :label="t('labels.paymentStatus')">
            <el-tag
              :type="
                currentAppointmentDetail.paymentStatus === 'paid'
                  ? 'success'
                  : currentAppointmentDetail.paymentStatus === 'unpaid'
                    ? 'warning'
                    : 'info'
              "
            >
              {{ t(`paymentStatuses.${currentAppointmentDetail.paymentStatus}`) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.paymentAmount')">
            {{
              currentAppointmentDetail.paymentAmount
                ? formatPrice(currentAppointmentDetail.paymentAmount)
                : '-'
            }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.paymentOrderNumber')">
            {{ currentAppointmentDetail.paymentOrderNumber || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="detailDialogVisible = false">{{ tc('cancel') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 环检单确认对话框 -->
    <el-dialog
      v-model="inspectionConfirmDialogVisible"
      :title="t('titles.confirmCreateInspection')"
      width="800px"
      class="appointment-detail-dialog"
    >
      <div v-if="currentInspectionAppointment" v-loading="inspectionConfirmLoading">
        <div
          style="
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 4px;
          "
        >
          <p style="margin: 0; color: #0369a1; font-weight: 500">
            <i class="el-icon-info"></i> {{ t('messages.confirmCreateInspection') }}
          </p>
        </div>

        <!-- 预约信息 -->
        <el-descriptions :title="t('titles.appointmentInfo')" :column="2" border>
          <el-descriptions-item :label="t('labels.appointmentId')">
            {{ currentInspectionAppointment.id }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.status')">
            <el-tag
              :type="
                currentInspectionAppointment.status === 'arrived'
                  ? 'success'
                  : currentInspectionAppointment.status === 'not_arrived'
                    ? 'info'
                    : currentInspectionAppointment.status === 'cancelled'
                      ? 'info'
                      : currentInspectionAppointment.status === 'pending_payment'
                        ? 'warning'
                        : 'danger'
              "
            >
              {{ t(`statuses.${currentInspectionAppointment.status}`) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.appointmentTime')">
            {{ currentInspectionAppointment.appointmentTime }}
            {{ currentInspectionAppointment.timeSlot }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.serviceType')">
            <el-tag
              :type="currentInspectionAppointment.serviceType === 'maintenance' ? 'success' : 'warning'"
            >
              {{ t(`serviceTypes.${currentInspectionAppointment.serviceType}`) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="currentInspectionAppointment.customerDescription"
            :label="t('labels.customerDescription')"
            :span="2"
          >
            {{ currentInspectionAppointment.customerDescription }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 客户信息 -->
        <el-descriptions :title="t('titles.customerInfo')" :column="2" border class="mt-20">
          <el-descriptions-item :label="t('labels.reservationContactName')">
            {{ currentInspectionAppointment.reservationContactName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.reservationContactPhone')">
            {{ currentInspectionAppointment.reservationContactPhone }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.serviceContactName')">
            <el-input
              v-model="editableServiceContact.name"
              :placeholder="t('placeholders.serviceContactName')"
              style="width: 200px"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.serviceContactPhone')">
            <el-input
              v-model="editableServiceContact.phone"
              :placeholder="t('placeholders.serviceContactPhone')"
              style="width: 200px"
            />
          </el-descriptions-item>
        </el-descriptions>

        <!-- 车辆信息 -->
        <el-descriptions :title="t('titles.vehicleInfo')" :column="2" border class="mt-20">
          <el-descriptions-item :label="t('labels.licensePlate')">
            {{ currentInspectionAppointment.licensePlate }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.vin')">
            {{ currentInspectionAppointment.vin }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.model')">
            {{ currentInspectionAppointment.model }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.variant')">
            {{ currentInspectionAppointment.variant }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.color')">
            {{ currentInspectionAppointment.color }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.mileage')">
            {{
              currentInspectionAppointment.mileage
                ? currentInspectionAppointment.mileage + 'km'
                : tc('unknown')
            }}
          </el-descriptions-item>
          <el-descriptions-item :label="tc('vehicleAge')">
            {{ calculateVehicleAgeInMonths(currentInspectionAppointment.productionDate) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 服务内容 - 保养类型显示 -->
        <div v-if="currentInspectionAppointment.serviceType === 'maintenance'" class="mt-20">
          <h4>{{ t('titles.serviceContent') }}</h4>
          <div v-if="currentInspectionAppointment.maintenancePackage" class="service-package">
            <!-- 保养服务包列表 -->
            <el-table
              :data="currentInspectionAppointment.maintenancePackage.items"
              border
              style="width: 100%"
            >
              <el-table-column
                prop="code"
                :label="t('headers.servicePackageCode')"
                width="120"
              />
              <el-table-column prop="name" :label="t('headers.servicePackageName')" />
              <el-table-column prop="quantity" :label="t('headers.quantity')" width="80" />
              <el-table-column prop="price" :label="t('headers.price')" width="100">
                <template #default="scope">
                  {{ formatPrice(scope.row.price) }}
                </template>
              </el-table-column>
            </el-table>

            <!-- 总金额行 -->
            <div class="total-amount mt-10">
              <el-descriptions :column="1" border>
                <el-descriptions-item :label="t('labels.totalAmount')">
                  <strong style="color: #409eff; font-size: 16px">
                    {{ formatPrice(currentInspectionAppointment.maintenancePackage.totalAmount) }}
                  </strong>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>

        <!-- 支付信息 - 保养类型显示 -->
        <el-descriptions
          v-if="
            currentInspectionAppointment.paymentStatus &&
            currentInspectionAppointment.serviceType === 'maintenance'
          "
          :title="t('titles.paymentInfo')"
          :column="2"
          border
          class="mt-20"
        >
          <el-descriptions-item :label="t('labels.paymentStatus')">
            <el-tag
              :type="
                currentInspectionAppointment.paymentStatus === 'paid'
                  ? 'success'
                  : currentInspectionAppointment.paymentStatus === 'unpaid'
                    ? 'warning'
                    : 'info'
              "
            >
              {{ t(`paymentStatuses.${currentInspectionAppointment.paymentStatus}`) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.paymentAmount')">
            {{
              currentInspectionAppointment.paymentAmount
                ? formatPrice(currentInspectionAppointment.paymentAmount)
                : '-'
            }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('labels.paymentOrderNumber')">
            {{ currentInspectionAppointment.paymentOrderNumber || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="cancelCreateQualityInspection">{{ tc('cancel') }}</el-button>
          <el-button
            type="primary"
            @click="confirmCreateQualityInspection"
            :loading="inspectionConfirmLoading"
          >
            {{ t('buttons.confirmCreation') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
// ... a lot of styles
</style>
