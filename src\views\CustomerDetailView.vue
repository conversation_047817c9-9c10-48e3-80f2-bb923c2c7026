<script setup lang="ts">
import { Aim, CirclePlus, Edit, House, Message, Phone } from '@element-plus/icons-vue'; // 导入必要的图标
import { ElAvatar, ElButton, ElCard, ElCol, ElDatePicker, ElForm, ElFormItem, ElIcon, ElInput, ElRow, ElSelect, ElTimeline, ElTimelineItem } from 'element-plus';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// 模拟数据 (仅用于布局展示，无实际业务逻辑)
const customerInfo = ref({
  name: '客户姓名',
  phone: '138****1234',
  gender: '男',
  age: '30',
  source: '线上广告',
  firstEntry: '2023-01-15',
  salesConsultant: '销售顾问A',
  budget: '20-30万',
  expectedPurchase: '1个月内',
});

const currentIntention = ref({
  model: '某车型-豪华版',
  color: '白色',
  level: 'A级',
  lastUpdate: '2023-05-20 10:30',
});

const intentionHistory = ref([
  { time: '2023-05-18 14:00', from: '车型A', to: '车型B', operator: '销售顾问A' },
  { time: '2023-05-10 11:00', from: '车型X', to: '车型A', operator: '销售顾问B' },
]);

const newFollowUp = ref({
  method: '',
  content: '',
  nextFollowUp: '',
  intentionLevel: '',
});

const followUpHistory = ref([
  { time: '2023-05-22 09:00', type: 'phone', content: '电话沟通，客户对价格有疑问，意向级别调整为A级。', levelChange: 'B -> A', operator: '销售顾问A' },
  { time: '2023-05-20 16:00', type: 'wechat', content: '微信发送车型配置信息。', levelChange: '', operator: '销售顾问B' },
  { time: '2023-05-18 10:00', type: 'visit', content: '客户到店试驾，反馈良好。', levelChange: '', operator: '销售顾问A' },
  { time: '2023-05-15 14:30', type: 'test_drive', content: '安排客户试驾。', levelChange: '', operator: '销售顾问C' },
]);

// 对话框显示状态
const editCustomerDialogVisible = ref(false);
const quickEditIntentionDialogVisible = ref(false);

// 表单数据
const editCustomerForm = ref({
  name: '',
  phone: '',
  gender: '',
  age: '',
  source: '',
  firstEntry: '',
  salesConsultant: '',
  budget: '',
  expectedPurchase: '',
});

const quickEditIntentionForm = ref({
  model: '',
  color: '',
  level: '',
  lastUpdate: '',
});

// 模拟事件处理函数
const handleEditCustomer = () => {
  editCustomerForm.value = { ...customerInfo.value }; // 初始化表单数据
  editCustomerDialogVisible.value = true; // 打开对话框
};

const handleQuickEditIntention = () => {
  quickEditIntentionForm.value = { ...currentIntention.value }; // 初始化表单数据
  quickEditIntentionDialogVisible.value = true; // 打开对话框
};

const handleSaveCustomerEdit = () => {
  console.log('保存客户编辑:', editCustomerForm.value);
  // 实际项目中这里会调用API保存数据
  customerInfo.value = { ...editCustomerForm.value }; // 模拟更新数据
  editCustomerDialogVisible.value = false; // 关闭对话框
};

const handleSaveQuickEditIntention = () => {
  console.log('保存快速修改意向:', quickEditIntentionForm.value);
  // 实际项目中这里会调用API保存数据

  // 记录旧的意向信息，用于生成历史记录
  const oldModel = currentIntention.value.model;
  const oldColor = currentIntention.value.color;
  const oldLevel = currentIntention.value.level;

  // 更新当前意向信息
  currentIntention.value = { ...quickEditIntentionForm.value };

  // 添加新的意向变更历史记录
  const now = new Date();
  const timeString = now.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' });

  intentionHistory.value.unshift({
    time: timeString,
    from: `${oldModel} (${oldColor}) - ${oldLevel}`,
    to: `${currentIntention.value.model} (${currentIntention.value.color}) - ${currentIntention.value.level}`,
    operator: '系统自动更新',
  });

  quickEditIntentionDialogVisible.value = false; // 关闭对话框
};

const handleSubmitFollowUp = () => {
  console.log('提交跟进');
};

const handleExpandFollowUp = () => {
  console.log('展开更多跟进内容');
};
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('customer.detailViewTitle') || '潜客详情' }}</h1>

    <!-- 1. 客户基本信息概览 -->
    <el-card class="mb-20 card-customer-info">
      <el-row :gutter="20" align="middle" class="card-header">
        <el-col :span="18" class="customer-profile">
          <el-avatar :size="50" src="https://empty" />
          <div class="customer-details">
            <div class="customer-name">{{ customerInfo.name }}</div>
            <div class="customer-contact">{{ customerInfo.phone }}</div>
            <div class="customer-tags">
              <el-tag size="small">A级客户</el-tag>
              <el-tag size="small" type="info">潜在客户</el-tag>
              <el-tag size="small" type="success">高意向</el-tag>
            </div>
          </div>
        </el-col>
        <el-col :span="6" class="text-right">
          <el-button type="primary" :icon="Edit" @click="handleEditCustomer">编辑</el-button>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="customer-attributes">
        <el-col :span="12">
          <div class="attribute-item"><span>性别:</span> <span>{{ customerInfo.gender }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="attribute-item"><span>年龄:</span> <span>{{ customerInfo.age }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="attribute-item"><span>客户来源:</span> <span>{{ customerInfo.source }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="attribute-item"><span>首次进入:</span> <span>{{ customerInfo.firstEntry }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="attribute-item"><span>负责销售:</span> <span>{{ customerInfo.salesConsultant }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="attribute-item"><span>预算范围:</span> <span>{{ customerInfo.budget }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="attribute-item"><span>期望购车:</span> <span>{{ customerInfo.expectedPurchase }}</span></div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 2. 意向信息管理 -->
    <el-card class="mb-20 card-intention-management">
      <h3>意向信息</h3>
      <el-row :gutter="20" align="middle" class="intention-current mb-15">
        <el-col :span="4">
          <div class="intention-image-placeholder"></div> <!-- 图片占位符 -->
        </el-col>
        <el-col :span="16" class="intention-details">
          <div>**当前意向:** {{ currentIntention.model }} ({{ currentIntention.color }})</div>
          <div>**意向级别:** <el-tag size="small" type="warning">{{ currentIntention.level }}</el-tag></div>
          <div>**最近更新:** {{ currentIntention.lastUpdate }}</div>
        </el-col>
        <el-col :span="4" class="text-right">
          <el-button type="primary" :icon="Edit" @click="handleQuickEditIntention">快速修改</el-button>
        </el-col>
      </el-row>

      <h4>意向变更历史</h4>
      <el-timeline class="intention-history">
        <el-timeline-item
          v-for="(item, index) in intentionHistory"
          :key="index"
          :timestamp="item.time"
          placement="top"
          type="primary"
          hollow
        >
          <div class="timeline-content">
            <div>从 {{ item.from }} 变为 {{ item.to }}</div>
            <div class="timeline-meta">操作人: {{ item.operator }}</div>
          </div>
        </el-timeline-item>
        <el-timeline-item timestamp="最初意向" placement="top" type="primary" hollow>
          <div class="timeline-content">
            <div>客户最初意向</div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 3. 跟进记录与管理 -->
    <el-card class="mb-20 card-follow-up-management">
      <h3>跟进记录</h3>
      <el-form :model="newFollowUp" label-position="top" class="follow-up-form mb-20">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="跟进方式">
              <el-select v-model="newFollowUp.method" placeholder="请选择跟进方式" style="width: 100%;">
                <el-option label="电话" value="phone" />
                <el-option label="微信" value="wechat" />
                <el-option label="到店" value="visit" />
                <el-option label="试驾" value="test_drive" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次跟进计划">
              <el-date-picker v-model="newFollowUp.nextFollowUp" type="datetime" placeholder="选择下次跟进时间" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="跟进内容">
          <el-input v-model="newFollowUp.content" type="textarea" :rows="3" placeholder="详细记录沟通内容" />
        </el-form-item>
        <el-form-item label="意向级别调整">
          <el-select v-model="newFollowUp.intentionLevel" placeholder="调整客户意向级别" style="width: 100%;">
            <el-option label="A级" value="A" />
            <el-option label="B级" value="B" />
            <el-option label="C级" value="C" />
          </el-select>
        </el-form-item>
        <div class="text-right">
          <el-button type="primary" :icon="CirclePlus" @click="handleSubmitFollowUp">提交跟进</el-button>
        </div>
      </el-form>

      <h4>历史跟进时间线</h4>
      <el-timeline class="follow-up-history">
        <el-timeline-item
          v-for="(item, index) in followUpHistory"
          :key="index"
          :timestamp="item.time"
          placement="top"
          type="primary"
          hollow
        >
          <div class="timeline-content">
            <div class="follow-up-header">
              <el-icon v-if="item.type === 'phone'"><Phone /></el-icon>
              <el-icon v-else-if="item.type === 'wechat'"><Message /></el-icon>
              <el-icon v-else-if="item.type === 'visit'"><House /></el-icon>
              <el-icon v-else-if="item.type === 'test_drive'"><Aim /></el-icon>
              <span>{{ item.content }}</span>
            </div>
            <div v-if="item.levelChange" class="level-change">意向级别变化: {{ item.levelChange }}</div>
            <div class="timeline-meta">跟进人: {{ item.operator }}</div>
            <el-button link @click="handleExpandFollowUp">展开</el-button>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 4. 客户编辑对话框 -->
    <el-dialog
      v-model="editCustomerDialogVisible"
      title="编辑客户信息"
      width="50%"
      >
      <el-form :model="editCustomerForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户姓名">
              <el-input v-model="editCustomerForm.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input v-model="editCustomerForm.phone"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别">
              <el-select v-model="editCustomerForm.gender" placeholder="请选择" style="width: 100%;">
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
                <el-option label="未知" value="未知" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄">
              <el-input v-model="editCustomerForm.age"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户来源">
              <el-input v-model="editCustomerForm.source"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="首次进入">
              <el-date-picker v-model="editCustomerForm.firstEntry" type="date" placeholder="选择日期" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责销售">
              <el-input v-model="editCustomerForm.salesConsultant"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预算范围">
              <el-input v-model="editCustomerForm.budget"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="期望购车">
              <el-input v-model="editCustomerForm.expectedPurchase"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editCustomerDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveCustomerEdit">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 5. 快速修改意向对话框 -->
    <el-dialog
      v-model="quickEditIntentionDialogVisible"
      title="快速修改意向"
      width="40%"
    >
      <el-form :model="quickEditIntentionForm" label-width="100px">
        <el-form-item label="当前意向车型">
          <el-input v-model="quickEditIntentionForm.model"></el-input>
        </el-form-item>
        <el-form-item label="车型颜色">
          <el-input v-model="quickEditIntentionForm.color"></el-input>
        </el-form-item>
        <el-form-item label="意向级别">
          <el-select v-model="quickEditIntentionForm.level" placeholder="请选择意向级别" style="width: 100%;">
            <el-option label="A级" value="A级" />
            <el-option label="B级" value="B级" />
            <el-option label="C级" value="C级" />
          </el-select>
        </el-form-item>
        <el-form-item label="最近更新时间">
          <el-date-picker v-model="quickEditIntentionForm.lastUpdate" type="datetime" placeholder="选择日期和时间" style="width: 100%;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="quickEditIntentionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveQuickEditIntention">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-15 {
  margin-bottom: 15px;
}

.text-right {
  text-align: right;
}

.card-customer-info {
  .customer-profile {
    display: flex;
    align-items: center;
    .el-avatar {
      margin-right: 15px;
    }
    .customer-details {
      .customer-name {
        font-size: 1.2em;
        font-weight: bold;
        margin-bottom: 5px;
      }
      .customer-contact {
        color: #666;
        margin-bottom: 5px;
      }
      .customer-tags .el-tag {
        margin-right: 5px;
      }
    }
  }

  .customer-attributes {
    margin-top: 15px;
    .attribute-item {
      margin-bottom: 10px;
      span:first-child {
        color: #999;
        margin-right: 5px;
      }
    }
  }
}

.card-intention-management {
  h3, h4 {
    margin-bottom: 15px;
  }

  .intention-current {
    display: flex;
    align-items: center;
    .intention-image-placeholder {
      width: 60px;
      height: 60px;
      background-color: #f0f2f5;
      border-radius: 4px;
      margin-right: 15px;
    }
    .intention-details div {
      margin-bottom: 5px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .intention-history {
    padding-left: 10px;
    .timeline-content {
      background-color: #f9f9f9;
      padding: 10px 15px;
      border-radius: 4px;
      .timeline-meta {
        font-size: 0.9em;
        color: #999;
        margin-top: 5px;
      }
    }
  }
}

.card-follow-up-management {
  h3, h4 {
    margin-bottom: 15px;
  }

  .follow-up-form .el-form-item {
    margin-bottom: 15px;
  }

  .follow-up-history {
    padding-left: 10px;
    .timeline-content {
      background-color: #f9f9f9;
      padding: 10px 15px;
      border-radius: 4px;
      .follow-up-header {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        .el-icon {
          margin-right: 8px;
          font-size: 1.1em;
          color: $primary-color;
        }
        span {
          font-weight: bold;
        }
      }
      .level-change {
        font-size: 0.95em;
        color: $warning-color;
        margin-bottom: 5px;
      }
      .timeline-meta {
        font-size: 0.9em;
        color: #999;
        margin-top: 5px;
      }
      .el-button {
        margin-top: 10px;
      }
    }
  }
}
</style>
