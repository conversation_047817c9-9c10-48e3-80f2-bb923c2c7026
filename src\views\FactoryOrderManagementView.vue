<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('factoryOrder.title') }}</h1>

    <!-- 统计概览区域 -->
    <el-card class="mb-20 statistics-card">
<!--      <template #header>-->
<!--        <div class="card-header">-->
<!--          <span>{{ t('factoryOrder.statisticsTitle') }}</span>-->
<!--          <el-button :icon="Refresh" @click="handleRefreshStatistics" :loading="refreshing">-->
<!--            {{ t('factoryOrder.refreshStatistics') }}-->
<!--          </el-button>-->
<!--        </div>-->
<!--      </template>-->

      <el-row :gutter="20">
        <!-- 本月订单数量 -->
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.monthlyOrderCount.toLocaleString() }}</div>
            <div class="stat-label">{{ t('factoryOrder.monthlyOrderCount') }}</div>
            <div class="stat-growth positive" v-if="statistics.monthlyGrowthRate > 0">
              {{ t('factoryOrder.monthlyGrowthRate') }} {{ statistics.monthlyGrowthRate }}%
            </div>
          </div>
        </el-col>

        <!-- 今日订单数量 -->
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.dailyOrderCount.toLocaleString() }}</div>
            <div class="stat-label">{{ t('factoryOrder.dailyOrderCount') }}</div>
            <div class="stat-growth positive" v-if="statistics.dailyGrowthRate > 0">
              {{ t('factoryOrder.dailyGrowthRate') }} {{ statistics.dailyGrowthRate }}%
            </div>
          </div>
        </el-col>

        <!-- 订单最多门店 -->
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-store">{{ statistics.topStoreName || '-' }}</div>
            <div class="stat-label">{{ t('factoryOrder.topDealer') }}</div>
            <div class="stat-detail">{{ t('factoryOrder.monthlyOrderCount') }} {{ statistics.topStoreOrderCount || 0 }}单</div>
          </div>
        </el-col>

        <!-- 最热销车型 -->
        <el-col :span="6" >
          <div class="stat-card">
            <div class="stat-store">{{ statistics.topModelName }}</div>
            <div class="stat-label">{{ t('factoryOrder.topVehicle') }}</div>
            <div class="stat-detail">{{ t('factoryOrder.monthlyOrderCount') }} {{ statistics.topModelCount }}单</div>
          </div>
        </el-col>

        <!-- 待交车订单 -->
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number urgent">{{ statistics.pendingDeliveryCount.toLocaleString() }}</div>
            <div class="stat-label">{{ t('factoryOrder.pendingDelivery') }}</div>
            <div class="stat-detail urgent">{{ t('factoryOrder.priorityProcessing') }}</div>
          </div>
        </el-col>
      </el-row>

<!--      <div class="last-update">-->
<!--        {{ t('factoryOrder.lastUpdate') }}: {{ formatDateTime(statistics.lastUpdateTime) }}-->
<!--      </div>-->
    </el-card>

    <!-- 筛选条件区域 -->
    <el-card class="mb-20 search-card">
      <template #header>
        <span>{{ t('factoryOrder.searchTitle') }}</span>
      </template>

      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('factoryOrder.dealerName')">
              <el-select v-model="searchParams.dealerName" :placeholder="t('factoryOrder.dealerNamePlaceholder')" clearable>
                <el-option label="吉隆坡中央店" value="吉隆坡中央店" />
                <el-option label="槟城旗舰店" value="槟城旗舰店" />
                <el-option label="新山商业中心店" value="新山商业中心店" />
                <el-option label="怡保市中心店" value="怡保市中心店" />
                <el-option label="马六甲历史城店" value="马六甲历史城店" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('factoryOrder.model')">
              <el-select v-model="searchParams.model" :placeholder="t('factoryOrder.modelPlaceholder')" clearable @change="handleModelChange">
                <el-option label="Model A" value="Model A" />
                <el-option label="Model B" value="Model B" />
                <el-option label="Model C" value="Model C" />
                <el-option label="Model D" value="Model D" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('factoryOrder.variant')">
              <el-select v-model="searchParams.variant" :placeholder="t('factoryOrder.variantPlaceholder')" clearable>
                <el-option v-for="variant in variantOptions" :key="variant" :label="variant" :value="variant" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('factoryOrder.orderStatus')">
              <el-select
                v-model="searchParams.orderStatus"
                :placeholder="t('factoryOrder.orderStatusPlaceholder')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in orderStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('factoryOrder.paymentStatus')">
              <el-select
                v-model="searchParams.paymentStatus"
                :placeholder="t('factoryOrder.paymentStatusPlaceholder')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in paymentStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item :label="t('factoryOrder.orderDate')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :start-placeholder="t('factoryOrder.orderDateStart')"
                :end-placeholder="t('factoryOrder.orderDateEnd')"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('factoryOrder.orderNumber')">
              <el-input v-model="searchParams.orderNumber" :placeholder="t('factoryOrder.orderNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="4" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ t('common.search') }}</el-button>
              <el-button @click="resetSearch">{{ t('common.reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <div class="operation-bar">
        <div></div>
        <el-button type="success" :icon="Download" @click="handleExport" :loading="exporting">
          {{ t('factoryOrder.exportExcel') }}
        </el-button>
      </div>
    </el-card>

    <!-- 订单列表区域 -->
    <el-card class="table-card">
      <template #header>
        <span>{{ t('factoryOrder.listTitle') }}</span>
      </template>

      <el-table
        :data="orderList"
        v-loading="loading"
        style="width: 100%"
        height="500"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column type="index" :label="t('factoryOrder.serialNumber')" width="60" fixed="left" />
        <el-table-column prop="orderNo" :label="t('factoryOrder.orderNumber')" min-width="150" fixed="left" />
        <el-table-column prop="storeName" :label="t('factoryOrder.dealerNameColumn')" min-width="150" />
        <el-table-column prop="createTime" :label="t('factoryOrder.creationTime')" min-width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="customerName" :label="t('factoryOrder.ordererName')" min-width="120" />
        <el-table-column prop="customerPhone" :label="t('factoryOrder.ordererPhone')" min-width="140" />
        <el-table-column prop="customerName" :label="t('factoryOrder.buyerName')" min-width="120" />
        <el-table-column prop="customerPhone" :label="t('factoryOrder.buyerPhone')" min-width="140" />
        <el-table-column prop="customerType" :label="t('factoryOrder.buyerCategory')" min-width="120" />
        <el-table-column prop="model" :label="t('factoryOrder.model')" min-width="100" />
        <el-table-column prop="variant" :label="t('factoryOrder.variant')" min-width="120" />
        <el-table-column prop="color" :label="t('common.color')" min-width="100" />
        <el-table-column prop="vin" :label="t('factoryOrder.vin')" min-width="150" />
        <el-table-column prop="paymentMethod" :label="t('factoryOrder.paymentMethod')" min-width="100" />
        <el-table-column prop="loanStatus" :label="t('factoryOrder.loanApprovalStatus')" min-width="120">
          <template #default="scope">
            <el-tag :type="getLoanStatusType(scope.row.loanStatus)">
              {{ scope.row.loanStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus" :label="t('factoryOrder.orderStatus')" min-width="100">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ formatOrderStatus(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="approvalStatus" :label="t('factoryOrder.orderApprovalStatus')" min-width="120">
          <template #default="scope">
            <el-tag :type="getApprovalStatusType(scope.row.approvalStatus)">
              {{ formatApprovalStatus(scope.row.approvalStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="t('factoryOrder.paymentStatus')" min-width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ formatPaymentStatus(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="insuranceStatus" :label="t('factoryOrder.insuranceStatus')" min-width="100">
          <template #default="scope">
            <el-tag :type="getInsuranceStatusType(scope.row.insuranceStatus)">
              {{ scope.row.insuranceStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="jpjRegistrationStatus" :label="t('factoryOrder.jpjRegistrationStatus')" min-width="140">
          <template #default="scope">
            <el-tag :type="getJpjStatusType(scope.row.jpjRegistrationStatus)">
              {{ scope.row.jpjRegistrationStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('factoryOrder.operations')" width="100" fixed="right">
          <template #default="scope">
            <el-button type="primary" :icon="View" link @click="showOrderDetail(scope.row)">
              {{ t('factoryOrder.details') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情弹窗 -->
    <FactoryOrderDetailDialog
      v-model:visible="detailDialogVisible"
      :order-number="selectedOrderNumber"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download, View } from '@element-plus/icons-vue'
import type {
  OrderStatistics,
  FactoryOrderListItem,
  FactoryOrderListParams
} from '@/types/module'
import {
  getOrderStatistics,
  getFactoryOrderList,
  exportFactoryOrderData,
  refreshStatistics
} from '@/api/modules/factoryOrder'
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { getVehicleModelList, getStoreList } from '@/api/modules/masterData';
import FactoryOrderDetailDialog from './components/FactoryOrderDetailDialog.vue'

const { t } = useI18n()

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS
]);

// 主数据
const vehicleModelOptions = ref([]);
const storeOptions = ref([]);
const masterDataLoading = ref(false);

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const exporting = ref(false)

// 统计数据
const statistics = ref<OrderStatistics>({
  monthlyOrderCount: 0,
  dailyOrderCount: 0,
  monthlyGrowthRate: 0,
  dailyGrowthRate: 0,
  topDealers: [],
  topVehicles: [],
  pendingDeliveryCount: 0,
  lastUpdateTime: ''
})

// 搜索参数
const searchParams = reactive<FactoryOrderListParams>({
  dealerName: '',
  model: '',
  variant: '',
  orderStatus: '',
  paymentStatus: '',
  orderNumber: ''
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 订单列表
const orderList = ref<FactoryOrderListItem[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 详情弹窗
const detailDialogVisible = ref(false)
const selectedOrderNumber = ref('')

// 获取字典选项的计算属性
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS));
const approvalStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_STATUS));
const paymentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PAYMENT_STATUS));

// 加载主数据
const loadMasterData = async () => {
  masterDataLoading.value = true;
  try {
    // 加载车型数据
    vehicleModelOptions.value = await getVehicleModelList();

    // 加载门店数据
    storeOptions.value = await getStoreList();
  } catch (error) {
    console.error('获取主数据失败:', error);
  } finally {
    masterDataLoading.value = false;
  }
};

// 车型配置联动选项 (保留原有逻辑)
const variantOptions = computed(() => {
  const variants: Record<string, string[]> = {
    'Model A': ['Variant 1', 'Variant 2', 'Variant 3'],
    'Model B': ['Variant 1', 'Variant 2'],
    'Model C': ['Variant 1'],
    'Model D': ['Variant 1', 'Variant 2']
  }
  return searchParams.model ? variants[searchParams.model] || [] : []
})

// 方法
const loadStatistics = async () => {
  try {

    const data = await getOrderStatistics()
    statistics.value = data.result
  } catch (error) {
    console.error('Failed to load statistics:', error)
  }
}

const loadOrderList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      pageNum: pagination.page,
      pageSize: pagination.pageSize
    }
    const response = await getFactoryOrderList(params)
    orderList.value = response.result.records
    pagination.total = response.result.total
  } catch (error) {
    console.error('Failed to load order list:', error)
    ElMessage.error(t('common.loadFailed'))
  } finally {
    loading.value = false
  }
}

const handleRefreshStatistics = async () => {
  refreshing.value = true
  try {
    statistics.value = await refreshStatistics()
    ElMessage.success(t('factoryOrder.refreshSuccess'))
  } catch (error) {
    console.error('Failed to refresh statistics:', error)
    ElMessage.error(t('factoryOrder.refreshFailed'))
  } finally {
    refreshing.value = false
  }
}

const handleModelChange = () => {
  searchParams.variant = ''
}

const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchParams.orderDateStart = value[0]
    searchParams.orderDateEnd = value[1]
  } else {
    searchParams.orderDateStart = ''
    searchParams.orderDateEnd = ''
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadOrderList()
}

const resetSearch = () => {
  Object.assign(searchParams, {
    dealerName: '',
    model: '',
    variant: '',
    orderStatus: '',
    paymentStatus: '',
    orderDateStart: '',
    orderDateEnd: '',
    orderNumber: ''
  })
  dateRange.value = null
  pagination.page = 1
  loadOrderList()
}

const handleExport = async () => {
  exporting.value = true
  try {
    const response = await exportFactoryOrderData({
      searchParams: searchParams,
      exportType: 'all_filtered'
    })
    // 创建下载链接
    const link = document.createElement('a')
    link.href = response.downloadUrl
    link.download = `factory-orders-${Date.now()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success(t('factoryOrder.exportSuccess'))
  } catch (error) {
    console.error('Failed to export:', error)
    ElMessage.error(t('factoryOrder.exportFailed'))
  } finally {
    exporting.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadOrderList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadOrderList()
}

const showOrderDetail = (row: FactoryOrderListItem) => {
  selectedOrderNumber.value = row.orderNo || row.orderNumber
  detailDialogVisible.value = true
}

// 格式化显示方法 - 使用字典接口
const formatOrderStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;
const formatApprovalStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.APPROVAL_STATUS, status) || status;
const formatPaymentStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;

// 状态标签颜色
const getLoanStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '已通过': 'success',
    '审批中': 'warning',
    '已拒绝': 'danger',
    '无需审批': 'info'
  }
  return typeMap[status] || 'info'
}

const getOrderStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '已确认': 'success',
    '生产中': 'warning',
    '已完成': 'success',
    '待确认': 'warning',
    '待交车': 'primary'
  }
  return typeMap[status] || 'info'
}

const getApprovalStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '已审批': 'success',
    '待审批': 'warning',
    '已拒绝': 'danger'
  }
  return typeMap[status] || 'info'
}

const getPaymentStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '已全款': 'success',
    '已付定金': 'primary',
    '已付尾款': 'success',
    '待付定金': 'warning'
  }
  return typeMap[status] || 'info'
}

const getInsuranceStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '已投保': 'success',
    '待投保': 'warning'
  }
  return typeMap[status] || 'info'
}

const getJpjStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '已注册': 'success',
    '待注册': 'warning'
  }
  return typeMap[status] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  loadStatistics()
  loadOrderList()
  loadMasterData() // 加载主数据
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 统计卡片样式
.stat-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;

  .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;

    &.urgent {
      color: #f56c6c;
    }
  }

  .stat-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 6px;
  }

  .stat-growth {
    font-size: 12px;

    &.positive {
      color: #67c23a;
    }
  }

  .stat-store {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
  }

  .stat-detail {
    font-size: 12px;
    color: #909399;

    &.urgent {
      color: #f56c6c;
    }
  }
}

// 最热销车型样式
.top-vehicles-section {
  margin-top: 30px;

  h4 {
    margin-bottom: 15px;
    color: #303133;
  }

  .vehicle-rank {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: #fafafa;

    .rank-number {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      background: #409eff;
      color: white;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
      margin-right: 10px;
    }

    .vehicle-info {
      flex: 1;

      .vehicle-name {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }

      .vehicle-sales {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.last-update {
  margin-top: 20px;
  text-align: right;
  font-size: 12px;
  color: #909399;
}

// 搜索表单样式
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

// 操作栏样式
.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 表格样式
:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

// 分页器样式
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
