<template>
  <div class="home-container">
    <!-- 欢迎横幅 -->
    <!-- <div class="welcome-banner">
      <div class="banner-content">
        <h1 class="system-title">{{ tc('systemTitle') }}</h1>
        <p class="system-subtitle">{{ tc('systemSubtitle') }}</p>
        <div class="user-welcome" v-if="authStore.userInfo">
          <span>{{ tc('welcome') }}{{ authStore.userInfo.realName || authStore.userInfo.username }}</span>
        </div>
      </div>
    </div> -->

    <!-- 功能模块展示 -->
    <!-- <div class="modules-section">
      <h2 class="section-title">{{ tc('systemModules') }}</h2>
      <div class="modules-grid">
        <div
          class="module-card"
          v-for="module in modules"
          :key="module.key"
          @click="handleModuleClick(module)"
          :class="{ 'clickable': module.route }"
        >
          <div class="module-icon">
            <el-icon :size="48">
              <component :is="module.icon" />
            </el-icon>
          </div>
          <h3 class="module-title">{{ module.title }}</h3>
          <p class="module-description">{{ module.description }}</p>
        </div>
      </div>
    </div> -->

    <!-- 系统信息 -->
    <!-- <div class="system-info">
      <div class="info-cards">
        <div class="info-card">
          <h3>{{ tc('systemStatus') }}</h3>
          <p class="status-text">{{ tc('systemRunning') }}</p>
        </div>
        <div class="info-card">
          <h3>{{ tc('lastUpdate') }}</h3>
          <p class="update-text">{{ formatDate(new Date()) }}</p>
        </div>
        <div class="info-card">
          <h3>{{ tc('onlineUsers') }}</h3>
          <p class="users-text">{{ tc('currentUser') }}</p>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useModuleI18n } from '@/composables/useModuleI18n'
import {
  ShoppingCart,
  User,
  Tools,
  Document,
  DataAnalysis,
  Odometer
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const { tc } = useModuleI18n('common')

// 系统功能模块
const modules = computed(() => {
  const allModules = [
    {
      key: 'sales',
      title: tc('salesManagement'),
      description: tc('salesDescription'),
      icon: ShoppingCart,
      route: '/sales-order'
    },
    {
      key: 'customer',
      title: tc('customerManagement'),
      description: tc('customerDescription'),
      icon: User,
      route: '/customer-detail'
    },
    {
      key: 'vehicle',
      title: tc('vehicleManagement'),
      description: tc('vehicleDescription'),
      icon: Odometer,
      route: '/vehicle-query'
    },
    {
      key: 'afterSales',
      title: tc('afterSalesManagement'),
      description: tc('afterSalesDescription'),
      icon: Tools,
      route: '/work-order'
    },
    {
      key: 'parts',
      title: tc('partsManagement'),
      description: tc('partsDescription'),
      icon: Document,
      route: '/part-management'
    },
    {
      key: 'reports',
      title: tc('reportManagement'),
      description: tc('reportDescription'),
      icon: DataAnalysis,
      route: '/inventory-report'
    }
  ]

  // 返回所有模块（暂时不做权限过滤，因为这些都是基础功能）
  return allModules
})

// 处理模块点击事件
const handleModuleClick = (module: any) => {
  if (module.route) {
    router.push(module.route)
  }
}

// 格式化日期
function formatDate(date: Date): string {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.home-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.banner-content {
  text-align: center;
}

.system-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.system-subtitle {
  font-size: 1.2rem;
  margin-bottom: 20px;
  opacity: 0.9;
}

.user-welcome {
  font-size: 1.1rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
  border-radius: 25px;
  display: inline-block;
}

.modules-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.module-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #f0f0f0;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.module-card.clickable {
  cursor: pointer;
}

.module-card.clickable:hover {
  background: #f8f9ff;
  border-color: #667eea;
}

.module-card.clickable:active {
  transform: translateY(-2px);
}

.module-icon {
  margin-bottom: 15px;
  color: #667eea;
}

.module-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.module-description {
  color: #666;
  line-height: 1.5;
  font-size: 0.95rem;
}

.system-info {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.info-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #495057;
}

.status-text {
  color: #28a745;
  font-weight: 600;
  font-size: 1.1rem;
}

.update-text {
  color: #6c757d;
  font-size: 0.95rem;
}

.users-text {
  color: #007bff;
  font-weight: 600;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .home-container {
    padding: 15px;
  }

  .welcome-banner {
    padding: 25px;
  }

  .system-title {
    font-size: 2rem;
  }

  .modules-grid {
    grid-template-columns: 1fr;
  }

  .info-cards {
    grid-template-columns: 1fr;
  }
}
</style>
