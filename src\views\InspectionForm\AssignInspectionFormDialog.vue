<template>
  <el-dialog
    :title="t('assignInspectionForm.title')"
    v-model="dialogVisible"
    width="500px"
    :before-close="handleClose"
  >
    <el-form :model="formData" label-position="top" class="dialog-form-modern">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="t('assignInspectionForm.inspectionNo')">
            <el-input v-model="formData.inspectionNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('assignInspectionForm.licensePlateNo')">
            <el-input v-model="formData.licensePlateNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('assignInspectionForm.repairmanName')">
            <el-input v-model="formData.repairmanName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('assignInspectionForm.registerType')">
            <el-input v-model="formData.registerType" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('assignInspectionForm.serviceType')">
            <el-input v-model="formData.serviceType" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('assignInspectionForm.technician')">
            <el-select
              v-model="formData.technicianId"
              :placeholder="t('assignInspectionForm.selectTechnicianPlaceholder')"
              clearable
            >
              <el-option
                v-for="item in technicians"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit">{{ tc('confirm') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { ElMessage } from 'element-plus'

const { t,tc } = useModuleI18n('inspectionForm')

const dialogVisible = ref(false)
const formData = reactive<any>({
  inspectionNo: '',
  licensePlateNo: '',
  repairmanName: '',
  registerType: '',
  serviceType: '',
  technicianId: null
})

// 模拟技师数据
const technicians = ref([
  { id: 1, name: t('technicians.technicianA') },
  { id: 2, name: t('technicians.technicianB') },
  { id: 3, name: t('technicians.technicianC') }
])

const open = (data: any) => {
  dialogVisible.value = true
  Object.assign(formData, data)
}

const handleClose = () => {
  dialogVisible.value = false
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'technicianId') {
      formData[key] = null
    } else {
      formData[key] = ''
    }
  })
}

const handleSubmit = () => {
  if (!formData.technicianId) {
    ElMessage.warning(t('assignInspectionForm.selectTechnicianPlaceholder'))
    return
  }
  ElMessage.success(tc('operationSuccessful'))
  console.log(t('messages.assignInspectionFormData'), formData)
  handleClose()
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.dialog-form-modern {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  .el-button {
    margin-left: 10px;
  }
}
</style>
