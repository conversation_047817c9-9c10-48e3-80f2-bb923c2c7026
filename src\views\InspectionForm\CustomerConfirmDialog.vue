<template>
  <el-dialog
    :title="t('customerConfirm.title')"
    v-model="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="formData" label-position="top" class="dialog-form-modern">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="t('customerConfirm.inspectionNo')">
            <el-input v-model="formData.inspectionNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('customerConfirm.licensePlateNo')">
            <el-input v-model="formData.licensePlateNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('customerConfirm.repairmanName')">
            <el-input v-model="formData.repairmanName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('customerConfirm.customerConfirmTime')">
            <el-date-picker
              v-model="formData.customerConfirmTime"
              type="date"
              :placeholder="t('customerConfirm.selectDatePlaceholder')"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('customerConfirm.uploadedFiles')">
            <el-upload
              class="upload-demo"
              :http-request="simulateUpload"
              :on-remove="handleUploadRemove"
              :file-list="fileList"
              list-type="picture"
              accept=".jpg,.jpeg,.png"
            >
              <el-button type="primary">{{ t('customerConfirm.clickToUploadImage') }}</el-button>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit">{{ tc('confirm') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { ElMessage } from 'element-plus'
// import SignaturePad from 'signature_pad' // Removed

const { t,tc } = useModuleI18n('inspectionForm')

const dialogVisible = ref(false)
const formData = reactive<any>({
  inspectionNo: '',
  licensePlateNo: '',
  repairmanName: '',
  customerConfirmTime: null, // Added for confirmation time
  uploadedFiles: [] as string[], // Added for uploaded files URLs
})

const fileList = ref<any[]>([]) // Used for el-upload component

// Removed SignaturePad related refs and logic
// const signatureCanvas = ref<HTMLCanvasElement | null>(null)
// let signaturePad: SignaturePad | null = null
// const signatureImage = ref<string | null>(null)
// const isSigned = ref(false)

// onMounted(() => {
//   nextTick(() => {
//     if (signatureCanvas.value) {
//       signaturePad = new SignaturePad(signatureCanvas.value)
//       signaturePad.addEventListener('beginStroke', () => { isSigned.value = true })
//     }
//   })
// })

const open = (data: any) => {
  dialogVisible.value = true
  Object.assign(formData, data)
  // Reset file list and time when opening
  fileList.value = []
  formData.customerConfirmTime = null

  // Removed SignaturePad related clear logic
  // nextTick(() => {
  //   if (signaturePad) {
  //     signaturePad.clear()
  //     signatureImage.value = null
  //     isSigned.value = false
  //   }
  // })
}

const handleClose = () => {
  dialogVisible.value = false
  // Reset form data and file list
  Object.keys(formData).forEach(key => {
    if (Array.isArray(formData[key])) {
      formData[key] = []
    } else if (typeof formData[key] === 'object' && formData[key] !== null) {
      formData[key] = null // For date object
    } else {
      formData[key] = ''
    }
  })
  fileList.value = []
  // Removed SignaturePad related clear logic
  // signatureImage.value = null
  // isSigned.value = false
  // if (signaturePad) {
  //   signaturePad.clear()
  // }
}

// Removed clearSignature and saveSignature functions
// const clearSignature = () => {
//   if (signaturePad) {
//     signaturePad.clear()
//     signatureImage.value = null
//     isSigned.value = false
//   }
// }

// const saveSignature = () => {
//   if (signaturePad && !signaturePad.isEmpty()) {
//     signatureImage.value = signaturePad.toDataURL()
//     ElMessage.success(t('common.uploadSuccessful'))
//   } else {
//     ElMessage.warning(t('common.pleaseSign'))
//   }
// }

const simulateUpload = (options: any) => {
  // Simulate success after a short delay
  setTimeout(() => {
    const file = options.file;
    const imageUrl = URL.createObjectURL(file); // Create a temporary URL for display
    fileList.value.push({ name: file.name, url: imageUrl, uid: file.uid });
    formData.uploadedFiles = fileList.value.map(f => f.url);
    ElMessage.success(tc('uploadSuccessful'));
    options.onSuccess(true, file); // Call Element Plus's success callback
  }, 500);
};

// Removed handleUploadSuccess as it's replaced by simulateUpload
// const handleUploadSuccess = (response: any, file: any) => {
//   // Simulate successful upload for front-end display
//   const imageUrl = URL.createObjectURL(file.raw); // Create a temporary URL for display
//   fileList.value.push({ name: file.name, url: imageUrl, uid: file.uid }); // uid is important for El-upload to correctly manage the list
//   formData.uploadedFiles = fileList.value.map(f => f.url);
//   ElMessage.success(tc('uploadSuccessful'));
// };

const handleUploadRemove = (file: any, uploadFiles: any[]) => {
  fileList.value = uploadFiles;
  formData.uploadedFiles = fileList.value.map(f => f.url);
};

const handleSubmit = () => {
  // if (!signatureImage.value) { // Removed signature check
  //   ElMessage.warning(tc('pleaseUploadSignature'))
  //   return
  // }
  if (!formData.customerConfirmTime) {
    ElMessage.warning(t('customerConfirm.pleaseSelectConfirmTime'));
    return;
  }
  if (formData.uploadedFiles.length === 0) {
    ElMessage.warning(t('customerConfirm.pleaseUploadFile'));
    return;
  }

  ElMessage.success(tc('operationSuccessful'))
  console.log(t('messages.customerConfirmData'), formData)
  handleClose()
}

defineExpose({
  open
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.dialog-form-modern {
  .el-form-item {
    margin-bottom: 20px;
  }
}

// Removed signature-pad-container and uploaded-signature-image styles
// .signature-pad-container {
//   border: 1px solid var(--el-border-color);
//   border-radius: var(--el-border-radius-base);
//   width: 100%;
//   height: 200px;
//   position: relative;
//   overflow: hidden;
//   .signature-pad-canvas {
//     width: 100%;
//     height: 100%;
//     background-color: var(--el-fill-color-light);
//     border-radius: var(--el-border-radius-base);
//   }
//   .signature-pad-controls {
//     position: absolute;
//     bottom: 10px;
//     right: 10px;
//     z-index: 10;
//   }
//   .signature-placeholder {
//     position: absolute;
//     top: 50%;
//     left: 50%;
//     transform: translate(-50%, -50%);
//     color: var(--el-text-color-placeholder);
//     font-size: 14px;
//   }
// }

// .uploaded-signature-image {
//   max-width: 100%;
//   max-height: 150px;
//   margin-top: 10px;
//   border: 1px dashed var(--el-border-color);
//   border-radius: var(--el-border-radius-base);
// }

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  .el-button {
    margin-left: 10px;
  }
}
</style>
