<template>
  <div class="inventory-report-container">
    <div class="page-header">
      <h1 class="page-title">{{ $t('inventoryReport.title') }}</h1>
    </div>

    <el-card class="box-card mb-4">
      <el-form :model="form" class="filter-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('inventoryReport.partName')">
              <el-select
                v-model="form.partName"
                :placeholder="$t('inventoryReport.partNamePlaceholder')"
                style="width: 220px;"
                filterable
                clearable
              >
                <el-option
                  v-for="item in partNameOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('inventoryReport.partNumber')">
              <el-select
                v-model="form.partNumber"
                :placeholder="$t('inventoryReport.partNumberPlaceholder')"
                style="width: 220px;"
                filterable
                clearable
              >
                <el-option
                  v-for="item in partNumberOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('inventoryReport.supplierName')">
              <el-select
                v-model="form.supplierName"
                :placeholder="$t('inventoryReport.supplierNamePlaceholder')"
                style="width: 220px;"
                filterable
                clearable
              >
                <el-option
                  v-for="item in supplierNameOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('inventoryReport.inventoryStatus')">
              <el-select
                v-model="form.inventoryStatus"
                :placeholder="$t('inventoryReport.inventoryStatusPlaceholder')"
                style="width: 220px;"
                clearable
              >
                <el-option :label="$t('inventoryReport.statusNormal')" value="normal" />
                <el-option :label="$t('inventoryReport.statusBelowSafety')" value="belowSafety" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="query-buttons-row">
          <el-form-item class="button-group">
            <el-button type="primary" @click="onSubmit" size="default">
              {{ $t('common.search') }}
            </el-button>
            <el-button @click="onReset" size="default">
              {{ $t('common.reset') }}
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="box-card">
      <el-table :data="tableData" border style="width: 100%" :scroll-x="true">
        <el-table-column :label="$t('inventoryReport.serialNumber')" type="index" min-width="50" align="left"></el-table-column>
        <el-table-column :label="$t('inventoryReport.partName')" prop="partName" min-width="120" align="left"></el-table-column>
        <el-table-column :label="$t('inventoryReport.partNumber')" prop="partNumber" min-width="120" align="left"></el-table-column>
        <el-table-column :label="$t('inventoryReport.supplierName')" prop="supplierName" min-width="120" align="left"></el-table-column>
        <el-table-column :label="$t('inventoryReport.totalInventory')" prop="totalInventory" min-width="100" align="right"></el-table-column>
        <el-table-column :label="$t('inventoryReport.availableInventory')" prop="availableInventory" min-width="100" align="right"></el-table-column>
        <el-table-column :label="$t('inventoryReport.lockedInventory')" prop="lockedInventory" min-width="100" align="right"></el-table-column>
        <el-table-column :label="$t('inventoryReport.defectiveProducts')" prop="defectiveProducts" min-width="100" align="right"></el-table-column>
        <el-table-column :label="$t('inventoryReport.pendingReceipt')" prop="pendingReceipt" min-width="100" align="right"></el-table-column>
        <el-table-column :label="$t('inventoryReport.safetyStock')" prop="safetyStock" min-width="100" align="right"></el-table-column>
        <el-table-column :label="$t('inventoryReport.inventoryStatus')" prop="inventoryStatus" min-width="100" align="center">
          <template #default="{ row }">
            <span v-if="row.inventoryStatus === 'normal'">{{ $t('inventoryReport.statusNormal') }}</span>
            <span v-else-if="row.inventoryStatus === 'belowSafety'" style="color: #f56c6c;">{{ $t('inventoryReport.statusBelowSafety') }}</span>
            <span v-else>{{ row.inventoryStatus }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { mockPartArchivesData } from '@/mock/data/partArchivesData';


export default defineComponent({
  name: 'InventoryReportView',
  setup() {
    const { t } = useI18n();

const form = reactive({
  partName: '',
  partNumber: '',
  supplierName: '',
  inventoryStatus: '',
});

    const partNameOptions = computed(() => {
      const uniquePartNames = [...new Set(mockPartArchivesData.map(item => item.partName))];
      return uniquePartNames.map(name => ({
        label: name,
        value: name
      }));
    });

    const partNumberOptions = computed(() => {
      const uniquePartNumbers = [...new Set(mockPartArchivesData.map(item => item.partNumber))];
      return uniquePartNumbers.map(number => ({
        label: number,
        value: number
      }));
    });

    const supplierNameOptions = computed(() => {
      const uniqueSupplierNames = [...new Set(mockPartArchivesData.map(item => item.supplierName))];
      return uniqueSupplierNames.map(name => ({
        label: name,
        value: name
      }));
    });

    const rawTableData = [
      // Mock data for demonstration
      {
        serialNumber: 1,
        partName: '刹车片',
        partNumber: 'BP001',
        supplierName: '供应商A',
        totalInventory: 100,
        availableInventory: 80,
        lockedInventory: 10,
        defectiveProducts: 5,
        pendingReceipt: 5,
        safetyStock: 20,
        inventoryStatus: 'normal',
      },
      {
        serialNumber: 2,
        partName: '机油滤清器',
        partNumber: 'OF002',
        supplierName: '供应商B',
        totalInventory: 50,
        availableInventory: 30,
        lockedInventory: 5,
        defectiveProducts: 2,
        pendingReceipt: 13,
        safetyStock: 10,
        inventoryStatus: 'belowSafety',
      },
       {
        serialNumber: 3,
        partName: '火花塞',
        partNumber: 'SP003',
        supplierName: '供应商C',
        totalInventory: 200,
        availableInventory: 150,
        lockedInventory: 20,
        defectiveProducts: 10,
        pendingReceipt: 20,
        safetyStock: 50,
        inventoryStatus: 'normal',
      },
       {
        serialNumber: 4,
        partName: '轮胎',
        partNumber: 'TYRE001',
        supplierName: '供应商D',
        totalInventory: 80,
        availableInventory: 60,
        lockedInventory: 5,
        defectiveProducts: 0,
        pendingReceipt: 15,
        safetyStock: 10,
        inventoryStatus: 'normal',
      },
       {
        serialNumber: 5,
        partName: '雨刮器',
        partNumber: 'WIPER005',
        supplierName: '供应商A',
        totalInventory: 120,
        availableInventory: 100,
        lockedInventory: 10,
        defectiveProducts: 2,
        pendingReceipt: 8,
        safetyStock: 25,
        inventoryStatus: 'belowSafety',
      },
    ];

    const tableData = ref(rawTableData); // Initialize with raw data

    const onSubmit = () => {
      console.log('Query form submitted:', form);
      fetchData();
    };

    const onReset = () => {
      form.partName = '';
      form.partNumber = '';
      form.supplierName = '';
      form.inventoryStatus = '';
      console.log('Form reset.');
      fetchData();
    };

    onMounted(() => {
      fetchData();
    });

    const fetchData = () => {
      console.log('Fetching data with form:', form);
      const filteredData = rawTableData.filter(item => {
        const matchesPartName = !form.partName || item.partName.includes(form.partName);
        const matchesPartNumber = !form.partNumber || item.partNumber.includes(form.partNumber);
        const matchesSupplierName = !form.supplierName || item.supplierName.includes(form.supplierName);
        const matchesInventoryStatus = !form.inventoryStatus || item.inventoryStatus === form.inventoryStatus;

        return matchesPartName && matchesPartNumber && matchesSupplierName && matchesInventoryStatus;
      });

      tableData.value = filteredData; // Update ref's value
    };



    return {
      form,
      tableData,
      partNameOptions,
      partNumberOptions,
      supplierNameOptions,
      onSubmit,
      onReset,
      t,
    };
  },
});
</script>

<style scoped>
.inventory-report-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.filter-form {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-row {
    margin-bottom: 20px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }
}

.query-buttons-row {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.button-group {
  text-align: center;
  margin-bottom: 0;
  padding-top: 8px;
}

.button-group .el-button {
  min-width: 80px;
  height: 32px;
  margin: 0 8px;
  font-size: 14px;
  border-radius: 4px;
}

/* 输入框样式调整 */
/* 下拉框样式调整 */
/* 按钮样式调整 */
/* 表格样式调整 */
</style>
