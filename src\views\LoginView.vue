<template>
  <div class="login-container">
    <!-- 语言切换器 -->
    <div class="language-selector-wrapper">
      <LanguageSelector />
    </div>

    <div class="login-box">
      <div class="login-header">
        <h2>{{ t('loginTitle') }}</h2>
        <p class="login-subtitle">DMS 经销商管理系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            :placeholder="t('username')"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            :placeholder="t('password')"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button
            :loading="authStore.loading"
            type="primary"
            class="login-button"
            @click="handleLogin"
          >
            {{ t('loginButton') }}
          </el-button>
        </el-form-item>
      </el-form>

<!--      <div class="login-footer">-->
<!--        <el-divider>测试账号</el-divider>-->
<!--        <div class="test-accounts">-->
<!--          <div class="account-item">-->
<!--            <span class="account-label">管理员：</span>-->
<!--            <el-tag type="primary" @click="fillAccount('admin', '123456')">admin / 123456</el-tag>-->
<!--          </div>-->
<!--          <div class="account-item">-->
<!--            <span class="account-label">普通用户：</span>-->
<!--            <el-tag type="success" @click="fillAccount('store_manager', '123456')">store_manager / 123456</el-tag>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { type FormInstance, type FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import LanguageSelector from '@/components/common/LanguageSelector.vue'
import { useModuleI18n } from '@/composables/useModuleI18n'

const router = useRouter()
const authStore = useAuthStore()
const { t } = useModuleI18n('base')

// 表单引用
const loginFormRef = ref<FormInstance>()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: t('usernameRequired'), trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    // 执行登录
    await authStore.login({
      username: loginForm.username,
      password: loginForm.password
    })

    // 登录成功，跳转到目标页面或首页
    const redirectPath = (router.currentRoute.value.query.redirect as string) || '/'
    console.log('redirectPath', redirectPath)
    router.push(redirectPath)
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 填充测试账号
const fillAccount = (username: string, password: string) => {
  loginForm.username = username
  loginForm.password = password
}
</script>

<style scoped lang="scss">
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }
}

.language-selector-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;

  :deep(.language-selector) {
    .el-dropdown-link {
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;

      &:hover {
        color: rgba(255, 255, 255, 1);
      }
    }
  }
}

.login-box {
  width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;

  h2 {
    font-size: 32px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
  }

  .login-subtitle {
    color: #7f8c8d;
    font-size: 14px;
    margin: 0;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      box-shadow: 0 0 0 1px #e4e7ed inset;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #409eff inset;
      }
    }
  }
}

.login-button {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.login-footer {
  margin-top: 30px;

  .el-divider {
    margin: 20px 0;

    :deep(.el-divider__text) {
      color: #909399;
      font-size: 12px;
    }
  }

  .test-accounts {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .account-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .account-label {
        font-size: 12px;
        color: #909399;
        min-width: 60px;
      }

      .el-tag {
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-box {
    width: 90%;
    padding: 30px 20px;
  }

  .login-header h2 {
    font-size: 28px;
  }
}
</style>
