<template>
  <div class="part-management-view">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- Query Section -->
    <el-card class="box-card mb-4">
      <el-form :model="queryForm" class="query-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('partManagement.documentType')">
              <el-select v-model="queryForm.documentType" :placeholder="t('partManagement.selectDocumentType')" style="width: 220px;" clearable>
                <el-option :label="t('partManagement.documentTypeRequisition')" value="requisition"></el-option>
                <el-option :label="t('partManagement.documentTypeScrap')" value="scrap"></el-option>
                <el-option :label="t('partManagement.documentTypePicking')" value="picking"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('requisitionNumber')">
              <el-input v-model="queryForm.requisitionNumber" :placeholder="t('requisitionNumberPlaceholder')" style="width: 220px;" :disabled="isFiltersDisabled"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partName')">
              <el-select
                v-model="queryForm.partName"
                :placeholder="t('partNamePlaceholder')"
                style="width: 220px;"
                filterable
                clearable
                :disabled="isFiltersDisabled"
              >
                <el-option
                  v-for="item in partOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partNumber')">
              <el-select
                v-model="queryForm.partNumber"
                :placeholder="t('partNumberPlaceholder')"
                style="width: 220px;"
                filterable
                clearable
                :disabled="isFiltersDisabled"
              >
                <el-option
                  v-for="item in partNumberOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('supplierName')">
              <el-select
                v-model="queryForm.supplierName"
                :placeholder="t('supplierNamePlaceholder')"
                style="width: 220px;"
                filterable
                clearable
                :disabled="isFiltersDisabled"
              >
                <el-option
                  v-for="item in supplierOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="单据日期区间">
              <el-date-picker
                v-model="queryForm.requisitionDateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                style="width: 220px;"
                :disabled="isFiltersDisabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="getStatusFilterLabel()">
              <el-select v-model="queryForm.requisitionStatus" :placeholder="getStatusFilterPlaceholder()" style="width: 220px;" :disabled="isFiltersDisabled">
                <template v-if="queryForm.documentType === 'scrap'">
                  <el-option :label="t('statusSubmitted')" value="submitted"></el-option>
                  <el-option :label="t('statusApproved')" value="approved"></el-option>
                  <el-option :label="t('statusRejected')" value="rejected"></el-option>
                </template>
                <template v-else-if="queryForm.documentType === 'picking'">
                  <el-option label="待拣货" value="pending"></el-option>
                  <el-option label="已拣货" value="picked"></el-option>
                  <el-option label="缺货" value="outOfStock"></el-option>
                  <el-option label="关闭" value="closed"></el-option>
                </template>
                <template v-else>
                  <el-option :label="t('statusSubmitted')" value="submitted"></el-option>
                  <el-option :label="t('statusApproved')" value="approved"></el-option>
                  <el-option :label="t('statusRejected')" value="rejected"></el-option>
                  <el-option :label="t('statusShipped')" value="shipped"></el-option>
                  <el-option :label="t('statusPartialShipped')" value="partialShipped"></el-option>
                  <el-option :label="t('statusPartialReceived')" value="partialReceived"></el-option>
                  <el-option :label="t('statusReceived')" value="received"></el-option>
                  <el-option :label="t('statusVoided')" value="voided"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row :gutter="20" class="query-buttons-row">
          <el-form-item class="form-buttons">
            <el-button type="primary" @click="onSubmit" :disabled="isFiltersDisabled">{{ tc('query') }}</el-button>
            <el-button @click="onReset" :disabled="isFiltersDisabled">{{ tc('reset') }}</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <!-- Operation Buttons -->
    <div class="mb-4">
      <el-button type="primary" @click="handleCreateRequisition">{{ t('newRequisition') }}</el-button>
      <el-button :disabled="multipleSelection.length === 0" @click="handleExportReport">{{ t('exportReport') }}</el-button>
      <el-button @click="handlePartScrap">{{ t('partScrap') }}</el-button>
      <el-button @click="handleScrapRecord">{{ t('scrapRecord') }}</el-button>
      <el-button @click="handleViewMaterialOrder">{{ t('partManagement.viewMaterialOrder') }}</el-button>
      <el-button type="danger" @click="testModal">测试模态框</el-button>
    </div>

    <!-- List Section -->
    <el-table :data="tableData" style="width: 100%;" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
      <el-table-column prop="requisitionNumber" :label="getDocumentNumberLabel()" min-width="120" align="left"></el-table-column>
      <el-table-column prop="documentType" :label="t('partManagement.documentType')" min-width="120" align="left">
        <template #default="{ row }">
          {{ row.documentType }}
        </template>
      </el-table-column>
      <!-- 报损单据专用列 -->
      <el-table-column label="报损数量" min-width="100" align="center" v-if="queryForm.documentType === 'scrap'">
        <template #default="{ row }">
          {{ row.scrapQuantity }}
        </template>
      </el-table-column>

      <!-- 非报损单据的零件信息列 -->

      <el-table-column prop="requisitionDate" :label="getGenerateDateLabel()" min-width="120" align="left"></el-table-column>

      <!-- 报损来源列 -->
      <el-table-column prop="scrapSource" label="报损来源" min-width="100" align="center" v-if="queryForm.documentType === 'scrap'">
        <template #default="{ row }">
          {{ getScrapSourceLabel(row.scrapSource) }}
        </template>
      </el-table-column>
      <el-table-column prop="requisitionStatus" :label="getDocumentStatusLabel()" min-width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getUnifiedStatusTagType(row.requisitionStatus, row.documentType)">{{ getUnifiedStatusLabel(row.requisitionStatus, row.documentType) }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column :label="tc('operations')" min-width="280" align="left" header-align="left">
        <template #default="scope">
          <div class="operation-buttons">
            <!-- 叫料单操作 -->
            <template v-if="queryForm.documentType === '' || queryForm.documentType === 'requisition'">
              <el-button link type="primary" size="small" @click="handleDetail(scope.row)">{{ tc('detail') }}</el-button>
              <el-button
                link
                type="warning"
                size="small"
                :disabled="scope.row.requisitionStatus !== 'submitted' && scope.row.requisitionStatus !== 'rejected'"
                @click="handleEdit(scope.row)"
              >
                {{ tc('edit') }}
              </el-button>
              <el-button
                link
                type="danger"
                size="small"
                :disabled="scope.row.requisitionStatus !== 'submitted' || !multipleSelection.some(item => item === scope.row)"
                @click="handleCancel(scope.row)"
              >
                {{ tc('void') }}
              </el-button>
            </template>
            <!-- 拣货单（工单）操作 -->
            <template v-else-if="queryForm.documentType === 'picking'">
              <el-button
                link
                type="primary"
                size="small"
                @click="handleWorkOrderDetail(scope.row)"
              >
                {{ t('partManagement.workOrderDetail') }}
              </el-button>
              <el-button
                link
                type="success"
                size="small"
                :disabled="scope.row.requisitionStatus === 'outOfStock'"
                @click="handlePrintMaterialOrder(scope.row)"
              >
                {{ t('partManagement.printMaterialOrder') }}
              </el-button>
              <el-button
                v-if="scope.row.requisitionStatus === 'pending_pick'"
                link
                type="warning"
                size="small"
                @click="handleCompletePicking(scope.row)"
              >
                {{ t('partManagement.completePicking') }}
              </el-button>
              <el-button
                v-if="scope.row.requisitionStatus === 'picked'"
                link
                type="danger"
                size="small"
                @click="handleReturnPicking(scope.row)"
              >
                {{ t('partManagement.returnPicking') }}
              </el-button>
            </template>
            <!-- 报损单操作 -->
            <template v-else-if="queryForm.documentType === 'scrap'">
              <el-button link type="primary" size="small" @click="handleDetail(scope.row)">{{ tc('detail') }}</el-button>
              <el-button
                link
                type="warning"
                size="small"
                :disabled="scope.row.requisitionStatus !== 'submitted' && scope.row.requisitionStatus !== 'rejected'"
                @click="handleEditScrapFromMain(scope.row)"
              >
                {{ tc('edit') }}
              </el-button>
              <el-button
                v-if="scope.row.requisitionStatus !== 'voided'"
                link
                type="danger"
                size="small"
                :disabled="scope.row.requisitionStatus !== 'submitted' || !multipleSelection.some(item => item === scope.row)"
                @click="handleVoidScrapOrder(scope.row)"
              >
                {{ tc('void') }}
              </el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- Pagination -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :small="false"
      :disabled="false"
      :background="true"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <!-- New Requisition Drawer -->
    <el-drawer
      v-model="newRequisitionDrawerVisible"
      :title="t('newRequisition')"
      direction="rtl"
      size="50%"
      :modal="true"
    >
      <NewRequisitionForm @submit-success="handleNewRequisitionSubmitSuccess" @cancel="newRequisitionDrawerVisible = false" :initialData="editingRequisition" />
    </el-drawer>

    <!-- Part Scrap Modal -->
    <el-dialog
      v-model="partScrapDialogVisible"
      :title="t('partScrap')"
      width="50%"
      :modal="true"
    >
      <PartScrapForm
        @submit-success="handlePartScrapSubmitSuccess"
        @cancel="partScrapDialogVisible = false"
        :is-visible="partScrapDialogVisible"
        :edit-mode="scrapEditMode"
        :edit-data="scrapEditData"
      />
    </el-dialog>

    <!-- Scrap Record Modal -->
    <el-dialog
      v-model="scrapRecordDialogVisible"
      :title="t('scrapRecord')"
      width="70%"
      :modal="true"
    >
      <ScrapRecordList
        @close="scrapRecordDialogVisible = false"
        @edit-scrap-order="handleEditScrapOrder"
        :is-visible="scrapRecordDialogVisible"
      />
    </el-dialog>

    <!-- Detail Modal -->
    <el-dialog
      v-model="detailDialogVisible"
      title="叫料单详情"
      width="70%"
      :modal="true"
    >
      <el-descriptions :column="3" border>
        <el-descriptions-item :label="t('requisitionNumber')">{{ currentDetail.requisitionNumber }}</el-descriptions-item>
        <el-descriptions-item
          v-if="shouldShowPurchaseOrderNumber(currentDetail.requisitionStatus)"
          :label="t('purchaseOrderNumber')"
        >{{ currentDetail.purchaseOrderNumber }}</el-descriptions-item>
        <el-descriptions-item :label="t('requisitionDate')">{{ currentDetail.requisitionDate }}</el-descriptions-item>
        <el-descriptions-item :label="t('requisitionStatus')">{{ getStatusLabel(currentDetail.requisitionStatus) }}</el-descriptions-item>
        <el-descriptions-item
          v-if="currentDetail.requisitionStatus === 'rejected'"
          :label="t('rejectionReason')"
        >{{ currentDetail.rejectionReason }}</el-descriptions-item>
      </el-descriptions>

      <el-table :data="currentDetail.items" style="width: 100%; margin-top: 20px;" border>
        <el-table-column type="index" :label="tc('sequence')" width="80" align="center"></el-table-column>
        <el-table-column prop="partName" :label="t('partName')" min-width="120"></el-table-column>
        <el-table-column prop="partNumber" :label="t('partNumber')" min-width="120"></el-table-column>
        <el-table-column prop="quantity" label="叫料数量" min-width="100"></el-table-column>
        <el-table-column prop="unit" label="单位" min-width="80"></el-table-column>
        <el-table-column prop="requisitionDate" :label="t('requisitionDate')" min-width="120"></el-table-column>
        <el-table-column label="期望到货日期" min-width="120">
          <template #default="scope">
            {{ formatDate(scope.row.expectedArrivalTime) }}
          </template>
        </el-table-column>
        <el-table-column label="收发状态" min-width="100" align="center">
          <template #default="scope">
            <span v-if="getShipReceiveStatus(scope.row)">{{ getShipReceiveStatus(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="supplierName" :label="t('supplierName')" min-width="120"></el-table-column>
      </el-table>
    </el-dialog>

    <!-- 查看拣货单模态框 -->
    <el-dialog
      v-model="materialOrderDialogVisible"
      :title="t('partManagement.viewMaterialOrder')"
      width="80%"
      :modal="true"
    >
      <!-- 筛选区域 -->
      <div class="filter-section" style="margin-bottom: 20px;">
        <el-form :model="materialOrderFilters" inline>
          <el-form-item :label="t('partManagement.workOrderNumber')">
            <el-input
              v-model="materialOrderFilters.workOrderNumber"
              :placeholder="tc('inputPlaceholder') + t('partManagement.workOrderNumber')"
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item :label="t('partManagement.workOrderCreateDate')">
            <el-date-picker
              v-model="materialOrderFilters.createDateRange"
              type="daterange"
              :range-separator="tc('to')"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 240px;"
            />
          </el-form-item>
          <el-form-item :label="t('partManagement.workOrderStatus.label')">
            <el-select
              v-model="materialOrderFilters.workOrderStatus"
              :placeholder="tc('selectPlaceholder') + t('partManagement.workOrderStatus.label')"
              clearable
              style="width: 150px;"
            >
              <el-option label="待拣货" value="pending" />
              <el-option label="已拣货" value="picked" />
              <el-option label="缺货" value="outOfStock" />
              <el-option label="关闭" value="closed" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleMaterialOrderSearch">
              {{ tc('search') }}
            </el-button>
            <el-button @click="handleMaterialOrderReset">
              {{ tc('reset') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="paginatedMaterialOrderData" style="width: 100%;" border>
        <el-table-column type="index" :label="tc('sequence')" width="80" align="center"></el-table-column>
        <el-table-column prop="workOrderNumber" :label="t('partManagement.workOrderNumber')" min-width="150" align="left"></el-table-column>
        <el-table-column prop="createDate" :label="t('partManagement.workOrderCreateDate')" min-width="120" align="left"></el-table-column>
        <el-table-column prop="workOrderStatus" :label="t('partManagement.workOrderStatus.label')" min-width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getWorkOrderStatusTagType(row.workOrderStatus)">
              {{ getWorkOrderStatusLabel(row.workOrderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operations')" min-width="280" align="left">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleWorkOrderDetail(scope.row)"
            >
              {{ t('partManagement.workOrderDetail') }}
            </el-button>
            <el-button
              link
              type="success"
              size="small"
              :disabled="scope.row.workOrderStatus === 'outOfStock' || scope.row.workOrderStatus === 'closed'"
              @click="handlePrintMaterialOrder(scope.row)"
            >
              {{ t('partManagement.printMaterialOrder') }}
            </el-button>
            <el-button
              v-if="scope.row.workOrderStatus === 'pending'"
              link
              type="warning"
              size="small"
              @click="handleCompletePicking(scope.row)"
            >
              {{ t('partManagement.completePicking') }}
            </el-button>
            <el-button
              v-if="scope.row.workOrderStatus === 'picked'"
              link
              type="danger"
              size="small"
              @click="handleReturnPicking(scope.row)"
            >
              {{ t('partManagement.returnPicking') }}
            </el-button>
            <el-button
              v-if="scope.row.workOrderStatus === 'picked'"
              link
              type="primary"
              size="small"
              @click="handleCompleteWork(scope.row)"
            >
              完工
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="materialOrderPagination.currentPage"
        v-model:page-size="materialOrderPagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :disabled="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="materialOrderPagination.total"
        style="margin-top: 20px; text-align: right;"
      ></el-pagination>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="materialOrderDialogVisible = false">{{ $t('common.close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 工单详情模态框 -->
    <el-dialog
      v-model="workOrderDetailDialogVisible"
      :title="t('partManagement.workOrderDetailTitle')"
      width="70%"
      :modal="true"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item :label="$t('partManagement.workOrderNumber')">
          {{ currentWorkOrderDetail.workOrderNumber }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('partManagement.generateDate')">
          {{ currentWorkOrderDetail.generateDate }}
        </el-descriptions-item>
      </el-descriptions>

      <div style="margin-top: 20px;">
        <h4>{{ $t('partManagement.partsListTitle') }}</h4>
        <el-table :data="currentWorkOrderDetail.parts" style="width: 100%; margin-top: 10px;" border>
          <el-table-column type="index" :label="$t('common.sequence')" width="80" align="center"></el-table-column>
          <el-table-column prop="partName" :label="$t('partManagement.partName')" min-width="150" align="left"></el-table-column>
          <el-table-column prop="partCode" :label="$t('partManagement.partCode')" min-width="120" align="left"></el-table-column>
          <el-table-column prop="quantity" :label="$t('partManagement.quantity')" min-width="100" align="center"></el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="workOrderDetailDialogVisible = false">{{ $t('common.close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 退拣模态框 -->
    <el-dialog
      v-model="returnPickingDialogVisible"
      :title="$t('partManagement.returnPickingTitle')"
      width="70%"
      :modal="true"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item :label="$t('partManagement.workOrderNumber')">
          {{ currentReturnPickingDetail.workOrderNumber }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('partManagement.generateDate')">
          {{ currentReturnPickingDetail.generateDate }}
        </el-descriptions-item>
      </el-descriptions>

      <div style="margin-top: 20px;">
        <h4>{{ $t('partManagement.returnPickingPartsTitle') }}</h4>
        <el-table :data="currentReturnPickingDetail.parts" style="width: 100%; margin-top: 10px;" border>
          <el-table-column type="index" :label="$t('common.sequence')" width="80" align="center"></el-table-column>
          <el-table-column prop="partName" :label="$t('partManagement.partName')" min-width="150" align="left"></el-table-column>
          <el-table-column prop="partCode" :label="$t('partManagement.partCode')" min-width="120" align="left"></el-table-column>
          <el-table-column prop="quantity" :label="$t('partManagement.quantity')" min-width="100" align="center"></el-table-column>
          <el-table-column :label="$t('partManagement.returnQuantity')" min-width="120" align="center">
            <template #default="{ row }">
              <el-input-number
                v-model="row.returnQuantity"
                :min="0"
                :max="row.quantity"
                size="small"
                style="width: 100px;"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="returnPickingDialogVisible = false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmReturnPicking">{{ $t('common.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 简单测试模态框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="测试模态框"
      width="50%"
      :modal="true"
    >
      <p>这是一个简单的测试模态框</p>
      <p>partScrapDialogVisible 的值: {{ partScrapDialogVisible }}</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="partScrapDialogVisible = true; testDialogVisible = false">打开零件报损</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick, triggerRef } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ElMessage, ElMessageBox, ElTag } from 'element-plus';
import NewRequisitionForm from '@/components/part-management/NewRequisitionForm.vue';
import PartScrapForm from '@/components/part-management/PartScrapForm.vue';
import ScrapRecordList from '@/components/part-management/ScrapRecordList.vue';
import { fetchPartManagementData } from '@/mock/data/partManagement';
import { fetchScrapRecordsData } from '@/mock/data/scrapRecordsData';
import { mockSupplierData } from '@/mock/data/supplierData';
import { mockPartArchivesData } from '@/mock/data/partArchivesData';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { getPartsNameList, getSupplierList } from '@/api/modules/masterData';
import type {
  MaterialOrderItem,
  WorkOrderDetail,
  ReturnPickingDetail,
  PartManagementItem,
  UnifiedTableItem,
  UnifiedSearchParams,
  QueryForm,
  DetailData,
  ReturnPickingItem,
  WorkOrderDataStore
} from '@/types/parts/management';

// 工单数据获取函数
const fetchWorkOrderData = (params: any) => {
  const { page = 1, pageSize = 10, workOrderNumber, workOrderStatus, createDateRange } = params;

  // 模拟工单数据 - 只保留必要字段
  const mockWorkOrderData = [
    {
      workOrderNumber: '**********',
      createDate: '2024-01-15',
      workOrderStatus: 'pending',
    },
    {
      workOrderNumber: '**********',
      createDate: '2024-01-16',
      workOrderStatus: 'picked',
    },
    {
      workOrderNumber: 'WO20240003',
      createDate: '2024-01-17',
      workOrderStatus: 'outOfStock',
    },
    {
      workOrderNumber: 'WO20240004',
      createDate: '2024-01-18',
      workOrderStatus: 'pending',
    },
    {
      workOrderNumber: 'WO20240005',
      createDate: '2024-01-19',
      workOrderStatus: 'picked',
    },
    // 添加更多数据以支持分页测试
    ...Array.from({ length: 50 }, (_, i) => ({
      workOrderNumber: `WO2024${String(i + 6).padStart(4, '0')}`,
      createDate: `2024-01-${String((i % 28) + 1).padStart(2, '0')}`,
      workOrderStatus: ['pending', 'picked', 'outOfStock', 'closed'][i % 4],
    }))
  ];

  let filteredData = [...mockWorkOrderData];

  // 应用筛选条件
  if (workOrderNumber) {
    filteredData = filteredData.filter(item =>
      item.workOrderNumber.toLowerCase().includes(workOrderNumber.toLowerCase())
    );
  }

  if (workOrderStatus) {
    filteredData = filteredData.filter(item => item.workOrderStatus === workOrderStatus);
  }

  if (createDateRange && createDateRange.length === 2) {
    const [startDate, endDate] = createDateRange;
    filteredData = filteredData.filter(item => {
      const itemDate = new Date(item.createDate);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return itemDate >= start && itemDate <= end;
    });
  }

  // 分页
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const paginatedData = filteredData.slice(start, end);

  return {
    data: paginatedData,
    total: filteredData.length,
  };
};

// 工单数据类型定义已移至 @/types/parts/management

// 工单详情类型定义已移至 @/types/parts/management

// 退拣详情类型定义已移至 @/types/parts/management

const { t, tc } = useModuleI18n('parts');

// PartManagementItem 类型定义已移至 @/types/parts/management
// 暂时注释掉有问题的字典调用
// const { getOptions } = useDictionary();
const masterDataLoading = ref(false);
const partsNameOptions = ref<{ label: string; value: string }[]>([]);
const supplierOptions = ref<{ label: string; value: string }[]>([]);

// 计算属性：判断是否禁用其他筛选框
const isFiltersDisabled = computed(() => {
  return !queryForm.documentType;
});

// 获取状态筛选框标签
const getStatusFilterLabel = () => {
  switch (queryForm.documentType) {
    case 'scrap':
      return '报损单状态';
    case 'picking':
      return '拣货单状态';
    default:
      return t('requisitionStatus');
  }
};

// 获取状态筛选框占位符
const getStatusFilterPlaceholder = () => {
  switch (queryForm.documentType) {
    case 'scrap':
      return '请选择报损单状态';
    case 'picking':
      return '请选择拣货单状态';
    default:
      return t('selectRequisitionStatus');
  }
};

const queryForm = reactive({
  partName: '',
  partNumber: '',
  requisitionNumber: '',
  documentType: 'requisition', // 默认选择叫料单
  supplierName: '',
  requisitionDateRange: [],
  requisitionStatus: '',
  inventoryStatus: '',
});

// 获取字典选项的计算属性
// const requisitionStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.REQUISITION_STATUS));

// 保留原有的选项作为备用 (兼容性)
const partOptions = ref(Array.from(new Set(mockPartArchivesData.map(item => item.partName))).map(name => ({ label: name, value: name })));
const partNumberOptions = ref(Array.from(new Set(mockPartArchivesData.map(item => item.partNumber))).map(number => ({ label: number, value: number })));

// 加载主数据
const loadMasterData = async () => {
  masterDataLoading.value = true;
  try {
    // 加载零件名称数据
    const partsData = await getPartsNameList();
    partsNameOptions.value = partsData.map(item => ({
      label: item.name,
      value: item.code
    }));

    // 加载供应商数据
    const suppliersData = await getSupplierList();
    supplierOptions.value = suppliersData.map(item => ({
      label: item.name,
      value: item.code
    }));
  } catch (error) {
    console.error('获取主数据失败:', error);
    // 降级使用mock数据
    supplierOptions.value = mockSupplierData.map(item => ({ label: item.name, value: item.name }));
  } finally {
    masterDataLoading.value = false;
  }
};

watch(() => queryForm.partName, (newVal) => {
  if (newVal) {
    const selectedPart = mockPartArchivesData.find(item => item.partName === newVal);
    if (selectedPart && queryForm.partNumber !== selectedPart.partNumber) {
      queryForm.partNumber = selectedPart.partNumber;
    }
  } else {
    queryForm.partNumber = '';
  }
});

watch(() => queryForm.partNumber, (newVal) => {
  if (newVal) {
    const selectedPart = mockPartArchivesData.find(item => item.partNumber === newVal);
    if (selectedPart && queryForm.partName !== selectedPart.partName) {
      queryForm.partName = selectedPart.partName;
    }
  } else {
    queryForm.partName = '';
  }
});

// 统一的表格数据类型已移至 @/types/parts/management

const tableData = ref<UnifiedTableItem[]>([]);
const multipleSelection = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const newRequisitionDrawerVisible = ref(false);
const partScrapDialogVisible = ref(false);
const scrapRecordDialogVisible = ref(false);
const scrapEditMode = ref(false);
const scrapEditData = ref(null);
const detailDialogVisible = ref(false);
const materialOrderDialogVisible = ref(false);
const workOrderDetailDialogVisible = ref(false);
const returnPickingDialogVisible = ref(false);
const testDialogVisible = ref(false);
const currentDetail = ref({
  requisitionNumber: '',
  purchaseOrderNumber: '',
  requisitionDate: '',
  requisitionStatus: '',
  rejectionReason: '',
  items: []
});
const editingRequisition = ref<Record<string, any> | null>(null);

// 拣货单相关数据
const materialOrderData = ref<MaterialOrderItem[]>([]);
const materialOrderPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 拣货单筛选条件
const materialOrderFilters = reactive({
  workOrderNumber: '',
  createDateRange: [] as string[],
  workOrderStatus: ''
});

// 筛选后的拣货单数据
const filteredMaterialOrderData = ref<MaterialOrderItem[]>([]);

// 工单详情数据
const currentWorkOrderDetail = ref<WorkOrderDetail>({
  workOrderNumber: '',
  generateDate: '',
  parts: []
});

// 退拣详情数据
const currentReturnPickingDetail = ref<ReturnPickingDetail>({
  workOrderNumber: '',
  generateDate: '',
  parts: []
});

// 全局工单数据存储，用于保持数据同步
const workOrderDataStore = ref<WorkOrderDataStore>({});

// 分页显示的拣货单数据
const paginatedMaterialOrderData = computed(() => {
  const start = (materialOrderPagination.currentPage - 1) * materialOrderPagination.pageSize;
  const end = start + materialOrderPagination.pageSize;
  return filteredMaterialOrderData.value.slice(start, end);
});

onMounted(() => {
  fetchData();
  loadMasterData(); // 加载主数据
});

// 监听单据类型变化，自动刷新数据
watch(() => queryForm.documentType, () => {
  // 清空状态筛选，因为不同单据类型的状态选项不同
  queryForm.requisitionStatus = '';
  currentPage.value = 1; // 重置到第一页
  fetchData();
});

const fetchData = () => {
  let data: UnifiedTableItem[] = [];
  let totalCount = 0;

  // 根据单据类型获取不同的数据源
  if (queryForm.documentType === 'scrap') {
    // 获取报损记录数据 - 直接获取详细记录而不是分组数据
    const scrapParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      scrapOrderNumber: queryForm.requisitionNumber, // 使用叫料单号字段作为报损单号搜索
      partName: queryForm.partName,
      partNumber: queryForm.partNumber,
      scrapDateRange: queryForm.requisitionDateRange, // 使用叫料日期范围作为报损日期范围
      status: queryForm.requisitionStatus, // 使用叫料状态作为报损状态
      scrapSource: queryForm.inventoryStatus, // 使用库存状态字段作为报损来源筛选
      returnDetailRecords: true, // 返回详细记录而不是分组数据
    };
    const scrapResult = fetchScrapRecordsData(scrapParams);
    data = scrapResult.data.map((item) => ({
      id: item.id,
      requisitionNumber: item.scrapOrderNumber, // 显示报损单号
      purchaseOrderNumber: item.deliveryOrderNumber || '-', // 显示到货单号（如果有）
      requisitionDate: item.scrapDate, // 显示报损日期
      requisitionStatus: item.status, // 显示报损状态
      partName: item.partName, // 显示零件名称
      partNumber: item.partNumber, // 显示零件编号
      scrapSource: item.scrapSource, // 显示报损来源
      documentType: t('partManagement.documentTypeScrap'),
      scrapQuantity: item.quantity, // 保存报损数量
      scrapReason: item.scrapReason, // 保存报损原因
      scrapImages: item.scrapImages, // 保存报损图片
      rejectionReason: item.rejectionReason, // 保存驳回原因
      items: [item], // 保存详细信息用于查看详情
    }));
    totalCount = scrapResult.total;
  } else if (queryForm.documentType === 'picking') {
    // 获取拣货单（工单）数据
    const workOrderResult = fetchWorkOrderData({
      page: currentPage.value,
      pageSize: pageSize.value,
      workOrderNumber: queryForm.requisitionNumber, // 使用叫料单号字段作为工单号搜索
      workOrderStatus: queryForm.requisitionStatus, // 使用叫料状态作为工单状态
      createDateRange: queryForm.requisitionDateRange, // 使用叫料日期范围作为创建日期范围
    });
    data = workOrderResult.data.map((item) => ({
      id: item.workOrderNumber,
      requisitionNumber: item.workOrderNumber, // 显示工单号
      purchaseOrderNumber: '-', // 工单没有采购单号
      requisitionDate: item.createDate, // 显示创建日期
      requisitionStatus: item.workOrderStatus, // 显示工单状态
      partName: '', // 不显示额外字段
      partNumber: '', // 不显示额外字段
      supplierName: '', // 不显示额外字段
      documentType: 'picking',
      workOrderData: item, // 保存完整工单数据
    }));
    totalCount = workOrderResult.total;
  } else {
    // 默认获取叫料单数据
    const requisitionResult = fetchPartManagementData({
      page: currentPage.value,
      pageSize: pageSize.value,
      partName: queryForm.partName,
      partNumber: queryForm.partNumber,
      requisitionNumber: queryForm.requisitionNumber,
      supplierName: queryForm.supplierName,
      requisitionDateRange: queryForm.requisitionDateRange,
      requisitionStatus: queryForm.requisitionStatus, // 传递状态筛选条件，如果为空则显示所有状态
      inventoryStatus: queryForm.inventoryStatus,
      approvalType: 'requisition', // 明确指定获取叫料单数据
    });

    // 直接使用后端返回的数据，不需要前端重复筛选
    data = requisitionResult.data.map((item: any) => ({
      id: item.id,
      requisitionNumber: item.requisitionNumber,
      purchaseOrderNumber: item.purchaseOrderNumber || '',
      requisitionDate: item.requisitionDate,
      requisitionStatus: item.requisitionStatus,
      documentType: t('partManagement.documentTypeRequisition'),
      items: item.items,
    }));
    totalCount = requisitionResult.total;
  }

  tableData.value = data;
  total.value = totalCount;
};

const onSubmit = () => {
  currentPage.value = 1;
  fetchData();
};

const onReset = () => {
  // 保存当前的单据类型
  const currentDocumentType = queryForm.documentType;
  Object.assign(queryForm, {
    partName: '',
    partNumber: '',
    requisitionNumber: '',
    documentType: currentDocumentType, // 保持单据类型不变
    supplierName: '',
    requisitionDateRange: [],
    requisitionStatus: '',
    inventoryStatus: '',
  });
  currentPage.value = 1;
  fetchData();
};

const handleSelectionChange = (val: PartManagementItem[]) => {
  multipleSelection.value = val;
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  fetchData();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  fetchData();
};

// 动态获取列标题
const getDocumentNumberLabel = () => {
  switch (queryForm.documentType) {
    case 'scrap':
      return t('partManagement.scrapOrderNumber');
    case 'picking':
      return t('partManagement.workOrderNumber');
    default:
      return t('requisitionNumber');
  }
};

const getGenerateDateLabel = () => {
  switch (queryForm.documentType) {
    case 'scrap':
      return t('partManagement.scrapDate');
    case 'picking':
      return t('partManagement.createDate');
    default:
      return t('requisitionDate');
  }
};

const getDocumentStatusLabel = () => {
  switch (queryForm.documentType) {
    case 'scrap':
      return t('partManagement.scrapStatus');
    case 'picking':
      return t('partManagement.workOrderStatus.label');
    default:
      return t('requisitionStatus');
  }
};

const handleCreateRequisition = () => {
  editingRequisition.value = null;
  newRequisitionDrawerVisible.value = true;
};

const handleExportReport = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(tc('pleaseSelectData'));
    return;
  }

  console.log('导出选中的数据:', multipleSelection.value);
  ElMessage.success(tc('exportingReport'));
  // Logic for exporting report
};

const handlePartScrap = () => {
  console.log('handlePartScrap clicked, setting partScrapDialogVisible to true');
  partScrapDialogVisible.value = true;
  console.log('partScrapDialogVisible.value:', partScrapDialogVisible.value);
};

const testModal = () => {
  console.log('testModal clicked');
  testDialogVisible.value = true;
  console.log('testDialogVisible.value:', testDialogVisible.value);
};

const handleScrapRecord = () => {
  scrapRecordDialogVisible.value = true;
};

const handleViewMaterialOrder = () => {
  console.log('查看拣货单按钮被点击');
  console.log('materialOrderDialogVisible 当前值:', materialOrderDialogVisible.value);

  try {
    // 打开拣货单模态框
    materialOrderDialogVisible.value = true;
    console.log('materialOrderDialogVisible 设置为 true 后:', materialOrderDialogVisible.value);

    // 加载工单数据
    loadMaterialOrderData();
  } catch (error) {
    console.error('处理查看拣货单时出错:', error);
  }
};

// 加载拣货单数据
const loadMaterialOrderData = () => {
  console.log('开始加载拣货单数据...');
  // 模拟工单数据
  const mockData = [
    {
      workOrderNumber: '**********',
      createDate: '2024-01-15',
      workOrderStatus: 'pending_pick',
      customerName: '张三',
      vehicleModel: '奔驰C200',
      serviceType: '保养维修'
    },
    {
      workOrderNumber: '**********',
      createDate: '2024-01-16',
      workOrderStatus: 'picked',
      customerName: '李四',
      vehicleModel: '宝马X3',
      serviceType: '故障维修'
    },
    {
      workOrderNumber: 'WO20240003',
      createDate: '2024-01-17',
      workOrderStatus: 'picked',
      customerName: '王五',
      vehicleModel: '奥迪A4',
      serviceType: '定期保养'
    },
    {
      workOrderNumber: 'WO20240004',
      createDate: '2024-01-18',
      workOrderStatus: 'pending_pick',
      customerName: '赵六',
      vehicleModel: '丰田凯美瑞',
      serviceType: '事故维修'
    },
    {
      workOrderNumber: 'WO20240005',
      createDate: '2024-01-19',
      workOrderStatus: 'pending_pick',
      customerName: '钱七',
      vehicleModel: '本田雅阁',
      serviceType: '保养维修'
    },
    {
      workOrderNumber: 'WO20240006',
      createDate: '2024-01-20',
      workOrderStatus: 'outOfStock',
      customerName: '孙八',
      vehicleModel: '大众帕萨特',
      serviceType: '故障维修'
    },
    {
      workOrderNumber: 'WO20240007',
      createDate: '2024-01-21',
      workOrderStatus: 'outOfStock',
      customerName: '周九',
      vehicleModel: '现代索纳塔',
      serviceType: '事故维修'
    }
  ];

  materialOrderData.value = mockData;
  materialOrderPagination.total = mockData.length;
  console.log('拣货单数据已加载:', mockData);
  console.log('数据长度:', mockData.length);
  // 初始化时显示所有数据
  applyMaterialOrderFilters();
};

// 应用拣货单筛选
const applyMaterialOrderFilters = () => {
  let filtered = [...materialOrderData.value];

  // 工单号筛选
  if (materialOrderFilters.workOrderNumber) {
    filtered = filtered.filter(item =>
      item.workOrderNumber.toLowerCase().includes(materialOrderFilters.workOrderNumber.toLowerCase())
    );
  }

  // 工单状态筛选
  if (materialOrderFilters.workOrderStatus) {
    filtered = filtered.filter(item => item.workOrderStatus === materialOrderFilters.workOrderStatus);
  }

  // 创建日期范围筛选
  if (materialOrderFilters.createDateRange && materialOrderFilters.createDateRange.length === 2) {
    const [startDate, endDate] = materialOrderFilters.createDateRange;
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.createDate);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return itemDate >= start && itemDate <= end;
    });
  }

  filteredMaterialOrderData.value = filtered;
  materialOrderPagination.total = filtered.length;
  console.log('筛选后的拣货单数据:', filtered);
  console.log('分页数据:', paginatedMaterialOrderData.value);
};

// 拣货单搜索
const handleMaterialOrderSearch = () => {
  materialOrderPagination.currentPage = 1;
  applyMaterialOrderFilters();
};

// 拣货单重置
const handleMaterialOrderReset = () => {
  Object.assign(materialOrderFilters, {
    workOrderNumber: '',
    createDateRange: [],
    workOrderStatus: ''
  });
  materialOrderPagination.currentPage = 1;
  applyMaterialOrderFilters();
};

// 拣货单分页处理
watch(() => materialOrderPagination.currentPage, () => {
  // 分页变化时不需要重新筛选，只需要重新计算显示的数据
});

watch(() => materialOrderPagination.pageSize, () => {
  materialOrderPagination.currentPage = 1;
});

// 查看工单明细
const handleWorkOrderDetail = (row: any) => {
  console.log('查看工单明细:', row);

  // 获取工单号 - 可能存储在不同字段中
  const workOrderNumber = row.workOrderNumber || row.requisitionNumber;

  // 检查是否已经有该工单的数据
  let workOrderData = workOrderDataStore.value[workOrderNumber];
  if (!workOrderData) {
    // 如果没有数据，加载工单详情
    workOrderData = loadWorkOrderData(row);
  }

  // 设置当前工单详情
  currentWorkOrderDetail.value = { ...workOrderData };
  workOrderDetailDialogVisible.value = true;
};

// 打印拣货单
const handlePrintMaterialOrder = (row: any) => {
  // 检查工单状态，缺货状态不允许打印
  if (row.workOrderStatus === 'outOfStock') {
    ElMessage.warning(t('partManagement.outOfStockCannotPrint'));
    return;
  }

  console.log('打印拣货单:', row);
  ElMessage.success(`工单 ${row.workOrderNumber} 的拣货单已发送到打印机`);
  // TODO: 实现打印拣货单功能
};

// 完成拣货
const handleCompletePicking = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('partManagement.confirmCompletePicking', { workOrderNumber: row.workOrderNumber }),
      t('common.tip'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    );

    // 更新工单状态为已拣货
    row.workOrderStatus = 'picked';

    // 同时更新 materialOrderData 中的对应记录
    const index = materialOrderData.value.findIndex(item => item.workOrderNumber === row.workOrderNumber);
    if (index !== -1) {
      materialOrderData.value[index].workOrderStatus = 'picked';
    }

    ElMessage.success(t('partManagement.completePickingSuccess', { workOrderNumber: row.workOrderNumber }));
  } catch (error) {
    ElMessage.info(t('common.operationCanceled'));
  }
};

// 完工
const handleCompleteWork = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确认完工工单 ${row.workOrderNumber}？完工后将无法再进行退拣操作。`,
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 更新工单状态为关闭
    row.workOrderStatus = 'closed';

    // 同时更新 materialOrderData 中的对应记录
    const index = materialOrderData.value.findIndex(item => item.workOrderNumber === row.workOrderNumber);
    if (index !== -1) {
      materialOrderData.value[index].workOrderStatus = 'closed';
    }

    ElMessage.success(`工单 ${row.workOrderNumber} 已完工`);
  } catch (error) {
    ElMessage.info('操作已取消');
  }
};

// 加载工单数据的辅助函数
const loadWorkOrderData = (row: any) => {
  const workOrderNumber = row.workOrderNumber || row.requisitionNumber;

  // Randomly select parts from the part archives
  const shuffledParts = [...mockPartArchivesData].sort(() => 0.5 - Math.random());
  const selectedPartsCount = Math.floor(Math.random() * 4) + 2; // Select 2 to 5 parts
  const selectedParts = shuffledParts.slice(0, selectedPartsCount);

  const mockWorkOrderDetail = {
    workOrderNumber: workOrderNumber,
    generateDate: row.requisitionDate || row.createDate,
    parts: selectedParts.map(part => ({
      partName: part.partName,
      partCode: part.partNumber, // Use partNumber from archives
      quantity: Math.floor(Math.random() * 5) + 1 // Random quantity between 1 and 5
    }))
  };

  workOrderDataStore.value[workOrderNumber] = mockWorkOrderDetail;
  return mockWorkOrderDetail;
};

// 退拣
const handleReturnPicking = (row: any) => {
  console.log('退拣操作:', row);

  // 获取工单号 - 可能存储在不同字段中
  const workOrderNumber = row.workOrderNumber || row.requisitionNumber;

  // 确保工单数据已加载
  let workOrderData = workOrderDataStore.value[workOrderNumber];
  if (!workOrderData) {
    // 如果没有数据，先加载工单详情
    workOrderData = loadWorkOrderData(row);
  }

  // 获取工单数据并创建退拣详情
  const workOrderParts = workOrderData.parts.map(part => ({
    partName: part.partName,
    partCode: part.partCode,
    quantity: part.quantity,
    returnQuantity: 0
  }));

  const returnPickingDetail = {
    workOrderNumber: workOrderNumber,
    generateDate: row.requisitionDate,
    parts: workOrderParts
  };

  currentReturnPickingDetail.value = returnPickingDetail;
  returnPickingDialogVisible.value = true;
};

// 确认退拣
const confirmReturnPicking = async () => {
  try {
    // 检查是否有选择退拣数量
    const returnItems = currentReturnPickingDetail.value.parts.filter(part => part.returnQuantity > 0);

    if (returnItems.length === 0) {
      ElMessage.warning(t('partManagement.pleaseSelectReturnQuantity'));
      return;
    }

    // 构建退拣详情信息
    const returnDetails = returnItems.map(item =>
      `${item.partName}(${item.partCode}): ${item.returnQuantity}个`
    ).join('\n');

    const confirmMessage = t('partManagement.confirmReturnPickingDetails', {
      workOrderNumber: currentReturnPickingDetail.value.workOrderNumber,
      returnDetails: returnDetails
    });

    await ElMessageBox.confirm(
      confirmMessage,
      t('partManagement.returnPickingConfirmTitle'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
        dangerouslyUseHTMLString: false,
        customClass: 'return-picking-confirm-dialog'
      }
    );

    // 更新退拣详情中的物料数量
    console.log('退拣前的数量:', currentReturnPickingDetail.value.parts.map(p => ({ code: p.partCode, qty: p.quantity })));
    updateReturnPickingPartsQuantity(returnItems);
    console.log('退拣后的数量:', currentReturnPickingDetail.value.parts.map(p => ({ code: p.partCode, qty: p.quantity })));

    // 更新工单详情中的物料数量（如果当前正在查看该工单详情）
    const workOrderNumber = currentReturnPickingDetail.value.workOrderNumber;
    console.log('工单详情更新前:', currentWorkOrderDetail.value.parts.map(p => ({ code: p.partCode, qty: p.quantity })));
    updateWorkOrderPartsQuantity(workOrderNumber, returnItems);
    console.log('工单详情更新后:', currentWorkOrderDetail.value.parts.map(p => ({ code: p.partCode, qty: p.quantity })));

    // 检查是否还有剩余物料，决定工单状态
    const hasRemainingParts = currentReturnPickingDetail.value.parts.some(part => part.quantity > 0);
    const newStatus = hasRemainingParts ? 'picked' : 'pending';

    // 更新工单状态
    const materialOrderIndex = materialOrderData.value.findIndex(item => item.workOrderNumber === workOrderNumber);
    if (materialOrderIndex !== -1) {
      materialOrderData.value[materialOrderIndex].workOrderStatus = newStatus;
    }

    // 重新应用筛选以更新显示
    applyMaterialOrderFilters();

    ElMessage.success(t('partManagement.returnPickingSuccess', { workOrderNumber: workOrderNumber }));
    returnPickingDialogVisible.value = false;

    // 使用 nextTick 确保DOM更新
    await nextTick();
  } catch (error) {
    ElMessage.info(t('common.operationCanceled'));
  }
};

// 更新退拣详情中的物料数量
const updateReturnPickingPartsQuantity = (returnItems: any[]) => {
  // 创建新的parts数组以触发响应式更新
  const updatedParts = currentReturnPickingDetail.value.parts.map(part => {
    const returnItem = returnItems.find(item => item.partCode === part.partCode);
    if (returnItem) {
      return {
        ...part,
        quantity: Math.max(0, part.quantity - returnItem.returnQuantity),
        returnQuantity: 0
      };
    }
    return { ...part };
  });

  // 更新退拣详情
  currentReturnPickingDetail.value = {
    ...currentReturnPickingDetail.value,
    parts: updatedParts
  };
};

// 更新工单物料数量（模拟更新拣货单明细）
const updateWorkOrderPartsQuantity = (workOrderNumber: string, returnItems: any[]) => {
  // 这里模拟更新拣货单明细中的物料数量
  // 在实际应用中，这应该调用API来更新后端数据
  console.log(`更新工单 ${workOrderNumber} 的物料数量:`, returnItems);

  // 更新全局数据存储中的数据
  if (workOrderDataStore.value[workOrderNumber]) {
    returnItems.forEach(returnItem => {
      const partIndex = workOrderDataStore.value[workOrderNumber].parts.findIndex(
        part => part.partCode === returnItem.partCode
      );
      if (partIndex !== -1) {
        // 减少对应零件的数量
        workOrderDataStore.value[workOrderNumber].parts[partIndex].quantity -= returnItem.returnQuantity;
        // 确保数量不会小于0
        if (workOrderDataStore.value[workOrderNumber].parts[partIndex].quantity < 0) {
          workOrderDataStore.value[workOrderNumber].parts[partIndex].quantity = 0;
        }
      }
    });

    // 强制更新当前工单详情显示（如果是同一个工单）
    if (currentWorkOrderDetail.value.workOrderNumber === workOrderNumber) {
      // 创建新的对象以触发Vue的响应式更新
      currentWorkOrderDetail.value = {
        workOrderNumber: workOrderDataStore.value[workOrderNumber].workOrderNumber,
        generateDate: workOrderDataStore.value[workOrderNumber].generateDate,
        parts: [...workOrderDataStore.value[workOrderNumber].parts.map(part => ({ ...part }))]
      };

      // 强制触发响应式更新
      triggerRef(currentWorkOrderDetail);
    }
  }
};

// 获取工单状态标签文本
const getWorkOrderStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待拣货',
    'picked': '已拣货',
    'outOfStock': '缺货',
    'closed': '关闭'
  };
  return statusMap[status] || status;
};

// 获取工单状态标签类型
const getWorkOrderStatusTagType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    'pending': 'warning',
    'picked': 'success',
    'outOfStock': 'danger',
    'closed': 'info'
  };
  return typeMap[status] || 'info';
};

const handleDetail = (row: any) => {
  console.log('handleDetail clicked for row:', row);
  currentDetail.value = {
    requisitionNumber: row.requisitionNumber,
    purchaseOrderNumber: row.purchaseOrderNumber,
    requisitionDate: row.requisitionDate,
    requisitionStatus: row.requisitionStatus,
    rejectionReason: row.rejectionReason || '',
    items: row.items || []
  };
  detailDialogVisible.value = true;
  console.log('detailDialogVisible.value:', detailDialogVisible.value);
};

const handleEdit = (row: any) => {
  if (row.requisitionStatus === 'submitted' || row.requisitionStatus === 'rejected') {
    editingRequisition.value = { ...row };
    newRequisitionDrawerVisible.value = true;
  }
};

const handleCancel = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('partManagement.voidRequisitionConfirm'),
      t('common.tip'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    );
    // 模拟作废操作，实际应调用API
    console.log('作废操作确认', row);
    row.requisitionStatus = 'voided'; // 假设作废后的状态是 'voided'
    ElMessage.success(t('common.operationSuccessful'));
    fetchData(); // 刷新列表
  } catch (error) {
    ElMessage.info(t('common.operationCanceled'));
  }
};

const handleNewRequisitionSubmitSuccess = () => {
  newRequisitionDrawerVisible.value = false;
  fetchData();
};



const handlePartScrapSubmitSuccess = () => {
  partScrapDialogVisible.value = false;
  // 重置编辑模式
  scrapEditMode.value = false;
  scrapEditData.value = null;
  fetchData();
};

const handleEditScrapOrder = (scrapOrderData: any) => {
  // 关闭报损记录对话框
  scrapRecordDialogVisible.value = false;

  // 设置编辑模式和数据
  scrapEditMode.value = true;
  scrapEditData.value = scrapOrderData;

  // 打开零件报损表单
  partScrapDialogVisible.value = true;
};

// 从主页面编辑报损单（复用相同逻辑，但不关闭报损记录对话框）
const handleEditScrapFromMain = (row: any) => {
  // 设置编辑模式和数据
  scrapEditMode.value = true;
  scrapEditData.value = row;

  // 打开零件报损表单
  partScrapDialogVisible.value = true;
};

// 作废报损单
const handleVoidScrapOrder = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('partManagement.scrapRecordForm.confirmVoidOrder', { orderNumber: row.requisitionNumber }),
      t('common.tip'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    );

    // 模拟作废操作，实际应调用API
    console.log('作废报损单操作确认', row);
    row.requisitionStatus = 'voided'; // 更新状态为作废
    ElMessage.success(t('partManagement.scrapRecordForm.voidOrderSuccess'));
    fetchData(); // 刷新列表
  } catch (error) {
    ElMessage.info(t('common.operationCanceled'));
  }
};

const canBatchVoid = computed(() => {
  return multipleSelection.value.length > 0 &&
         multipleSelection.value.every(item => item.requisitionStatus === 'submitted');
});

const handleBatchVoid = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t('common.pleaseSelectOne'));
    return;
  }

  try {
    await ElMessageBox.confirm(
      t('partManagement.batchVoidRequisitionConfirm'),
      t('common.tip'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    );

    // 模拟批量作废操作，实际应调用API
    multipleSelection.value.forEach((item: any) => {
      if (item.requisitionStatus === 'submitted') {
        item.requisitionStatus = 'voided';
      }
    });
    ElMessage.success(t('common.operationSuccessful'));
    fetchData(); // 刷新列表
  } catch (error) {
    ElMessage.info(t('common.operationCanceled'));
  }
};

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'submitted':
      return 'primary'; // 普通标签
    case 'approved':
      return 'success'; // 成功标签
    case 'rejected':
      return 'danger'; // 危险标签
    case 'shipped':
      return 'warning'; // 警示标签
    case 'received':
      return 'success'; // 成功标签
    case 'voided': // 添加作废状态的标签类型
      return 'info';
    default:
      return 'primary';
  }
};

// 获取状态标签
const getStatusLabel = (status: string) => {
  // 直接使用翻译键，不依赖字典接口
  switch (status) {
    case 'submitted':
      return t('statusSubmitted');
    case 'approved':
      return t('statusApproved');
    case 'rejected':
      return t('statusRejected');
    case 'shipped':
      return t('statusShipped');
    case 'partialShipped':
      return t('statusPartialShipped');
    case 'partialReceived':
      return t('statusPartialReceived');
    case 'received':
      return t('statusReceived');
    case 'voided':
      return t('statusVoided');
    default:
      return status;
  }
};

// 统一状态标签函数，根据单据类型选择合适的状态标签
const getUnifiedStatusLabel = (status: string, documentType: string) => {
  if (documentType === 'picking') {
    // 拣货单使用工单状态标签
    return getWorkOrderStatusLabel(status);
  } else {
    // 其他单据使用默认状态标签
    return getStatusLabel(status);
  }
};

// 获取报损来源标签
const getScrapSourceLabel = (scrapSource: string) => {
  const sourceMap: Record<string, string> = {
    'receipt': '收货',
    'repair': '维修',
    'other': '其它'
  };
  return sourceMap[scrapSource] || scrapSource;
};

// 统一状态标签类型函数，根据单据类型选择合适的状态标签类型
const getUnifiedStatusTagType = (status: string, documentType: string) => {
  if (documentType === 'picking') {
    // 拣货单使用工单状态标签类型
    return getWorkOrderStatusTagType(status);
  } else {
    // 其他单据使用默认状态标签类型
    return getStatusTagType(status);
  }
};

// Helper function to check if purchase order number should be displayed
const shouldShowPurchaseOrderNumber = (status: string) => {
  return ['shipped', 'partialShipped', 'partialReceived', 'received', 'approved'].includes(status);
};

// Helper function to get ship/receive status
const getShipReceiveStatus = (item: any) => {
  // 检查是否已收货
  if (item.isFullyReceived || (item.receivedQuantity && item.receivedQuantity >= item.quantity)) {
    return '已收货';
  }
  // 检查是否已发货
  if (item.isFullyShipped || (item.shippedQuantity && item.shippedQuantity >= item.quantity)) {
    return '已发货';
  }
  // 未发货或部分发货/收货，返回空字符串
  return '';
};

// Helper function to format date
const formatDate = (dateString: string | Date) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};
</script>

<style scoped>
.part-management-view {
  padding: 20px;
}
.mb-4 {
  margin-bottom: 20px;
}
.page-title {
  font-size: 28px; /* 根据截图调整字体大小 */
  font-weight: bold; /* 使字体加粗 */
  margin-bottom: 20px; /* 标题下方留白 */
}
.query-form {
  .el-form-item {
    margin-bottom: 0; /* 移除el-form-item的默认margin，垂直间距由el-row控制 */
  }
  .el-row {
    margin-bottom: 20px; /* 控制行之间的垂直间距 */

    &:last-of-type {
      margin-bottom: 0; /* 最后一个el-row移除底部间距，以贴合卡片底部 */
    }
  }
}

.operation-buttons {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: flex-start !important;
  min-height: 32px;
  padding: 4px 0;
  width: 100%;
  text-align: left !important;
}

.operation-buttons .el-button {
  margin: 0 !important;
  min-width: 60px;
  white-space: nowrap;
}

/* 确保表格操作列左对齐 */
.el-table .el-table__cell {
  text-align: left !important;
}

.el-table .el-table__cell:last-child {
  text-align: left !important;
}

/* 确保表格标题也左对齐 */
.el-table .el-table__header .el-table__cell {
  text-align: left !important;
}

.el-table .el-table__header .el-table__cell:last-child {
  text-align: left !important;
}

/* 退拣确认对话框样式 */
:deep(.return-picking-confirm-dialog) {
  .el-message-box {
    width: 500px;
  }

  .el-message-box__message {
    white-space: pre-line;
    line-height: 1.6;
    font-size: 14px;
  }
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;

  .el-button {
    padding: 2px 6px;
    font-size: 12px;
    margin: 0;
  }
}

.query-buttons-row {
  display: flex;
  justify-content: center; /* 按钮在行内居中对齐 */
  margin-top: 20px; /* 为按钮行添加顶部间距，使其与上方表单项保持适当距离 */
}
</style>
