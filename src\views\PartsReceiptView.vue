<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('partsReceipt.partName')">
              <el-select
                v-model="searchParams.partName"
                :placeholder="t('partsReceipt.partNamePlaceholder')"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in partNameOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partsReceipt.partNumber')">
              <el-select
                v-model="searchParams.partNumber"
                :placeholder="t('partsReceipt.partNumberPlaceholder')"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in partNumberOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partsReceipt.supplierName')">
              <el-select
                v-model="searchParams.supplierName"
                :placeholder="t('partsReceipt.supplierNamePlaceholder')"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in supplierNameOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('requisitionNumber')">
              <el-input v-model="searchParams.requisitionNumber" :placeholder="t('requisitionNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('purchaseOrderNumber')">
              <el-input v-model="searchParams.purchaseOrderNumber" :placeholder="t('purchaseOrderNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('deliveryOrderNumber')">
              <el-input v-model="searchParams.deliveryOrderNumber" :placeholder="t('deliveryOrderNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="buttons-col">
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20" justify="end">
        <el-col :span="24" style="text-align: right;">
          <el-button :disabled="!selectedRows.length" @click="handleGenerateReceiptOrder">{{ t('partsReceipt.generateReceiptOrder') }}</el-button>
          <el-button @click="handleInvalidateReceiptOrder">{{ t('partsReceipt.invalidateReceiptOrder') }}</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据展示区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" :label="t('common.sequence')" width="80" />
        <el-table-column :label="t('partsReceipt.receiptOrderNumber')" prop="receiptOrderNumber" min-width="150" />
        <el-table-column :label="t('partsReceipt.requisitionNumber')" prop="requisitionNumber" min-width="150" />
        <el-table-column :label="t('partsReceipt.deliveryOrderNumber')" prop="deliveryOrderNumber" min-width="150" />
        <el-table-column :label="t('partsReceipt.purchaseOrderNumber')" prop="purchaseOrderNumber" min-width="150" />
        <el-table-column :label="t('partsReceipt.receiptStatus')" prop="receiptStatus" min-width="120">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.receiptStatus)"
            >
              {{ getStatusLabel(row.receiptStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('common.operations')" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">{{ t('common.detail') }}</el-button>
            <el-button
              v-if="row.receiptStatus === 'notReceived'"
              type="success"
              link
              @click="handleReceiptSingle(row)"
            >
              {{ t('partsReceipt.receipt') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 收货模态框 -->
    <el-dialog
      v-model="receiptDialogVisible"
      :title="t('partsReceipt.receipt')"
      width="750px"
    >
      <template #header="{ titleId, titleClass }">
        <div class="receipt-dialog-header">
          <h4 :id="titleId" :class="titleClass">{{ t('partsReceipt.receipt') }}</h4>
        </div>
      </template>
      <div v-for="receiptOrder in selectedRows" :key="receiptOrder.receiptOrderNumber" class="receipt-order-section">
        <div class="receipt-order-header">
          <span class="receipt-order-title">{{ t('partsReceipt.receiptOrderNumber') }}: {{ receiptOrder.receiptOrderNumber }}</span>
          <el-button type="primary" size="small" @click="handleDamageReport">破损申报</el-button>
        </div>

        <el-table :data="receiptOrder.items" border style="margin-bottom: 20px;">
          <el-table-column type="index" :label="t('common.sequence')" width="60" />
          <el-table-column :label="t('partsReceipt.partName')" prop="partName" width="140" />
          <el-table-column :label="t('partsReceipt.partNumber')" prop="partNumber" width="110" />
          <el-table-column :label="t('partsReceipt.quantity')" prop="quantity" width="80" />
          <el-table-column :label="t('partsReceipt.unit')" prop="unit" width="70" />
          <el-table-column :label="t('partsReceipt.receiptQuantity')" width="140">
            <template #default="{ row, $index }">
              <el-input-number
                v-model="row.receiptQuantity"
                :min="0"
                :max="row.quantity"
                size="small"
                @change="updateReceiptQuantity(receiptOrder.receiptOrderNumber, $index, row.receiptQuantity)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="t('partsReceipt.damagedQuantity')" width="140">
            <template #default="{ row }">
              <span>{{ row.damagedQuantity || 0 }}</span>
            </template>
          </el-table-column>

        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="receiptDialogVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmReceipt">{{ t('partsReceipt.receive') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 生成收货单模态框 -->
    <el-dialog
      v-model="generateReceiptOrderDialogVisible"
      :title="t('generateReceiptOrder')"
      width="80%"
    >
      <el-form :model="generateReceiptOrderForm" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('partsReceipt.partName')">
              <el-select
                v-model="generateReceiptOrderForm.partName"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in partNameOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partsReceipt.partNumber')">
              <el-select
                v-model="generateReceiptOrderForm.partNumber"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in partNumberOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partsReceipt.supplierName')">
              <el-select
                v-model="generateReceiptOrderForm.supplierName"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in supplierNameOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('receiptTime')">
              <el-date-picker v-model="generateReceiptOrderForm.receiptTime" type="daterange" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right;">
            <el-button type="primary" @click="searchReceiptOrders">{{ tc('search') }}</el-button>
            <el-button @click="resetReceiptOrderSearch">{{ tc('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-table :data="receiptOrderData" border @selection-change="handleReceiptOrderSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" :label="tc('sequence')" width="80" />
        <el-table-column :label="t('partName')" prop="partName" />
        <el-table-column :label="t('partNumber')" prop="partNumber" />
        <el-table-column :label="t('partQuantity')" prop="partQuantity" />
        <el-table-column :label="t('unit')" prop="unit" />
        <el-table-column :label="t('supplierName')" prop="supplierName" />
        <el-table-column :label="t('receiptTime')" prop="receiptTime" />
        <el-table-column :label="t('receiptOrderNumber')" prop="receiptOrderNumber" />
        <el-table-column :label="t('purchaseOrderNumber')" prop="purchaseOrderNumber" />
        <el-table-column :label="t('requisitionNumber')" prop="requisitionNumber" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateReceiptOrderDialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" :disabled="!selectedReceiptOrders.length" @click="printReceiptOrder">
            {{ t('printReceiptOrder') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 作废收货单模态框 -->
    <el-dialog
      v-model="invalidateReceiptOrderDialogVisible"
      :title="t('invalidateReceiptOrder')"
      width="80%"
    >
      <el-form :model="invalidateReceiptOrderForm" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('receiptOrderNumber')">
              <el-input v-model="invalidateReceiptOrderForm.receiptOrderNumber" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('receiptOrderStatus')">
              <el-select v-model="invalidateReceiptOrderForm.status" clearable>
                <el-option :label="t('statusActive')" value="active" />
                <el-option :label="t('statusInvalid')" value="invalid" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partsReceipt.partName')">
              <el-select
                v-model="invalidateReceiptOrderForm.partName"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in partNameOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partsReceipt.partNumber')">
              <el-select
                v-model="invalidateReceiptOrderForm.partNumber"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="option in partNumberOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right;">
            <el-button type="primary" @click="searchInvalidateOrders">{{ tc('search') }}</el-button>
            <el-button @click="resetInvalidateOrderSearch">{{ tc('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-table :data="invalidateOrderData" border @selection-change="handleInvalidateOrderSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" :label="tc('sequence')" width="80" />
        <el-table-column :label="t('receiptOrderNumber')" prop="receiptOrderNumber" />
        <el-table-column :label="t('generateDate')" prop="generateDate" />
        <el-table-column :label="t('receiptOrderStatus')" prop="status" />
        <el-table-column :label="tc('operations')" width="200">
          <template #default="{ row }">
            <el-button type="primary" link @click="viewOrderDetails(row)">{{ t('viewDetails') }}</el-button>
            <el-button type="danger" link :disabled="row.status === 'invalid'" @click="invalidateOrder(row)">
              {{ t('invalidate') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 详情模态框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="t('partsReceipt.receiptOrderDetailTitle')"
      width="80%"
    >
      <div v-if="currentDetailData">
        <el-descriptions :column="3" border>
          <el-descriptions-item :label="t('partsReceipt.receiptOrderNumber')">
            {{ currentDetailData.receiptOrderNumber }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('partsReceipt.requisitionNumber')">
            {{ currentDetailData.requisitionNumber }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('partsReceipt.deliveryOrderNumber')">
            {{ currentDetailData.deliveryOrderNumber }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('partsReceipt.purchaseOrderNumber')">
            {{ currentDetailData.purchaseOrderNumber }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('partsReceipt.receiptTime')" v-if="currentDetailData.isReceived">
            {{ currentDetailData.receiptTime }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider>{{ t('partsReceipt.receiptOrderDetailTitle') }}</el-divider>

        <el-table :data="currentDetailData.items" border style="width: 100%">
          <el-table-column type="index" :label="t('common.sequence')" width="80" />
          <el-table-column :label="t('partsReceipt.partName')" prop="partName" min-width="150" />
          <el-table-column :label="t('partsReceipt.partNumber')" prop="partNumber" min-width="150" />
          <el-table-column :label="t('partsReceipt.quantity')" prop="quantity" min-width="100" />
          <el-table-column :label="t('partsReceipt.unit')" prop="unit" min-width="80" />
          <el-table-column :label="t('partsReceipt.supplierName')" prop="supplierName" min-width="150" />
          <el-table-column :label="t('parts.requisitionDate')" prop="requisitionDate" min-width="120" />
          <el-table-column :label="t('parts.expectedArrivalTime')" prop="expectedArrivalTime" min-width="120" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">{{ t('common.close') }}</el-button>
          <el-button
            v-if="currentDetailData && currentDetailData.receiptStatus === 'billed'"
            type="primary"
            @click="handleViewReceiptOrder"
          >
            {{ t('partsReceipt.viewReceiptOrder') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 收货单查看模态框 -->
    <el-dialog
      v-model="receiptOrderViewDialogVisible"
      title="收货单详情"
      width="70%"
    >
      <div v-if="currentReceiptOrderData">
        <el-descriptions :column="2" border style="margin-bottom: 20px;">
          <el-descriptions-item label="收货单号">
            {{ currentReceiptOrderData.receiptOrderNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="收货时间">
            {{ currentReceiptOrderData.receiptTime }}
          </el-descriptions-item>
          <el-descriptions-item label="申购单号">
            {{ currentReceiptOrderData.requisitionNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="送货单号">
            {{ currentReceiptOrderData.deliveryOrderNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="采购单号">
            {{ currentReceiptOrderData.purchaseOrderNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商">
            {{ currentReceiptOrderData.supplierName }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider>收货明细</el-divider>

        <el-table :data="currentReceiptOrderData.items" border style="width: 100%">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column label="零件名称" prop="partName" min-width="150" />
          <el-table-column label="零件编号" prop="partNumber" min-width="120" />
          <el-table-column label="收货数量" min-width="100">
            <template #default="{ row }">
              {{ row.receiptQuantity || row.quantity }}
            </template>
          </el-table-column>
          <el-table-column label="单位" prop="unit" width="80" />
          <el-table-column label="破损数量" min-width="100">
            <template #default="{ row }">
              {{ row.damagedQuantity || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="破损说明" min-width="150">
            <template #default="{ row }">
              {{ row.damageDescription || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="供应商" prop="supplierName" min-width="120" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="receiptOrderViewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handlePrintReceiptOrder">打印收货单</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 零件报损模态框 -->
    <el-dialog
      v-model="partScrapDialogVisible"
      title="零件报损"
      width="50%"
      :modal="false"
    >
      <PartScrapForm
        @submit-success="handlePartScrapSubmitSuccess"
        @cancel="partScrapDialogVisible = false"
        :is-visible="partScrapDialogVisible"
        :edit-mode="true"
        :edit-data="scrapPreFillData || undefined"
        :receipt-order-number="currentReceiptOrderNumber"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { mockPartArchivesData } from '@/mock/data/partArchivesData';
import { mockSupplierData } from '@/mock/data/supplierData';
import PartScrapForm from '@/components/part-management/PartScrapForm.vue';

const { t } = useI18n();

// 定义接口
interface ReceiptDetailItem {
  partName: string;
  partNumber: string;
  quantity: number;
  unit: string;
  requisitionStatus: string;
  requisitionDate: string;
  expectedArrivalTime: string;
  supplierName: string;
  receiptQuantity?: number;
  damagedQuantity?: number;
  damageDescription?: string;
}

interface ReceiptItem {
  receiptOrderNumber: string;
  partName: string;
  partNumber: string;
  partQuantity: number;
  unit: string;
  supplierName: string;
  requisitionNumber: string;
  deliveryOrderNumber: string;
  purchaseOrderNumber: string;
  receiptQuantity?: number;
  damagedQuantity?: number;
  damageDescription?: string;
  receiptTime?: string; // 收货时间，完成收货后显示
  isReceived?: boolean; // 是否已收货
  receiptStatus?: string; // 收货状态：'notReceived' | 'received' | 'invoiced'
  items?: ReceiptDetailItem[]; // 叫料明细
}

// 从零件档案数据生成下拉选项
const partNameOptions = ref(Array.from(new Set(mockPartArchivesData.map(item => item.partName))).map(name => ({ label: name, value: name })));
const partNumberOptions = ref(Array.from(new Set(mockPartArchivesData.map(item => item.partNumber))).map(number => ({ label: number, value: number })));
const supplierNameOptions = ref(mockSupplierData.map(item => ({ label: item.name, value: item.name })));

const searchParams = reactive({
  partName: '',
  partNumber: '',
  supplierName: '',
  requisitionNumber: '',
  purchaseOrderNumber: '',
  deliveryOrderNumber: ''
});

const tableData = ref<ReceiptItem[]>([
  // RO20240001 - 收货单1（包含3种零件）
  {
    receiptOrderNumber: 'RO20240001',
    partName: '刹车片等3种零件',
    partNumber: 'BP001等',
    partQuantity: 170,
    unit: '件',
    supplierName: '供应商A',
    requisitionNumber: 'RQ20240001',
    deliveryOrderNumber: 'DO20240001',
    purchaseOrderNumber: 'PO20240001',
    isReceived: true,
    receiptTime: '2024-01-15 14:30:00',
    receiptStatus: 'received',
    items: [
      {
        partName: '刹车片',
        partNumber: 'BP001',
        quantity: 100,
        unit: '套',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-10',
        expectedArrivalTime: '2024-01-15',
        supplierName: '供应商A'
      },
      {
        partName: '机油滤清器',
        partNumber: 'OF002',
        quantity: 50,
        unit: '个',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-10',
        expectedArrivalTime: '2024-01-15',
        supplierName: '供应商A'
      },
      {
        partName: '火花塞',
        partNumber: 'SP003',
        quantity: 20,
        unit: '支',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-10',
        expectedArrivalTime: '2024-01-15',
        supplierName: '供应商A'
      }
    ]
  },

  // RO20240002 - 收货单2（包含2种零件）
  {
    receiptOrderNumber: 'RO20240002',
    partName: '空气滤芯等2种零件',
    partNumber: 'AF005等',
    partQuantity: 45,
    unit: '件',
    supplierName: '供应商B',
    requisitionNumber: 'RQ20240002',
    deliveryOrderNumber: 'DO20240002',
    purchaseOrderNumber: 'PO20240002',
    isReceived: false,
    receiptTime: undefined,
    receiptStatus: 'notReceived',
    items: [
      {
        partName: '空气滤芯',
        partNumber: 'AF005',
        quantity: 30,
        unit: '个',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-14',
        expectedArrivalTime: '2024-01-20',
        supplierName: '供应商B'
      },
      {
        partName: '雨刮器',
        partNumber: 'WW004',
        quantity: 15,
        unit: '对',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-16',
        expectedArrivalTime: '2024-01-22',
        supplierName: '供应商B'
      }
    ]
  },

  // RO20240003 - 收货单3（包含4种零件）
  {
    receiptOrderNumber: 'RO20240003',
    partName: '轮胎等4种零件',
    partNumber: 'TY001等',
    partQuantity: 18,
    unit: '件',
    supplierName: '供应商C',
    requisitionNumber: 'RQ20240003',
    deliveryOrderNumber: 'DO20240003',
    purchaseOrderNumber: 'PO20240003',
    isReceived: true,
    receiptTime: '2024-01-18 10:45:00',
    receiptStatus: 'received',
    items: [
      {
        partName: '轮胎',
        partNumber: 'TY001',
        quantity: 8,
        unit: '条',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-15',
        expectedArrivalTime: '2024-01-18',
        supplierName: '供应商C'
      },
      {
        partName: '刹车盘',
        partNumber: 'BD002',
        quantity: 4,
        unit: '套',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-15',
        expectedArrivalTime: '2024-01-18',
        supplierName: '供应商C'
      },
      {
        partName: '减震器',
        partNumber: 'SH003',
        quantity: 4,
        unit: '支',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-15',
        expectedArrivalTime: '2024-01-18',
        supplierName: '供应商C'
      },
      {
        partName: '车灯总成',
        partNumber: 'HL004',
        quantity: 2,
        unit: '套',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-15',
        expectedArrivalTime: '2024-01-18',
        supplierName: '供应商C'
      }
    ]
  },

  // RO20240004 - 收货单4（包含3种零件）- 已开单状态
  {
    receiptOrderNumber: 'RO20240004',
    partName: '发动机皮带等3种零件',
    partNumber: 'EB005等',
    partQuantity: 11,
    unit: '件',
    supplierName: '供应商D',
    requisitionNumber: 'RQ20240004',
    deliveryOrderNumber: 'DO20240004',
    purchaseOrderNumber: 'PO20240004',
    isReceived: true,
    receiptTime: '2024-01-22 09:15:00',
    receiptStatus: 'billed',
    items: [
      {
        partName: '发动机皮带',
        partNumber: 'EB005',
        quantity: 6,
        unit: '条',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-18',
        expectedArrivalTime: '2024-01-25',
        supplierName: '供应商D'
      },
      {
        partName: '水泵',
        partNumber: 'WP006',
        quantity: 2,
        unit: '个',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-18',
        expectedArrivalTime: '2024-01-25',
        supplierName: '供应商D'
      },
      {
        partName: '节温器',
        partNumber: 'TH007',
        quantity: 3,
        unit: '个',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-18',
        expectedArrivalTime: '2024-01-25',
        supplierName: '供应商D'
      }
    ]
  },

  // RO20240005 - 收货单5（包含2种零件）- 已开单状态
  {
    receiptOrderNumber: 'RO20240005',
    partName: '变速箱油等2种零件',
    partNumber: 'TO008等',
    partQuantity: 18,
    unit: '升',
    supplierName: '供应商E',
    requisitionNumber: 'RQ20240005',
    deliveryOrderNumber: 'DO20240005',
    purchaseOrderNumber: 'PO20240005',
    isReceived: true,
    receiptTime: '2024-01-25 16:20:00',
    receiptStatus: 'billed',
    items: [
      {
        partName: '变速箱油',
        partNumber: 'TO008',
        quantity: 10,
        unit: '升',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-20',
        expectedArrivalTime: '2024-01-28',
        supplierName: '供应商E'
      },
      {
        partName: '冷却液',
        partNumber: 'CL009',
        quantity: 8,
        unit: '升',
        requisitionStatus: 'shipped',
        requisitionDate: '2024-01-20',
        expectedArrivalTime: '2024-01-28',
        supplierName: '供应商E'
      }
    ]
  }
]);

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 5
});

const selectedRows = ref<ReceiptItem[]>([]);
const selectedReceiptOrders = ref<ReceiptItem[]>([]);
const selectedInvalidateOrders = ref<ReceiptItem[]>([]);

const receiptDialogVisible = ref(false);
const generateReceiptOrderDialogVisible = ref(false);
const invalidateReceiptOrderDialogVisible = ref(false);
const partScrapDialogVisible = ref(false);

const generateReceiptOrderForm = reactive({
  partName: '',
  partNumber: '',
  supplierName: '',
  receiptTime: [] as string[]
});

const invalidateReceiptOrderForm = reactive({
  receiptOrderNumber: '',
  status: '',
  partName: '',
  partNumber: ''
});

const receiptOrderData = ref<ReceiptItem[]>([]);
const invalidateOrderData = ref<ReceiptItem[]>([]);

// 报损相关状态
const scrapPreFillData = ref<Record<string, any> | null>(null);

const currentScrapContext = ref<{
  receiptOrderNumber: string;
  itemIndex: number;
  item: ReceiptDetailItem;
} | null>(null);

const currentReceiptOrderNumber = ref<string>('');

const handleSelectionChange = (selection: ReceiptItem[]) => {
  selectedRows.value = selection;
};

const handleReceiptOrderSelectionChange = (selection: ReceiptItem[]) => {
  selectedReceiptOrders.value = selection;
};

const handleInvalidateOrderSelectionChange = (selection: ReceiptItem[]) => {
  selectedInvalidateOrders.value = selection;
};

const handleSearch = () => {
  // TODO: 搜索逻辑
};

const resetSearch = () => {
  searchParams.partName = '';
  searchParams.partNumber = '';
  searchParams.supplierName = '';
  searchParams.requisitionNumber = '';
  searchParams.purchaseOrderNumber = '';
  searchParams.deliveryOrderNumber = '';
};

// 分页事件处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  // TODO: 重新加载数据
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  // TODO: 重新加载数据
};

const confirmReceipt = async () => {
  // 验证破损数量是否通过破损申报填写
  let hasInvalidItems = false;

  selectedRows.value.forEach(receiptOrder => {
    if (receiptOrder.items) {
      receiptOrder.items.forEach(item => {
        if ((item.damagedQuantity || 0) > 0 && item.damageDescription !== '已申报破损') {
          hasInvalidItems = true;
        }
      });
    }
  });

  if (hasInvalidItems) {
    ElMessage.warning('存在破损物料未通过破损申报填写，请先进行破损申报');
    return;
  }

  // 更新收货状态和收货时间
  const currentTime = new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');

  // 准备库存更新数据
  interface InventoryUpdateItem {
    partName: string;
    partNumber: string;
    supplierName: string;
    receiptQuantity: number;
    damagedQuantity: number;
    receiptTime: string;
    receiptOrderNumber: string;
  }
  const inventoryUpdateItems: InventoryUpdateItem[] = [];

  selectedRows.value.forEach(receiptOrder => {
    // 在tableData中找到对应的行并更新
    const tableRow = tableData.value.find(item => item.receiptOrderNumber === receiptOrder.receiptOrderNumber);
    if (tableRow) {
      tableRow.isReceived = true;
      tableRow.receiptTime = currentTime;
      tableRow.receiptStatus = 'received';

      // 更新物料明细的收货信息并准备库存更新数据
      if (tableRow.items && receiptOrder.items) {
        tableRow.items.forEach((tableItem, index) => {
          if (receiptOrder.items && receiptOrder.items[index]) {
            const receiptItem = receiptOrder.items[index];
            tableItem.receiptQuantity = receiptItem.receiptQuantity;
            tableItem.damagedQuantity = receiptItem.damagedQuantity;
            tableItem.damageDescription = receiptItem.damageDescription;

            // 添加到库存更新列表
            inventoryUpdateItems.push({
              partName: tableItem.partName,
              partNumber: tableItem.partNumber,
              supplierName: tableItem.supplierName,
              receiptQuantity: receiptItem.receiptQuantity || tableItem.quantity,
              damagedQuantity: receiptItem.damagedQuantity || 0,
              receiptTime: currentTime,
              receiptOrderNumber: receiptOrder.receiptOrderNumber
            });
          }
        });
      }
    }
  });

  try {
    // 模拟库存更新API调用（在实际环境中会调用真实API）
    console.log('更新库存数据:', inventoryUpdateItems);

    // 这里可以调用真实的库存更新API
    // await updateInventoryFromReceipt(inventoryUpdateItems);

    // 保存收货记录到localStorage，供收货查询页面使用
    saveReceiptRecords(selectedRows.value, currentTime);

    console.log('收货数据:', selectedRows.value);
    ElMessage.success(t('partsReceipt.receiptSuccess'));
    receiptDialogVisible.value = false;

    // 清空选择
    selectedRows.value = [];
  } catch (error) {
    console.error('库存更新失败:', error);
    ElMessage.error('收货成功，但库存更新失败，请联系管理员');
  }
};

const handleGenerateReceiptOrder = () => {
  // 将选中的收货单内容自动填充到模态框表格中
  const receiptOrderItems: ReceiptItem[] = [];

  selectedRows.value.forEach(receiptOrder => {
    if (receiptOrder.items) {
      receiptOrder.items.forEach(item => {
        receiptOrderItems.push({
          receiptOrderNumber: receiptOrder.receiptOrderNumber,
          partName: item.partName,
          partNumber: item.partNumber,
          partQuantity: item.quantity,
          unit: item.unit,
          supplierName: item.supplierName,
          requisitionNumber: receiptOrder.requisitionNumber,
          deliveryOrderNumber: receiptOrder.deliveryOrderNumber,
          purchaseOrderNumber: receiptOrder.purchaseOrderNumber,
          receiptTime: receiptOrder.receiptTime || new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).replace(/\//g, '-'),
          isReceived: receiptOrder.isReceived
        });
      });
    }
  });

  // 更新模态框表格数据
  receiptOrderData.value = receiptOrderItems;
  generateReceiptOrderDialogVisible.value = true;
};

const searchReceiptOrders = () => {
  // TODO: 实现收货单搜索逻辑
  console.log('收货单搜索参数:', generateReceiptOrderForm);
};

const resetReceiptOrderSearch = () => {
  generateReceiptOrderForm.partName = '';
  generateReceiptOrderForm.partNumber = '';
  generateReceiptOrderForm.supplierName = '';
  generateReceiptOrderForm.receiptTime = [];
};

const printReceiptOrder = () => {
  // 实现打印收货单逻辑并更新状态为"已开票"
  console.log('打印收货单:', selectedReceiptOrders.value);

  // 更新选中收货单的状态为"已开单"
  selectedRows.value.forEach(selectedRow => {
    const tableRow = tableData.value.find(item => item.receiptOrderNumber === selectedRow.receiptOrderNumber);
    if (tableRow) {
      tableRow.receiptStatus = 'billed';
    }
  });

  ElMessage.success('收货单已生成并开单完成');
  generateReceiptOrderDialogVisible.value = false;

  // 清空选择
  selectedRows.value = [];
};

const handleInvalidateReceiptOrder = () => {
  invalidateReceiptOrderDialogVisible.value = true;
};

const searchInvalidateOrders = () => {
  // TODO: 实现作废收货单搜索逻辑
  console.log('作废收货单搜索参数:', invalidateReceiptOrderForm);
};

const resetInvalidateOrderSearch = () => {
  invalidateReceiptOrderForm.receiptOrderNumber = '';
  invalidateReceiptOrderForm.status = '';
  invalidateReceiptOrderForm.partName = '';
  invalidateReceiptOrderForm.partNumber = '';
};

const viewOrderDetails = (row: ReceiptItem) => {
  // TODO: 实现查看订单详情逻辑
  console.log('查看订单详情:', row);
};

const invalidateOrder = (row: ReceiptItem) => {
  ElMessageBox.confirm(
    t('confirmInvalidate'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning'
    }
  ).then(() => {
    // TODO: 实现作废订单逻辑
    console.log('作废订单:', row);
  }).catch(() => {
    // 取消作废
  });
};

// 详情查看功能
const detailDialogVisible = ref(false);
const currentDetailData = ref<ReceiptItem | null>(null);

// 收货单查看功能
const receiptOrderViewDialogVisible = ref(false);
const currentReceiptOrderData = ref<ReceiptItem | null>(null);

const handleViewDetail = (row: ReceiptItem) => {
  currentDetailData.value = row;
  detailDialogVisible.value = true;
};

// 查看收货单功能
const handleViewReceiptOrder = () => {
  if (currentDetailData.value) {
    // 设置收货单数据并打开模态框
    currentReceiptOrderData.value = currentDetailData.value;
    receiptOrderViewDialogVisible.value = true;
  }
};

// 打印收货单功能
const handlePrintReceiptOrder = () => {
  if (currentReceiptOrderData.value) {
    // 实际应用中这里会调用打印功能
    ElMessage.success(`收货单 ${currentReceiptOrderData.value.receiptOrderNumber} 已发送到打印机`);

    // 实际实现可能包括：
    // 1. 生成PDF并打印
    // 2. 调用浏览器打印功能
    // 3. 发送到指定打印机
    console.log('打印收货单:', currentReceiptOrderData.value);
  }
};

// 单行收货功能
const handleReceiptSingle = (row: ReceiptItem) => {
  // 为每个物料项初始化收货数据
  if (row.items) {
    row.items.forEach(item => {
      if (!item.receiptQuantity) item.receiptQuantity = item.quantity;
      if (!item.damagedQuantity) item.damagedQuantity = 0;
      if (!item.damageDescription) item.damageDescription = '';
    });
  }
  selectedRows.value = [row];
  receiptDialogVisible.value = true;
};

// 更新收货数量
const updateReceiptQuantity = (receiptOrderNumber: string, itemIndex: number, quantity: number) => {
  const receiptOrder = selectedRows.value.find(row => row.receiptOrderNumber === receiptOrderNumber);
  if (receiptOrder && receiptOrder.items && receiptOrder.items[itemIndex]) {
    receiptOrder.items[itemIndex].receiptQuantity = quantity;
  }
};









// 收货状态处理函数
const getStatusTagType = (status?: string) => {
  switch (status) {
    case 'received':
      return 'success';
    case 'billed':
      return 'info';
    case 'notReceived':
    default:
      return 'warning';
  }
};

const getStatusLabel = (status?: string) => {
  switch (status) {
    case 'received':
      return t('partsReceipt.received');
    case 'billed':
      return t('partsReceipt.billed');
    case 'notReceived':
    default:
      return t('partsReceipt.notReceived');
  }
};

// 处理破损申报
const handleDamageReport = () => {
  console.log('handleDamageReport called');
  console.log('selectedRows.value:', selectedRows.value);

  // 收集当前收货单中的所有物料信息，用于破损申报
  const allItems: ReceiptDetailItem[] = [];

  selectedRows.value.forEach(receiptOrder => {
    if (receiptOrder.items) {
      receiptOrder.items.forEach(item => {
        allItems.push(item);
      });
    }
  });

  if (allItems.length === 0) {
    ElMessage.warning('没有可申报破损的物料');
    return;
  }

  // 设置当前收货单号
  currentReceiptOrderNumber.value = selectedRows.value[0]?.receiptOrderNumber || '';
  console.log('PartsReceiptView setting currentReceiptOrderNumber:', currentReceiptOrderNumber.value);

  // 保存当前上下文，用于后续更新破损数量
  currentScrapContext.value = {
    receiptOrderNumber: selectedRows.value[0]?.receiptOrderNumber || '',
    itemIndex: -1, // 表示是批量申报
    item: allItems[0] // 临时存储第一个物料信息
  };

  // 创建一个临时的报损单号用于预填充
  const tempScrapOrderNumber = `TEMP_${Date.now()}`;

  // 预填充报损表单数据 - 创建一个模拟的editData结构
  scrapPreFillData.value = {
    scrapOrderNumber: tempScrapOrderNumber,
    status: 'draft', // 草稿状态
    items: allItems.map(item => ({
      partName: item.partName,
      partNumber: item.partNumber,
      quantity: 0, // 初始破损数量为0，用户需要填写
      scrapSource: 'receipt', // 收货报损
      scrapReason: '',
      scrapImages: []
    }))
  };

  console.log('Opening scrap dialog with receiptOrderNumber:', currentReceiptOrderNumber.value);
  partScrapDialogVisible.value = true;
};

// 处理报损表单提交成功
const handlePartScrapSubmitSuccess = () => {
  // 从报损表单获取提交的数据，更新收货模态框中的破损数量
  if (scrapPreFillData.value && scrapPreFillData.value.items) {
    const scrapItems = scrapPreFillData.value.items;

    // 遍历所有选中的收货单，更新对应物料的破损数量
    selectedRows.value.forEach(receiptOrder => {
      if (receiptOrder.items) {
        receiptOrder.items.forEach(receiptItem => {
          // 查找对应的报损项目
          const scrapItem = scrapItems.find((item: any) =>
            item.partName === receiptItem.partName &&
            item.partNumber === receiptItem.partNumber
          );

          if (scrapItem && scrapItem.quantity > 0) {
            // 更新破损数量和描述
            receiptItem.damagedQuantity = scrapItem.quantity;
            receiptItem.damageDescription = '已申报破损';
          }
        });
      }
    });
  }

  partScrapDialogVisible.value = false;
  currentScrapContext.value = null;
  scrapPreFillData.value = null;
  currentReceiptOrderNumber.value = '';

  ElMessage.success('破损申报已保存，破损数量已更新到收货单中');
};

// 保存收货记录到localStorage
const saveReceiptRecords = (receiptOrders: ReceiptItem[], receiptTime: string) => {
  try {
    // 获取现有的收货记录
    const existingRecords = JSON.parse(localStorage.getItem('receiptRecords') || '[]') as Array<{
      receiptOrderNumber: string;
      generateDate: string;
      receiptTime: string;
      status: string;
      id: string;
      items: Array<{
        partName: string;
        partNumber: string;
        quantity: number;
        unit: string;
        receiptTime: string;
        receiptOrderNumber: string;
        supplierName: string;
        requisitionNumber: string;
        purchaseOrderNumber: string;
        deliveryOrderNumber: string;
        damagedQuantity: number;
        damageDescription: string;
      }>;
    }>;

    // 为每个收货单创建记录
    receiptOrders.forEach(receiptOrder => {
      // 检查是否已存在该收货单的记录
      const existingIndex = existingRecords.findIndex(record =>
        record.receiptOrderNumber === receiptOrder.receiptOrderNumber
      );

      const receiptRecord = {
        receiptOrderNumber: receiptOrder.receiptOrderNumber,
        generateDate: receiptTime.split(' ')[0], // 提取日期部分
        receiptTime: receiptTime,
        status: 'active',
        id: receiptOrder.receiptOrderNumber,
        items: receiptOrder.items?.map(item => ({
          partName: item.partName,
          partNumber: item.partNumber,
          quantity: item.receiptQuantity || item.quantity,
          unit: item.unit,
          receiptTime: receiptTime,
          receiptOrderNumber: receiptOrder.receiptOrderNumber,
          supplierName: item.supplierName,
          requisitionNumber: receiptOrder.requisitionNumber,
          purchaseOrderNumber: receiptOrder.purchaseOrderNumber,
          deliveryOrderNumber: receiptOrder.deliveryOrderNumber,
          damagedQuantity: item.damagedQuantity || 0,
          damageDescription: item.damageDescription || ''
        })) || []
      };

      if (existingIndex >= 0) {
        // 更新现有记录
        existingRecords[existingIndex] = receiptRecord;
      } else {
        // 添加新记录
        existingRecords.push(receiptRecord);
      }
    });

    // 保存到localStorage
    localStorage.setItem('receiptRecords', JSON.stringify(existingRecords));
    console.log('收货记录已保存到localStorage:', existingRecords);
  } catch (error) {
    console.error('保存收货记录失败:', error);
  }
};
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.page-title {
  margin-bottom: 20px;
}
.search-card,
.operation-card {
  margin-bottom: 20px;
}
.table-card {
  margin-bottom: 20px;
}
.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}
.buttons-col {
  text-align: right;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.receipt-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.receipt-order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.receipt-order-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}
</style>
