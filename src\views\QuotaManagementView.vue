<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  ElButton,
  ElCard,
  ElCol,
  ElDialog,
  ElFormItem,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElPagination,
  ElRow,
  ElSelect,
  ElTable,
  ElTableColumn,
  ElTag,
  ElDatePicker,
} from 'element-plus';
import { Plus, Edit, Delete, InfoFilled, Calendar, Clock, Check } from '@element-plus/icons-vue';

const { t, tc } = useModuleI18n('afterSales');

const t_quota = (key: string, params?: Record<string, unknown>) => t(`quotaManagement.${key}`, params);

// 门店信息
const storeInfo = computed(() => ({
  name: t_quota('storeName'),
  code: t_quota('storeCode'),
}));

// 表格数据结构
interface QuotaConfig {
  id: number;
  configDate: string;
  timeSlotCount: number;
  totalQuota: number;
  bookedQuantity: number;
  lastUpdateTime: string;
  isExpired: boolean;
}

// 时段配置数据结构
interface TimeSlot {
  id: number;
  start: string;
  end: string;
  quota: number;
}

const configuredList = ref<QuotaConfig[]>([]);
const totalRecords = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref(false);

// 弹窗相关
const dialogVisible = ref(false);
const isEditModal = ref(false);
const modalSelectedDate = ref<string>('');
const modalTimeSlots = ref<TimeSlot[]>([]);
const modalSlotIdCounter = ref(0); // 用于生成唯一时段ID

// 模拟已存在配额数据
const existingQuotaData: { [key: string]: TimeSlot[] } = {};

// 生成测试数据
const generateTestData = () => {
  const data: QuotaConfig[] = [];
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset to start of day

  for (let i = -3; i <= 7; i++) { // Generate for past 3 days to future 7 days
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    const configDate = date.toISOString().split('T')[0];

    const timeSlotCount = Math.floor(Math.random() * 5) + 2; // 2-6 slots
    let totalQuota = 0;
    const slots: TimeSlot[] = [];
    for (let j = 0; j < timeSlotCount; j++) {
      const startHour = 8 + j * 1; // Start from 08:00, 1-hour interval
      const startMin = Math.random() < 0.5 ? 0 : 30; // 0 or 30 mins
      const endHour = startHour + 1;
      const endMin = startMin;

      const start = `${String(startHour).padStart(2, '0')}:${String(startMin).padStart(2, '0')}`;
      const end = `${String(endHour).padStart(2, '0')}:${String(endMin).padStart(2, '0')}`;
      const quota = Math.floor(Math.random() * 20) + 5; // 5-25 quota

      slots.push({ id: modalSlotIdCounter.value++, start, end, quota });
      totalQuota += quota;
    }
    existingQuotaData[configDate] = slots;

    const bookedQuantity = Math.floor(Math.random() * (totalQuota + 1)); // 0 to totalQuota

    const lastUpdate = new Date(date.getTime() - Math.random() * 86400000)
      .toISOString()
      .slice(0, 16)
      .replace('T', ' ');

    data.push({
      id: i + 4, // Simple ID
      configDate,
      timeSlotCount,
      totalQuota,
      bookedQuantity,
      lastUpdateTime: lastUpdate,
      isExpired: date < today,
    });
  }
  configuredList.value = data;
  totalRecords.value = data.length;
};

// 渲染列表数据
const renderConfiguredList = () => {
  loading.value = true;
  // In a real application, this would fetch from a backend API based on pagination and filters
  // For now, we just simulate loading and display the generated data
  setTimeout(() => {
    configuredList.value = configuredList.value.map(item => ({
      ...item,
      isExpired: new Date(item.configDate) < new Date(new Date().setHours(0, 0, 0, 0)),
    }));
    totalRecords.value = configuredList.value.length;
    loading.value = false;
  }, 500);
};

// 分页逻辑
const handlePageChange = (page: number) => {
  currentPage.value = page;
  renderConfiguredList();
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  renderConfiguredList();
};

// 打开新增预约限量弹窗
const openNewQuotaModal = () => {
  isEditModal.value = false;
  dialogVisible.value = true;
  modalSelectedDate.value = new Date().toISOString().split('T')[0]; // 默认今天
  modalTimeSlots.value = [];
  modalSlotIdCounter.value = 0;
  onModalDateChange(); // 触发日期变更处理
};

// 编辑已有配置
const editConfig = (configDate: string) => {
  isEditModal.value = true;
  dialogVisible.value = true;
  modalSelectedDate.value = configDate;
  loadModalExistingConfig();
};

// 关闭弹窗
const closeNewQuotaModal = async (saved: boolean = false) => {
  if (!saved && modalTimeSlots.value.length > 0) {
    try {
      await ElMessageBox.confirm(
        t('quotaManagement.unsavedChangesWarning'),
        tc('warning'), {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning',
      });
      dialogVisible.value = false;
      resetModalData();
    } catch {
      // User cancelled closing the dialog
    }
  } else {
    dialogVisible.value = false;
    resetModalData();
  }
};

const resetModalData = () => {
  modalSelectedDate.value = '';
  modalTimeSlots.value = [];
  modalSlotIdCounter.value = 0;
  isEditModal.value = false;
};

// 日期选择变更处理
const onModalDateChange = () => {
  if (modalSelectedDate.value) {
    const selectedDate = new Date(modalSelectedDate.value);
    const today = new Date(new Date().setHours(0, 0, 0, 0));
    if (selectedDate < today) {
      ElMessage.warning(t('quotaManagement.dateMustBeFutureOrToday'));
      modalSelectedDate.value = today.toISOString().split('T')[0]; // Reset to today
      modalTimeSlots.value = [];
      return;
    }

    if (existingQuotaData[modalSelectedDate.value]) {
      loadModalExistingConfig();
    } else {
      modalTimeSlots.value = [];
    }
  } else {
    modalTimeSlots.value = [];
  }
};

// 加载已有配置数据到弹窗
const loadModalExistingConfig = () => {
  if (existingQuotaData[modalSelectedDate.value]) {
    modalTimeSlots.value = JSON.parse(JSON.stringify(existingQuotaData[modalSelectedDate.value]));
    modalSlotIdCounter.value = Math.max(...modalTimeSlots.value.map(s => s.id)) + 1 || 0;
  } else {
    modalTimeSlots.value = [];
  }
};

// 添加时段
const addModalTimeSlot = () => {
  if (!modalSelectedDate.value) {
    ElMessage.warning(t('quotaManagement.selectDateFirst'));
    return;
  }

  // Check if adding new slot exceeds 18:00
  const checkEndTime = new Date(`2000/01/01 ${end}`);
  if (checkEndTime.getHours() > 18 || (checkEndTime.getHours() === 18 && checkEndTime.getMinutes() > 0)) {
    ElMessage.warning(t('quotaManagement.timeSlotExceedsOperatingHours'));
    return;
  }

  modalTimeSlots.value.push({
    id: modalSlotIdCounter.value++,
    start: start,
    end: end,
    quota: 1,
  });
};

// 删除时段
const deleteModalTimeSlot = (slotId: number) => {
  modalTimeSlots.value = modalTimeSlots.value.filter((slot) => slot.id !== slotId);
};

// 更新时段字段
const updateModalTimeSlot = (slotId: number, field: 'start' | 'end' | 'quota', value: string | number) => {
  const slotIndex = modalTimeSlots.value.findIndex((slot) => slot.id === slotId);
  if (slotIndex !== -1) {
    if (field === 'quota') {
      modalTimeSlots.value[slotIndex].quota = Math.max(1, Math.floor(Number(value)));
    } else {
      modalTimeSlots.value[slotIndex][field] = value as string;
    }
    // If start time changes, ensure end time is still valid
    if (field === 'start') {
      const startTime = modalTimeSlots.value[slotIndex].start;
      const endTime = modalTimeSlots.value[slotIndex].end;
      if (compareTimes(startTime, endTime) >= 0) {
        // If end time is not after start time, auto-adjust to start time + 1 hour
        const [startHour, startMin] = startTime.split(':').map(Number);
        const newEndTime = new Date();
        newEndTime.setHours(startHour + 1, startMin, 0);
        modalTimeSlots.value[slotIndex].end = `${String(newEndTime.getHours()).padStart(2, '0')}:${String(newEndTime.getMinutes()).padStart(2, '0')}`;
      }
    }
  }
};

// 生成时间选项
const getModalTimeOptions = (isEndTime: boolean, startTime?: string) => {
  const options = [];
  for (let h = 8; h <= 18; h++) {
    for (const m of [0, 30]) {
      if (h === 18 && m > 0) continue; // Operating hours end at 18:00
      const time = `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}`;
      if (isEndTime && startTime) {
        if (compareTimes(time, startTime) > 0) { // End time must be > start time
          options.push({ label: time, value: time });
        }
      } else {
        options.push({ label: time, value: time });
      }
    }
  }
  return options;
};

// 比较时间字符串 (HH:mm)
const compareTimes = (time1: string, time2: string) => {
  const [h1, m1] = time1.split(':').map(Number);
  const [h2, m2] = time2.split(':').map(Number);
  if (h1 !== h2) return h1 - h2;
  return m1 - m2;
};

// 校验配置
const validateModalConfig = (): boolean => {
  if (!modalSelectedDate.value) {
    ElMessage.error(t('quotaManagement.validation.dateRequired'));
    return false;
  }
  if (modalTimeSlots.value.length === 0) {
    ElMessage.error(t('quotaManagement.validation.atLeastOneTimeSlot'));
    return false;
  }

  const sortedTimeSlots = [...modalTimeSlots.value].sort((a, b) => compareTimes(a.start, b.start));
  const timeRanges: { start: string; end: string }[] = [];

  for (const slot of sortedTimeSlots) {
    if (!slot.start || !slot.end) {
      ElMessage.error(t('quotaManagement.validation.timeRequired'));
      return false;
    }
    if (compareTimes(slot.start, slot.end) >= 0) {
      ElMessage.error(t('quotaManagement.validation.startBeforeEnd'));
      return false;
    }
    if (slot.quota < 1) {
      ElMessage.error(t('quotaManagement.validation.quotaPositive'));
      return false;
    }
  }
  return true;
};

// 保存配置
const saveModalConfig = async () => {
  if (!validateModalConfig()) {
    return;
  }

  // Simulate API call
  loading.value = true;
  try {
    const saveData = {
      date: modalSelectedDate.value,
      timeSlots: modalTimeSlots.value.map(slot => ({
        start: slot.start,
        end: slot.end,
        quota: slot.quota,
      })),
    };
    console.log('Saving config:', saveData);

    // Update existingQuotaData for simulation
    existingQuotaData[saveData.date] = saveData.timeSlots.map((s, idx) => ({ ...s, id: idx }));

    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
    ElMessage.success(tc('operationSuccessful'));
    closeNewQuotaModal(true);
    renderConfiguredList(); // Refresh list after save
  } catch {
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 汇总信息
const modalSummaryInfo = computed(() => {
  if (!modalSelectedDate.value) {
    return t('quotaManagement.summary.selectDate');
  }
  if (modalTimeSlots.value.length === 0) {
    return t('quotaManagement.summary.addTimeSlot');
  }
  const totalQuota = modalTimeSlots.value.reduce((sum, slot) => sum + slot.quota, 0);
  return `${modalSelectedDate.value} · ${modalTimeSlots.value.length}${t('quotaManagement.summary.timeSlotsUnit')} · ${t('quotaManagement.summary.totalQuota')}${totalQuota}`;
});

// 检查是否有已有配置
const hasExistingConfig = computed(() => {
  return !!existingQuotaData[modalSelectedDate.value];
});


onMounted(() => {
  generateTestData();
  renderConfiguredList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('quotaManagement.pageTitle') }}</h1>

    <!-- 门店信息展示区 -->
    <el-card class="mb-20 store-info-card">
      <el-row :gutter="20" class="store-info-row">
        <el-col :span="12">
          <span>{{ t('quotaManagement.storeName') }}：{{ storeInfo.name }}</span>
        </el-col>
        <el-col :span="12">
          <span>{{ t('quotaManagement.storeCode') }}：{{ storeInfo.code }}</span>
        </el-col>
      </el-row>
      <el-row class="permission-tip">
        <el-col>
          <el-icon><InfoFilled /></el-icon>
          <span>{{ t('quotaManagement.permissionTip') }}</span>
        </el-col>
      </el-row>
    </el-card>

    <!-- 已配置限量列表区 -->
    <el-card class="mb-20 configured-list-card">
      <div class="card-header">
        <h2>{{ t('quotaManagement.configuredListTitle') }}</h2>
        <el-button type="primary" :icon="Plus" @click="openNewQuotaModal">
          {{ t('quotaManagement.addNewQuota') }}
        </el-button>
      </div>

      <el-table :data="configuredList" v-loading="loading" style="width: 100%" class="configured-table">
        <el-table-column type="index" :label="tc('index')" width="80" />
        <el-table-column prop="configDate" :label="t('quotaManagement.table.configDate')" min-width="150" />
        <el-table-column prop="timeSlotCount" :label="t('quotaManagement.table.timeSlotCount')" min-width="120">
          <template #default="{ row }">
            {{ row.timeSlotCount }}{{ t('quotaManagement.summary.timeSlotsUnit') }}
          </template>
        </el-table-column>
        <el-table-column prop="totalQuota" :label="t('quotaManagement.table.totalQuota')" min-width="100" />
        <el-table-column prop="bookedQuantity" :label="t('quotaManagement.table.bookedQuantity')" min-width="120" />
        <el-table-column prop="lastUpdateTime" :label="t('quotaManagement.table.lastUpdateTime')" min-width="180" />
        <el-table-column :label="tc('operations')" width="100" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="!row.isExpired"
              type="primary"
              :icon="Edit"
              link
              @click="editConfig(row.configDate)"
            >{{ tc('edit') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && configuredList.length === 0" class="empty-state">
        <el-icon class="empty-icon"><Calendar /></el-icon>
        <p>{{ t('quotaManagement.emptyState') }}</p>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalRecords"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
          :page-sizes="[10, 20, 50]"
        />
      </div>
    </el-card>

    <!-- 新增/编辑预约限量弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditModal ? t('quotaManagement.modal.editTitle') : t('quotaManagement.modal.title')"
      width="800px"
      class="quota-dialog"
      :before-close="() => closeNewQuotaModal(false)"
    >
      <div class="dialog-content">
        <!-- 日期选择区 -->
        <el-card class="mb-20">
          <template #header>
            <div class="card-header-flex">
              <span>{{ t('quotaManagement.modal.selectDate') }}</span>
            </div>
          </template>
          <el-form-item :label="t('quotaManagement.modal.dateLabel')">
            <el-date-picker
              v-model="modalSelectedDate"
              type="date"
              value-format="YYYY-MM-DD"
              :placeholder="t('quotaManagement.modal.datePlaceholder')"
              style="width: 100%;"
              :clearable="false"
              :disabled-date="(date: Date) => date.getTime() < new Date(new Date().setHours(0,0,0,0)).getTime()"
              @change="onModalDateChange"
            />
          </el-form-item>
          <div v-if="hasExistingConfig" class="existing-config-tip">
            <el-tag type="warning">{{ t('quotaManagement.modal.existingConfig') }}</el-tag>
          </div>
          <div class="info-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>{{ t('quotaManagement.modal.dateTip') }}</span>
          </div>
        </el-card>

        <!-- 时段配置区 -->
        <el-card class="mb-20">
          <template #header>
            <div class="card-header-flex">
              <span>{{ t('quotaManagement.modal.timeSlotConfiguration') }}</span>
              <el-button type="info" :icon="Plus" link @click="addModalTimeSlot">
                {{ t('quotaManagement.modal.addTimeSlot') }}
              </el-button>
            </div>
          </template>

          <div v-if="modalTimeSlots.length === 0" class="empty-state-modal">
            <el-icon class="empty-icon"><Clock /></el-icon>
            <p>{{ t('quotaManagement.modal.noTimeSlots') }}</p>
            <p>{{ t('quotaManagement.modal.clickAddPrompt') }}</p>
          </div>

          <div class="time-slot-list">
            <el-card v-for="(slot, index) in modalTimeSlots" :key="slot.id" class="time-slot-card mb-10">
              <template #header>
                <div class="card-header-flex">
                  <span>{{ t('quotaManagement.modal.timeSlot') }} {{ index + 1 }}</span>
                  <el-button type="danger" :icon="Delete" link @click="deleteModalTimeSlot(slot.id)" class="delete-button">
                    {{ tc('delete') }}
                  </el-button>
                </div>
              </template>
              <el-row :gutter="10" align="middle">
                <el-col :xs="24" :sm="12" :md="8">
                  <el-form-item :label="t('quotaManagement.modal.startTime')">
                    <el-select
                      v-model="slot.start"
                      :placeholder="t('quotaManagement.modal.startTimePlaceholder')"
                      class="time-select"
                      @change="(val: string) => updateModalTimeSlot(slot.id, 'start', val)"
                    >
                      <el-option
                        v-for="item in getModalTimeOptions(false)"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <el-form-item :label="t('quotaManagement.modal.endTime')">
                    <el-select
                      v-model="slot.end"
                      :placeholder="t('quotaManagement.modal.endTimePlaceholder')"
                      class="time-select"
                      @change="(val: string) => updateModalTimeSlot(slot.id, 'end', val)"
                    >
                      <el-option
                        v-for="item in getModalTimeOptions(true, slot.start)"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="8">
                  <el-form-item :label="t('quotaManagement.modal.quota')">
                    <el-input
                      v-model.number="slot.quota"
                      type="number"
                      :min="1"
                      :placeholder="t('quotaManagement.modal.quotaPlaceholder')"
                      @change="(val: string | number) => updateModalTimeSlot(slot.id, 'quota', val)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </div>
        </el-card>

        <!-- 配置说明区 -->
        <el-card>
          <template #header>
            <div class="card-header-flex">
              <span>{{ t('quotaManagement.modal.configDescriptionTitle') }}</span>
            </div>
          </template>
          <ul>
            <li>{{ t('quotaManagement.modal.configDescription.item1') }}</li>
            <li>{{ t('quotaManagement.modal.configDescription.item2') }}</li>
            <li>{{ t('quotaManagement.modal.configDescription.item3') }}</li>
            <li>{{ t('quotaManagement.modal.configDescription.item4') }}</li>
          </ul>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer-buttons">
          <span class="summary-info">{{ modalSummaryInfo }}</span>
          <div>
            <el-button @click="closeNewQuotaModal(false)">{{ tc('cancel') }}</el-button>
            <el-button type="primary" :icon="Check" @click="saveModalConfig">{{ tc('save') }}</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
  background-color: #f5f7fa; // Light background
}

.page-title {
  color: #303133;
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
}

.mb-20 {
  margin-bottom: 20px;
}

/* Store Info Card */
.store-info-card {
  .store-info-row {
    font-size: 16px;
    color: #606266;
    margin-bottom: 10px;
    span {
      display: inline-block;
      margin-right: 20px;
    }
  }
  .permission-tip {
    font-size: 14px;
    color: #909399;
    .el-icon {
      vertical-align: middle;
      margin-right: 5px;
    }
  }
}

/* Card Headers with flex alignment */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  h2 {
    margin: 0;
    font-size: 18px;
    color: #303133;
  }
}

.card-header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}


/* Configured List Table */
.configured-list-card {
  .configured-table {
    margin-top: 15px;
    .el-table__body-wrapper {
      overflow-x: auto; // Enable horizontal scroll for table body
    }
    .expired-text {
      color: #C0C4CC; // Light gray for expired text
      font-style: italic;
    }
  }
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 50px 0;
  color: #909399;
  .empty-icon {
    font-size: 60px;
    margin-bottom: 15px;
  }
  p {
    margin: 0;
    font-size: 16px;
  }
}

/* Pagination */
.pagination-container {
  margin-top: 20px;
  text-align: right;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Dialog Styles */
.quota-dialog {
  .dialog-content {
    max-height: 70vh; // Limit height for scrollability
    overflow-y: auto; // Enable vertical scrolling
    padding-right: 10px; // For scrollbar
  }
  .el-dialog__header {
    border-bottom: 1px solid #EBEEF5;
    padding-bottom: 15px;
    margin-bottom: 20px;
  }
  .el-dialog__footer {
    border-top: 1px solid #EBEEF5;
    padding-top: 15px;
    margin-top: 20px;
  }

  .existing-config-tip {
    margin-top: 10px;
    text-align: right;
  }

  .info-tip {
    font-size: 13px;
    color: #909399;
    margin-top: 10px;
    .el-icon {
      vertical-align: middle;
      margin-right: 5px;
    }
  }

  .empty-state-modal {
    text-align: center;
    padding: 30px 0;
    color: #909399;
    .empty-icon {
      font-size: 50px;
      margin-bottom: 10px;
    }
    p {
      margin: 5px 0;
      font-size: 14px;
    }
  }

  .time-slot-list {
    .time-slot-card {
      background-color: #F8F8F8;
      border: 1px dashed #DCDFE6;
      .el-form-item {
        margin-bottom: 0; // Remove extra margin in time slot form items
      }
      .time-select {
        min-width: 120px;
        width: 100%;
      }
      .delete-button {
        margin-left: 10px;
      }
    }
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .summary-info {
      font-weight: bold;
      color: #303133;
      font-size: 16px;
    }
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
