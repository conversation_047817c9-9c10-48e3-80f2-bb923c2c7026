<template>
  <div class="test-container">
    <h1>销售订单管理系统测试页面</h1>

    <el-card>
      <h2>测试功能</h2>
      <el-space direction="vertical" size="large" style="width: 100%">
        <el-button type="primary" @click="testAPI">测试API接口</el-button>
        <el-button type="success" @click="goToSalesOrderManagement">进入销售订单管理</el-button>
        <el-button type="warning" @click="testDetailDialog">测试详情弹窗</el-button>
      </el-space>
    </el-card>

    <el-card v-if="apiTestResult" style="margin-top: 20px;">
      <h3>API测试结果:</h3>
      <pre>{{ JSON.stringify(apiTestResult, null, 2) }}</pre>
    </el-card>

    <!-- 测试详情弹窗 -->
    <SalesOrderDetailDialog
      v-model:visible="detailDialogVisible"
      :order-id="testOrderId"
      @close="detailDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getSalesOrderList, getSalesOrderDetail } from '@/api/modules/order'
import SalesOrderDetailDialog from '@/views/customerOrderManagement/components/SalesOrderDetailDialog.vue'

const router = useRouter()
const apiTestResult = ref<any>(null)
const detailDialogVisible = ref(false)
const testOrderId = ref('1')

const testAPI = async () => {
  try {
    console.log('Testing Sales Order API...')

    // 测试列表接口
    const listResult = await getSalesOrderList({
      page: 1,
      pageSize: 5
    })

    console.log('List API Result:', listResult)

    // 测试详情接口
    if (listResult.list && listResult.list.length > 0) {
      const detailResult = await getSalesOrderDetail(listResult.list[0].id)
      console.log('Detail API Result:', detailResult)

      apiTestResult.value = {
        listCount: listResult.total,
        firstOrderId: listResult.list[0].id,
        detailLoaded: !!detailResult
      }
    }

    ElMessage.success('API测试成功！')
  } catch (error: any) {
    console.error('API测试失败:', error)
    ElMessage.error('API测试失败')
    apiTestResult.value = { error: error.message }
  }
}

const goToSalesOrderManagement = () => {
router.push({ name: 'sales-order' })
}

const testDetailDialog = () => {
  detailDialogVisible.value = true
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

pre {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
