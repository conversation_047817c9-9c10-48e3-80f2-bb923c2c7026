<script setup lang="ts">
// 从售后模块的 API 导入相关功能函数
import { getAppointments, getAppointmentDetail, createQualityInspection } from '@/api/modules/afterSales/appointments';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { getServiceAdvisorList, getTechnicianList } from '@/api/modules/masterData';
// 导入售后预约相关的 TypeScript 类型定义
import type {
  AppointmentListItem,
  AppointmentDetail,
  AppointmentListParams
} from '@/types/module.d.ts';
// 从新的类型定义文件导入
import type {
  AppointmentListItem as NewAppointmentListItem,
  AppointmentDetail as NewAppointmentDetail,
  AppointmentListParams as NewAppointmentListParams
} from '@/types/afterSales/appointments.d.ts';
// 从主数据模块导入服务顾问和技师类型
import type {
  ServiceAdvisor,
  Technician
} from '@/api/modules/masterData';
// 导入子组件
import AppointmentDetailDialog from './components/AppointmentDetailDialog.vue';
import QualityInspectionDialog from './components/QualityInspectionDialog.vue';
// 从 Element Plus 导入所需的图标
import { Search, View, Document } from '@element-plus/icons-vue';
// 从 Element Plus 导入所需的 UI 组件
import {
  ElButton,
  ElCard,
  ElCol,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElPagination,
  ElRow,
  ElSelect,
  ElTable,
  ElTableColumn,
  ElTag,
  ElDatePicker
} from 'element-plus';
// 从 Vue 导入响应式和生命周期钩子函数
import { computed, onMounted, ref } from 'vue';

// 获取国际化函数 t 和当前语言环境 locale
const { t, tc } = useModuleI18n('afterSales.appointments');

// 计算车龄（月）
const calculateVehicleAgeInMonths = (productionDate: string | undefined): string => {
  if (!productionDate) {
    return tc('unknown');
  }
  const production = new Date(productionDate);
  const now = new Date();
  const years = now.getFullYear() - production.getFullYear();
  const months = now.getMonth() - production.getMonth();
  const totalMonths = years * 12 + months;
  return totalMonths >= 0 ? `${totalMonths} ${tc('months')}` : tc('unknown');
};

// 定义响应式变量来存储表格数据和相关状态
const appointments = ref<NewAppointmentListItem[]>([]); // 预约列表数据
const loading = ref(true); // 表格加载状态
const total = ref(0); // 数据总数，用于分页
const currentPage = ref(1); // 当前页码
const pageSize = ref(10); // 每页显示条数

// 查询表单数据，用于筛选预约列表
const searchParams = ref<NewAppointmentListParams>({
  appointmentId: '', // 预约单号
  licensePlate: '', // 车牌号
  reservationPhone: '', // 预约人手机号
  servicePhone: '', // 送修人手机号
  dateRange: undefined, // 创建时间范围
  status: undefined, // 状态
  serviceType: undefined, // 维修类型
  serviceAdvisorId: undefined, // 服务顾问
  technicianId: undefined // 技师
});

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.APPOINTMENT_STATUS,
  DICTIONARY_TYPES.WORK_ORDER_TYPE
]);

// 服务顾问数据 (主数据)
const serviceAdvisorOptions = ref([]);
const masterDataLoading = ref(false);

// 服务顾问和技师列表 (保留原有的)
const serviceAdvisors = ref<ServiceAdvisor[]>([]);
const technicians = ref<Technician[]>([]);

// 详情对话框相关的响应式变量
const detailDialogVisible = ref(false);
const currentAppointmentDetail = ref<NewAppointmentDetail | null>(null);
const detailLoading = ref(false);

// 环检单确认对话框相关的响应式变量
const inspectionConfirmDialogVisible = ref(false);
const currentInspectionAppointment = ref<NewAppointmentDetail | null>(null);
const inspectionConfirmLoading = ref(false);

// 获取字典选项的计算属性
const appointmentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPOINTMENT_STATUS));
const serviceTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.WORK_ORDER_TYPE));

// 加载服务顾问数据
const loadServiceAdvisors = async () => {
  masterDataLoading.value = true;
  try {
    serviceAdvisorOptions.value = await getServiceAdvisorList();
  } catch (error) {
    console.error('获取服务顾问数据失败:', error);
  } finally {
    masterDataLoading.value = false;
  }
};

// 计算属性：当前日期（用于默认筛选）
const todayDate = computed(() => {
  const today = new Date();
  return today.toISOString().split('T')[0];
});

// 异步函数：获取预约列表数据
const fetchAppointments = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchParams.value,
      page: currentPage.value,
      pageSize: pageSize.value
    };
    const response = await getAppointments(params);
    console.log(response)
    appointments.value = response.list;
    total.value = response.total;
  } catch (error) {
    console.error('Failed to fetch appointments:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 异步函数：获取服务顾问和技师列表
const fetchStaffList = async () => {
  try {
    const [advisors, techs] = await Promise.all([getServiceAdvisorList(), getTechnicianList()]);
    serviceAdvisors.value = advisors;
    technicians.value = techs;
  } catch {
    console.error('Failed to fetch staff list:');
  }
};

// 事件处理函数：搜索按钮点击
const handleSearch = () => {
  currentPage.value = 1;
  fetchAppointments();
};

// 事件处理函数：重置搜索表单
const resetSearch = () => {
  searchParams.value = {
    appointmentId: '',
    licensePlate: '',
    reservationPhone: '',
    servicePhone: '',
    dateRange: undefined,
    status: undefined,
    serviceType: undefined,
    serviceAdvisorId: undefined,
    technicianId: undefined
  };
  currentPage.value = 1;
  fetchAppointments();
};

// 事件处理函数：分页页码改变
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchAppointments();
};

// 事件处理函数：分页每页数量改变
const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchAppointments();
};

// 事件处理函数：查看详情
const handleViewDetail = async (row: NewAppointmentListItem) => {
  detailLoading.value = true;
  detailDialogVisible.value = true;
  try {
    const detail = await getAppointmentDetail(row.id);
    currentAppointmentDetail.value = detail;
  } catch (error) {
    console.error('Failed to fetch appointment detail:', error);
    ElMessage.error(tc('operationFailed'));
    detailDialogVisible.value = false;
  } finally {
    detailLoading.value = false;
  }
};

// 事件处理函数：创建环检单
const handleCreateQualityInspection = async (row: NewAppointmentListItem) => {
  try {
    const detail = await getAppointmentDetail(row.id);
    currentInspectionAppointment.value = detail;
    inspectionConfirmDialogVisible.value = true;
  } catch (error) {
    console.error('Failed to fetch appointment detail:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 确认创建环检单
const handleConfirmCreateQualityInspection = async (serviceContactData: { name: string; phone: string }) => {
  if (!currentInspectionAppointment.value) return;

  inspectionConfirmLoading.value = true;
  try {
    await createQualityInspection(currentInspectionAppointment.value.id, serviceContactData);

    // 创建环检单成功后，更新本地数据
    const appointmentIndex = appointments.value.findIndex(
      (app) => app.id === currentInspectionAppointment.value!.id
    );
    if (appointmentIndex !== -1) {
      appointments.value[appointmentIndex].serviceAdvisor = { id: 'SA001', name: '张三' };
      appointments.value[appointmentIndex].inspectionCreated = true;
      appointments.value[appointmentIndex].serviceContactName = serviceContactData.name;
      appointments.value[appointmentIndex].serviceContactPhone = serviceContactData.phone;
    }

    ElMessage.success(t('messages.inspectionCreatedSuccess'));
    inspectionConfirmDialogVisible.value = false;
  } catch (error) {
    console.error('Failed to create quality inspection:', error);
    ElMessage.error(t('messages.inspectionCreatedFailed'));
  } finally {
    inspectionConfirmLoading.value = false;
  }
};

// 格式化显示方法 - 使用字典接口
const formatAppointmentStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.APPOINTMENT_STATUS, status) || status;
const formatServiceType = (type: string) => getNameByCode(DICTIONARY_TYPES.WORK_ORDER_TYPE, type) || type;

// 生命周期钩子：组件挂载后执行
onMounted(() => {
  // 设置默认日期范围为今天
  // searchParams.value.dateRange = [todayDate.value, todayDate.value];
  fetchAppointments();
  fetchStaffList(); // 保留原有的获取方法
  loadServiceAdvisors(); // 新增主数据API获取方法
});
</script>

<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('appointmentManagement') }}</h1>

    <!-- 搜索区域卡片 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('labels.appointmentId')">
              <el-input
                v-model="searchParams.appointmentId"
                :placeholder="t('placeholders.appointmentId')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.licensePlate')">
              <el-input
                v-model="searchParams.licensePlate"
                :placeholder="t('placeholders.licensePlate')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.reservationPhone')">
              <el-input
                v-model="searchParams.reservationPhone"
                :placeholder="t('placeholders.reservationPhone')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.servicePhone')">
              <el-input
                v-model="searchParams.servicePhone"
                :placeholder="t('placeholders.servicePhone')"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('labels.creationTime')">
              <el-date-picker
                v-model="searchParams.dateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.status')">
              <el-select
                v-model="searchParams.status"
                :placeholder="t('placeholders.selectStatus')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="item in appointmentStatusOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.serviceType')">
              <el-select
                v-model="searchParams.serviceType"
                :placeholder="t('placeholders.selectServiceType')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="item in serviceTypeOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('labels.serviceAdvisor')">
              <el-select
                v-model="searchParams.serviceAdvisorId"
                :placeholder="t('placeholders.selectServiceAdvisor')"
                clearable
                style="width: 100%"
                :loading="masterDataLoading"
              >
                <el-option
                  v-for="item in serviceAdvisorOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="buttons-col text-right">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ tc('search') }}
            </el-button>
            <el-button @click="resetSearch">
              {{ tc('reset') }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 新增操作区域卡片 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20">
        <el-col :span="12">
          <span class="filter-summary">
            {{ t('labels.totalRecords', { total }) }}
          </span>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" :icon="Document">
            {{ t('exportExcel') }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表格区域卡片 -->
    <el-card class="table-card">
      <el-table :data="appointments" v-loading="loading" style="width: 100%">
        <el-table-column :label="t('headers.appointmentId')" min-width="140">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="licensePlate"
          :label="t('headers.licensePlate')"
          min-width="100"
        />
        <el-table-column
          prop="reservationContactName"
          :label="t('headers.reservationContactName')"
          min-width="100"
        />
        <el-table-column
          prop="reservationContactPhone"
          :label="t('headers.reservationContactPhone')"
          min-width="120"
        />
        <el-table-column
          prop="serviceContactName"
          :label="t('headers.serviceContactName')"
          min-width="100"
        />
        <el-table-column
          prop="serviceContactPhone"
          :label="t('headers.serviceContactPhone')"
          min-width="120"
        />
        <el-table-column :label="t('headers.appointmentDate')" min-width="100">
          <template #default="{ row }">
            {{ row.appointmentTime }}
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.timeSlot')" min-width="120">
          <template #default="{ row }">
            {{ row.timeSlot }}
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.serviceType')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.serviceType === 'maintenance' ? 'success' : 'warning'">
              {{ t(`serviceTypes.${row.serviceType}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.status')" min-width="100">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === 'arrived'
                  ? 'success'
                  : row.status === 'not_arrived'
                    ? 'info'
                    : row.status === 'cancelled'
                      ? 'info'
                      : row.status === 'pending_payment'
                        ? 'warning'
                        : 'danger'
              "
            >
              {{ t(`statuses.${row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.serviceAdvisor')" min-width="100">
          <template #default="{ row }">
            {{ row.serviceAdvisor?.name || t('labels.unassigned') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.qualityInspectionId')" min-width="140">
          <template #default="{ row }">
            {{ row.qualityInspectionId || t('labels.notGenerated') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('headers.creationTime')" min-width="150">
          <template #default="{ row }">
            {{ row.createdAt }}
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="View" link @click="handleViewDetail(row)">
              {{ tc('detail') }}
            </el-button>
            <el-button
              v-if="row.status === 'arrived' && !row.inspectionCreated"
              type="success"
              :icon="Document"
              link
              @click="handleCreateQualityInspection(row)"
            >
              {{ t('createInspection') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-card>

    <!-- 预约详情对话框 -->
    <AppointmentDetailDialog
      v-model:visible="detailDialogVisible"
      :appointment-detail="currentAppointmentDetail"
      :loading="detailLoading"
    />

    <!-- 环检单确认对话框 -->
    <QualityInspectionDialog
      v-model:visible="inspectionConfirmDialogVisible"
      :appointment-data="currentInspectionAppointment"
      :loading="inspectionConfirmLoading"
      @confirm="handleConfirmCreateQualityInspection"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-title {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .search-card {
    .search-form {
      .buttons-col {
        margin-top: 20px;
      }

      .text-right {
        text-align: right;
      }
    }
  }

  .operation-card {
    .filter-summary {
      font-size: 14px;
      color: #606266;
      line-height: 32px;
    }

    .text-right {
      text-align: right;
    }
  }

  .table-card {
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

// 全局样式覆盖
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  .el-button--link {
    padding: 0;
    height: auto;
    line-height: 1;
  }
}

:deep(.el-pagination) {
  justify-content: flex-end;
}
</style>
