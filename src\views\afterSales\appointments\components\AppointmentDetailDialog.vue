<script setup lang="ts">
import { computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { AppointmentDetail } from '@/types/afterSales/appointments.d.ts';
import {
  ElDialog,
  ElDescriptions,
  ElDescriptionsItem,
  ElTag,
  ElTable,
  ElTableColumn,
  ElButton
} from 'element-plus';

interface Props {
  visible: boolean;
  appointmentDetail: AppointmentDetail | null;
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.appointments');

// 计算车龄（月）
const calculateVehicleAgeInMonths = (productionDate: string | undefined): string => {
  if (!productionDate) {
    return tc('unknown');
  }
  const production = new Date(productionDate);
  const now = new Date();
  const years = now.getFullYear() - production.getFullYear();
  const months = now.getMonth() - production.getMonth();
  const totalMonths = years * 12 + months;
  return totalMonths >= 0 ? `${totalMonths} ${tc('months')}` : tc('unknown');
};

// 格式化价格显示
const formatPrice = (price: number) => {
  return `¥${price.toLocaleString()}`;
};

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return t('appointmentDetail') + (props.appointmentDetail ? ' - ' + props.appointmentDetail.id : '');
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    class="appointment-detail-dialog"
  >
    <div v-if="appointmentDetail" v-loading="loading">
      <!-- 预约信息 -->
      <el-descriptions :title="t('titles.appointmentInfo')" :column="2" border>
        <el-descriptions-item :label="t('labels.appointmentId')">
          {{ appointmentDetail.id }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.status')">
          <el-tag
            :type="
              appointmentDetail.status === 'arrived'
                ? 'success'
                : appointmentDetail.status === 'not_arrived'
                  ? 'info'
                  : appointmentDetail.status === 'cancelled'
                    ? 'info'
                    : 'danger'
            "
          >
            {{ t(`statuses.${appointmentDetail.status}`) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.appointmentTime')">
          {{ appointmentDetail.appointmentTime }} {{ appointmentDetail.timeSlot }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.serviceType')">
          <el-tag :type="appointmentDetail.serviceType === 'maintenance' ? 'success' : 'warning'">
            {{ t(`serviceTypes.${appointmentDetail.serviceType}`) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item
          v-if="appointmentDetail.customerDescription"
          :label="t('labels.customerDescription')"
          :span="2"
        >
          {{ appointmentDetail.customerDescription }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 客户信息 -->
      <el-descriptions :title="t('titles.customerInfo')" :column="2" border class="mt-20">
        <el-descriptions-item :label="t('labels.reservationContactName')">
          {{ appointmentDetail.reservationContactName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.reservationContactPhone')">
          {{ appointmentDetail.reservationContactPhone }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.serviceContactName')">
          {{ appointmentDetail.serviceContactName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.serviceContactPhone')">
          {{ appointmentDetail.serviceContactPhone }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 车辆信息 -->
      <el-descriptions :title="t('titles.vehicleInfo')" :column="2" border class="mt-20">
        <el-descriptions-item :label="t('labels.licensePlate')">
          {{ appointmentDetail.licensePlate }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.vin')">
          {{ appointmentDetail.vin }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.model')">
          {{ appointmentDetail.model }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.variant')">
          {{ appointmentDetail.variant }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.color')">
          {{ appointmentDetail.color }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.mileage')">
          {{
            appointmentDetail.mileage
              ? appointmentDetail.mileage + 'km'
              : tc('unknown')
          }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.vehicleAge')">
          {{ calculateVehicleAgeInMonths(appointmentDetail.productionDate) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 预约信息 -->
      <el-descriptions :title="t('titles.appointmentBookingInfo')" :column="2" border class="mt-20">
        <el-descriptions-item :label="t('labels.store')">
          {{ appointmentDetail.store.name }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.serviceAdvisor')">
          {{ appointmentDetail.serviceAdvisor?.name || t('labels.unassigned') }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 服务内容 - 保养类型显示 -->
      <div v-if="appointmentDetail.serviceType === 'maintenance'" class="mt-20">
        <h4>{{ t('titles.serviceContent') }}</h4>
        <div v-if="appointmentDetail.maintenancePackage" class="service-package">
          <!-- 保养服务包列表 -->
          <el-table :data="appointmentDetail.maintenancePackage.items" border style="width: 100%">
            <el-table-column prop="code" :label="t('headers.servicePackageCode')" width="120" />
            <el-table-column prop="name" :label="t('headers.servicePackageName')" />
            <el-table-column prop="quantity" :label="t('headers.quantity')" width="80" />
            <el-table-column prop="price" :label="t('headers.price')" width="100">
              <template #default="scope">
                {{ formatPrice(scope.row.price) }}
              </template>
            </el-table-column>
          </el-table>

          <!-- 总金额行 -->
          <div class="total-amount mt-10">
            <el-descriptions :column="1" border>
              <el-descriptions-item :label="t('labels.totalAmount')">
                <strong style="color: #409eff; font-size: 16px">
                  {{ formatPrice(appointmentDetail.maintenancePackage.totalAmount) }}
                </strong>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>

      <!-- 支付信息 - 保养类型显示 -->
      <el-descriptions
        v-if="
          appointmentDetail.paymentStatus &&
          appointmentDetail.serviceType === 'maintenance'
        "
        :title="t('titles.paymentInfo')"
        :column="2"
        border
        class="mt-20"
      >
        <el-descriptions-item :label="t('labels.paymentStatus')">
          <el-tag
            :type="
              appointmentDetail.paymentStatus === 'paid'
                ? 'success'
                : appointmentDetail.paymentStatus === 'unpaid'
                  ? 'warning'
                  : 'info'
            "
          >
            {{ t(`paymentStatuses.${appointmentDetail.paymentStatus}`) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.paymentAmount')">
          {{
            appointmentDetail.paymentAmount
              ? formatPrice(appointmentDetail.paymentAmount)
              : '-'
          }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.paymentOrderNumber')">
          {{ appointmentDetail.paymentOrderNumber || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="dialogVisible = false">{{ tc('cancel') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.appointment-detail-dialog {
  .mt-20 {
    margin-top: 20px;
  }

  .mt-10 {
    margin-top: 10px;
  }

  .service-package {
    .total-amount {
      text-align: right;
    }
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
