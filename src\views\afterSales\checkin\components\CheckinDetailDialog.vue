<script setup lang="ts">
import { computed } from 'vue';
import { ElDialog, ElDescriptions, ElDescriptionsItem } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListItem } from '@/types/afterSales/checkin.d.ts';

interface Props {
  visible: boolean;
  recordData: CheckinListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const currentDetails = computed(() => props.recordData || {});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('viewDetails')"
    width="500px"
    class="checkin-details-dialog"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item :label="t('checkinId')">
        {{ currentDetails.checkinId }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('licensePlate')">
        {{ currentDetails.licensePlate }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('vin')">
        {{ currentDetails.vin }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('vehicleModel')">
        {{ currentDetails.vehicleModel }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('vehicleConfiguration')">
        {{ currentDetails.vehicleConfiguration }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('color')">
        {{ currentDetails.color }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('mileage')">
        {{ currentDetails.mileage ? `${currentDetails.mileage} ${t('mileageUnit')}` : '-' }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('vehicleAge')">
        {{ currentDetails.vehicleAge ? `${currentDetails.vehicleAge} ${tc('months')}` : '-' }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('repairPersonName')">
        {{ currentDetails.repairPersonName }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('repairPersonPhone')">
        {{ currentDetails.repairPersonPhone }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('serviceAdvisor')">
        {{ currentDetails.serviceAdvisor }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('relatedRepairOrderId')">
        {{ currentDetails.relatedRepairOrderId || '-' }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('serviceType')">
        {{ currentDetails.serviceType }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('createdAt')">
        {{ currentDetails.createdAt }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('updatedAt')">
        {{ currentDetails.updatedAt }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('notes')">
        {{ currentDetails.notes || '-' }}
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<style scoped lang="scss">
.checkin-details-dialog {
  // 详情弹窗样式，如果需要可以定制
}
</style>
