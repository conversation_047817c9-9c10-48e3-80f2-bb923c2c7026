<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElTag } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { DashboardAppointmentItem } from '@/types/afterSales/dashboard.d.ts';

interface Props {
  appointments: DashboardAppointmentItem[];
}

defineProps<Props>();

const { t } = useModuleI18n('afterSales.dashboard');

// 获取服务类型标签样式
const getServiceTypeTag = (serviceType: string) => {
  return serviceType === 'maintenance' ? 'success' : 'warning';
};

// 获取服务类型显示文本
const getServiceTypeText = (serviceType: string) => {
  return t(`serviceTypes.${serviceType}`);
};

// 获取状态标签样式
const getStatusTag = (status: string) => {
  switch (status) {
    case 'arrived':
      return 'success';
    case 'notArrived':
      return 'info';
    case 'notFulfilled':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取状态显示文本
const getStatusText = (status: string) => {
  return t(`status.${status}`);
};
</script>

<template>
  <el-card class="table-card">
    <el-table :data="appointments" style="width: 100%" stripe>
      <el-table-column prop="plateNumber" :label="t('table.plateNumber')" />
      <el-table-column prop="appointmentDate" :label="t('table.appointmentDate')" />
      <el-table-column prop="timeSlot" :label="t('table.timeSlot')" />
      <el-table-column prop="serviceType" :label="t('table.serviceType')">
        <template #default="{ row }">
          <el-tag :type="getServiceTypeTag(row.serviceType)">
            {{ getServiceTypeText(row.serviceType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="t('table.status')">
        <template #default="{ row }">
          <el-tag :type="getStatusTag(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!-- Empty State -->
    <div v-if="appointments.length === 0" class="empty-state">
      <p>{{ t('emptyState') }}</p>
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}
</style>
