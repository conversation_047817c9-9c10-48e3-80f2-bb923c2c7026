<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('dispatch.dialog.assignTitle')"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
    >
      <el-form-item :label="t('dispatch.workOrderNo')" prop="workOrderNo">
        <el-input v-model="formData.workOrderNo" disabled />
      </el-form-item>
      
      <el-form-item :label="t('dispatch.dialog.selectTechnician')" prop="technicianId">
        <el-select
          v-model="formData.technicianId"
          :placeholder="t('dispatch.dialog.selectTechnician')"
          style="width: 100%"
          filterable
          @change="handleTechnicianChange"
        >
          <el-option
            v-for="technician in availableTechnicians"
            :key="technician.id"
            :label="getTechnician<PERSON>abel(technician)"
            :value="technician.id"
            :disabled="technician.status !== 'available'"
          >
            <div class="technician-option">
              <div class="technician-info">
                <span class="technician-name">{{ technician.name }}</span>
                <span class="technician-department">({{ technician.department }})</span>
              </div>
              <div class="technician-status">
                <el-tag 
                  :type="getTechnicianStatusTagType(technician.status)"
                  size="small"
                >
                  {{ t(`dispatch.technicianStatus.${technician.status}`) }}
                </el-tag>
                <span class="workload">{{ technician.currentWorkload }}/{{ technician.maxWorkload }}</span>
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item :label="t('dispatch.dialog.estimatedWorkHours')" prop="estimatedWorkHours">
        <el-input-number
          v-model="formData.estimatedWorkHours"
          :min="0.5"
          :max="24"
          :step="0.5"
          :precision="1"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item :label="t('dispatch.dialog.estimatedStartTime')" prop="estimatedStartTime">
        <el-date-picker
          v-model="formData.estimatedStartTime"
          type="datetime"
          :placeholder="t('dispatch.dialog.estimatedStartTime')"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
          :disabled-date="disabledDate"
          :disabled-hours="disabledHours"
          @change="handleStartTimeChange"
        />
      </el-form-item>
      
      <el-form-item :label="t('dispatch.dialog.estimatedFinishTime')" prop="estimatedFinishTime">
        <el-date-picker
          v-model="formData.estimatedFinishTime"
          type="datetime"
          :placeholder="t('dispatch.dialog.estimatedFinishTime')"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
          :disabled-date="disabledDate"
          :disabled-hours="disabledHours"
        />
      </el-form-item>
      
      <el-form-item :label="t('dispatch.dialog.notes')" prop="notes">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          :placeholder="t('dispatch.dialog.notes')"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <!-- 技师技能展示 -->
      <el-form-item v-if="selectedTechnician" :label="t('dispatch.technician.skills')">
        <div class="technician-skills">
          <el-tag
            v-for="skill in selectedTechnician.skills"
            :key="skill"
            size="small"
            type="info"
            style="margin-right: 8px; margin-bottom: 4px;"
          >
            {{ skill }}
          </el-tag>
        </div>
      </el-form-item>
      
      <!-- 技师当前工作负载 -->
      <el-form-item v-if="selectedTechnician" :label="t('dispatch.technician.workload')">
        <div class="workload-info">
          <el-progress
            :percentage="(selectedTechnician.currentWorkload / selectedTechnician.maxWorkload) * 100"
            :color="getWorkloadColor(selectedTechnician.currentWorkload / selectedTechnician.maxWorkload)"
            :show-text="false"
            style="margin-bottom: 8px;"
          />
          <div class="workload-text">
            {{ selectedTechnician.currentWorkload }}/{{ selectedTechnician.maxWorkload }} 
            ({{ Math.round((selectedTechnician.currentWorkload / selectedTechnician.maxWorkload) * 100) }}%)
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ t('dispatch.dialog.confirmAssign') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { 
  DispatchListItem, 
  TechnicianInfo, 
  AssignmentFormData 
} from '@/types/afterSales/dispatch';

// 组件Props
interface Props {
  visible: boolean;
  workOrder: DispatchListItem | null;
  technicians: TechnicianInfo[];
  loading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm', data: AssignmentFormData): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 表单引用
const formRef = ref<FormInstance>();

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单数据
const formData = reactive<AssignmentFormData>({
  workOrderNo: '',
  technicianId: '',
  estimatedStartTime: '',
  estimatedFinishTime: '',
  estimatedWorkHours: 2,
  notes: ''
});

// 表单验证规则
const formRules: FormRules = {
  technicianId: [
    { required: true, message: t('dispatch.messages.selectTechnician'), trigger: 'change' }
  ],
  estimatedStartTime: [
    { required: true, message: t('dispatch.messages.inputStartTime'), trigger: 'change' }
  ],
  estimatedFinishTime: [
    { required: true, message: t('dispatch.messages.inputFinishTime'), trigger: 'change' }
  ],
  estimatedWorkHours: [
    { required: true, message: t('dispatch.messages.inputWorkHours'), trigger: 'blur' }
  ]
};

// 可用技师列表
const availableTechnicians = computed(() => {
  return props.technicians.filter(tech => tech.status !== 'offline');
});

// 选中的技师
const selectedTechnician = computed(() => {
  return props.technicians.find(tech => tech.id === formData.technicianId);
});

// 监听工单变化
watch(
  () => props.workOrder,
  (newWorkOrder) => {
    if (newWorkOrder) {
      formData.workOrderNo = newWorkOrder.workOrderNo;
      formData.estimatedWorkHours = newWorkOrder.estimatedWorkHours || 2;
      
      // 设置默认开始时间为当前时间的下一个整点
      const now = new Date();
      const nextHour = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours() + 1, 0, 0);
      formData.estimatedStartTime = nextHour.toISOString().slice(0, 19).replace('T', ' ');
      
      // 计算预计完成时间
      updateFinishTime();
    }
  },
  { immediate: true }
);

// 获取技师标签
const getTechnicianLabel = (technician: TechnicianInfo) => {
  return `${technician.name} - ${technician.department}`;
};

// 获取技师状态标签类型
const getTechnicianStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    available: 'success',
    busy: 'warning',
    offline: 'info'
  };
  return statusMap[status] || 'info';
};

// 获取工作负载颜色
const getWorkloadColor = (ratio: number) => {
  if (ratio < 0.6) return '#67c23a';
  if (ratio < 0.8) return '#e6a23c';
  return '#f56c6c';
};

// 禁用日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
};

// 禁用小时
const disabledHours = () => {
  const hours = [];
  for (let i = 0; i < 8; i++) {
    hours.push(i);
  }
  for (let i = 19; i < 24; i++) {
    hours.push(i);
  }
  return hours;
};

// 技师变化处理
const handleTechnicianChange = () => {
  // 可以根据技师的工作安排调整时间
  updateFinishTime();
};

// 开始时间变化处理
const handleStartTimeChange = () => {
  updateFinishTime();
};

// 更新完成时间
const updateFinishTime = () => {
  if (formData.estimatedStartTime && formData.estimatedWorkHours) {
    const startTime = new Date(formData.estimatedStartTime);
    const finishTime = new Date(startTime.getTime() + formData.estimatedWorkHours * 60 * 60 * 1000);
    formData.estimatedFinishTime = finishTime.toISOString().slice(0, 19).replace('T', ' ');
  }
};

// 确认处理
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    emit('confirm', { ...formData });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 关闭处理
const handleClose = () => {
  formRef.value?.resetFields();
  emit('close');
};
</script>

<style scoped>
.technician-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.technician-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.technician-name {
  font-weight: 500;
}

.technician-department {
  color: #909399;
  font-size: 12px;
}

.technician-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.workload {
  font-size: 12px;
  color: #606266;
}

.technician-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.workload-info {
  width: 200px;
}

.workload-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 8px 20px;
}
</style>
