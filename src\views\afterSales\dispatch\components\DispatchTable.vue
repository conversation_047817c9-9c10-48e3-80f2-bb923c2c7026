<template>
  <el-card class="table-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">{{ t('dispatch.title') }}</span>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :icon="Download" 
            @click="handleExport"
            :loading="exportLoading"
          >
            {{ t('dispatch.actions.export') }}
          </el-button>
          <el-button 
            :icon="Refresh" 
            @click="handleRefresh"
            :loading="loading"
          >
            {{ t('dispatch.actions.refresh') }}
          </el-button>
        </div>
      </div>
    </template>

    <el-table
      :data="data"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column 
        prop="workOrderNo" 
        :label="t('dispatch.workOrderNo')" 
        width="140"
        fixed="left"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="handleViewDetail(row)">
            {{ row.workOrderNo }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="priority" 
        :label="t('dispatch.priorityLabel')" 
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="row.priority === 'urgent' ? 'danger' : 'info'"
            size="small"
          >
            {{ t(`dispatch.priority.${row.priority}`) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="workOrderType" 
        :label="t('dispatch.workOrderTypeLabel')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="getWorkOrderTypeTagType(row.workOrderType)"
            size="small"
          >
            {{ t(`dispatch.workOrderType.${row.workOrderType}`) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="workOrderStatus" 
        :label="t('dispatch.workOrderStatusLabel')" 
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="getWorkOrderStatusTagType(row.workOrderStatus)"
            size="small"
          >
            {{ t(`dispatch.workOrderStatus.${row.workOrderStatus}`) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="creationTime" 
        :label="t('dispatch.creationTime')" 
        width="160"
        align="center"
      />
      
      <el-table-column 
        prop="customerSource" 
        :label="t('dispatch.customerSourceLabel')" 
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="row.customerSource === 'appointment' ? 'success' : 'warning'"
            size="small"
          >
            {{ t(`dispatch.customerSource.${row.customerSource}`) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="repairmanName" 
        :label="t('dispatch.repairmanName')" 
        width="100"
      />
      
      <el-table-column 
        prop="licensePlateNumber" 
        :label="t('dispatch.licensePlateNumber')" 
        width="120"
      />
      
      <el-table-column 
        prop="vehicleModel" 
        :label="t('dispatch.vehicleModel')" 
        width="100"
      />
      
      <el-table-column 
        prop="serviceAdvisor" 
        :label="t('dispatch.serviceAdvisor')" 
        width="120"
      />
      
      <el-table-column 
        prop="technician" 
        :label="t('dispatch.technicianLabel')" 
        width="120"
      >
        <template #default="{ row }">
          <span v-if="row.technician">{{ row.technician }}</span>
          <el-tag v-else type="info" size="small">{{ t('dispatch.assignmentStatus.pending') }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="estimatedWorkHours" 
        :label="t('dispatch.estimatedWorkHours')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          {{ row.estimatedWorkHours }}h
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="estimatedStartTime" 
        :label="t('dispatch.estimatedStartTime')" 
        width="160"
        align="center"
      />
      
      <el-table-column 
        :label="t('dispatch.table.operations')" 
        width="200"
        fixed="right"
        align="center"
      >
        <template #default="{ row }">
          <div class="operation-buttons">
            <el-button 
              v-if="row.assignmentStatus === 'pending'"
              type="primary" 
              size="small" 
              @click="handleAssign(row)"
            >
              {{ t('dispatch.actions.assign') }}
            </el-button>
            
            <el-button 
              v-if="row.assignmentStatus === 'assigned'"
              type="warning" 
              size="small" 
              @click="handleReassign(row)"
            >
              {{ t('dispatch.actions.reassign') }}
            </el-button>
            
            <el-button 
              v-if="row.workOrderStatus === 'inProgress' && !row.isPaused"
              type="danger" 
              size="small" 
              @click="handlePause(row)"
            >
              {{ t('dispatch.actions.pause') }}
            </el-button>
            
            <el-button 
              v-if="row.workOrderStatus === 'paused'"
              type="success" 
              size="small" 
              @click="handleResume(row)"
            >
              {{ t('dispatch.actions.resume') }}
            </el-button>
            
            <el-button 
              v-if="row.workOrderStatus === 'inProgress'"
              type="success" 
              size="small" 
              @click="handleComplete(row)"
            >
              {{ t('dispatch.actions.complete') }}
            </el-button>
            
            <el-button 
              type="info" 
              size="small" 
              @click="handleViewDetail(row)"
            >
              {{ t('dispatch.actions.detail') }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Download, Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { DispatchListItem } from '@/types/afterSales/dispatch';

// 组件Props
interface Props {
  data: DispatchListItem[];
  loading?: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
  exportLoading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'assign', row: DispatchListItem): void;
  (e: 'reassign', row: DispatchListItem): void;
  (e: 'pause', row: DispatchListItem): void;
  (e: 'resume', row: DispatchListItem): void;
  (e: 'complete', row: DispatchListItem): void;
  (e: 'view-detail', row: DispatchListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'size-change', size: number): void;
  (e: 'selection-change', selection: DispatchListItem[]): void;
  (e: 'export'): void;
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  exportLoading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t } = useModuleI18n('afterSales');

// 选中的行
const selectedRows = ref<DispatchListItem[]>([]);

// 获取工单类型标签类型
const getWorkOrderTypeTagType = (type: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  const typeMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    repair: 'danger',
    maintenance: 'success',
    insurance: 'warning'
  };
  return typeMap[type] || 'info';
};

// 获取工单状态标签类型
const getWorkOrderStatusTagType = (status: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  const statusMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    pendingAssignment: 'info',
    pendingStart: 'warning',
    inProgress: 'primary',
    paused: 'danger',
    completed: 'success',
    cancelled: 'info',
    pendingQualityInspection: 'warning'
  };
  return statusMap[status] || 'info';
};

// 事件处理
const handleAssign = (row: DispatchListItem) => {
  emit('assign', row);
};

const handleReassign = (row: DispatchListItem) => {
  emit('reassign', row);
};

const handlePause = (row: DispatchListItem) => {
  emit('pause', row);
};

const handleResume = (row: DispatchListItem) => {
  emit('resume', row);
};

const handleComplete = (row: DispatchListItem) => {
  emit('complete', row);
};

const handleViewDetail = (row: DispatchListItem) => {
  emit('view-detail', row);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleSelectionChange = (selection: DispatchListItem[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

const handleExport = () => {
  emit('export');
};

const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped>
.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 500;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table .cell) {
  padding: 0 8px;
}

:deep(.el-button + .el-button) {
  margin-left: 5px;
}
</style>
