<template>
  <el-card class="technician-card" :class="{ 'card-available': technician.status === 'available' }">
    <template #header>
      <div class="card-header">
        <div class="technician-basic">
          <span class="technician-name">{{ technician.name }}</span>
          <el-tag 
            :type="getStatusTagType(technician.status)"
            size="small"
            class="status-tag"
          >
            {{ t(`dispatch.technicianStatus.${technician.status}`) }}
          </el-tag>
        </div>
        <div class="technician-department">
          {{ technician.department }}
        </div>
      </div>
    </template>

    <div class="card-content">
      <!-- 工作负载 -->
      <div class="workload-section">
        <div class="section-title">{{ t('dispatch.technician.workload') }}</div>
        <div class="workload-progress">
          <el-progress
            :percentage="utilizationPercentage"
            :color="getWorkloadColor(utilizationRate)"
            :show-text="false"
            :stroke-width="8"
          />
          <div class="workload-text">
            {{ technician.currentWorkload }}/{{ technician.maxWorkload }}
            ({{ utilizationPercentage }}%)
          </div>
        </div>
      </div>

      <!-- 当前工单 -->
      <div class="current-orders-section">
        <div class="section-title">
          {{ t('dispatch.technician.currentWorkOrders') }}
          <el-badge :value="technician.currentWorkOrders.length" class="badge" />
        </div>
        <div class="current-orders-list">
          <el-tag
            v-for="orderNo in technician.currentWorkOrders.slice(0, 3)"
            :key="orderNo"
            size="small"
            type="info"
            class="order-tag"
          >
            {{ orderNo }}
          </el-tag>
          <span v-if="technician.currentWorkOrders.length > 3" class="more-orders">
            +{{ technician.currentWorkOrders.length - 3 }}
          </span>
        </div>
      </div>

      <!-- 技能标签 -->
      <div class="skills-section">
        <div class="section-title">{{ t('dispatch.technician.skills') }}</div>
        <div class="skills-list">
          <el-tag
            v-for="skill in technician.skills"
            :key="skill"
            size="small"
            type="success"
            class="skill-tag"
          >
            {{ skill }}
          </el-tag>
        </div>
      </div>

      <!-- 工作时间 -->
      <div class="working-hours-section">
        <div class="section-title">{{ t('dispatch.workingHours') || '工作时间' }}</div>
        <div class="working-hours">
          <el-icon><Clock /></el-icon>
          <span>{{ technician.workingHours.start }} - {{ technician.workingHours.end }}</span>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">{{ t('dispatch.technician.experience') }}</div>
            <div class="stat-value">{{ technician.experience }}年</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">{{ t('dispatch.technician.efficiency') }}</div>
            <div class="stat-value">{{ Math.round(technician.efficiency * 100) }}%</div>
          </div>
        </div>
      </div>

      <!-- 预计空闲时间 -->
      <div v-if="technician.status === 'busy'" class="available-time-section">
        <div class="section-title">{{ t('dispatch.technician.estimatedAvailableTime') }}</div>
        <div class="available-time">
          <el-icon><Timer /></el-icon>
          <span>{{ formatAvailableTime() }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer v-if="showActions">
      <div class="card-actions">
        <el-button 
          size="small" 
          type="primary" 
          :disabled="technician.status !== 'available'"
          @click="handleAssignWork"
        >
          {{ t('dispatch.actions.assign') }}
        </el-button>
        <el-button 
          size="small" 
          type="info" 
          @click="handleViewSchedule"
        >
          {{ t('dispatch.viewSchedule') || '查看日程' }}
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Clock, Timer } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { TechnicianInfo } from '@/types/afterSales/dispatch';

// 组件Props
interface Props {
  technician: TechnicianInfo;
  showActions?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'assign-work', technician: TechnicianInfo): void;
  (e: 'view-schedule', technician: TechnicianInfo): void;
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true
});

const emit = defineEmits<Emits>();

// 国际化
const { t } = useModuleI18n('afterSales');

// 计算利用率
const utilizationRate = computed(() => {
  return props.technician.currentWorkload / props.technician.maxWorkload;
});

// 计算利用率百分比
const utilizationPercentage = computed(() => {
  return Math.round(utilizationRate.value * 100);
});

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    available: 'success',
    busy: 'warning',
    offline: 'info'
  };
  return statusMap[status] || 'info';
};

// 获取工作负载颜色
const getWorkloadColor = (ratio: number) => {
  if (ratio < 0.6) return '#67c23a';
  if (ratio < 0.8) return '#e6a23c';
  return '#f56c6c';
};

// 格式化预计空闲时间
const formatAvailableTime = () => {
  // 这里可以根据实际需求计算预计空闲时间
  const now = new Date();
  const availableTime = new Date(now.getTime() + Math.random() * 4 * 60 * 60 * 1000);
  return availableTime.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 分配工作处理
const handleAssignWork = () => {
  emit('assign-work', props.technician);
};

// 查看日程处理
const handleViewSchedule = () => {
  emit('view-schedule', props.technician);
};
</script>

<style scoped>
.technician-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.technician-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-available {
  border-color: #67c23a;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.technician-basic {
  display: flex;
  align-items: center;
  gap: 8px;
}

.technician-name {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.status-tag {
  margin-left: 8px;
}

.technician-department {
  color: #909399;
  font-size: 12px;
}

.card-content {
  padding: 0;
}

.section-title {
  font-weight: 500;
  color: #606266;
  font-size: 12px;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.workload-section {
  margin-bottom: 16px;
}

.workload-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.workload-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.current-orders-section {
  margin-bottom: 16px;
}

.current-orders-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.order-tag {
  font-size: 10px;
}

.more-orders {
  font-size: 12px;
  color: #909399;
}

.badge {
  margin-left: 8px;
}

.skills-section {
  margin-bottom: 16px;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.skill-tag {
  font-size: 10px;
}

.working-hours-section {
  margin-bottom: 16px;
}

.working-hours {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.stats-section {
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stat-label {
  font-size: 10px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.available-time-section {
  margin-bottom: 16px;
}

.available-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #e6a23c;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-card__footer) {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}
</style>
