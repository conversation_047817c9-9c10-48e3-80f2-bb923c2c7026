<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('dispatch.dialog.detailTitle')"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="detail-container">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane :label="t('dispatch.basicInfo') || '基本信息'" name="basic">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('dispatch.workOrderNo')">
                {{ workOrderDetail?.workOrderNo }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.priorityLabel')">
                <el-tag 
                  :type="workOrderDetail?.priority === 'urgent' ? 'danger' : 'info'"
                  size="small"
                >
                  {{ workOrderDetail?.priority ? t(`dispatch.priority.${workOrderDetail.priority}`) : '' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.workOrderTypeLabel')">
                <el-tag size="small">
                  {{ workOrderDetail?.workOrderType ? t(`dispatch.workOrderType.${workOrderDetail.workOrderType}`) : '' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.workOrderStatusLabel')">
                <el-tag 
                  :type="getWorkOrderStatusTagType(workOrderDetail?.workOrderStatus)"
                  size="small"
                >
                  {{ workOrderDetail?.workOrderStatus ? t(`dispatch.workOrderStatus.${workOrderDetail.workOrderStatus}`) : '' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.creationTime')">
                {{ workOrderDetail?.creationTime }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.customerSourceLabel')">
                <el-tag 
                  :type="workOrderDetail?.customerInfo?.source === 'appointment' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ workOrderDetail?.customerInfo?.source ? t(`dispatch.customerSource.${workOrderDetail.customerInfo.source}`) : '' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 客户信息 -->
        <el-tab-pane :label="t('dispatch.customerInfo') || '客户信息'" name="customer">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('dispatch.repairmanName')">
                {{ workOrderDetail?.customerInfo?.name }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.repairmanPhone')">
                {{ workOrderDetail?.customerInfo?.phone }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 车辆信息 -->
        <el-tab-pane :label="t('dispatch.vehicleInfo') || '车辆信息'" name="vehicle">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('dispatch.licensePlateNumber')">
                {{ workOrderDetail?.vehicleInfo?.licensePlateNumber }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.vehicleModel')">
                {{ workOrderDetail?.vehicleInfo?.model }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.configuration')">
                {{ workOrderDetail?.vehicleInfo?.configuration }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.color')">
                {{ workOrderDetail?.vehicleInfo?.color }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.vehicleAge')">
                {{ workOrderDetail?.vehicleInfo?.age }}个月
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.mileage')">
                {{ workOrderDetail?.vehicleInfo?.mileage }}km
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 服务信息 -->
        <el-tab-pane :label="t('dispatch.serviceInfo') || '服务信息'" name="service">
          <div class="detail-section">
            <el-descriptions :column="1" border>
              <el-descriptions-item :label="t('dispatch.serviceAdvisor')">
                {{ workOrderDetail?.serviceInfo?.advisor }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.serviceDescription') || '服务描述'">
                {{ workOrderDetail?.serviceInfo?.description }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.symptoms') || '故障现象'">
                <div class="symptoms-list">
                  <el-tag
                    v-for="symptom in workOrderDetail?.serviceInfo?.symptoms"
                    :key="symptom"
                    size="small"
                    type="warning"
                    style="margin-right: 8px; margin-bottom: 4px;"
                  >
                    {{ symptom }}
                  </el-tag>
                </div>
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.diagnosis') || '诊断结果'">
                {{ workOrderDetail?.serviceInfo?.diagnosis }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 分配信息 -->
        <el-tab-pane :label="t('dispatch.assignmentInfo') || '分配信息'" name="assignment">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('dispatch.assignmentStatusLabel')">
                <el-tag 
                  :type="workOrderDetail?.assignmentInfo?.status === 'assigned' ? 'success' : 'info'"
                  size="small"
                >
                  {{ workOrderDetail?.assignmentInfo?.status ? t(`dispatch.assignmentStatus.${workOrderDetail.assignmentInfo.status}`) : '' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.technicianLabel')">
                {{ workOrderDetail?.assignmentInfo?.technician || t('dispatch.assignmentStatus.pending') }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.estimatedWorkHours')">
                {{ workOrderDetail?.assignmentInfo?.estimatedWorkHours }}h
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.estimatedStartTime')">
                {{ workOrderDetail?.assignmentInfo?.estimatedStartTime }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.estimatedFinishTime')">
                {{ workOrderDetail?.assignmentInfo?.estimatedFinishTime }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.actualStartTime')">
                {{ workOrderDetail?.assignmentInfo?.actualStartTime || '-' }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.actualFinishTime')">
                {{ workOrderDetail?.assignmentInfo?.actualFinishTime || '-' }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.notes')" :span="2">
                {{ workOrderDetail?.assignmentInfo?.notes || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 进度信息 -->
        <el-tab-pane :label="t('dispatch.progressInfo') || '进度信息'" name="progress">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('dispatch.isPaused') || '是否暂停'">
                <el-tag 
                  :type="workOrderDetail?.progressInfo?.isPaused ? 'danger' : 'success'"
                  size="small"
                >
                  {{ workOrderDetail?.progressInfo?.isPaused ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.pauseReason')" v-if="workOrderDetail?.progressInfo?.isPaused">
                {{ workOrderDetail?.progressInfo?.pauseReason }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.completionTime')">
                {{ workOrderDetail?.progressInfo?.completionTime || '-' }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.qualityInspector')">
                {{ workOrderDetail?.progressInfo?.qualityInspector || '-' }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('dispatch.qualityInspectionTime')">
                {{ workOrderDetail?.progressInfo?.qualityInspectionTime || '-' }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 工作日志 -->
            <div class="work-log-section" style="margin-top: 20px;">
              <h4>{{ t('dispatch.workLog') || '工作日志' }}</h4>
              <el-timeline>
                <el-timeline-item
                  v-for="log in workOrderDetail?.progressInfo?.workLog"
                  :key="log.id"
                  :timestamp="log.timestamp"
                  placement="top"
                >
                  <div class="log-content">
                    <div class="log-header">
                      <span class="log-operator">{{ log.operator }}</span>
                      <span class="log-action">{{ log.action }}</span>
                    </div>
                    <div class="log-description">{{ log.description }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getWorkOrderDetail } from '@/api/modules/afterSales/dispatch';
import type { WorkOrderDetail } from '@/types/afterSales/dispatch';

// 组件Props
interface Props {
  visible: boolean;
  workOrderNo: string;
}

// 组件Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 当前激活的标签页
const activeTab = ref('basic');

// 加载状态
const loading = ref(false);

// 工单详情数据
const workOrderDetail = ref<WorkOrderDetail | null>(null);

// 获取工单状态标签类型
const getWorkOrderStatusTagType = (status?: string) => {
  if (!status) return 'info';
  const statusMap: Record<string, string> = {
    pendingAssignment: 'info',
    pendingStart: 'warning',
    inProgress: 'primary',
    paused: 'danger',
    completed: 'success',
    cancelled: 'info',
    pendingQualityInspection: 'warning'
  };
  return statusMap[status] || 'info';
};

// 监听工单编号变化，加载详情
watch(
  () => props.workOrderNo,
  async (newWorkOrderNo) => {
    if (newWorkOrderNo && props.visible) {
      await loadWorkOrderDetail(newWorkOrderNo);
    }
  },
  { immediate: true }
);

// 监听对话框显示状态
watch(
  () => props.visible,
  async (visible) => {
    if (visible && props.workOrderNo) {
      await loadWorkOrderDetail(props.workOrderNo);
    }
  }
);

// 加载工单详情
const loadWorkOrderDetail = async (workOrderNo: string) => {
  try {
    loading.value = true;
    workOrderDetail.value = await getWorkOrderDetail(workOrderNo);
  } catch (error) {
    console.error('加载工单详情失败:', error);
    workOrderDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 关闭处理
const handleClose = () => {
  activeTab.value = 'basic';
  workOrderDetail.value = null;
  emit('close');
};
</script>

<style scoped>
.detail-container {
  min-height: 400px;
}

.detail-section {
  padding: 20px;
}

.symptoms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.work-log-section h4 {
  margin-bottom: 16px;
  color: #303133;
  font-weight: 500;
}

.log-content {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.log-operator {
  font-weight: 500;
  color: #303133;
}

.log-action {
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.log-description {
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}
</style>
