<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getInspectionList,
  assignTechnician,
  submitForConfirm,
  recallInspection,
  customerConfirm
} from '@/api/modules/afterSales/inspection';
import { getTechnicianList } from '@/api/modules/masterData';
import type {
  InspectionListItem,
  InspectionSearchParams
} from '@/types/afterSales/inspection.d.ts';
import type { Technician } from '@/api/modules/masterData';

// 导入子组件
import InspectionSearchForm from './components/InspectionSearchForm.vue';
import InspectionTable from './components/InspectionTable.vue';
import AssignDialog from './components/AssignDialog.vue';
import DetailEditDialog from './components/DetailEditDialog.vue';
import CustomerConfirmDialog from './components/CustomerConfirmDialog.vue';

const { t, tc } = useModuleI18n('afterSales.inspection');

// 搜索相关
const searchParams = reactive<InspectionSearchParams>({
  inspectionNo: '',
  inspectionStatus: '',
  licensePlateNo: '',
  repairmanName: '',
  technician: '',
  repairmanPhone: '',
  createTimeRange: null,
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  searchParams.createTimeRange = newVal;
});

const inspectionList = ref<InspectionListItem[]>([]);
const loading = ref(false);
const technicians = ref<Technician[]>([]);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 弹窗相关
const assignDialogVisible = ref(false);
const detailEditDialogVisible = ref(false);
const customerConfirmDialogVisible = ref(false);
const currentRecord = ref<InspectionListItem | null>(null);

// 技师选项
const technicianOptions = computed(() => 
  technicians.value.map(tech => ({ label: tech.name, value: tech.name }))
);

// 获取环检单列表
const fetchInspectionList = async () => {
  loading.value = true;
  try {
    const response = await getInspectionList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    inspectionList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch inspection list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 获取技师列表
const fetchTechnicianList = async () => {
  try {
    const response = await getTechnicianList();
    technicians.value = response;
  } catch (error) {
    console.error('Failed to fetch technician list:', error);
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchInspectionList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    inspectionNo: '',
    inspectionStatus: '',
    licensePlateNo: '',
    repairmanName: '',
    technician: '',
    repairmanPhone: '',
    createTimeRange: null,
  });
  dateRange.value = null;
  pagination.page = 1;
  fetchInspectionList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchInspectionList();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchInspectionList();
};

// 分配技师
const handleAssignTechnician = (row: InspectionListItem) => {
  currentRecord.value = row;
  assignDialogVisible.value = true;
};

// 提交客户确认
const handleSubmitForConfirm = async (row: InspectionListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmSubmit'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await submitForConfirm(row.inspectionNo);
        ElMessage.success(t('messages.submitSuccess'));
        fetchInspectionList();
      } catch (error) {
        console.error('Failed to submit for confirm:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 撤回检查
const handleRecallInspection = async (row: InspectionListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmRecall'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await recallInspection(row.inspectionNo);
        ElMessage.success(t('messages.recallSuccess'));
        fetchInspectionList();
      } catch (error) {
        console.error('Failed to recall inspection:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 客户确认
const handleCustomerConfirm = (row: InspectionListItem) => {
  currentRecord.value = row;
  customerConfirmDialogVisible.value = true;
};

// 查看详情
const handleViewDetail = (row: InspectionListItem) => {
  currentRecord.value = row;
  detailEditDialogVisible.value = true;
};

// 编辑详情
const handleEditDetail = (row: InspectionListItem) => {
  currentRecord.value = row;
  detailEditDialogVisible.value = true;
};

// 分配技师确认
const handleAssignConfirm = async (technicianId: string) => {
  if (!currentRecord.value) return;
  
  try {
    await assignTechnician(currentRecord.value.inspectionNo, technicianId);
    ElMessage.success(t('messages.assignSuccess'));
    assignDialogVisible.value = false;
    fetchInspectionList();
  } catch (error) {
    console.error('Failed to assign technician:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 客户确认提交
const handleCustomerConfirmSubmit = async (confirmTime: string) => {
  if (!currentRecord.value) return;
  
  try {
    await customerConfirm(currentRecord.value.inspectionNo, confirmTime);
    ElMessage.success(t('messages.confirmSuccess'));
    customerConfirmDialogVisible.value = false;
    fetchInspectionList();
  } catch (error) {
    console.error('Failed to customer confirm:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTechnicianList();
  fetchInspectionList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索表单 -->
    <InspectionSearchForm
      v-model:search-params="searchParams"
      v-model:date-range="dateRange"
      :technician-options="technicianOptions"
      @search="handleSearch"
      @reset="resetSearch"
    />

    <!-- 数据表格 -->
    <InspectionTable
      :inspection-list="inspectionList"
      :loading="loading"
      :pagination="pagination"
      @assign-technician="handleAssignTechnician"
      @submit-for-confirm="handleSubmitForConfirm"
      @recall-inspection="handleRecallInspection"
      @customer-confirm="handleCustomerConfirm"
      @view-detail="handleViewDetail"
      @edit-detail="handleEditDetail"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 分配技师弹窗 -->
    <AssignDialog
      v-model:visible="assignDialogVisible"
      :record-data="currentRecord"
      :technicians="technicians"
      @confirm="handleAssignConfirm"
    />

    <!-- 详情编辑弹窗 -->
    <DetailEditDialog
      v-model:visible="detailEditDialogVisible"
      :record-data="currentRecord"
    />

    <!-- 客户确认弹窗 -->
    <CustomerConfirmDialog
      v-model:visible="customerConfirmDialogVisible"
      :record-data="currentRecord"
      @confirm="handleCustomerConfirmSubmit"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}
</style>
