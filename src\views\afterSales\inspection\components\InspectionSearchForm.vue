<script setup lang="ts">
import { ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElButton, ElRow, ElCol } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionSearchParams } from '@/types/afterSales/inspection.d.ts';

interface Props {
  searchParams: InspectionSearchParams;
  dateRange: [string, string] | null;
  technicianOptions: Array<{ label: string; value: string }>;
}

interface Emits {
  (e: 'update:searchParams', value: InspectionSearchParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const statusOptions = [
  { label: t('status.pending'), value: 'pending' },
  { label: t('status.in_progress'), value: 'in_progress' },
  { label: t('status.pending_confirm'), value: 'pending_confirm' },
  { label: t('status.confirmed'), value: 'confirmed' }
];

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const updateSearchParams = (field: keyof InspectionSearchParams, value: any) => {
  emit('update:searchParams', { ...props.searchParams, [field]: value });
};

const updateDateRange = (value: [string, string] | null) => {
  emit('update:dateRange', value);
};
</script>

<template>
  <el-card class="mb-20 search-card">
    <el-form :model="searchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.inspectionNo')">
            <el-input
              :model-value="searchParams.inspectionNo"
              @update:model-value="(val) => updateSearchParams('inspectionNo', val)"
              :placeholder="t('searchForm.inspectionNoPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.inspectionStatus')">
            <el-select
              :model-value="searchParams.inspectionStatus"
              @update:model-value="(val) => updateSearchParams('inspectionStatus', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.licensePlateNo')">
            <el-input
              :model-value="searchParams.licensePlateNo"
              @update:model-value="(val) => updateSearchParams('licensePlateNo', val)"
              :placeholder="t('searchForm.licensePlateNoPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.repairmanName')">
            <el-input
              :model-value="searchParams.repairmanName"
              @update:model-value="(val) => updateSearchParams('repairmanName', val)"
              :placeholder="t('searchForm.repairmanNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.technician')">
            <el-select
              :model-value="searchParams.technician"
              @update:model-value="(val) => updateSearchParams('technician', val)"
              :placeholder="t('searchForm.technicianPlaceholder')"
              clearable
            >
              <el-option
                v-for="option in technicianOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.repairmanPhone')">
            <el-input
              :model-value="searchParams.repairmanPhone"
              @update:model-value="(val) => updateSearchParams('repairmanPhone', val)"
              :placeholder="t('searchForm.repairmanPhonePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.createTime')">
            <el-date-picker
              :model-value="dateRange"
              @update:model-value="updateDateRange"
              type="daterange"
              range-separator="-"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item class="search-buttons">
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="handleReset">{{ tc('reset') }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
  .search-buttons {
    display: flex;
    align-items: flex-end;
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
