<script setup lang="ts">
import { <PERSON><PERSON>ard, ElTable, ElTableColumn, ElButton, ElPagination, ElTag } from 'element-plus';
import { View, Edit, UserFilled, Check, RefreshLeft } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionListItem } from '@/types/afterSales/inspection.d.ts';

interface Props {
  inspectionList: InspectionListItem[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

interface Emits {
  (e: 'assign-technician', row: InspectionListItem): void;
  (e: 'submit-for-confirm', row: InspectionListItem): void;
  (e: 'recall-inspection', row: InspectionListItem): void;
  (e: 'customer-confirm', row: InspectionListItem): void;
  (e: 'view-detail', row: InspectionListItem): void;
  (e: 'edit-detail', row: InspectionListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    in_progress: 'primary',
    pending_confirm: 'info',
    confirmed: 'success'
  };
  return statusMap[status as keyof typeof statusMap] || 'info';
};

const getRegisterTypeText = (type: string) => {
  return type === 'appointment' ? t('registerType.appointment') : t('registerType.walk_in');
};

const getServiceTypeText = (type: string) => {
  return type === 'maintenance' ? t('serviceType.maintenance') : t('serviceType.repair');
};

const handleAssignTechnician = (row: InspectionListItem) => {
  emit('assign-technician', row);
};

const handleSubmitForConfirm = (row: InspectionListItem) => {
  emit('submit-for-confirm', row);
};

const handleRecallInspection = (row: InspectionListItem) => {
  emit('recall-inspection', row);
};

const handleCustomerConfirm = (row: InspectionListItem) => {
  emit('customer-confirm', row);
};

const handleViewDetail = (row: InspectionListItem) => {
  emit('view-detail', row);
};

const handleEditDetail = (row: InspectionListItem) => {
  emit('edit-detail', row);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (size: number) => {
  emit('page-size-change', size);
};
</script>

<template>
  <el-card class="table-card">
    <el-table
      :data="inspectionList"
      v-loading="loading"
      style="width: 100%"
      border
      :empty-text="tc('noData')"
    >
      <el-table-column type="index" :label="tc('index')" width="60" />
      <el-table-column prop="inspectionNo" :label="t('table.inspectionNo')" min-width="140" />
      <el-table-column prop="inspectionStatus" :label="t('table.inspectionStatus')" min-width="120">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.inspectionStatus)">
            {{ t(`status.${scope.row.inspectionStatus}`) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="repairmanName" :label="t('table.repairmanName')" min-width="100" />
      <el-table-column prop="repairmanPhone" :label="t('table.repairmanPhone')" min-width="120" />
      <el-table-column prop="licensePlateNo" :label="t('table.licensePlateNo')" min-width="100" />
      <el-table-column prop="vehicleModel" :label="t('table.vehicleModel')" min-width="120" />
      <el-table-column prop="color" :label="t('table.color')" min-width="80" />
      <el-table-column prop="serviceAdvisor" :label="t('table.serviceAdvisor')" min-width="100" />
      <el-table-column prop="technician" :label="t('table.technician')" min-width="100" />
      <el-table-column prop="registerType" :label="t('table.registerType')" min-width="100">
        <template #default="scope">
          {{ getRegisterTypeText(scope.row.registerType) }}
        </template>
      </el-table-column>
      <el-table-column prop="serviceType" :label="t('table.serviceType')" min-width="100">
        <template #default="scope">
          {{ getServiceTypeText(scope.row.serviceType) }}
        </template>
      </el-table-column>
      <el-table-column prop="customerConfirmTime" :label="t('table.customerConfirmTime')" min-width="140">
        <template #default="scope">
          {{ scope.row.customerConfirmTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="t('table.createTime')" min-width="140" />
      <el-table-column :label="tc('operations')" width="300" fixed="right">
        <template #default="scope">
          <!-- 待处理状态 -->
          <template v-if="scope.row.inspectionStatus === 'pending'">
            <el-button type="primary" :icon="UserFilled" link @click="handleAssignTechnician(scope.row)">
              {{ t('actions.assign') }}
            </el-button>
          </template>
          
          <!-- 检查中状态 -->
          <template v-if="scope.row.inspectionStatus === 'in_progress'">
            <el-button type="success" :icon="Check" link @click="handleSubmitForConfirm(scope.row)">
              {{ t('actions.submitForConfirm') }}
            </el-button>
            <el-button type="primary" :icon="Edit" link @click="handleEditDetail(scope.row)">
              {{ t('actions.editDetail') }}
            </el-button>
          </template>
          
          <!-- 待客户确认状态 -->
          <template v-if="scope.row.inspectionStatus === 'pending_confirm'">
            <el-button type="warning" :icon="RefreshLeft" link @click="handleRecallInspection(scope.row)">
              {{ t('actions.recall') }}
            </el-button>
            <el-button type="success" :icon="Check" link @click="handleCustomerConfirm(scope.row)">
              {{ t('actions.customerConfirm') }}
            </el-button>
          </template>
          
          <!-- 通用操作 -->
          <el-button type="info" :icon="View" link @click="handleViewDetail(scope.row)">
            {{ t('actions.viewDetail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
