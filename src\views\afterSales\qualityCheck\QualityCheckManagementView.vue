<template>
  <div class="quality-check-management-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ t('qualityCheck.title') }}</h1>
      <p class="page-subtitle">{{ t('qualityCheck.subtitle') }}</p>
    </div>

    <!-- 统计卡片 -->
    <QualityCheckStatsCard
      :statistics="statistics"
      :loading="statisticsLoading"
      @refresh="loadStatistics"
    />

    <!-- 搜索表单 -->
    <QualityCheckSearchForm
      v-model:searchParams="searchParams"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 质检表格 -->
    <QualityCheckTable
      :data="qualityCheckList"
      :loading="loading"
      :total="total"
      :current-page="searchParams.page"
      :page-size="searchParams.pageSize"
      :export-loading="exportLoading"
      @start="handleStart"
      @submit="handleSubmit"
      @audit="handleAudit"
      @rework="handleRework"
      @view-detail="handleViewDetail"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
      @selection-change="handleSelectionChange"
      @export="handleExport"
      @batch-audit="handleBatchAudit"
      @refresh="handleRefresh"
    />

    <!-- 质检提交对话框 -->
    <QualityCheckSubmitDialog
      v-model:visible="submitDialogVisible"
      :quality-check="selectedQualityCheck"
      :loading="submitLoading"
      :draft-loading="draftLoading"
      @confirm="handleSubmitConfirm"
      @save-draft="handleSaveDraft"
      @close="handleSubmitClose"
    />

    <!-- 质检审核对话框 -->
    <QualityCheckAuditDialog
      v-model:visible="auditDialogVisible"
      :quality-check="selectedQualityCheck"
      :loading="auditLoading"
      @confirm="handleAuditConfirm"
      @close="handleAuditClose"
    />

    <!-- 质检详情对话框 -->
    <QualityCheckDetailDialog
      v-model:visible="detailDialogVisible"
      :quality-check-id="selectedQualityCheckId"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';

// 组件导入
import QualityCheckStatsCard from './components/QualityCheckStatsCard.vue';
import QualityCheckSearchForm from './components/QualityCheckSearchForm.vue';
import QualityCheckTable from './components/QualityCheckTable.vue';
import QualityCheckSubmitDialog from './components/QualityCheckSubmitDialog.vue';
import QualityCheckAuditDialog from './components/QualityCheckAuditDialog.vue';
import QualityCheckDetailDialog from './components/QualityCheckDetailDialog.vue';

// API导入
import {
  getQualityCheckList,
  startQualityCheck,
  submitQualityCheck,
  auditQualityCheck,
  processRework,
  getQualityCheckStatistics,
  exportQualityCheckData,
  saveQualityCheckDraft,
  batchQualityCheckOperation
} from '@/api/modules/afterSales/qualityCheck';

// 类型导入
import type {
  QualityCheckListItem,
  QualityCheckSearchParams,
  QualityCheckSubmitForm,
  QualityCheckAuditForm,
  QualityCheckStatistics
} from '@/types/afterSales/qualityCheck';

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 响应式数据
const loading = ref(false);
const statisticsLoading = ref(false);
const submitLoading = ref(false);
const auditLoading = ref(false);
const draftLoading = ref(false);
const exportLoading = ref(false);

// 搜索参数
const searchParams = reactive<QualityCheckSearchParams>({
  page: 1,
  pageSize: 20,
  qualityCheckNo: '',
  workOrderNo: '',
  status: [],
  workOrderType: undefined,
  technicianName: '',
  plateNumber: '',
  serviceCustomerName: '',
  createTimeStart: '',
  createTimeEnd: '',
  isClaimRelated: undefined,
  isOutsourceRelated: undefined
});

// 数据列表
const qualityCheckList = ref<QualityCheckListItem[]>([]);
const statistics = ref<QualityCheckStatistics | null>(null);
const total = ref(0);
const selectedRows = ref<QualityCheckListItem[]>([]);

// 对话框状态
const submitDialogVisible = ref(false);
const auditDialogVisible = ref(false);
const detailDialogVisible = ref(false);

// 选中的数据
const selectedQualityCheck = ref<QualityCheckListItem | null>(null);
const selectedQualityCheckId = ref('');

// 页面初始化
onMounted(async () => {
  await Promise.all([
    loadQualityCheckList(),
    loadStatistics()
  ]);
});

// 加载质检单列表
const loadQualityCheckList = async () => {
  try {
    loading.value = true;
    const response = await getQualityCheckList(searchParams);
    qualityCheckList.value = response.data;
    total.value = response.total;
  } catch (error) {
    console.error('加载质检单列表失败:', error);
    ElMessage.error(t('qualityCheck.messages.loadListFailed'));
  } finally {
    loading.value = false;
  }
};

// 加载统计数据
const loadStatistics = async () => {
  try {
    statisticsLoading.value = true;
    statistics.value = await getQualityCheckStatistics();
  } catch (error) {
    console.error('加载统计数据失败:', error);
  } finally {
    statisticsLoading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  searchParams.page = 1;
  loadQualityCheckList();
};

// 重置处理
const handleReset = () => {
  Object.assign(searchParams, {
    page: 1,
    pageSize: 20,
    qualityCheckNo: '',
    workOrderNo: '',
    status: [],
    workOrderType: undefined,
    technicianName: '',
    plateNumber: '',
    serviceCustomerName: '',
    createTimeStart: '',
    createTimeEnd: '',
    isClaimRelated: undefined,
    isOutsourceRelated: undefined
  });
  loadQualityCheckList();
};

// 分页处理
const handlePageChange = (page: number) => {
  searchParams.page = page;
  loadQualityCheckList();
};

const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  loadQualityCheckList();
};

// 选择变化处理
const handleSelectionChange = (selection: QualityCheckListItem[]) => {
  selectedRows.value = selection;
};

// 刷新处理
const handleRefresh = () => {
  Promise.all([
    loadQualityCheckList(),
    loadStatistics()
  ]);
};

// 开始质检处理
const handleStart = async (row: QualityCheckListItem) => {
  try {
    await ElMessageBox.confirm(
      t('qualityCheck.messages.confirmOperation'),
      t('qualityCheck.actions.start'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );
    
    await startQualityCheck(row.id);
    ElMessage.success(t('qualityCheck.messages.startSuccess'));
    loadQualityCheckList();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始质检失败:', error);
      ElMessage.error(t('qualityCheck.messages.startFailed'));
    }
  }
};

// 提交质检处理
const handleSubmit = (row: QualityCheckListItem) => {
  selectedQualityCheck.value = row;
  submitDialogVisible.value = true;
};

// 审核处理
const handleAudit = (row: QualityCheckListItem) => {
  selectedQualityCheck.value = row;
  auditDialogVisible.value = true;
};

// 返工处理
const handleRework = async (row: QualityCheckListItem) => {
  try {
    const { value: notes } = await ElMessageBox.prompt(
      t('qualityCheck.dialog.inputReworkRequirement'),
      t('qualityCheck.actions.rework'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        inputPlaceholder: t('qualityCheck.dialog.inputReworkRequirement')
      }
    );
    
    await processRework({
      qualityCheckId: row.id,
      reworkNotes: notes
    });
    ElMessage.success(t('qualityCheck.messages.reworkSuccess'));
    loadQualityCheckList();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('返工处理失败:', error);
      ElMessage.error(t('qualityCheck.messages.reworkFailed'));
    }
  }
};

// 查看详情处理
const handleViewDetail = (row: QualityCheckListItem) => {
  selectedQualityCheckId.value = row.id;
  detailDialogVisible.value = true;
};

// 导出处理
const handleExport = async () => {
  try {
    exportLoading.value = true;
    const blob = await exportQualityCheckData({
      ...searchParams,
      exportType: 'all',
      exportFormat: 'excel'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `quality_check_data_${new Date().toISOString().slice(0, 10)}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success(t('qualityCheck.messages.exportSuccess'));
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('qualityCheck.messages.exportFailed'));
  } finally {
    exportLoading.value = false;
  }
};

// 批量审核处理
const handleBatchAudit = async (selection: QualityCheckListItem[]) => {
  try {
    const { value: auditResult } = await ElMessageBox.prompt(
      t('qualityCheck.dialog.selectAuditResult'),
      t('qualityCheck.actions.batchAudit'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        inputType: 'select',
        inputOptions: [
          { label: t('qualityCheck.auditResult.passed'), value: 'passed' },
          { label: t('qualityCheck.auditResult.rework'), value: 'rework' }
        ]
      }
    );

    await batchQualityCheckOperation({
      qualityCheckIds: selection.map(item => item.id),
      operation: 'audit',
      auditResult: auditResult as 'passed' | 'rework'
    });

    ElMessage.success(t('qualityCheck.messages.batchOperationSuccess'));
    loadQualityCheckList();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量审核失败:', error);
      ElMessage.error(t('qualityCheck.messages.batchOperationFailed'));
    }
  }
};

// 提交确认处理
const handleSubmitConfirm = async (data: QualityCheckSubmitForm) => {
  try {
    submitLoading.value = true;
    await submitQualityCheck(data);
    ElMessage.success(t('qualityCheck.messages.submitSuccess'));
    submitDialogVisible.value = false;
    loadQualityCheckList();
    loadStatistics();
  } catch (error) {
    console.error('提交质检结果失败:', error);
    ElMessage.error(t('qualityCheck.messages.submitFailed'));
  } finally {
    submitLoading.value = false;
  }
};

// 保存草稿处理
const handleSaveDraft = async (data: QualityCheckSubmitForm) => {
  try {
    draftLoading.value = true;
    await saveQualityCheckDraft(data);
    ElMessage.success(t('qualityCheck.messages.saveDraftSuccess'));
  } catch (error) {
    console.error('保存草稿失败:', error);
    ElMessage.error(t('qualityCheck.messages.saveDraftFailed'));
  } finally {
    draftLoading.value = false;
  }
};

// 提交关闭处理
const handleSubmitClose = () => {
  submitDialogVisible.value = false;
  selectedQualityCheck.value = null;
};

// 审核确认处理
const handleAuditConfirm = async (data: QualityCheckAuditForm) => {
  try {
    auditLoading.value = true;
    await auditQualityCheck(data);
    ElMessage.success(t('qualityCheck.messages.auditSuccess'));
    auditDialogVisible.value = false;
    loadQualityCheckList();
    loadStatistics();
  } catch (error) {
    console.error('质检审核失败:', error);
    ElMessage.error(t('qualityCheck.messages.auditFailed'));
  } finally {
    auditLoading.value = false;
  }
};

// 审核关闭处理
const handleAuditClose = () => {
  auditDialogVisible.value = false;
  selectedQualityCheck.value = null;
};

// 详情关闭处理
const handleDetailClose = () => {
  detailDialogVisible.value = false;
  selectedQualityCheckId.value = '';
};
</script>

<style scoped>
.quality-check-management-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-subtitle {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .quality-check-management-container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .quality-check-management-container {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }
}
</style>
