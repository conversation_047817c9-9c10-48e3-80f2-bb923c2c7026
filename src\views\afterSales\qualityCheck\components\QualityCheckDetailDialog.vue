<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('qualityCheck.dialog.detailTitle')"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="detail-container">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane :label="t('qualityCheck.tabs.basicInfo')" name="basic">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('qualityCheck.qualityCheckNo')">
                {{ qualityCheckDetail?.qualityCheck?.qualityCheckNo }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.workOrderNo')">
                {{ qualityCheckDetail?.qualityCheck?.workOrderNo }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.statusLabel')">
                <el-tag 
                  :type="getStatusTagType(qualityCheckDetail?.qualityCheck?.status)"
                  size="small"
                >
                  {{ qualityCheckDetail?.qualityCheck?.status ? t(`qualityCheck.status.${qualityCheckDetail.qualityCheck.status}`) : '' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.workOrderTypeLabel')">
                <el-tag size="small">
                  {{ qualityCheckDetail?.qualityCheck?.workOrderType ? t(`qualityCheck.workOrderType.${qualityCheckDetail.qualityCheck.workOrderType}`) : '' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.technicianName')">
                {{ qualityCheckDetail?.qualityCheck?.technicianName }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.createTime')">
                {{ formatDateTime(qualityCheckDetail?.qualityCheck?.createTime) }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.startTime')">
                {{ qualityCheckDetail?.qualityCheck?.startTime ? formatDateTime(qualityCheckDetail.qualityCheck.startTime) : '-' }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.finishTime')">
                {{ qualityCheckDetail?.qualityCheck?.finishTime ? formatDateTime(qualityCheckDetail.qualityCheck.finishTime) : '-' }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.estimatedHours')">
                {{ qualityCheckDetail?.qualityCheck?.estimatedHours }}h
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.actualHours')">
                {{ qualityCheckDetail?.qualityCheck?.actualHours ? `${qualityCheckDetail.qualityCheck.actualHours}h` : '-' }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.isClaimRelated')">
                <el-tag 
                  :type="qualityCheckDetail?.qualityCheck?.isClaimRelated ? 'warning' : 'info'"
                  size="small"
                >
                  {{ qualityCheckDetail?.qualityCheck?.isClaimRelated ? tc('yes') : tc('no') }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.isOutsourceRelated')">
                <el-tag 
                  :type="qualityCheckDetail?.qualityCheck?.isOutsourceRelated ? 'warning' : 'info'"
                  size="small"
                >
                  {{ qualityCheckDetail?.qualityCheck?.isOutsourceRelated ? tc('yes') : tc('no') }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 质检项目 -->
        <el-tab-pane :label="t('qualityCheck.tabs.checkItems')" name="checkItems">
          <div class="detail-section">
            <div v-for="category in groupedCheckItems" :key="category.categoryCode" class="check-category">
              <h4 class="category-title">{{ category.categoryName }}</h4>
              <el-table :data="category.items" border style="width: 100%">
                <el-table-column 
                  prop="itemName" 
                  :label="t('qualityCheck.checkItem.itemName')" 
                  width="200"
                />
                <el-table-column 
                  prop="itemType" 
                  :label="t('qualityCheck.itemType.BOOLEAN')" 
                  width="100"
                  align="center"
                >
                  <template #default="{ row }">
                    <el-tag size="small">
                      {{ t(`qualityCheck.itemType.${row.itemType}`) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="standardValue" 
                  :label="t('qualityCheck.checkItem.standardValue')" 
                  width="150"
                />
                <el-table-column 
                  :label="t('qualityCheck.checkItem.actualValue')" 
                  width="150"
                >
                  <template #default="{ row }">
                    <span v-if="row.itemType === 'BOOLEAN'">
                      <el-tag 
                        :type="row.checkResult === 'PASS' ? 'success' : 'danger'"
                        size="small"
                      >
                        {{ row.checkResult ? t(`qualityCheck.checkResult.${row.checkResult}`) : '-' }}
                      </el-tag>
                    </span>
                    <span v-else-if="row.itemType === 'NUMERIC'">
                      {{ row.numericValue }}{{ row.unit }}
                    </span>
                    <span v-else>
                      {{ row.textValue || '-' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column 
                  prop="isRequired" 
                  :label="t('qualityCheck.checkItem.required')" 
                  width="100"
                  align="center"
                >
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.isRequired ? 'danger' : 'info'"
                      size="small"
                    >
                      {{ row.isRequired ? t('qualityCheck.checkItem.required') : t('qualityCheck.checkItem.optional') }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 工单信息 -->
        <el-tab-pane :label="t('qualityCheck.tabs.workOrderInfo')" name="workOrder">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('qualityCheck.workOrderNo')">
                {{ qualityCheckDetail?.workOrderInfo?.workOrderNo }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.workOrderTypeLabel')">
                {{ qualityCheckDetail?.workOrderInfo?.workOrderType ? t(`qualityCheck.workOrderType.${qualityCheckDetail.workOrderInfo.workOrderType}`) : '' }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.serviceAdvisor')">
                {{ qualityCheckDetail?.workOrderInfo?.serviceAdvisor }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.estimatedHours')">
                {{ qualityCheckDetail?.workOrderInfo?.estimatedHours }}h
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.serviceDescription')" :span="2">
                {{ qualityCheckDetail?.workOrderInfo?.description }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.symptoms')" :span="2">
                <div class="symptoms-list">
                  <el-tag
                    v-for="symptom in qualityCheckDetail?.workOrderInfo?.symptoms"
                    :key="symptom"
                    size="small"
                    type="warning"
                    style="margin-right: 8px; margin-bottom: 4px;"
                  >
                    {{ symptom }}
                  </el-tag>
                </div>
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.diagnosis')" :span="2">
                {{ qualityCheckDetail?.workOrderInfo?.diagnosis }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 车辆信息 -->
        <el-tab-pane :label="t('qualityCheck.tabs.vehicleInfo')" name="vehicle">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('qualityCheck.plateNumber')">
                {{ qualityCheckDetail?.vehicleInfo?.plateNumber }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.vehicleModel')">
                {{ qualityCheckDetail?.vehicleInfo?.vehicleModel }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.vehicleConfig')">
                {{ qualityCheckDetail?.vehicleInfo?.vehicleConfig }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.vehicleColor')">
                {{ qualityCheckDetail?.vehicleInfo?.vehicleColor }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.vehicleAge')">
                {{ qualityCheckDetail?.vehicleInfo?.vehicleAge }}个月
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.mileage')">
                {{ qualityCheckDetail?.vehicleInfo?.mileage }}km
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.vin') || 'VIN码'">
                {{ qualityCheckDetail?.vehicleInfo?.vin }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 服务信息 -->
        <el-tab-pane :label="t('qualityCheck.tabs.serviceInfo')" name="service">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('qualityCheck.serviceCustomerName')">
                {{ qualityCheckDetail?.serviceInfo?.serviceCustomerName }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.serviceCustomerPhone')">
                {{ qualityCheckDetail?.serviceInfo?.serviceCustomerPhone }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.serviceType') || '服务类型'">
                {{ qualityCheckDetail?.serviceInfo?.serviceType }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.serviceDate') || '服务日期'">
                {{ formatDateTime(qualityCheckDetail?.serviceInfo?.serviceDate) }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.serviceLocation') || '服务地点'">
                {{ qualityCheckDetail?.serviceInfo?.serviceLocation }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('qualityCheck.serviceNotes') || '服务备注'" :span="2">
                {{ qualityCheckDetail?.serviceInfo?.serviceNotes || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getQualityCheckDetail } from '@/api/modules/afterSales/qualityCheck';
import type { QualityCheckDetail } from '@/types/afterSales/qualityCheck';

// 组件Props
interface Props {
  visible: boolean;
  qualityCheckId: string;
}

// 组件Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 当前激活的标签页
const activeTab = ref('basic');

// 加载状态
const loading = ref(false);

// 质检详情数据
const qualityCheckDetail = ref<QualityCheckDetail | null>(null);

// 分组的质检项目
const groupedCheckItems = computed(() => {
  if (!qualityCheckDetail.value?.checkItems) return [];
  
  const groups = new Map();
  qualityCheckDetail.value.checkItems.forEach(item => {
    if (!groups.has(item.categoryCode)) {
      groups.set(item.categoryCode, {
        categoryCode: item.categoryCode,
        categoryName: item.categoryName,
        items: []
      });
    }
    groups.get(item.categoryCode).items.push(item);
  });
  
  return Array.from(groups.values()).sort((a, b) => a.categoryCode.localeCompare(b.categoryCode));
});

// 获取状态标签类型
const getStatusTagType = (status?: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  if (!status) return 'info';
  const statusMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    pending_check: 'info',
    checking: 'primary',
    pending_review: 'warning',
    passed: 'success',
    rework: 'danger'
  };
  return statusMap[status] || 'info';
};

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 监听质检ID变化，加载详情
watch(
  () => props.qualityCheckId,
  async (newId) => {
    if (newId && props.visible) {
      await loadQualityCheckDetail(newId);
    }
  },
  { immediate: true }
);

// 监听对话框显示状态
watch(
  () => props.visible,
  async (visible) => {
    if (visible && props.qualityCheckId) {
      await loadQualityCheckDetail(props.qualityCheckId);
    }
  }
);

// 加载质检详情
const loadQualityCheckDetail = async (qualityCheckId: string) => {
  try {
    loading.value = true;
    qualityCheckDetail.value = await getQualityCheckDetail(qualityCheckId);
  } catch (error) {
    console.error('加载质检详情失败:', error);
    qualityCheckDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 关闭处理
const handleClose = () => {
  activeTab.value = 'basic';
  qualityCheckDetail.value = null;
  emit('close');
};
</script>

<style scoped>
.detail-container {
  min-height: 400px;
}

.detail-section {
  padding: 20px;
}

.check-category {
  margin-bottom: 24px;
}

.category-title {
  margin-bottom: 16px;
  color: #303133;
  font-weight: 500;
  font-size: 16px;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.symptoms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-table .cell) {
  padding: 8px;
}
</style>
