<template>
  <el-card class="mb-20 search-card">
    <template #header>
      <span class="card-title">{{ t('qualityCheck.searchForm.title') }}</span>
    </template>
    
    <el-form :model="localSearchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.qualityCheckNo')">
            <el-input 
              v-model="localSearchParams.qualityCheckNo" 
              :placeholder="t('qualityCheck.searchForm.qualityCheckNoPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.workOrderNo')">
            <el-input 
              v-model="localSearchParams.workOrderNo" 
              :placeholder="t('qualityCheck.searchForm.workOrderNoPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.statusLabel')">
            <el-select 
              v-model="localSearchParams.status" 
              :placeholder="tc('pleaseSelect')" 
              multiple
              clearable
              style="width: 100%"
            >
              <el-option 
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.workOrderTypeLabel')">
            <el-select 
              v-model="localSearchParams.workOrderType" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" value="" />
              <el-option 
                v-for="option in workOrderTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.technicianName')">
            <el-input 
              v-model="localSearchParams.technicianName" 
              :placeholder="t('qualityCheck.searchForm.technicianNamePlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.plateNumber')">
            <el-input 
              v-model="localSearchParams.plateNumber" 
              :placeholder="t('qualityCheck.searchForm.plateNumberPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.serviceCustomerName')">
            <el-input 
              v-model="localSearchParams.serviceCustomerName" 
              :placeholder="t('qualityCheck.searchForm.serviceCustomerNamePlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.isClaimRelated')">
            <el-select 
              v-model="localSearchParams.isClaimRelated" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" :value="undefined" />
              <el-option :label="tc('yes')" :value="true" />
              <el-option :label="tc('no')" :value="false" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('qualityCheck.isOutsourceRelated')">
            <el-select 
              v-model="localSearchParams.isOutsourceRelated" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" :value="undefined" />
              <el-option :label="tc('yes')" :value="true" />
              <el-option :label="tc('no')" :value="false" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="18">
          <el-form-item :label="t('qualityCheck.createTime')">
            <el-date-picker
              v-model="createTimeRange"
              type="datetimerange"
              range-separator="-"
              :start-placeholder="tc('startTime')"
              :end-placeholder="tc('endTime')"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24" class="text-right">
          <el-button type="primary" :icon="Search" @click="handleSearch" :loading="loading">
            {{ tc('search') }}
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            {{ tc('reset') }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { Search, Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { QualityCheckSearchParams } from '@/types/afterSales/qualityCheck';

// 组件Props
interface Props {
  searchParams: QualityCheckSearchParams;
  loading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'search', params: QualityCheckSearchParams): void;
  (e: 'reset'): void;
  (e: 'update:searchParams', params: QualityCheckSearchParams): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 本地搜索参数
const localSearchParams = reactive<QualityCheckSearchParams>({ ...props.searchParams });

// 创建时间范围
const createTimeRange = ref<[string, string] | null>(null);

// 状态选项
const statusOptions = computed(() => [
  { label: t('qualityCheck.status.pending_check'), value: 'pending_check' },
  { label: t('qualityCheck.status.checking'), value: 'checking' },
  { label: t('qualityCheck.status.pending_review'), value: 'pending_review' },
  { label: t('qualityCheck.status.passed'), value: 'passed' },
  { label: t('qualityCheck.status.rework'), value: 'rework' }
]);

// 工单类型选项
const workOrderTypeOptions = computed(() => [
  { label: t('qualityCheck.workOrderType.maintenance'), value: 'maintenance' },
  { label: t('qualityCheck.workOrderType.repair'), value: 'repair' },
  { label: t('qualityCheck.workOrderType.insurance'), value: 'insurance' }
]);

// 监听创建时间范围变化
watch(createTimeRange, (newRange) => {
  if (newRange && newRange.length === 2) {
    localSearchParams.createTimeStart = newRange[0];
    localSearchParams.createTimeEnd = newRange[1];
  } else {
    localSearchParams.createTimeStart = '';
    localSearchParams.createTimeEnd = '';
  }
});

// 监听外部搜索参数变化
watch(
  () => props.searchParams,
  (newParams) => {
    Object.assign(localSearchParams, newParams);
    
    // 更新创建时间范围
    if (newParams.createTimeStart && newParams.createTimeEnd) {
      createTimeRange.value = [newParams.createTimeStart, newParams.createTimeEnd];
    } else {
      createTimeRange.value = null;
    }
  },
  { deep: true }
);

// 搜索处理
const handleSearch = () => {
  emit('search', { ...localSearchParams });
  emit('update:searchParams', { ...localSearchParams });
};

// 重置处理
const handleReset = () => {
  Object.assign(localSearchParams, {
    page: 1,
    pageSize: 20,
    qualityCheckNo: '',
    workOrderNo: '',
    status: [],
    workOrderType: undefined,
    technicianName: '',
    plateNumber: '',
    serviceCustomerName: '',
    createTimeStart: '',
    createTimeEnd: '',
    isClaimRelated: undefined,
    isOutsourceRelated: undefined
  });
  createTimeRange.value = null;
  emit('reset');
};
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: 500;
  color: #303133;
}

.search-form {
  margin-top: 10px;
}

.text-right {
  text-align: right;
  padding-top: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style>
