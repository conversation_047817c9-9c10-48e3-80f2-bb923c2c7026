<template>
  <el-card class="table-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">{{ t('qualityCheck.title') }}</span>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :icon="Download" 
            @click="handleExport"
            :loading="exportLoading"
          >
            {{ t('qualityCheck.actions.export') }}
          </el-button>
          <el-button 
            type="warning" 
            :icon="Check" 
            @click="handleBatchAudit"
            :disabled="selectedRows.length === 0"
          >
            {{ t('qualityCheck.actions.batchAudit') }}
          </el-button>
          <el-button 
            :icon="Refresh" 
            @click="handleRefresh"
            :loading="loading"
          >
            {{ tc('refresh') }}
          </el-button>
        </div>
      </div>
    </template>

    <el-table
      :data="data"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column 
        prop="qualityCheckNo" 
        :label="t('qualityCheck.qualityCheckNo')" 
        width="140"
        fixed="left"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="handleViewDetail(row)">
            {{ row.qualityCheckNo }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="workOrderNo" 
        :label="t('qualityCheck.workOrderNo')" 
        width="120"
      />
      
      <el-table-column 
        prop="status" 
        :label="t('qualityCheck.statusLabel')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="getStatusTagType(row.status)"
            size="small"
          >
            {{ t(`qualityCheck.status.${row.status}`) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="workOrderType" 
        :label="t('qualityCheck.workOrderTypeLabel')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="getWorkOrderTypeTagType(row.workOrderType)"
            size="small"
          >
            {{ t(`qualityCheck.workOrderType.${row.workOrderType}`) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="technicianName" 
        :label="t('qualityCheck.technicianName')" 
        width="100"
      />
      
      <el-table-column 
        prop="plateNumber" 
        :label="t('qualityCheck.plateNumber')" 
        width="120"
      />
      
      <el-table-column 
        prop="serviceCustomerName" 
        :label="t('qualityCheck.serviceCustomerName')" 
        width="120"
      />
      
      <el-table-column 
        prop="vehicleModel" 
        :label="t('qualityCheck.vehicleModel')" 
        width="100"
      />
      
      <el-table-column 
        prop="isClaimRelated" 
        :label="t('qualityCheck.isClaimRelated')" 
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="row.isClaimRelated ? 'warning' : 'info'"
            size="small"
          >
            {{ row.isClaimRelated ? tc('yes') : tc('no') }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="isOutsourceRelated" 
        :label="t('qualityCheck.isOutsourceRelated')" 
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="row.isOutsourceRelated ? 'warning' : 'info'"
            size="small"
          >
            {{ row.isOutsourceRelated ? tc('yes') : tc('no') }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="estimatedHours" 
        :label="t('qualityCheck.estimatedHours')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          {{ row.estimatedHours }}h
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="actualHours" 
        :label="t('qualityCheck.actualHours')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          {{ row.actualHours ? `${row.actualHours}h` : '-' }}
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="createTime" 
        :label="t('qualityCheck.createTime')" 
        width="160"
        align="center"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column 
        :label="t('qualityCheck.table.operations')" 
        width="200"
        fixed="right"
        align="center"
      >
        <template #default="{ row }">
          <div class="operation-buttons">
            <el-button 
              v-if="row.status === 'pending_check'"
              type="primary" 
              size="small" 
              @click="handleStart(row)"
            >
              {{ t('qualityCheck.actions.start') }}
            </el-button>
            
            <el-button 
              v-if="row.status === 'checking'"
              type="success" 
              size="small" 
              @click="handleSubmit(row)"
            >
              {{ t('qualityCheck.actions.submit') }}
            </el-button>
            
            <el-button 
              v-if="row.status === 'pending_review'"
              type="warning" 
              size="small" 
              @click="handleAudit(row)"
            >
              {{ t('qualityCheck.actions.audit') }}
            </el-button>
            
            <el-button 
              v-if="row.status === 'rework'"
              type="danger" 
              size="small" 
              @click="handleRework(row)"
            >
              {{ t('qualityCheck.actions.rework') }}
            </el-button>
            
            <el-button 
              type="info" 
              size="small" 
              @click="handleViewDetail(row)"
            >
              {{ t('qualityCheck.actions.detail') }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Download, Check, Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { QualityCheckListItem } from '@/types/afterSales/qualityCheck';

// 组件Props
interface Props {
  data: QualityCheckListItem[];
  loading?: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
  exportLoading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'start', row: QualityCheckListItem): void;
  (e: 'submit', row: QualityCheckListItem): void;
  (e: 'audit', row: QualityCheckListItem): void;
  (e: 'rework', row: QualityCheckListItem): void;
  (e: 'view-detail', row: QualityCheckListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'size-change', size: number): void;
  (e: 'selection-change', selection: QualityCheckListItem[]): void;
  (e: 'export'): void;
  (e: 'batch-audit', selection: QualityCheckListItem[]): void;
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  exportLoading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 选中的行
const selectedRows = ref<QualityCheckListItem[]>([]);

// 获取状态标签类型
const getStatusTagType = (status: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  const statusMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    pending_check: 'info',
    checking: 'primary',
    pending_review: 'warning',
    passed: 'success',
    rework: 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取工单类型标签类型
const getWorkOrderTypeTagType = (type: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  const typeMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    maintenance: 'success',
    repair: 'warning',
    insurance: 'danger'
  };
  return typeMap[type] || 'info';
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 事件处理
const handleStart = (row: QualityCheckListItem) => {
  emit('start', row);
};

const handleSubmit = (row: QualityCheckListItem) => {
  emit('submit', row);
};

const handleAudit = (row: QualityCheckListItem) => {
  emit('audit', row);
};

const handleRework = (row: QualityCheckListItem) => {
  emit('rework', row);
};

const handleViewDetail = (row: QualityCheckListItem) => {
  emit('view-detail', row);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleSelectionChange = (selection: QualityCheckListItem[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

const handleExport = () => {
  emit('export');
};

const handleBatchAudit = () => {
  emit('batch-audit', selectedRows.value);
};

const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped>
.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 500;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table .cell) {
  padding: 0 8px;
}

:deep(.el-button + .el-button) {
  margin-left: 5px;
}
</style>
