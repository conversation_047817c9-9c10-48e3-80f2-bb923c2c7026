<script setup lang="ts">
import { El<PERSON><PERSON>, ElTable, ElTableColumn, ElButton, ElPagination, ElIcon } from 'element-plus';
import { Plus, Edit, Calendar } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { QuotaConfig } from '@/types/afterSales/quota.d.ts';

interface Props {
  quotaList: QuotaConfig[];
  total: number;
  currentPage: number;
  pageSize: number;
  loading: boolean;
}

interface Emits {
  (e: 'add-quota'): void;
  (e: 'edit-quota', configDate: string): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.quota');

const handleAddQuota = () => {
  emit('add-quota');
};

const handleEditQuota = (configDate: string) => {
  emit('edit-quota', configDate);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (size: number) => {
  emit('page-size-change', size);
};
</script>

<template>
  <el-card class="quota-list-card">
    <div class="card-header">
      <h2>{{ t('configuredListTitle') }}</h2>
      <el-button type="primary" :icon="Plus" @click="handleAddQuota">
        {{ t('addNewQuota') }}
      </el-button>
    </div>

    <el-table :data="quotaList" v-loading="loading" style="width: 100%" class="quota-table">
      <el-table-column type="index" :label="tc('index')" width="80" />
      <el-table-column prop="configDate" :label="t('table.configDate')" min-width="150" />
      <el-table-column prop="timeSlotCount" :label="t('table.timeSlotCount')" min-width="120">
        <template #default="{ row }">
          {{ row.timeSlotCount }}{{ t('summary.timeSlotsUnit') }}
        </template>
      </el-table-column>
      <el-table-column prop="totalQuota" :label="t('table.totalQuota')" min-width="100" />
      <el-table-column prop="bookedQuantity" :label="t('table.bookedQuantity')" min-width="120" />
      <el-table-column prop="lastUpdateTime" :label="t('table.lastUpdateTime')" min-width="180" />
      <el-table-column :label="tc('operations')" width="100" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="!row.isExpired"
            type="primary"
            :icon="Edit"
            link
            @click="handleEditQuota(row.configDate)"
          >
            {{ tc('edit') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态 -->
    <div v-if="!loading && quotaList.length === 0" class="empty-state">
      <el-icon class="empty-icon"><Calendar /></el-icon>
      <p>{{ t('emptyState') }}</p>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
        :page-sizes="[10, 20, 50]"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.quota-list-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    h2 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }

  .quota-table {
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 50px 0;
  color: #909399;
  .empty-icon {
    font-size: 60px;
    margin-bottom: 15px;
  }
  p {
    margin: 0;
    font-size: 16px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
