<template>
  <div class="settlement-management-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ t('settlement.title') }}</h1>
      <p class="page-subtitle">{{ t('settlement.subtitle') }}</p>
    </div>

    <!-- 统计卡片 -->
    <SettlementStatsCard
      :statistics="statistics"
      :loading="statisticsLoading"
      @refresh="loadStatistics"
    />

    <!-- 搜索表单 -->
    <SettlementSearchForm
      v-model:searchParams="searchParams"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 结算表格 -->
    <SettlementTable
      :data="settlementList"
      :loading="loading"
      :total="total"
      :current-page="searchParams.page"
      :page-size="searchParams.pageSize"
      :export-loading="exportLoading"
      @push="handlePush"
      @payment="handlePayment"
      @complete="handleComplete"
      @view-detail="handleViewDetail"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
      @selection-change="handleSelectionChange"
      @export="handleExport"
      @batch-push="handleBatchPush"
      @refresh="handleRefresh"
    />

    <!-- 结算详情对话框 -->
    <SettlementDetailDialog
      v-model:visible="detailDialogVisible"
      :settlement-id="selectedSettlementId"
      @close="handleDetailClose"
    />

    <!-- 收退款管理对话框 -->
    <PaymentManagementDialog
      v-model:visible="paymentDialogVisible"
      :settlement="selectedSettlement"
      :loading="paymentLoading"
      @confirm="handlePaymentConfirm"
      @close="handlePaymentClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';

// 组件导入
import SettlementStatsCard from './components/SettlementStatsCard.vue';
import SettlementSearchForm from './components/SettlementSearchForm.vue';
import SettlementTable from './components/SettlementTable.vue';
import SettlementDetailDialog from './components/SettlementDetailDialog.vue';
import PaymentManagementDialog from './components/PaymentManagementDialog.vue';

// API导入
import {
  getSettlementList,
  pushSettlement,
  processPayment,
  completeSettlement,
  getSettlementStatistics,
  exportSettlementData,
  batchSettlementOperation
} from '@/api/modules/afterSales/settlement';

// 类型导入
import type {
  SettlementListItem,
  SettlementSearchParams,
  PaymentForm,
  SettlementStatistics
} from '@/types/afterSales/settlement';

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 响应式数据
const loading = ref(false);
const statisticsLoading = ref(false);
const paymentLoading = ref(false);
const exportLoading = ref(false);

// 搜索参数
const searchParams = reactive<SettlementSearchParams>({
  page: 1,
  pageSize: 20,
  settlementNo: '',
  workOrderNo: '',
  settlementStatus: undefined,
  paymentStatus: undefined,
  customerName: '',
  vehiclePlate: '',
  technician: '',
  serviceAdvisor: '',
  createTimeStart: '',
  createTimeEnd: '',
  amountMin: undefined,
  amountMax: undefined
});

// 数据列表
const settlementList = ref<SettlementListItem[]>([]);
const statistics = ref<SettlementStatistics | null>(null);
const total = ref(0);
const selectedRows = ref<SettlementListItem[]>([]);

// 对话框状态
const detailDialogVisible = ref(false);
const paymentDialogVisible = ref(false);

// 选中的数据
const selectedSettlement = ref<SettlementListItem | null>(null);
const selectedSettlementId = ref('');

// 页面初始化
onMounted(async () => {
  await Promise.all([
    loadSettlementList(),
    loadStatistics()
  ]);
});

// 加载结算单列表
const loadSettlementList = async () => {
  try {
    loading.value = true;
    const response = await getSettlementList(searchParams);
    settlementList.value = response.data;
    total.value = response.total;
  } catch (error) {
    console.error('加载结算单列表失败:', error);
    ElMessage.error(t('settlement.messages.loadListFailed'));
  } finally {
    loading.value = false;
  }
};

// 加载统计数据
const loadStatistics = async () => {
  try {
    statisticsLoading.value = true;
    statistics.value = await getSettlementStatistics();
  } catch (error) {
    console.error('加载统计数据失败:', error);
  } finally {
    statisticsLoading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  searchParams.page = 1;
  loadSettlementList();
};

// 重置处理
const handleReset = () => {
  Object.assign(searchParams, {
    page: 1,
    pageSize: 20,
    settlementNo: '',
    workOrderNo: '',
    settlementStatus: undefined,
    paymentStatus: undefined,
    customerName: '',
    vehiclePlate: '',
    technician: '',
    serviceAdvisor: '',
    createTimeStart: '',
    createTimeEnd: '',
    amountMin: undefined,
    amountMax: undefined
  });
  loadSettlementList();
};

// 分页处理
const handlePageChange = (page: number) => {
  searchParams.page = page;
  loadSettlementList();
};

const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  loadSettlementList();
};

// 选择变化处理
const handleSelectionChange = (selection: SettlementListItem[]) => {
  selectedRows.value = selection;
};

// 刷新处理
const handleRefresh = () => {
  Promise.all([
    loadSettlementList(),
    loadStatistics()
  ]);
};

// 推送结算处理
const handlePush = async (row: SettlementListItem) => {
  try {
    await ElMessageBox.confirm(
      t('settlement.messages.confirmOperation'),
      t('settlement.actions.push'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );
    
    await pushSettlement(row.id);
    ElMessage.success(t('settlement.messages.pushSuccess'));
    loadSettlementList();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('推送结算失败:', error);
      ElMessage.error(t('settlement.messages.pushFailed'));
    }
  }
};

// 收退款处理
const handlePayment = (row: SettlementListItem) => {
  selectedSettlement.value = row;
  paymentDialogVisible.value = true;
};

// 完成结算处理
const handleComplete = async (row: SettlementListItem) => {
  try {
    await ElMessageBox.confirm(
      t('settlement.messages.confirmOperation'),
      t('settlement.actions.complete'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );
    
    await completeSettlement(row.id);
    ElMessage.success(t('settlement.messages.completeSuccess'));
    loadSettlementList();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成结算失败:', error);
      ElMessage.error(t('settlement.messages.completeFailed'));
    }
  }
};

// 查看详情处理
const handleViewDetail = (row: SettlementListItem) => {
  selectedSettlementId.value = row.id;
  detailDialogVisible.value = true;
};

// 导出处理
const handleExport = async () => {
  try {
    exportLoading.value = true;
    const blob = await exportSettlementData({
      ...searchParams,
      exportType: 'all',
      exportFormat: 'excel'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `settlement_data_${new Date().toISOString().slice(0, 10)}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success(t('settlement.messages.exportSuccess'));
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('settlement.messages.exportFailed'));
  } finally {
    exportLoading.value = false;
  }
};

// 批量推送处理
const handleBatchPush = async (selection: SettlementListItem[]) => {
  try {
    await ElMessageBox.confirm(
      t('settlement.messages.confirmOperation'),
      t('settlement.actions.batchPush'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );

    await batchSettlementOperation({
      settlementIds: selection.map(item => item.id),
      operation: 'push'
    });

    ElMessage.success(t('settlement.messages.batchOperationSuccess'));
    loadSettlementList();
    loadStatistics();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量推送失败:', error);
      ElMessage.error(t('settlement.messages.batchOperationFailed'));
    }
  }
};

// 收退款确认处理
const handlePaymentConfirm = async (data: PaymentForm) => {
  try {
    paymentLoading.value = true;
    await processPayment(data);
    ElMessage.success(t('settlement.messages.paymentSuccess'));
    paymentDialogVisible.value = false;
    loadSettlementList();
    loadStatistics();
  } catch (error) {
    console.error('收退款处理失败:', error);
    ElMessage.error(t('settlement.messages.paymentFailed'));
  } finally {
    paymentLoading.value = false;
  }
};

// 收退款关闭处理
const handlePaymentClose = () => {
  paymentDialogVisible.value = false;
  selectedSettlement.value = null;
};

// 详情关闭处理
const handleDetailClose = () => {
  detailDialogVisible.value = false;
  selectedSettlementId.value = '';
};
</script>

<style scoped>
.settlement-management-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-subtitle {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .settlement-management-container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .settlement-management-container {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }
}
</style>
