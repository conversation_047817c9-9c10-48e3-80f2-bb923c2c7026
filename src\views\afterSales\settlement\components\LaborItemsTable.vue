<template>
  <div class="labor-items-table">
    <el-table :data="laborItems" border style="width: 100%">
      <el-table-column 
        prop="laborCode" 
        :label="t('settlement.laborItem.laborCode')" 
        width="120"
      />
      <el-table-column 
        prop="laborName" 
        :label="t('settlement.laborItem.laborName')" 
        width="150"
      />
      <el-table-column 
        prop="standardHours" 
        :label="t('settlement.laborItem.standardHours')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          {{ row.standardHours }}h
        </template>
      </el-table-column>
      <el-table-column 
        prop="actualHours" 
        :label="t('settlement.laborItem.actualHours')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          {{ row.actualHours }}h
        </template>
      </el-table-column>
      <el-table-column 
        prop="unitPrice" 
        :label="t('settlement.laborItem.unitPrice')" 
        width="100"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.unitPrice) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="totalAmount" 
        :label="t('settlement.laborItem.totalAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.totalAmount) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="receivableAmount" 
        :label="t('settlement.laborItem.receivableAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.receivableAmount) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="warrantyAmount" 
        :label="t('settlement.laborItem.warrantyAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.warrantyAmount) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="discountAmount" 
        :label="t('settlement.laborItem.discountAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.discountAmount) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="remarks" 
        :label="t('settlement.laborItem.remarks')" 
        min-width="150"
      >
        <template #default="{ row }">
          {{ row.remarks || '-' }}
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 汇总信息 -->
    <div class="summary-section" v-if="laborItems.length > 0">
      <el-row :gutter="20" class="summary-row">
        <el-col :span="6">
          <div class="summary-item">
            <span class="summary-label">工时总数:</span>
            <span class="summary-value">{{ totalHours }}h</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <span class="summary-label">总金额:</span>
            <span class="summary-value">¥{{ formatAmount(totalAmount) }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <span class="summary-label">应收金额:</span>
            <span class="summary-value">¥{{ formatAmount(totalReceivableAmount) }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <span class="summary-label">质保金额:</span>
            <span class="summary-value">¥{{ formatAmount(totalWarrantyAmount) }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { SettlementLaborItem } from '@/types/afterSales/settlement';

// 组件Props
interface Props {
  laborItems: SettlementLaborItem[];
}

const props = defineProps<Props>();

// 国际化
const { t } = useModuleI18n('afterSales');

// 格式化金额
const formatAmount = (amount: number) => {
  return amount.toFixed(2);
};

// 计算汇总数据
const totalHours = computed(() => {
  return props.laborItems.reduce((sum, item) => sum + item.actualHours, 0).toFixed(1);
});

const totalAmount = computed(() => {
  return props.laborItems.reduce((sum, item) => sum + item.totalAmount, 0);
});

const totalReceivableAmount = computed(() => {
  return props.laborItems.reduce((sum, item) => sum + item.receivableAmount, 0);
});

const totalWarrantyAmount = computed(() => {
  return props.laborItems.reduce((sum, item) => sum + item.warrantyAmount, 0);
});
</script>

<style scoped>
.labor-items-table {
  width: 100%;
}

.summary-section {
  margin-top: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.summary-row {
  margin: 0;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.summary-label {
  font-weight: 500;
  color: #606266;
}

.summary-value {
  font-weight: 600;
  color: #303133;
}

:deep(.el-table .cell) {
  padding: 8px;
}
</style>
