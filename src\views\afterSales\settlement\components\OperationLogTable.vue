<template>
  <div class="operation-log-table">
    <el-table :data="operationLogs" border style="width: 100%">
      <el-table-column 
        prop="operationType" 
        :label="t('operationType')" 
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="getOperationTagType(row.operationType)"
            size="small"
          >
            {{ row.operationType }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="operator" 
        :label="t('operator')" 
        width="100"
        align="center"
      />
      
      <el-table-column 
        prop="operationTime" 
        :label="t('operationTime')" 
        width="160"
        align="center"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.operationTime) }}
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="operationContent" 
        :label="t('operationContent')" 
        min-width="200"
      />
      
      <el-table-column 
        prop="remarks" 
        :label="t('remarks')" 
        min-width="150"
      >
        <template #default="{ row }">
          {{ row.remarks || '-' }}
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 空状态 -->
    <div v-if="operationLogs.length === 0" class="empty-state">
      <el-empty :description="tc('noData')" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { OperationLog } from '@/types/afterSales/settlement';

// 组件Props
interface Props {
  operationLogs: OperationLog[];
}

const props = defineProps<Props>();

// 国际化
const { t,tc } = useModuleI18n('afterSales.settlement');

// 获取操作类型标签类型
const getOperationTagType = (operationType: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  const typeMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    '创建结算单': 'info',
    '推送结算': 'primary',
    '收款': 'success',
    '退款': 'warning',
    '完成结算': 'success',
    '取消结算': 'danger',
    '修改结算': 'warning'
  };
  return typeMap[operationType] || 'info';
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};
</script>

<style scoped>
.operation-log-table {
  width: 100%;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

:deep(.el-table .cell) {
  padding: 8px;
}

:deep(.el-empty) {
  padding: 20px 0;
}
</style>
