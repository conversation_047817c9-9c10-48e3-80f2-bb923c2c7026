<template>
  <div class="part-items-table">
    <el-table :data="partItems" border style="width: 100%">
      <el-table-column 
        prop="partCode" 
        :label="t('settlement.partItem.partCode')" 
        width="120"
      />
      <el-table-column 
        prop="partName" 
        :label="t('settlement.partItem.partName')" 
        width="150"
      />
      <el-table-column 
        prop="specification" 
        :label="t('settlement.partItem.specification')" 
        width="150"
      />
      <el-table-column 
        prop="unit" 
        :label="t('settlement.partItem.unit')" 
        width="80"
        align="center"
      />
      <el-table-column 
        prop="quantity" 
        :label="t('settlement.partItem.quantity')" 
        width="80"
        align="center"
      />
      <el-table-column 
        prop="unitPrice" 
        :label="t('settlement.partItem.unitPrice')" 
        width="100"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.unitPrice) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="totalAmount" 
        :label="t('settlement.partItem.totalAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.totalAmount) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="receivableAmount" 
        :label="t('settlement.partItem.receivableAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.receivableAmount) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="warrantyAmount" 
        :label="t('settlement.partItem.warrantyAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.warrantyAmount) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="discountAmount" 
        :label="t('settlement.partItem.discountAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.discountAmount) }}
        </template>
      </el-table-column>
      <el-table-column 
        prop="remarks" 
        :label="t('settlement.partItem.remarks')" 
        min-width="150"
      >
        <template #default="{ row }">
          {{ row.remarks || '-' }}
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 汇总信息 -->
    <div class="summary-section" v-if="partItems.length > 0">
      <el-row :gutter="20" class="summary-row">
        <el-col :span="6">
          <div class="summary-item">
            <span class="summary-label">零件总数:</span>
            <span class="summary-value">{{ totalQuantity }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <span class="summary-label">总金额:</span>
            <span class="summary-value">¥{{ formatAmount(totalAmount) }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <span class="summary-label">应收金额:</span>
            <span class="summary-value">¥{{ formatAmount(totalReceivableAmount) }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <span class="summary-label">质保金额:</span>
            <span class="summary-value">¥{{ formatAmount(totalWarrantyAmount) }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { SettlementPartItem } from '@/types/afterSales/settlement';

// 组件Props
interface Props {
  partItems: SettlementPartItem[];
}

const props = defineProps<Props>();

// 国际化
const { t } = useModuleI18n('afterSales');

// 格式化金额
const formatAmount = (amount: number) => {
  return amount.toFixed(2);
};

// 计算汇总数据
const totalQuantity = computed(() => {
  return props.partItems.reduce((sum, item) => sum + item.quantity, 0);
});

const totalAmount = computed(() => {
  return props.partItems.reduce((sum, item) => sum + item.totalAmount, 0);
});

const totalReceivableAmount = computed(() => {
  return props.partItems.reduce((sum, item) => sum + item.receivableAmount, 0);
});

const totalWarrantyAmount = computed(() => {
  return props.partItems.reduce((sum, item) => sum + item.warrantyAmount, 0);
});
</script>

<style scoped>
.part-items-table {
  width: 100%;
}

.summary-section {
  margin-top: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.summary-row {
  margin: 0;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.summary-label {
  font-weight: 500;
  color: #606266;
}

.summary-value {
  font-weight: 600;
  color: #303133;
}

:deep(.el-table .cell) {
  padding: 8px;
}
</style>
