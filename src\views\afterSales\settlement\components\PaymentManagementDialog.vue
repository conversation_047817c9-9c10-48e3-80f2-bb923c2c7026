<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('settlement.dialog.paymentTitle')"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
    >
      <el-form-item :label="t('settlement.settlementNo')" prop="settlementNo">
        <el-input v-model="formData.settlementNo" disabled />
      </el-form-item>
      
      <el-form-item :label="t('settlement.customerName')" prop="customerName">
        <el-input v-model="formData.customerName" disabled />
      </el-form-item>
      
      <el-form-item :label="t('settlement.totalAmount')" prop="totalAmount">
        <el-input v-model="formData.totalAmount" disabled>
          <template #prefix>¥</template>
        </el-input>
      </el-form-item>
      
      <el-form-item :label="t('settlement.paidAmount')" prop="paidAmount">
        <el-input v-model="formData.paidAmount" disabled>
          <template #prefix>¥</template>
        </el-input>
      </el-form-item>
      
      <el-form-item :label="t('settlement.payableAmount')" prop="payableAmount">
        <el-input v-model="formData.payableAmount" disabled>
          <template #prefix>¥</template>
        </el-input>
      </el-form-item>
      
      <el-divider />
      
      <el-form-item :label="t('settlement.paymentRecord.businessType')" prop="businessType">
        <el-radio-group v-model="formData.businessType">
          <el-radio label="收款">
            <el-tag type="success" size="small">收款</el-tag>
          </el-radio>
          <el-radio label="退款">
            <el-tag type="warning" size="small">退款</el-tag>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item :label="t('settlement.paymentRecord.paymentMethod')" prop="paymentMethod">
        <el-select
          v-model="formData.paymentMethod"
          :placeholder="t('settlement.dialog.selectPaymentMethod')"
          style="width: 100%"
        >
          <el-option 
            v-for="option in paymentMethodOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item :label="t('settlement.paymentRecord.amount')" prop="amount">
        <el-input-number
          v-model="formData.amount"
          :min="0.01"
          :max="maxAmount"
          :precision="2"
          style="width: 100%"
          :placeholder="t('settlement.dialog.inputAmount')"
        />
      </el-form-item>
      
      <el-form-item :label="t('settlement.paymentRecord.paymentType')" prop="paymentType">
        <el-select
          v-model="formData.paymentType"
          :placeholder="tc('pleaseSelect')"
          style="width: 100%"
        >
          <el-option label="定金" value="定金" />
          <el-option label="尾款" value="尾款" />
          <el-option label="全款" value="全款" />
          <el-option label="退款" value="退款" />
        </el-select>
      </el-form-item>
      
      <el-form-item :label="t('settlement.paymentRecord.remarks')" prop="remarks">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="3"
          :placeholder="t('settlement.dialog.inputRemarks')"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ tc('confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { SettlementListItem, PaymentForm } from '@/types/afterSales/settlement';

// 组件Props
interface Props {
  visible: boolean;
  settlement: SettlementListItem | null;
  loading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm', data: PaymentForm): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 表单引用
const formRef = ref<FormInstance>();

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单数据
const formData = reactive<{
  settlementNo: string;
  customerName: string;
  totalAmount: string;
  paidAmount: string;
  payableAmount: string;
  settlementId: string;
  businessType: '收款' | '退款';
  paymentMethod: string;
  amount?: number;
  paymentType: string;
  remarks?: string;
}>({
  settlementNo: '',
  customerName: '',
  totalAmount: '',
  paidAmount: '',
  payableAmount: '',
  settlementId: '',
  businessType: '收款',
  paymentMethod: '',
  amount: undefined,
  paymentType: '',
  remarks: ''
});

// 支付方式选项
const paymentMethodOptions = computed(() => [
  { label: t('settlement.paymentMethod.cash'), value: 'cash' },
  { label: t('settlement.paymentMethod.pos'), value: 'pos' },
  { label: t('settlement.paymentMethod.wechat'), value: 'wechat' },
  { label: t('settlement.paymentMethod.alipay'), value: 'alipay' },
  { label: t('settlement.paymentMethod.bank_transfer'), value: 'bank_transfer' }
]);

// 最大金额限制
const maxAmount = computed(() => {
  if (formData.businessType === '收款') {
    return parseFloat(formData.payableAmount) || 0;
  } else {
    return parseFloat(formData.paidAmount) || 0;
  }
});

// 表单验证规则
const formRules: FormRules = {
  businessType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: t('settlement.dialog.selectPaymentMethod'), trigger: 'change' }
  ],
  amount: [
    { required: true, message: t('settlement.dialog.inputAmount'), trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value || value <= 0) {
          callback(new Error('金额必须大于0'));
        } else if (value > maxAmount.value) {
          callback(new Error(`金额不能超过${maxAmount.value}`));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ],
  paymentType: [
    { required: true, message: '请选择款项类型', trigger: 'change' }
  ]
};

// 监听结算单变化
watch(
  () => props.settlement,
  (newSettlement) => {
    if (newSettlement) {
      formData.settlementNo = newSettlement.settlementNo;
      formData.customerName = newSettlement.customerName;
      formData.totalAmount = newSettlement.totalAmount.toFixed(2);
      formData.paidAmount = newSettlement.paidAmount.toFixed(2);
      formData.payableAmount = newSettlement.payableAmount.toFixed(2);
      formData.settlementId = newSettlement.id;
      formData.businessType = '收款';
      formData.paymentMethod = '';
      formData.amount = undefined;
      formData.paymentType = '';
      formData.remarks = '';
    }
  },
  { immediate: true }
);

// 确认处理
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    const paymentData: PaymentForm = {
      settlementId: formData.settlementId,
      businessType: formData.businessType,
      paymentMethod: formData.paymentMethod as any,
      amount: formData.amount!,
      paymentType: formData.paymentType,
      remarks: formData.remarks
    };
    
    emit('confirm', paymentData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 关闭处理
const handleClose = () => {
  formRef.value?.resetFields();
  emit('close');
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-divider) {
  margin: 20px 0;
}
</style>
