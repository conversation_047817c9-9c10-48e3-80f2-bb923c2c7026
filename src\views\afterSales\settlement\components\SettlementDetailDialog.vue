<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('settlement.dialog.detailTitle')"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="detail-container">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane :label="t('settlement.tabs.basicInfo')" name="basic">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('settlement.settlementNo')">
                {{ settlementDetail?.settlementNo }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.workOrderNo')">
                {{ settlementDetail?.workOrderNo }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.settlementStatus')">
                <el-tag 
                  :type="getSettlementStatusTagType(settlementDetail?.settlementStatus)"
                  size="small"
                >
                  {{ settlementDetail?.settlementStatus ? t(`settlement.status.${settlementDetail.settlementStatus}`) : '' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.paymentStatusLabel')">
                <el-tag 
                  :type="getPaymentStatusTagType(settlementDetail?.paymentStatus)"
                  size="small"
                >
                  {{ settlementDetail?.paymentStatus ? t(`settlement.paymentStatus.${settlementDetail.paymentStatus}`) : '' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.serviceAdvisor')">
                {{ settlementDetail?.serviceAdvisor }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.createdAt')">
                {{ formatDateTime(settlementDetail?.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.totalAmount')">
                ¥{{ formatAmount(settlementDetail?.totalAmount) }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.paidAmount')">
                ¥{{ formatAmount(settlementDetail?.paidAmount) }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.payableAmount')">
                ¥{{ formatAmount(settlementDetail?.payableAmount) }}
              </el-descriptions-item>
              <el-descriptions-item :label="tc('remark')" :span="2">
                {{ settlementDetail?.remarks || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 客户信息 -->
        <el-tab-pane :label="t('settlement.tabs.customerInfo')" name="customer">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('settlement.customerName')">
                {{ settlementDetail?.customerName }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.customerPhone')">
                {{ settlementDetail?.customerPhone }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 车辆信息 -->
        <el-tab-pane :label="t('settlement.tabs.vehicleInfo')" name="vehicle">
          <div class="detail-section">
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="t('settlement.vehiclePlate')">
                {{ settlementDetail?.vehiclePlate }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.vehicleModel')">
                {{ settlementDetail?.vehicleModel }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.vehicleConfig')">
                {{ settlementDetail?.vehicleConfig }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.vehicleColor')">
                {{ settlementDetail?.vehicleColor }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.vin')">
                {{ settlementDetail?.vin }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.vehicleAge')">
                {{ settlementDetail?.vehicleAge }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('settlement.mileage')">
                {{ settlementDetail?.mileage }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 工时明细 -->
        <el-tab-pane :label="t('settlement.tabs.laborItems')" name="laborItems">
          <div class="detail-section">
            <LaborItemsTable :labor-items="settlementDetail?.laborItems || []" />
          </div>
        </el-tab-pane>

        <!-- 零件明细 -->
        <el-tab-pane :label="t('settlement.tabs.partItems')" name="partItems">
          <div class="detail-section">
            <PartItemsTable :part-items="settlementDetail?.partItems || []" />
          </div>
        </el-tab-pane>

        <!-- 收退款记录 -->
        <el-tab-pane :label="t('settlement.tabs.paymentRecords')" name="paymentRecords">
          <div class="detail-section">
            <el-table :data="paymentRecords" border style="width: 100%">
              <el-table-column 
                prop="paymentNo" 
                :label="t('settlement.paymentRecord.paymentNo')" 
                width="140"
              />
              <el-table-column 
                prop="businessType" 
                :label="t('settlement.paymentRecord.businessType')" 
                width="100"
                align="center"
              >
                <template #default="{ row }">
                  <el-tag 
                    :type="row.businessType === '收款' ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ row.businessType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column 
                prop="paymentMethod" 
                :label="t('settlement.paymentRecord.paymentMethod')" 
                width="100"
              >
                <template #default="{ row }">
                  {{ t(`settlement.paymentMethod.${row.paymentMethod}`) }}
                </template>
              </el-table-column>
              <el-table-column 
                prop="amount" 
                :label="t('settlement.paymentRecord.amount')" 
                width="120"
                align="right"
              >
                <template #default="{ row }">
                  ¥{{ formatAmount(row.amount) }}
                </template>
              </el-table-column>
              <el-table-column 
                prop="paymentType" 
                :label="t('settlement.paymentRecord.paymentType')" 
                width="100"
              />
              <el-table-column 
                prop="paymentTime" 
                :label="t('settlement.paymentRecord.paymentTime')" 
                width="160"
                align="center"
              >
                <template #default="{ row }">
                  {{ formatDateTime(row.paymentTime) }}
                </template>
              </el-table-column>
              <el-table-column 
                prop="remarks" 
                :label="t('settlement.paymentRecord.remarks')" 
                min-width="150"
              />
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 操作日志 -->
        <el-tab-pane :label="t('settlement.tabs.operationLogs')" name="operationLogs">
          <div class="detail-section">
            <OperationLogTable :operation-logs="settlementDetail?.operationLogs || []" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getSettlementDetail, getPaymentRecords } from '@/api/modules/afterSales/settlement';
import type { SettlementDetail, PaymentRecord } from '@/types/afterSales/settlement';
import LaborItemsTable from './LaborItemsTable.vue';
import PartItemsTable from './PartItemsTable.vue';
import OperationLogTable from './OperationLogTable.vue';

// 组件Props
interface Props {
  visible: boolean;
  settlementId: string;
}

// 组件Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 当前激活的标签页
const activeTab = ref('basic');

// 加载状态
const loading = ref(false);

// 结算详情数据
const settlementDetail = ref<SettlementDetail | null>(null);
const paymentRecords = ref<PaymentRecord[]>([]);

// 获取结算状态标签类型
const getSettlementStatusTagType = (status?: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  if (!status) return 'info';
  const statusMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    pre_settlement: 'info',
    pending_settlement: 'warning',
    completed: 'success',
    cancelled: 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取支付状态标签类型
const getPaymentStatusTagType = (status?: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  if (!status) return 'info';
  const statusMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    pending: 'danger',
    deposit_paid: 'warning',
    fully_paid: 'success',
    refunding: 'primary',
    refunded: 'info'
  };
  return statusMap[status] || 'info';
};

// 格式化金额
const formatAmount = (amount?: number) => {
  if (amount === undefined || amount === null) return '0.00';
  return amount.toFixed(2);
};

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 监听结算ID变化，加载详情
watch(
  () => props.settlementId,
  async (newId) => {
    if (newId && props.visible) {
      await loadSettlementDetail(newId);
    }
  },
  { immediate: true }
);

// 监听对话框显示状态
watch(
  () => props.visible,
  async (visible) => {
    if (visible && props.settlementId) {
      await loadSettlementDetail(props.settlementId);
    }
  }
);

// 加载结算详情
const loadSettlementDetail = async (settlementId: string) => {
  try {
    loading.value = true;
    const [detail, records] = await Promise.all([
      getSettlementDetail(settlementId),
      getPaymentRecords(settlementId)
    ]);
    settlementDetail.value = detail;
    paymentRecords.value = records;
  } catch (error) {
    console.error('加载结算详情失败:', error);
    settlementDetail.value = null;
    paymentRecords.value = [];
  } finally {
    loading.value = false;
  }
};

// 关闭处理
const handleClose = () => {
  activeTab.value = 'basic';
  settlementDetail.value = null;
  paymentRecords.value = [];
  emit('close');
};
</script>

<style scoped>
.detail-container {
  min-height: 400px;
}

.detail-section {
  padding: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-table .cell) {
  padding: 8px;
}
</style>
