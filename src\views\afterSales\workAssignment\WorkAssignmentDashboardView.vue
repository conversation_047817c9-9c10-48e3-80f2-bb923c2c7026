<!-- src/views/afterSales/workAssignment/WorkAssignmentDashboardView.vue -->
<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { ElRow, ElCol, ElCard, ElButton, ElDatePicker, ElSelect, ElOption } from 'element-plus';
import { Refresh, Setting, FullScreen } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { 
  getDashboardStatistics,
  getTechnicianSchedules,
  getWorkloadData,
  getStatusDistribution,
  getTechnicianPerformance,
  getOrderFlowData,
  getDepartmentWorkload
} from '@/api/modules/afterSales/workAssignmentDashboard';
import type { 
  DashboardStatistics,
  TechnicianSchedule,
  WorkloadData,
  StatusDistribution,
  DashboardFilters,
  RealTimeConfig
} from '@/types/afterSales/workAssignmentDashboard.d.ts';

// 导入子组件
import DashboardHeader from './components/DashboardHeader.vue';
import WorkloadChart from './components/WorkloadChart.vue';
import StatusDistributionChart from './components/StatusDistributionChart.vue';
import TechnicianScheduleTable from './components/TechnicianScheduleTable.vue';
import RealTimeUpdater from './components/RealTimeUpdater.vue';

const { t, tc } = useModuleI18n('afterSales');

// 数据状态
const statistics = ref<DashboardStatistics>({} as DashboardStatistics);
const technicianSchedules = ref<TechnicianSchedule[]>([]);
const workloadData = ref<WorkloadData[]>([]);
const statusDistribution = ref<StatusDistribution[]>([]);

// 加载状态
const loading = reactive({
  statistics: false,
  schedules: false,
  workload: false,
  status: false
});

// 筛选条件
const filters = reactive<DashboardFilters>({
  date: new Date().toISOString().slice(0, 10),
  department: '',
  technicianId: '',
  workOrderType: '',
  priority: ''
});

// 实时更新配置
const realTimeConfig = reactive<RealTimeConfig>({
  enabled: true,
  interval: 30,
  lastUpdateTime: '',
  autoRefresh: true
});

let refreshTimer: number | null = null;

// 获取看板统计数据
const fetchStatistics = async () => {
  loading.statistics = true;
  try {
    statistics.value = await getDashboardStatistics();
  } catch (error) {
    console.error('Failed to fetch dashboard statistics:', error);
  } finally {
    loading.statistics = false;
  }
};

// 获取技师排班数据
const fetchTechnicianSchedules = async () => {
  loading.schedules = true;
  try {
    technicianSchedules.value = await getTechnicianSchedules(filters);
  } catch (error) {
    console.error('Failed to fetch technician schedules:', error);
  } finally {
    loading.schedules = false;
  }
};

// 获取工作负荷数据
const fetchWorkloadData = async () => {
  loading.workload = true;
  try {
    workloadData.value = await getWorkloadData();
  } catch (error) {
    console.error('Failed to fetch workload data:', error);
  } finally {
    loading.workload = false;
  }
};

// 获取状态分布数据
const fetchStatusDistribution = async () => {
  loading.status = true;
  try {
    statusDistribution.value = await getStatusDistribution();
  } catch (error) {
    console.error('Failed to fetch status distribution:', error);
  } finally {
    loading.status = false;
  }
};

// 刷新所有数据
const refreshAllData = async () => {
  await Promise.all([
    fetchStatistics(),
    fetchTechnicianSchedules(),
    fetchWorkloadData(),
    fetchStatusDistribution()
  ]);
  
  realTimeConfig.lastUpdateTime = new Date().toLocaleTimeString();
};

// 处理筛选变更
const handleFilterChange = () => {
  fetchTechnicianSchedules();
};

// 启动实时更新
const startRealTimeUpdate = () => {
  if (realTimeConfig.enabled && realTimeConfig.autoRefresh) {
    refreshTimer = setInterval(() => {
      refreshAllData();
    }, realTimeConfig.interval * 1000);
  }
};

// 停止实时更新
const stopRealTimeUpdate = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 处理实时更新配置变更
const handleRealTimeConfigChange = (config: RealTimeConfig) => {
  Object.assign(realTimeConfig, config);
  
  stopRealTimeUpdate();
  if (config.enabled && config.autoRefresh) {
    startRealTimeUpdate();
  }
};

// 组件挂载时获取数据
onMounted(() => {
  refreshAllData();
  startRealTimeUpdate();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopRealTimeUpdate();
});
</script>

<template>
  <div class="dashboard-container">
    <div class="dashboard-header-section">
      <h1 class="page-title">{{ t('workAssignmentDashboard.title') }}</h1>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" :icon="Refresh" @click="refreshAllData">
          {{ t('workAssignmentDashboard.common.refresh') }}
        </el-button>
        <el-button :icon="Setting">
          {{ t('workAssignmentDashboard.common.settings') }}
        </el-button>
        <el-button :icon="FullScreen">
          {{ t('workAssignmentDashboard.common.fullscreen') }}
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card mb-20">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-date-picker
            v-model="filters.date"
            type="date"
            :placeholder="t('workAssignmentDashboard.filters.date')"
            value-format="YYYY-MM-DD"
            @change="handleFilterChange"
          />
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="filters.department"
            :placeholder="t('workAssignmentDashboard.filters.department')"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="维修部" value="维修部" />
            <el-option label="保养部" value="保养部" />
            <el-option label="检测部" value="检测部" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="filters.workOrderType"
            :placeholder="t('workAssignmentDashboard.filters.workOrderType')"
            clearable
            @change="handleFilterChange"
          >
            <el-option :label="t('workAssignmentDashboard.workOrderType.maintenance')" value="maintenance" />
            <el-option :label="t('workAssignmentDashboard.workOrderType.repair')" value="repair" />
            <el-option :label="t('workAssignmentDashboard.workOrderType.inspection')" value="inspection" />
            <el-option :label="t('workAssignmentDashboard.workOrderType.insurance')" value="insurance" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="filters.priority"
            :placeholder="t('workAssignmentDashboard.filters.priority')"
            clearable
            @change="handleFilterChange"
          >
            <el-option :label="t('workAssignmentDashboard.priority.low')" value="low" />
            <el-option :label="t('workAssignmentDashboard.priority.normal')" value="normal" />
            <el-option :label="t('workAssignmentDashboard.priority.high')" value="high" />
            <el-option :label="t('workAssignmentDashboard.priority.urgent')" value="urgent" />
          </el-select>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计卡片 -->
    <DashboardHeader 
      :statistics="statistics" 
      :loading="loading.statistics" 
    />

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <WorkloadChart 
          :data="workloadData" 
          :loading="loading.workload" 
        />
      </el-col>
      <el-col :span="12">
        <StatusDistributionChart 
          :data="statusDistribution" 
          :loading="loading.status" 
        />
      </el-col>
    </el-row>

    <!-- 技师排班表格 -->
    <TechnicianScheduleTable 
      :schedules="technicianSchedules" 
      :loading="loading.schedules" 
    />

    <!-- 实时更新组件 -->
    <RealTimeUpdater 
      :config="realTimeConfig"
      @config-change="handleRealTimeConfigChange"
      @refresh="refreshAllData"
    />
  </div>
</template>

<style scoped lang="scss">
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .page-title {
    margin: 0;
    color: #303133;
  }
  
  .action-buttons {
    .el-button {
      margin-left: 10px;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
}

.charts-section {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
