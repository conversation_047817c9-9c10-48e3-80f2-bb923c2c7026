<!-- src/views/afterSales/workAssignment/components/AssignmentDialog.vue -->
<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElSelect, ElOption, ElDatePicker, ElInputNumber, ElInput, ElButton, ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderListItem, TechnicianInfo, AssignWorkOrderRequest } from '@/types/afterSales/workAssignment.d.ts';

interface Props {
  visible: boolean;
  workOrder: WorkOrderListItem | null;
  technicianList: TechnicianInfo[];
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: AssignWorkOrderRequest): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

const formRef = ref();
const formData = reactive<AssignWorkOrderRequest>({
  workOrderId: '',
  technicianId: '',
  estimatedStartTime: '',
  estimatedDuration: 0,
  notes: ''
});

const rules = {
  technicianId: [
    { required: true, message: t('workAssignment.messages.selectTechnicianFirst'), trigger: 'change' }
  ],
  estimatedStartTime: [
    { required: true, message: '请选择预计开始时间', trigger: 'change' }
  ]
};

// 监听工单变化，更新表单数据
watch(() => props.workOrder, (newWorkOrder) => {
  if (newWorkOrder) {
    formData.workOrderId = newWorkOrder.workOrderId;
    formData.estimatedDuration = newWorkOrder.estimatedDuration;
    formData.technicianId = '';
    formData.estimatedStartTime = '';
    formData.notes = '';
  }
}, { immediate: true });

// 监听对话框显示状态
watch(() => props.visible, (newVisible) => {
  if (!newVisible) {
    // 重置表单
    formRef.value?.resetFields();
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = async () => {
  try {
    await formRef.value?.validate();
    emit('confirm', { ...formData });
  } catch (error) {
    ElMessage.error('请完善表单信息');
  }
};

// 获取技师状态显示文本
const getTechnicianStatusText = (status: string) => {
  return t(`workAssignment.technicianStatus.${status}`);
};

// 获取技师状态标签类型
const getTechnicianStatusType = (status: string) => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    'available': 'success',
    'busy': 'warning',
    'offline': 'danger'
  };
  return statusMap[status] || 'info';
};

// 过滤可用技师
const availableTechnicians = computed(() => {
  return props.technicianList.filter(tech => tech.isActive);
});
</script>

<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleClose"
    :title="t('workAssignment.dialog.assignTitle')"
    width="600px"
    :close-on-click-modal="false"
  >
    <div v-if="workOrder" class="work-order-info">
      <h4>工单信息</h4>
      <p><strong>{{ t('workAssignment.table.workOrderNo') }}:</strong> {{ workOrder.workOrderNo }}</p>
      <p><strong>{{ t('workAssignment.table.customerName') }}:</strong> {{ workOrder.customerName }}</p>
      <p><strong>{{ t('workAssignment.table.vehicleInfo') }}:</strong> {{ workOrder.licensePlate }} {{ workOrder.vehicleModel }}</p>
      <p><strong>{{ t('workAssignment.table.workOrderType') }}:</strong> {{ t(`workAssignment.workOrderType.${workOrder.workOrderType}`) }}</p>
      <p><strong>{{ t('workAssignment.table.priority') }}:</strong> {{ t(`workAssignment.priority.${workOrder.priority}`) }}</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="assignment-form"
    >
      <el-form-item
        :label="t('workAssignment.dialog.selectTechnician')"
        prop="technicianId"
      >
        <el-select
          v-model="formData.technicianId"
          :placeholder="t('workAssignment.technician.selectTechnician')"
          style="width: 100%"
        >
          <el-option
            v-for="technician in availableTechnicians"
            :key="technician.technicianId"
            :label="technician.technicianName"
            :value="technician.technicianId"
            :disabled="technician.status === 'offline'"
          >
            <div class="technician-option">
              <div class="technician-info">
                <span class="technician-name">{{ technician.technicianName }}</span>
                <el-tag 
                  :type="getTechnicianStatusType(technician.status)" 
                  size="small"
                  class="status-tag"
                >
                  {{ getTechnicianStatusText(technician.status) }}
                </el-tag>
              </div>
              <div class="technician-details">
                <span class="skill-level">技能等级: {{ technician.skillLevel }}</span>
                <span class="workload">负荷: {{ technician.currentWorkload }}%</span>
              </div>
              <div class="specialties">
                专长: {{ technician.specialties.join(', ') }}
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        :label="t('workAssignment.dialog.estimatedStartTime')"
        prop="estimatedStartTime"
      >
        <el-date-picker
          v-model="formData.estimatedStartTime"
          type="datetime"
          placeholder="选择预计开始时间"
          value-format="YYYY-MM-DD HH:mm"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item :label="t('workAssignment.dialog.estimatedDuration')">
        <el-input-number
          v-model="formData.estimatedDuration"
          :min="30"
          :max="480"
          :step="30"
          style="width: 100%"
        />
        <span class="duration-unit">分钟</span>
      </el-form-item>

      <el-form-item :label="t('workAssignment.dialog.notes')">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          :placeholder="t('workAssignment.dialog.notesPlaceholder')"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">
        {{ t('workAssignment.actions.cancel') }}
      </el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :loading="loading"
      >
        {{ t('workAssignment.actions.confirm') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.work-order-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;

  h4 {
    margin: 0 0 10px 0;
    color: #303133;
  }

  p {
    margin: 5px 0;
    color: #606266;
    font-size: 14px;
  }
}

.assignment-form {
  .duration-unit {
    margin-left: 10px;
    color: #909399;
  }
}

.technician-option {
  .technician-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    .technician-name {
      font-weight: 500;
    }

    .status-tag {
      margin-left: 10px;
    }
  }

  .technician-details {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #909399;
    margin-bottom: 3px;
  }

  .specialties {
    font-size: 12px;
    color: #909399;
  }
}
</style>
