<!-- src/views/afterSales/workAssignment/components/RealTimeUpdater.vue -->
<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { ElCard, ElSwitch, ElButton, ElInputNumber, ElFormItem, ElForm } from 'element-plus';
import { Refresh, Timer } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { RealTimeConfig } from '@/types/afterSales/workAssignmentDashboard.d.ts';

interface Props {
  config: RealTimeConfig;
}

interface Emits {
  (e: 'config-change', config: RealTimeConfig): void;
  (e: 'refresh'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

const localConfig = ref<RealTimeConfig>({ ...props.config });
let refreshTimer: number | null = null;

// 监听配置变化
watch(() => props.config, (newConfig) => {
  localConfig.value = { ...newConfig };
}, { deep: true });

// 监听本地配置变化并发送给父组件
watch(localConfig, (newConfig) => {
  emit('config-change', { ...newConfig });
}, { deep: true });

// 启动自动刷新
const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  
  if (localConfig.value.enabled && localConfig.value.autoRefresh) {
    refreshTimer = setInterval(() => {
      handleRefresh();
    }, localConfig.value.interval * 1000);
  }
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 处理刷新
const handleRefresh = () => {
  localConfig.value.lastUpdateTime = new Date().toLocaleTimeString();
  emit('refresh');
};

// 处理启用状态变化
const handleEnabledChange = (enabled: boolean) => {
  localConfig.value.enabled = enabled;
  if (enabled) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

// 处理自动刷新状态变化
const handleAutoRefreshChange = (autoRefresh: boolean) => {
  localConfig.value.autoRefresh = autoRefresh;
  if (autoRefresh && localConfig.value.enabled) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

// 处理间隔时间变化
const handleIntervalChange = (interval: number) => {
  localConfig.value.interval = interval;
  if (localConfig.value.enabled && localConfig.value.autoRefresh) {
    startAutoRefresh();
  }
};

// 组件挂载时启动自动刷新
onMounted(() => {
  if (localConfig.value.enabled && localConfig.value.autoRefresh) {
    startAutoRefresh();
  }
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<template>
  <el-card class="real-time-updater">
    <template #header>
      <div class="header-content">
        <span>{{ t('workAssignmentDashboard.realTime.title') }}</span>
        <div class="last-update" v-if="localConfig.lastUpdateTime">
          <el-icon><Timer /></el-icon>
          <span>{{ t('workAssignmentDashboard.realTime.lastUpdate') }}: {{ localConfig.lastUpdateTime }}</span>
        </div>
      </div>
    </template>
    
    <el-form :model="localConfig" label-width="120px" class="config-form">
      <el-form-item :label="t('workAssignmentDashboard.realTime.enabled')">
        <el-switch
          :model-value="localConfig.enabled"
          @update:model-value="handleEnabledChange"
          :active-text="tc('enabled')"
          :inactive-text="tc('disabled')"
        />
      </el-form-item>

      <el-form-item
        :label="t('workAssignmentDashboard.realTime.autoRefresh')"
        v-if="localConfig.enabled"
      >
        <el-switch
          :model-value="localConfig.autoRefresh"
          @update:model-value="handleAutoRefreshChange"
          :active-text="tc('enabled')"
          :inactive-text="tc('disabled')"
        />
      </el-form-item>

      <el-form-item
        :label="t('workAssignmentDashboard.realTime.interval')"
        v-if="localConfig.enabled && localConfig.autoRefresh"
      >
        <div class="interval-input">
          <el-input-number
            :model-value="localConfig.interval"
            @update:model-value="handleIntervalChange"
            :min="10"
            :max="300"
            :step="10"
            style="width: 120px"
          />
          <span class="unit">秒</span>
        </div>
      </el-form-item>
      
      <el-form-item>
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="handleRefresh"
          size="small"
        >
          {{ t('workAssignmentDashboard.realTime.refreshNow') }}
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.real-time-updater {
  margin-top: 20px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .last-update {
      display: flex;
      align-items: center;
      gap: 5px;
      color: #909399;
      font-size: 12px;
    }
  }
  
  .config-form {
    .interval-input {
      display: flex;
      align-items: center;
      gap: 10px;
      
      .unit {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}
</style>
