<!-- src/views/afterSales/workAssignment/components/WorkAssignmentSearchForm.vue -->
<script setup lang="ts">
import { ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElButton, ElRow, ElCol } from 'element-plus';
import { Search, RefreshLeft } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderListParams, TechnicianInfo } from '@/types/afterSales/workAssignment.d.ts';

interface Props {
  searchParams: WorkOrderListParams;
  dateRange: [string, string] | null;
  technicianList: TechnicianInfo[];
  statusOptions: Array<{ label: string; value: string }>;
  typeOptions: Array<{ label: string; value: string }>;
  priorityOptions: Array<{ label: string; value: string }>;
  loading?: boolean;
}

interface Emits {
  (e: 'update:searchParams', value: WorkOrderListParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const updateSearchParams = (field: keyof WorkOrderListParams, value: any) => {
  emit('update:searchParams', { ...props.searchParams, [field]: value });
};

const updateDateRange = (value: [string, string] | null) => {
  emit('update:dateRange', value);
};
</script>

<template>
  <el-card class="mb-20 search-card">
    <el-form :model="searchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('workAssignment.searchForm.workOrderNo')">
            <el-input
              :model-value="searchParams.workOrderNo"
              @update:model-value="(val) => updateSearchParams('workOrderNo', val)"
              :placeholder="t('workAssignment.searchForm.workOrderNoPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workAssignment.searchForm.customerName')">
            <el-input
              :model-value="searchParams.customerName"
              @update:model-value="(val) => updateSearchParams('customerName', val)"
              :placeholder="t('workAssignment.searchForm.customerNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workAssignment.searchForm.licensePlate')">
            <el-input
              :model-value="searchParams.licensePlate"
              @update:model-value="(val) => updateSearchParams('licensePlate', val)"
              :placeholder="t('workAssignment.searchForm.licensePlatePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workAssignment.searchForm.status')">
            <el-select
              :model-value="searchParams.status"
              @update:model-value="(val) => updateSearchParams('status', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('workAssignment.searchForm.workOrderType')">
            <el-select
              :model-value="searchParams.workOrderType"
              @update:model-value="(val) => updateSearchParams('workOrderType', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in typeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workAssignment.searchForm.priority')">
            <el-select
              :model-value="searchParams.priority"
              @update:model-value="(val) => updateSearchParams('priority', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in priorityOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workAssignment.searchForm.assignedTechnician')">
            <el-select
              :model-value="searchParams.assignedTechnicianId"
              @update:model-value="(val) => updateSearchParams('assignedTechnicianId', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="technician in technicianList"
                :key="technician.technicianId"
                :label="technician.technicianName"
                :value="technician.technicianId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workAssignment.searchForm.creationTime')">
            <el-date-picker
              :model-value="dateRange"
              @update:model-value="updateDateRange"
              type="daterange"
              range-separator="-"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" class="buttons-col">
          <el-button 
            type="primary" 
            :icon="Search" 
            @click="handleSearch"
            :loading="loading"
          >
            {{ t('workAssignment.actions.search') }}
          </el-button>
          <el-button
            :icon="RefreshLeft"
            @click="handleReset"
          >
            {{ t('workAssignment.actions.reset') }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.search-card {
  margin-bottom: 20px;
  
  .search-form {
    .el-form-item {
      margin-bottom: 15px;
    }
    
    .buttons-col {
      text-align: right;
      
      .el-button {
        margin-left: 10px;
      }
    }
  }
}
</style>
