<!-- 审批详情页面组件 -->
<template>
  <el-dialog
    :model-value="visible"
    :title="t('workOrderApproval.dialog.approvalDetail')"
    width="900px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="loading" class="approval-detail-view">
      <!-- 索赔审批详情 -->
      <div v-if="approvalType === 'claim_approval' && claimDetail" class="detail-content">
        <div class="detail-section">
          <h3>{{ t('workOrderApproval.dialog.basicInfo') }}</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="t('workOrderApproval.approvalNo')">
              {{ claimDetail.approvalNo }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.submitter')">
              {{ claimDetail.submitterName }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.submitTime')">
              {{ formatDateTime(claimDetail.submitTime) }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.orderNo')">
              {{ claimDetail.orderNo }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.currentLevel')">
              <el-tag :type="getLevelTagType(claimDetail.currentLevel)" size="small">
                {{ getLevelText(claimDetail.currentLevel) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.approvalStatus')">
              <el-tag :type="getStatusTagType(claimDetail.approvalStatus)" size="small">
                {{ getStatusText(claimDetail.approvalStatus) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h3>{{ t('workOrderApproval.dialog.customerVehicleInfo') }}</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="t('workOrderApproval.customerName')">
              {{ claimDetail.customerInfo.customerName }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.customerPhone')">
              {{ claimDetail.customerInfo.phone }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.licensePlate')">
              {{ claimDetail.vehicleInfo.licensePlate }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.vehicleModel')">
              {{ claimDetail.vehicleInfo.vehicleModel }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.vin')">
              {{ claimDetail.vehicleInfo.vin }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.mileage')">
              {{ claimDetail.vehicleInfo.mileage.toLocaleString() }} km
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 索赔明细 -->
        <div class="detail-section">
          <h3>{{ t('workOrderApproval.dialog.claimDetails') }}</h3>
          
          <!-- 工时索赔 -->
          <div v-if="claimDetail.claimLaborList.length > 0" class="claim-section">
            <h4>{{ t('workOrderApproval.dialog.laborClaim') }}</h4>
            <el-table :data="claimDetail.claimLaborList" border size="small">
              <el-table-column prop="laborCode" :label="t('workOrderApproval.dialog.laborCode')" width="120" />
              <el-table-column prop="laborName" :label="t('workOrderApproval.dialog.laborName')" />
              <el-table-column prop="laborHours" :label="t('workOrderApproval.dialog.laborHours')" width="100" />
              <el-table-column prop="laborRate" :label="t('workOrderApproval.dialog.laborRate')" width="100" />
              <el-table-column prop="laborAmount" :label="t('workOrderApproval.dialog.laborAmount')" width="120">
                <template #default="{ row }">
                  ¥{{ row.laborAmount.toFixed(2) }}
                </template>
              </el-table-column>
            </el-table>
            <div class="subtotal">
              {{ t('workOrderApproval.dialog.laborTotal') }}: ¥{{ claimDetail.claimLaborTotal.toFixed(2) }}
            </div>
          </div>

          <!-- 零件索赔 -->
          <div v-if="claimDetail.claimPartsList.length > 0" class="claim-section">
            <h4>{{ t('workOrderApproval.dialog.partsClaim') }}</h4>
            <el-table :data="claimDetail.claimPartsList" border size="small">
              <el-table-column prop="partCode" :label="t('workOrderApproval.dialog.partCode')" width="120" />
              <el-table-column prop="partName" :label="t('workOrderApproval.dialog.partName')" />
              <el-table-column prop="quantity" :label="t('workOrderApproval.dialog.quantity')" width="80" />
              <el-table-column prop="unitPrice" :label="t('workOrderApproval.dialog.unitPrice')" width="100">
                <template #default="{ row }">
                  ¥{{ row.unitPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="totalAmount" :label="t('workOrderApproval.dialog.totalAmount')" width="120">
                <template #default="{ row }">
                  ¥{{ row.totalAmount.toFixed(2) }}
                </template>
              </el-table-column>
            </el-table>
            <div class="subtotal">
              {{ t('workOrderApproval.dialog.partsTotal') }}: ¥{{ claimDetail.claimPartsTotal.toFixed(2) }}
            </div>
          </div>

          <!-- 总计 -->
          <div class="total-amount">
            <strong>{{ t('workOrderApproval.dialog.totalAmount') }}: ¥{{ claimDetail.claimTotalAmount.toFixed(2) }}</strong>
          </div>
        </div>
      </div>

      <!-- 取消审批详情 -->
      <div v-if="approvalType === 'cancel_approval' && cancelDetail" class="detail-content">
        <div class="detail-section">
          <h3>{{ t('workOrderApproval.dialog.basicInfo') }}</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="t('workOrderApproval.approvalNo')">
              {{ cancelDetail.approvalNo }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.submitter')">
              {{ cancelDetail.submitterName }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.submitTime')">
              {{ formatDateTime(cancelDetail.submitTime) }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.orderNo')">
              {{ cancelDetail.orderNo }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.currentLevel')">
              <el-tag :type="getLevelTagType(cancelDetail.currentLevel)" size="small">
                {{ getLevelText(cancelDetail.currentLevel) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.approvalStatus')">
              <el-tag :type="getStatusTagType(cancelDetail.approvalStatus)" size="small">
                {{ getStatusText(cancelDetail.approvalStatus) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h3>{{ t('workOrderApproval.dialog.customerVehicleInfo') }}</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="t('workOrderApproval.customerName')">
              {{ cancelDetail.customerInfo.customerName }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.customerPhone')">
              {{ cancelDetail.customerInfo.phone }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.licensePlate')">
              {{ cancelDetail.vehicleInfo.licensePlate }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.vehicleModel')">
              {{ cancelDetail.vehicleInfo.vehicleModel }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.vin')">
              {{ cancelDetail.vehicleInfo.vin }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.mileage')">
              {{ cancelDetail.vehicleInfo.mileage.toLocaleString() }} km
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 取消原因 -->
        <div class="detail-section">
          <h3>{{ t('workOrderApproval.dialog.cancelReason') }}</h3>
          <div class="reason-content">
            <p><strong>{{ t('workOrderApproval.dialog.requestReason') }}:</strong> {{ cancelDetail.requestReason }}</p>
            <p><strong>{{ t('workOrderApproval.dialog.cancelReason') }}:</strong> {{ cancelDetail.cancelReason }}</p>
            <div v-if="cancelDetail.cancelDescription" class="cancel-description">
              <strong>{{ t('workOrderApproval.dialog.cancelDescription') }}:</strong>
              <p>{{ cancelDetail.cancelDescription }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 审批流程 -->
      <div v-if="approvalProcessList.length > 0" class="detail-section">
        <h3>{{ t('workOrderApproval.dialog.approvalProcess') }}</h3>
        <el-timeline>
          <el-timeline-item
            v-for="process in approvalProcessList"
            :key="process.processId"
            :timestamp="formatDateTime(process.approvalTime)"
            :type="getProcessType(process.approvalResult)"
          >
            <div class="process-item">
              <div class="process-header">
                <span class="process-level">{{ getLevelText(process.approvalLevel) }}</span>
                <span class="process-result">
                  <el-tag :type="getResultTagType(process.approvalResult)" size="small">
                    {{ getResultText(process.approvalResult) }}
                  </el-tag>
                </span>
              </div>
              <div class="process-content">
                <p><strong>{{ t('workOrderApproval.dialog.approver') }}:</strong> {{ process.approverName }}</p>
                <p v-if="process.approvalRemark">
                  <strong>{{ t('workOrderApproval.dialog.approvalRemark') }}:</strong> {{ process.approvalRemark }}
                </p>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ tc('close') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getClaimApprovalDetail, getCancelApprovalDetail } from '@/api/modules/afterSales/workOrderApproval';
import type {
  ClaimApprovalDetail,
  CancelApprovalDetail,
  WorkOrderApprovalType,
  ApprovalLevel,
  ApprovalStatus,
  ApprovalResult,
  WorkOrderApprovalProcess
} from '@/types/afterSales/workOrderApproval';

interface Props {
  visible: boolean;
  approvalNo: string;
  approvalType: WorkOrderApprovalType;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

// 加载状态
const loading = ref(false);

// 详情数据
const claimDetail = ref<ClaimApprovalDetail | null>(null);
const cancelDetail = ref<CancelApprovalDetail | null>(null);

// 审批流程列表
const approvalProcessList = computed(() => {
  if (props.approvalType === 'claim_approval' && claimDetail.value) {
    return claimDetail.value.approvalProcessList;
  }
  if (props.approvalType === 'cancel_approval' && cancelDetail.value) {
    return cancelDetail.value.approvalProcessList;
  }
  return [];
});

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '';
  return dateTime.replace('T', ' ').slice(0, 19);
};

// 获取级别标签类型
const getLevelTagType = (level: ApprovalLevel) => {
  const levelMap: Record<ApprovalLevel, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'first_level': 'primary',
    'second_level': 'success'
  };
  return levelMap[level] || 'info';
};

// 获取级别文本
const getLevelText = (level: ApprovalLevel) => {
  const levelMap: Record<ApprovalLevel, string> = {
    'first_level': t('workOrderApproval.level.firstLevel'),
    'second_level': t('workOrderApproval.level.secondLevel')
  };
  return levelMap[level] || level;
};

// 获取状态标签类型
const getStatusTagType = (status: ApprovalStatus) => {
  const statusMap: Record<ApprovalStatus, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'pending_review': 'warning',
    'reviewed': 'success'
  };
  return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: ApprovalStatus) => {
  const statusMap: Record<ApprovalStatus, string> = {
    'pending_review': t('workOrderApproval.status.pendingReview'),
    'reviewed': t('workOrderApproval.status.reviewed')
  };
  return statusMap[status] || status;
};

// 获取结果标签类型
const getResultTagType = (result: ApprovalResult) => {
  const resultMap: Record<ApprovalResult, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'approved': 'success',
    'rejected': 'danger'
  };
  return resultMap[result] || 'info';
};

// 获取结果文本
const getResultText = (result: ApprovalResult) => {
  const resultMap: Record<ApprovalResult, string> = {
    'approved': t('workOrderApproval.result.approved'),
    'rejected': t('workOrderApproval.result.rejected')
  };
  return resultMap[result] || result;
};

// 获取流程类型
const getProcessType = (result: ApprovalResult) => {
  return result === 'approved' ? 'success' : 'danger';
};

// 加载详情数据
const loadDetailData = async () => {
  if (!props.approvalNo || !props.approvalType) return;

  try {
    loading.value = true;

    if (props.approvalType === 'claim_approval') {
      claimDetail.value = await getClaimApprovalDetail(props.approvalNo);
      cancelDetail.value = null;
    } else if (props.approvalType === 'cancel_approval') {
      cancelDetail.value = await getCancelApprovalDetail(props.approvalNo);
      claimDetail.value = null;
    }
  } catch (error) {
    console.error('加载审批详情失败:', error);
    ElMessage.error(t('workOrderApproval.messages.loadDetailFailed'));
  } finally {
    loading.value = false;
  }
};

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      loadDetailData();
    } else {
      // 清空数据
      claimDetail.value = null;
      cancelDetail.value = null;
    }
  }
);

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};
</script>

<style scoped lang="scss">
.approval-detail-view {
  .detail-content {
    .detail-section {
      margin-bottom: 30px;

      h3 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }

      .claim-section {
        margin-bottom: 20px;

        h4 {
          margin: 0 0 10px 0;
          color: #606266;
          font-size: 14px;
          font-weight: 500;
        }

        .subtotal {
          text-align: right;
          margin-top: 10px;
          font-weight: 500;
          color: #409eff;
        }
      }

      .total-amount {
        text-align: right;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 2px solid #409eff;
        font-size: 16px;
        color: #409eff;
      }

      .reason-content {
        background: #f5f7fa;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid #409eff;

        p {
          margin: 0 0 10px 0;
          line-height: 1.6;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .cancel-description {
          margin-top: 15px;
          padding-top: 15px;
          border-top: 1px solid #e4e7ed;

          p {
            margin-top: 5px;
            padding-left: 20px;
            color: #606266;
            font-style: italic;
          }
        }
      }
    }
  }

  .process-item {
    .process-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 8px;

      .process-level {
        font-weight: 600;
        color: #303133;
      }
    }

    .process-content {
      p {
        margin: 0 0 5px 0;
        color: #606266;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
