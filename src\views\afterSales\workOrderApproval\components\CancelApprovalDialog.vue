<!-- 取消审批对话框组件 -->
<template>
  <el-dialog
    :model-value="visible"
    :title="t('workOrderApproval.dialog.cancelApprovalTitle')"
    width="700px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="detailLoading" class="cancel-approval-dialog">
      <!-- 基本信息 -->
      <div v-if="approvalDetail" class="approval-info">
        <h3>{{ t('workOrderApproval.dialog.basicInfo') }}</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="t('workOrderApproval.approvalNo')">
            {{ approvalDetail.approvalNo }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.submitter')">
            {{ approvalDetail.submitterName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.submitTime')">
            {{ formatDateTime(approvalDetail.submitTime) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.orderNo')">
            {{ approvalDetail.orderNo }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.customerName')">
            {{ approvalDetail.customerInfo.customerName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.vehicleInfo')">
            {{ approvalDetail.vehicleInfo.licensePlate }} {{ approvalDetail.vehicleInfo.vehicleModel }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 取消原因 -->
        <div class="cancel-reason">
          <h3>{{ t('workOrderApproval.dialog.cancelReason') }}</h3>
          <div class="reason-content">
            <p><strong>{{ t('workOrderApproval.dialog.requestReason') }}:</strong> {{ approvalDetail.requestReason }}</p>
            <p><strong>{{ t('workOrderApproval.dialog.cancelReason') }}:</strong> {{ approvalDetail.cancelReason }}</p>
            <div v-if="approvalDetail.cancelDescription" class="cancel-description">
              <strong>{{ t('workOrderApproval.dialog.cancelDescription') }}:</strong>
              <p>{{ approvalDetail.cancelDescription }}</p>
            </div>
          </div>
        </div>

        <!-- 车辆详细信息 -->
        <div class="vehicle-details">
          <h3>{{ t('workOrderApproval.dialog.vehicleDetails') }}</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="t('workOrderApproval.dialog.vin')">
              {{ approvalDetail.vehicleInfo.vin }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.engineNo')">
              {{ approvalDetail.vehicleInfo.engineNo }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.purchaseDate')">
              {{ approvalDetail.vehicleInfo.purchaseDate }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.warrantyEndDate')">
              {{ approvalDetail.vehicleInfo.warrantyEndDate }}
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.mileage')">
              {{ approvalDetail.vehicleInfo.mileage.toLocaleString() }} km
            </el-descriptions-item>
            <el-descriptions-item :label="t('workOrderApproval.dialog.customerPhone')">
              {{ approvalDetail.customerInfo.phone }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 审批表单 -->
      <div class="approval-form">
        <h3>{{ t('workOrderApproval.dialog.approvalForm') }}</h3>
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item :label="t('workOrderApproval.dialog.approvalResult')" prop="approvalResult">
            <el-radio-group v-model="formData.approvalResult">
              <el-radio value="approved">{{ t('workOrderApproval.result.approved') }}</el-radio>
              <el-radio value="rejected">{{ t('workOrderApproval.result.rejected') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="formData.approvalResult === 'approved'"
            :label="t('workOrderApproval.dialog.approvalRemark')"
          >
            <el-input
              v-model="formData.approvalRemark"
              type="textarea"
              :rows="3"
              :placeholder="t('workOrderApproval.dialog.approvalRemarkPlaceholder')"
            />
          </el-form-item>

          <el-form-item
            v-if="formData.approvalResult === 'rejected'"
            :label="t('workOrderApproval.dialog.rejectionReason')"
            prop="rejectionReason"
          >
            <el-input
              v-model="formData.rejectionReason"
              type="textarea"
              :rows="3"
              :placeholder="t('workOrderApproval.dialog.rejectionReasonPlaceholder')"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ tc('cancel') }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ tc('confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getCancelApprovalDetail } from '@/api/modules/afterSales/workOrderApproval';
import type {
  CancelApprovalDetail,
  ApprovalFormData
} from '@/types/afterSales/workOrderApproval';

interface Props {
  visible: boolean;
  approvalNo: string;
  loading: boolean;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm', data: ApprovalFormData): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

// 表单引用
const formRef = ref<FormInstance>();

// 审批详情
const approvalDetail = ref<CancelApprovalDetail | null>(null);
const detailLoading = ref(false);

// 表单数据
const formData = reactive<ApprovalFormData>({
  approvalResult: 'approved',
  approvalRemark: '',
  rejectionReason: ''
});

// 表单验证规则
const rules: FormRules = {
  approvalResult: [
    { required: true, message: t('workOrderApproval.messages.selectApprovalResult'), trigger: 'change' }
  ],
  rejectionReason: [
    { required: true, message: t('workOrderApproval.messages.enterRejectionReason'), trigger: 'blur' }
  ]
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '';
  return dateTime.replace('T', ' ').slice(0, 19);
};

// 加载审批详情
const loadApprovalDetail = async () => {
  if (!props.approvalNo) return;
  
  try {
    detailLoading.value = true;
    approvalDetail.value = await getCancelApprovalDetail(props.approvalNo);
  } catch (error) {
    console.error('加载审批详情失败:', error);
    ElMessage.error(t('workOrderApproval.messages.loadDetailFailed'));
  } finally {
    detailLoading.value = false;
  }
};

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.approvalNo) {
      loadApprovalDetail();
      // 重置表单
      formData.approvalResult = 'approved';
      formData.approvalRemark = '';
      formData.rejectionReason = '';
    }
  }
);

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};

// 确认审批
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    emit('confirm', { ...formData });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};
</script>

<style scoped lang="scss">
.cancel-approval-dialog {
  .approval-info {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .cancel-reason {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .reason-content {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 6px;
      border-left: 4px solid #409eff;

      p {
        margin: 0 0 10px 0;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .cancel-description {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e4e7ed;

        p {
          margin-top: 5px;
          padding-left: 20px;
          color: #606266;
          font-style: italic;
        }
      }
    }
  }

  .vehicle-details {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .approval-form {
    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
