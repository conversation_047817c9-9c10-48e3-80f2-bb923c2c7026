<!-- 索赔审批对话框组件 -->
<template>
  <el-dialog
    :model-value="visible"
    :title="t('workOrderApproval.dialog.claimApprovalTitle')"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="detailLoading" class="claim-approval-dialog">
      <!-- 基本信息 -->
      <div v-if="approvalDetail" class="approval-info">
        <h3>{{ t('workOrderApproval.dialog.basicInfo') }}</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="t('workOrderApproval.approvalNo')">
            {{ approvalDetail.approvalNo }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.submitter')">
            {{ approvalDetail.submitterName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.submitTime')">
            {{ formatDateTime(approvalDetail.submitTime) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.orderNo')">
            {{ approvalDetail.orderNo }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.customerName')">
            {{ approvalDetail.customerInfo.customerName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('workOrderApproval.vehicleInfo')">
            {{ approvalDetail.vehicleInfo.licensePlate }} {{ approvalDetail.vehicleInfo.vehicleModel }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 索赔明细 -->
        <div class="claim-details">
          <h3>{{ t('workOrderApproval.dialog.claimDetails') }}</h3>
          
          <!-- 工时索赔 -->
          <div v-if="approvalDetail.claimLaborList.length > 0" class="claim-section">
            <h4>{{ t('workOrderApproval.dialog.laborClaim') }}</h4>
            <el-table :data="approvalDetail.claimLaborList" border size="small">
              <el-table-column prop="laborCode" :label="t('workOrderApproval.dialog.laborCode')" width="120" />
              <el-table-column prop="laborName" :label="t('workOrderApproval.dialog.laborName')" />
              <el-table-column prop="laborHours" :label="t('workOrderApproval.dialog.laborHours')" width="100" />
              <el-table-column prop="laborRate" :label="t('workOrderApproval.dialog.laborRate')" width="100" />
              <el-table-column prop="laborAmount" :label="t('workOrderApproval.dialog.laborAmount')" width="120">
                <template #default="{ row }">
                  ¥{{ row.laborAmount.toFixed(2) }}
                </template>
              </el-table-column>
            </el-table>
            <div class="subtotal">
              {{ t('workOrderApproval.dialog.laborTotal') }}: ¥{{ approvalDetail.claimLaborTotal.toFixed(2) }}
            </div>
          </div>

          <!-- 零件索赔 -->
          <div v-if="approvalDetail.claimPartsList.length > 0" class="claim-section">
            <h4>{{ t('workOrderApproval.dialog.partsClaim') }}</h4>
            <el-table :data="approvalDetail.claimPartsList" border size="small">
              <el-table-column prop="partCode" :label="t('workOrderApproval.dialog.partCode')" width="120" />
              <el-table-column prop="partName" :label="t('workOrderApproval.dialog.partName')" />
              <el-table-column prop="quantity" :label="t('workOrderApproval.dialog.quantity')" width="80" />
              <el-table-column prop="unitPrice" :label="t('workOrderApproval.dialog.unitPrice')" width="100">
                <template #default="{ row }">
                  ¥{{ row.unitPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="totalAmount" :label="t('workOrderApproval.dialog.totalAmount')" width="120">
                <template #default="{ row }">
                  ¥{{ row.totalAmount.toFixed(2) }}
                </template>
              </el-table-column>
            </el-table>
            <div class="subtotal">
              {{ t('workOrderApproval.dialog.partsTotal') }}: ¥{{ approvalDetail.claimPartsTotal.toFixed(2) }}
            </div>
          </div>

          <!-- 总计 -->
          <div class="total-amount">
            <strong>{{ t('workOrderApproval.dialog.totalAmount') }}: ¥{{ approvalDetail.claimTotalAmount.toFixed(2) }}</strong>
          </div>
        </div>
      </div>

      <!-- 审批表单 -->
      <div class="approval-form">
        <h3>{{ t('workOrderApproval.dialog.approvalForm') }}</h3>
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item :label="t('workOrderApproval.dialog.approvalResult')" prop="approvalResult">
            <el-radio-group v-model="formData.approvalResult">
              <el-radio value="approved">{{ t('workOrderApproval.result.approved') }}</el-radio>
              <el-radio value="rejected">{{ t('workOrderApproval.result.rejected') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="formData.approvalResult === 'approved'"
            :label="t('workOrderApproval.dialog.approvalRemark')"
          >
            <el-input
              v-model="formData.approvalRemark"
              type="textarea"
              :rows="3"
              :placeholder="t('workOrderApproval.dialog.approvalRemarkPlaceholder')"
            />
          </el-form-item>

          <el-form-item
            v-if="formData.approvalResult === 'rejected'"
            :label="t('workOrderApproval.dialog.rejectionReason')"
            prop="rejectionReason"
          >
            <el-input
              v-model="formData.rejectionReason"
              type="textarea"
              :rows="3"
              :placeholder="t('workOrderApproval.dialog.rejectionReasonPlaceholder')"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ tc('cancel') }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ tc('confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getClaimApprovalDetail } from '@/api/modules/afterSales/workOrderApproval';
import type {
  ClaimApprovalDetail,
  ApprovalFormData,
  ApprovalResult
} from '@/types/afterSales/workOrderApproval';

interface Props {
  visible: boolean;
  approvalNo: string;
  loading: boolean;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm', data: ApprovalFormData): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

// 表单引用
const formRef = ref<FormInstance>();

// 审批详情
const approvalDetail = ref<ClaimApprovalDetail | null>(null);
const detailLoading = ref(false);

// 表单数据
const formData = reactive<ApprovalFormData>({
  approvalResult: 'approved',
  approvalRemark: '',
  rejectionReason: ''
});

// 表单验证规则
const rules: FormRules = {
  approvalResult: [
    { required: true, message: t('workOrderApproval.messages.selectApprovalResult'), trigger: 'change' }
  ],
  rejectionReason: [
    { required: true, message: t('workOrderApproval.messages.enterRejectionReason'), trigger: 'blur' }
  ]
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '';
  return dateTime.replace('T', ' ').slice(0, 19);
};

// 加载审批详情
const loadApprovalDetail = async () => {
  if (!props.approvalNo) return;
  
  try {
    detailLoading.value = true;
    approvalDetail.value = await getClaimApprovalDetail(props.approvalNo);
  } catch (error) {
    console.error('加载审批详情失败:', error);
    ElMessage.error(t('workOrderApproval.messages.loadDetailFailed'));
  } finally {
    detailLoading.value = false;
  }
};

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.approvalNo) {
      loadApprovalDetail();
      // 重置表单
      formData.approvalResult = 'approved';
      formData.approvalRemark = '';
      formData.rejectionReason = '';
    }
  }
);

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};

// 确认审批
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    emit('confirm', { ...formData });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};
</script>

<style scoped lang="scss">
.claim-approval-dialog {
  .approval-info {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .claim-details {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .claim-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 10px 0;
        color: #606266;
        font-size: 14px;
        font-weight: 500;
      }

      .subtotal {
        text-align: right;
        margin-top: 10px;
        font-weight: 500;
        color: #409eff;
      }
    }

    .total-amount {
      text-align: right;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 2px solid #409eff;
      font-size: 16px;
      color: #409eff;
    }
  }

  .approval-form {
    h3 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
