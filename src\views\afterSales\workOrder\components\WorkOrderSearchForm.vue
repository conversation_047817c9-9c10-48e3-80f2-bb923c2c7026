<script setup lang="ts">
import { ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElButton, ElRow, ElCol } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderSearchParams } from '@/types/afterSales/workOrder.d.ts';

interface Props {
  searchParams: WorkOrderSearchParams;
  dateRange: [string, string] | null;
  serviceAdvisorOptions: Array<{ label: string; value: string }>;
  technicianOptions: Array<{ label: string; value: string }>;
  statusOptions: Array<{ label: string; value: string }>;
  priorityOptions: Array<{ label: string; value: string }>;
  customerSourceOptions: Array<{ label: string; value: string }>;
  workOrderTypeOptions: Array<{ label: string; value: string }>;
}

interface Emits {
  (e: 'update:searchParams', value: WorkOrderSearchParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.workOrder');

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const updateSearchParams = (field: keyof WorkOrderSearchParams, value: any) => {
  emit('update:searchParams', { ...props.searchParams, [field]: value });
};

const updateDateRange = (value: [string, string] | null) => {
  emit('update:dateRange', value);
};
</script>

<template>
  <el-card class="mb-20 search-card">
    <el-form :model="searchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.workOrderNumber')">
            <el-input
              :model-value="searchParams.workOrderNumber"
              @update:model-value="(val) => updateSearchParams('workOrderNumber', val)"
              :placeholder="t('searchForm.workOrderNumberPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.status')">
            <el-select
              :model-value="searchParams.status"
              @update:model-value="(val) => updateSearchParams('status', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.priority')">
            <el-select
              :model-value="searchParams.priority"
              @update:model-value="(val) => updateSearchParams('priority', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in priorityOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.customerSource')">
            <el-select
              :model-value="searchParams.customerSource"
              @update:model-value="(val) => updateSearchParams('customerSource', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in customerSourceOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.workOrderType')">
            <el-select
              :model-value="searchParams.workOrderType"
              @update:model-value="(val) => updateSearchParams('workOrderType', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in workOrderTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.customerName')">
            <el-input
              :model-value="searchParams.customerName"
              @update:model-value="(val) => updateSearchParams('customerName', val)"
              :placeholder="t('searchForm.customerNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.licensePlate')">
            <el-input
              :model-value="searchParams.licensePlate"
              @update:model-value="(val) => updateSearchParams('licensePlate', val)"
              :placeholder="t('searchForm.licensePlatePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.createdAt')">
            <el-date-picker
              :model-value="dateRange"
              @update:model-value="updateDateRange"
              type="daterange"
              range-separator="-"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" class="buttons-col">
          <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
          <el-button @click="handleReset">{{ tc('reset') }}</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
  .buttons-col {
    text-align: right;
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
