<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="getDialogTitle()"
    width="1200px"
    :close-on-click-modal="false"
    class="approval-dialog"
  >
    <div v-if="approvalDetail" class="dialog-content">
      <!-- 基础信息区域 -->
      <div class="info-section">
        <h3>{{ t('workOrderApproval.basicInfo') }}</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>{{ t('workOrderApproval.approvalNo') }}:</label>
              <span>{{ approvalDetail.approval_request.approval_no }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('workOrderApproval.approvalType') }}:</label>
              <span>{{ getApprovalTypeText(approvalDetail.approval_request.approval_type) }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('workOrderApproval.submitter') }}:</label>
              <span>{{ approvalDetail.submitter.name }} ({{ approvalDetail.submitter.employee_no }})</span>
            </div>
            <div class="info-item">
              <label>{{ t('workOrderApproval.submitTime') }}:</label>
              <span>{{ approvalDetail.approval_request.submit_time }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>{{ t('workOrderApproval.workOrderNo') }}:</label>
              <span>{{ approvalDetail.work_order.order_no }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('workOrderApproval.requestReason') }}:</label>
              <span>{{ approvalDetail.approval_request.request_reason }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 客户信息和车辆信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-section">
            <h3>{{ t('workOrderApproval.customerInfo') }}</h3>
            <div class="info-item">
              <label>{{ t('workOrderApproval.customerName') }}:</label>
              <span>{{ approvalDetail.customer.customer_name }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('workOrderApproval.customerPhone') }}:</label>
              <span>{{ approvalDetail.customer.phone }}</span>
            </div>
            <div class="info-item" v-if="approvalDetail.customer.sender_name">
              <label>{{ t('workOrderApproval.senderName') }}:</label>
              <span>{{ approvalDetail.customer.sender_name }}</span>
            </div>
            <div class="info-item" v-if="approvalDetail.customer.sender_phone">
              <label>{{ t('workOrderApproval.senderPhone') }}:</label>
              <span>{{ approvalDetail.customer.sender_phone }}</span>
            </div>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="info-section">
            <h3>{{ t('workOrderApproval.vehicleInfo') }}</h3>
            <div class="info-item">
              <label>{{ t('workOrderApproval.licensePlate') }}:</label>
              <span>{{ approvalDetail.vehicle.license_plate }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('workOrderApproval.vin') }}:</label>
              <span>{{ approvalDetail.vehicle.vin }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('workOrderApproval.model') }}:</label>
              <span>{{ approvalDetail.vehicle.model }}</span>
            </div>
            <div class="info-item" v-if="approvalDetail.vehicle.color">
              <label>{{ t('workOrderApproval.color') }}:</label>
              <span>{{ approvalDetail.vehicle.color }}</span>
            </div>
            <div class="info-item" v-if="approvalDetail.vehicle.mileage">
              <label>{{ t('workOrderApproval.mileage') }}:</label>
              <span>{{ approvalDetail.vehicle.mileage }} {{ t('workOrderApproval.km') }}</span>
            </div>
            <div class="info-item" v-if="approvalDetail.vehicle.vehicle_age">
              <label>{{ t('workOrderApproval.vehicleAge') }}:</label>
              <span>{{ approvalDetail.vehicle.vehicle_age }} {{ t('workOrderApproval.months') }}</span>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 索赔内容详情（仅索赔审批显示） -->
      <div v-if="isClaimApproval" class="info-section">
        <h3>{{ t('workOrderApproval.claimDetails') }}</h3>

        <!-- 索赔工时 -->
        <div v-if="claimLabors.length > 0">
          <h4>{{ t('workOrderApproval.claimLabor') }}</h4>
          <el-table :data="claimLabors" border class="claim-table">
            <el-table-column
              prop="item_code"
              :label="t('workOrderApproval.laborCode')"
              min-width="100"
            />
            <el-table-column
              prop="item_name"
              :label="t('workOrderApproval.laborName')"
              min-width="150"
            />
            <el-table-column
              prop="quantity"
              :label="t('workOrderApproval.standardLabor')"
              min-width="100"
            >
              <template #default="{ row }">
                {{ row.quantity }} {{ t('workOrderApproval.hours') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="unit_price"
              :label="t('workOrderApproval.laborPrice')"
              min-width="100"
            >
              <template #default="{ row }">
                {{ row.unit_price }} {{ t('workOrderApproval.yuan') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="claim_amount"
              :label="t('workOrderApproval.claimAmount')"
              min-width="100"
            >
              <template #default="{ row }">
                <span class="amount-highlight">{{ row.claim_amount }} {{ t('workOrderApproval.yuan') }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 索赔零件 -->
        <div v-if="claimParts.length > 0" class="mt-20">
          <h4>{{ t('workOrderApproval.claimParts') }}</h4>
          <el-table :data="claimParts" border class="claim-table">
            <el-table-column
              prop="item_code"
              :label="t('workOrderApproval.partCode')"
              min-width="120"
            />
            <el-table-column
              prop="item_name"
              :label="t('workOrderApproval.partName')"
              min-width="150"
            />
            <el-table-column
              prop="quantity"
              :label="t('workOrderApproval.quantity')"
              min-width="80"
            >
              <template #default="{ row }">
                {{ row.quantity }} {{ t('workOrderApproval.pieces') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="unit_price"
              :label="t('workOrderApproval.unitPrice')"
              min-width="100"
            >
              <template #default="{ row }">
                {{ row.unit_price }} {{ t('workOrderApproval.yuan') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="claim_amount"
              :label="t('workOrderApproval.claimAmount')"
              min-width="100"
            >
              <template #default="{ row }">
                <span class="amount-highlight">{{ row.claim_amount }} {{ t('workOrderApproval.yuan') }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 索赔总金额 -->
        <div class="total-amount">
          <span class="total-label">{{ t('workOrderApproval.totalClaimAmount') }}:</span>
          <span class="total-value">{{ totalClaimAmount }} {{ t('workOrderApproval.yuan') }}</span>
        </div>
      </div>

      <!-- 工单状态信息（取消审批显示） -->
      <div v-if="!isClaimApproval" class="info-section">
        <h3>{{ t('workOrderApproval.workOrderInfo') }}</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>{{ t('workOrderApproval.workOrderStatus') }}:</label>
              <el-tag>{{ getWorkOrderStatusText(approvalDetail.work_order.order_status) }}</el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>{{ t('workOrderApproval.paymentStatus') }}:</label>
              <el-tag>{{ getPaymentStatusText(approvalDetail.work_order.payment_status) }}</el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>{{ t('workOrderApproval.estimatedStartTime') }}:</label>
              <span>{{ formatDateTime(approvalDetail.work_order.last_approval_time) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 审批操作区域 -->
      <div class="approval-section">
        <h3>{{ t('workOrderApproval.approvalAction') }}</h3>
        <el-form :model="approvalForm" :rules="approvalRules" ref="approvalFormRef" label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('workOrderApproval.approvalResult')" prop="approval_result">
                <el-radio-group v-model="approvalForm.approval_result">
                  <el-radio value="审批通过">{{ t('workOrderApproval.approved') }}</el-radio>
                  <el-radio value="审批驳回">{{ t('workOrderApproval.rejected') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('workOrderApproval.approvalRemark')" prop="approval_remark">
                <el-input
                  v-model="approvalForm.approval_remark"
                  type="textarea"
                  :rows="4"
                  :placeholder="t('workOrderApproval.approvalRemarkPlaceholder')"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ t('workOrderApproval.confirmApproval') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import type {
  ApprovalDetail,
  ClaimContent,
  ApprovalActionRequest
} from '@/types/workOrderApproval';

const { t } = useI18n();

// Props
interface Props {
  visible: boolean;
  approvalDetail: ApprovalDetail | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'approval-submit': [data: ApprovalActionRequest];
}>();

// 响应式数据
const submitting = ref(false);
const approvalFormRef = ref<FormInstance>();

// 审批表单
const approvalForm = reactive({
  approval_result: '',
  approval_remark: ''
});

// 表单验证规则
const approvalRules: FormRules = {
  approval_result: [
    { required: true, message: t('workOrderApproval.selectApprovalResult'), trigger: 'change' }
  ],
  approval_remark: [
    {
      validator: (rule, value, callback) => {
        if (approvalForm.approval_result === '审批驳回' && !value) {
          callback(new Error(t('workOrderApproval.approvalRemarkRequired')));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

// 计算属性
const isClaimApproval = computed(() => {
  return props.approvalDetail?.approval_request.approval_type === '索赔审批';
});

const claimLabors = computed(() => {
  return props.approvalDetail?.claim_contents.filter(item => item.claim_type === '工时索赔') || [];
});

const claimParts = computed(() => {
  return props.approvalDetail?.claim_contents.filter(item => item.claim_type === '零件索赔') || [];
});

const totalClaimAmount = computed(() => {
  return props.approvalDetail?.claim_contents.reduce((total, item) => total + item.claim_amount, 0) || 0;
});

// 方法
const getDialogTitle = (): string => {
  if (!props.approvalDetail) return '';

  const typeText = props.approvalDetail.approval_request.approval_type === '索赔审批'
    ? t('workOrderApproval.claimApproval')
    : t('workOrderApproval.cancelOrderApproval');

  return `${typeText} - ${props.approvalDetail.approval_request.approval_no}`;
};

const getApprovalTypeText = (type: string): string => {
  return type === '索赔审批' ? t('workOrderApproval.claimApproval') : t('workOrderApproval.cancelOrderApproval');
};

const getWorkOrderStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '草稿': t('workOrderApproval.draft'),
    '等待审批': t('workOrderApproval.waitingApproval'),
    '已确认': t('workOrderApproval.confirmed'),
    '已取消': t('workOrderApproval.cancelled')
  };
  return statusMap[status] || status;
};

const getPaymentStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '待支付': t('workOrderApproval.pendingPayment'),
    '已支付': t('workOrderApproval.paid')
  };
  return statusMap[status] || status;
};

const formatDateTime = (dateTime?: string): string => {
  return dateTime || '--';
};

const handleCancel = () => {
  emit('update:visible', false);
  resetForm();
};

const handleSubmit = async () => {
  if (!approvalFormRef.value || !props.approvalDetail) return;

  const valid = await approvalFormRef.value.validate().catch(() => false);
  if (!valid) return;

  const submitData: ApprovalActionRequest = {
    approval_no: props.approvalDetail.approval_request.approval_no,
    approval_result: approvalForm.approval_result as any,
    approval_remark: approvalForm.approval_remark
  };

  emit('approval-submit', submitData);
};

const resetForm = () => {
  approvalForm.approval_result = '';
  approvalForm.approval_remark = '';
  approvalFormRef.value?.clearValidate();
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});
</script>

<style scoped lang="scss">
.approval-dialog {
  .dialog-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .info-section {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    h4 {
      margin: 16px 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #606266;
    }
  }

  .info-item {
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    label {
      min-width: 100px;
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
    }

    span {
      color: #303133;
    }
  }

  .claim-table {
    margin-bottom: 16px;

    .amount-highlight {
      font-weight: 600;
      color: #e6a23c;
    }
  }

  .total-amount {
    text-align: right;
    padding: 16px;
    background-color: #f0f9ff;
    border-radius: 6px;
    border: 1px solid #b3d8ff;

    .total-label {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-right: 12px;
    }

    .total-value {
      font-size: 18px;
      font-weight: 600;
      color: #e6a23c;
    }
  }

  .approval-section {
    margin-top: 24px;
    padding: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    background-color: #ffffff;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;

    .el-button {
      margin-left: 10px;
    }
  }
}

.mt-20 {
  margin-top: 20px;
}
</style>
