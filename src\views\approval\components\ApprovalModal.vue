<template>
  <el-dialog
    v-model="visible"
    :title="modalTitle"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div v-if="approvalData" class="approval-modal-content">
      <!-- 基础信息区域 -->
      <div class="info-section">
        <h3 class="section-title">基础信息</h3>
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label>审批单号：</label>
              <span>{{ approvalData.approvalNo }}</span>
            </div>
            <div class="info-item">
              <label>工单编号：</label>
              <span>{{ approvalData.orderNo }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <label>审批类型：</label>
              <el-tag :type="approvalData.approvalType === 'claim_approval' ? 'warning' : 'info'">
                {{ getApprovalTypeText(approvalData.approvalType) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>提交人：</label>
              <span>{{ approvalData.submitterName }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <label>提交时间：</label>
              <span>{{ approvalData.submitTime }}</span>
            </div>
            <div class="info-item">
              <label>申请原因：</label>
              <span>{{ approvalData.requestReason }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户信息区域 -->
      <div class="info-section">
        <h3 class="section-title">客户信息</h3>
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label>客户姓名：</label>
              <span>{{ approvalData.customerName }}</span>
            </div>
            <div class="info-item">
              <label>车牌号：</label>
              <span>{{ approvalData.licensePlate }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <label>车型：</label>
              <span>{{ approvalData.vehicleModel }}</span>
            </div>
            <div class="info-item">
              <label>门店：</label>
              <span>{{ approvalData.storeName }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 索赔内容详情（仅索赔审批显示） -->
      <div v-if="approvalData.approvalType === 'claim_approval'" class="info-section">
        <h3 class="section-title">索赔内容详情</h3>

        <!-- 索赔工时表格 -->
        <div class="claim-table-section">
          <h4 class="table-title">索赔工时：</h4>
          <el-table :data="mockClaimLabor" border size="small">
            <el-table-column prop="itemCode" label="工时代码" width="120" />
            <el-table-column prop="itemName" label="工时名称" />
            <el-table-column prop="quantity" label="标准工时" width="120" align="right" />
            <el-table-column prop="unitPrice" label="工时单价" width="120" align="right">
              <template #default="{ row }">
                ¥{{ row.unitPrice.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="claimAmount" label="索赔金额" width="120" align="right">
              <template #default="{ row }">
                <span class="amount-highlight">¥{{ row.claimAmount.toFixed(2) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 索赔零件表格 -->
        <div class="claim-table-section">
          <h4 class="table-title">索赔零件：</h4>
          <el-table :data="mockClaimParts" border size="small">
            <el-table-column prop="itemCode" label="零件编码" width="120" />
            <el-table-column prop="itemName" label="零件名称" />
            <el-table-column prop="quantity" label="数量" width="80" align="right" />
            <el-table-column prop="unitPrice" label="单价" width="120" align="right">
              <template #default="{ row }">
                ¥{{ row.unitPrice.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="claimAmount" label="索赔金额" width="120" align="right">
              <template #default="{ row }">
                <span class="amount-highlight">¥{{ row.claimAmount.toFixed(2) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 索赔总金额统计 -->
        <div class="claim-summary">
          <div class="summary-item">
            <label>索赔工时总额：</label>
            <span class="amount">¥{{ claimLaborTotal.toFixed(2) }}</span>
          </div>
          <div class="summary-item">
            <label>索赔零件总额：</label>
            <span class="amount">¥{{ claimPartsTotal.toFixed(2) }}</span>
          </div>
          <div class="summary-item total">
            <label>索赔总金额：</label>
            <span class="total-amount">¥{{ claimTotalAmount.toFixed(2) }}</span>
          </div>
        </div>
      </div>

      <!-- 审批操作区域 -->
      <div class="approval-section">
        <h3 class="section-title">审批操作</h3>
        <el-form
          ref="approvalFormRef"
          :model="approvalForm"
          :rules="approvalRules"
          label-width="100px"
        >
          <el-form-item label="审批结果" prop="result" required>
            <el-radio-group v-model="approvalForm.result">
              <el-radio value="approved">
                <el-icon><Check /></el-icon>
                审批通过
              </el-radio>
              <el-radio value="rejected">
                <el-icon><Close /></el-icon>
                审批驳回
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批备注" prop="remark">
            <el-input
              v-model="approvalForm.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入审批备注（最多500字符）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          提交审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { PendingApprovalListItem } from '@/types/module'
import { submitApproval } from '@/api/modules/workOrderApproval'

// Props
interface Props {
  modelValue: boolean
  approvalData?: PendingApprovalListItem | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const submitting = ref(false)
const approvalFormRef = ref<FormInstance>()

// 审批表单
const approvalForm = ref({
  result: '',
  remark: ''
})

// 表单验证规则
const approvalRules: FormRules = {
  result: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' }
  ]
}

// Mock数据 - 实际应该从详情接口获取
const mockClaimLabor = ref([
  {
    itemCode: 'L001',
    itemName: '发动机检修',
    quantity: 2.5,
    unitPrice: 120,
    claimAmount: 300
  },
  {
    itemCode: 'L002',
    itemName: '零件更换',
    quantity: 1.0,
    unitPrice: 120,
    claimAmount: 120
  }
])

const mockClaimParts = ref([
  {
    itemCode: 'P001',
    itemName: '发动机皮带',
    quantity: 1,
    unitPrice: 280,
    claimAmount: 280
  },
  {
    itemCode: 'P002',
    itemName: '机油滤清器',
    quantity: 1,
    unitPrice: 65,
    claimAmount: 65
  }
])

// 计算属性
const modalTitle = computed(() => {
  if (!props.approvalData) return '审批操作'
  return props.approvalData.approvalType === 'claim_approval' ? '索赔审批' : '取消工单审批'
})

const claimLaborTotal = computed(() => {
  return mockClaimLabor.value.reduce((sum, item) => sum + item.claimAmount, 0)
})

const claimPartsTotal = computed(() => {
  return mockClaimParts.value.reduce((sum, item) => sum + item.claimAmount, 0)
})

const claimTotalAmount = computed(() => {
  return claimLaborTotal.value + claimPartsTotal.value
})

// 方法
const getApprovalTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    claim_approval: '索赔审批',
    cancel_approval: '取消工单审批'
  }
  return typeMap[type] || type
}

const resetForm = () => {
  approvalForm.value = {
    result: '',
    remark: ''
  }
  approvalFormRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}

const handleSubmit = async () => {
  if (!approvalFormRef.value || !props.approvalData) return

  try {
    await approvalFormRef.value.validate()

    await ElMessageBox.confirm(
      `确认${approvalForm.value.result === 'approved' ? '通过' : '驳回'}此审批申请吗？`,
      '确认操作',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    submitting.value = true

    const submitData = {
      approvalNo: props.approvalData.approvalNo,
      approvalResult: approvalForm.value.result,
      approvalRemark: approvalForm.value.remark,
      approverId: 'current_user_id' // 实际应该从用户信息获取
    }

    await submitApproval(submitData)

    ElMessage.success('审批操作成功')
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交审批失败:', error)
      ElMessage.error('审批操作失败')
    }
  } finally {
    submitting.value = false
  }
}

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      resetForm()
    }
  },
  { immediate: true }
)

watch(visible, (newVal) => {
  if (newVal !== props.modelValue) {
    emit('update:modelValue', newVal)
  }
})
</script>

<style scoped>
.approval-modal-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px;
}

.info-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  gap: 24px;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
  min-width: 80px;
}

.claim-table-section {
  margin-bottom: 20px;
}

.table-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
}

.amount-highlight {
  color: #f56c6c;
  font-weight: 600;
}

.claim-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.summary-item.total {
  border-top: 1px solid #e4e7ed;
  padding-top: 8px;
  margin-top: 8px;
  font-size: 16px;
  font-weight: 600;
}

.amount {
  color: #409eff;
  font-weight: 600;
}

.total-amount {
  color: #f56c6c;
  font-size: 18px;
  font-weight: 700;
}

.approval-section {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #409eff;
}

.el-radio {
  margin-right: 24px;
  font-weight: 500;
}

.el-radio .el-icon {
  margin-right: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
    gap: 12px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .claim-summary {
    font-size: 12px;
  }

  .summary-item.total {
    font-size: 14px;
  }

  .total-amount {
    font-size: 16px;
  }
}
</style>
