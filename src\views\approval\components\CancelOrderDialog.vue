<template>
  <el-dialog
    v-model="visible"
    :title="t('approval.cancelOrderApproval')"
    width="1000px"
    :close-on-click-modal="false"
    class="cancel-order-dialog"
  >
    <div class="dialog-content">
      <!-- 基础信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span class="card-header-title">{{ t('approval.basicInfo') }}</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">{{ t('approval.approvalNo') }}:</span>
              <span class="info-value">{{ approvalData?.approvalNo }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.approvalType') }}:</span>
              <span class="info-value">{{ t(`approval.type.${approvalData?.approvalType}`) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.submitter') }}:</span>
              <span class="info-value">{{ approvalData?.submitter }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.submitTime') }}:</span>
              <span class="info-value">{{ approvalData?.submitTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">{{ t('approval.orderNo') }}:</span>
              <span class="info-value">{{ approvalData?.orderNo }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.cancelReason') }}:</span>
              <div class="info-value reason-text">{{ approvalData?.requestReason }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 审批操作区域 -->
      <el-card>
        <template #header>
          <span class="card-header-title">{{ t('approval.approvalOperation') }}</span>
        </template>
        
        <el-form :model="formData" ref="formRef" :rules="formRules" label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('approval.approvalResult')" prop="approvalResult">
                <el-radio-group v-model="formData.approvalResult">
                  <el-radio value="approved">{{ t('approval.approved') }}</el-radio>
                  <el-radio value="rejected">{{ t('approval.rejected') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('approval.approvalRemark')" prop="approvalRemark">
                <el-input
                  v-model="formData.approvalRemark"
                  type="textarea"
                  :rows="4"
                  :placeholder="t('approval.approvalRemarkPlaceholder')"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer-buttons">
        <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitLoading">
          {{ t('approval.submitApproval') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'

const { t } = useI18n()

// Props
const props = defineProps<{
  modelValue: boolean
  approvalData: any
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
  approvalResult: '',
  approvalRemark: ''
})

// 表单验证规则
const formRules: FormRules = {
  approvalResult: [
    { required: true, message: t('approval.approvalResultRequired'), trigger: 'change' }
  ]
}

// 表单引用
const formRef = ref<FormInstance>()
const submitLoading = ref(false)

// 监听弹窗显示状态，重置表单
watch(visible, (newValue) => {
  if (newValue) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formData.approvalResult = ''
  formData.approvalRemark = ''
  formRef.value?.clearValidate()
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 确认提交
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    const isValid = await formRef.value.validate()
    if (!isValid) return
    
    submitLoading.value = true
    
    const submitData = {
      approvalNo: props.approvalData?.approvalNo,
      approvalResult: formData.approvalResult,
      approvalRemark: formData.approvalRemark
    }
    
    emit('confirm', submitData)
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    submitLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.card-header-title {
  font-weight: 600;
  color: $primary-color;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  
  .info-label {
    min-width: 120px;
    color: $text-color-secondary;
    font-weight: 500;
  }
  
  .info-value {
    color: $text-color-primary;
    flex: 1;
    
    &.reason-text {
      line-height: 1.5;
      word-break: break-word;
    }
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  
  .el-button {
    margin-left: 10px;
  }
}

.mb-20 {
  margin-bottom: 20px;
}
</style> 