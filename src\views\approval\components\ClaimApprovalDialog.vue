<template>
  <el-dialog
    v-model="visible"
    :title="t('approval.claimApproval')"
    width="1200px"
    :close-on-click-modal="false"
    class="claim-approval-dialog"
  >
    <div class="dialog-content">
      <!-- 基础信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span class="card-header-title">{{ t('approval.basicInfo') }}</span>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">{{ t('approval.approvalNo') }}:</span>
              <span class="info-value">{{ approvalData?.approvalNo }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.approvalType') }}:</span>
              <span class="info-value">{{ t(`approval.type.${approvalData?.approvalType}`) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.submitter') }}:</span>
              <span class="info-value">{{ approvalData?.submitter }} ({{ approvalData?.submitterNo }})</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.submitTime') }}:</span>
              <span class="info-value">{{ approvalData?.submitTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">{{ t('approval.orderNo') }}:</span>
              <span class="info-value">{{ approvalData?.orderNo }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.requestReason') }}:</span>
              <div class="info-value reason-text">{{ approvalData?.requestReason }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户信息和车辆信息 -->
      <el-row :gutter="20" class="mb-20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span class="card-header-title">{{ t('approval.customerInfo') }}</span>
            </template>

            <div class="info-item">
              <span class="info-label">{{ t('approval.customerName') }}:</span>
              <span class="info-value">{{ approvalData?.customerName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.customerPhone') }}:</span>
              <span class="info-value">{{ approvalData?.customerPhone }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.senderName') }}:</span>
              <span class="info-value">{{ approvalData?.senderName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.senderPhone') }}:</span>
              <span class="info-value">{{ approvalData?.senderPhone }}</span>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card>
            <template #header>
              <span class="card-header-title">{{ t('approval.vehicleInfo') }}</span>
            </template>

            <div class="info-item">
              <span class="info-label">{{ t('approval.licensePlate') }}:</span>
              <span class="info-value">{{ approvalData?.licensePlate }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.vin') }}:</span>
              <span class="info-value">{{ approvalData?.vin }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.vehicleModel') }}:</span>
              <span class="info-value">{{ approvalData?.vehicleModel }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.vehicleColor') }}:</span>
              <span class="info-value">{{ approvalData?.vehicleColor }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.mileage') }}:</span>
              <span class="info-value">{{ approvalData?.mileage }}{{ t('approval.km') }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.vehicleAge') }}:</span>
              <span class="info-value">{{ approvalData?.vehicleAge }}{{ t('approval.months') }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('approval.repairTime') }}:</span>
              <span class="info-value">{{ approvalData?.repairTime }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 索赔内容详情 -->
      <el-card class="mb-20">
        <template #header>
          <span class="card-header-title">{{ t('approval.claimContentDetail') }}</span>
        </template>

        <!-- 索赔工时 -->
        <div class="claim-section">
          <h4 class="section-title">{{ t('approval.claimLabor') }}:</h4>
          <el-table :data="claimLaborData" border class="claim-table">
            <el-table-column prop="laborCode" :label="t('approval.laborCode')" min-width="100" />
            <el-table-column prop="laborName" :label="t('approval.laborName')" min-width="150" />
            <el-table-column prop="standardHours" :label="t('approval.standardHours')" min-width="100" align="right" />
            <el-table-column prop="hourlyRate" :label="t('approval.hourlyRate')" min-width="100" align="right">
              <template #default="{ row }">
                ¥{{ row.hourlyRate }}
              </template>
            </el-table-column>
            <el-table-column prop="claimAmount" :label="t('approval.claimAmount')" min-width="100" align="right" class-name="amount-column">
              <template #default="{ row }">
                <span class="amount-text">¥{{ row.claimAmount }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 索赔零件 -->
        <div class="claim-section">
          <h4 class="section-title">{{ t('approval.claimParts') }}:</h4>
          <el-table :data="claimPartsData" border class="claim-table">
            <el-table-column prop="partCode" :label="t('approval.partCode')" min-width="120" />
            <el-table-column prop="partName" :label="t('approval.partName')" min-width="180" />
            <el-table-column prop="quantity" :label="t('approval.quantity')" min-width="80" align="right" />
            <el-table-column prop="unitPrice" :label="t('approval.unitPrice')" min-width="100" align="right">
              <template #default="{ row }">
                ¥{{ row.unitPrice }}
              </template>
            </el-table-column>
            <el-table-column prop="claimAmount" :label="t('approval.claimAmount')" min-width="100" align="right" class-name="amount-column">
              <template #default="{ row }">
                <span class="amount-text">¥{{ row.claimAmount }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 索赔总金额统计 -->
        <div class="claim-summary">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <span class="summary-label">{{ t('approval.claimLaborTotal') }}:</span>
                <span class="summary-value">¥{{ claimLaborTotal }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <span class="summary-label">{{ t('approval.claimPartsTotal') }}:</span>
                <span class="summary-value">¥{{ claimPartsTotal }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <span class="summary-label">{{ t('approval.claimTotalAmount') }}:</span>
                <span class="summary-value total-amount">¥{{ claimTotalAmount }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 审批操作区域 -->
      <el-card>
        <template #header>
          <span class="card-header-title">{{ t('approval.approvalOperation') }}</span>
        </template>

        <el-form :model="formData" ref="formRef" :rules="formRules" label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('approval.approvalResult')" prop="approvalResult">
                <el-radio-group v-model="formData.approvalResult">
                  <el-radio value="approved">{{ t('approval.approved') }}</el-radio>
                  <el-radio value="rejected">{{ t('approval.rejected') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('approval.approvalRemark')" prop="approvalRemark">
                <el-input
                  v-model="formData.approvalRemark"
                  type="textarea"
                  :rows="4"
                  :placeholder="t('approval.approvalRemarkPlaceholder')"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer-buttons">
        <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitLoading">
          {{ t('approval.submitApproval') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { ApprovalRequest, ClaimLaborItem, ClaimPartItem, ApprovalFormData } from '@/types/approval'
import type { ApprovalListItem } from '@/types/approval'

const { t } = useI18n()

// Props
const props = defineProps<{
  modelValue: boolean
  approvalData: ApprovalListItem | null
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
  approvalResult: '',
  approvalRemark: ''
})

// 表单验证规则
const formRules: FormRules = {
  approvalResult: [
    { required: true, message: t('approval.approvalResultRequired'), trigger: 'change' }
  ],
  approvalRemark: [
    {
      validator: (rule, value, callback) => {
        if (formData.approvalResult === 'rejected' && !value) {
          callback(new Error(t('approval.approvalRemarkRequiredWhenRejected')))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 表单引用
const formRef = ref<FormInstance>()
const submitLoading = ref(false)

// 索赔工时数据
const claimLaborData = computed(() => {
  return props.approvalData?.claimLabor || []
})

// 索赔零件数据
const claimPartsData = computed(() => {
  return props.approvalData?.claimParts || []
})

// 索赔工时总额
const claimLaborTotal = computed(() => {
  return claimLaborData.value.reduce((sum, item) => sum + item.claimAmount, 0).toFixed(2)
})

// 索赔零件总额
const claimPartsTotal = computed(() => {
  return claimPartsData.value.reduce((sum, item: any) => sum + item.claimAmount, 0).toFixed(2)
})

// 索赔总金额
const claimTotalAmount = computed(() => {
  return (Number(claimLaborTotal.value) + Number(claimPartsTotal.value)).toFixed(2)
})

// 监听弹窗显示状态，重置表单
watch(visible, (newValue) => {
  if (newValue) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formData.approvalResult = ''
  formData.approvalRemark = ''
  formRef.value?.clearValidate()
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 确认提交
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    const isValid = await formRef.value.validate()
    if (!isValid) return

    submitLoading.value = true

    const submitData = {
      approvalNo: props.approvalData?.approvalNo,
      approvalResult: formData.approvalResult,
      approvalRemark: formData.approvalRemark
    }

    emit('confirm', submitData)
  } catch (error) {
    console.error('Validation failed:', error)
  } finally {
    submitLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.claim-approval-dialog {
  .dialog-content {
    max-height: 80vh;
    overflow-y: auto;
  }
}

.card-header-title {
  font-weight: 600;
  color: $primary-color;
}

.info-item {
  display: flex;
  margin-bottom: 12px;

  .info-label {
    min-width: 120px;
    color: $text-color-secondary;
    font-weight: 500;
  }

  .info-value {
    color: $text-color-primary;
    flex: 1;

    &.reason-text {
      line-height: 1.5;
      word-break: break-word;
    }
  }
}

.claim-section {
  margin-bottom: 20px;

  .section-title {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: $text-color-primary;
  }

  .claim-table {
    margin-bottom: 20px;

    :deep(.amount-column) {
      .cell {
        .amount-text {
          font-weight: 600;
          color: $primary-color;
        }
      }
    }
  }
}

.claim-summary {
  padding: 20px;
  background-color: $background-color;
  border-radius: 8px;

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    .summary-label {
      font-weight: 500;
      color: $text-color-secondary;
    }

    .summary-value {
      font-weight: 600;
      color: $text-color-primary;
      font-size: 16px;

      &.total-amount {
        color: $primary-color;
        font-size: 18px;
      }
    }
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;

  .el-button {
    margin-left: 10px;
  }
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
