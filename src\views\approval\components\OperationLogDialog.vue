<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="t('workOrderApproval.operationLog')"
    width="800px"
    height="600px"
    class="operation-log-dialog"
  >
    <div class="dialog-content">
      <el-table
        :data="operationLogs"
        v-loading="loading"
        :empty-text="t('common.noData')"
        stripe
        border
        height="500"
      >
        <el-table-column
          prop="operation"
          :label="t('workOrderApproval.operationContent')"
          min-width="180"
        >
          <template #default="{ row }">
            <span :class="getOperationClass(row.operation)">
              {{ getOperationText(row.operation) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="operator"
          :label="t('workOrderApproval.operator')"
          min-width="120"
        >
          <template #default="{ row }">
            <el-tag
              :type="getOperatorTagType(row.operator)"
              size="small"
            >
              {{ row.operator }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="operationTime"
          :label="t('workOrderApproval.operationTime')"
          min-width="160"
          show-overflow-tooltip
        />

        <el-table-column
          prop="remark"
          :label="t('workOrderApproval.operationRemark')"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span v-if="row.remark">{{ row.remark }}</span>
            <span v-else class="no-remark">--</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ t('common.close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { workOrderApprovalAPI } from '@/api/modules/workOrderApproval';

const { t } = useI18n();

// Props
interface Props {
  visible: boolean;
  approvalNo?: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 响应式数据
const loading = ref(false);
const operationLogs = ref<Array<{
  operation: string;
  operator: string;
  operationTime: string;
  remark?: string;
}>>([]);

// 方法
const getOperationText = (operation: string): string => {
  const operationMap: Record<string, string> = {
    '提交索赔审批': t('workOrderApproval.submitClaimApproval'),
    '一级审批通过': t('workOrderApproval.firstLevelApprovalPass'),
    '一级审批驳回': t('workOrderApproval.firstLevelApprovalReject'),
    '二级审批通过': t('workOrderApproval.secondLevelApprovalPass'),
    '二级审批驳回': t('workOrderApproval.secondLevelApprovalReject'),
    '提交取消申请': t('workOrderApproval.submitCancelRequest'),
    '取消审批通过': t('workOrderApproval.cancelApprovalPass'),
    '取消审批驳回': t('workOrderApproval.cancelApprovalReject')
  };
  return operationMap[operation] || operation;
};

const getOperationClass = (operation: string): string => {
  if (operation.includes('通过')) {
    return 'operation-success';
  } else if (operation.includes('驳回')) {
    return 'operation-danger';
  } else if (operation.includes('提交')) {
    return 'operation-primary';
  }
  return 'operation-default';
};

const getOperatorTagType = (operator: string): string => {
  if (operator === t('workOrderApproval.systemOperation') || operator === '系统') {
    return 'info';
  } else if (operator.includes(t('workOrderApproval.technicianManager')) || operator.includes('技师经理')) {
    return 'primary';
  } else if (operator.includes(t('workOrderApproval.factoryManager')) || operator.includes('厂端管理人员')) {
    return 'success';
  } else if (operator.includes(t('workOrderApproval.serviceAdvisor')) || operator.includes('服务顾问')) {
    return 'warning';
  }
  return 'primary';
};

const loadOperationLogs = async () => {
  if (!props.approvalNo) return;

  try {
    loading.value = true;
    operationLogs.value = await workOrderApprovalAPI.getOperationLog(props.approvalNo);
  } catch (error) {
    ElMessage.error(t('common.operationFailed'));
    console.error('Failed to load operation logs:', error);
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  emit('update:visible', false);
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.approvalNo) {
    loadOperationLogs();
  }
});
</script>

<style scoped lang="scss">
.operation-log-dialog {
  .dialog-content {
    .operation-success {
      color: #67c23a;
      font-weight: 500;
    }

    .operation-danger {
      color: #f56c6c;
      font-weight: 500;
    }

    .operation-primary {
      color: #409eff;
      font-weight: 500;
    }

    .operation-default {
      color: #606266;
    }

    .no-remark {
      color: #c0c4cc;
      font-style: italic;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
