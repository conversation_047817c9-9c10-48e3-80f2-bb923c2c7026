<template>
  <el-dialog
    v-model="dialogVisible"
    title="操作日志"
    width="800px"
    :before-close="handleClose"
  >
    <div class="operation-log-content">
      <el-table
        :data="operationLogs"
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="operationType" label="操作类型" width="120" />
        <el-table-column prop="operator" label="操作人" width="100" />
        <el-table-column prop="operationTime" label="操作时间" width="170" />
        <el-table-column prop="operationRemark" label="操作备注" />
      </el-table>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && operationLogs.length === 0"
        description="暂无操作日志"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  approvalNo: {
    type: String,
    default: ''
  }
})

// 定义 emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const operationLogs = ref<any[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 加载操作日志
const loadOperationLogs = async () => {
  if (!props.approvalNo) return

  try {
    loading.value = true
    // 模拟数据
    operationLogs.value = [
      {
        logId: '1',
        operationType: '提交申请',
        operator: '张三',
        operationTime: '2024-12-10 09:30:00',
        operationRemark: '提交索赔申请'
      },
      {
        logId: '2',
        operationType: '系统提醒',
        operator: '系统',
        operationTime: '2024-12-10 15:30:00',
        operationRemark: '审批即将超时提醒'
      }
    ]
  } catch (error) {
    console.error('加载操作日志失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭弹框
const handleClose = () => {
  dialogVisible.value = false
}

// 监听弹框显示状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && props.approvalNo) {
      loadOperationLogs()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.operation-log-content {
  max-height: 400px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}
</style>
