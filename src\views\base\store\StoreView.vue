<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('store.title') }}</h1>

    <!-- 搜索表单组件 -->
    <StoreSearchForm
      v-model="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 表格组件 -->
    <StoreTable
      :loading="loading"
      :table-data="tableData"
      :pagination="pagination"
      @add="handleAdd"
      @edit="handleEdit"
      @view="handleView"
      @delete="handleDelete"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 表单弹窗组件 -->
    <StoreFormDialog
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :is-view="isView"
      :store-data="currentStoreData"
      :store-options="storeOptions"
      :loading="formLoading"
      @submit="handleFormSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type {
  StoreItem,
  StoreSearchParams,
  CreateStoreRequest,
  UpdateStoreRequest
} from '@/types/base/store';
import {
  getStorePage,
  getStoreDetail,
  addStore,
  updateStore,
  deleteStore
} from '@/api/modules/base/store';
import StoreSearchForm from './components/StoreSearchForm.vue';
import StoreTable from './components/StoreTable.vue';
import StoreFormDialog from './components/StoreFormDialog.vue';

const { t, tc } = useModuleI18n('base');

const loading = ref(false);
const formLoading = ref(false);
const tableData = ref<StoreItem[]>([]);
const storeOptions = ref<StoreItem[]>([]);
const dialogVisible = ref(false);

const isEdit = ref(false);
const isView = ref(false);
const currentStoreData = ref<StoreItem | undefined>(undefined);

// 注意：字典数据现在通过各个组件内部的字典系统获取
// 不再需要在主页面中管理字典数据

// ✅ 使用MyBatisPlus标准分页参数
const pagination = reactive({
  current: 1,        // ✅ 符合MyBatisPlus标准
  size: 10,          // ✅ 符合MyBatisPlus标准
  total: 0,
});

const searchParams = reactive<StoreSearchParams>({
  storeName: '',
  storeCode: '',
  storeStatus: ''
});

// 构建树形结构
const buildTree = (stores: StoreItem[]): StoreItem[] => {
  const storeMap = new Map(stores.map(store => [store.id, { ...store, children: [] }]));
  const tree: StoreItem[] = [];

  const root = stores.find(s => s.storeType === '02010001');

  if (root) {
    const rootNode = storeMap.get(root.id)!;
    tree.push(rootNode);

    stores.forEach(store => {
      if (store.id === root.id) return;

      const childNode = storeMap.get(store.id)!;
      if (store.parentId === root.id || !store.parentId) {
        rootNode.children!.push(childNode);
      }
    });

    stores.forEach(store => {
      if (store.parentId && store.parentId !== root.id) {
        const parentNode = storeMap.get(store.parentId);
        if (parentNode) {
          const childNode = storeMap.get(store.id)!;
          if (!parentNode.children!.some(c => c.id === childNode.id)) {
            parentNode.children!.push(childNode);
          }
        }
      }
    });
  } else {
    stores.forEach(store => {
      if (store.parentId) {
        const parent = storeMap.get(store.parentId);
        if (parent) {
          parent.children!.push(storeMap.get(store.id)!);
        } else {
          tree.push(storeMap.get(store.id)!);
        }
      } else {
        tree.push(storeMap.get(store.id)!);
      }
    });
  }

  return tree;
};

// ✅ 标准数据加载函数
const loadData = async () => {
  try {
    loading.value = true;

    const queryParams = {
      ...searchParams,
      current: pagination.current,    // ✅ 使用MyBatisPlus标准参数
      size: pagination.size          // ✅ 使用MyBatisPlus标准参数
    };

    // 添加调试信息
    console.log('🔍 门店查询参数:', queryParams);
    console.log('📋 搜索条件:', {
      storeName: searchParams.storeName,
      storeCode: searchParams.storeCode,
      storeStatus: searchParams.storeStatus
    });

    const response = await getStorePage(queryParams);

    // ✅ 标准响应处理
    if (response.code === '200' && response.success) {
      // ✅ 使用response.result.records
      tableData.value = response.result.records || [];
      // ✅ 使用response.result.total
      pagination.total = response.result.total || 0;
      pagination.current = response.result.current || pagination.current;

      // 获取所有门店节点用于父门店选择器
      const flattenStores = (stores: StoreItem[]): StoreItem[] => {
        const result: StoreItem[] = [];
        stores.forEach(store => {
          result.push(store);
          if (store.children && store.children.length > 0) {
            result.push(...flattenStores(store.children));
          }
        });
        return result;
      };
      storeOptions.value = flattenStores(tableData.value);
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取门店列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索事件处理
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  searchParams.storeName = '';
  searchParams.storeCode = '';
  searchParams.storeStatus = '';
  pagination.current = 1;
  loadData();
};

// 表格事件处理
const handleAdd = () => {
  isEdit.value = false;
  isView.value = false;
  currentStoreData.value = undefined;
  dialogVisible.value = true;
};

const handleEdit = (row: StoreItem) => {
  isEdit.value = true;
  isView.value = false;
  currentStoreData.value = { ...row };
  dialogVisible.value = true;
};

const handleView = async (row: StoreItem) => {
  try {
    loading.value = true;
    const response = await getStoreDetail(row.id);

    if (response.code === '200' && response.success) {
      isEdit.value = false;
      isView.value = true;
      currentStoreData.value = response.result;
      dialogVisible.value = true;
    } else {
      ElMessage.error(response.message || tc('loadFailed'));
    }
  } catch (error) {
    console.error('获取门店详情失败:', error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false;
  }
};

const handleDelete = async (row: StoreItem) => {
  try {
    await ElMessageBox.confirm(
      tc('confirmDelete', { item: row.storeName }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );

    const response = await deleteStore(row.id);
    if (response.code === '200' && response.success) {
      ElMessage.success(tc('success'));
      loadData();
    } else {
      ElMessage.error(response.message || tc('operationFailed'));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
    }
  }
};

// 表单提交处理
const handleFormSubmit = async (data: CreateStoreRequest | UpdateStoreRequest) => {
  try {
    formLoading.value = true;

    let response: any;
    if (isEdit.value) {
      response = await updateStore(data as UpdateStoreRequest);
    } else {
      response = await addStore(data as CreateStoreRequest);
    }

    if (response.code === '200' && response.success) {
      ElMessage.success(tc('success'));
      dialogVisible.value = false;
      loadData();
    } else {
      ElMessage.error(response.message || tc('operationFailed'));
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    formLoading.value = false;
  }
};

// ✅ 修正分页处理函数
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;    // ✅ 使用current
  loadData();
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;  // ✅ 使用current
  loadData();
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__label {
    margin-bottom: 8px;
  }
}

.table-toolbar {
  display: flex;
  gap: 8px;
}

.dialog-form-modern {
  .el-form-item {
    margin-bottom: 22px;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}
</style>