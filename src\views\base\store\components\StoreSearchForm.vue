<template>
  <el-card class="search-card mb-20" shadow="never">
    <el-form :model="props.modelValue" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('store.storeCode')">
            <el-input
              v-model="storeCodeModel"
              :placeholder="tc('inputPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('store.storeName')">
            <el-input
              v-model="storeNameModel"
              :placeholder="tc('inputPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('store.storeStatus')">
            <DictionarySelect
              v-model="storeStatusModel"
              :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
              :placeholder="tc('pleaseSelect')"
              show-all
              :all-label="tc('all')"
              all-value=""
              @change="handleSearch"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6" style="text-align: right;">
          <el-form-item label="&nbsp;">
            <el-button 
              type="primary" 
              :icon="Search" 
              @click="handleSearch"
            >
              {{ tc('search') }}
            </el-button>
            <el-button 
              :icon="RefreshLeft" 
              @click="handleReset"
            >
              {{ tc('reset') }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
// 移除不再需要的reactive和watch导入
import { computed } from 'vue';
import { Search, RefreshLeft } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { StoreSearchParams } from '@/types/base/store';

interface Props {
  modelValue: StoreSearchParams;
}

interface Emits {
  (e: 'update:modelValue', value: StoreSearchParams): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('base');

// 创建计算属性用于双向绑定
const storeCodeModel = computed({
  get: () => props.modelValue.storeCode || '',
  set: (value: string) => emit('update:modelValue', { ...props.modelValue, storeCode: value })
});

const storeNameModel = computed({
  get: () => props.modelValue.storeName || '',
  set: (value: string) => emit('update:modelValue', { ...props.modelValue, storeName: value })
});

// 处理状态下拉框变化
const handleStatusChange = (value: string | number | string[] | number[] | null) => {
  // 处理不同类型的值，确保转换为字符串
  let statusValue = '';
  if (Array.isArray(value)) {
    statusValue = value.length > 0 ? String(value[0]) : '';
  } else {
    statusValue = String(value || '');
  }

  console.log('🔍 状态下拉框变化:', value, '-> 转换后:', statusValue);

  // 构建新的搜索参数
  const newSearchParams = { ...props.modelValue, storeStatus: statusValue };
  console.log('🔍 准备更新的搜索参数:', newSearchParams);

  // 先更新数据
  emit('update:modelValue', newSearchParams);

  // 触发搜索事件
  console.log('🔍 状态变化后立即触发搜索');
  emit('search');
};

const handleSearch = () => {
  console.log('🔍 搜索表单触发查询，当前参数:', props.modelValue);
  emit('search');
};

const handleReset = () => {
  emit('update:modelValue', {
    storeName: '',
    storeCode: '',
    storeStatus: ''
  });
  emit('reset');
};
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__label {
    margin-bottom: 8px;
  }
}
</style>
