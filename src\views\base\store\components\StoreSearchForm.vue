<template>
  <el-card class="search-card mb-20" shadow="never">
    <el-form :model="props.modelValue" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('store.storeCode')">
            <el-input
              :model-value="props.modelValue.storeCode"
              :placeholder="tc('inputPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleSearch"
              @input="(value) => handleInputChange('storeCode', value)"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('store.storeName')">
            <el-input
              :model-value="props.modelValue.storeName"
              :placeholder="tc('inputPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleSearch"
              @input="(value) => handleInputChange('storeName', value)"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('store.storeStatus')">
            <DictionarySelect
              :model-value="props.modelValue.storeStatus"
              :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
              :placeholder="tc('pleaseSelect')"
              show-all
              :all-label="tc('all')"
              all-value=""
              @update:model-value="(value) => emit('update:modelValue', { ...props.modelValue, storeStatus: String(value || '') })"
              @change="handleSearch"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6" style="text-align: right;">
          <el-form-item label="&nbsp;">
            <el-button 
              type="primary" 
              :icon="Search" 
              @click="handleSearch"
            >
              {{ tc('search') }}
            </el-button>
            <el-button 
              :icon="RefreshLeft" 
              @click="handleReset"
            >
              {{ tc('reset') }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
// 移除不再需要的reactive和watch导入
import { Search, RefreshLeft } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { StoreSearchParams } from '@/types/base/store';

interface Props {
  modelValue: StoreSearchParams;
}

interface Emits {
  (e: 'update:modelValue', value: StoreSearchParams): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('base');

// 移除内部searchParams，直接使用props.modelValue

// 处理输入框变化
const handleInputChange = (field: keyof StoreSearchParams, value: string) => {
  console.log(`🔍 输入框变化 - ${field}:`, value);
  emit('update:modelValue', { ...props.modelValue, [field]: value });
  // 注意：这里不自动触发搜索，需要用户点击搜索按钮或按回车键
};

const handleSearch = () => {
  console.log('🔍 搜索表单触发查询，当前参数:', props.modelValue);
  emit('search');
};

const handleReset = () => {
  emit('update:modelValue', {
    storeName: '',
    storeCode: '',
    storeStatus: ''
  });
  emit('reset');
};
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__label {
    margin-bottom: 8px;
  }
}
</style>
