<template>
  <el-card class="search-card mb-20" shadow="never">
    <el-form class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('store.storeCode')">
            <el-input
              v-model="searchForm.storeCode"
              :placeholder="tc('inputPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('store.storeName')">
            <el-input
              v-model="searchForm.storeName"
              :placeholder="tc('inputPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('store.storeStatus')">
            <DictionarySelect
              v-model="searchForm.storeStatus"
              :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
              :placeholder="tc('pleaseSelect')"
              show-all
              :all-label="tc('all')"
              all-value=""
              @change="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6" style="text-align: right;">
          <el-form-item label="&nbsp;">
            <el-button
              type="primary"
              :icon="Search"
              @click="handleSearch"
            >
              {{ tc('search') }}
            </el-button>
            <el-button
              :icon="RefreshLeft"
              @click="handleReset"
            >
              {{ tc('reset') }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';
import { Search, RefreshLeft } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { StoreSearchParams } from '@/types/base/store';

interface Props {
  modelValue: StoreSearchParams;
}

interface Emits {
  (e: 'update:modelValue', value: StoreSearchParams): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('base');

// 创建本地响应式表单数据
const searchForm = reactive<StoreSearchParams>({
  storeName: '',
  storeCode: '',
  storeStatus: ''
});

// 监听 props 变化，同步到本地表单
watch(() => props.modelValue, (newValue) => {
  searchForm.storeName = newValue.storeName || '';
  searchForm.storeCode = newValue.storeCode || '';
  searchForm.storeStatus = newValue.storeStatus || '';
}, { immediate: true });

// 监听本地表单变化，同步到父组件
watch(searchForm, (newValue) => {
  emit('update:modelValue', { ...newValue });
}, { deep: true });

const handleSearch = () => {
  console.log('🔍 搜索表单触发查询，当前参数:', searchForm);
  emit('search');
};

const handleReset = () => {
  searchForm.storeName = '';
  searchForm.storeCode = '';
  searchForm.storeStatus = '';
  emit('reset');
};
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__label {
    margin-bottom: 8px;
  }
}
</style>
