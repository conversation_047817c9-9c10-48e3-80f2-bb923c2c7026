<template>
  <el-card class="table-card" shadow="never">
    <!-- 操作工具栏 -->
    <div class="table-toolbar mb-20">
      <el-button
        v-permission="'system:store:create'"
        type="primary"
        :icon="Plus"
        @click="handleAdd"
      >
        {{ t('store.addStore') }}
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      row-key="id"
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column 
        type="index" 
        :label="tc('index')" 
        width="60" 
        align="center" 
      />
      
      <el-table-column 
        :label="t('store.storeCode')" 
        prop="storeCode" 
        width="200" 
      />
      
      <el-table-column
        :label="t('store.storeName')"
        prop="storeName"
        min-width="150"
        show-overflow-tooltip
      />

      <el-table-column
        :label="t('store.storeShortName')"
        prop="storeShortName"
        width="120"
        show-overflow-tooltip
      />

      <el-table-column
        :label="t('store.storeProperties')"
        prop="storeProperties"
        width="150"
      >
        <template #default="{ row }">
          <span>{{ formatStoreProperties(row.storeProperties) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column 
        :label="t('store.storeType')" 
        prop="storeType" 
        width="120"
      >
        <template #default="{ row }">
          <span>{{ formatStoreType(row.storeType) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column 
        :label="t('store.manager')" 
        prop="manager" 
        width="120" 
      />
      
      <el-table-column 
        :label="t('store.contactPhone')" 
        prop="contactPhone" 
        width="150" 
      />
      
      <el-table-column 
        :label="t('store.storeStatus')" 
        prop="storeStatus" 
        width="120" 
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="getStoreStatusType(row.storeStatus)">
            {{ formatStoreStatus(row.storeStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        :label="tc('createTime')" 
        prop="createTime" 
        width="180" 
      />
      
      <el-table-column 
        :label="tc('operations')" 
        width="240" 
        fixed="right" 
        align="center"
      >
        <template #default="{ row }">
          <el-button
            type="info"
            :icon="View"
            link
            @click="handleView(row)"
          >
            {{ tc('view') }}
          </el-button>
          
          <el-button
            v-permission="'system:store:update'"
            type="primary"
            :icon="Edit"
            link
            @click="handleEdit(row)"
          >
            {{ tc('edit') }}
          </el-button>
          
          <el-button
            v-permission="'system:store:delete'"
            type="danger"
            :icon="Delete"
            link
            @click="handleDelete(row)"
          >
            {{ tc('delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      v-if="pagination.total > 0"
      class="mt-20"
      :current-page="pagination.current"
      :page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Plus, Edit, Delete, View } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { StoreItem } from '@/types/base/store';

interface Props {
  loading: boolean;
  tableData: StoreItem[];
  pagination: {
    current: number;
    size: number;
    total: number;
  };
}

interface Emits {
  (e: 'add'): void;
  (e: 'edit', row: StoreItem): void;
  (e: 'view', row: StoreItem): void;
  (e: 'delete', row: StoreItem): void;
  (e: 'size-change', size: number): void;
  (e: 'current-change', current: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('base');

// 使用字典系统获取门店状态和门店属性
const {
  getOptions: getDictionaryOptions,
  getNameByCode
} = useBatchDictionary([
  DICTIONARY_TYPES.COMMON_STATUS,      // 门店状态
  DICTIONARY_TYPES.STORE_TYPE,         // 门店类型
  DICTIONARY_TYPES.STORE_PROPERTIES    // 门店属性
]);

// ✅ 标准字典转义函数
const formatStoreStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.COMMON_STATUS, status) || status;
};

const formatStoreProperties = (properties: string[]) => {
  if (!properties || properties.length === 0) return '-';
  return properties.map(prop => {
    return getNameByCode(DICTIONARY_TYPES.STORE_PROPERTIES, prop) || prop;
  }).join(', ');
};

const formatStoreType = (type: string) => {
 return getNameByCode(DICTIONARY_TYPES.STORE_TYPE, type) || type;
};

// ✅ 状态标签类型映射
const getStoreStatusType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusTypeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '00020001': 'success',  // 正常
    '00020002': 'danger'    // 禁用
  };
  return statusTypeMap[status] || 'info';
};

// 事件处理函数
const handleAdd = () => {
  emit('add');
};

const handleEdit = (row: StoreItem) => {
  emit('edit', row);
};

const handleView = (row: StoreItem) => {
  emit('view', row);
};

const handleDelete = (row: StoreItem) => {
  emit('delete', row);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleCurrentChange = (current: number) => {
  emit('current-change', current);
};
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.table-toolbar {
  display: flex;
  gap: 8px;
}
</style>
