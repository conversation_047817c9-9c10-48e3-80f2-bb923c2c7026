<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('payment.title') }}</h1>

    <!-- 筛选条件区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('payment.orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('payment.enterOrderNumber')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('payment.buyerName')">
              <el-input
                v-model="searchParams.buyerName"
                :placeholder="t('payment.enterBuyerName')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('payment.buyerPhone')">
              <el-input
                v-model="searchParams.buyerPhone"
                :placeholder="t('payment.enterBuyerPhone')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('payment.orderStatus')">
              <el-select
                v-model="searchParams.orderStatus"
                :placeholder="t('payment.selectOrderStatus')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.ORDER_STATUS)"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('payment.paymentStatus')">
              <el-select
                v-model="searchParams.paymentStatus"
                :placeholder="t('payment.selectPaymentStatus')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.PAYMENT_STATUS)"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('payment.orderCreateTime')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                :start-placeholder="t('common.startDate')"
                :end-placeholder="t('common.endDate')"
                value-format="YYYY-MM-DD"
                :placeholder="t('payment.selectDateRange')"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('payment.canInvoice')">
              <el-select
                v-model="searchParams.canInvoice"
                :placeholder="t('payment.selectCanInvoice')"
                clearable
              >
                <el-option :label="t('common.yes')" :value="true" />
                <el-option :label="t('common.no')" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ t('common.search') }}
              </el-button>
              <el-button @click="resetSearch">{{ t('common.reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#fafafa', fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="t('common.index')" width="80" fixed="left" />
        <el-table-column prop="orderNumber" :label="t('payment.orderNumber')" width="120" sortable />
        <el-table-column prop="buyerName" :label="t('payment.buyerName')" width="100" />
        <el-table-column prop="buyerPhone" :label="t('payment.buyerPhone')" width="120">
          <template #default="scope">
            {{ formatPhone(scope.row.buyerPhone) }}
          </template>
        </el-table-column>
        <el-table-column prop="dealerStoreName" :label="t('payment.dealerStore')" width="100" />
        <el-table-column prop="salesConsultantName" :label="t('payment.salesConsultant')" width="100" />
        <el-table-column prop="vin" :label="t('payment.vin')" width="120" />
        <el-table-column prop="model" :label="t('payment.model')" width="100" />
        <el-table-column prop="variant" :label="t('payment.variant')" width="100" />
        <el-table-column prop="color" :label="t('payment.color')" width="80" />
        <el-table-column prop="orderCreateTime" :label="t('payment.orderCreateTime')" width="150" sortable />
        <el-table-column prop="orderStatus" :label="t('payment.orderStatus')" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="t('payment.paymentStatus')" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="vehicleSalesPrice" :label="t('payment.vehicleSalesPrice')" width="100" align="right">
          <template #default="scope">
            {{ formatAmount(scope.row.vehicleSalesPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="insuranceAmount" :label="t('payment.insuranceAmount')" width="100" align="right">
          <template #default="scope">
            {{ formatAmount(scope.row.insuranceAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="otrAmount" :label="t('payment.otrAmount')" width="100" align="right">
          <template #default="scope">
            {{ formatAmount(scope.row.otrAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="discountAmount" :label="t('payment.discountAmount')" width="100" align="right">
          <template #default="scope">
            <span style="color: #f56c6c">{{ formatAmount(scope.row.discountAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" :label="t('payment.totalAmount')" width="100" align="right">
          <template #default="scope">
            <strong>{{ formatAmount(scope.row.totalAmount) }}</strong>
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" :label="t('payment.paidAmount')" width="100" align="right">
          <template #default="scope">
            <span style="color: #67c23a; font-weight: bold">{{ formatAmount(scope.row.paidAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unpaidAmount" :label="t('payment.unpaidAmount')" width="100" align="right">
          <template #default="scope">
            <span style="color: #f56c6c; font-weight: bold">{{ formatAmount(scope.row.unpaidAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="loanAmount" :label="t('payment.loanAmount')" width="120" align="right">
          <template #default="scope">
            {{ scope.row.loanAmount ? formatAmount(scope.row.loanAmount) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="canInvoice" :label="t('payment.canInvoice')" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.canInvoice ? 'success' : ''">
              {{ scope.row.canInvoice ? t('common.yes') : t('common.no') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceTime" :label="t('payment.invoiceTime')" width="150">
          <template #default="scope">
            {{ scope.row.invoiceTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="invoiceNumber" :label="t('payment.invoiceNumber')" width="120">
          <template #default="scope">
            {{ scope.row.invoiceNumber || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="t('payment.createTime')" width="150" sortable />
        <el-table-column prop="updateTime" :label="t('payment.updateTime')" width="150" sortable />
        <el-table-column :label="t('common.operations')" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" :icon="View" link @click="handleViewDetail(scope.row)">
              {{ t('payment.orderDetail') }}
            </el-button>
            <el-button
              type="primary"
              :icon="CreditCard"
              link
              @click="handlePaymentOperation(scope.row)"
              :disabled="!canOperatePayment(scope.row.paymentStatus)"
            >
              {{ t('payment.paymentOperation') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 收退款弹窗 -->
    <PaymentOperationDialog
      v-model="paymentDialogVisible"
      :order-id="currentOrderId"
      @refresh="loadData"
    />

    <!-- 详情弹窗 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order-id="currentOrderId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, View, CreditCard } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import { getOrderPaymentList } from '@/api/modules/payment'
import PaymentOperationDialog from './components/PaymentOperationDialog.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import type { OrderPaymentItem, OrderPaymentListParams } from '@/types/module'

const { t } = useI18n()

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS
])

// 响应式数据
const loading = ref(false)
const tableData = ref<OrderPaymentItem[]>([])
const paymentDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentOrderId = ref('')
const dateRange = ref<[string, string] | null>(null)

// 搜索参数
const searchParams = reactive<Omit<OrderPaymentListParams, 'page' | 'pageSize'>>({
  orderNumber: '',
  buyerName: '',
  buyerPhone: '',
  orderStatus: '',
  paymentStatus: '',
  startDate: '',
  endDate: '',
  canInvoice: undefined
})

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal) {
    searchParams.startDate = newVal[0]
    searchParams.endDate = newVal[1]
  } else {
    searchParams.startDate = ''
    searchParams.endDate = ''
  }
})

// 初始化默认时间范围（最近一个月）
const initDefaultDateRange = () => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setMonth(startDate.getMonth() - 1)

  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params: OrderPaymentListParams = {
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    const response = await getOrderPaymentList(params)
    tableData.value = response.list
    pagination.total = response.total
  } catch (error) {
    console.error('Failed to load order payment list:', error)
    ElMessage.error(t('common.loadFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchParams).forEach(key => {
    if (key === 'canInvoice') {
      searchParams[key as keyof typeof searchParams] = undefined
    } else {
      searchParams[key as keyof typeof searchParams] = ''
    }
  })
  dateRange.value = null
  initDefaultDateRange()
  pagination.page = 1
  loadData()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  loadData()
}

// 查看详情
const handleViewDetail = (row: OrderPaymentItem) => {
  currentOrderId.value = row.orderId
  detailDialogVisible.value = true
}

// 收退款操作
const handlePaymentOperation = (row: OrderPaymentItem) => {
  currentOrderId.value = row.orderId
  paymentDialogVisible.value = true
}

// 判断是否可以收退款操作
const canOperatePayment = (paymentStatus: string) => {
  return ['待支付定金', '已支付定金', '待支付尾款', '退款中'].includes(paymentStatus)
}

// 格式化手机号（脱敏）
const formatPhone = (phone: string) => {
  if (!phone || phone.length < 4) return phone
  return phone.replace(/(\d{4})(\d*)(\d{4})/, '$1****$3')
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `RM ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 获取订单状态样式
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已确认': 'success',
    '待审核': 'warning',
    '已取消': 'danger',
    '已交车': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': t('payment.orderStatusSubmitted'),
    '取消审核中': t('payment.orderStatusCancelPending'),
    '取消审核通过': t('payment.orderStatusCancelApproved'),
    '已取消': t('payment.orderStatusCancelled'),
    '已确认': t('payment.orderStatusConfirmed'),
    '待审核': t('payment.orderStatusPendingReview'),
    '已审核': t('payment.orderStatusReviewed'),
    '待交车': t('payment.orderStatusPendingDelivery'),
    '已交车': t('payment.orderStatusDelivered')
  }
  return statusMap[status] || status
}

// 获取支付状态样式
const getPaymentStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已支付尾款': 'success',
    '已支付定金': 'primary',
    '待支付定金': 'warning',
    '退款中': 'danger',
    '退款完成': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '待支付定金': t('payment.paymentStatusPendingDeposit'),
    '已支付定金': t('payment.paymentStatusDepositPaid'),
    '退款中': t('payment.paymentStatusRefunding'),
    '退款完成': t('payment.paymentStatusRefunded'),
    '待支付尾款': t('payment.paymentStatusPendingFinal'),
    '已支付尾款': t('payment.paymentStatusFullyPaid')
  }
  return statusMap[status] || status
}

// 组件挂载
onMounted(() => {
  initDefaultDateRange()
  loadData()
})
</script>

<style scoped>
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
