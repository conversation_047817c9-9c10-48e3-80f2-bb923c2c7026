<template>
  <div class="page-container" v-loading="loading">
    <h1 class="page-title">{{ t('salesOrderEdit.title') }}</h1>

    <!-- 订单基本信息 -->
    <el-card class="mb-20">
      <el-row :gutter="20" class="order-summary-header">
        <el-col :span="12">
          {{ t('orderNumberLabel') }}: <strong id="detailOrderNo">{{ orderDetails.orderNo || '-' }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('createTimeLabel') }}: <strong id="detailCreateTime">{{ orderDetails.createTime || '-' }}</strong>
        </el-col>
      </el-row>
    </el-card>

    <!-- 客户信息 -->
    <el-card class="mb-20 section">
      <h2>{{ t('customerInformation') }}</h2>
      <el-row :gutter="20" class="form-row">
        <el-col :span="12">
          <div class="form-item-static">{{ t('ordererNameLabel') }}: <span class="form-control-static">{{ orderDetails.customerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('ordererPhoneLabel') }}: <span class="form-control-static">{{ orderDetails.customerPhone || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerNameLabel') }}: <span class="form-control-static">{{ orderDetails.customerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerPhoneLabel') }}: <span class="form-control-static">{{ orderDetails.customerPhone || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerIdTypeLabel') }}: <span class="form-control-static">{{ orderDetails.idType || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerIdNumberLabel') }}: <span class="form-control-static">{{ orderDetails.idNumber || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerEmailLabel') }}: <span class="form-control-static">{{ orderDetails.email || '-' }}</span></div>
        </el-col>
        <el-col :span="24">
          <div class="form-item-static">{{ t('buyerAddressLabel') }}: <span class="form-control-static">{{ orderDetails.address || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">>{{ t('buyerStateLabel') }}: <span class="form-control-static">{{ orderDetails.state || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerCityLabel') }}: <span class="form-control-static">{{ orderDetails.city || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerPostcodeLabel') }}: <span class="form-control-static">{{ orderDetails.zipCode || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerTypeLabel') }}: <span class="form-control-static">{{ formatCustomerType(orderDetails.customerType) || '-' }}</span></div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 购车门店信息 -->
    <el-card class="mb-20 section">
      <h2>{{ t('dealershipInformation') }}</h2>
      <el-row :gutter="20" class="form-row">
        <el-col :span="12">
          <div class="form-item-static">{{ t('regionLabel') }}: <span class="form-control-static">{{ orderDetails.storeRegion || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('cityLabel') }}: <span class="form-control-static">{{ orderDetails.dealerCity || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('dealershipLabel') }}: <span class="form-control-static">{{ orderDetails.dealerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('salesAdvisorLabel') }}: <span class="form-control-static">{{ orderDetails.salesConsultantName || '-' }}</span></div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 购车信息 -->
    <el-card class="mb-20 section">
      <h2>{{ t('purchaseInformation') }}</h2>
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane :label="t('vehicleInfoTab')" name="vehicle"></el-tab-pane>
        <el-tab-pane :label="t('invoiceInfoTab')" name="invoice"></el-tab-pane>
        <el-tab-pane :label="t('rightsInfoTab')" name="rights"></el-tab-pane>
        <el-tab-pane :label="t('paymentInfoTab')" name="payment"></el-tab-pane>
        <el-tab-pane :label="t('insuranceInfoTab')" name="insurance"></el-tab-pane>
        <el-tab-pane :label="t('otrInfoTab')" name="otr"></el-tab-pane>
      </el-tabs>

      <div v-if="activeTab === 'vehicle'" class="tab-content-section">
        <h3>{{ t('vehicleInformation') }}</h3>
        <el-form :model="editForm" :rules="formRules" ref="formRef" label-position="top">
          <el-row :gutter="20" class="form-row">
            <el-col :span="12">
              <div class="form-item-static">{{ t('modelLabel') }}: <span class="form-control-static">{{ orderDetails.model || '-' }}</span></div>
            </el-col>
            <el-col :span="12">
              <div class="form-item-static">{{ t('variantLabel') }}: <span class="form-control-static">{{ orderDetails.variant || '-' }}</span></div>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('colorLabel')" prop="color">
                <el-select
                  v-model="editForm.color"
                  :placeholder="t('salesOrderEdit.placeholders.selectColor')"
                  :loading="masterDataLoading"
                >
                  <el-option
                    v-for="color in colorOptions"
                    :key="color.value"
                    :label="color.label"
                    :value="color.value"
                  />
                </el-select>
                <div class="form-tip">{{ t('salesOrderEdit.colorChangeNotice') }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <div class="form-item-static">{{ t('salesSubtotalLabel') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.vehiclePrice) || '-' }}</span></div>
            </el-col>
            <el-col :span="12">
              <div class="form-item-static">{{ t('numberPlatesFeeLabel') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.numberPlatesFee) || '-' }}</span></div>
            </el-col>
            <el-col :span="12">
              <div class="form-item-static">{{ t('vinLabel') }}: <span class="form-control-static">{{ orderDetails.vin || '-' }}</span></div>
            </el-col>
          </el-row>
        </el-form>

        <h4>{{ t('accessoriesInformation') }}</h4>
        <el-table :data="orderDetails.accessories" style="width: 100%" border class="mb-10">
          <el-table-column prop="category" :label="t('accessoryCategory')" width="120"></el-table-column>
          <el-table-column prop="name" :label="t('accessoryName')"></el-table-column>
          <el-table-column prop="price" :label="t('unitPrice')" width="100">
            <template #default="{ row }">
              {{ formatCurrency(row.price) }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" :label="t('quantity')" width="100"></el-table-column>
          <el-table-column prop="total" :label="t('totalPrice')" width="120">
            <template #default="{ row }">
              {{ formatCurrency(row.total) }}
            </template>
          </el-table-column>
        </el-table>
        <div class="total-amount">{{ t('accessoriesTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalAccessoryAmount) }}</span></div>
      </div>

      <div v-if="activeTab === 'invoice'" class="tab-content-section">
        <h3>{{ t('invoiceInfoTab') }}</h3>
        <el-row :gutter="20" class="form-row">
          <el-col :span="12">
            <div class="form-item-static">{{ t('invoicingType') }}: <span class="form-control-static">{{ orderDetails.invoiceType || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('invoicingName') }}: <span class="form-control-static">{{ orderDetails.invoiceName || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('invoicingPhone') }}: <span class="form-control-static">{{ orderDetails.invoicePhone || '-' }}</span></div>
          </el-col>
          <el-col :span="24">
            <div class="form-item-static">{{ t('invoicingAddress') }}: <span class="form-control-static">{{ orderDetails.invoiceAddress || '-' }}</span></div>
          </el-col>
        </el-row>
      </div>

      <div v-if="activeTab === 'rights'" class="tab-content-section">
        <h3>{{ t('rightsInfoTab') }}</h3>
        <div style="margin-bottom: 16px;">
          <el-button type="primary" @click="handleSelectRights">{{ t('addRights') }}</el-button>
        </div>
        <el-table :data="editForm.selectedRights" style="width: 100%" border class="mb-10">
          <el-table-column prop="code" :label="t('rightCode')" width="120"></el-table-column>
          <el-table-column prop="name" :label="t('rightName')"></el-table-column>
          <el-table-column prop="mode" :label="t('rightMode')" width="100"></el-table-column>
          <el-table-column prop="discountPrice" :label="t('discountAmount')" width="120">
            <template #default="{ row }">
              {{ formatCurrency(row.discountPrice) }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" :label="t('effectiveDate')" width="120"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('expiryDate')" width="120"></el-table-column>
          <el-table-column :label="tc('actions')" width="100">
            <template #default="{ $index }">
              <el-button link type="danger" @click="handleRemoveRight($index)">{{ tc('delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="total-amount">{{ t('rightsDiscountAmount') }}: <span class="static-value">{{ formatCurrency(calculateRightsDiscount()) }}</span></div>
      </div>

            <div v-if="activeTab === 'payment'" class="tab-content-section">
        <h3>{{ t('paymentInfoTab') }}</h3>
        <el-form :model="editForm" :rules="formRules" ref="formRef" label-position="top">
          <!-- 分期付款时显示的字段 -->
          <template v-if="editForm.paymentMethod === 'LOAN'">
            <!-- 第一行：支付方式、贷款资质审核状态、定金金额、贷款金额 -->
            <el-row :gutter="20" class="form-row">
              <el-col :span="6">
                <el-form-item :label="t('paymentMethod')" prop="paymentMethod">
                  <el-select
                    v-model="editForm.paymentMethod"
                    :placeholder="t('salesOrderEdit.placeholders.selectPaymentMethod')"
                    style="width: 100%"
                    :loading="dictionaryLoading"
                  >
                    <el-option
                      v-for="option in paymentMethodOptions"
                      :key="option.code"
                      :value="option.code"
                      :label="option.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('loanApprovalStatusField')" prop="loanApprovalStatus">
                  <el-select
                    v-model="editForm.loanApprovalStatus"
                    :placeholder="t('salesOrderEdit.placeholders.selectApprovalStatus')"
                    style="width: 100%"
                    :loading="dictionaryLoading"
                  >
                    <el-option
                      v-for="option in approvalStatusOptions"
                      :key="option.code"
                      :value="option.code"
                      :label="option.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <div class="form-item-static">
                  <div class="form-label">{{ t('depositAmount') }}</div>
                  <div class="form-control-static">{{ formatCurrency(orderDetails.depositAmount) || '-' }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('loanAmount')" prop="loanAmount">
                  <el-input-number
                    v-model="editForm.loanAmount"
                    :min="0"
                    :precision="2"
                    :controls="false"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 第二行：贷款期数、尾款金额 -->
            <el-row :gutter="20" class="form-row">
              <el-col :span="12">
                <el-form-item :label="t('salesOrderEdit.loanTermLabel')" prop="loanTerm">
                  <el-select v-model="editForm.loanTerm" :placeholder="t('salesOrderEdit.placeholders.selectLoanTerm')" style="width: 100%">
                    <el-option
                      v-for="term in loanTermOptions"
                      :key="term.value"
                      :label="term.label"
                      :value="term.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <div class="form-item-static">
                  <div class="form-label">{{ t('balanceAmount') }}</div>
                  <div class="form-control-static">{{ formatCurrency(calculateBalanceAmount()) || '-' }}</div>
                </div>
              </el-col>
            </el-row>
          </template>

          <!-- 全款付款时显示的字段 -->
          <template v-else>
            <!-- 第一行：支付方式、定金金额、全款金额 -->
            <el-row :gutter="20" class="form-row">
              <el-col :span="8">
                <el-form-item :label="t('paymentMethod')" prop="paymentMethod">
                  <el-select
                    v-model="editForm.paymentMethod"
                    :placeholder="t('salesOrderEdit.placeholders.selectPaymentMethod')"
                    style="width: 100%"
                    :loading="dictionaryLoading"
                  >
                    <el-option
                      v-for="option in paymentMethodOptions"
                      :key="option.code"
                      :value="option.code"
                      :label="option.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <div class="form-item-static">
                  <div class="form-label">{{ t('depositAmount') }}</div>
                  <div class="form-control-static">{{ formatCurrency(orderDetails.depositAmount) || '-' }}</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="form-item-static">
                  <div class="form-label">{{ t('salesOrderEdit.fullPaymentAmount') }}</div>
                  <div class="form-control-static">{{ formatCurrency(orderDetails.totalInvoicePrice) || '-' }}</div>
                </div>
              </el-col>
            </el-row>
          </template>
        </el-form>
      </div>

      <div v-if="activeTab === 'insurance'" class="tab-content-section">
        <h3>{{ t('insuranceInfoTab') }}</h3>
        <el-table :data="orderDetails.policies" style="width: 100%" border class="mb-10">
          <el-table-column prop="policyNumber" :label="t('policyNumber')"></el-table-column>
          <el-table-column prop="insuranceType" :label="t('insuranceType')"></el-table-column>
          <el-table-column prop="insuranceCompany" :label="t('insuranceCompany')"></el-table-column>
          <el-table-column prop="effectiveDate" :label="t('effectiveDate')"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('expiryDate')"></el-table-column>
          <el-table-column prop="price" :label="t('insurancePrice')">
            <template #default="{ row }">
              {{ formatCurrency(row.price) }}
            </template>
          </el-table-column>
        </el-table>

        <el-form-item :label="t('insuranceNotes')">
          <el-input
            type="textarea"
            v-model="editForm.insuranceNotes"
            :placeholder="t('salesOrderEdit.placeholders.insuranceNotes')"
            rows="4"
          />
        </el-form-item>
        <div class="total-amount">{{ t('insuranceTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalInsuranceAmount) }}</span></div>
      </div>

      <div v-if="activeTab === 'otr'" class="tab-content-section">
        <h3>{{ t('otrFeesTab') }}</h3>
        <el-table :data="orderDetails.otrFees" style="width: 100%" border class="mb-10">
          <el-table-column prop="invoiceNumber" :label="t('ticketNumber')"></el-table-column>
          <el-table-column prop="item" :label="t('feeItem')"></el-table-column>
          <el-table-column prop="price" :label="t('feePrice')">
            <template #default="{ row }">
              {{ formatCurrency(row.price) }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" :label="t('effectiveDate')"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('expiryDate')"></el-table-column>
        </el-table>
        <div class="total-amount">{{ t('otrFeesTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalOtrAmount) }}</span></div>
      </div>
    </el-card>

    <!-- 底部价格栏 -->
    <div class="footer-bar">
      <div class="price-summary">
        <span>{{ t('totalInvoiceAmount') }}: <span class="total-price">{{ formatCurrency(orderDetails.totalInvoicePrice) }}</span></span>
        <span>{{ t('remainingAmount') }}: <span class="remaining-amount">{{ formatCurrency(orderDetails.remainingAmount) }}</span></span>
      </div>
      <div class="buttons">
        <el-button @click="goToList">{{ tc('back') }}</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">{{ tc('save') }}</el-button>
      </div>
    </div>

    <!-- 权益选择弹窗 -->
    <RightsSelectionDialog
      v-model:visible="rightsDialogVisible"
      :selected-rights="editForm.selectedRights"
      @confirm="handleRightsConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { getVehicleColorList } from '@/api/modules/masterData'
import type {
  SalesOrderDetail,
  SaveOrderRequest,
  SalesOrderRight,
  PaymentMethod,
  BuyerType,
  LoanApprovalStatus
} from '@/types/module.d'
import type { VehicleColor } from '@/api/modules/masterData'
import { getSalesOrderDetail, saveSalesOrder } from '@/api/modules/order'
import RightsSelectionDialog from './components/RightsSelectionDialog.vue'

const { t, tc } = useModuleI18n('sales.salesOrderManagement')
const route = useRoute()
const router = useRouter()

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS
])

// 主数据
const vehicleColorOptions = ref<VehicleColor[]>([])
const masterDataLoading = ref(false)

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const orderDetails = ref<SalesOrderDetail>({} as SalesOrderDetail)
const formRef = ref<FormInstance>()
const rightsDialogVisible = ref(false)
const activeTab = ref('vehicle')

// 编辑表单
const editForm = reactive({
  color: '',
  selectedRights: [] as SalesOrderRight[],
  paymentMethod: '',
  loanAmount: 0,
  loanTerm: undefined as number | undefined,
  loanApprovalStatus: 'pending_review' as LoanApprovalStatus,
  insuranceNotes: ''
})

// 表单验证规则
const formRules = computed<FormRules>(() => ({
  color: [
    { required: true, message: t('salesOrderEdit.validation.selectColor'), trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: t('salesOrderEdit.validation.selectPaymentMethod'), trigger: 'change' }
  ],
  loanAmount: [
    {
      validator: (rule, value, callback) => {
        if (editForm.paymentMethod === 'LOAN') {
          if (!value || value <= 0) {
            callback(new Error(t('salesOrderEdit.validation.enterLoanAmount')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}))


// 获取车辆颜色数据
const fetchVehicleColorData = async () => {
  try {
    masterDataLoading.value = true
    const colors = await getVehicleColorList()
    vehicleColorOptions.value = colors
  } catch (error) {
    console.error('获取车辆颜色数据失败:', error)
    ElMessage.error('获取车辆颜色数据失败')
    vehicleColorOptions.value = []
  } finally {
    masterDataLoading.value = false
  }
}

// 选项数据 - 使用字典和主数据接口
const colorOptions = computed(() => vehicleColorOptions.value.map(color => ({
  label: color.name,
  value: color.code
})))

const paymentMethodOptions = computed(() => getOptions(DICTIONARY_TYPES.PAYMENT_STATUS))
const approvalStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_STATUS))

// 贷款期数选项 (暂时保留硬编码，后续可考虑添加字典类型)
const loanTermOptions = computed(() => [
  { label: t('salesOrderEdit.loanTerms.12'), value: 12 },
  { label: t('salesOrderEdit.loanTerms.24'), value: 24 },
  { label: t('salesOrderEdit.loanTerms.36'), value: 36 },
  { label: t('salesOrderEdit.loanTerms.48'), value: 48 },
  { label: t('salesOrderEdit.loanTerms.60'), value: 60 },
  { label: t('salesOrderEdit.loanTerms.72'), value: 72 }
]);


// 计算属性
const orderId = computed(() => route.params.orderNo as string)

// 计算权益折扣总额
const calculateRightsDiscount = () => {
  return editForm.selectedRights.reduce((total, right) => total + (right.discountPrice || 0), 0)
}

// 计算订单总额
const calculateTotalAmount = () => {
  const baseAmount = (orderDetails.value.salesSubtotal || 0) +
                    (orderDetails.value.numberPlatesFee || 0) +
                    (orderDetails.value.accessoriesTotalAmount || 0) +
                    (orderDetails.value.insuranceTotalAmount || 0) +
                    (orderDetails.value.otrFeesTotalAmount || 0)

  const discount = calculateRightsDiscount()
  return Math.max(0, baseAmount - discount)
}

// 计算尾款金额
const calculateBalanceAmount = () => {
  const totalAmount = calculateTotalAmount()
  const depositAmount = orderDetails.value?.depositAmount || 0
  const loanAmount = editForm.paymentMethod === 'LOAN' ? (editForm.loanAmount || 0) : 0
  return Math.max(0, totalAmount - depositAmount - loanAmount)
}

// 计算剩余应收
const calculateRemainingAmount = () => {
  const totalAmount = calculateTotalAmount()
  const depositAmount = orderDetails.value?.depositAmount || 0
  const loanAmount = editForm.paymentMethod === 'LOAN' ? (editForm.loanAmount || 0) : 0
  return Math.max(0, totalAmount - depositAmount - loanAmount)
}

// 格式化方法
const formatCustomerType = (type: BuyerType | undefined) => {
  if (!type) return ''
  // return t(`buyerTypes.${type}`);
  return type;
}

const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined || amount === null) return 'RM 0.00'
  return `RM ${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 获取订单详情
const fetchOrderDetail = async () => {
  try {
    loading.value = true
    console.log('开始获取订单详情，orderId:', orderId.value)
    orderDetails.value = await getSalesOrderDetail(orderId.value)
    console.log('获取订单详情成功:', orderDetails.value)

    // 初始化表单数据
    editForm.color = orderDetails.value.color
    editForm.selectedRights = [...(orderDetails.value.rights || [])]
    editForm.paymentMethod = orderDetails.value.paymentMethod
    editForm.loanAmount = orderDetails.value.loanAmount || 0
    editForm.loanApprovalStatus = orderDetails.value.loanApprovalStatus || 'pending_review'
    editForm.insuranceNotes = orderDetails.value.insuranceNotes || ''

  } catch (error) {
    console.error(t('salesOrderEdit.messages.fetchDetailFailed'), error)
    ElMessage.error(t('salesOrderEdit.messages.fetchDetailFailed'))
    router.push({ name: 'sales-order' })
  } finally {
    loading.value = false
  }
}

// 权益选择
const handleSelectRights = () => {
  rightsDialogVisible.value = true
}

// 权益选择确认
const handleRightsConfirm = (selectedRights: SalesOrderRight[]) => {
  editForm.selectedRights = selectedRights
  rightsDialogVisible.value = false
}

// 移除权益
const handleRemoveRight = (index: number) => {
  editForm.selectedRights.splice(index, 1)
}

// 保存订单
const handleSave = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    saveLoading.value = true

    const saveData: SaveOrderRequest = {
      orderNo: orderId.value,
      color: editForm.color,
      selectedRights: editForm.selectedRights.map(right => right.id),
      paymentMethod: editForm.paymentMethod,
      loanAmount: editForm.paymentMethod === 'LOAN' ? editForm.loanAmount : undefined,
      loanTerm: editForm.paymentMethod === 'LOAN' ? editForm.loanTerm : undefined,
      loanApprovalStatus: editForm.paymentMethod === 'LOAN' ? editForm.loanApprovalStatus : undefined,
      insuranceNotes: editForm.insuranceNotes
    }

    const result = await saveSalesOrder(saveData)
    ElMessage.success(result.message || t('salesOrderEdit.messages.saveSuccess'))

    // 如果颜色有变更，提示审核信息
    if (editForm.color !== orderDetails.value?.color) {
      ElMessageBox.alert(
        t('salesOrderEdit.messages.colorChangeAlert'),
        tc('common.tip'),
        { type: 'info' }
      )
    }

    // router.push({ name: 'SalesOrderManagement' })

  } catch (error: unknown) {
    console.error(t('salesOrderEdit.messages.saveFailed'), error)
    const errorMessage = error instanceof Error ? error.message : t('salesOrderEdit.messages.saveFailed')
    ElMessage.error(errorMessage)
  } finally {
    saveLoading.value = false
  }
}

// 返回列表
const goToList = () => {
  router.push({ name: 'sales-order' })
}

// 组件挂载
onMounted(() => {
  console.log('onMounted 被调用')
  console.log('路由参数 orderNo:', route.params.orderNo)
  console.log('计算属性 orderId:', orderId.value)
  fetchOrderDetail()
  fetchVehicleColorData()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-title {
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
  color: #303133;
}

.mb-20 {
  margin-bottom: 20px;
}

.order-summary-header {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.order-summary-header strong {
  font-weight: bold;
  color: #333;
  margin-right: 20px;
}

.section h2 {
  font-size: 22px;
  color: #303133;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.form-row .form-item-static {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.form-row .form-control-static {
  font-weight: bold;
  color: #333;
  margin-top: 8px;
}

.form-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.custom-tabs .el-tabs__item {
  font-size: 16px;
}

.tab-content-section {
  padding: 20px 0;
}

.tab-content-section h3 {
  font-size: 18px;
  color: #606266;
  margin-top: 20px;
  margin-bottom: 15px;
}

.tab-content-section h4 {
  font-size: 16px;
  color: #606266;
  margin-top: 15px;
  margin-bottom: 10px;
}

.total-amount {
  text-align: right;
  margin-top: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.static-value {
  color: #E6A23C;
  font-weight: bold;
}

.footer-bar {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 15px 20px;
  border-top: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 1000;
}

.price-summary span {
  font-size: 18px;
  margin-right: 30px;
  color: #303133;
}

.total-price {
  color: #409EFF;
  font-weight: bold;
}

.remaining-amount {
  color: #F56C6C;
  font-weight: bold;
}

.buttons .el-button {
  margin-left: 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-table .cell) {
  word-break: break-word;
}
</style>
