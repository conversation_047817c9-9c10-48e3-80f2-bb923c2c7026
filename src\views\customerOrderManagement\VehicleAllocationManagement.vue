<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('vehicleAllocation.title') }}</h1>

    <!-- 搜索筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('vehicleAllocation.orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.customerName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('vehicleAllocation.customerNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.customerPhone')">
              <el-input
                v-model="searchParams.customerPhone"
                :placeholder="t('vehicleAllocation.customerPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.allocationStatus')">
              <el-select
                v-model="searchParams.allocationStatus"
                :placeholder="t('vehicleAllocation.allocationStatusPlaceholder')"
                clearable
              >
                <el-option :label="t('common.pleaseSelect')" value="" />
                <el-option :label="t('vehicleAllocation.allocated')" value="allocated" />
                <el-option :label="t('vehicleAllocation.unallocated')" value="unallocated" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.orderStatus')">
              <el-select
                v-model="searchParams.orderStatus"
                :placeholder="t('vehicleAllocation.orderStatusPlaceholder')"
                clearable
              >
                <el-option :label="t('common.pleaseSelect')" value="" />
                <el-option
                  v-for="status in orderStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.vin')">
              <el-input
                v-model="searchParams.vin"
                :placeholder="t('vehicleAllocation.vinPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.factoryOrderNumber')">
              <el-input
                v-model="searchParams.factoryOrderNumber"
                :placeholder="t('vehicleAllocation.factoryOrderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.allocationTime')">
              <el-date-picker
                v-model="allocationTimeRange"
                type="datetimerange"
                :start-placeholder="t('common.startDate')"
                :end-placeholder="t('common.endDate')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.salesConsultant')">
              <el-select
                v-model="searchParams.salesConsultant"
                :placeholder="t('vehicleAllocation.salesConsultantPlaceholder')"
                clearable
              >
                <el-option :label="t('common.pleaseSelect')" value="" />
                <el-option
                  v-for="consultant in salesConsultantOptions"
                  :key="consultant.value"
                  :label="consultant.label"
                  :value="consultant.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.orderCreateTime')">
              <el-date-picker
                v-model="orderCreateTimeRange"
                type="datetimerange"
                :start-placeholder="t('common.startDate')"
                :end-placeholder="t('common.endDate')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ t('common.search') }}
              </el-button>
              <el-button @click="resetSearch">{{ t('common.reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        :empty-text="t('common.noData')"
        style="width: 100%"
      >
        <el-table-column type="index" :label="t('common.index')" width="80" />
        <el-table-column :label="t('vehicleAllocation.orderNumber')" prop="orderNumber" min-width="120" />
        <el-table-column :label="t('vehicleAllocation.customerName')" prop="customerName" min-width="100" />
        <el-table-column :label="t('vehicleAllocation.customerPhone')" prop="customerPhone" min-width="120" />
        <el-table-column :label="t('vehicleAllocation.salesConsultant')" prop="salesConsultant" min-width="100" />
        <el-table-column :label="t('vehicleAllocation.model')" prop="model" min-width="80" />
        <el-table-column :label="t('vehicleAllocation.variant')" prop="variant" min-width="100" />
        <el-table-column :label="t('vehicleAllocation.color')" prop="color" min-width="80" />
        <el-table-column :label="t('vehicleAllocation.factoryOrderNumber')" prop="factoryOrderNumber" min-width="120" />
        <el-table-column :label="t('vehicleAllocation.vin')" prop="vin" min-width="140" />
        <el-table-column :label="t('vehicleAllocation.allocationStatus')" prop="allocationStatus" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.allocationStatus === 'allocated' ? 'success' : 'info'">
              {{ row.allocationStatus === 'allocated' ? t('vehicleAllocation.allocated') : t('vehicleAllocation.unallocated') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleAllocation.orderStatus')" prop="orderStatus" min-width="100">
          <template #default="{ row }">
            <span v-if="row.orderStatus">{{ t(`vehicleAllocation.orderStatuses.${row.orderStatus}`) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleAllocation.orderCreateTime')" prop="orderCreateTime" min-width="140" />
        <el-table-column :label="t('common.operations')" width="220" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="canAllocate(row)"
              type="primary"
              :icon="CaretRight"
              link
              @click="showAllocationModal(row)"
            >
              {{ t('actions.allocate') }}
            </el-button>
            <el-button
              v-if="canCancelAllocate(row)"
              type="danger"
              :icon="Close"
              link
              @click="showCancelModal(row)"
            >
              {{ t('vehicleAllocation.cancelAllocate') }}
            </el-button>
            <el-button
              type="info"
              :icon="Document"
              link
              @click="showRecordModal(row)"
            >
              {{ t('vehicleAllocation.records') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </el-card>

    <!-- 导出确认弹窗 -->
    <el-dialog
      v-model="exportModalVisible"
      :title="t('vehicleAllocation.exportData')"
      width="400px"
      :close-on-click-modal="false"
    >
      <p>{{ t('vehicleAllocation.confirmExport') }}</p>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="exportModalVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleExport" :loading="exportLoading">
            {{ t('common.export') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 配车确认弹窗 -->
    <el-dialog
      v-model="allocationModalVisible"
      :title="t('vehicleAllocation.allocationConfirm')"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div v-if="currentOrder">
        <!-- 订单信息展示 -->
        <div class="order-info-section">
          <h3>{{ t('vehicleAllocation.orderDetail') }}</h3>
          <el-row :gutter="20" class="order-info-grid">
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.orderNumber') }}:</strong> {{ currentOrder.orderNumber }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.customerName') }}:</strong> {{ currentOrder.customerName }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.customerPhone') }}:</strong> {{ currentOrder.customerPhone }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.model') }}:</strong> {{ currentOrder.model }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.variant') }}:</strong> {{ currentOrder.variant }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.color') }}:</strong> {{ currentOrder.color }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.salesConsultant') }}:</strong> {{ currentOrder.salesConsultant }}</div>
            </el-col>
          </el-row>
        </div>

        <!-- 可配车辆查询 -->
        <div class="vehicle-query-section">
          <h3>{{ t('vehicleAllocation.availableVehiclesQuery') }}</h3>
          <el-form :model="vehicleQueryParams" label-position="top">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="t('vehicleAllocation.vin')">
                  <el-input
                    v-model="vehicleQueryParams.vin"
                    :placeholder="t('vehicleAllocation.vinPlaceholder')"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('vehicleAllocation.factoryOrderNumber')">
                  <el-input
                    v-model="vehicleQueryParams.factoryOrderNumber"
                    :placeholder="t('vehicleAllocation.factoryOrderNumberPlaceholder')"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button type="primary" @click="queryAvailableVehicles" :loading="vehicleQueryLoading">
                    {{ t('common.search') }}
                  </el-button>
                  <el-button @click="resetVehicleQuery">{{ t('common.reset') }}</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 可配车辆列表 -->
        <div class="available-vehicles-section">
          <h3>{{ t('vehicleAllocation.availableVehicles') }}</h3>
          <el-table
            :data="availableVehicles"
            v-loading="vehicleQueryLoading"
            :empty-text="hasSearched ? t('common.noData') : t('vehicleAllocation.pleaseSearchFirst')"
            @selection-change="handleVehicleSelection"
          >
            <el-table-column type="radio" width="55">
              <template #default="{ row, $index }">
                <el-radio v-model="selectedVehicleIndex" :label="$index">{{ '' }}</el-radio>
              </template>
            </el-table-column>
            <el-table-column :label="t('vehicleAllocation.vin')" prop="vin" min-width="140" />
            <el-table-column :label="t('vehicleAllocation.factoryOrderNumber')" prop="factoryOrderNumber" min-width="120" />
            <el-table-column :label="t('vehicleAllocation.model')" prop="model" min-width="80" />
            <el-table-column :label="t('vehicleAllocation.variant')" prop="variant" min-width="100" />
            <el-table-column :label="t('vehicleAllocation.color')" prop="color" min-width="80" />
            <el-table-column :label="t('vehicleAllocation.warehouseName')" prop="warehouseName" min-width="100" />
            <el-table-column :label="t('vehicleAllocation.inStockTime')" prop="inStockTime" min-width="140" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="allocationModalVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmAllocation" :loading="allocationLoading">
            {{ t('common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 取消配车确认弹窗 -->
    <el-dialog
      v-model="cancelModalVisible"
      :title="t('common.tip')"
      width="500px"
      :close-on-click-modal="false"
      @closed="cancelReason = ''"
    >
      <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <el-icon color="#E6A23C" style="font-size: 24px; margin-right: 10px;">
          <Warning />
        </el-icon>
        <span>{{ t('vehicleAllocation.confirmCancelAllocation') }}</span>
      </div>
      <el-form label-position="top">
        <el-form-item :label="t('vehicleAllocation.cancelReasonLabel')" required>
          <el-input
            v-model="cancelReason"
            type="textarea"
            :rows="3"
            :placeholder="t('vehicleAllocation.cancelReasonPlaceholder')"
          />
        </el-form-item>
        <div v-if="allocateModal.vehicleInfo" class="vehicle-info">
          <p><strong>{{ t('allocateModal.model') }}:</strong> {{ allocateModal.vehicleInfo.model }}</p>
          <p><strong>{{ t('allocateModal.color') }}:</strong> {{ allocateModal.vehicleInfo.color }}</p>
          <p><strong>{{ t('allocateModal.configuration') }}:</strong> {{ allocateModal.vehicleInfo.configuration }}</p>
          <p><strong>{{ t('allocateModal.warehouse') }}:</strong> {{ allocateModal.vehicleInfo.warehouse }}</p>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="cancelModalVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmCancelAllocation" :loading="cancelLoading">
            {{ t('common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 配车记录弹窗 -->
    <el-dialog
      v-model="recordModalVisible"
      :title="t('vehicleAllocation.allocationRecordDetail')"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentRecordOrder">
        <!-- 订单基本信息 -->
        <div class="record-order-info">
          <h3>{{ t('vehicleAllocation.orderDetail') }}</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.orderNumber') }}:</strong> {{ currentRecordOrder.orderNumber }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.customerName') }}:</strong> {{ currentRecordOrder.customerName }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.customerPhone') }}:</strong> {{ currentRecordOrder.customerPhone }}</div>
            </el-col>
          </el-row>
        </div>

        <!-- 配车历史时间轴 -->
        <div class="allocation-timeline">
          <h3>{{ t('vehicleAllocation.allocationHistory') }}</h3>
          <el-timeline v-if="allocationTimeline.length > 0">
            <el-timeline-item
              v-for="item in allocationTimeline"
              :key="item.id"
              :type="getTimelineType(item.processResult)"
              :timestamp="item.operationTime"
            >
              <div class="timeline-content">
                <div class="timeline-title">
                  {{ t(`vehicleAllocation.operationTypes.${item.operationType}`) }} -
                  {{ t(`vehicleAllocation.processResults.${item.processResult}`) }}
                </div>
                <div class="timeline-details">
                  <p><strong>{{ t('vehicleAllocation.operator') }}:</strong> {{ item.operatorName }}</p>
                  <p v-if="item.vin"><strong>{{ t('vehicleAllocation.vin') }}:</strong> {{ item.vin }}</p>
                  <p v-if="item.warehouseName"><strong>{{ t('vehicleAllocation.warehouseName') }}:</strong> {{ item.warehouseName }}</p>
                  <p v-if="item.remarks"><strong>{{ t('vehicleAllocation.remarks') }}:</strong> {{ item.remarks }}</p>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div v-else class="no-timeline">
            <el-empty :description="t('common.noData')" />
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="recordModalVisible = false">{{ t('common.cancel') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Finished, Refresh, CircleClose, View } from '@element-plus/icons-vue';
import type { TabsPaneContext } from 'element-plus';


const { t,tc } = useModuleI18n('vehicleAllocation');

import {
  getVehicleAllocationOrderList,
  getAvailableVehicles,
  confirmAllocation as apiConfirmAllocation,
  cancelAllocation as apiCancelAllocation,
  getOrderAllocationTimeline,
  exportAllocationData,
  getStoreOptions,
  getSalesConsultantOptions,
  getOrderStatusOptions,
  getOperationTypeOptions
} from '@/api/modules/vehicleAllocation';

interface Order {
  id: number;
  orderNo: string;
  customerName: string;
  customerPhone: string;
  vehicleModel: string;
  downPayment: string;
  orderDate: string;
  allocatedVehicle?: VehicleInfo;
  allocationTime?: string;
}

interface VehicleInfo {
  vin: string;
  model: string;
  color: string;
  configuration: string;
  warehouse: string;
}

const activeTab = ref('pending');
const searchForm = reactive({ customerName: '', orderNo: '' });
const tableData = ref<Order[]>([]);
const loading = ref(false);
const pagination = reactive({ page: 1, pageSize: 10, total: 0 });

const allocateModal = reactive({
  visible: false,
  isReallocate: false,
  order: null as Order | null,
  form: {
    vin: '',
  },
  vehicleInfo: null as VehicleInfo | null,
});
// 搜索参数
const searchParams = reactive<Partial<VehicleAllocationOrderParams>>({
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  allocationStatus: undefined,
  orderStatus: undefined,
  vin: '',
  factoryOrderNumber: '',
  salesConsultant: '',
});

// 时间范围
const allocationTimeRange = ref<[string, string] | null>(null);
const orderCreateTimeRange = ref<[string, string] | null>(null);

// 选项数据
const storeOptions = ref<Array<{label: string, value: string}>>([]);
const salesConsultantOptions = ref<Array<{label: string, value: string}>>([]);
const orderStatusOptions = ref<Array<{label: string, value: string}>>([]);
const operationTypeOptions = ref<Array<{label: string, value: string}>>([]);

// 弹窗状态
const exportModalVisible = ref(false);
const exportLoading = ref(false);
const allocationModalVisible = ref(false);
const allocationLoading = ref(false);
const cancelModalVisible = ref(false);
const cancelLoading = ref(false);
const recordModalVisible = ref(false);
const cancelReason = ref('');

// 配车相关数据
const currentOrder = ref<VehicleAllocationOrderItem | null>(null);
const currentCancelOrder = ref<VehicleAllocationOrderItem | null>(null);
const currentRecordOrder = ref<VehicleAllocationOrderItem | null>(null);
const vehicleQueryParams = reactive<AvailableVehicleParams>({
  orderNumber: '', // P03契约必需参数
  vin: '',
  factoryOrderNumber: ''
});
const vehicleQueryLoading = ref(false);
const availableVehicles = ref<AvailableVehicle[]>([]);
const selectedVehicleIndex = ref<number | null>(null);
const hasSearched = ref(false); // 跟踪是否已经查询过
const allocationTimeline = ref<OrderAllocationTimelineItem[]>([]);
const mockTimelineDb: Record<string, OrderAllocationTimelineItem[]> = reactive({});

// 计算属性
const selectedVehicle = computed(() => {
  if (selectedVehicleIndex.value !== null && availableVehicles.value[selectedVehicleIndex.value]) {
    return availableVehicles.value[selectedVehicleIndex.value];
  }
  return null;
});

// 监听时间范围变化
watch(allocationTimeRange, (newVal) => {
  if (newVal) {
    searchParams.allocationTimeStart = newVal[0];
    searchParams.allocationTimeEnd = newVal[1];
  } else {
    searchParams.allocationTimeStart = undefined;
    searchParams.allocationTimeEnd = undefined;
  }
});

watch(orderCreateTimeRange, (newVal) => {
  if (newVal) {
    searchParams.orderCreateTimeStart = newVal[0];
    searchParams.orderCreateTimeEnd = newVal[1];
  } else {
    searchParams.orderCreateTimeStart = undefined;
    searchParams.orderCreateTimeEnd = undefined;
  }
});

// 模拟数据
const mockData: VehicleAllocationOrderItem[] = Array.from({ length: 100 }).map((_, i) => ({
  id: i + 1,
  orderNumber: `ORD${String(i + 1).padStart(5, '0')}`,
  customerName: `客户${i + 1}`,
  customerPhone: `13${String(Math.floor(Math.random() * 9000000000) + 1000000000).substring(0, 9)}`,
  store: `门店${Math.floor(Math.random() * 5) + 1}`,
  salesConsultant: `销售顾问${Math.floor(Math.random() * 10) + 1}`,
  model: `车型${Math.floor(Math.random() * 3) + 1}`,
  variant: `配置${Math.floor(Math.random() * 5) + 1}`,
  color: `颜色${Math.floor(Math.random() * 4) + 1}`,
  factoryOrderNumber: `FACORD${String(i + 1).padStart(5, '0')}`,
  vin: `VIN${String(Math.floor(Math.random() * 900000000000000) + 100000000000000).substring(0, 15)}`,
  allocationStatus: i % 3 === 0 ? 'allocated' : 'unallocated',
  orderStatus: (['submitted', 'confirmed', 'cancel_review', 'cancel_approved', 'cancelled', 'ready_delivery', 'delivered'] as OrderStatus[])[Math.floor(Math.random() * 7)],
  orderCreateTime: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString().slice(0, 19).replace('T', ' '),
  allocationTime: i % 3 === 0 ? new Date(Date.now() - Math.floor(Math.random() * 15 * 24 * 60 * 60 * 1000)).toISOString().slice(0, 19).replace('T', ' ') : undefined,
}));

// 模拟 API 调用，根据分页和搜索参数返回过滤后的数据
const getMockVehicleAllocationOrderList = (params: VehicleAllocationOrderParams) => {
  return new Promise<{ data: VehicleAllocationOrderItem[], total: number }>((resolve) => {
    setTimeout(() => {
      let filteredData = mockData;

      if (params.orderNumber) {
        filteredData = filteredData.filter(item => item.orderNumber.includes(params.orderNumber!));
      }
      if (params.customerName) {
        filteredData = filteredData.filter(item => item.customerName.includes(params.customerName!));
      }
      if (params.customerPhone) {
        filteredData = filteredData.filter(item => item.customerPhone.includes(params.customerPhone!));
      }
      if (params.allocationStatus !== undefined) {
        filteredData = filteredData.filter(item => item.allocationStatus === params.allocationStatus);
      }
      if (params.orderStatus !== undefined) {
        filteredData = filteredData.filter(item => item.orderStatus === params.orderStatus);
      }
      if (params.vin) {
        filteredData = filteredData.filter(item => item.vin.includes(params.vin!));
      }
      if (params.factoryOrderNumber) {
        filteredData = filteredData.filter(item => item.factoryOrderNumber.includes(params.factoryOrderNumber!));
      }
      if (params.salesConsultant) {
        filteredData = filteredData.filter(item => item.salesConsultant.includes(params.salesConsultant!));
      }
      if (params.allocationTimeStart && params.allocationTimeEnd) {
        filteredData = filteredData.filter(item => {
          if (!item.allocationTime) return false;
          return item.allocationTime >= params.allocationTimeStart! && item.allocationTime <= params.allocationTimeEnd!;
        });
      }
      if (params.orderCreateTimeStart && params.orderCreateTimeEnd) {
        filteredData = filteredData.filter(item => {
          return item.orderCreateTime >= params.orderCreateTimeStart! && item.orderCreateTime <= params.orderCreateTimeEnd!;
        });
      }

      const total = filteredData.length;
      const start = (params.page! - 1) * params.pageSize!;
      const end = start + params.pageSize!;
      const data = filteredData.slice(start, end);

      resolve({ data, total });
    }, 500); // 模拟网络延迟
  });
};

// 模拟 confirmAllocation API 响应
const mockConfirmAllocation = (params: AllocationConfirmParams) => {
  return new Promise<{ success: boolean; message: string }>((resolve) => {
    setTimeout(() => {
      const order = mockData.find(item => item.orderNumber === params.orderNumber);

      if (order) {
        // 找到对应的订单，更新其状态和车辆信息
        const orderIndex = mockData.findIndex(item => item.orderNumber === params.orderNumber);
        if (orderIndex !== -1) {
          mockData[orderIndex] = {
            ...mockData[orderIndex],
            allocationStatus: 'allocated',
            vin: params.vin,
            factoryOrderNumber: `FAC-MOCK-${params.vin.slice(-5)}`, // 根据VIN生成一个模拟工厂订单号
            allocationTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
          };
        }

        if (!mockTimelineDb[params.orderNumber]) {
          mockTimelineDb[params.orderNumber] = [];
        }
        mockTimelineDb[params.orderNumber].push({
          id: Date.now(),
          operationType: 'allocate',
          operator: '张三（模拟）',
          operationTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
          processResult: 'success',
          vin: params.vin,
          remarks: '配车成功',
          operationDetails: `操作员 张三（模拟） 分配了车辆 ${params.vin}`,
          isSystemOperation: false,
        });

        resolve({ success: true, message: t('vehicleAllocation.allocationSuccess') });
      } else {
        resolve({ success: false, message: t('vehicleAllocation.allocationFailed') + ': 订单或车辆不存在' });
      }
    }, 300);
  });
};

// 模拟 cancelAllocation API 响应
const mockCancelAllocation = (params: CancelAllocationParams) => {
  return new Promise<{ success: boolean; message: string }>((resolve) => {
    setTimeout(() => {
      const order = mockData.find(item => item.orderNumber === params.orderNumber);
      if (order) {
        const orderIndex = mockData.findIndex(item => item.orderNumber === params.orderNumber);
        if (orderIndex !== -1) {
          mockData[orderIndex] = {
            ...mockData[orderIndex],
            allocationStatus: 'unallocated',
            vin: '', // 设置为空字符串，而不是 undefined
            factoryOrderNumber: '', // 取消配车时，也清除工厂订单号
            allocationTime: undefined,
          };
        }

        if (!mockTimelineDb[params.orderNumber]) {
          mockTimelineDb[params.orderNumber] = [];
        }
        mockTimelineDb[params.orderNumber].push({
          id: Date.now(),
          operationType: 'cancel_allocate',
          operator: '李四（模拟）',
          operationTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
          processResult: 'success',
          vin: order.vin,
          remarks: params.reason,
          operationDetails: `操作员 李四（模拟） 取消了车辆 ${order.vin} 的分配`,
          isSystemOperation: false,
        });

        resolve({ success: true, message: t('vehicleAllocation.cancelAllocationSuccess') });
      } else {
        resolve({ success: false, message: t('vehicleAllocation.cancelAllocationFailed') + ': 订单不存在' });
      }
    }, 300);
  });
};

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const params: VehicleAllocationOrderParams = {
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize
    };

    // 使用真实API
    const response = await getVehicleAllocationOrderList(params);
    tableData.value = response.data;
    pagination.total = response.total;
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error(t('common.networkError'));
  } finally {
    loading.value = false;
  }
};

const loadOptions = async () => {
  try {
    const [stores, consultants, orderStatuses, operationTypes] = await Promise.all([
      getStoreOptions(),
      getSalesConsultantOptions(),
      getOrderStatusOptions(),
      getOperationTypeOptions()
    ]);

    storeOptions.value = stores;
    salesConsultantOptions.value = consultants;
    orderStatusOptions.value = orderStatuses;
    operationTypeOptions.value = operationTypes;
  } catch (error) {
    console.error('加载选项失败:', error);
    ElMessage.error(t('common.networkError'));
  }
};

const handleSearch = () => {
  pagination.page = 1;
  fetchData();
};

const resetSearch = () => {
  searchForm.customerName = '';
  searchForm.orderNo = '';
  handleSearch();
};

const openAllocateModal = (row: Order, isReallocate = false) => {
  allocateModal.order = row;
  allocateModal.isReallocate = isReallocate;
  allocateModal.form.vin = row.allocatedVehicle?.vin || '';
  allocateModal.vehicleInfo = row.allocatedVehicle || null;
  allocateModal.visible = true;
};

const handleCurrentChange = (val: number) => {
  pagination.page = val;
  loadData();
};

// 业务逻辑方法
const canAllocate = (row: VehicleAllocationOrderItem) => {
  // 只要订单状态不是终结状态，且未配车，就允许配车
  return row.allocationStatus === 'unallocated' &&
         !['ready_delivery', 'delivered', 'cancelled', 'cancel_approved'].includes(row.orderStatus);
};

const canCancelAllocate = (row: VehicleAllocationOrderItem) => {
  return row.allocationStatus === 'allocated' &&
         !['ready_delivery', 'delivered', 'cancelled', 'cancel_approved'].includes(row.orderStatus);
};

const showExportModal = () => {
  exportModalVisible.value = true;
};

const handleExport = async () => {
  exportLoading.value = true;
  try {
    const params = {
      searchParams: {
        ...searchParams,
        page: pagination.page,
        pageSize: pagination.pageSize
      },
      exportType: 'all_results' as const // 假设导出所有结果
    };

    const response = await exportAllocationData(params);
    if (response.success) {
      ElMessage.success(response.message);
      exportModalVisible.value = false;
      if (response.downloadUrl) {
        window.open(response.downloadUrl, '_blank');
      }
    } else {
      ElMessage.error(response.message || t('common.operationFailed'));
    }
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('common.operationFailed'));
  } finally {
    exportLoading.value = false;
  }
};

const showAllocationModal = async (row: VehicleAllocationOrderItem) => {
  currentOrder.value = row;
  allocationModalVisible.value = true;

  // 根据P03契约设置查询参数
  vehicleQueryParams.orderNumber = row.orderNumber; // P03契约必需参数
  vehicleQueryParams.vin = ''; // 清空VIN和工厂订单号，以便重新查询
  vehicleQueryParams.factoryOrderNumber = '';

    // 清空之前的查询结果和状态
  availableVehicles.value = [];
  selectedVehicleIndex.value = null;
  hasSearched.value = false; // 重置查询状态

  // 不自动查询，等待用户点击查询按钮
};

const queryAvailableVehicles = async () => {
  vehicleQueryLoading.value = true;
  try {
    // 使用真实接口
    const vehicles = await getAvailableVehicles(vehicleQueryParams);
    availableVehicles.value = vehicles;
    selectedVehicleIndex.value = null; // 清空已选车辆
    hasSearched.value = true; // 标记已经查询过
  } catch (error) {
    console.error('查询可配车辆失败:', error);
    ElMessage.error(t('common.networkError'));
  } finally {
    vehicleQueryLoading.value = false;
  }
};

const resetVehicleQuery = () => {
  vehicleQueryParams.vin = '';
  vehicleQueryParams.factoryOrderNumber = '';
  // orderNumber保持不变，因为这是订单固有属性
  queryAvailableVehicles();
};

const handleVehicleSelection = (selection: AvailableVehicle[]) => {
  // Element Plus table selection for radio type passes an array of selected rows
  // We only expect one, so we take the first one.
  if (selection.length > 0) {
    selectedVehicleIndex.value = availableVehicles.value.findIndex(v => v.id === selection[0].id);
  } else {
    selectedVehicleIndex.value = null;
  }
};

const confirmAllocation = async () => {
  if (!selectedVehicle.value) {
    ElMessage.warning(t('vehicleAllocation.noVehicleSelected'));
    return;
  }

  if (!currentOrder.value) return;

  // 再次验证车辆配置是否匹配，这是一个重要的业务规则
  const isConfigMatch = selectedVehicle.value.model === currentOrder.value.model &&
                        selectedVehicle.value.variant === currentOrder.value.variant &&
                        selectedVehicle.value.color === currentOrder.value.color;

  if (!isConfigMatch) {
    ElMessage.error(t('vehicleAllocation.vehicleConfigMismatch'));
    return;
  }

  allocationLoading.value = true;
  try {
    const params = {
      orderNumber: currentOrder.value.orderNumber,
      vin: selectedVehicle.value.vin
    };

    const response = await apiConfirmAllocation(params); // 使用真实API接口
    if (response.success) {
      ElMessage.success(response.message);
      allocationModalVisible.value = false;
      loadData(); // 刷新列表
    } else {
      ElMessage.error(t('allocateModal.vinNotFound'));
      allocateModal.vehicleInfo = null;
    }
  } catch (error) {
    console.error('取消配车失败:', error);
    ElMessage.error(t('common.operationFailed'));
  } finally {
    cancelLoading.value = false;
  }
};

const confirmCancelAllocation = async () => {
  if (!currentCancelOrder.value) return;

  if (!cancelReason.value.trim()) {
    ElMessage.warning(t('vehicleAllocation.reasonRequired'));
    return;
  }

  cancelLoading.value = true;
  try {
    const params: CancelAllocationParams = {
      orderNumber: currentCancelOrder.value.orderNumber,
      reason: cancelReason.value,
    };

    const response = await apiCancelAllocation(params); // 使用真实API接口
    if (response.success) {
      ElMessage.success(response.message);
      cancelModalVisible.value = false;
      loadData(); // 刷新列表
    } else {
      ElMessage.error(response.message || t('common.operationFailed'));
    }
  } catch (error) {
    console.error('取消配车失败:', error);
    ElMessage.error(t('common.operationFailed'));
  } finally {
    cancelLoading.value = false;
  }
};

const showRecordModal = async (row: VehicleAllocationOrderItem) => {
  currentRecordOrder.value = row;
  recordModalVisible.value = true;

  try {
    // 这里假设 getOrderAllocationDetail 可以获取到订单的基本信息，以便在记录详情弹窗中展示
    // 如果 API 不支持，可能需要从列表数据中直接取或者单独请求
    allocationTimeline.value = await getOrderAllocationTimeline(row.orderNumber);

    // const timeline = await mockGetOrderAllocationTimeline(row.orderNumber);
    // allocationTimeline.value = timeline;
  } catch (error) {
    console.error('加载配车记录失败:', error);
    ElMessage.error(t('common.networkError'));
  }
};

const getTimelineType = (result: string) => {
  return result === 'success' ? 'success' : 'danger';
};

// 模拟 getSalesConsultantOptions API 响应
const mockGetSalesConsultantOptions = (store?: string) => {
  return new Promise<Array<{ label: string, value: string }>>((resolve) => {
    setTimeout(() => {
      let consultants: Array<{ label: string, value: string }> = [];
      if (store === 'storeA') {
        consultants = [
          { label: '销售顾问A1', value: 'consultantA1' },
          { label: '销售顾问A2', value: 'consultantA2' },
        ];
      } else if (store === 'storeB') {
        consultants = [
          { label: '销售顾问B1', value: 'consultantB1' },
          { label: '销售顾问B2', value: 'consultantB2' },
        ];
      } else if (store === 'storeC') {
        consultants = [
          { label: '销售顾问C1', value: 'consultantC1' },
          { label: '销售顾问C2', value: 'consultantC2' },
        ];
      } else {
        // 默认返回所有或一部分模拟销售顾问
        consultants = [
          { label: '销售顾问1', value: 'consultant1' },
          { label: '销售顾问2', value: 'consultant2' },
          { label: '销售顾问3', value: 'consultant3' },
          { label: '销售顾问4', value: 'consultant4' },
          { label: '销售顾问5', value: 'consultant5' },
        ];
      }
      resolve(consultants);
    }, 200);
  });
};

// 模拟 getOrderStatusOptions API 响应
const mockGetOrderStatusOptions = () => {
  return new Promise<Array<{ label: string, value: OrderStatus }>>((resolve) => {
    setTimeout(() => {
      resolve([
        { label: t('vehicleAllocation.orderStatuses.submitted'), value: 'submitted' },
        { label: t('vehicleAllocation.orderStatuses.confirmed'), value: 'confirmed' },
        { label: t('vehicleAllocation.orderStatuses.cancel_review'), value: 'cancel_review' },
        { label: t('vehicleAllocation.orderStatuses.cancel_approved'), value: 'cancel_approved' },
        { label: t('vehicleAllocation.orderStatuses.cancelled'), value: 'cancelled' },
        { label: t('vehicleAllocation.orderStatuses.ready_delivery'), value: 'ready_delivery' },
        { label: t('vehicleAllocation.orderStatuses.delivered'), value: 'delivered' },
      ]);
    }, 200);
  });
};

// 模拟 getOperationTypeOptions API 响应
const mockGetOperationTypeOptions = () => {
  return new Promise<Array<{ label: string, value: OperationType }>>((resolve) => {
    setTimeout(() => {
      resolve([
        { label: t('vehicleAllocation.operationTypes.allocate'), value: 'allocate' },
        { label: t('vehicleAllocation.operationTypes.cancel_allocate'), value: 'cancel_allocate' },
        { label: t('vehicleAllocation.operationTypes.view_detail'), value: 'view_detail' },
        { label: t('vehicleAllocation.operationTypes.system_auto_cancel'), value: 'system_auto_cancel' },
      ]);
    }, 200);
  });
};

// 模拟 getAvailableVehicles API 响应
const mockGetAvailableVehicles = (params: AvailableVehicleParams) => {
  return new Promise<AvailableVehicle[]>((resolve) => {
    setTimeout(() => {
      const mockVehicles: AvailableVehicle[] = Array.from({ length: 25 }).map((_, i) => ({
        id: 1000 + i,
        vin: `LVIN${String(Math.floor(Math.random() * 900000000) + 100000000).padStart(14, '0')}${i}`,
        factoryOrderNumber: `FAC${String(Math.floor(Math.random() * 90000) + 10000).padStart(8, '0')}${i}`,
        model: `车型${(i % 3) + 1}`,
        variant: `配置${(i % 5) + 1}`,
        color: `颜色${(i % 4) + 1}`,
        warehouseName: `仓库${(i % 2) + 1}`,
        stockStatus: 'in_stock',
        lockStatus: false,
        inStockTime: new Date(Date.now() - Math.floor(Math.random() * 60 * 24 * 60 * 60 * 1000)).toISOString().slice(0, 19).replace('T', ' '),
      }));

      let filtered = mockVehicles;

      if (params.vin) {
        filtered = filtered.filter(v => v.vin.includes(params.vin!));
      }
      if (params.factoryOrderNumber) {
        filtered = filtered.filter(v => v.factoryOrderNumber.includes(params.factoryOrderNumber!));
      }
      if (params.model) {
        filtered = filtered.filter(v => v.model === params.model);
      }
      if (params.variant) {
        filtered = filtered.filter(v => v.variant === params.variant);
      }
      if (params.color) {
        filtered = filtered.filter(v => v.color === params.color);
      }

      resolve(filtered);
    }, 400);
  });
};

// 模拟 getOrderAllocationTimeline API 响应
const mockGetOrderAllocationTimeline = (orderNumber: string) => {
  return new Promise<OrderAllocationTimelineItem[]>((resolve) => {
    setTimeout(() => {
      if (!mockTimelineDb[orderNumber]) {
        // 如果没有动态记录，则返回一个初始的静态模拟列表
        mockTimelineDb[orderNumber] = [
          {
            id: 1,
            operationType: 'allocate',
            operator: '张三',
            operationTime: '2023-10-26 10:00:00',
            processResult: 'success',
            vin: 'VIN1234567890ABCDE',
            warehouseName: '中心仓库A',
            remarks: '首次配车成功',
            operationDetails: '操作员 张三 分配了车辆 VIN1234567890ABCDE',
            isSystemOperation: false,
          },
          {
            id: 2,
            operationType: 'cancel_allocate',
            operator: '李四',
            operationTime: '2023-10-27 14:30:00',
            processResult: 'success',
            vin: 'VIN1234567890ABCDE',
            warehouseName: '中心仓库A',
            remarks: '客户要求更换颜色，取消配车',
            operationDetails: '操作员 李四 取消了车辆 VIN1234567890ABCDE 的分配',
            isSystemOperation: false,
          },
          {
            id: 3,
            operationType: 'allocate',
            operator: '李四',
            operationTime: '2023-10-27 15:00:00',
            processResult: 'success',
            vin: 'VINFGHIJKL1234567',
            warehouseName: '分仓库B',
            remarks: '重新配车，新颜色',
            operationDetails: '操作员 李四 分配了车辆 VINFGHIJKL1234567',
            isSystemOperation: false,
          },
          {
            id: 4,
            operationType: 'system_auto_cancel',
            operator: '系统',
            operationTime: '2023-10-29 09:00:00',
            processResult: 'failed',
            vin: 'VINFGHIJKL1234567',
            warehouseName: '分仓库B',
            remarks: '超时未提车，系统自动取消配车，释放车辆资源',
            operationDetails: '系统自动取消了车辆 VINFGHIJKL1234567 的分配',
            isSystemOperation: true,
          },
        ];
      }
      resolve(mockTimelineDb[orderNumber]);
    }, 300);
  });
};

// 模拟 exportAllocationData API 响应
const mockExportAllocationData = (params: any) => {
  return new Promise<{ success: boolean; message: string; downloadUrl?: string }>((resolve) => {
    setTimeout(() => {
      console.log('Exporting data with params:', params);
      resolve({
        success: true,
        message: '导出任务已创建，请稍后下载',
        downloadUrl: '/mock-download/vehicle-allocation-export.xlsx'
      });
    }, 1000);
  });
};

// 组件挂载时执行
onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .operation-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.operation-buttons {
  text-align: right;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;

  .el-button {
    margin-left: 10px;
  }
}

.order-info-section, .vehicle-query-section, .available-vehicles-section {
  margin-bottom: 20px;

  h3 {
    margin-bottom: 15px;
    color: $primary-color;
  }
}

.order-info-grid {
  .el-col {
    margin-bottom: 10px;
  }
}

.record-order-info, .allocation-timeline {
  margin-bottom: 20px;

  h3 {
    margin-bottom: 15px;
    color: $primary-color;
  }
}

.timeline-content {
  .timeline-title {
    font-weight: bold;
    margin-bottom: 8px;
  }

  .timeline-details {
    p {
      margin: 4px 0;
      color: #666;
    }
  }
}

.no-timeline {
  text-align: center;
  padding: 20px;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
