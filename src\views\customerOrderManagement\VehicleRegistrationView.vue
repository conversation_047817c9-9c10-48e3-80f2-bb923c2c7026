<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <!-- 第一行筛选字段 -->
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('customerNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerPhone')">
              <el-input
                v-model="searchParams.customerPhone"
                :placeholder="t('customerPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('registrationStatus')">
              <el-select v-model="searchParams.registrationStatus" :placeholder="t('registrationStatusAll')" clearable>
                <el-option :label="t('registrationStatusAll')" value="" />
                <el-option :label="t('registrationStatusPending')" value="pending" />
                <el-option :label="t('registrationStatusProcessing')" value="processing" />
                <el-option :label="t('registrationStatusSuccess')" value="success" />
                <el-option :label="t('registrationStatusFailed')" value="failed" />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第二行筛选字段 -->
          <el-col :span="6">
            <el-form-item :label="t('vin')">
              <el-input
                v-model="searchParams.vin"
                :placeholder="t('vinPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('insuranceStatus')">
              <el-select v-model="searchParams.insuranceStatus" :placeholder="t('insuranceStatusAll')" clearable>
                <el-option :label="t('insuranceStatusAll')" value="" />
                <el-option :label="t('insuranceStatusInsured')" value="insured" />
                <el-option :label="t('insuranceStatusNotInsured')" value="not_insured" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('pushTimeRange')">
              <el-date-picker
                v-model="pushTimeRange"
                type="datetimerange"
                :placeholder="t('pushTimeRange')"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesAdvisor')">
              <el-select v-model="searchParams.salesAdvisor" :placeholder="t('salesAdvisorAll')" clearable>
                <el-option :label="t('salesAdvisorAll')" value="" />
                <el-option
                  v-for="advisor in salesAdvisorsList"
                  :key="advisor.value"
                  :label="advisor.label"
                  :value="advisor.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 按钮区域 -->
          <el-col :span="24" class="buttons-col">
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20 operation-card">
              <el-button :icon="Download" @click="handleExport">{{ t('export') }}</el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        :element-loading-text="t('loading')"
      >
        <el-table-column :label="t('index')" type="index" width="80" align="center" />
        <el-table-column :label="t('orderNumber')" prop="orderNo" min-width="120" />
        <el-table-column :label="t('customerName')" prop="customerName" min-width="100">
          <template #default="{ row }">
            {{ maskCustomerName(row.customerName) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('customerPhone')" prop="customerPhone" min-width="120">
          <template #default="{ row }">
            {{ maskPhoneNumber(row.customerPhone) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vin')" prop="vin" min-width="140" />
        <el-table-column :label="t('vehicleModel')" prop="vehicleModel" min-width="100" />
        <el-table-column :label="t('vehicleColor')" prop="vehicleColor" min-width="80" />
        <el-table-column :label="t('insuranceStatus')" prop="insuranceStatus" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.insuranceStatus === 'insured' ? 'success' : 'info'">
              {{ getNameByCode(row.insuranceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('companyName')" prop="companyName" min-width="120" />
        <el-table-column :label="t('status')" prop="status" min-width="110">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusDisplayText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('lastPushTime')" prop="lastPushTime" min-width="140" />
        <el-table-column :label="t('registrationFee')" prop="registrationFee" min-width="120" align="right">
          <template #default="{ row }">
            <span v-if="row.registrationFee > 0" class="fee-amount">{{ t('feeAmount', { amount: formatAmount(row.registrationFee) }) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('salesAdvisorName')" prop="salesAdvisorName" min-width="100" />
        <el-table-column :label="t('actions')" width="180" fixed="right" align="center">
          <template #default="{ row }">
            <!-- 待登记状态 -->
            <template v-if="row.status === 'pending'">
              <el-button
                v-if="userRole === 'vehicle_registration_officer'"
                type="primary"
                link
                @click="handlePushRegistration(row)"
              >
                {{ t('pushRegistration') }}
              </el-button>
            </template>
            <!-- 登记失败状态 -->
            <template v-if="row.status === 'failed'">
              <el-button
                v-if="userRole === 'vehicle_registration_officer'"
                type="warning"
                link
                @click="handleRetryPush(row)"
                style="margin-right: 8px;"
              >
                {{ t('retryPush') }}
              </el-button>
            </template>
            <!-- 所有状态都显示查看详情 -->
            <el-button type="success" link @click="handleViewDetails(row)">
              {{ t('viewDetails') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 推送确认弹窗 -->
    <el-dialog
      v-model="pushDialogVisible"
      :title="t('pushConfirmTitle')"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="push-confirm-content">
        <!-- 确认信息区域 -->
        <div class="confirm-message">
          <i class="el-icon-info" style="color: #1890ff; margin-right: 8px;"></i>
          {{ t('pushConfirmMessage') }}
        </div>

        <!-- 订单信息展示 -->
        <div class="order-info-grid">
          <div class="info-row">
            <div class="info-item">
              <label>{{ t('orderNumber') }}</label>
              <div class="info-value">{{ selectedOrder?.orderNumber || 'ORD001' }}</div>
            </div>
            <div class="info-item">
              <label>{{ t('customerName') }}</label>
              <div class="info-value">{{ selectedOrder?.customerName || '张三' }}</div>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <label>{{ t('vin') }}</label>
              <div class="info-value">{{ selectedOrder?.vin || 'WVWZZZ1JZ3W386752' }}</div>
            </div>
            <div class="info-item">
              <label>{{ t('vehicleModel') }}</label>
              <div class="info-value">{{ selectedOrder?.vehicleModel || 'MYVI 1.5L' }}</div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="pushDialogVisible = false">{{ t('cancel') }}</el-button>
          <el-button type="primary" :loading="pushLoading" @click="confirmPushRegistration">
            {{ t('confirmPush') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重新推送弹窗 -->
    <el-dialog
      v-model="retryDialogVisible"
      :title="t('retryPushTitle')"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="retry-push-content">
        <!-- 失败原因警告区域 -->
        <div class="failure-reason-alert">
          <i class="el-icon-warning" style="color: #d32f2f; margin-right: 8px;"></i>
          <div>
            <div class="alert-title">{{ t('lastPushFailureReason') }}</div>
            <div class="alert-description">
              {{ selectedOrderDetail?.jpjInfo?.failureReason || t('retryPushReasonHint') }}
            </div>
          </div>
        </div>

        <!-- 订单信息展示 -->
        <div class="retry-order-info">
          <div class="info-row">
            <div class="info-item">
              <label>{{ t('orderNumber') }}</label>
              <div class="info-value">{{ selectedOrder?.orderNumber || 'ORD004' }}</div>
            </div>
            <div class="info-item">
              <label>{{ t('customerName') }}</label>
              <div class="info-value">{{ selectedOrder?.customerName || '赵六' }}</div>
            </div>
          </div>
        </div>

        <!-- 提示信息 -->
        <div class="retry-tip">{{ t('retryPushTip') }}</div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="retryDialogVisible = false">{{ t('cancel') }}</el-button>
          <el-button type="warning" :loading="retryLoading" @click="confirmRetryPush">
            {{ t('retryPush') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 订单详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="t('registrationDetails')"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div v-if="!selectedOrderDetail" style="text-align: center; padding: 40px;">
        <el-icon class="is-loading" style="font-size: 24px;"><Loading /></el-icon>
        <p style="margin-top: 16px;">{{ t('loading') }}</p>
      </div>

      <div v-else class="detail-content">
        <!-- 订单基本信息 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-document"></i>
            <span class="section-title">{{ t('orderBasicInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('orderNumber') }}</label>
                <div class="info-value">{{ selectedOrderDetail.orderInfo?.orderNumber || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('orderStatus') }}</label>
                <div class="info-value">{{ selectedOrderDetail.orderInfo?.orderStatus || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('lastPushTime') }}</label>
                <div class="info-value">{{ selectedOrderDetail.orderInfo?.lastPushTime || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('createdAt') }}</label>
                <div class="info-value">{{ selectedOrderDetail.orderInfo?.createdAt || '-' }}</div>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('salesAdvisor') }}</label>
                <div class="info-value">{{ selectedOrderDetail.orderInfo?.salesAdvisor || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 购车人信息 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-user"></i>
            <span class="section-title">{{ t('customerInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('customerName') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.name || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('customerIdType') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.idType || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('customerIdNumber') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.idNumber || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('customerPhone') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.phone || '-' }}</div>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('customerEmail') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.email || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('customerAddress') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.address || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('customerCity') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.city || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('customerPostcode') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.postcode || '-' }}</div>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('customerState') }}</label>
                <div class="info-value">{{ selectedOrderDetail.customerInfo?.state || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 车辆信息 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-truck"></i>
            <span class="section-title">{{ t('vehicleInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('vin') }}</label>
                <div class="info-value">{{ selectedOrderDetail.vehicleInfo?.vin || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('vehicleModel') }}</label>
                <div class="info-value">{{ selectedOrderDetail.vehicleInfo?.model || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('vehicleColor') }}</label>
                <div class="info-value">{{ selectedOrderDetail.vehicleInfo?.color || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('engineNo') }}</label>
                <div class="info-value">{{ selectedOrderDetail.vehicleInfo?.engineNumber || '-' }}</div>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('modelCode') }}</label>
                <div class="info-value">{{ selectedOrderDetail.vehicleInfo?.modelCode || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('vehicleVariant') }}</label>
                <div class="info-value">{{ selectedOrderDetail.vehicleInfo?.variant || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('productionYear') }}</label>
                <div class="info-value">{{ selectedOrderDetail.vehicleInfo?.productionYear || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('manufactureDate') }}</label>
                <div class="info-value">{{ selectedOrderDetail.vehicleInfo?.manufactureDate || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 保险信息 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-shield"></i>
            <span class="section-title">{{ t('insuranceInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('insuranceStatus') }}</label>
                <div class="info-value">
                  <el-tag :type="selectedOrderDetail.insuranceInfo?.status === t('insured') ? 'success' : 'info'">
                    {{ selectedOrderDetail.insuranceInfo?.status || '-' }}
                  </el-tag>
                </div>
              </div>
              <div class="info-item">
                <label>{{ t('companyName') }}</label>
                <div class="info-value">{{ selectedOrderDetail.insuranceInfo?.company || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('policyNumber') }}</label>
                <div class="info-value">{{ selectedOrderDetail.insuranceInfo?.policyNumber || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('insurancePeriod') }}</label>
                <div class="info-value">{{ selectedOrderDetail.insuranceInfo?.period || '-' }}</div>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('insuranceDate') }}</label>
                <div class="info-value">{{ selectedOrderDetail.insuranceInfo?.date || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('insuranceFee') }}</label>
                <div class="info-value fee-amount">RM {{ formatAmount(selectedOrderDetail.insuranceInfo?.fee || 0) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- JPJ登记信息 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-edit-outline"></i>
            <span class="section-title">{{ t('jpjRegistrationInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('jpjRegistrationStatus') }}</label>
                <div class="info-value">
                  <el-tag :type="getStatusTagType(selectedOrderDetail.jpjInfo?.status)">
                    {{ selectedOrderDetail.jpjInfo?.status || '-' }}
                  </el-tag>
                </div>
              </div>
              <div class="info-item">
                <label>{{ t('certificateNumber') }}</label>
                <div class="info-value">{{ selectedOrderDetail.jpjInfo?.certificateNumber || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('pushTime') }}</label>
                <div class="info-value">{{ selectedOrderDetail.jpjInfo?.pushTime || '-' }}</div>
              </div>
              <div class="info-item">
                <label>{{ t('completionTime') }}</label>
                <div class="info-value">{{ selectedOrderDetail.jpjInfo?.completionTime || '-' }}</div>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <label>{{ t('operator') }}</label>
                <div class="info-value">{{ selectedOrderDetail.jpjInfo?.operator || '-' }}</div>
              </div>
              <div class="info-item" v-if="selectedOrderDetail.jpjInfo?.failureReason">
                <label>{{ t('failureReason') }}</label>
                <div class="info-value failure-reason">{{ selectedOrderDetail.jpjInfo.failureReason }}</div>
              </div>
            </div>
          </div>

          <!-- 登记失败时显示重新推送按钮 -->
          <div v-if="selectedOrderDetail.jpjInfo?.status === t('registrationFailed') && userRole === 'vehicle_registration_officer'" style="margin-top: 20px;">
            <el-button type="warning" @click="handleRetryPush(selectedOrder!)">
              {{ t('retryPush') }}
            </el-button>
          </div>
        </div>

        <!-- 操作日志 -->
        <div class="detail-section">
          <div class="section-header">
            <i class="el-icon-time"></i>
            <span class="section-title">{{ t('operationLogs') }}</span>
          </div>
          <div class="operation-logs">
            <el-table :data="selectedOrderDetail.operationLogs" border size="small">
              <el-table-column :label="t('operationTime')" prop="operationTime" width="160" />
              <el-table-column :label="t('operationType')" prop="operationType" width="120" />
              <el-table-column :label="t('operatorName')" prop="operatorName" width="100" />
              <el-table-column :label="t('result')" prop="result" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.result === t('success') ? 'success' : 'danger'" size="small">
                    {{ row.result }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column :label="t('remark')" prop="remark" />
            </el-table>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">{{ t('close') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n'
import { ElMessage } from 'element-plus';
import { Search, Download, Loading } from '@element-plus/icons-vue';
import type {
  VehicleRegistrationListParams,
  VehicleRegistrationListItem,
  VehicleRegistrationDetail,
  UserRole
} from '@/types/vehicleRegistration.d';
import {
  getVehicleRegistrationList,
  getVehicleRegistrationDetail,
  pushVehicleRegistration,
  retryPushVehicleRegistration,
  getSalesAdvisorsList,
  exportVehicleRegistrationData
} from '@/api/modules/vehicleRegistration';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 使用国际化
const { t ,tc} = useModuleI18n('sales.vehicleRegistration');

// 使用字典数据
const {
  options: insuranceStatusOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.INSURANCE_STATUS);

// 数据定义
const loading = ref(false);
const tableData = ref<VehicleRegistrationListItem[]>([]);
const salesAdvisorsList = ref<Array<{ value: string; label: string }>>([]);

// 用户角色 (实际项目中应该从用户状态管理中获取)
const userRole = ref<UserRole>('vehicle_registration_officer');

// 搜索参数
const searchParams = reactive<VehicleRegistrationListParams>({
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  registrationStatus: '',
  vin: '',
  insuranceStatus: '',
  salesAdvisor: '',
  pageNum: 1,
  pageSize: 20
});

// 推送时间范围
const pushTimeRange = ref<[string, string] | null>(null);

// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
});

// 弹窗状态
const pushDialogVisible = ref(false);
const retryDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const pushLoading = ref(false);
const retryLoading = ref(false);

// 选中的订单
const selectedOrder = ref<VehicleRegistrationListItem | null>(null);
const selectedOrderDetail = ref<VehicleRegistrationDetail | null>(null);

// 计算属性
// 移除未使用的计算属性

// 页面初始化
onMounted(() => {
  loadData();
  loadSalesAdvisors();
});

// 过滤空值和空字符串的请求参数
const filterEmptyParams = (params: VehicleRegistrationListParams): VehicleRegistrationListParams => {
  const filteredParams: VehicleRegistrationListParams = {
    pageNum: params.pageNum,
    pageSize: params.pageSize
  };

  // 只保留非空值：不为空字符串、不为null、不为undefined
  if (params.orderNumber && params.orderNumber.trim() !== '') {
    filteredParams.orderNumber = params.orderNumber;
  }
  if (params.customerName && params.customerName.trim() !== '') {
    filteredParams.customerName = params.customerName;
  }
  if (params.customerPhone && params.customerPhone.trim() !== '') {
    filteredParams.customerPhone = params.customerPhone;
  }
  if (params.registrationStatus && params.registrationStatus.trim() !== '') {
    filteredParams.registrationStatus = params.registrationStatus;
  }
  if (params.vin && params.vin.trim() !== '') {
    filteredParams.vin = params.vin;
  }
  if (params.insuranceStatus && params.insuranceStatus.trim() !== '') {
    filteredParams.insuranceStatus = params.insuranceStatus;
  }
  if (params.salesAdvisor && params.salesAdvisor.trim() !== '') {
    filteredParams.salesAdvisor = params.salesAdvisor;
  }
  if (params.pushTimeStart) {
    filteredParams.pushTimeStart = params.pushTimeStart;
  }
  if (params.pushTimeEnd) {
    filteredParams.pushTimeEnd = params.pushTimeEnd;
  }

  return filteredParams;
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;

    // 准备搜索参数
    const requestParams = { ...searchParams };

    // 处理推送时间范围
    if (pushTimeRange.value) {
      requestParams.pushTimeStart = pushTimeRange.value[0];
      requestParams.pushTimeEnd = pushTimeRange.value[1];
    } else {
      requestParams.pushTimeStart = undefined;
      requestParams.pushTimeEnd = undefined;
    }

    requestParams.pageNum = pagination.pageNum;
    requestParams.pageSize = pagination.pageSize;

    // 过滤空值和空字符串的参数
    const filteredParams = filterEmptyParams(requestParams);

    const response = await getVehicleRegistrationList(filteredParams);
    console.log('response', response);
    tableData.value = response.result.records || [];
    pagination.total = response.result.total || 0;
  } catch (error) {
    ElMessage.error(t('common.loadFailed'));
  } finally {
    loading.value = false;
  }
};

// 加载销售顾问列表
const loadSalesAdvisors = async () => {
  try {
    const advisors = await getSalesAdvisorsList();
    salesAdvisorsList.value = advisors.result;
  } catch (error) {
    console.error('Failed to load sales advisors:', error);
  }
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    orderNumber: '',
    customerName: '',
    customerPhone: '',
    registrationStatus: '',
    vin: '',
    insuranceStatus: '',
    salesAdvisor: '',
    pageNum: 1,
    pageSize: 20
  });
  pushTimeRange.value = null;
  pagination.pageNum = 1;
  loadData();
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  loadData();
};

// 推送登记
const handlePushRegistration = (row: VehicleRegistrationListItem) => {
  selectedOrder.value = row;
  pushDialogVisible.value = true;
};

// 确认推送登记
const confirmPushRegistration = async () => {
  if (!selectedOrder.value) return;

  try {
    pushLoading.value = true;
    const result = await pushVehicleRegistration(selectedOrder.value.id);
    if (result.success) {
      ElMessage.success(t('pushSuccess'));
      pushDialogVisible.value = false;
      loadData(); // 重新加载数据
    } else {
      ElMessage.error(result.message || t('pushFailed'));
    }
  } catch {
    ElMessage.error(t('pushFailed'));
  } finally {
    pushLoading.value = false;
  }
};

// 重新推送
const handleRetryPush = async (row: VehicleRegistrationListItem) => {
  try {
    selectedOrder.value = row;
    const detail = await getVehicleRegistrationDetail(row.id);
    selectedOrderDetail.value = detail;
    retryDialogVisible.value = true;
  } catch {
    ElMessage.error(t('common.loadFailed'));
  }
};

// 确认重新推送
const confirmRetryPush = async () => {
  if (!selectedOrder.value) return;

  try {
    retryLoading.value = true;
    const result = await retryPushVehicleRegistration(selectedOrder.value.id);
    if (result.success) {
      ElMessage.success(t('retryPushSuccess'));
      retryDialogVisible.value = false;
      detailDialogVisible.value = false; // 关闭详情弹窗
      loadData(); // 重新加载数据
    } else {
      ElMessage.error(result.message || t('retryPushFailed'));
    }
  } catch {
    ElMessage.error(t('retryPushFailed'));
  } finally {
    retryLoading.value = false;
  }
};

// 查看详情
const handleViewDetails = async (row: VehicleRegistrationListItem) => {
  console.log('点击查看详情，行数据:', row);

  // 立即设置选中的订单并显示模态框
  selectedOrder.value = row;
  selectedOrderDetail.value = null; // 重置详情数据
  detailDialogVisible.value = true;
  console.log('立即显示模态框');

  try {
    console.log('开始获取详情数据，ID:', row.id);
    const detail = await getVehicleRegistrationDetail(row.id);
    console.log('获取到的详情数据:', detail);
    selectedOrderDetail.value = detail.result;
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error('加载失败');
    detailDialogVisible.value = false; // 获取失败时关闭模态框
  }
};

// 导出数据
const handleExport = async () => {
  try {
    // 准备导出参数
    const exportParams = { ...searchParams };
    if (pushTimeRange.value) {
      exportParams.pushTimeStart = pushTimeRange.value[0];
      exportParams.pushTimeEnd = pushTimeRange.value[1];
    } else {
      exportParams.pushTimeStart = undefined;
      exportParams.pushTimeEnd = undefined;
    }

    // 过滤空值和空字符串的参数
    const filteredParams = filterEmptyParams(exportParams);

    await exportVehicleRegistrationData(filteredParams);

    // 这里简化处理，实际项目中可能需要创建下载链接
    ElMessage.success(t('exportSuccess'));
  } catch {
    ElMessage.error(t('exportFailed'));
  }
};

// 工具方法
const maskCustomerName = (name: string): string => {
  if (!name) return '';
  return name.charAt(0) + '*'.repeat(name.length - 1);
};

const maskPhoneNumber = (phone: string): string => {
  if (!phone || phone.length < 7) return phone;
  return phone.slice(0, 3) + '****' + phone.slice(-4);
};

const formatAmount = (amount: number): string => {
  return amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const getStatusTagType = (status: string): string => {
  switch (status) {
    case 'pending':
    case '待登记':
      return 'primary';
    case 'processing':
    case '登记中':
      return 'warning';
    case 'success':
    case '登记成功':
      return 'success';
    case 'failed':
    case '登记失败':
      return 'danger';
    default:
      return 'info';
  }
};

const getStatusDisplayText = (status: string): string => {
  switch (status) {
    case 'pending':
      return '待登记';
    case 'processing':
      return '登记中';
    case 'success':
      return '登记成功';
    case 'failed':
      return '登记失败';
    case '待登记':
      return '待登记';
    case '登记中':
      return '登记中';
    case '登记成功':
      return '登记成功';
    case '登记失败':
      return '登记失败';
    default:
      return status;
  }
};

// 移除未使用的函数
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .operation-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.fee-amount {
  color: #67c23a;
  font-weight: bold;
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;

  .el-button {
    margin-left: 10px;
  }
}

.order-info-summary {
  margin-top: 20px;
}

.push-confirm-content,
.retry-push-content {
  .el-alert {
    margin-bottom: 20px;
  }
}

// 表格样式优化
:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

// 推送确认弹窗样式
.push-confirm-content {
  .confirm-message {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 24px;
    font-size: 14px;
    color: #434343;
    display: flex;
    align-items: flex-start;
  }

  .order-info-grid {
    .info-row {
      display: flex;
      margin-bottom: 16px;
      gap: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-item {
      flex: 1;

      label {
        display: block;
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
        font-weight: normal;
      }

      .info-value {
        background-color: #f5f5f5;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 14px;
        color: #333;
        min-height: 20px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    min-width: 80px;
  }
}

// 重新推送弹窗样式
.retry-push-content {
  .failure-reason-alert {
    background-color: #fce4ec;
    border: 1px solid #f8bbd9;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;

    .alert-title {
      font-size: 14px;
      color: #d32f2f;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .alert-description {
      font-size: 14px;
      color: #434343;
      line-height: 1.4;
    }
  }

  .retry-order-info {
    margin-bottom: 20px;

    .info-row {
      display: flex;
      gap: 24px;
    }

    .info-item {
      flex: 1;

      label {
        display: block;
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
        font-weight: normal;
      }

      .info-value {
        background-color: #f5f5f5;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 14px;
        color: #333;
        min-height: 20px;
      }
    }
  }

  .retry-tip {
    font-size: 14px;
    color: #666;
    text-align: left;
    margin-bottom: 8px;
  }
}

// 详情页面样式
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    border-radius: 4px 4px 0 0;

    i {
      margin-right: 8px;
      color: #409eff;
      font-size: 16px;
    }

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }

  .info-grid {
    padding: 16px;

    .info-row {
      display: flex;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-item {
        flex: 1;
        margin-right: 24px;

        &:last-child {
          margin-right: 0;
        }

        label {
          display: block;
          margin-bottom: 4px;
          font-size: 13px;
          color: #606266;
          font-weight: 500;
        }

        .info-value {
          font-size: 14px;
          color: #303133;
          line-height: 1.4;

          &.fee-amount {
            color: #67c23a;
            font-weight: 600;
          }

          &.failure-reason {
            color: #f56c6c;
          }
        }
      }
    }
  }

  .operation-logs {
    padding: 16px;
  }
}
</style>
