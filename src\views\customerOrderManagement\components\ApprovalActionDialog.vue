<!--
  审批操作弹窗组件
  已完成国际化改造，使用 useModuleI18n('orderApproval') 和 useModuleI18n('common')
  支持中英文切换
-->
<template>
  <el-dialog
    v-model="currentVisible"
    :title="t('dialogs.approveTitle')"
    :width="approvalDetail?.approvalType === 'orderUpdate' ? '800px' : '500px'"
    :before-close="handleClose"
    class="approval-action-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      v-loading="loading"
    >
      <!-- Conditional display based on approval type -->
      <template v-if="approvalDetail">
        <!-- Cancel Order Details -->
        <div v-if="approvalDetail.approvalType === 'orderCancel'">
          <el-form-item :label="t('cancelReason')">
            <div class="readonly-field">{{ approvalDetail.cancelReason || 'N/A' }}</div>
          </el-form-item>
        </div>

        <!-- Modify Order Details -->
        <div v-if="approvalDetail.approvalType === 'orderUpdate'">
          <h3 class="form-group-title">{{ t('changeDetails') }}</h3>
          <el-table :data="detailData" border class="mb-20">
            <el-table-column :label="t('changedField')" prop="detailName" />
            <el-table-column :label="t('originalValue')" prop="originalData" />
            <el-table-column :label="t('newValue')" prop="changedData" />
          </el-table>
        </div>
      </template>

      <h3 class="form-group-title">{{ t('review') }}</h3>
      <el-form-item :label="t('approvalResult')" prop="approvalResult">
        <el-radio-group v-model="formData.approvalResult">
          <el-radio label="approved">{{ t('result.approved') }}</el-radio>
          <el-radio label="rejected">{{ t('result.rejected') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="t('comments')" prop="comment">
        <el-input
          v-model="formData.comment"
          type="textarea"
          :rows="3"
          :placeholder="t('placeholders.enterComments')"
        ></el-input>
      </el-form-item>
<!--       <el-form-item-->
<!--        v-if="formData.approvalResult === 'rejected'"-->
<!--        :label="t('reason')"-->
<!--        prop="reason"-->
<!--      >-->
<!--        <el-input-->
<!--          v-model="formData.reason"-->
<!--          type="textarea"-->
<!--          :rows="3"-->
<!--          :placeholder="t('placeholders.enterReason')"-->
<!--        ></el-input>-->
<!--      </el-form-item>-->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit(formRef)">
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, computed } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { approveApproval, rejectApproval, getApprovalDetail } from "@/api/modules/approval";
import type { ApprovalListItem, ApprovalActionRequest } from "@/types/module";
import { useModuleI18n } from "@/composables/useModuleI18n";

const props = defineProps<{
  visible: boolean;
  approvalId: string | null;
  approvalDetail: ApprovalListItem | null;
}>();

const emit = defineEmits(["update:visible", "close", "success"]);

const { t } = useModuleI18n('sales.orderApproval');
const { tc } = useModuleI18n('common');

const formRef = ref<FormInstance>();
const loading = ref(false);
let detailData = [{}];
const formData = reactive({
  approvalId: "",
  result: "approved", // Default to approved
  comments: "",
  reason: "",
  approverId: "current_user_id" // TODO: 使用实际用户ID
});


const fetchDetail = async () => {
  if (!props.approvalId) return;
  loading.value = true;
  try {
    const approvalData = await getApprovalDetail(props.approvalId);
    detailData = approvalData.result;
  } catch (error: unknown) {
    ElMessage.error(tc('fetchFailed') + ': ' + (error as Error).message);
  } finally {
    loading.value = false;
  }
};

const rules = reactive<FormRules>({
  result: [
    { required: true, message: t("dialogs.resultRequired"), trigger: "change" },
  ],
  reason: [
    {
      required: true,
      message: t("dialogs.reasonRequired"),
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (formData.approvalResult === "rejected" && !value) {
          callback(new Error(t("dialogs.reasonRequired")));
        } else {
          callback();
        }
      },
    },
  ],
});

const currentVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.approvalId) {
      fetchDetail();
      formData.approvalId = props.approvalId;
      formData.approvalResult = "approved"; // Reset to default
      formData.comment = "";
      formData.reason = "";
    }
  }
);

const handleSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        if (formData.approvalResult === "approved") {
          await approveApproval(formData);
          ElMessage.success(t("dialogs.approveSuccess"));
        } else {
          await rejectApproval(formData);
          ElMessage.success(t("dialogs.rejectSuccess"));
        }
        emit("success");
        handleClose();
      } catch (error: unknown) {
        ElMessage.error(tc("operationFailed") + ": " + (error as Error).message);
      } finally {
        loading.value = false;
      }
    }
  });
};

const handleClose = () => {
  currentVisible.value = false;
  formRef.value?.resetFields();
  emit("close");
};
</script>

<style lang="scss" scoped>
.approval-action-dialog .el-dialog__body {
  padding-top: 10px;
}

.readonly-field {
  width: 100%;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #606266;
  min-height: 80px;
  white-space: pre-wrap;
  word-break: break-all;
}

.form-group-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 20px 0 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;

  &:first-child {
    margin-top: 0;
  }
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
