<!--
  审批导出对话框组件
  已完成国际化改造，使用 useModuleI18n('orderApproval') 和 useModuleI18n('common')
  支持中英文切换
-->
<template>
  <el-dialog
    v-model="currentVisible"
    :title="t('dialogs.exportTitle')"
    width="500px"
    :before-close="handleClose"
    class="export-approval-data-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item :label="t('export.format')" prop="format">
        <el-select v-model="formData.format" :placeholder="t('export.format')">
          <el-option label="Excel" value="excel"></el-option>
          <el-option label="PDF" value="pdf"></el-option>
          <el-option label="CSV" value="csv"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('export.range')" prop="range">
        <el-select v-model="formData.range" :placeholder="t('export.range')">
          <el-option :label="t('export.currentPage')" value="current_page"></el-option>
          <el-option :label="t('export.filteredResult')" value="filtered_result"></el-option>
          <el-option :label="t('export.allData')" value="all_data"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('timeRange')" v-if="formData.range === 'all_data' || formData.range === 'filtered_result'">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="-"
          :start-placeholder="tc('startDate')"
          :end-placeholder="tc('endDate')"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit(formRef)">
          {{ tc('confirmExport') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, computed } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { exportApprovalData } from "@/api/modules/approval";
import type { ExportSettings, ApprovalListParams } from "@/types/module";
import { useModuleI18n } from "@/composables/useModuleI18n";

const props = defineProps<{
  visible: boolean;
  filterParams: ApprovalListParams; // 从父组件传递的筛选参数
}>();

const emit = defineEmits(["update:visible", "close", "success"]);

const { t } = useModuleI18n('sales.orderApproval');
const { tc } = useModuleI18n('common');

const formRef = ref<FormInstance>();
const loading = ref(false);
const dateRange = ref<[string, string] | null>(null);

const formData = reactive<ExportSettings>({
  format: "excel",
  range: "current_page",
  timeRange: undefined,
});

const rules = reactive<FormRules>({
  format: [{ required: true, message: t('formatRequired'), trigger: "change" }],
  range: [{ required: true, message: t('rangeRequired'), trigger: "change" }],
});

const currentVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      formData.format = "excel";
      formData.range = "current_page";
      formData.timeRange = undefined;
      dateRange.value = null;
    }
  }
);

watch(
  () => dateRange.value,
  (newVal) => {
    if (newVal && newVal.length === 2) {
      formData.timeRange = {
        start: newVal[0],
        end: newVal[1],
      };
    } else {
      formData.timeRange = undefined;
    }
  }
);

const handleSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        let paramsToSend = {};
        if (formData.range === "current_page") {
          // 对于当前页，直接使用父组件传来的 filterParams
          paramsToSend = props.filterParams;
        } else if (formData.range === "filtered_result") {
          // 对于筛选结果，使用父组件传来的 filterParams 并加上时间范围
          paramsToSend = { ...props.filterParams, ...formData.timeRange };
        } else if (formData.range === "all_data") {
          // 对于所有数据，只使用时间范围
          paramsToSend = { ...formData.timeRange };
        }

        await exportApprovalData(formData, paramsToSend);
        ElMessage.success(t("dialogs.exportSuccess"));
        handleClose();
      } catch (error: unknown) {
        ElMessage.error(tc("operationFailed") + ": " + (error as Error).message);
      } finally {
        loading.value = false;
      }
    }
  });
};

const handleClose = () => {
  currentVisible.value = false;
  formRef.value?.resetFields();
  emit("close");
};
</script>

<style scoped>
.export-approval-data-dialog .el-dialog__body {
  padding-top: 10px;
}
.export-approval-data-dialog .el-select,
.export-approval-data-dialog .el-date-editor {
  width: 100%;
}
</style>
