<template>
  <el-dialog
    :title="t('detailTitle')"
    :model-value="modelValue"
    @update:model-value="(val: boolean) => $emit('update:modelValue', val)"
    width="1000px"
    top="3vh"
    custom-class="delivery-detail-dialog"
  >
    <div v-loading="loading" style="min-height: 500px;">
      <div v-if="orderData" class="detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-header">
            <span class="section-icon">ℹ️</span>
            <span class="section-title">{{ t('basicInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('orderNumber') }}</span>
                <span class="value">{{ orderData?.orderNumber || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('customerName') }}</span>
                <span class="value">{{ orderData?.customerName || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('orderCreatorName') }}</span>
                <span class="value">{{ orderData?.customerName || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('orderCreatorPhone') }}</span>
                <span class="value">{{ orderData?.customerPhone || 'N/A' }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('customerPhone') }}</span>
                <span class="value">{{ orderData?.customerPhone || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('customerType') }}</span>
                <span class="value">{{ orderData?.customerType || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('idType') }}</span>
                <span class="value">{{ orderData?.idType || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('idNumber') }}</span>
                <span class="value">{{ orderData?.idNumber || 'N/A' }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item full-width">
                <span class="label">{{ t('address') }}</span>
                <span class="value">{{ orderData?.customerAddress || 'N/A' }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('city') }}</span>
                <span class="value">{{ orderData?.customerCity || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('postcode') }}</span>
                <span class="value">{{ orderData?.customerPostcode || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('state') }}</span>
                <span class="value">{{ orderData?.customerState || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <!-- 空位保持对齐 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 订单信息 -->
        <div class="detail-section">
          <div class="section-header">
            <span class="section-icon">🛒</span>
            <span class="section-title">{{ t('orderInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('orderCreateTime') }}</span>
                <span class="value">{{ orderData?.createTime || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('orderStatus') }}</span>
                <span class="value">
                  <el-tag :type="getOrderStatusTagType(orderData.orderStatus)" size="small">
                    {{ getOrderStatusText(orderData.orderStatus) }}
                  </el-tag>
                </span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('orderPaymentStatus') }}</span>
                <span class="value">{{ orderData?.orderPaymentStatus || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('paymentMethod') }}</span>
                <span class="value">{{ orderData?.paymentMethod || 'N/A' }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('invoiceTime') }}</span>
                <span class="value">{{ orderData?.invoiceTime || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('dealerStore') }}</span>
                <span class="value">{{ orderData?.dealerStore || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('salesConsultant') }}</span>
                <span class="value">{{ orderData?.salesConsultant || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <!-- 空位保持对齐 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 车辆信息 -->
        <div class="detail-section">
          <div class="section-header">
            <span class="section-icon">🚗</span>
            <span class="section-title">{{ t('vehicleInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('vin') }}</span>
                <span class="value">{{ orderData?.vin || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('model') }}</span>
                <span class="value">{{ orderData?.model || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('variant') }}</span>
                <span class="value">{{ orderData?.variant || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('color') }}</span>
                <span class="value">{{ orderData?.color || 'N/A' }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('warehouseName') }}</span>
                <span class="value">{{ orderData?.warehouseName || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('productionDate') }}</span>
                <span class="value">{{ orderData?.productionDate || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('entryTime') }}</span>
                <span class="value">{{ orderData?.entryTime || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <!-- 空位保持对齐 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 交车信息 -->
        <div class="detail-section">
          <div class="section-header">
            <span class="section-icon">🚚</span>
            <span class="section-title">{{ t('deliveryInfo') }}</span>
          </div>
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('deliveryNumber') }}</span>
                <span class="value">{{ orderData?.deliveryNumber || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('deliveryStatus') }}</span>
                <span class="value">
                  <el-tag :type="getStatusTagType(orderData.deliveryStatus)" size="small">
                    {{ getStatusText(orderData.deliveryStatus) }}
                  </el-tag>
                </span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('customerConfirmed') }}</span>
                <span class="value">{{ orderData?.customerConfirmed ? tc('yes') : tc('no') }}</span>
              </div>
              <div class="info-item">
                <span class="label">{{ t('confirmationType') }}</span>
                <span class="value">{{ orderData?.confirmationType ? getConfirmationTypeText(orderData.confirmationType) : '-' }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">{{ t('deliveryTime') }}</span>
                <span class="value">{{ orderData?.deliveryTime || '-' }}</span>
              </div>
            </div>
            <div v-if="orderData?.signaturePhoto" class="info-row">
              <div class="info-item full-width">
                <span class="label">{{ t('signaturePhoto') }}</span>
                <div class="signature-container">
                  <el-image
                    :src="orderData.signaturePhoto"
                    :preview-src-list="[orderData.signaturePhoto]"
                    fit="contain"
                    style="width: 200px; height: 120px; border: 1px solid #ddd; border-radius: 4px;"
                    :alt="t('signaturePhoto')"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-data-tip">
        {{ t('noDetailData') }}
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)" size="default">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { ElImage, ElTag } from 'element-plus'
import type { DeliveryOrderItem, DeliveryStatus, OrderStatus, ConfirmationType } from '@/types/module'

defineProps({
  modelValue: { type: Boolean, default: false },
  orderData: { type: Object as () => DeliveryOrderItem | null, default: null },
  loading: { type: Boolean, default: false }
})

defineEmits(['update:modelValue'])
const { t } = useModuleI18n('sales.delivery')
const { t: tc } = useModuleI18n('common')

// Status utility functions (reuse from main view or define here if specific)
const getStatusText = (status: DeliveryStatus) => {
  const statusMap: Record<string, string> = {
    'pending': t('statusPending'),
    'processing': t('statusProcessing'),
    'delivered': t('statusDelivered'),
    'cancelled': t('statusCancelled'),
    'pending_delivery': t('statusPending'),
    'pending_confirm': t('statusProcessing')
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: DeliveryStatus) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'processing': 'primary',
    'delivered': 'success',
    'cancelled': 'danger',
    'pending_delivery': 'warning',
    'pending_confirm': 'primary'
  }
  return (typeMap[status] as 'success' | 'warning' | 'danger' | 'info' | 'primary') || 'info'
}

const getOrderStatusText = (status: OrderStatus) => {
  const statusMap: Record<string, string> = {
    'pending': t('orderStatusPending'),
    'confirmed': t('orderStatusConfirmed'),
    'cancelled': t('orderStatusCancelled'),
    'normal': t('orderStatusNormal'),
    'pending_delivery': t('orderStatusPending'),
    'delivered': t('orderStatusConfirmed')
  }
  return statusMap[status] || status
}

const getOrderStatusTagType = (status: OrderStatus) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'confirmed': 'success',
    'cancelled': 'danger',
    'normal': 'success',
    'pending_delivery': 'warning',
    'delivered': 'success'
  }
  return (typeMap[status] as 'success' | 'warning' | 'danger' | 'info' | 'primary') || 'info'
}

const getConfirmationTypeText = (type: ConfirmationType) => {
  const typeMap: Record<string, string> = {
    'online': t('confirmationTypeOnline'),
    'offline': t('confirmationTypeOffline'),
    'phone': t('confirmationTypePhone'),
    'app': t('confirmationTypeOnline')
  }
  return typeMap[type] || type
}
</script>

<style scoped>
.delivery-detail-dialog {
  border-radius: 8px;
}

.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 32px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fff;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 6px 6px 0 0;
}

.section-icon {
  margin-right: 8px;
  font-size: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.info-grid {
  padding: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 16px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 24px;
}

.info-item:last-child {
  margin-right: 0;
}

.info-item.full-width {
  flex: 2;
}

.info-item .label {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  font-weight: normal;
}

.info-item .value {
  font-size: 13px;
  color: #333;
  font-weight: 400;
  word-break: break-all;
}

.signature-container {
  margin-top: 8px;
}

.no-data-tip {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 60px 0;
}

.dialog-footer {
  text-align: right;
  padding: 16px 0 0 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
  }

  .info-item {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style>
