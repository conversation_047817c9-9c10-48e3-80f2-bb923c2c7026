<template>
  <div class="insurance-info-tab">
    <!-- 保险推送 -->
    <div class="push-section">
      <el-button 
        type="primary" 
        :disabled="isPushed"
        @click="handlePushToInsurance"
      >
        {{ isPushed ? '已推送' : '推送至保险系统' }}
      </el-button>
    </div>

    <!-- 保单信息 -->
    <div class="policy-section">
      <h3 class="section-subtitle">保单信息</h3>
      <el-table :data="props.formData.insurancePolicies" style="width: 100%">
        <el-table-column prop="policyNo" label="保单号" width="180" />
        <el-table-column prop="type" label="保险类型" width="150" />
        <el-table-column prop="company" label="保险公司" width="150" />
        <el-table-column prop="startDate" label="生效日期" width="180" />
        <el-table-column prop="endDate" label="到期日期" width="180" />
        <el-table-column prop="price" label="保险价格">
          <template #default="{ row }">
            RM {{ row.price.toFixed(2) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 保险总金额 -->
      <div class="insurance-total">
        <span>保险总金额：</span>
        <span class="amount">RM {{ props.formData.insuranceTotalAmount.toFixed(2) }}</span>
      </div>
    </div>

    <!-- 备注区域 -->
    <div class="remark-section">
      <el-form-item label="备注">
        <el-input
          v-model="props.formData.insuranceRemark"
          type="textarea"
          :rows="4"
          resize="none"
          style="height: 100px"
        />
      </el-form-item>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

interface InsurancePolicy {
  policyNo: string;
  type: string;
  company: string;
  startDate: string;
  endDate: string;
  price: number;
}

interface FormData {
  insurancePolicies: InsurancePolicy[];
  insuranceTotalAmount: number;
  insuranceRemark: string;
  vin?: string;
}

const props = defineProps<{
  formData: FormData;
}>();

const emit = defineEmits<{
  'update:insurance': [insurance: Partial<FormData>];
}>();

const isPushed = ref(false);

// 处理推送至保险系统
const handlePushToInsurance = async () => {
  // 验证必要条件
  if (!props.formData.vin) {
    ElMessage.error('VIN码未关联，无法推送至保险系统');
    return;
  }

  try {
    // TODO: 调用API推送至保险系统
    // 模拟推送成功
    const mockPolicies: InsurancePolicy[] = [
      {
        policyNo: 'POL' + Date.now(),
        type: '综合保险',
        company: 'ABC Insurance',
        startDate: '2024-01-01',
        endDate: '2025-01-01',
        price: 2000,
      }
    ];

    emit('update:insurance', {
      insurancePolicies: mockPolicies,
      insuranceTotalAmount: mockPolicies.reduce((sum, policy) => sum + policy.price, 0)
    });

    isPushed.value = true;
    ElMessage.success('推送成功');
  } catch (error) {
    ElMessage.error('推送失败，请重试');
  }
};
</script>

<style scoped>
.insurance-info-tab {
  padding: 20px 0;
}

.push-section {
  margin-bottom: 20px;
}

.section-subtitle {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0;
  color: #303133;
}

.insurance-total {
  margin-top: 20px;
  text-align: right;
  padding: 10px;
  background-color: #f5f7fa;

  .amount {
    font-weight: bold;
    color: #409EFF;
    margin-left: 10px;
  }
}

.remark-section {
  margin-top: 20px;
}
</style> 