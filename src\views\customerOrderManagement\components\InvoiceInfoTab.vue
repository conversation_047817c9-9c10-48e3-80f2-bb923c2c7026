<template>
  <div class="invoice-info-tab">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="开票">
          <el-select v-model="props.formData.invoiceType" disabled>
            <el-option label="个人" value="personal" />
            <el-option label="公司" value="company" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="开票名称">
          <el-input v-model="props.formData.invoiceName" readonly />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="开票电话">
          <el-input v-model="props.formData.invoicePhone" readonly />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="开票地址">
          <el-input v-model="props.formData.invoiceAddress" readonly />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
interface FormData {
  invoiceType: 'personal' | 'company';
  invoiceName: string;
  invoicePhone: string;
  invoiceAddress: string;
}

const props = defineProps<{
  formData: FormData;
}>();
</script>

<style scoped>
.invoice-info-tab {
  padding: 20px 0;
}
</style> 