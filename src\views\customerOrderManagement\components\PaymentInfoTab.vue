<template>
  <div class="payment-info-tab">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="支付方式">
          <el-select 
            v-model="props.formData.paymentMethod"
            @change="handlePaymentMethodChange"
          >
            <el-option label="全款" value="full_payment" />
            <el-option label="分期" value="installment" />
          </el-select>
        </el-form-item>
      </el-col>
      
      <el-col :span="6" v-if="props.formData.paymentMethod === 'installment'">
        <el-form-item label="贷款资质审核状态">
          <el-select 
            v-model="props.formData.loanApprovalStatus"
            @change="handleLoanStatusChange"
          >
            <el-option label="待审核" value="pending" />
            <el-option label="审核通过" value="approved" />
            <el-option label="审核驳回" value="rejected" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="6">
        <el-form-item label="定金金额">
          <el-input 
            v-model="props.formData.depositAmount" 
            readonly
            :formatter="(value: number | string) => `RM ${Number(value).toFixed(2)}`"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20" v-if="showLoanFields">
      <el-col :span="6">
        <el-form-item label="贷款金额">
          <el-input-number 
            v-model="props.formData.loanAmount"
            :min="0"
            :precision="2"
            :step="100"
            style="width: 100%"
            @change="handleLoanAmountChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="贷款期数（月）">
          <el-input-number
            v-model="props.formData.loanTerms"
            :min="1"
            :precision="0"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="尾款金额">
          <el-input 
            v-model="props.formData.finalPayment"
            readonly
            :formatter="(value: number | string) => `RM ${Number(value).toFixed(2)}`"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface FormData {
  paymentMethod: 'full_payment' | 'installment';
  loanApprovalStatus: 'pending' | 'approved' | 'rejected';
  depositAmount: number;
  loanAmount: number;
  loanTerms: number;
  finalPayment: number;
}

const props = defineProps<{
  formData: FormData;
}>();

const emit = defineEmits<{
  'update:payment': [payment: Partial<FormData>];
}>();

// 是否显示贷款相关字段
const showLoanFields = computed(() => {
  return props.formData.paymentMethod === 'installment' && 
         props.formData.loanApprovalStatus === 'approved';
});

// 处理支付方式变更
const handlePaymentMethodChange = () => {
  if (props.formData.paymentMethod === 'full_payment') {
    emit('update:payment', {
      loanApprovalStatus: 'pending' as const,
      loanAmount: 0,
      loanTerms: 1,
      finalPayment: 0
    });
  }
};

// 处理贷款状态变更
const handleLoanStatusChange = () => {
  if (props.formData.loanApprovalStatus !== 'approved') {
    emit('update:payment', {
      loanAmount: 0,
      loanTerms: 1,
      finalPayment: 0
    });
  }
};

// 处理贷款金额变更
const handleLoanAmountChange = (value: number) => {
  emit('update:payment', {
    loanAmount: value
  });
};
</script>

<style scoped>
.payment-info-tab {
  padding: 20px 0;
}
</style> 