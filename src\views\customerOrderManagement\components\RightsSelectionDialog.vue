<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('rightsSelectionDialog.title')"
    width="80%"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="rights-selection-container">
      <!-- 搜索表单 -->
      <el-card class="search-card" shadow="never">
        <el-form :model="searchParams" class="search-form" inline>
          <el-form-item :label="t('rightCode')">
            <el-input
              v-model="searchParams.rightCode"
              :placeholder="t('rightsSelectionDialog.rightCodePlaceholder')"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="t('rightName')">
            <el-input
              v-model="searchParams.rightName"
              :placeholder="t('rightsSelectionDialog.rightNamePlaceholder')"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              {{ tc('search') }}
            </el-button>
            <el-button @click="handleReset">{{ tc('reset') }}</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 可选权益列表 -->
      <el-card class="rights-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">{{ t('rightsSelectionDialog.availableRights') }}</span>
            <span class="total-count">{{ t('rightsSelectionDialog.totalCount', { count: total }) }}</span>
          </div>
        </template>

        <el-table
          :data="availableRightsList"
          :loading="loading"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="60" align="center" />
          <el-table-column type="index" :label="tc('index')" width="80" align="center" />
          <el-table-column prop="code" :label="t('rightCode')" width="120" align="left" />
          <el-table-column prop="name" :label="t('rightName')" align="left" />
          <el-table-column prop="mode" :label="t('rightMode')" width="100" align="center" />
          <el-table-column prop="discountPrice" :label="t('discountAmount')" width="120" align="right">
            <template #default="{ row }">
              RM {{ row.discountPrice }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" :label="t('effectiveDate')" width="120" align="center" />
          <el-table-column prop="expiryDate" :label="t('expiryDate')" width="120" align="center" />
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="searchParams.page"
            v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 已选权益列表 -->
      <el-card class="selected-rights-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">{{ t('rightsSelectionDialog.selectedRights') }}</span>
            <span class="selected-count">{{ t('rightsSelectionDialog.selectedCount', { count: tempSelectedRights.length }) }}</span>
          </div>
        </template>

        <el-table :data="tempSelectedRights" border style="width: 100%">
          <el-table-column type="index" :label="tc('index')" width="80" align="center" />
          <el-table-column prop="code" :label="t('rightCode')" width="120" align="left" />
          <el-table-column prop="name" :label="t('rightName')" align="left" />
          <el-table-column prop="mode" :label="t('rightMode')" width="100" align="center" />
          <el-table-column prop="discountPrice" :label="t('discountAmount')" width="120" align="right">
            <template #default="{ row }">
              RM {{ row.discountPrice }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" :label="t('effectiveDate')" width="120" align="center" />
          <el-table-column prop="expiryDate" :label="t('expiryDate')" width="120" align="center" />
          <el-table-column :label="tc('actions')" width="100" align="center">
            <template #default="{ $index }">
              <el-button link type="danger" @click="handleRemoveSelectedRight($index)">
                {{ tc('remove') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="tempSelectedRights.length === 0" class="empty-state">
          {{ t('rightsSelectionDialog.noRightsSelected') }}
        </div>

        <!-- 总计信息 -->
        <div v-if="tempSelectedRights.length > 0" class="summary-section">
          <el-divider />
          <div class="summary-item">
            <span class="summary-label">{{ t('rightsSelectionDialog.totalDiscountAmount') }}:</span>
            <span class="summary-amount">RM {{ calculateTotalDiscount() }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm">
          {{ t('rightsSelectionDialog.confirmButtonText', { count: tempSelectedRights.length }) }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type {
  AvailableRight,
  SalesOrderRight,
  RightSearchParams,
  PaginationResponse
} from '@/types/module.d'
import { getAvailableRights } from '@/api/modules/order'

const { t, tc } = useModuleI18n('sales.salesOrderManagement');

// Props
interface Props {
  visible: boolean
  selectedRights: SalesOrderRight[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selectedRights: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: [selectedRights: SalesOrderRight[]]
}>()

// 响应式数据
const loading = ref(false)
const availableRightsList = ref<AvailableRight[]>([])
const total = ref(0)
const tempSelectedRights = ref<SalesOrderRight[]>([])
const selectedAvailableRights = ref<AvailableRight[]>([])

// 搜索参数
const searchParams = reactive<RightSearchParams>({
  page: 1,
  pageSize: 10,
  rightCode: '',
  rightName: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value)
  }
})

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    // 初始化临时选中的权益
    tempSelectedRights.value = [...props.selectedRights]
    fetchAvailableRights()
  }
})

// 获取可选权益列表
const fetchAvailableRights = async () => {
  try {
    loading.value = true
    const response: PaginationResponse<AvailableRight> = await getAvailableRights(searchParams)
    console.log('获取可选权益成功:', response.records);
    availableRightsList.value = response.records
    total.value = response.total
  } catch (error) {
    console.error('获取可选权益失败:', error)
    ElMessage.error(t('rightsSelectionDialog.fetchRightsError'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchParams.page = 1
  fetchAvailableRights()
}

// 重置
const handleReset = () => {
  searchParams.rightCode = ''
  searchParams.rightName = ''
  searchParams.page = 1
  fetchAvailableRights()
}

// 分页相关
const handleSizeChange = (size: number) => {
  searchParams.pageSize = size
  searchParams.page = 1
  fetchAvailableRights()
}

const handleCurrentChange = (page: number) => {
  searchParams.page = page
  fetchAvailableRights()
}

// 选择权益
const handleSelectionChange = (selection: AvailableRight[]) => {
  selectedAvailableRights.value = selection

  // 将选中的权益添加到临时选中列表
  selection.forEach(right => {
    const exists = tempSelectedRights.value.find(item => item.id === right.id)
    if (!exists) {
      tempSelectedRights.value.push({
        id: right.id,
        rightCode: right.rightCode,
        rightName: right.rightName,
        rightMode: right.rightMode,
        discountAmount: right.discountAmount,
        effectiveDate: right.effectiveDate,
        expiryDate: right.expiryDate
      })
    }
  })
}

// 移除已选权益
const handleRemoveSelectedRight = (index: number) => {
  tempSelectedRights.value.splice(index, 1)
}

// 计算总折扣金额
const calculateTotalDiscount = () => {
  return tempSelectedRights.value.reduce((total, right) => total + right.discountPrice, 0)
}

// 确认选择
const handleConfirm = () => {
  emit('confirm', [...tempSelectedRights.value])
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  tempSelectedRights.value = []
  selectedAvailableRights.value = []
}

// 组件挂载
onMounted(() => {
  if (props.visible) {
    fetchAvailableRights()
  }
})
</script>

<style scoped>
.rights-selection-container {
  max-height: 70vh;
  overflow-y: auto;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.rights-card {
  margin-bottom: 20px;
}

.selected-rights-card {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.total-count,
.selected-count {
  color: #909399;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  color: #909399;
  padding: 40px 0;
  font-size: 14px;
}

.summary-section {
  margin-top: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.summary-label {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.summary-amount {
  font-size: 18px;
  font-weight: bold;
  color: #67C23A;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table .cell) {
  word-break: break-word;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-divider) {
  margin: 16px 0;
}
</style>
