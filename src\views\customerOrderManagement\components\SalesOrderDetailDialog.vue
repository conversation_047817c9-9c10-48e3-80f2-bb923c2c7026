<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('salesOrder.detailTitle')"
    width="90%"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="order-detail-container" v-loading="loading">
      <div v-if="orderDetail">
        <!-- 订单基本信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">{{ $t('salesOrder.basicInfo') }}</span>
              <div class="status-tags">
                <el-tag :type="getOrderStatusTagType(orderDetail.orderStatus)" size="large">
                  {{ getOrderStatusText(orderDetail.orderStatus) }}
                </el-tag>
                <el-tag :type="getApprovalStatusTagType(orderDetail.approvalStatus)" size="large">
                  {{ getApprovalStatusText(orderDetail.approvalStatus) }}
                </el-tag>
                <el-tag :type="getPaymentStatusTagType(orderDetail.paymentStatus)" size="large">
                  {{ getPaymentStatusText(orderDetail.paymentStatus) }}
                </el-tag>
              </div>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.orderNumber') }}:</span>
                <span class="value">{{ orderDetail.orderNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.createTime') }}:</span>
                <span class="value">{{ orderDetail.createTime }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.totalAmount') }}:</span>
                <span class="value amount">RM {{ orderDetail.totalInvoiceAmount?.toLocaleString() }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ $t('salesOrder.customerInfo') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.ordererName') }}:</span>
                <span class="value">{{ orderDetail.ordererName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.ordererPhone') }}:</span>
                <span class="value">{{ orderDetail.ordererPhone }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerName') }}:</span>
                <span class="value">{{ orderDetail.buyerName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerPhone') }}:</span>
                <span class="value">{{ orderDetail.buyerPhone }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerIdType') }}:</span>
                <span class="value">{{ orderDetail.buyerIdType }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerIdNumber') }}:</span>
                <span class="value">{{ orderDetail.buyerIdNumber }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerEmail') }}:</span>
                <span class="value">{{ orderDetail.buyerEmail }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerType') }}:</span>
                <el-tag :type="orderDetail.buyerType === 'individual' ? 'primary' : 'success'">
                  {{ getBuyerTypeText(orderDetail.buyerType) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerAddress') }}:</span>
                <span class="value">{{ orderDetail.buyerAddress }}</span>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerState') }}:</span>
                <span class="value">{{ orderDetail.buyerState }}</span>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerCity') }}:</span>
                <span class="value">{{ orderDetail.buyerCity }}</span>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.buyerPostcode') }}:</span>
                <span class="value">{{ orderDetail.buyerPostcode }}</span>
              </div>
            </el-col>
            <el-col :span="4">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.salesConsultant') }}:</span>
                <span class="value">{{ orderDetail.salesConsultantName }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ $t('salesOrder.vehicleInfo') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.model') }}:</span>
                <span class="value">{{ orderDetail.model }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.variant') }}:</span>
                <span class="value">{{ orderDetail.variant }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.color') }}:</span>
                <span class="value">{{ orderDetail.color }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.vin') }}:</span>
                <span class="value">{{ orderDetail.vin || 'N/A' }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.salesSubtotal') }}:</span>
                <span class="value amount">RM {{ orderDetail.salesSubtotal.toLocaleString() }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.numberPlatesFee') }}:</span>
                <span class="value amount">RM {{ orderDetail.numberPlatesFee.toLocaleString() }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.storeRegion') }}:</span>
                <span class="value">{{ orderDetail.storeRegion }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.storeName') }}:</span>
                <span class="value">{{ orderDetail.storeName }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 选配件信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">{{ $t('salesOrder.accessoriesInfo') }}</span>
              <span class="total-amount">{{ $t('salesOrder.totalAmount') }}: RM {{ orderDetail.accessoriesTotalAmount.toLocaleString() }}</span>
            </div>
          </template>

          <el-table :data="orderDetail.accessories" border style="width: 100%">
            <el-table-column type="index" :label="$t('common.index')" width="80" align="center" />
            <el-table-column prop="category" :label="$t('salesOrder.accessoryCategory')" align="left" />
            <el-table-column prop="accessoryName" :label="$t('salesOrder.accessoryName')" align="left" />
            <el-table-column prop="unitPrice" :label="$t('salesOrder.unitPrice')" align="right">
              <template #default="{ row }">
                RM {{ row.unitPrice.toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" :label="$t('salesOrder.quantity')" width="100" align="center" />
            <el-table-column prop="totalPrice" :label="$t('salesOrder.totalPrice')" align="right">
              <template #default="{ row }">
                RM {{ row.totalPrice.toLocaleString() }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 开票信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ $t('salesOrder.invoicingInfo') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.invoicingType') }}:</span>
                <span class="value">{{ orderDetail.invoicingType }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.invoicingName') }}:</span>
                <span class="value">{{ orderDetail.invoicingName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.invoicingPhone') }}:</span>
                <span class="value">{{ orderDetail.invoicingPhone }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.invoicingAddress') }}:</span>
                <span class="value">{{ orderDetail.invoicingAddress }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 权益信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">{{ $t('salesOrder.rightsInfo') }}</span>
              <span class="total-amount">{{ $t('salesOrder.discountAmount') }}: RM {{ orderDetail.rightsDiscountAmount.toLocaleString() }}</span>
            </div>
          </template>

          <el-table :data="orderDetail.rights" border style="width: 100%">
            <el-table-column type="index" :label="$t('common.index')" width="80" align="center" />
            <el-table-column prop="rightCode" :label="$t('salesOrder.rightCode')" align="left" />
            <el-table-column prop="rightName" :label="$t('salesOrder.rightName')" align="left" />
            <el-table-column prop="rightMode" :label="$t('salesOrder.rightMode')" align="center" />
            <el-table-column prop="discountAmount" :label="$t('salesOrder.discountAmount')" align="right">
              <template #default="{ row }">
                RM {{ row.discountAmount.toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column prop="effectiveDate" :label="$t('salesOrder.effectiveDate')" width="120" align="center" />
            <el-table-column prop="expiryDate" :label="$t('salesOrder.expiryDate')" width="120" align="center" />
          </el-table>
        </el-card>

        <!-- 支付信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ $t('salesOrder.paymentInfo') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.paymentMethod') }}:</span>
                <el-tag :type="orderDetail.paymentMethod === 'full_payment' ? 'success' : 'warning'">
                  {{ getPaymentMethodText(orderDetail.paymentMethod) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6" v-if="orderDetail.loanApprovalStatus">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.loanApprovalStatus') }}:</span>
                <el-tag :type="getLoanApprovalStatusTagType(orderDetail.loanApprovalStatus)">
                  {{ getLoanApprovalStatusText(orderDetail.loanApprovalStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.depositAmount') }}:</span>
                <span class="value amount">RM {{ orderDetail.depositAmount.toLocaleString() }}</span>
              </div>
            </el-col>
            <el-col :span="6" v-if="orderDetail.loanAmount">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.loanAmount') }}:</span>
                <span class="value amount">RM {{ orderDetail.loanAmount.toLocaleString() }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.balanceAmount') }}:</span>
                <span class="value amount">RM {{ orderDetail.balanceAmount.toLocaleString() }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.remainingAmount') }}:</span>
                <span class="value amount">RM {{ orderDetail.remainingAmount?.toLocaleString() || '0' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 保险信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">{{ $t('salesOrder.insuranceInfo') }}</span>
              <div class="status-and-amount">
                <el-tag :type="getInsuranceStatusTagType(orderDetail.insuranceStatus)">
                  {{ getInsuranceStatusText(orderDetail.insuranceStatus) }}
                </el-tag>
                <span class="total-amount">{{ $t('salesOrder.totalAmount') }}: RM {{ orderDetail.insuranceTotalAmount.toLocaleString() }}</span>
              </div>
            </div>
          </template>

          <el-table :data="orderDetail.insuranceList" border style="width: 100%">
            <el-table-column type="index" :label="$t('common.index')" width="80" align="center" />
            <el-table-column prop="policyNumber" :label="$t('salesOrder.policyNumber')" align="left" />
            <el-table-column prop="insuranceType" :label="$t('salesOrder.insuranceType')" align="left" />
            <el-table-column prop="insuranceCompany" :label="$t('salesOrder.insuranceCompany')" align="left" />
            <el-table-column prop="effectiveDate" :label="$t('salesOrder.effectiveDate')" width="120" align="center" />
            <el-table-column prop="expiryDate" :label="$t('salesOrder.expiryDate')" width="120" align="center" />
            <el-table-column prop="insurancePrice" :label="$t('salesOrder.insurancePrice')" align="right">
              <template #default="{ row }">
                RM {{ row.insurancePrice.toLocaleString() }}
              </template>
            </el-table-column>
          </el-table>

          <div v-if="orderDetail.insuranceNotes" class="notes-section">
            <div class="info-item">
              <span class="label">{{ $t('salesOrder.insuranceNotes') }}:</span>
              <p class="value">{{ orderDetail.insuranceNotes }}</p>
            </div>
          </div>
        </el-card>

        <!-- OTR费用信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">{{ $t('salesOrder.otrFeesInfo') }}</span>
              <span class="total-amount">{{ $t('salesOrder.totalAmount') }}: RM {{ orderDetail.otrFeesTotalAmount.toLocaleString() }}</span>
            </div>
          </template>

          <el-table :data="orderDetail.otrFees" border style="width: 100%">
            <el-table-column type="index" :label="$t('common.index')" width="80" align="center" />
            <el-table-column prop="ticketNumber" :label="$t('salesOrder.ticketNumber')" align="left" />
            <el-table-column prop="feeItem" :label="$t('salesOrder.feeItem')" align="left" />
            <el-table-column prop="feePrice" :label="$t('salesOrder.feePrice')" align="right">
              <template #default="{ row }">
                RM {{ row.feePrice.toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column prop="effectiveDate" :label="$t('salesOrder.effectiveDate')" width="120" align="center" />
            <el-table-column prop="expiryDate" :label="$t('salesOrder.expiryDate')" width="120" align="center" />
          </el-table>
        </el-card>

        <!-- 状态信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ $t('salesOrder.statusInfo') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.orderStatus') }}:</span>
                <el-tag :type="getOrderStatusTagType(orderDetail.orderStatus)">
                  {{ getOrderStatusText(orderDetail.orderStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.approvalStatus') }}:</span>
                <el-tag :type="getApprovalStatusTagType(orderDetail.approvalStatus)">
                  {{ getApprovalStatusText(orderDetail.approvalStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.paymentStatus') }}:</span>
                <el-tag :type="getPaymentStatusTagType(orderDetail.paymentStatus)">
                  {{ getPaymentStatusText(orderDetail.paymentStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.insuranceStatus') }}:</span>
                <el-tag :type="getInsuranceStatusTagType(orderDetail.insuranceStatus)">
                  {{ getInsuranceStatusText(orderDetail.insuranceStatus) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ $t('salesOrder.jpjRegistrationStatus') }}:</span>
                <el-tag :type="getJPJStatusTagType(orderDetail.jpjRegistrationStatus)">
                  {{ getJPJStatusText(orderDetail.jpjRegistrationStatus) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 订单变更记录 -->
        <el-card v-if="orderDetail.changeRecords && orderDetail.changeRecords.length > 0" class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ $t('salesOrder.changeRecords') }}</span>
          </template>

          <el-table :data="orderDetail.changeRecords" border style="width: 100%">
            <el-table-column type="index" :label="$t('common.index')" width="80" align="center" />
            <el-table-column prop="originalContent" :label="$t('salesOrder.originalContent')" align="left" />
            <el-table-column prop="changedContent" :label="$t('salesOrder.changedContent')" align="left" />
            <el-table-column prop="operator" :label="$t('salesOrder.operator')" width="120" align="left" />
            <el-table-column prop="operationTime" :label="$t('salesOrder.operationTime')" width="150" align="center" />
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('common.close') }}</el-button>
        <el-button v-if="canEdit" type="warning" @click="handleEdit">
          {{ $t('common.edit') }}
        </el-button>
        <el-button v-if="canPrint" type="success" @click="handlePrint">
          {{ $t('common.print') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { SalesOrderDetail } from '@/types/module.d'
import { getSalesOrderDetail } from '@/api/modules/order'

const { t } = useI18n()
const router = useRouter()

// Props
interface Props {
  visible: boolean
  orderId: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 响应式数据
const loading = ref(false)
const orderDetail = ref<SalesOrderDetail | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value)
  }
})

const canEdit = computed(() => {
  return orderDetail.value && ['submitted', 'confirmed'].includes(orderDetail.value.orderStatus)
})

const canPrint = computed(() => {
  return orderDetail.value !== null
})

// 监听orderId变化
watch(() => props.orderId, (newOrderId) => {
  if (newOrderId && props.visible) {
    fetchOrderDetail()
  }
}, { immediate: true })

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.orderId) {
    fetchOrderDetail()
  }
})

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!props.orderId) return

  try {
    loading.value = true
    orderDetail.value = await getSalesOrderDetail(props.orderId)
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error(t('salesOrder.fetchDetailError'))
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  orderDetail.value = null
  emit('close')
}

// 编辑订单
const handleEdit = () => {
  if (orderDetail.value) {
    router.push({
      name: 'SalesOrderEdit',
      params: { orderNo: orderDetail.value.orderNumber }
    })
    handleClose()
  }
}

// 打印订单
const handlePrint = () => {
  window.print()
}

// 状态文本和颜色方法（与列表页面相同）
const getBuyerTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    individual: t('salesOrder.buyerTypeIndividual'),
    company: t('salesOrder.buyerTypeCompany')
  }
  return textMap[type] || type
}

const getPaymentMethodText = (method: string) => {
  const textMap: Record<string, string> = {
    full_payment: t('salesOrder.paymentMethodFull'),
    installment: t('salesOrder.paymentMethodInstallment')
  }
  return textMap[method] || method
}

const getLoanApprovalStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending_review: t('salesOrder.loanApprovalStatusPending'),
    approved: t('salesOrder.loanApprovalStatusApproved'),
    rejected: t('salesOrder.loanApprovalStatusRejected')
  }
  return textMap[status] || status
}

const getLoanApprovalStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending_review: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    submitted: t('salesOrder.statusSubmitted'),
    confirmed: t('salesOrder.statusConfirmed'),
    cancel_pending: t('salesOrder.statusCancelPending'),
    cancel_approved: t('salesOrder.statusCancelApproved'),
    cancelled: t('salesOrder.statusCancelled'),
    pending_delivery: t('salesOrder.statusPendingDelivery'),
    delivered: t('salesOrder.statusDelivered')
  }
  return textMap[status] || status
}

const getOrderStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    submitted: 'warning',
    confirmed: 'success',
    cancel_pending: 'warning',
    cancel_approved: 'info',
    cancelled: 'info',
    pending_delivery: 'primary',
    delivered: 'success'
  }
  return typeMap[status] || 'info'
}

const getApprovalStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending_approval: t('salesOrder.approvalStatusPending'),
    approved: t('salesOrder.approvalStatusApproved')
  }
  return textMap[status] || status
}

const getApprovalStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending_approval: 'warning',
    approved: 'success'
  }
  return typeMap[status] || 'info'
}

const getPaymentStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending_deposit: t('salesOrder.paymentStatusPendingDeposit'),
    deposit_paid: t('salesOrder.paymentStatusDepositPaid'),
    pending_balance: t('salesOrder.paymentStatusPendingBalance'),
    balance_paid: t('salesOrder.paymentStatusBalancePaid'),
    refunding: t('salesOrder.paymentStatusRefunding'),
    refunded: t('salesOrder.paymentStatusRefunded')
  }
  return textMap[status] || status
}

const getPaymentStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending_deposit: 'danger',
    deposit_paid: 'warning',
    pending_balance: 'warning',
    balance_paid: 'success',
    refunding: 'warning',
    refunded: 'info'
  }
  return typeMap[status] || 'info'
}

const getInsuranceStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    not_insured: t('salesOrder.insuranceStatusNotInsured'),
    insuring: t('salesOrder.insuranceStatusInsuring'),
    insured: t('salesOrder.insuranceStatusInsured')
  }
  return textMap[status] || status
}

const getInsuranceStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    not_insured: 'danger',
    insuring: 'warning',
    insured: 'success'
  }
  return typeMap[status] || 'info'
}

const getJPJStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending_registration: t('salesOrder.jpjStatusPending'),
    registering: t('salesOrder.jpjStatusRegistering'),
    registered: t('salesOrder.jpjStatusRegistered'),
    registration_failed: t('salesOrder.jpjStatusFailed')
  }
  return textMap[status] || status
}

const getJPJStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending_registration: 'warning',
    registering: 'warning',
    registered: 'success',
    registration_failed: 'danger'
  }
  return typeMap[status] || 'info'
}
</script>

<style scoped>
.order-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 20px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.status-tags {
  display: flex;
  gap: 8px;
}

.status-and-amount {
  display: flex;
  align-items: center;
  gap: 12px;
}

.total-amount {
  font-size: 14px;
  font-weight: bold;
  color: #409EFF;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  min-height: 32px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  word-break: break-all;
}

.value.amount {
  font-weight: bold;
  color: #E6A23C;
}

.notes-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #EBEEF5;
}

.notes-section .value {
  margin-top: 4px;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table .cell) {
  word-break: break-word;
}

@media print {
  .dialog-footer {
    display: none;
  }

  .order-detail-container {
    max-height: none;
    overflow: visible;
  }

  .info-card {
    page-break-inside: avoid;
    margin-bottom: 20px;
  }
}
</style>
