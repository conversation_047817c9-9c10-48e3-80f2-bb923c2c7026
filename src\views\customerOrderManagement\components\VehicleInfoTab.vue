<template>
  <div class="vehicle-info-tab">
    <!-- 基本车辆信息 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="Model">
          <el-input v-model="props.formData.model" readonly />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="Variant">
          <el-input v-model="props.formData.variant" readonly />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="Color">
          <el-select 
            v-model="props.formData.color" 
            placeholder="请选择颜色"
            @change="handleColorChange"
          >
            <el-option label="白色" value="white" />
            <el-option label="黑色" value="black" />
            <el-option label="红色" value="red" />
            <el-option label="蓝色" value="blue" />
            <el-option label="银色" value="silver" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="销售小计">
          <el-input 
            v-model="props.formData.salesSubtotal" 
            readonly
            :formatter="(value: number | string) => `RM ${Number(value).toFixed(2)}`"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="车牌费">
          <el-input 
            v-model="props.formData.numberPlatesFee" 
            readonly
            :formatter="(value: number | string) => `RM ${Number(value).toFixed(2)}`"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="VIN码">
          <el-input v-model="props.formData.vin" readonly />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 选配件信息 -->
    <div class="accessories-section">
      <h3 class="section-subtitle">选配件信息</h3>
      <el-table :data="props.formData.accessories" style="width: 100%">
        <el-table-column prop="category" label="类别" width="180" />
        <el-table-column prop="name" label="配件名称" width="180" />
        <el-table-column prop="unitPrice" label="配件单价">
          <template #default="{ row }">
            RM {{ row.unitPrice.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="120" />
        <el-table-column prop="totalPrice" label="总价">
          <template #default="{ row }">
            RM {{ row.totalPrice.toFixed(2) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 选配件总金额 -->
      <div class="accessories-total">
        <span>选配件总金额：</span>
        <span class="amount">RM {{ props.formData.accessoriesTotalAmount.toFixed(2) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

interface Accessory {
  category: string;
  name: string;
  unitPrice: number;
  quantity: number;
  totalPrice: number;
}

interface FormData {
  model: string;
  variant: string;
  color: string;
  salesSubtotal: number;
  numberPlatesFee: number;
  vin: string;
  accessories: Accessory[];
  accessoriesTotalAmount: number;
}

const props = defineProps<{
  formData: FormData;
}>();

const emit = defineEmits<{
  'update:color': [color: string];
}>();

const handleColorChange = (newColor: string) => {
  emit('update:color', newColor);
};
</script>

<style scoped>
.vehicle-info-tab {
  padding: 20px 0;
}

.section-subtitle {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0;
  color: #303133;
}

.accessories-section {
  margin-top: 30px;
}

.accessories-total {
  margin-top: 20px;
  text-align: right;
  padding: 10px;
  background-color: #f5f7fa;

  .amount {
    font-weight: bold;
    color: #409EFF;
    margin-left: 10px;
  }
}
</style> 