<template>
  <el-dialog
    :model-value="show"
    title="试驾单详情"
    width="800px"
    :modal="false"
    @update:model-value="emit('update:show', $event)"
  >
    <el-form label-position="left" :label-width="120">
        <h3>潜客信息</h3>
        <el-row :gutter="16">
            <el-col :span="12">
                <el-form-item label="潜客来源">
                    {{ safeRecordData.sourceChannel || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="潜客名称">
                    {{ maskName(safeRecordData.customerName || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="潜客手机号">
                    {{ maskPhone(safeRecordData.customerPhone || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="身份证件类别">
                    {{ safeRecordData.idType || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="潜客证件号">
                    {{ maskIdNumber(safeRecordData.idNumber || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="潜客邮箱">
                    {{ maskEmail(safeRecordData.email || '') }}
                </el-form-item>
            </el-col>
        </el-row>

        <h3>试驾信息</h3>
        <el-row :gutter="16">
             <el-col :span="12">
                <el-form-item label="试驾单号">
                    {{ safeRecordData.testDriveNo }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="试驾人">
                    {{ maskName(safeRecordData.driverName || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="试驾人手机号">
                    {{ maskPhone(safeRecordData.driverPhone || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="试驾人证件类型">
                    {{ safeRecordData.driverIdType || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="试驾人证件号">
                    {{ maskIdNumber(safeRecordData.driverIdNumber || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="试驾车型">
                    {{ `${safeRecordData.model || '-'} ${safeRecordData.variant ? '- ' + safeRecordData.variant : ''}` }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="开始里程数">
                    {{ safeRecordData.startMileage || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="结束里程数">
                    {{ safeRecordData.endMileage || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="开始时间">
                    {{ formatDate(safeRecordData.startTime) }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="结束时间">
                    {{ formatDate(safeRecordData.endTime) }}
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="试驾反馈">
                    <div style="white-space: pre-wrap;">{{ safeRecordData.feedback || '-' }}</div>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="emit('update:show', false)">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TestDriveRecord } from '../types'
import { maskPhone, maskName, maskIdNumber } from '../../../../utils/data-mask'

// 添加 maskEmail 函数，因为原来的 data-mask.ts 中可能没有
const maskEmail = (email: string): string => {
  if (!email || email.length < 5) return email;
  const parts = email.split('@');
  if (parts.length !== 2) return email;

  const name = parts[0];
  const domain = parts[1];

  // 保留名称的第一个字符，其余用 * 替换
  const maskedName = name.charAt(0) + '*'.repeat(Math.max(name.length - 1, 2));

  return `${maskedName}@${domain}`;
};

const props = defineProps<{
  show: boolean
  recordData: TestDriveRecord | null
}>()

const emit = defineEmits(['update:show'])

const safeRecordData = computed<TestDriveRecord>(() => {
  return props.recordData || ({} as TestDriveRecord)
})

const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  try {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/,/g, '')
  } catch {
    return dateString
  }
}
</script>

<style scoped>
h3 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  padding-left: 8px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
