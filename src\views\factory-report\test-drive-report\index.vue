<template>
  <div class="test-drive-report">
    <h1 class="page-title">厂端试驾报表</h1>

    <!-- 统计概览区 -->
    <div class="stats-section mb-20">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">本月试驾次数</div>
            <div class="stat-value">{{ stats.monthlyTestDrives }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">今日试驾次数</div>
            <div class="stat-value">{{ stats.todayTestDrives }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">试驾次数最多的门店</div>
            <div class="stat-value-long">{{ stats.topStore }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">试驾量最多的车型</div>
            <div class="stat-value-long">{{ stats.topModel }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-section mb-20">
      <template #header>
        <span>查询条件</span>
      </template>
      <el-form ref="filterFormRef" :model="filterForm" label-position="top">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="门店">
              <el-select
                v-model="filterForm.storeIds"
                placeholder="全部门店"
                clearable
                multiple
              >
                <el-option
                  v-for="option in storeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="试驾车型 (Model)">
              <el-select
                v-model="filterForm.model"
                placeholder="全部车型"
                clearable
                @change="handleModelChange"
              >
                <el-option
                  v-for="option in modelOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="试驾配置 (Variant)">
              <el-select
                v-model="filterForm.variant"
                placeholder="全部配置"
                clearable
                :disabled="!filterForm.model"
              >
                <el-option
                  v-for="option in variantOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="试驾时间">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                clearable
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <div style="display: flex; justify-content: flex-end; width: 100%;">
                <el-space>
                  <el-button @click="handleReset" :icon="Refresh">
                    重置
                  </el-button>
                  <el-button type="primary" @click="handleSearch" :icon="Search">
                    查询
                  </el-button>
                </el-space>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="action-section mb-20">
      <el-space>
        <el-button @click="handleExport" type="primary" plain :icon="Download">
          导出数据
        </el-button>
      </el-space>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-container">
      <template #header>
        <span>试驾记录列表</span>
      </template>
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        border
        stripe
        height="600"
        style="width: 100%"
        :scroll-x="1800"
      >
        <el-table-column type="index" label="序号" width="60" fixed="left" />
        <el-table-column prop="testDriveNo" label="试驾单号" width="150" />
        <el-table-column prop="storeName" label="门店" width="120" />
        <el-table-column prop="createTime" label="试驾单录入时间" width="140" />
        <el-table-column prop="customerName" label="试驾人" width="120" />
        <el-table-column prop="customerPhone" label="试驾人手机号" width="140" />
        <el-table-column prop="model" label="试驾车型 (Model)" width="180" />
        <el-table-column prop="variant" label="试驾配置 (Variant)" width="180" />
        <el-table-column prop="mileage" label="里程数" width="100" />
        <el-table-column prop="startTime" label="试驾开始时间" width="120" />
        <el-table-column prop="endTime" label="试驾结束时间" width="120" />
        <el-table-column prop="consultantName" label="销售顾问" width="180" />
        <el-table-column label="操作" width="80" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section mt-20">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :total="pagination.itemCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 试驾单详情模态框 -->
    <TestDriveDetailModal
      v-model:show="showDetailModal"
      :record-data="currentRecord"
    />
  </div>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'TestDriveReportView'
})

import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import TestDriveDetailModal from './components/TestDriveDetailModal.vue'
// 导入 API 函数
import { http } from '@/utils/http'
import { format } from 'date-fns'

// 定义接口
interface QueryParams {
  pageNum?: number;
  pageSize?: number;
  model?: string | null;
  variant?: number | null;
  storeIds?: number[];
  startTimeBegin?: string;
  startTimeEnd?: string;
}

// 定义 API 函数
const getTestDriveStats = () => {
  return http.get('/sales/test-drive-report/statistics')
}

const getTestDriveReportList = (params: QueryParams) => {
  return http.post('/sales/test-drive-report/list', params)
}

const getTestDriveDetail = (testDriveNo: string) => {
  return http.get(`/sales/test-drive/detail/${testDriveNo}`)
}

const exportTestDriveReport = (params: QueryParams) => {
  return http.post('/sales/test-drive-report/export', params, { responseType: 'blob' })
}

// Types
interface TestDriveRecord {
  // Fields from the list API
  testDriveNo: string;
  customerName: string;
  customerPhone: string;
  model: string;
  variant: string;
  startTime: string;
  endTime: string;
  mileage: number;
  consultantName: string;
  storeName: string;
  createTime: string;
  editable?: boolean;

  // Extra fields from the detail API, all optional
  id?: string;
  customerId?: number;
  customerIdType?: string;
  customerIdNumber?: string;
  driverName?: string;
  driverPhone?: string;
  driverIdType?: string;
  driverIdNumber?: string;
  source?: string;
  email?: string;
  driverLicenseNumber?: string;
  startMileage?: number;
  endMileage?: number;
  feedback?: string;
  consultantId?: number;
  storeId?: number;
  storeRegion?: string;
  updateTime?: string;
}

// 使用 Element Plus 的消息组件
const message = {
  success: (msg: string) => ElMessage.success(msg),
  error: (msg: string) => ElMessage.error(msg)
}
const tableRef = ref()
const filterFormRef = ref()

const stats = reactive({
  monthlyTestDrives: 0,
  todayTestDrives: 0,
  topStore: '-',
  topModel: '-'
})

const filterForm = reactive({
  storeIds: [],
  model: null,
  variant: null,
  dateRange: null as [number, number] | null,
})

const loading = ref(false)
const tableData = ref<TestDriveRecord[]>([])
const showDetailModal = ref(false)
const currentRecord = ref<TestDriveRecord | null>(null)

// Dynamic Options
const storeOptions = ref<{label: string, value: number}[]>([
  { label: '北京朝阳门店', value: 1001 },
  { label: '北京海淀门店', value: 1002 },
  { label: '上海徐汇门店', value: 2001 },
  { label: '上海浦东门店', value: 2002 },
  { label: '天津门店', value: 1003 },
]);

const modelOptions = ref<{label: string, value: string}[]>([
  { label: 'ModelX', value: 'ModelX' },
  { label: 'ModelY', value: 'ModelY' },
]);

// Variant options are still computed locally based on model selection
const variantOptions = computed(() => {
  if (filterForm.model === 'ModelX') {
    return [
      { label: 'Premium', value: 1 },
      { label: 'Standard', value: 2 },
    ]
  }
  if (filterForm.model === 'ModelY') {
    return [
      { label: 'Premium', value: 1 },
      { label: 'Standard', value: 2 },
    ]
  }
  return []
})

const handleModelChange = () => {
  filterForm.variant = null
}

const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadTableData()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadTableData()
}

// 不再需要这些辅助函数，因为我们在表格中直接使用数据

// Element Plus 表格不需要定义列结构，已在模板中定义

const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

const handleReset = () => {
  filterForm.storeIds = []
  filterForm.model = null
  filterForm.variant = null
  filterForm.dateRange = null
  handleSearch()
}

const handleExport = async () => {
  try {
    const params = buildQueryParams();
    const exportParams = { ...params };
    // 移除分页参数
    if ('pageNum' in exportParams) exportParams.pageNum = undefined;
    if ('pageSize' in exportParams) exportParams.pageSize = undefined;

    const response = await exportTestDriveReport(exportParams);
    // 处理 axios 的 blob 响应
    const blobData = response.data;
    const blob = new Blob([blobData], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `厂端试驾报表_${format(new Date(), 'yyyyMMddHHmmss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    message.success('导出成功');
  } catch (error) {
    message.error('导出失败');
    console.error(error);
  }
}

const handleViewDetail = async (record: TestDriveRecord) => {
  try {
    const res = await getTestDriveDetail(record.testDriveNo)
    // 根据实际API返回结构处理数据
    currentRecord.value = res.result as any
    showDetailModal.value = true
  } catch(error) {
    message.error('获取详情失败');
    console.error(error);
  }
}

const buildQueryParams = () => {
  const params: QueryParams = {
    pageNum: pagination.page,
    pageSize: pagination.pageSize,
    model: filterForm.model || undefined,
    variant: filterForm.variant || undefined,
    storeIds: filterForm.storeIds.length > 0 ? filterForm.storeIds : undefined,
  };
  if (filterForm.dateRange) {
    params.startTimeBegin = format(filterForm.dateRange[0], "yyyy-MM-dd'T'HH:mm:ss");
    params.startTimeEnd = format(filterForm.dateRange[1], "yyyy-MM-dd'T'HH:mm:ss");
  }
  return params;
}

const loadTableData = async () => {
  loading.value = true;
  try {
    const params = buildQueryParams();
    const res = await getTestDriveReportList(params);
    console.log('loadTableData', res);
    // 根据实际API返回结构处理数据
    const result = res.result as any;
    if (result && result.records) {
      tableData.value = result.records;
      pagination.itemCount = result.total;
    } else {
      tableData.value = [];
      pagination.itemCount = 0;
    }
  } catch (error) {
    message.error('加载数据失败');
    console.error(error);
    tableData.value = [];
    pagination.itemCount = 0;
  } finally {
    loading.value = false;
  }
}

const loadStats = async () => {
  try {
    const res = await getTestDriveStats();
    // 根据实际API返回结构处理数据
    const statsData = res.result as any;
    if (statsData) {
      stats.monthlyTestDrives = statsData.monthlyCount || 0;
      stats.todayTestDrives = statsData.dailyCount || 0;
      stats.topStore = statsData.topStore || '-';
      stats.topModel = statsData.topModel || '-';
    }
  } catch (error) {
    console.error('Failed to load stats:', error);
  }
}

const loadOptions = () => {
  // 直接使用静态数据，不请求后端
  console.log('使用静态数据，不请求后端');
}

onMounted(() => {
  loadStats()
  loadTableData()
  loadOptions()
})
</script>

<style scoped lang="scss">
.test-drive-report {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333333;
  }

  .stats-section {
    margin-bottom: 20px;
    .stat-card {
      background-color: white;
      border-radius: 4px;
      padding: 20px;
      box-shadow: 0 1px 4px rgba(0,21,41,.08);
      border-left: 4px solid #1890ff;
      height: 100%;

      .stat-title {
        font-size: 14px;
        color: rgba(0,0,0,.45);
        margin-bottom: 8px;
      }
      .stat-value {
        font-size: 30px;
        font-weight: bold;
        color: #333;
      }
      .stat-value-long {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .filter-section {
    background-color: white;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 16px;

    .button-gi {
      display: flex;
      align-items: flex-end;
      padding-bottom: 2px; /* Small adjustment for alignment */
    }
  }

  .action-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
  }

  .table-container {
    background-color: white;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    border-radius: 6px;
    overflow: hidden;

    :deep(.n-data-table-thead th) {
      background-color: #fafafa;
      font-weight: bold;
    }

    :deep(.n-data-table-tbody tr:hover) {
      background-color: #e6f7ff;
    }
  }
}
</style>
