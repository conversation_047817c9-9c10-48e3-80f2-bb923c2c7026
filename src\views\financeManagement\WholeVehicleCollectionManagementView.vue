<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 筛选条件区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('enterOrderNumber')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('buyerName')">
              <el-input
                v-model="searchParams.buyerName"
                :placeholder="t('enterBuyerName')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('buyerPhone')">
              <el-input
                v-model="searchParams.buyerPhone"
                :placeholder="t('enterBuyerPhone')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderStatus')">
              <el-select
                v-model="searchParams.orderStatus"
                :placeholder="t('selectOrderStatus')"
                clearable
              >
                <el-option :label="t('orderStatusSubmitted')" value="已提交" />
                <el-option :label="t('orderStatusCancelPending')" value="取消审核中" />
                <el-option :label="t('orderStatusCancelApproved')" value="取消审核通过" />
                <el-option :label="t('orderStatusCancelled')" value="已取消" />
                <el-option :label="t('orderStatusConfirmed')" value="已确认" />
                <el-option :label="t('orderStatusPendingReview')" value="待审核" />
                <el-option :label="t('orderStatusReviewed')" value="已审核" />
                <el-option :label="t('orderStatusPendingDelivery')" value="待交车" />
                <el-option :label="t('orderStatusDelivered')" value="已交车" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('paymentStatus')">
              <el-select
                v-model="searchParams.paymentStatus"
                :placeholder="t('selectPaymentStatus')"
                clearable
              >
                <el-option :label="t('paymentStatusPendingDeposit')" value="待支付定金" />
                <el-option :label="t('paymentStatusDepositPaid')" value="已支付定金" />
                <el-option :label="t('paymentStatusRefunding')" value="退款中" />
                <el-option :label="t('paymentStatusRefunded')" value="退款完成" />
                <el-option :label="t('paymentStatusPendingFinal')" value="待支付尾款" />
                <el-option :label="t('paymentStatusFullyPaid')" value="已支付尾款" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderCreateTime')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="t('selectDateRange')"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('canInvoice')">
              <el-select
                v-model="searchParams.canInvoice"
                :placeholder="t('selectCanInvoice')"
                clearable
              >
                <el-option :label="tc('yes')" :value="true" />
                <el-option :label="tc('no')" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#fafafa', fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="tc('index')" width="80" fixed="left" />
        <el-table-column prop="orderNumber" :label="t('orderNumber')" width="120" sortable />
        <el-table-column prop="buyerName" :label="t('buyerName')" width="100" />
        <el-table-column prop="buyerPhone" :label="t('buyerPhone')" width="120">
          <template #default="scope">
            {{ formatPhone(scope.row.buyerPhone) }}
          </template>
        </el-table-column>
        <el-table-column prop="dealerStoreName" :label="t('dealerStore')" width="100" />
        <el-table-column prop="salesConsultantName" :label="t('salesConsultant')" width="100" />
        <el-table-column prop="vin" :label="t('vin')" width="120" />
        <el-table-column prop="model" :label="t('model')" width="100" />
        <el-table-column prop="variant" :label="t('variant')" width="100" />
        <el-table-column prop="color" :label="t('color')" width="80" />
        <el-table-column prop="createTime" :label="t('orderCreateTime')" width="150" sortable />
        <el-table-column prop="orderStatus" :label="t('orderStatus')" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
              {{ getOrderStatusText(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="t('paymentStatus')" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="vehicleSalesPrice" :label="t('vehicleSalesPrice')" width="100" align="right">
          <template #default="scope">
            {{ formatAmount(scope.row.vehicleSalesPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="insuranceAmount" :label="t('insuranceAmount')" width="100" align="right">
          <template #default="scope">
            {{ formatAmount(scope.row.insuranceAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="otrAmount" :label="t('otrAmount')" width="100" align="right">
          <template #default="scope">
            {{ formatAmount(scope.row.otrAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="discountAmount" :label="t('discountAmount')" width="100" align="right">
          <template #default="scope">
            <span style="color: #f56c6c">{{ formatAmount(scope.row.discountAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" :label="t('totalAmount')" width="100" align="right">
          <template #default="scope">
            <strong>{{ formatAmount(scope.row.totalAmount) }}</strong>
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" :label="t('paidAmount')" width="100" align="right">
          <template #default="scope">
            <span style="color: #67c23a; font-weight: bold">{{ formatAmount(scope.row.paidAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unpaidAmount" :label="t('unpaidAmount')" width="100" align="right">
          <template #default="scope">
            <span style="color: #f56c6c; font-weight: bold">{{ formatAmount(scope.row.unpaidAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="loanAmount" :label="t('loanAmount')" width="120" align="right">
          <template #default="scope">
            {{ scope.row.loanAmount ? formatAmount(scope.row.loanAmount) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="canInvoice" :label="t('canInvoice')" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.canInvoice ? 'success' : 'info'">
              {{ scope.row.canInvoice ? tc('yes') : tc('no') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceTime" :label="t('invoiceTime')" width="150">
          <template #default="scope">
            {{ scope.row.invoiceTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="invoiceNumber" :label="t('invoiceNumber')" width="120">
          <template #default="scope">
            {{ scope.row.invoiceNumber || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="t('createTime')" width="150" sortable />
        <el-table-column prop="updateTime" :label="t('updateTime')" width="150" sortable />
        <el-table-column :label="tc('operations')" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" :icon="View" link @click="handleViewDetail(scope.row)">
              {{ t('orderDetail') }}
            </el-button>
            <el-button
              type="primary"
              :icon="CreditCard"
              link
              @click="handlePaymentOperation(scope.row)"
              :disabled="!canOperatePayment(scope.row.paymentStatus)"
            >
              {{ t('paymentOperation') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 收退款弹窗 -->
    <PaymentOperationDialog
      v-model="paymentDialogVisible"
      :order-id="currentOrderId"
      @refresh="loadData"
    />

    <!-- 详情弹窗 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order-id="currentOrderId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, View, CreditCard } from '@element-plus/icons-vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { getOrderPaymentList } from '@/api/modules/payment'
import PaymentOperationDialog from './components/PaymentOperationDialog.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import type { OrderPaymentItem, OrderPaymentListParams } from '@/types/module'

const { t,tc } = useModuleI18n('sales.payment')

// 响应式数据
const loading = ref(false)
const tableData = ref<OrderPaymentItem[]>([])
const paymentDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentOrderId = ref('')
const dateRange = ref<[string, string] | null>(null)

// 搜索参数
const searchParams = reactive<Omit<OrderPaymentListParams, 'page' | 'pageSize'>>({
  orderNumber: '',
  buyerName: '',
  buyerPhone: '',
  orderStatus: '',
  paymentStatus: '',
  startDate: '',
  endDate: '',
  canInvoice: undefined
})

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal) {
    searchParams.startDate = newVal[0]
    searchParams.endDate = newVal[1]
  } else {
    searchParams.startDate = ''
    searchParams.endDate = ''
  }
})

// 初始化默认时间范围（最近一个月）
const initDefaultDateRange = () => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setMonth(startDate.getMonth() - 1)

  dateRange.value = [
    `${startDate.toISOString().split('T')[0]} 00:00:00`,
    `${endDate.toISOString().split('T')[0]} 23:59:59`
  ]
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 创建基础参数
    const params: Partial<OrderPaymentListParams> = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    // 只添加非空参数
    if (searchParams.orderNumber) params.orderNumber = searchParams.orderNumber
    if (searchParams.buyerName) params.buyerName = searchParams.buyerName
    if (searchParams.buyerPhone) params.buyerPhone = searchParams.buyerPhone
    if (searchParams.orderStatus) params.orderStatus = searchParams.orderStatus
    if (searchParams.paymentStatus) params.paymentStatus = searchParams.paymentStatus
    if (searchParams.startDate) params.startDate = searchParams.startDate
    if (searchParams.endDate) params.endDate = searchParams.endDate
    if (searchParams.canInvoice !== undefined) params.canInvoice = searchParams.canInvoice

    const response = await getOrderPaymentList(params as OrderPaymentListParams)
    console.log(response);
    tableData.value = response.result.records
    pagination.total = response.result.total
  } catch (error) {
    console.error('Failed to load order payment list:', error)
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  searchParams.orderNumber = ''
  searchParams.buyerName = ''
  searchParams.buyerPhone = ''
  searchParams.orderStatus = ''
  searchParams.paymentStatus = ''
  searchParams.startDate = ''
  searchParams.endDate = ''
  searchParams.canInvoice = undefined
  dateRange.value = null
  initDefaultDateRange()
  pagination.page = 1
  loadData()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  loadData()
}

// 查看详情
const handleViewDetail = (row: OrderPaymentItem) => {
  currentOrderId.value = row.orderId
  detailDialogVisible.value = true
}

// 收退款操作
const handlePaymentOperation = (row: OrderPaymentItem) => {
  currentOrderId.value = row.orderId
  paymentDialogVisible.value = true
}

// 判断是否可以收退款操作
const canOperatePayment = (paymentStatus: string) => {
  return ['DEPOSIT_PAID', 'PENDING_FINAL_PAYMENT', 'FINAL_PAYMENT_PAID'].includes(paymentStatus)
}

// 格式化手机号（脱敏）
const formatPhone = (phone: string) => {
  if (!phone || phone.length < 4) return phone
  return phone.replace(/(\d{4})(\d*)(\d{4})/, '$1****$3')
}

// 格式化金额
const formatAmount = (amount: number) => {
  return amount;

}

// 获取订单状态样式
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已确认': 'success',
    '待审核': 'warning',
    '已取消': 'danger',
    '已交车': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': t('orderStatusSubmitted'),
    '取消审核中': t('orderStatusCancelPending'),
    '取消审核通过': t('orderStatusCancelApproved'),
    '已取消': t('orderStatusCancelled'),
    '已确认': t('orderStatusConfirmed'),
    '待审核': t('orderStatusPendingReview'),
    '已审核': t('orderStatusReviewed'),
    '待交车': t('orderStatusPendingDelivery'),
    '已交车': t('orderStatusDelivered')
  }
  return statusMap[status] || status
}

// 获取支付状态样式
const getPaymentStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已支付尾款': 'success',
    '已支付定金': 'primary',
    '待支付定金': 'warning',
    '退款中': 'danger',
    '退款完成': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '待支付定金': t('paymentStatusPendingDeposit'),
    '已支付定金': t('paymentStatusDepositPaid'),
    '退款中': t('paymentStatusRefunding'),
    '退款完成': t('paymentStatusRefunded'),
    '待支付尾款': t('paymentStatusPendingFinal'),
    '已支付尾款': t('paymentStatusFullyPaid')
  }
  return statusMap[status] || status
}

// 组件挂载
onMounted(() => {
  initDefaultDateRange()
  loadData()
})
</script>

<style scoped>
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
