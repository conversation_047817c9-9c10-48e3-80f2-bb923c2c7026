<template>
  <el-dialog
    v-model="visible"
    :title="`${t('orderDetail')} [${t('orderNumber')}: ${orderDetail?.orderNumber || '-'}]`"
    width="85vw"
    min-width="900px"
    max-width="1300px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading" class="dialog-content">
      <div v-if="orderDetail">
        <!-- 订单基本信息 -->
        <el-card class="mb-20 basic-info-card">
          <template #header>
            <span class="card-title">{{ t('orderBasicInfo') }}</span>
          </template>
          <div class="order-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="copyToClipboard(orderDetail.orderNumber)">
                  <label>{{ t('orderNumber') }}:</label>
                  <span class="bold">{{ orderDetail.orderNumber }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderCreateTime') }}:</label>
                  <span>{{ orderDetail.orderCreateTime }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderStatus') }}:</label>
                  <el-tag :type="getOrderStatusType(orderDetail.orderStatus)">
                    {{ getOrderStatusText(orderDetail.orderStatus) }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('paymentStatus') }}:</label>
                  <el-tag :type="getPaymentStatusType(orderDetail.paymentStatus)">
                    {{ getPaymentStatusText(orderDetail.paymentStatus) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('paymentMethod') }}:</label>
                  <span>{{ orderDetail.paymentMethod }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('loanAmount') }}:</label>
                  <span :class="{'red-text': orderDetail.loanAmount}">{{ orderDetail.loanAmount ? formatAmount(orderDetail.loanAmount) : '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('customerDetailInfo') }} - Personal Details</span>
          </template>
          <div class="customer-info">
            <h3 class="sub-title">{{ t('ordererInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('ordererName') }}:</label>
                  <span>{{ orderDetail.ordererName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.ordererPhone && callPhone(orderDetail.ordererPhone)">
                  <label>{{ t('ordererPhone') }}:</label>
                  <span>{{ orderDetail.ordererPhone ? orderDetail.ordererPhone : '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <h3 class="sub-title">{{ t('buyerInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerName') }}:</label>
                  <span>{{ orderDetail.buyerName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.buyerPhone && callPhone(orderDetail.buyerPhone)">
                  <label>{{ t('buyerPhone') }}:</label>
                  <span>{{ orderDetail.buyerPhone ? orderDetail.buyerPhone : '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerIdType') }}:</label>
                  <span>{{ orderDetail.buyerIdType || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="toggleMaskIdNumber(orderDetail.buyerIdNumber)">
                  <label>{{ t('buyerIdNumber') }}:</label>
                  <span>{{ maskedIdNumber(orderDetail.buyerIdNumber) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.buyerEmail && sendEmail(orderDetail.buyerEmail)">
                  <label>{{ t('buyerEmail') }}:</label>
                  <span>{{ orderDetail.buyerEmail || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerState') }}:</label>
                  <span>{{ orderDetail.buyerState || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerCity') }}:</label>
                  <span>{{ orderDetail.buyerCity || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerPostcode') }}:</label>
                  <span>{{ orderDetail.buyerPostcode || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerAddress') }}:</label>
                  <span>{{ orderDetail.buyerAddress || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 门店与销售信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('dealerAndSalesInfo') }} - Preferred Outlet & Sales Advisor</span>
          </template>
          <div class="dealer-sales-info">
            <h3 class="sub-title">{{ t('dealerInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('dealerRegion') }}:</label>
                  <span>{{ orderDetail.dealerRegion || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('dealerCity') }}:</label>
                  <span>{{ orderDetail.dealerCity || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('dealerStore') }}:</label>
                  <span>{{ orderDetail.dealerStoreName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <h3 class="sub-title">{{ t('salesConsultantInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('salesConsultant') }}:</label>
                  <span>{{ orderDetail.salesConsultantName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('vehicleDetailInfo') }}</span>
          </template>
          <div class="vehicle-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('model') }}:</label>
                  <span>{{ orderDetail.model || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('variant') }}:</label>
                  <span>{{ orderDetail.variant || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('color') }}:</label>
                  <span>
                    <span class="color-dot" :style="{ backgroundColor: orderDetail.color }"></span>
                    {{ orderDetail.color || '-' }}
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="copyToClipboard(orderDetail.vin)">
                  <label>{{ t('vin') }}:</label>
                  <span class="vin-text">{{ orderDetail.vin || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('warehouse') }}:</label>
                  <span>{{ orderDetail.warehouseName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('productionDate') }}:</label>
                  <span>{{ orderDetail.productionDate || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" v-if="orderDetail.options && orderDetail.options.length > 0">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('options') }}:</label>
                  <ul class="option-list">
                    <li v-for="(option, index) in orderDetail.options" :key="index">
                      {{ option.name }} (RM {{ formatAmount(option.price) }})
                    </li>
                    <li class="total-options-price">
                      <strong>{{ t('optionsTotalPrice') }}: RM {{ formatAmount(Array.isArray(orderDetail.options) ? orderDetail.options.reduce((sum, item) => sum + item.price, 0) : 0) }}</strong>
                    </li>
                  </ul>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 价格详细信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('priceDetailInfo') }}</span>
          </template>
          <div class="price-info">
            <el-table :data="priceTableData" :show-header="false" border style="width: 100%">
              <el-table-column prop="item" width="200" />
              <el-table-column prop="amount" align="right">
                <template #default="{ row }">
                  <span :class="row.class">{{ formatAmount(row.amount) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="description" />
            </el-table>
          </div>
        </el-card>

        <!-- 收退款历史记录区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('paymentRefundHistory') }}</span>
          </template>
          <div class="payment-history-info">
            <el-table :data="orderDetail.paymentRecords" style="width: 100%" border :header-cell-style="{ backgroundColor: '#fafafa', fontWeight: 'bold' }">
              <el-table-column type="index" :label="tc('index')" width="60" align="center" />
              <el-table-column prop="recordNumber" :label="t('paymentRecordNumber')" min-width="150" />
              <el-table-column prop="businessType" :label="t('businessType')" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.businessType === '收款' ? 'success' : 'danger'">
                    {{ row.businessType === '收款' ? '+' : '-' }}{{ row.businessType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="transactionNumber" :label="t('transactionNumber')" min-width="150" />
              <el-table-column prop="channel" :label="t('channel')" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getChannelType(row.channel)">
                    {{ row.channel }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="amount" :label="t('amount')" width="120" align="right">
                <template #default="{ row }">
                  <span :class="row.businessType === '收款' ? 'green-text' : 'red-text'">
                    {{ formatAmount(row.amount) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="paymentType" :label="t('paymentType')" width="120" align="center" />
              <el-table-column prop="arrivalTime" :label="t('arrivalTime')" width="150" align="center" />
              <el-table-column prop="remark" :label="tc('remark')" min-width="200" />
            </el-table>
          </div>
        </el-card>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button type="primary" @click="visible = false">{{ tc('close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { getOrderDetail } from '@/api/modules/payment'
import type { OrderDetailInfo } from '@/types/module'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const { t } = useModuleI18n('sales.payment')
const { t: tc } = useModuleI18n('common')

const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const loading = ref(false)
const orderDetail = ref<OrderDetailInfo | null>(null)
const showFullIdNumber = ref(false) // 用于控制身份证件号显示状态

watch(() => props.orderId, (newId) => {
  if (newId && visible.value) {
    loadOrderDetail(newId)
  } else {
    orderDetail.value = null
  }
}, { immediate: true })

watch(visible, (newVal) => {
  if (newVal && props.orderId) {
    loadOrderDetail(props.orderId)
  }
})

const loadOrderDetail = async (id: string) => {
  loading.value = true
  try {
    const data = await getOrderDetail(id)
    console.log(data);
    orderDetail.value = data.result;
  } catch (error) {
    console.error('Failed to load order detail:', error)
    ElMessage.error(tc('loadFailed'))
    orderDetail.value = null
  } finally {
    loading.value = false
  }
}

// 格式化金额
const formatAmount = (amount: number) => {
  if (typeof amount !== 'number') return 'RM 0.00'
  return `RM ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 格式化手机号（脱敏） - 根据文档要求，这里不再脱敏，只显示完整号码
// 拨打电话功能会直接使用完整号码
// const formatPhone = (phone: string) => {
//   if (!phone || phone.length < 4) return phone
//   return phone.replace(/(\d{4})(\d*)(\d{4})/, '$1****$3')
// }

// 脱敏身份证件号（前3后4），并提供切换显示完整号码功能
const maskedIdNumber = (idNumber: string | undefined) => {
  if (!idNumber || idNumber.length < 7) return idNumber || '-'
  if (showFullIdNumber.value) {
    return idNumber
  }
  const prefix = idNumber.substring(0, 3)
  const suffix = idNumber.substring(idNumber.length - 4)
  return `${prefix}****${suffix}`
}

const toggleMaskIdNumber = (idNumber: string | undefined) => {
  if (idNumber) {
    showFullIdNumber.value = !showFullIdNumber.value
    ElMessage.info(showFullIdNumber.value ? t('showFullIdNumber') : t('maskIdNumber'))
  }
}

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  if (text) {
    navigator.clipboard.writeText(text)
      .then(() => {
        ElMessage.success(tc('copied'))
      })
      .catch(err => {
        console.error('Failed to copy text:', err)
        ElMessage.error(tc('copyFailed'))
      })
  }
}

// 拨打电话
const callPhone = (phone: string) => {
  if (phone) {
    window.location.href = `tel:${phone}`
  }
}

// 发送邮件
const sendEmail = (email: string) => {
  if (email) {
    window.location.href = `mailto:${email}`
  }
}

// 获取订单状态样式
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已确认': 'success',
    '待审核': 'warning',
    '已取消': 'danger',
    '已交车': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': t('orderStatusSubmitted'),
    '取消审核中': t('orderStatusCancelPending'),
    '取消审核通过': t('orderStatusCancelApproved'),
    '已取消': t('orderStatusCancelled'),
    '已确认': t('orderStatusConfirmed'),
    '待审核': t('orderStatusPendingReview'),
    '已审核': t('orderStatusReviewed'),
    '待交车': t('orderStatusPendingDelivery'),
    '已交车': t('orderStatusDelivered')
  }
  return statusMap[status] || status
}

// 获取支付状态样式
const getPaymentStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已支付尾款': 'success',
    '已支付定金': 'primary',
    '待支付定金': 'warning',
    '退款中': 'danger',
    '退款完成': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '待支付定金': t('paymentStatusPendingDeposit'),
    '已支付定金': t('paymentStatusDepositPaid'),
    '退款中': t('paymentStatusRefunding'),
    '退款完成': t('paymentStatusRefunded'),
    '待支付尾款': t('paymentStatusPendingFinal'),
    '已支付尾款': t('paymentStatusFullyPaid')
  }
  return statusMap[status] || status
}

// 价格表格数据
const priceTableData = computed(() => {
  if (!orderDetail.value) return []
  const detail = orderDetail.value
  return [
    { item: t('salesSubtotalIncludeGASA'), amount: detail.salesSubtotal || 0, description: t('salesSubtotalIncludeGASADesc'), class: '' },
    { item: t('consumptionTax'), amount: detail.consumptionTax || 0, description: t('consumptionTaxDesc'), class: '' },
    { item: t('salesTax'), amount: detail.salesTax || 0, description: t('salesTaxDesc'), class: '' },
    { item: t('numberPlatesFee'), amount: detail.numberPlatesFee || 0, description: t('numberPlatesFeeDesc'), class: '' },
    { item: t('optionsPrice'), amount: detail.accessoriesTotalAmount || 0, description: t('optionsPriceDesc'), class: '' },
    { item: t('vehicleSalesPriceSubtotal'), amount: detail.vehicleSalesPrice || 0, description: t('vehicleSalesPriceSubtotalDesc'), class: 'bold' },
    { item: t('insuranceAmount'), amount: detail.insuranceAmount || 0, description: t('insuranceAmountDesc'), class: '' },
    { item: t('otrAmount'), amount: detail.otrAmount || 0, description: t('otrAmountDesc'), class: '' },
    { item: t('orderDiscountAmount'), amount: detail.discountAmount || 0, description: t('orderDiscountAmountDesc'), class: 'red-text' },
    { item: t('orderTotalAmount'), amount: detail.totalAmount || 0, description: t('orderTotalAmountDesc'), class: 'large-font bold' },
    { item: t('orderPaidAmount'), amount: detail.paidAmount || 0, description: t('orderPaidAmountDesc'), class: 'green-text bold' },
    { item: t('orderUnpaidAmount'), amount: detail.unpaidAmount || 0, description: t('orderUnpaidAmountDesc'), class: 'red-text bold' }
  ]
})

// 收退款历史 - 渠道样式
const getChannelType = (channel: string) => {
  const channelMap: Record<string, string> = {
    'APP': 'success',
    '银行卡': 'primary',
    '转账': 'warning'
  }
  return channelMap[channel] || 'info'
}

// // 计算总收款金额 (已移除，文档无此项)
// const totalReceivedAmount = computed(() => {
//   if (!orderDetail.value || !orderDetail.value.paymentRecords) return 0
//   return orderDetail.value.paymentRecords
//     .filter(record => record.businessType === '收款')
//     .reduce((sum, record) => sum + record.amount, 0)
// })

// // 计算总退款金额 (已移除，文档无此项)
// const totalRefundAmount = computed(() => {
//   if (!orderDetail.value || !orderDetail.value.paymentRecords) return 0
//   return orderDetail.value.paymentRecords
//     .filter(record => record.businessType === '退款')
//     .reduce((sum, record) => sum + record.amount, 0)
// })

// // 计算净收款金额 (已移除，文档无此项)
// const netReceivedAmount = computed(() => {
//   return totalReceivedAmount.value - totalRefundAmount.value
// })

</script>

<style scoped>
@use '@/assets/styles/_variables.scss' as *;

.dialog-content {
  padding: 0 20px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.sub-title {
  font-size: 14px;
  font-weight: bold;
  color: #409eff; /* Element Plus primary color */
  margin-top: 15px;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.order-info,
.customer-info,
.dealer-sales-info,
.vehicle-info,
.amount-info,
.invoice-info,
.payment-history-info {
  font-size: 13px;
  color: #606266;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
}

.info-item label {
  font-weight: bold;
  margin-right: 8px;
  color: #303133;
  min-width: 80px; /* 确保标签宽度一致 */
}

.info-item span {
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

.amount {
  font-weight: bold;
}

.discount {
  color: #f56c6c;
}

.total {
  font-size: 16px;
  color: #409eff;
}

.paid {
  color: #67c23a;
}

.unpaid {
  color: #f56c6c;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}

.dialog-footer-buttons .el-button {
  margin-left: 10px;
}

.red-text {
  color: #f56c6c;
}

.green-text {
  color: #67c23a;
}

.blue-text {
  color: #409eff;
}

.bold {
  font-weight: bold;
}

.large-font {
  font-size: 16px;
}

.clickable-info {
  cursor: pointer;
  color: #409eff; /* Link-like color */
}

.clickable-info:hover {
  text-decoration: underline;
}

.color-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
  vertical-align: middle;
  border: 1px solid #ccc; /* fallback border */
}

.vin-text {
  font-family: 'Courier New', Courier, monospace; /* 等宽字体 */
  background-color: #f0f0f0;
  padding: 2px 5px;
  border-radius: 3px;
}

.option-list {
  list-style-type: disc;
  padding-left: 20px;
  margin: 0;
}

.option-list li {
  margin-bottom: 5px;
}

.total-options-price {
  margin-top: 10px;
  font-size: 14px;
}

.payment-history-info :deep(.el-table .el-table__body td),
.payment-history-info :deep(.el-table .el-table__header th) {
  white-space: nowrap;
}

.basic-info-card {
  background-color: #f0f8ff; /* 浅蓝色背景 */
  padding: 20px; /* 区域内边距 */
}

/* 根据文档，手机号不需要脱敏 */
/* .customer-info .info-item span {
  word-break: break-all;
} */

</style>
