<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ t('wholeVehicleCollection.title') }}</h1>
    </div>

    <!-- 筛选条件区域 -->
    <el-card class="mb-20 search-card">
      <el-form
        ref="searchFormRef"
        :model="searchForm"
        class="search-form"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('wholeVehicleCollection.search.orderNumber')">
              <el-input
                v-model="searchForm.orderNumber"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('wholeVehicleCollection.search.buyerName')">
              <el-input
                v-model="searchForm.buyerName"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('wholeVehicleCollection.search.buyerPhone')">
              <el-input
                v-model="searchForm.buyerPhone"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('wholeVehicleCollection.search.orderStatus')">
              <el-select
                v-model="searchForm.orderStatus"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="item in orderStatusOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('wholeVehicleCollection.search.paymentStatus')">
              <el-select
                v-model="searchForm.paymentStatus"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="item in paymentStatusOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('wholeVehicleCollection.search.dateRange')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :range-separator="tc('datePicker.rangeSeparator')"
                :start-placeholder="tc('datePicker.startDate')"
                :end-placeholder="tc('datePicker.endDate')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('wholeVehicleCollection.search.canInvoice')">
              <el-select
                v-model="searchForm.canInvoice"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="item in canInvoiceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="handleReset">
                {{ tc('reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#fafafa', fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="tc('index')" width="80" fixed="left" />

        <el-table-column
          :label="t('wholeVehicleCollection.table.orderNumber')"
          prop="orderNumber"
          width="120"
          sortable
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.buyerName')"
          prop="buyerName"
          width="100"
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.buyerPhone')"
          prop="buyerPhone"
          width="120"
        >
          <template #default="{ row }">
            {{ formatPhone(row.buyerPhone) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.dealerStoreName')"
          prop="dealerStoreName"
          width="100"
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.salesConsultantName')"
          prop="salesConsultantName"
          width="100"
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.vin')"
          prop="vin"
          width="120"
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.model')"
          prop="model"
          width="100"
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.variant')"
          prop="variant"
          width="100"
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.color')"
          prop="color"
          width="80"
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.orderCreateTime')"
          prop="orderCreateTime"
          width="150"
          sortable
        />

        <el-table-column
          :label="t('wholeVehicleCollection.table.orderStatus')"
          prop="orderStatus"
          width="120"
        >
          <template #default="{ row }">
            <el-tag :type="getOrderStatusTagType(row.orderStatus)">
              {{ getOrderStatusLabel(row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.paymentStatus')"
          prop="paymentStatus"
          width="120"
        >
          <template #default="{ row }">
            <el-tag :type="getPaymentStatusTagType(row.paymentStatus)">
              {{ getPaymentStatusLabel(row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.vehicleSalesPrice')"
          prop="vehicleSalesPrice"
          width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.vehicleSalesPrice) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.insuranceAmount')"
          prop="insuranceAmount"
          width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.insuranceAmount) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.otrAmount')"
          prop="otrAmount"
          width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.otrAmount) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.discountAmount')"
          prop="discountAmount"
          width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.discountAmount) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.totalAmount')"
          prop="totalAmount"
          width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.totalAmount) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.paidAmount')"
          prop="paidAmount"
          width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.paidAmount) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.unpaidAmount')"
          prop="unpaidAmount"
          width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.unpaidAmount) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.loanAmount')"
          prop="loanAmount"
          width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.loanAmount) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('wholeVehicleCollection.table.canInvoice')"
          prop="canInvoice"
          width="100"
        >
          <template #default="{ row }">
            <el-tag :type="row.canInvoice ? 'success' : 'info'">
              {{ row.canInvoice ? t('wholeVehicleCollection.status.canInvoice.yes') : t('wholeVehicleCollection.status.canInvoice.no') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          :label="tc('actions')"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              {{ t('wholeVehicleCollection.table.viewDetail') }}
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handlePaymentOperation(row)"
            >
              {{ t('wholeVehicleCollection.table.paymentOperation') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情弹窗 -->
    <OrderDetailDialog
      v-model:visible="orderDetailVisible"
      :order-id="selectedOrderId"
    />

    <!-- 收退款操作弹窗 -->
    <PaymentOperationDialog
      v-model:visible="paymentOperationVisible"
      :order-data="selectedOrderData"
      @success="handlePaymentSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type {
  WholeVehicleCollectionSearchParams,
  WholeVehicleCollectionItem,
  WholeVehicleCollectionPageResponse
} from '@/types/finance/wholeVehicleCollection';
import {
  getWholeVehicleCollectionList
} from '@/api/modules/finance/wholeVehicleCollection';
import OrderDetailDialog from './components/OrderDetailDialog.vue';
import PaymentOperationDialog from './components/PaymentOperationDialog.vue';

// 国际化
const { t, tc } = useModuleI18n('finance');

// 表单引用
const searchFormRef = ref<FormInstance>();

// 搜索表单数据
const searchForm = reactive<WholeVehicleCollectionSearchParams>({
  orderNumber: '',
  buyerName: '',
  buyerPhone: '',
  orderStatus: '',
  paymentStatus: '',
  canInvoice: undefined,
  pageNum: 1,
  pageSize: 20
});

// 日期范围
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchForm.startDate = newVal[0];
    searchForm.endDate = newVal[1];
  } else {
    searchForm.startDate = undefined;
    searchForm.endDate = undefined;
  }
});

// 表格数据
const tableData = ref<WholeVehicleCollectionItem[]>([]);
const tableLoading = ref(false);

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
});

// 弹窗状态
const orderDetailVisible = ref(false);
const paymentOperationVisible = ref(false);
const selectedOrderId = ref('');
const selectedOrderData = ref<WholeVehicleCollectionItem | null>(null);

// 数据字典
const { getOptions, getNameByCode, loading: dictLoading } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS
]);

// 订单状态选项
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS));

// 付款状态选项
const paymentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PAYMENT_STATUS));

// 可开票选项
const canInvoiceOptions = [
  { value: true, label: t('wholeVehicleCollection.status.canInvoice.yes') },
  { value: false, label: t('wholeVehicleCollection.status.canInvoice.no') }
];

// 获取订单状态标签
const getOrderStatusLabel = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;
};

// 获取订单状态标签类型
const getOrderStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': 'info',
    '取消审核中': 'warning',
    '取消审核通过': 'warning',
    '已取消': 'danger',
    '已确认': 'success',
    '待审核': 'warning',
    '已审核': 'success',
    '待交车': 'primary',
    '已交车': 'success'
  };
  return statusMap[status] || 'info';
};

// 获取付款状态标签
const getPaymentStatusLabel = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;
};

// 获取付款状态标签类型
const getPaymentStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待支付定金': 'warning',
    '已支付定金': 'primary',
    '退款中': 'warning',
    '退款完成': 'info',
    '待支付尾款': 'warning',
    '已支付尾款': 'success'
  };
  return statusMap[status] || 'info';
};

// 格式化货币
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-MY', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// 格式化手机号
const formatPhone = (phone: string) => {
  if (!phone) return '';
  // 简单的手机号格式化，可根据需要调整
  return phone.replace(/(\d{3})(\d{3,4})(\d{4})/, '$1-$2-$3');
};

// 获取列表数据
const getListData = async () => {
  try {
    tableLoading.value = true;
    const params = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    };

    const response = await getWholeVehicleCollectionList(params);

    if (response.code === '200' || response.code === 200) {
      tableData.value = response.result.records;
      pagination.total = response.result.total;
      pagination.pageNum = response.result.pageNum;
      pagination.pageSize = response.result.pageSize;
    } else {
      ElMessage.error(response.message || t('wholeVehicleCollection.messages.loadError'));
    }
  } catch (error) {
    console.error('获取列表数据失败:', error);
    ElMessage.error(t('wholeVehicleCollection.messages.loadError'));
  } finally {
    tableLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  getListData();
};

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields();
  dateRange.value = null;
  Object.assign(searchForm, {
    orderNumber: '',
    buyerName: '',
    buyerPhone: '',
    orderStatus: '',
    paymentStatus: '',
    canInvoice: undefined,
    startDate: undefined,
    endDate: undefined
  });
  pagination.pageNum = 1;
  getListData();
};



// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;
  getListData();
};

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;
  getListData();
};

// 查看详情
const handleViewDetail = (row: WholeVehicleCollectionItem) => {
  selectedOrderId.value = row.orderId;
  orderDetailVisible.value = true;
};

// 收退款操作
const handlePaymentOperation = (row: WholeVehicleCollectionItem) => {
  selectedOrderData.value = row;
  paymentOperationVisible.value = true;
};

// 收退款操作成功回调
const handlePaymentSuccess = () => {
  getListData();
};

// 组件挂载时获取数据
onMounted(() => {
  getListData();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
}

.mb-20 {
  margin-bottom: 20px;
}

.search-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin-bottom: 0;
}

.buttons-col {
  display: flex;
  align-items: flex-end;
}

.table-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.price-info {
  font-size: 12px;
  line-height: 1.4;
}

.text-gray-500 {
  color: #9ca3af;
}

.text-green-600 {
  color: #059669;
}

.text-red-600 {
  color: #dc2626;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 8px 10px;
}
</style>
