<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getInventoryList } from '@/mock/data/inventory'

const { t } = useI18n()

// 使用字典数据
const {
  options: inventoryStatusOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.INVENTORY_STATUS)

// 搜索表单数据
const searchFormRef = ref<FormInstance>()
const searchParams = reactive({
  partName: '',
  partNumber: '',
  supplierName: '',
  inventoryStatus: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})



// 搜索方法
const handleSearch = async () => {
  loading.value = true
  try {
    const response = await getInventoryList({
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...searchParams
    })
    tableData.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取库存数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  if (!searchFormRef.value) return
  searchFormRef.value.resetFields()
  handleSearch()
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  handleSearch()
}

// 初始化数据
onMounted(() => {
  handleSearch()
})
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('inventory.title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form
        ref="searchFormRef"
        :model="searchParams"
        class="search-form"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('inventory.partName')">
              <el-input
                v-model="searchParams.partName"
                :placeholder="t('inventory.partNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('inventory.partNumber')">
              <el-input
                v-model="searchParams.partNumber"
                :placeholder="t('inventory.partNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('inventory.supplierName')">
              <el-input
                v-model="searchParams.supplierName"
                :placeholder="t('inventory.supplierNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('inventory.inventoryStatus')">
              <el-select
                v-model="searchParams.inventoryStatus"
                :placeholder="t('inventory.inventoryStatusPlaceholder')"
                clearable
                class="w-full"
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="item in inventoryStatusOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ t('common.search') }}
              </el-button>
              <el-button :icon="Refresh" @click="resetSearch">
                {{ t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column type="index" :label="t('common.index')" width="60" />
        <el-table-column
          prop="partName"
          :label="t('inventory.partName')"
          min-width="120"
        />
        <el-table-column
          prop="partNumber"
          :label="t('inventory.partNumber')"
          min-width="120"
        />
        <el-table-column
          prop="supplierName"
          :label="t('inventory.supplierName')"
          min-width="120"
        />
        <el-table-column
          prop="totalInventory"
          :label="t('inventory.totalInventory')"
          min-width="100"
        />
        <el-table-column
          prop="availableInventory"
          :label="t('inventory.availableInventory')"
          min-width="100"
        />
        <el-table-column
          prop="lockedInventory"
          :label="t('inventory.lockedInventory')"
          min-width="100"
        />
        <el-table-column
          prop="defectiveCount"
          :label="t('inventory.defectiveCount')"
          min-width="100"
        />
        <el-table-column
          prop="pendingCount"
          :label="t('inventory.pendingCount')"
          min-width="100"
        />
        <el-table-column
          prop="safetyStock"
          :label="t('inventory.safetyStock')"
          min-width="100"
        />
        <el-table-column
          prop="inventoryStatus"
          :label="t('inventory.inventoryStatus')"
          min-width="120"
        >
          <template #default="{ row }">
            <el-tag
              :type="row.inventoryStatus === 'normal' ? 'success' : 'warning'"
            >
              {{ t(`inventory.status.${row.inventoryStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.search-form {
  .el-form-item {
    margin-bottom: 15px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.w-full {
  width: 100%;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>

</style>
