
<template>
  <div class="page-container">
    <InventoryDashboard />
    <InventorySearchForm />
    <InventoryListTable
      @detail="handleDetail"
      @adjust="handleAdjust"
      @batch-replenish="handleBatchReplenish"
    />
    <InventoryDetailModal
      v-model="detailModalVisible"
      :inventory-id="selectedInventoryId"
    />
    <InventoryAdjustModal
      v-model="adjustModalVisible"
      :inventory-id="selectedInventoryId"
      @success="handleAdjustSuccess"
    />
    <BatchReplenishmentModal
      v-model="batchReplenishModalVisible"
      :selected-items="selectedItemsForReplenish"
      @success="handleReplenishSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useInventoryStore } from '@/stores/modules/parts/inventory';
import InventoryDashboard from './components/InventoryDashboard.vue';
import InventorySearchForm from './components/InventorySearchForm.vue';
import InventoryListTable from './components/InventoryListTable.vue';
import InventoryDetailModal from './components/InventoryDetailModal.vue';
import InventoryAdjustModal from './components/InventoryAdjustModal.vue';
import BatchReplenishmentModal from './components/BatchReplenishmentModal.vue';
import type { InventoryItem } from '@/types/parts/inventory';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';

const { t } = useModuleI18n('parts.inventoryManagement');
const inventoryStore = useInventoryStore();

const detailModalVisible = ref(false);
const adjustModalVisible = ref(false);
const batchReplenishModalVisible = ref(false);

const selectedInventoryId = ref<number | null>(null);
const selectedItemsForReplenish = ref<InventoryItem[]>([]);

const handleDetail = (inventoryId: number) => {
  selectedInventoryId.value = inventoryId;
  detailModalVisible.value = true;
};

const handleAdjust = (inventoryId: number) => {
  selectedInventoryId.value = inventoryId;
  adjustModalVisible.value = true;
};

const handleBatchReplenish = (items: InventoryItem[]) => {
  selectedItemsForReplenish.value = items;
  batchReplenishModalVisible.value = true;
};

const refreshList = () => {
  inventoryStore.fetchInventoryList({ storeId: 1001 }); // Hardcoded storeId
};

const handleAdjustSuccess = () => {
  ElMessage.success(t('messages.adjustSuccess'));
  refreshList();
};

const handleReplenishSuccess = () => {
  ElMessage.success(t('messages.replenishmentSuccess'));
  refreshList();
};

onMounted(() => {
  inventoryStore.fetchDashboard(1001); // Hardcoded storeId
  refreshList();
});
</script>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}
</style>
