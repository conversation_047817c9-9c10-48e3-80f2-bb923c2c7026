
<template>
  <el-card class="table-card">
    <div class="function-area mb-20">
      <div class="function-title">{{ t('function.title') }}</div>
      <div class="action-buttons">
        <el-button @click="onInventoryCheck">{{ t('actions.inventoryCheck') }}</el-button>
        <el-button type="primary" @click="onBatchReplenishment">{{ t('actions.batchReplenishment') }}</el-button>
        <el-button @click="onSetSafetyStock">{{ t('actions.setSafetyStock') }}</el-button>
      </div>
    </div>

    <el-table :data="inventoryList" v-loading="loading.list" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column :label="t('table.stockStatus')" prop="stockStatus" align="center" width="100">
        <template #default="{ row }">
          <span class="status-display">
            {{ getStatusEmoji(row.stockStatus) }} {{ t(`status.${row.stockStatus}`) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="t('table.partCode')" prop="partCode" width="130" />
      <el-table-column :label="t('table.partName')" prop="partName" min-width="180" show-overflow-tooltip />
      <el-table-column :label="t('table.currentStock')" prop="currentStock" align="center" width="100" />
      <el-table-column :label="t('table.safetyStock')" prop="safetyStock" align="center" width="100" />
      <el-table-column :label="t('table.availableStock')" prop="availableStock" align="center" width="100" />
      <el-table-column :label="t('table.occupiedStock')" prop="occupiedStock" align="center" width="120" />
      <el-table-column :label="t('table.lastCheckTime')" prop="lastCheckTime" align="center" width="120" />
      <el-table-column :label="globalT('common.operations')" fixed="right" align="center" width="140">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="onDetail(row)">
            {{ globalT('common.detail') }}
          </el-button>
          <el-button link type="warning" size="small" @click="onAdjust(row)">
            {{ t('actions.adjust') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useInventoryStore } from '@/stores/modules/parts/inventory';
import { storeToRefs } from 'pinia';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useI18n } from 'vue-i18n';
import type { InventoryItem, StockStatus } from '@/types/parts/inventory';

const emit = defineEmits(['detail', 'adjust', 'batch-replenish']);

const { t } = useModuleI18n('parts.inventoryManagement');
const { t: globalT } = useI18n();
const inventoryStore = useInventoryStore();
const { inventoryList, loading, pagination } = storeToRefs(inventoryStore);

const selectedItems = ref<InventoryItem[]>([]);

const getStatusEmoji = (status: StockStatus) => {
  switch (status) {
    case 'SHORTAGE': return '🔴';
    case 'WARNING': return '⚠️';
    case 'NORMAL': return '✅';
    case 'OVERSTOCKED': return '📦';
    default: return '❓';
  }
};

const handleSelectionChange = (selection: InventoryItem[]) => {
  selectedItems.value = selection;
};

const handleSizeChange = (size: number) => {
  inventoryStore.setPageSize(size);
  inventoryStore.fetchInventoryList({ storeId: 1001 }); // Assuming storeId
};

const handlePageChange = (page: number) => {
  inventoryStore.setPage(page);
  inventoryStore.fetchInventoryList({ storeId: 1001 }); // Assuming storeId
};

const onDetail = (item: InventoryItem) => emit('detail', item.inventoryId);
const onAdjust = (item: InventoryItem) => emit('adjust', item.inventoryId);

const onInventoryCheck = () => {
  // TODO: 实现库存盘点功能
  console.log('库存盘点功能待实现');
};

const onBatchReplenishment = () => {
  if (selectedItems.value.length === 0) {
    // ElMessage.warning(tc('noDataSelected'));
    return;
  }
  emit('batch-replenish', selectedItems.value);
};

const onSetSafetyStock = () => {
  // TODO: 实现安全库存设置功能
  console.log('安全库存设置功能待实现');
};
</script>

<style scoped lang="scss">
.table-card {
  .function-area {
    .function-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      color: #303133;
      border-left: 3px solid #409EFF;
      padding-left: 8px;
    }
    
    .action-buttons {
      display: flex;
      gap: 12px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

.mb-20 {
  margin-bottom: 20px;
}

.status-display {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}
</style>
