
<template>
  <el-card class="search-card mb-20">
    <div class="search-title">{{ globalT('common.searchConditions') }}</div>
    <el-form :model="searchForm" label-position="top">
      <el-row :gutter="20" align="bottom">
        <el-col :span="5">
          <el-form-item :label="t('search.category')">
            <el-select
              v-model="searchForm.category"
              clearable
              :placeholder="t('search.categoryPlaceholder')"
              style="width: 100%"
            >
              <el-option label="制动系统" value="制动系统" />
              <el-option label="发动机" value="发动机" />
              <el-option label="悬挂系统" value="悬挂系统" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item :label="t('search.partCode')">
            <el-input
              v-model="searchForm.partCode"
              :placeholder="t('search.partCodePlaceholder')"
              clearable
              @keyup.enter="onSearch"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item :label="t('search.partName')">
            <el-input
              v-model="searchForm.partName"
              :placeholder="t('search.partNamePlaceholder')"
              clearable
              @keyup.enter="onSearch"
            />
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="&nbsp;">
            <el-button @click="onReset">{{ globalT('common.reset') }}</el-button>
            <el-button type="primary" @click="onSearch" :loading="loading.list">{{ globalT('common.search') }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useInventoryStore } from '@/stores/modules/parts/inventory';
import { storeToRefs } from 'pinia';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useI18n } from 'vue-i18n';
import type { StockStatus } from '@/types/parts/inventory';

const { t } = useModuleI18n('parts.inventoryManagement');
const { t: globalT } = useI18n();
const inventoryStore = useInventoryStore();
const { loading } = storeToRefs(inventoryStore);

const searchForm = reactive({
  partCode: '',
  partName: '',
  category: '',
  storeId: 1001, // Assuming a default storeId
});

const onSearch = () => {
  inventoryStore.setPage(1);
  inventoryStore.fetchInventoryList(searchForm);
};

const onReset = () => {
  searchForm.partCode = '';
  searchForm.partName = '';
  searchForm.category = '';
  onSearch();
};
</script>

<style scoped lang="scss">
.search-card {
  .search-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #303133;
    border-left: 3px solid #409EFF;
    padding-left: 8px;
  }
  
  .el-form-item {
    margin-bottom: 0;
  }
  
  .el-button {
    margin-left: 8px;
    
    &:first-child {
      margin-left: 0;
    }
  }
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
