<template>
  <el-form :model="form" ref="formRef" :rules="formRules" label-width="120px" style="max-width: 600px">
    <el-form-item :label="t('partName')" prop="partName">
      <el-select
        v-model="form.partName"
        :placeholder="t('partNamePlaceholder')"
        filterable
        clearable
      >
        <el-option
          v-for="item in partOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('partNumber')" prop="partNumber">
      <el-select
        v-model="form.partNumber"
        :placeholder="t('partNumberPlaceholder')"
        filterable
        clearable
      >
        <el-option
          v-for="item in partNumberOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('quantity')">
      <el-input-number v-model="form.quantity" :min="1" :disabled="false" />
    </el-form-item>
    <el-form-item :label="t('storeName')">
      <el-input v-model="form.storeName" disabled />
    </el-form-item>
    <el-form-item :label="t('expectedArrivalTime')">
      <el-date-picker v-model="form.expectedArrivalTime" type="date" :disabled="false" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleAdd" :disabled="isAddButtonDisabled">{{ tc('add') }}</el-button>
      <el-button @click="handleReset">{{ tc('reset') }}</el-button>
    </el-form-item>
    <el-table :data="requisitionItems" style="width: 100%" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" header-class-name="hide-selection-header" />
      <el-table-column type="index" :label="tc('sequence')" width="80" />
      <el-table-column prop="partName" :label="t('partName')" />
      <el-table-column prop="partNumber" :label="t('partNumber')" />
      <el-table-column prop="quantity" :label="t('quantity')" />
      <el-table-column :label="t('expectedArrivalTime')" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.expectedArrivalTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="tc('operations')" width="100">
        <template #default="scope">
          <el-button
            link
            type="danger"
            size="small"
            @click="handleDeleteItem(scope.row)"
            :disabled="!selectedItems.includes(scope.row)"
          >{{ tc('delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-form-item class="mt-4">
      <el-button type="primary" @click="submitForm">{{ t('submitRequisition') }}</el-button>
      <el-button @click="cancelForm">{{ tc('cancel') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { FormInstance, FormRules } from 'element-plus';
import { createRequisition, updateRequisition } from '@/api/modules/parts/management';
import { getPartArchives } from '@/api/modules/parts/archives';

// 国际化设置
const { t, tc } = useModuleI18n('parts.newRequisitionForm');

const emit = defineEmits(['submit-success', 'cancel']);

const props = defineProps({
  initialData: {
    type: Object,
    default: null,
  },
});

// 定义数据类型
interface RequisitionItem {
  partName: string;
  partNumber: string;
  quantity: number;
  expectedArrivalTime: string;
  supplierName: string;
}

interface OptionItem {
  label: string;
  value: string;
}

const formRef = ref<FormInstance>();

const form = reactive({
  partName: '',
  partNumber: '',
  quantity: 1,
  storeName: '示例门店', // This should be dynamically set
  expectedArrivalTime: '',
});

const formRules = reactive<FormRules>({
  partName: [
    { required: true, message: t('partNameRequired'), trigger: 'change' },
  ],
  partNumber: [
    { required: true, message: t('partNumberRequired'), trigger: 'change' },
  ],
});

const isAddButtonDisabled = computed(() => {
  return !(form.partName && form.partNumber);
});

const requisitionItems = reactive<any[]>([]);
const selectedItems = ref<any[]>([]); // 用于存储表格选中项
const originalRequisitionNumber = ref<string>(''); // 存储原始叫料单号

// 零件选项数据
const partOptions = ref<OptionItem[]>([]);
const partNumberOptions = ref<OptionItem[]>([]);
const partsArchivesData = ref<any[]>([]);

// 加载零件档案数据
const loadPartsArchives = async () => {
  try {
    const response = await getPartArchives({ page: 1, pageSize: 1000 });
    partsArchivesData.value = response.list;
    
    // 生成选项数据
    const uniquePartNames = Array.from(new Set(response.list.map((item: any) => item.partName)));
    const uniquePartNumbers = Array.from(new Set(response.list.map((item: any) => item.partNumber)));
    
    partOptions.value = uniquePartNames.map((name: string) => ({ label: name, value: name }));
    partNumberOptions.value = uniquePartNumbers.map((number: string) => ({ label: number, value: number }));
  } catch (error) {
    console.error('Failed to load parts archives:', error);
    ElMessage.error(tc('loadDataFailed'));
  }
};

watch(() => form.partName, (newVal) => {
  if (newVal) {
    const selectedPart = partsArchivesData.value.find(item => item.partName === newVal);
    if (selectedPart && form.partNumber !== selectedPart.partNumber) {
      form.partNumber = selectedPart.partNumber;
    }
  } else {
    form.partNumber = '';
  }
});

watch(() => form.partNumber, (newVal) => {
  if (newVal) {
    const selectedPart = partsArchivesData.value.find(item => item.partNumber === newVal);
    if (selectedPart && form.partName !== selectedPart.partName) {
      form.partName = selectedPart.partName;
    }
  } else {
    form.partName = '';
  }
});

watch(() => props.initialData, (newVal) => {
  if (newVal) {
    // 如果有传入 initialData，则将其用于初始化 requisitionItems
    // 假设 initialData.items 包含了叫料单明细
    Object.assign(requisitionItems, newVal.items || []);
    // 保存原始叫料单号，用于编辑时的更新操作
    originalRequisitionNumber.value = newVal.requisitionNumber || '';
    // 如果需要，也可以初始化表单的其他字段
    // 例如：form.requisitionNumber = newVal.requisitionNumber;
  } else {
    // 如果 initialData 为空，表示是新建操作，清空表单和列表
    requisitionItems.splice(0, requisitionItems.length);
    originalRequisitionNumber.value = '';
    Object.assign(form, { partName: '', partNumber: '', quantity: 1, expectedArrivalTime: '' });
  }
}, { immediate: true }); // immediate: true 使得在组件挂载时也执行一次监听

// 组件挂载时加载数据
loadPartsArchives();

const handleAdd = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const selectedPart = partsArchivesData.value.find(item => item.partName === form.partName);
    requisitionItems.push({
      ...form,
      supplierName: selectedPart?.supplierName || ''
    });
    Object.assign(form, {
      partName: '',
      partNumber: '',
      quantity: 1,
      expectedArrivalTime: '',
      supplierName: selectedPart?.supplierName || ''
    });
    formRef.value?.resetFields(); // 重置表单验证状态
  } catch (error) {
    ElMessage.warning(tc('fillAllRequired'));
  }
};

const handleReset = () => {
  Object.assign(form, { partName: '', partNumber: '', quantity: 1, expectedArrivalTime: '' });
  formRef.value?.resetFields(); // 重置表单验证状态
};

const handleSelectionChange = (val: RequisitionItem[]) => {
  selectedItems.value = val;
};

const handleDeleteItem = async (rowToDelete: RequisitionItem) => {
  try {
    await ElMessageBox.confirm(
      tc('confirmDelete', { item: 1 + tc('item') }),
      tc('tip'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning',
      }
    );


    const index = requisitionItems.findIndex(item => item === rowToDelete);
    if (index !== -1) {
      requisitionItems.splice(index, 1);
    }
    selectedItems.value = selectedItems.value.filter(item => item !== rowToDelete); // 移除已删除的选中项
    ElMessage.success(tc('operationSuccessful'));
  } catch (error) {
    ElMessage.info(tc('operationCanceled'));
  }
};

const submitForm = async () => {
  if (requisitionItems.length === 0) {
    ElMessage.warning(t('addItemsPrompt'));
    return;
  }

  try {
    // Logic to submit the new requisition, including requisitionItems
    console.log('提交叫料单：', requisitionItems);

    const isEditing = props.initialData && originalRequisitionNumber.value;
    const isEditingSubmitted = isEditing && props.initialData.requisitionStatus === 'submitted';
    const isEditingRejected = isEditing && props.initialData.requisitionStatus === 'rejected';

    // 如果是编辑已驳回的叫料单，显示确认弹窗
    if (isEditingRejected) {
      await ElMessageBox.confirm(
        t('rejectedRequisitionConfirmMessage'),
        tc('confirm'),
        {
          confirmButtonText: tc('confirm'),
          cancelButtonText: tc('cancel'),
          type: 'warning',
        }
      );
    }

    if (isEditingSubmitted) {
      // 已提交状态：更新现有叫料单，不生成新单据号
      const updatedRequisition = {
        requisitionNumber: originalRequisitionNumber.value, // 使用原始单据号
        partName: requisitionItems[0].partName,
        partNumber: requisitionItems[0].partNumber,
        supplierName: requisitionItems[0].supplierName || '默认供应商',
        items: requisitionItems.map(item => ({
          id: `DI${Date.now()}${Math.random().toString(36).substring(2, 6)}`,
          partName: item.partName,
          partNumber: item.partNumber,
          quantity: item.quantity,
          unit: '个',
          requisitionStatus: 'submitted',
          requisitionDate: new Date(item.expectedArrivalTime).toISOString().slice(0, 10),
          expectedArrivalTime: new Date(item.expectedArrivalTime).toISOString().slice(0, 10),
          supplierName: item.supplierName || '默认供应商',
        })),
      };

      updateRequisitionItem(originalRequisitionNumber.value, updatedRequisition);
      ElMessage.success(t('requisitionUpdated'));

    } else {
      // 新建或已驳回状态：创建新的叫料单
      const newRequisition = {
        id: `PM${Date.now()}`,
        requisitionNumber: `JL${Date.now().toString().slice(-8)}`, // 生成新的叫料单号
        purchaseOrderNumber: '', // 模拟数据可以为空
        requisitionDate: new Date().toISOString().slice(0, 10),
        requisitionStatus: 'submitted', // 默认为提交状态
        partName: requisitionItems[0].partName, // 取第一个零件的名称作为主记录的零件名称
        partNumber: requisitionItems[0].partNumber, // 取第一个零件的编号作为主记录的零件编号
        supplierName: requisitionItems[0].supplierName || '默认供应商', // 可以根据实际情况设置
        inventoryStatus: 'normal',
        items: requisitionItems.map(item => ({
          id: `DI${Date.now()}${Math.random().toString(36).substring(2, 6)}`,
          partName: item.partName,
          partNumber: item.partNumber,
          quantity: item.quantity,
          unit: '个', // 假设单位，或者从partsSelectData中获取
          requisitionStatus: 'submitted',
          requisitionDate: new Date(item.expectedArrivalTime).toISOString().slice(0, 10),
          expectedArrivalTime: new Date(item.expectedArrivalTime).toISOString().slice(0, 10),
          supplierName: item.supplierName || '默认供应商',
        })),
      };

      createRequisition(newRequisition);

      if (isEditingRejected) {
        ElMessage.success(t('rejectedRequisitionResubmitted'));
      } else {
        ElMessage.success(tc('operationSuccessful'));
      }
    }

    emit('submit-success');
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Submit failed:', error);
      ElMessage.error(error.message || tc('operationFailed'));
    }
  }
};

const cancelForm = () => {
  emit('cancel');
};

// Helper function to format date
const formatDate = (dateString: string | Date) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};
</script>

<style scoped>
.mt-4 {
  margin-top: 20px;
}

.hide-selection-header .el-checkbox {
  display: none;
}

/* 确保表单左对齐 */
.el-form {
  text-align: left;
}

.el-form-item {
  margin-bottom: 18px;
}

/* 确保表单项标签左对齐 */
.el-form-item__label {
  text-align: right;
  padding-right: 12px;
}

/* 确保表单项内容左对齐 */
.el-form-item__content {
  text-align: left;
}
</style>
