<template>
  <el-form :model="form" ref="formRef" :rules="formRules" label-width="auto">
    <!-- 收货单号字段 - 仅在从收货页面进入时显示 -->
    <el-form-item v-if="receiptOrderNumber && receiptOrderNumber.length > 0" label="收货单号">
      <el-input :value="receiptOrderNumber" disabled style="width: 100%;" />
    </el-form-item>
    <el-form-item :label="t('partName')" prop="partName">
      <el-select
        v-model="form.partName"
        :placeholder="t('partNamePlaceholder')"
        filterable
        clearable
        style="width: 100%;"
      >
        <el-option
          v-for="item in partOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('partNumber')" prop="partNumber">
      <el-select
        v-model="form.partNumber"
        :placeholder="t('partNumberPlaceholder')"
        filterable
        clearable
        style="width: 100%;"
      >
        <el-option
          v-for="item in partNumberOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('quantity')" prop="quantity">
      <el-input-number v-model="form.quantity" :min="1" />
    </el-form-item>
    <el-form-item :label="t('scrapSource')" prop="scrapSource">
      <el-select
        v-if="!receiptOrderNumber || receiptOrderNumber.length === 0"
        v-model="form.scrapSource"
        :placeholder="t('selectScrapSource')"
        style="width: 100%;"
      >
        <el-option :label="t('sourceReceipt')" value="receipt"></el-option>
        <el-option :label="t('sourceRepair')" value="repair"></el-option>
        <el-option :label="t('sourceOther')" value="other"></el-option>
      </el-select>
      <el-input
        v-else
        :value="t('sourceReceipt')"
        disabled
        style="width: 100%;"
      />
    </el-form-item>
    <!-- 收货单号字段 - 当报损来源为"收货报损"时显示 -->
    <el-form-item
      v-if="form.scrapSource === 'receipt' && (!receiptOrderNumber || receiptOrderNumber.length === 0)"
      label="收货单号"
      prop="receiptOrderNumberInput"
      required
    >
      <el-input
        v-model="form.receiptOrderNumberInput"
        placeholder="请输入收货单号"
        style="width: 100%;"
      />
    </el-form-item>
    <el-form-item :label="t('scrapReason')" prop="scrapReason">
      <el-input v-model="form.scrapReason" type="textarea" :rows="3" maxlength="200" show-word-limit />
    </el-form-item>
    <el-form-item :label="t('scrapImages')" prop="scrapImages" required>
      <div class="upload-container">
        <!-- 自定义图片上传和预览区域 -->
        <div class="custom-upload-area">
          <!-- 已上传的图片预览 -->
          <div class="uploaded-images" v-if="form.scrapImages.length > 0">
            <div
              v-for="(image, index) in form.scrapImages"
              :key="index"
              class="image-item"
            >
              <div class="image-wrapper">
                <img
                  :src="getImageUrl(image)"
                  alt="报损图片"
                  class="preview-img"
                  @click="previewImage(index)"
                  @error="handleImageError"
                />
                <div class="image-actions">
                  <el-icon class="action-icon zoom-icon" @click="previewImage(index)">
                    <ZoomIn />
                  </el-icon>
                  <el-icon class="action-icon delete-icon" @click="removeImage(index)">
                    <Close />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>

          <!-- 上传按钮 -->
          <div class="upload-button" v-if="form.scrapImages.length < 5">
            <el-upload
              action="#"
              :auto-upload="false"
              :on-change="handleImageChange"
              :before-upload="beforeImageUpload"
              accept="image/*"
              :show-file-list="false"
            >
              <div class="upload-area">
                <el-icon class="upload-icon"><Plus /></el-icon>
                <div class="upload-text">上传图片</div>
              </div>
            </el-upload>
          </div>
        </div>

        <div class="upload-tip">
          {{ t('imageUploadTip') }}
        </div>
      </div>
    </el-form-item>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="图片预览"
      width="90%"
      :before-close="closeImagePreview"
      append-to-body
      :modal="true"
      class="image-preview-dialog"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <div class="image-preview-container">
        <div class="image-viewer-wrapper">
          <!-- 图片显示区域 -->
          <div class="image-display-area">
            <img
              v-if="currentPreviewImage"
              :src="currentPreviewImage"
              alt="预览图片"
              class="preview-large-image"
              :style="{
                transform: `scale(${imageScale}) rotate(${imageRotation}deg)`,
                transition: 'transform 0.3s ease'
              }"
              @load="handleImageLoad"
              @error="handleImageError"
            />
          </div>

          <!-- 图片导航控制 -->
          <div class="image-navigation" v-if="form.scrapImages.length > 1">
            <el-button
              circle
              :icon="ArrowLeft"
              @click="prevImage"
              :disabled="currentImageIndex <= 0"
              class="nav-button prev-button"
            />
            <el-button
              circle
              :icon="ArrowRight"
              @click="nextImage"
              :disabled="currentImageIndex >= form.scrapImages.length - 1"
              class="nav-button next-button"
            />
          </div>

          <!-- 图片信息栏 -->
          <div class="image-info-bar">
            <div class="image-counter">
              {{ currentImageIndex + 1 }} / {{ form.scrapImages.length }}
            </div>
            <div class="image-actions">
              <el-button
                circle
                :icon="ZoomIn"
                @click="zoomIn"
                class="action-button"
                title="放大"
              />
              <el-button
                circle
                :icon="ZoomOut"
                @click="zoomOut"
                class="action-button"
                title="缩小"
              />
              <el-button
                circle
                :icon="RefreshRight"
                @click="rotateRight"
                class="action-button"
                title="顺时针旋转"
              />
              <el-button
                circle
                :icon="RefreshLeft"
                @click="rotateLeft"
                class="action-button"
                title="逆时针旋转"
              />
              <el-button
                circle
                :icon="Delete"
                @click="removeCurrentImage"
                class="action-button delete-button"
                type="danger"
                title="删除当前图片"
              />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="preview-dialog-footer">
          <el-button @click="closeImagePreview" size="large">
            关闭预览
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 表格图片预览对话框 -->
    <el-dialog
      v-model="tableImagePreviewVisible"
      title="图片预览"
      width="90%"
      :before-close="closeTableImagePreview"
      append-to-body
      :modal="true"
      class="image-preview-dialog"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <div class="image-preview-container">
        <div class="image-viewer-wrapper">
          <!-- 图片显示区域 -->
          <div class="image-display-area">
            <img
              v-if="currentTablePreviewImage"
              :src="currentTablePreviewImage"
              alt="预览图片"
              class="preview-large-image"
              :style="{
                transform: `scale(${tableImageScale}) rotate(${tableImageRotation}deg)`,
                transition: 'transform 0.3s ease'
              }"
              @load="handleTableImageLoad"
              @error="handleImageError"
            />
          </div>

          <!-- 图片导航控制 -->
          <div class="image-navigation" v-if="currentTableImages.length > 1">
            <el-button
              circle
              :icon="ArrowLeft"
              @click="prevTableImage"
              :disabled="currentTableImageIndex <= 0"
              class="nav-button prev-button"
            />
            <el-button
              circle
              :icon="ArrowRight"
              @click="nextTableImage"
              :disabled="currentTableImageIndex >= currentTableImages.length - 1"
              class="nav-button next-button"
            />
          </div>

          <!-- 图片信息栏 -->
          <div class="image-info-bar">
            <div class="image-counter">
              {{ currentTableImageIndex + 1 }} / {{ currentTableImages.length }}
            </div>
            <div class="image-actions">
              <el-button
                circle
                :icon="ZoomIn"
                @click="zoomInTable"
                class="action-button"
                title="放大"
              />
              <el-button
                circle
                :icon="ZoomOut"
                @click="zoomOutTable"
                class="action-button"
                title="缩小"
              />
              <el-button
                circle
                :icon="RefreshRight"
                @click="rotateRightTable"
                class="action-button"
                title="顺时针旋转"
              />
              <el-button
                circle
                :icon="RefreshLeft"
                @click="rotateLeftTable"
                class="action-button"
                title="逆时针旋转"
              />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="preview-dialog-footer">
          <el-button @click="closeTableImagePreview" size="large">
            关闭预览
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-form-item>
      <el-button type="primary" @click="handleAdd" :disabled="isAddButtonDisabled">{{ tc('add') }}</el-button>
    </el-form-item>
    <div style="width: 100%; margin-bottom: 20px;">
      <el-table :data="scrapItems" border width="100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" :label="tc('sequence')" width="80" />
        <el-table-column prop="partName" :label="t('partName')" min-width="100" />
        <el-table-column prop="partNumber" :label="t('partNumber')" min-width="100" />
        <el-table-column prop="quantity" :label="t('quantity')" min-width="100" />
        <el-table-column :label="t('scrapSource')" min-width="100">
          <template #default="scope">
            {{ getScrapSourceLabel(scope.row.scrapSource) }}
          </template>
        </el-table-column>
        <el-table-column prop="scrapReason" :label="t('scrapReason')" min-width="150" />
        <el-table-column :label="t('scrapImages')" min-width="120">
          <template #default="scope">
            <div class="image-preview" v-if="scope.row.scrapImages && scope.row.scrapImages.length > 0">
              <div
                v-for="(image, index) in scope.row.scrapImages.slice(0, 3)"
                :key="index"
                class="table-image-item"
              >
                <img
                  :src="getImageUrl(image)"
                  alt="报损图片"
                  class="preview-image"
                  @click="previewTableImage(scope.row.scrapImages, index)"
                  @error="handleImageError"
                />
                <el-icon
                  class="table-image-delete"
                  @click.stop="removeImageFromItem(scope.$index, index)"
                >
                  <Close />
                </el-icon>
              </div>
              <span v-if="scope.row.scrapImages.length > 3" class="more-images">
                +{{ scope.row.scrapImages.length - 3 }}
              </span>
            </div>
            <span v-else class="no-image">{{ t('noImages') }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" width="100" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="danger"
              size="small"
              @click="handleDeleteItem(scope.row)"
              :disabled="!selectedItems.includes(scope.row)"
            >{{ tc('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-form-item class="mt-4">
      <el-button type="primary" @click="submitForm">{{ tc('confirm') }}</el-button>
      <el-button @click="cancelForm">{{ tc('cancel') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed, toRefs, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, ZoomIn, Close, ArrowLeft, ArrowRight, ZoomOut, RefreshRight, RefreshLeft, Delete } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getPartArchives } from '@/api/modules/parts/archives';
import { createScrapRecord, updateScrapRecord } from '@/api/modules/parts/management';
import type { FormInstance, FormRules, UploadFile, UploadFiles } from 'element-plus';

// 类型定义
interface ScrapItem {
  partName: string;
  partNumber: string;
  quantity: number;
  scrapReason: string;
  scrapSource: string;
  receiptOrderNumber?: string;
  scrapImages: UploadFile[];
}

interface OptionItem {
  label: string;
  value: string;
}

// 国际化设置
const { t, tc } = useModuleI18n('parts.partScrapForm');

const emit = defineEmits(['submit-success', 'cancel']);

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
  editMode: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Object,
    default: null,
  },
  receiptOrderNumber: {
    type: String,
    default: '',
  },
});

// 解构props以便在模板中使用
const { receiptOrderNumber } = toRefs(props);

const formRef = ref<FormInstance>();

const form = reactive({
  partName: '',
  partNumber: '',
  quantity: 1,
  scrapReason: '',
  scrapSource: '', // New field
  receiptOrderNumberInput: '', // 收货单号输入字段
  scrapImages: [] as UploadFile[], // 图片列表
});

// 组件挂载后设置初始值
onMounted(() => {
  // 如果是从收货页面进入，自动设置报损来源为收货报损
  if (receiptOrderNumber.value && receiptOrderNumber.value.length > 0) {
    form.scrapSource = 'receipt';
    console.log('PartScrapForm initialized with receiptOrderNumber:', receiptOrderNumber.value);
    console.log('Set form.scrapSource to receipt on mount:', form.scrapSource);
  } else {
    console.log('No receiptOrderNumber on mount:', receiptOrderNumber.value);
  }
});

// 监控receiptOrderNumber的变化，自动设置报损来源
watch(receiptOrderNumber, (newVal) => {
  console.log('PartScrapForm receiptOrderNumber changed:', newVal);
  // 如果是从收货页面进入，自动设置报损来源为收货报损
  if (newVal && newVal.length > 0) {
    form.scrapSource = 'receipt';
    console.log('Set form.scrapSource to receipt:', form.scrapSource);
  }
});

const formRules = reactive<FormRules>({
  partName: [
    { required: true, message: t('partNameRequired'), trigger: 'change' },
  ],
  partNumber: [
    { required: true, message: t('partNumberRequired'), trigger: 'change' },
  ],
  quantity: [
    { required: true, message: t('quantityRequired'), trigger: 'change' },
  ],
  scrapSource: [
    { required: true, message: t('scrapSourceRequired'), trigger: 'change' },
  ],
  receiptOrderNumberInput: [
    {
      validator: (rule: any, value: string, callback: any) => {
        // 只有当报损来源为"收货报损"且不是从收货页面进入时才需要验证
        if (form.scrapSource === 'receipt' && (!receiptOrderNumber.value || receiptOrderNumber.value.length === 0)) {
          if (!value || value.trim() === '') {
            callback(new Error('收货单号为必填项'));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: ['blur', 'change']
    },
  ],
  scrapReason: [
    { required: true, message: t('scrapReasonRequired'), trigger: 'blur' },
  ],
  scrapImages: [
    {
      validator: (rule: any, value: UploadFile[], callback: any) => {
        if (!value || value.length === 0) {
          callback(new Error(t('scrapImagesRequired')));
        } else {
          callback();
        }
      },
      trigger: 'change'
    },
  ],
});

const isAddButtonDisabled = computed(() => {
  // 基础字段验证
  const basicFieldsValid = form.partName && form.partNumber && form.quantity && form.scrapReason && form.scrapSource && form.scrapImages.length > 0;

  // 收货单号验证：当报损来源为"收货报损"且不是从收货页面进入时，需要输入收货单号
  const receiptOrderNumberValid = form.scrapSource === 'receipt' && (!receiptOrderNumber.value || receiptOrderNumber.value.length === 0)
    ? (form.receiptOrderNumberInput && form.receiptOrderNumberInput.trim() !== '')
    : true;

  const disabled = !(basicFieldsValid && receiptOrderNumberValid);

  console.log('=== isAddButtonDisabled check ===');
  console.log('partName:', form.partName);
  console.log('partNumber:', form.partNumber);
  console.log('quantity:', form.quantity);
  console.log('scrapReason:', form.scrapReason);
  console.log('scrapSource:', form.scrapSource);
  console.log('receiptOrderNumberInput:', form.receiptOrderNumberInput);
  console.log('scrapImagesLength:', form.scrapImages.length);
  console.log('basicFieldsValid:', basicFieldsValid);
  console.log('receiptOrderNumberValid:', receiptOrderNumberValid);
  console.log('disabled:', disabled);
  console.log('=== end check ===');
  return disabled;
});

const scrapItems = reactive<any[]>([]);
const selectedItems = ref<any[]>([]); // 用于存储表格选中项
const editingScrapOrderNumber = ref<string>(''); // 编辑模式下的报损单号

// 图片预览相关数据
const imagePreviewVisible = ref(false);
const currentPreviewImage = ref<string>('');
const currentImageIndex = ref<number>(0);
const imageScale = ref<number>(1);
const imageRotation = ref<number>(0);

// 零件选项数据
const partOptions = ref<OptionItem[]>([]);
const partNumberOptions = ref<OptionItem[]>([]);
const partsArchivesData = ref<any[]>([]);

// 加载零件档案数据
const loadPartsArchives = async () => {
  try {
    const response = await getPartArchives({ page: 1, pageSize: 1000 });
    partsArchivesData.value = response.list;
    
    // 生成选项数据
    const uniquePartNames = Array.from(new Set(response.list.map((item: any) => item.partName)));
    const uniquePartNumbers = Array.from(new Set(response.list.map((item: any) => item.partNumber)));
    
    partOptions.value = uniquePartNames.map((name: string) => ({ label: name, value: name }));
    partNumberOptions.value = uniquePartNumbers.map((number: string) => ({ label: number, value: number }));
  } catch (error) {
    console.error('Failed to load parts archives:', error);
    ElMessage.error(tc('loadDataFailed'));
  }
};

// 组件挂载时加载数据
loadPartsArchives();

// 监听零件名称变化，联动更新零件编号
watch(() => form.partName, (newVal) => {
  if (newVal) {
    const selectedPart = partsArchivesData.value.find(item => item.partName === newVal);
    if (selectedPart && form.partNumber !== selectedPart.partNumber) {
      form.partNumber = selectedPart.partNumber;
    }
  } else {
    form.partNumber = '';
  }
});

// 监听零件编号变化，联动更新零件名称
watch(() => form.partNumber, (newVal) => {
  if (newVal) {
    const selectedPart = partsArchivesData.value.find(item => item.partNumber === newVal);
    if (selectedPart && form.partName !== selectedPart.partName) {
      form.partName = selectedPart.partName;
    }
  } else {
    form.partName = '';
  }
});

// 监听编辑数据变化，初始化表单
watch(() => props.editData, async (newEditData) => {
  if (props.editMode && newEditData) {
    editingScrapOrderNumber.value = newEditData.scrapOrderNumber;

    // 获取报损单详情数据
    try {
      const orderDetails = await fetchScrapOrderDetails(newEditData.scrapOrderNumber);

      // 清空当前的报损项目列表
      scrapItems.length = 0;

      // 将报损单详情数据填充到表单中
      orderDetails.forEach((item: any) => {
        scrapItems.push({
          partName: item.partName,
          partNumber: item.partNumber,
          quantity: item.quantity,
          scrapReason: item.scrapReason,
          scrapSource: item.scrapSource,
          scrapImages: item.scrapImages ? item.scrapImages.map((url: string) => ({ url })) : []
        });
      });
    } catch (error) {
      console.error('Failed to load scrap order details:', error);
      ElMessage.error(t('common.loadDataFailed'));
    }
  }
}, { immediate: true });

// 图片上传相关函数
const handleImageChange = (file: UploadFile) => {
  // 验证文件
  if (!beforeImageUpload(file.raw!)) {
    return;
  }

  // 创建图片对象并添加到列表
  const imageObj = {
    ...file,
    url: URL.createObjectURL(file.raw!)
  };
  form.scrapImages.push(imageObj);
};

const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error(t('imageTypeError'));
    return false;
  }
  if (!isLt5M) {
    ElMessage.error(t('imageSizeError'));
    return false;
  }
  return true;
};

// 获取图片URL
const getImageUrl = (image: UploadFile | any) => {
  if (typeof image === 'string') {
    return image;
  }
  if (image && image.url) {
    return image.url;
  }
  if (image && image.raw) {
    return URL.createObjectURL(image.raw);
  }
  return '';
};

// 图片加载错误处理
const handleImageError = (event: Event) => {
  console.error('图片加载失败:', event);
  const img = event.target as HTMLImageElement;
  img.style.display = 'none';
};

// 删除图片
const removeImage = (index: number) => {
  ElMessageBox.confirm(
    '确定要删除这张图片吗？',
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 释放URL对象内存
    const imageToRemove = form.scrapImages[index];
    if (imageToRemove && imageToRemove.url && imageToRemove.url.startsWith('blob:')) {
      URL.revokeObjectURL(imageToRemove.url);
    }

    form.scrapImages.splice(index, 1);
    ElMessage.success('图片删除成功');
  }).catch(() => {
    // 用户取消删除
  });
};

// 删除表格中的图片
const removeImageFromItem = (itemIndex: number, imageIndex: number) => {
  ElMessageBox.confirm(
    '确定要删除这张图片吗？',
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const item = scrapItems.value[itemIndex];
    if (item && item.scrapImages && item.scrapImages.length > imageIndex) {
      // 释放URL对象内存
      const imageToRemove = item.scrapImages[imageIndex];
      if (imageToRemove && imageToRemove.url && imageToRemove.url.startsWith('blob:')) {
        URL.revokeObjectURL(imageToRemove.url);
      }

      item.scrapImages.splice(imageIndex, 1);
      ElMessage.success('图片删除成功');
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 预览图片
const previewImage = (index: number) => {
  if (form.scrapImages[index]) {
    currentImageIndex.value = index;
    currentPreviewImage.value = getImageUrl(form.scrapImages[index]);
    imagePreviewVisible.value = true;
  }
};

// 表格图片预览相关数据
const tableImagePreviewVisible = ref(false);
const currentTableImages = ref<any[]>([]);
const currentTableImageIndex = ref<number>(0);
const currentTablePreviewImage = ref<string>('');
const tableImageScale = ref<number>(1);
const tableImageRotation = ref<number>(0);

// 预览表格中的图片
const previewTableImage = (images: any[], index: number) => {
  if (images && images[index]) {
    currentTableImages.value = [...images];
    currentTableImageIndex.value = index;
    currentTablePreviewImage.value = getImageUrl(images[index]);
    tableImageScale.value = 1;
    tableImageRotation.value = 0;
    tableImagePreviewVisible.value = true;
  }
};

// 关闭图片预览
const closeImagePreview = () => {
  imagePreviewVisible.value = false;
  currentPreviewImage.value = '';
  currentImageIndex.value = 0;
  imageScale.value = 1;
  imageRotation.value = 0;
};

// 上一张图片
const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
    currentPreviewImage.value = getImageUrl(form.scrapImages[currentImageIndex.value]);
  }
};

// 下一张图片
const nextImage = () => {
  if (currentImageIndex.value < form.scrapImages.length - 1) {
    currentImageIndex.value++;
    currentPreviewImage.value = getImageUrl(form.scrapImages[currentImageIndex.value]);
  }
};

// 删除当前预览的图片
const removeCurrentImage = () => {
  ElMessageBox.confirm(
    '确定要删除当前图片吗？',
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 释放URL对象内存
    const imageToRemove = form.scrapImages[currentImageIndex.value];
    if (imageToRemove && imageToRemove.url && imageToRemove.url.startsWith('blob:')) {
      URL.revokeObjectURL(imageToRemove.url);
    }

    form.scrapImages.splice(currentImageIndex.value, 1);
    ElMessage.success('图片删除成功');

    // 如果删除后没有图片了，关闭预览
    if (form.scrapImages.length === 0) {
      closeImagePreview();
    } else {
      // 调整当前索引
      if (currentImageIndex.value >= form.scrapImages.length) {
        currentImageIndex.value = form.scrapImages.length - 1;
      }
      currentPreviewImage.value = getImageUrl(form.scrapImages[currentImageIndex.value]);
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 图片缩放和旋转操作
const zoomIn = () => {
  imageScale.value = Math.min(imageScale.value * 1.2, 3);
};

const zoomOut = () => {
  imageScale.value = Math.max(imageScale.value / 1.2, 0.5);
};

const rotateRight = () => {
  imageRotation.value = (imageRotation.value + 90) % 360;
};

const rotateLeft = () => {
  imageRotation.value = (imageRotation.value - 90 + 360) % 360;
};

// 图片加载完成处理
const handleImageLoad = () => {
  // 重置缩放和旋转
  imageScale.value = 1;
  imageRotation.value = 0;
};

// 表格图片预览相关方法
const closeTableImagePreview = () => {
  tableImagePreviewVisible.value = false;
  currentTablePreviewImage.value = '';
  currentTableImageIndex.value = 0;
  currentTableImages.value = [];
  tableImageScale.value = 1;
  tableImageRotation.value = 0;
};

const prevTableImage = () => {
  if (currentTableImageIndex.value > 0) {
    currentTableImageIndex.value--;
    currentTablePreviewImage.value = getImageUrl(currentTableImages.value[currentTableImageIndex.value]);
  }
};

const nextTableImage = () => {
  if (currentTableImageIndex.value < currentTableImages.value.length - 1) {
    currentTableImageIndex.value++;
    currentTablePreviewImage.value = getImageUrl(currentTableImages.value[currentTableImageIndex.value]);
  }
};

const zoomInTable = () => {
  tableImageScale.value = Math.min(tableImageScale.value * 1.2, 3);
};

const zoomOutTable = () => {
  tableImageScale.value = Math.max(tableImageScale.value / 1.2, 0.5);
};

const rotateRightTable = () => {
  tableImageRotation.value = (tableImageRotation.value + 90) % 360;
};

const rotateLeftTable = () => {
  tableImageRotation.value = (tableImageRotation.value - 90 + 360) % 360;
};

const handleTableImageLoad = () => {
  tableImageScale.value = 1;
  tableImageRotation.value = 0;
};

const handleAdd = async () => {
  console.log('handleAdd called');
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    console.log('Form validation result:', valid);
    if (valid) {
      const selectedPart = partsArchivesData.value.find(item => item.partName === form.partName);
      // 深拷贝图片数据，避免引用问题
      const imagesCopy = form.scrapImages.map(img => ({
        ...img,
        url: img.url || URL.createObjectURL(img.raw!)
      }));
      // 确定收货单号：优先使用传入的receiptOrderNumber，否则使用用户输入的
      const finalReceiptOrderNumber = receiptOrderNumber.value || form.receiptOrderNumberInput || '';

      scrapItems.push({
        ...form,
        receiptOrderNumber: finalReceiptOrderNumber,
        scrapImages: imagesCopy
      });
      // 保存当前的scrapSource，因为它是从收货页面传入的固定值
      const currentScrapSource = form.scrapSource;
      // 先重置表单字段
      formRef.value?.resetFields();
      // 然后手动设置字段值，确保scrapSource保持正确
      Object.assign(form, {
        partName: '',
        partNumber: '',
        quantity: 1,
        scrapReason: '',
        scrapSource: currentScrapSource, // 保持原有的scrapSource值
        receiptOrderNumberInput: currentScrapSource === 'receipt' && (!receiptOrderNumber.value || receiptOrderNumber.value.length === 0) ? '' : form.receiptOrderNumberInput,
        scrapImages: []
      });
    } else {
      ElMessage.warning(t('common.fillAllRequired'));
      return false;
    }
  });
};

const handleSelectionChange = (val: ScrapItem[]) => {
  selectedItems.value = val;
};

const handleDeleteItem = async (rowToDelete: ScrapItem) => {
  try {
    await ElMessageBox.confirm(
      tc('confirmDelete', { item: 1 + tc('item') }),
      tc('tip'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning',
      }
    );


    const index = scrapItems.findIndex(item => item === rowToDelete);
    if (index !== -1) {
      scrapItems.splice(index, 1);
    }
    selectedItems.value = selectedItems.value.filter(item => item !== rowToDelete); // 移除已删除的选中项
    ElMessage.success(tc('operationSuccessful'));
  } catch {
    ElMessage.info(tc('operationCanceled'));
  }
};

const submitForm = async () => {
  if (scrapItems.length === 0) {
    ElMessage.warning(t('addItemsPrompt'));
    return;
  }

  try {
    if (props.editMode && editingScrapOrderNumber.value) {
      // 编辑模式：根据原始状态决定操作

      if (props.editData?.status === 'rejected') {
        // 驳回状态：创建新的报损单
        const timestamp = Date.now();
        const scrapOrderNumber = `BS${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(timestamp).slice(-3)}`;

        scrapItems.forEach((item, index) => {
          const record: any = {
            id: `SR${timestamp}${index}${Math.random().toString(36).substring(2, 4)}`,
            scrapOrderNumber: scrapOrderNumber,
            partName: item.partName,
            partNumber: item.partNumber,
            quantity: item.quantity,
            scrapReason: item.scrapReason,
            scrapImages: item.scrapImages ? item.scrapImages.map((img: any) => img.url) : [],
            scrapDate: new Date().toISOString(),
            scrapSource: item.scrapSource,
            receiptTime: new Date().toISOString().slice(0, 10),
          };

          // 如果是收货报损，添加到货单号和收货单号
          if (item.scrapSource === 'receipt') {
            // 如果是从收货页面进入，使用传入的收货单号
            if (receiptOrderNumber.value && receiptOrderNumber.value.length > 0) {
              record.receiptOrderNumber = receiptOrderNumber.value;
              record.deliveryOrderNumber = `DH${timestamp.toString().slice(-6)}`;
            } else if (form.receiptOrderNumberInput) {
              // 如果是手动输入的收货单号
              record.receiptOrderNumber = form.receiptOrderNumberInput;
              record.deliveryOrderNumber = `DH${timestamp.toString().slice(-6)}`;
            }
          }

          addScrapRecord(record);
        });
        ElMessage.success(t('newScrapOrderCreated'));
      } else if (props.editData?.status === 'submitted') {
        // 已提交状态：更新现有记录
        scrapItems.forEach((item, index) => {
          updateScrapRecord(editingScrapOrderNumber.value, {
            partName: item.partName,
            partNumber: item.partNumber,
            quantity: item.quantity,
            scrapReason: item.scrapReason,
            scrapImages: item.scrapImages ? item.scrapImages.map((img: any) => img.url) : [],
            scrapSource: item.scrapSource,
          });
        });
        ElMessage.success(t('scrapOrderUpdated'));
      }
    } else {
      // 新建模式：创建新的报损单
      const timestamp = Date.now();
      const scrapOrderNumber = `BS${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(timestamp).slice(-3)}`;

      scrapItems.forEach((item, index) => {
        const record: any = {
          id: `SR${timestamp}${index}${Math.random().toString(36).substring(2, 4)}`,
          scrapOrderNumber: scrapOrderNumber,
          partName: item.partName,
          partNumber: item.partNumber,
          quantity: item.quantity,
          scrapReason: item.scrapReason,
          scrapImages: item.scrapImages ? item.scrapImages.map((img: any) => img.url) : [],
          scrapDate: new Date().toISOString(),
          scrapSource: item.scrapSource,
          receiptTime: new Date().toISOString().slice(0, 10),
        };

        // 如果是收货报损，添加到货单号和收货单号
        if (item.scrapSource === 'receipt') {
          // 如果是从收货页面进入，使用传入的收货单号
          if (receiptOrderNumber.value && receiptOrderNumber.value.length > 0) {
            record.receiptOrderNumber = receiptOrderNumber.value;
            record.deliveryOrderNumber = `DH${timestamp.toString().slice(-6)}`;
          } else if (form.receiptOrderNumberInput) {
            // 如果是手动输入的收货单号
            record.receiptOrderNumber = form.receiptOrderNumberInput;
            record.deliveryOrderNumber = `DH${timestamp.toString().slice(-6)}`;
          }
        }

        addScrapRecord(record);
      });
      ElMessage.success(t('common.submitSuccess'));
    }

    emit('submit-success');
  } catch (error) {
    console.error('Submit failed:', error);
    ElMessage.error(t('common.operationFailed'));
  }
};

const cancelForm = () => {
  emit('cancel');
};

watch(() => props.isVisible, (newVal) => {
  if (!newVal) {
    // Reset form and scrap items when dialog closes
    Object.assign(form, {
      partName: '',
      partNumber: '',
      quantity: 1,
      scrapReason: '',
      scrapSource: '',
      receiptOrderNumberInput: '',
      scrapImages: []
    });
    scrapItems.splice(0, scrapItems.length); // Clear the array
    selectedItems.value = []; // Clear selected items
    formRef.value?.resetFields();
  }
});

const getScrapSourceLabel = (source: string) => {
  switch (source) {
    case 'receipt':
      return t('sourceReceipt');
    case 'repair':
      return t('sourceRepair');
    case 'other':
      return t('sourceOther');
    default:
      return source;
  }
};
</script>

<style scoped>
.mt-4 {
  margin-top: 20px;
}

.upload-container {
  width: 100%;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

/* 自定义上传区域样式 */
.custom-upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  position: relative;
}

.image-wrapper {
  position: relative;
  width: 80px;
  height: 80px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.image-wrapper:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.image-wrapper:hover .image-actions {
  opacity: 1;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.action-icon {
  width: 20px;
  height: 20px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s;
}

.action-icon:hover {
  transform: scale(1.2);
}

.zoom-icon:hover {
  color: #409eff;
}

.delete-icon:hover {
  color: #f56c6c;
}

.upload-button {
  width: 80px;
  height: 80px;
}

.upload-area {
  width: 100%;
  height: 100%;
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background: #fafafa;
}

.upload-area:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-icon {
  font-size: 20px;
  color: #8c939d;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
  color: #8c939d;
}

/* 图片预览对话框样式 */
.image-preview-dialog {
  --el-dialog-bg-color: #000;
}

.image-preview-dialog .el-dialog__header {
  background: #000;
  color: #fff;
  border-bottom: 1px solid #333;
}

.image-preview-dialog .el-dialog__body {
  background: #000;
  padding: 0;
}

.image-preview-dialog .el-dialog__footer {
  background: #000;
  border-top: 1px solid #333;
}

.image-preview-container {
  position: relative;
  width: 100%;
  height: 70vh;
  background: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.image-viewer-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-display-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.preview-large-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: grab;
  user-select: none;
}

.preview-large-image:active {
  cursor: grabbing;
}

.image-navigation {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.nav-button {
  pointer-events: all;
  background: rgba(0, 0, 0, 0.6) !important;
  border: none !important;
  color: #fff !important;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: all 0.3s;
}

.nav-button:hover {
  background: rgba(0, 0, 0, 0.8) !important;
  transform: scale(1.1);
}

.nav-button:disabled {
  background: rgba(0, 0, 0, 0.3) !important;
  color: rgba(255, 255, 255, 0.3) !important;
  cursor: not-allowed;
  transform: none;
}

.image-info-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-counter {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transition: all 0.3s;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  transform: scale(1.1);
}

.delete-button:hover {
  background: rgba(245, 108, 108, 0.8) !important;
  border-color: #f56c6c !important;
}

.preview-dialog-footer {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.preview-dialog-footer .el-button {
  background: #333 !important;
  border-color: #555 !important;
  color: #fff !important;
}

.preview-dialog-footer .el-button:hover {
  background: #555 !important;
  border-color: #777 !important;
}

/* 表格中的图片容器 */
.table-image-item {
  position: relative;
  display: inline-block;
  margin-right: 8px;
}

.table-image-delete {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  background: #f56c6c;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  z-index: 10;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.table-image-delete:hover {
  background: #e53e3e;
  transform: scale(1.1);
}

.image-preview {
  display: flex;
  align-items: center;
  gap: 4px;
}

.preview-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
}

.more-images {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.no-image {
  color: #999;
  font-size: 12px;
}
</style>
