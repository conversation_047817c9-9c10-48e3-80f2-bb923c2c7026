<template>
  <div class="scrap-record-list">
    <el-form :inline="true" :model="queryForm" class="demo-form-inline mb-4">
      <el-form-item :label="t('scrapOrderNumber')">
        <el-input v-model="queryForm.scrapOrderNumber" :placeholder="t('scrapOrderNumberPlaceholder')" style="width: 220px;"></el-input>
      </el-form-item>
      <el-form-item :label="t('partName')">
        <el-select
          v-model="queryForm.partName"
          :placeholder="t('partNamePlaceholder')"
          style="width: 220px;"
          filterable
          clearable
          @change="handlePartNameChange"
        >
          <el-option
            v-for="item in partNameOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('partNumber')">
        <el-select
          v-model="queryForm.partNumber"
          :placeholder="t('partNumberPlaceholder')"
          style="width: 220px;"
          filterable
          clearable
          @change="handlePartNumberChange"
        >
          <el-option
            v-for="item in partNumberOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('scrapDate')">
        <el-date-picker
          v-model="queryForm.scrapDateRange"
          type="daterange"
          :range-separator="tc('to')"
          :start-placeholder="tc('startDate')"
          :end-placeholder="tc('endDate')"
          style="width: 220px;"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="t('scrapSource')">
        <el-select v-model="queryForm.scrapSource" :placeholder="t('selectScrapSource')" style="width: 220px;">
          <el-option :label="t('sourceReceipt')" value="receipt"></el-option>
          <el-option :label="t('sourceRepair')" value="repair"></el-option>
          <el-option :label="t('sourceOther')" value="other"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="t('status')">
        <el-select v-model="queryForm.status" :placeholder="t('selectStatus')" style="width: 220px;">
          <el-option :label="t('statusSubmitted')" value="submitted"></el-option>
          <el-option :label="t('statusApproved')" value="approved"></el-option>
          <el-option :label="t('statusRejected')" value="rejected"></el-option>
          <el-option :label="t('statusVoided')" value="voided"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="form-buttons-center mb-4">
      <el-button type="primary" @click="onSubmit">{{ tc('query') }}</el-button>
      <el-button @click="onReset">{{ tc('reset') }}</el-button>
    </div>

    <el-table :data="tableData" style="width: 100%" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" :selectable="isRowSelectable"></el-table-column>
      <el-table-column prop="scrapOrderNumber" :label="t('scrapOrderNumber')" min-width="120"></el-table-column>
      <el-table-column :label="t('itemCount')" min-width="100">
        <template #default="scope">
          {{ scope.row.itemCount }}
        </template>
      </el-table-column>
      <el-table-column :label="t('totalQuantity')" min-width="100">
        <template #default="scope">
          {{ scope.row.totalQuantity }}
        </template>
      </el-table-column>
      <el-table-column :label="t('scrapDate')" min-width="120">
        <template #default="scope">
          {{ formatDate(scope.row.scrapDate) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('scrapSource')" min-width="100">
        <template #default="scope">
          {{ getScrapSourceLabel(scope.row.scrapSource) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('status')" min-width="120">
        <template #default="scope">
          {{ getStatusLabel(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column :label="tc('operations')" width="200">
        <template #default="scope">
          <el-button link type="primary" size="small" @click="showScrapOrderDetails(scope.row)">{{ $t('common.details') }}</el-button>
          <el-button
            link
            type="primary"
            size="small"
            :disabled="scope.row.status !== 'submitted' && scope.row.status !== 'rejected'"
            @click="editScrapOrder(scope.row)"
          >
            {{ $t('common.edit') }}
          </el-button>
          <el-button
            v-if="scope.row.status !== 'voided'"
            link
            type="danger"
            size="small"
            :disabled="!isVoidButtonEnabled(scope.row)"
            @click="voidScrapOrder(scope.row)"
          >
            {{ $t('common.void') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :small="false"
      :disabled="false"
      :background="true"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <el-dialog
      v-model="reasonDialogVisible"
      :title="t('scrapReasonDetail')"
      width="60%"
      :modal="false"
    >
      <div class="scrap-reason-content">
        <!-- 报损原因文字描述 -->
        <div class="reason-text">
          <h4>{{ t('scrapReasonText') }}</h4>
          <p>{{ currentScrapReason }}</p>
        </div>

        <!-- 报损图片 -->
        <div v-if="currentScrapImages.length > 0" class="reason-images">
          <h4>{{ t('scrapImages') }}</h4>
          <div class="image-gallery">
            <el-image
              v-for="(image, index) in currentScrapImages"
              :key="index"
              :src="image"
              :preview-src-list="currentScrapImages"
              :initial-index="index"
              fit="cover"
              class="scrap-image"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reasonDialogVisible = false">{{ $t('common.close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 报损单详情对话框 -->
    <el-dialog
      v-model="detailsDialogVisible"
      :title="t('orderDetails')"
      width="80%"
      :modal="false"
    >
      <el-table :data="currentOrderDetails" border>
        <el-table-column prop="partName" :label="t('partName')" min-width="120"></el-table-column>
        <el-table-column prop="partNumber" :label="t('partNumber')" min-width="120"></el-table-column>
        <el-table-column prop="quantity" :label="t('scrapQuantity')" min-width="100"></el-table-column>
        <el-table-column :label="t('scrapSource')" min-width="100">
          <template #default="scope">
            {{ getScrapSourceLabel(scope.row.scrapSource) }}
          </template>
        </el-table-column>
        <!-- 到货单号列 - 仅当报损来源为收货时显示 -->
        <el-table-column
          v-if="hasReceiptScrapSource"
          prop="deliveryOrderNumber"
          :label="t('deliveryOrderNumber')"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.deliveryOrderNumber || '-' }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operations')" width="120">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="showScrapReason(scope.row)">{{ $t('common.details') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 驳回原因显示 - 仅在驳回状态时显示 -->
      <div v-if="currentOrderStatus === 'rejected'" class="rejection-reason-section">
        <h4>驳回原因</h4>
        <div class="rejection-reason-content">
          {{ currentRejectionReason }}
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailsDialogVisible = false">{{ $t('common.close') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getPartArchives } from '@/api/modules/parts/archives';
import { getScrapRecordsListAPI, voidScrapRecord } from '@/api/modules/parts/management';

// 类型定义
interface ScrapRecord {
  id: string;
  scrapOrderNumber: string;
  partName: string;
  partNumber: string;
  quantity: number;
  itemCount: number;
  totalQuantity: number;
  scrapDate: string;
  scrapSource: string;
  status: string;
  rejectionReason?: string;
}

interface OptionItem {
  label: string;
  value: string;
}

// 国际化设置
const { t, tc } = useModuleI18n('parts.scrapRecordForm');

const emit = defineEmits(['close', 'edit-scrap-order']);

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
});

const queryForm = reactive({
  scrapOrderNumber: '',
  partName: '',
  partNumber: '',
  scrapDateRange: [],
  scrapSource: '',
  status: '',
});

const tableData = ref<ScrapRecord[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const reasonDialogVisible = ref(false);
const currentScrapReason = ref('');
const currentScrapImages = ref<string[]>([]);
const detailsDialogVisible = ref(false);
const currentOrderDetails = ref<any[]>([]);
const currentOrderStatus = ref(''); // 当前报损单状态
const currentRejectionReason = ref(''); // 当前驳回原因
const selectedRows = ref<any[]>([]); // 选中的行

// 零件选项数据
const partNameOptions = ref<OptionItem[]>([]);
const partNumberOptions = ref<OptionItem[]>([]);
const partsArchivesData = ref<any[]>([]);

// 加载零件档案数据
const loadPartsArchives = async () => {
  try {
    const response = await getPartArchives({ page: 1, pageSize: 1000 });
    partsArchivesData.value = response.list;
    
    // 生成选项数据
    const uniquePartNames = Array.from(new Set(response.list.map((item: any) => item.partName)));
    const uniquePartNumbers = Array.from(new Set(response.list.map((item: any) => item.partNumber)));
    
    partNameOptions.value = uniquePartNames.map((name: string) => ({ label: name, value: name }));
    partNumberOptions.value = uniquePartNumbers.map((number: string) => ({ label: number, value: number }));
  } catch (error) {
    console.error('Failed to load parts archives:', error);
    ElMessage.error(tc('loadDataFailed'));
  }
};

// 计算属性：判断当前报损单详情中是否包含收货报损的数据
const hasReceiptScrapSource = computed(() => {
  return currentOrderDetails.value.some(item => item.scrapSource === 'receipt');
});

onMounted(() => {
  loadPartsArchives();
  fetchData();
});

// 监听 isVisible 变化，在弹窗显示时刷新数据
watch(() => props.isVisible, (newVal) => {
  if (newVal) {
    onReset(); // Reset query form when dialog opens
    fetchData();
  }
});

const fetchData = async () => {
  try {
    const response = await getScrapRecordsListAPI({
      page: currentPage.value,
      pageSize: pageSize.value,
      ...queryForm
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    console.error('Failed to fetch scrap records:', error);
    ElMessage.error(tc('loadDataFailed'));
  }
};

const onSubmit = () => {
  currentPage.value = 1;
  fetchData();
};

const onReset = () => {
  Object.assign(queryForm, {
    scrapOrderNumber: '',
    partName: '',
    partNumber: '',
    scrapDateRange: [],
    scrapSource: '',
    status: '',
  });
  currentPage.value = 1;
  fetchData();
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  fetchData();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  fetchData();
};

// 零件名称变化处理
const handlePartNameChange = (value: string) => {
  if (value) {
    const selectedPart = partsArchivesData.value.find(item => item.partName === value);
    if (selectedPart && queryForm.partNumber !== selectedPart.partNumber) {
      queryForm.partNumber = selectedPart.partNumber;
    }
  } else {
    queryForm.partNumber = '';
  }
};

// 零件编号变化处理
const handlePartNumberChange = (value: string) => {
  if (value) {
    const selectedPart = partsArchivesData.value.find(item => item.partNumber === value);
    if (selectedPart && queryForm.partName !== selectedPart.partName) {
      queryForm.partName = selectedPart.partName;
    }
  } else {
    queryForm.partName = '';
  }
};

const showScrapOrderDetails = (row: any) => {
  // TODO: 实现获取报损单详情API
  currentOrderDetails.value = [];
  currentOrderStatus.value = row.status;
  currentRejectionReason.value = row.rejectionReason || ''; // 设置驳回原因
  detailsDialogVisible.value = true;
};

const showScrapReason = (row: any) => {
  currentScrapReason.value = row.scrapReason;
  currentScrapImages.value = row.scrapImages || [];
  reasonDialogVisible.value = true;
};

// 处理表格行选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

// 判断行是否可以选择（只有已提交状态的可以选择）
const isRowSelectable = (row: any) => {
  return row.status === 'submitted';
};

// 判断作废按钮是否可用（必须选中且为已提交状态）
const isVoidButtonEnabled = (row: any) => {
  return row.status === 'submitted' && selectedRows.value.some(selectedRow => selectedRow.scrapOrderNumber === row.scrapOrderNumber);
};

const editScrapOrder = (row: any) => {
  // 触发编辑事件，传递报损单数据给父组件
  emit('edit-scrap-order', row);
};

const voidScrapOrder = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('confirmVoidOrder', { orderNumber: row.scrapOrderNumber }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning',
      }
    );

    const result = await voidScrapRecord(row.scrapOrderNumber);
    if (result.success) {
      ElMessage.success(t('voidOrderSuccess'));
      fetchData(); // 刷新数据
    } else {
      ElMessage.error(t('voidOrderFailed'));
    }
  } catch {
    // 用户取消操作
  }
};

const closeDialog = () => {
  emit('close');
};

const getScrapSourceLabel = (source: string) => {
  switch (source) {
    case 'receipt':
      return t('sourceReceipt');
    case 'repair':
      return t('sourceRepair');
    case 'other':
      return t('sourceOther');
    default:
      return source;
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'submitted':
      return t('statusSubmitted');
    case 'approved':
      return t('statusApproved');
    case 'rejected':
      return t('statusRejected');
    case 'voided':
      return t('statusVoided');
    default:
      return status;
  }
};

// Helper function to format date
const formatDate = (dateString: string | Date) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};
</script>

<style scoped>
.mb-4 {
  margin-bottom: 20px;
}

.form-buttons-center {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 20px;
}

.scrap-reason-content {
  max-height: 500px;
  overflow-y: auto;
}

.reason-text {
  margin-bottom: 20px;
}

.reason-text h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.reason-text p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  word-wrap: break-word;
}

.reason-images h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.scrap-image {
  width: 120px;
  height: 120px;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
}

.scrap-image:hover {
  border-color: #409eff;
}

/* 表格中的图片预览样式 */
.image-preview-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-thumbnails {
  display: flex;
  align-items: center;
  gap: 4px;
}

.thumbnail-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.thumbnail-image:hover {
  border-color: #409eff;
  transform: scale(1.1);
}

.more-images {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
  white-space: nowrap;
}

.no-image {
  color: #999;
  font-size: 12px;
}

/* 驳回原因样式 */
.rejection-reason-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}

.rejection-reason-section h4 {
  margin: 0 0 8px 0;
  color: #f56c6c;
  font-size: 14px;
  font-weight: 600;
}

.rejection-reason-content {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
}
</style>
