<template>
  <div class="create-purchase-page">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ t('actions.create') }}</span>
          <el-button @click="handleBack">
            <el-icon><ArrowLeft /></el-icon>
            {{ tc('common.back') }}
          </el-button>
        </div>
      </template>

      <DealerOrderForm
        :loading="loading"
        @submit="handleSubmit"
        @save-draft="handleSaveDraft"
        @cancel="handleBack"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { purchaseDealerApi } from '@/api/modules/parts/purchase-dealer'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { PurchaseOrderForm } from '@/types/parts/purchase-dealer'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import DealerOrderForm from './components/DealerOrderForm.vue'

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer')
const { t: tc } = useI18n()

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)

// 返回列表
const handleBack = () => {
  router.back()
}

// 保存草稿
const handleSaveDraft = async (formData: PurchaseOrderForm) => {
  try {
    loading.value = true
    
    const createData = {
      warehouseId: formData.warehouseId,
      expectedDeliveryDate: formData.expectedDeliveryDate,
      remark: formData.remarks,
      items: formData.items.map(item => ({
        partId: item.partId,
        partCode: item.partCode,
        partName: item.partName,
        partType: item.partType,
        unit: item.unit,
        unitPrice: item.unitPrice,
        quantity: item.quantity
      }))
    }
    
    await purchaseDealerApi.createOrder(createData)
    
    ElMessage.success(t('messages.saveSuccess'))
    router.push('/parts/purchase/dealer')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 提交审核
const handleSubmit = async (formData: PurchaseOrderForm) => {
  try {
    loading.value = true
    
    const createData = {
      warehouseId: formData.warehouseId,
      expectedDeliveryDate: formData.expectedDeliveryDate,
      remark: formData.remarks,
      items: formData.items.map(item => ({
        partId: item.partId,
        partCode: item.partCode,
        partName: item.partName,
        partType: item.partType,
        unit: item.unit,
        unitPrice: item.unitPrice,
        quantity: item.quantity
      }))
    }
    
    // 先创建订单
    const order = await purchaseDealerApi.createOrder(createData)
    
    // 然后提交审核（可能返回多个订单）
    const submitResult = await purchaseDealerApi.submitForApproval(order.id)
    
    // 显示成功消息
    ElMessage.success(submitResult.message)
    
    // 如果生成了多个订单，显示详细信息
    if (submitResult.data.length > 1) {
      const orderNumbers = submitResult.data.map(o => o.orderNo).join(', ')
      ElMessage.info(`已生成订单：${orderNumbers}`)
    }
    
    router.push('/parts/purchase/dealer')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.create-purchase-page {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}
</style>