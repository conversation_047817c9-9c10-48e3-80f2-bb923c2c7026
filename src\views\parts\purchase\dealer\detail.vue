<template>
  <div class="detail-purchase-page">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ getPageTitle() }}</span>
          <el-button @click="handleBack">
            <el-icon><ArrowLeft /></el-icon>
            {{ tc('common.back') }}
          </el-button>
        </div>
      </template>

      <DealerOrderForm
        v-if="orderData"
        :loading="loading"
        :order-data="orderData"
        :edit-mode="isEditable"
        :readonly="!isEditable"
        @submit="handleSubmit"
        @save-draft="handleSaveDraft"
        @cancel="handleBack"
      />
      
      <!-- 操作按钮区域 -->
      <div v-if="orderData" class="action-section">
        <el-divider />
        <div class="action-buttons">
          <!-- 草稿状态：编辑、提交、删除 -->
          <template v-if="orderData.status === 'draft'">
            <el-button @click="handleEdit">
              <el-icon><Edit /></el-icon>
              {{ t('actions.edit') }}
            </el-button>
            <el-button type="primary" @click="handleSubmitForApproval">
              <el-icon><Check /></el-icon>
              {{ t('actions.submit') }}
            </el-button>
            <el-button type="danger" @click="handleDelete">
              <el-icon><Delete /></el-icon>
              {{ t('actions.delete') }}
            </el-button>
          </template>
          
          <!-- 驳回状态：隐藏所有按钮 -->
          <template v-else-if="orderData.status === 'rejected'">
            <!-- 驳回状态不显示任何操作按钮 -->
          </template>
          
          <!-- 已提交状态：取消 -->
          <template v-else-if="orderData.status === 'submitted'">
            <el-button type="warning" @click="handleCancel">
              <el-icon><Close /></el-icon>
              {{ t('actions.cancel') }}
            </el-button>
          </template>
          
          <!-- 已发货/部分发货状态：收货 -->
          <template v-else-if="['shipped', 'partiallyShipped'].includes(orderData.status)">
            <el-button type="success" @click="handleReceipt">
              <el-icon><Box /></el-icon>
              {{ t('actions.receipt') }}
            </el-button>
          </template>
          
          <!-- 其他状态：只能查看，无操作按钮 -->
        </div>
      </div>
    </el-card>

    <!-- 收货弹窗 -->
    <DealerReceiptModal
      v-model="receiptDialogVisible"
      :order-id="orderId"
      @success="handleReceiptSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { purchaseDealerApi } from '@/api/modules/parts/purchase-dealer'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { PurchaseOrder, PurchaseOrderForm } from '@/types/parts/purchase-dealer'
import { ArrowLeft, Box, Check, Close, Delete, Edit, RefreshRight } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import DealerOrderForm from './components/DealerOrderForm.vue'
import DealerReceiptModal from './components/DealerReceiptModal.vue'

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer')
const { t: tc } = useI18n()

// 路由
const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const orderData = ref<PurchaseOrder | null>(null)
const receiptDialogVisible = ref(false)

// 获取订单ID
const orderId = route.params.id as string

// 计算属性：是否可编辑
const isEditable = computed(() => {
  if (!orderData.value) return false
  return ['draft', 'rejected'].includes(orderData.value.status)
})

// 获取页面标题
const getPageTitle = (): string => {
  if (!orderData.value) return t('actions.detail')
  
  if (isEditable.value) {
    return orderData.value.status === 'draft' ? '编辑叫料单' : '修改叫料单'
  }
  return '叫料单详情'
}

// 加载订单数据
const loadOrderData = async () => {
  try {
    loading.value = true
    orderData.value = await purchaseDealerApi.getOrderDetail(orderId)
  } catch (error) {
    console.error('加载订单数据失败:', error)
    ElMessage.error('加载订单数据失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 返回列表
const handleBack = () => {
  router.push('/parts/purchase/dealer')
}

// 编辑订单
const handleEdit = () => {
  router.push(`/parts/purchase/dealer/${orderId}/edit`)
}

// 保存草稿（仅在可编辑模式下）
const handleSaveDraft = async (formData: PurchaseOrderForm) => {
  if (!isEditable.value) return
  
  try {
    loading.value = true
    
    const updateData = {
      warehouseId: formData.warehouseId,
      expectedDeliveryDate: formData.expectedDeliveryDate,
      remark: formData.remarks,
      items: formData.items.map(item => ({
        id: item.partId,
        partId: item.partId,
        partCode: item.partCode,
        partName: item.partName,
        partType: item.partType,
        unit: item.unit,
        unitPrice: item.unitPrice,
        quantity: item.quantity
      }))
    }
    
    await purchaseDealerApi.updateOrder(orderId, updateData)
    ElMessage.success(t('messages.saveSuccess'))
    
    // 重新加载数据
    await loadOrderData()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 提交表单（仅在可编辑模式下）
const handleSubmit = async (formData: PurchaseOrderForm) => {
  if (!isEditable.value) return
  
  try {
    // 先保存
    await handleSaveDraft(formData)
    
    // 再提交审核
    await handleSubmitForApproval()
  } catch (error) {
    console.error('提交失败:', error)
  }
}

// 提交审核
const handleSubmitForApproval = async () => {
  if (!orderData.value) return
  
  try {
    await ElMessageBox.confirm(
      t('messages.confirmSubmit'),
      '确认',
      {
        type: 'warning'
      }
    )
    
    const result = await purchaseDealerApi.submitForApproval(orderId)
    ElMessage.success(result.message)
    
    // 如果拆分了订单，显示额外信息
    if (result.data.length > 1) {
      const orderNumbers = result.data.map(o => o.orderNo).join(', ')
      ElMessage.info(`已生成订单：${orderNumbers}`)
    }
    
    // 重新加载数据或返回列表
    router.push('/parts/purchase/dealer')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
}

// 取消订单
const handleCancel = async () => {
  if (!orderData.value) return
  
  try {
    await ElMessageBox.confirm(
      t('messages.confirmCancel'),
      '确认',
      {
        type: 'warning'
      }
    )
    
    await purchaseDealerApi.cancelOrder(orderId)
    ElMessage.success('取消成功')
    
    // 重新加载数据
    await loadOrderData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消失败:', error)
      ElMessage.error('取消失败')
    }
  }
}

// 删除订单
const handleDelete = async () => {
  if (!orderData.value) return
  
  try {
    await ElMessageBox.confirm(
      t('messages.confirmDelete'),
      '确认',
      {
        type: 'warning'
      }
    )
    
    await purchaseDealerApi.deleteOrder(orderId)
    ElMessage.success('删除成功')
    
    // 返回列表
    router.push('/parts/purchase/dealer')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 收货
const handleReceipt = () => {
  receiptDialogVisible.value = true
}

// 收货成功回调
const handleReceiptSuccess = () => {
  loadOrderData()
}

// 初始化
onMounted(() => {
  loadOrderData()
})
</script>

<style scoped lang="scss">
.detail-purchase-page {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  .action-section {
    margin-top: 20px;
    
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 16px;
      flex-wrap: wrap;
    }
  }
}
</style> 