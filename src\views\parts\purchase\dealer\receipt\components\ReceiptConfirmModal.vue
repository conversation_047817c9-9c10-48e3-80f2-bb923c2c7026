<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('confirmReceipt')"
    width="90%"
    :before-close="handleClose"
    append-to-body
    destroy-on-close
  >
    <div class="receipt-confirm" v-loading="loading">
      <!-- 发货单选择 -->
      <div class="confirm-section">
        <h4 class="section-title">{{ t('confirm.selectShipment') }}</h4>
        <div class="shipment-selection">
          <el-radio-group v-model="selectedShipmentId" @change="handleShipmentChange">
            <div 
              v-for="shipment in availableShipments" 
              :key="shipment.id"
              class="shipment-option"
            >
              <el-radio :label="shipment.id" class="shipment-radio">
                <div class="shipment-info">
                  <div class="shipment-header">
                    <span class="shipment-no">{{ shipment.shipmentNo }}</span>
                    <el-tag size="small" type="primary">{{ shipment.carrierName }}</el-tag>
                  </div>
                  <div class="shipment-details">
                    <span>发货日期：{{ formatDate(shipment.shippingDate) }}</span>
                    <span>物流单号：{{ shipment.trackingNumber }}</span>
                    <span>货物数量：{{ shipment.totalItems }} 项</span>
                  </div>
                </div>
              </el-radio>
            </div>
          </el-radio-group>
          
          <div v-if="availableShipments.length === 0" class="no-shipments">
            <el-empty :description="t('confirm.noShipments')" />
          </div>
        </div>
      </div>

      <!-- 收货明细表格 -->
      <div v-if="selectedShipmentId && receiptItems.length > 0" class="confirm-section">
        <div class="section-header">
          <h4 class="section-title">{{ t('confirm.receiptDetails') }}</h4>
          <div class="batch-actions">
            <el-button 
              type="text" 
              @click="handleBatchReceiveAll"
              size="small"
            >
              {{ t('confirm.receiveAll') }}
            </el-button>
            <el-button 
              type="text" 
              @click="handleBatchSetNormal"
              size="small"
            >
              {{ t('confirm.setAllNormal') }}
            </el-button>
          </div>
        </div>

        <el-table 
          :data="receiptItems" 
          border
          stripe
          class="receipt-table"
          :row-class-name="getRowClassName"
        >
          <el-table-column 
            type="index" 
            :label="tc('index')" 
            width="60" 
            align="center"
          />
          
          <el-table-column 
            prop="partCode" 
            :label="t('detail.partCode')" 
            min-width="120"
          />
          
          <el-table-column 
            prop="partName" 
            :label="t('detail.partName')" 
            min-width="150"
            show-overflow-tooltip
          />
          
          <el-table-column 
            prop="unit" 
            :label="t('detail.unit')" 
            width="80" 
            align="center"
          />
          
          <el-table-column 
            prop="orderedQuantity" 
            :label="t('detail.orderedQuantity')" 
            width="100" 
            align="center"
          />
          
          <el-table-column 
            prop="shippedQuantity" 
            :label="t('detail.shippedQuantity')" 
            width="100" 
            align="center"
          />
          
          <el-table-column 
            :label="t('confirm.actualReceiptQuantity')" 
            width="120" 
            align="center"
          >
            <template #default="{ row, $index }">
              <el-input-number
                v-model="row.actualReceiptQuantity"
                :min="0"
                :max="row.shippedQuantity * 2"
                size="small"
                controls-position="right"
                @change="handleQuantityChange(row, $index)"
              />
            </template>
          </el-table-column>
          
          <el-table-column 
            :label="t('confirm.difference')" 
            width="80" 
            align="center"
          >
            <template #default="{ row }">
              <span 
                :class="{
                  'diff-normal': getDifference(row) === 0,
                  'diff-shortage': getDifference(row) < 0,
                  'diff-excess': getDifference(row) > 0
                }"
              >
                {{ getDifference(row) > 0 ? `+${getDifference(row)}` : getDifference(row) }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column 
            :label="t('confirm.receiptStatus')" 
            width="120" 
            align="center"
          >
            <template #default="{ row, $index }">
              <el-select
                v-model="row.status"
                size="small"
                @change="handleStatusChange(row, $index)"
              >
                <el-option :label="t('status.NORMAL')" value="NORMAL" />
                <el-option :label="t('status.SHORTAGE')" value="SHORTAGE" />
                <el-option :label="t('status.DAMAGE')" value="DAMAGE" />
                <el-option :label="t('status.REJECTED')" value="REJECTED" />
              </el-select>
            </template>
          </el-table-column>
          
          <el-table-column 
            :label="t('confirm.abnormalType')" 
            width="120" 
            align="center"
          >
            <template #default="{ row }">
              <el-select
                v-model="row.abnormalType"
                size="small"
                :disabled="row.status === 'NORMAL'"
                :placeholder="tc('pleaseSelect')"
                clearable
              >
                <el-option 
                  v-for="option in abnormalTypeOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </template>
          </el-table-column>
          
          <el-table-column 
            :label="t('confirm.abnormalReason')" 
            width="150"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.abnormalReason"
                size="small"
                :disabled="row.status === 'NORMAL'"
                :placeholder="t('confirm.enterAbnormalReason')"
                maxlength="100"
                show-word-limit
              />
            </template>
          </el-table-column>
          
          <el-table-column 
            :label="t('confirm.storageLocation')" 
            width="120"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.locationName"
                size="small"
                :placeholder="t('confirm.storageLocation')"
                maxlength="50"
              />
            </template>
          </el-table-column>
          
          <el-table-column 
            :label="t('confirm.batchNo')" 
            width="120"
          >
            <template #default="{ row }">
              <el-input
                v-model="row.batchNo"
                size="small"
                :placeholder="t('confirm.batchNo')"
                maxlength="50"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 入库信息表单 -->
      <div v-if="selectedShipmentId" class="confirm-section">
        <h4 class="section-title">{{ t('confirm.warehouseInfo') }}</h4>
        <el-form 
          :model="receiptForm" 
          :rules="formRules"
          ref="formRef"
          label-width="120px"
          class="receipt-form"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('confirm.receiptDate')" prop="receiptDate">
                <el-date-picker
                  v-model="receiptForm.receiptDate"
                  type="date"
                  :placeholder="t('confirm.selectReceiptDate')"
                  style="width: 100%"
                  :disabled-date="disabledDate"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('confirm.signer')" prop="handler">
                <el-input
                  v-model="receiptForm.handler"
                  :placeholder="t('confirm.enterSignerName')"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('confirm.warehouse')" prop="warehouseId">
                <el-input
                  :value="currentWarehouseName"
                  disabled
                  :placeholder="t('confirm.warehouse')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :label="t('confirm.receiptRemark')">
                <el-input
                  v-model="receiptForm.remark"
                  type="textarea"
                  :rows="3"
                  :placeholder="t('confirm.enterReceiptRemark')"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 汇总信息 -->
      <div v-if="selectedShipmentId && receiptItems.length > 0" class="confirm-section">
        <h4 class="section-title">{{ t('confirm.receiptSummary') }}</h4>
        <div class="summary-grid">
          <div class="summary-item">
            <label>{{ t('confirm.totalItems') }}：</label>
            <span>{{ receiptItems.length }}</span>
          </div>
          <div class="summary-item">
            <label>{{ t('confirm.normalItems') }}：</label>
            <span class="normal">{{ normalCount }}</span>
          </div>
          <div class="summary-item">
            <label>{{ t('confirm.abnormalItems') }}：</label>
            <span class="abnormal">{{ abnormalCount }}</span>
          </div>
          <div class="summary-item">
            <label>{{ t('confirm.totalReceiptQuantity') }}：</label>
            <span>{{ totalReceiptQuantity }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirmReceipt"
          :loading="confirmLoading"
          :disabled="!canConfirm"
        >
          {{ t('list.confirmReceipt') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { receiptApi } from '@/api/modules/parts/receipt';
import type { 
  ReceiptOrder,
  ReceiptOrderItem, 
  ReceiptConfirmRequest,
  ReceiptStatus,
  ReceiptAbnormalType
} from '@/types/parts/purchase-dealer';

// Props
interface Props {
  modelValue: boolean;
  receiptOrderId: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [receiptOrder: ReceiptOrder];
}>();

// 国际化
const { t } = useModuleI18n('parts.purchase.receipt');
const { t: tc } = useModuleI18n('common');

// 响应式数据
const loading = ref(false);
const confirmLoading = ref(false);
const formRef = ref<FormInstance>();

// 发货单相关数据 (模拟数据，实际应该从API获取)
const availableShipments = ref([
  {
    id: 'ship_001',
    shipmentNo: 'SF2024010801',
    carrierName: '顺丰快递',
    shippingDate: '2024-01-08',
    trackingNumber: 'SF1234567890',
    totalItems: 5
  }
]);

const selectedShipmentId = ref('');
const receiptItems = ref<ReceiptOrderItem[]>([]);
const abnormalTypeOptions = ref<Array<{ code: string; name: string }>>([]);

// 表单数据
const receiptForm = reactive({
  receiptDate: new Date().toISOString().slice(0, 10),
  handler: '',
  warehouseId: '',
  remark: ''
});

// 表单验证规则
const formRules: FormRules = {
  receiptDate: [
    { required: true, message: t('confirm.selectReceiptDateRequired'), trigger: 'change' }
  ],
  handler: [
    { required: true, message: t('confirm.enterSignerRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('confirm.signerLengthValidation'), trigger: 'blur' }
  ]
};

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const currentWarehouseName = computed(() => '主仓库'); // 从上下文获取

const normalCount = computed(() => {
  return receiptItems.value.filter(item => item.status === 'NORMAL').length;
});

const abnormalCount = computed(() => {
  return receiptItems.value.filter(item => item.status !== 'NORMAL').length;
});

const totalReceiptQuantity = computed(() => {
  return receiptItems.value.reduce((total, item) => {
    return total + (item.actualReceiptQuantity || 0);
  }, 0);
});

const canConfirm = computed(() => {
  return selectedShipmentId.value && 
         receiptItems.value.length > 0 && 
         receiptForm.receiptDate && 
         receiptForm.handler;
});

/**
 * 初始化数据
 */
const initializeData = async () => {
  try {
    loading.value = true;
    
    // 获取异常类型选项
    abnormalTypeOptions.value = await receiptApi.getAbnormalTypeOptions();
    
    // 模拟收货明细数据
    receiptItems.value = [
      {
        id: 'item_001',
        receiptOrderId: props.receiptOrderId,
        partId: 'part_001',
        partCode: 'BP001',
        partName: '刹车片套装',
        unit: '套',
        orderedQuantity: 10,
        shippedQuantity: 10,
        actualReceiptQuantity: 10,
        status: 'NORMAL',
        unitPrice: 280.00,
        amount: 2800.00
      },
      {
        id: 'item_002',
        receiptOrderId: props.receiptOrderId,
        partId: 'part_002',
        partCode: 'BP002',
        partName: '机油滤清器',
        unit: '个',
        orderedQuantity: 20,
        shippedQuantity: 18,
        actualReceiptQuantity: 18,
        status: 'SHORTAGE',
        abnormalType: 'QUANTITY_SHORTAGE',
        abnormalReason: '供应商库存不足',
        unitPrice: 45.00,
        amount: 900.00
      }
    ];
    
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error(t('messages.initDataFailed'));
  } finally {
    loading.value = false;
  }
};

/**
 * 发货单选择变化
 */
const handleShipmentChange = (shipmentId: string) => {
  console.log('选择发货单:', shipmentId);
  // 这里可以根据发货单ID获取对应的收货明细
};

/**
 * 数量变化处理
 */
const handleQuantityChange = (row: ReceiptOrderItem, index: number) => {
  // 自动设置状态
  if (row.actualReceiptQuantity === row.shippedQuantity) {
    row.status = 'NORMAL';
    row.abnormalType = undefined;
    row.abnormalReason = '';
  } else if (row.actualReceiptQuantity < row.shippedQuantity) {
    row.status = 'SHORTAGE';
    if (!row.abnormalType) {
      row.abnormalType = 'QUANTITY_SHORTAGE';
    }
  } else {
    row.status = 'NORMAL'; // 超收也可以设为正常
  }
};

/**
 * 状态变化处理
 */
const handleStatusChange = (row: ReceiptOrderItem, index: number) => {
  if (row.status === 'NORMAL') {
    row.abnormalType = undefined;
    row.abnormalReason = '';
  } else if (!row.abnormalType) {
    // 自动设置默认异常类型
    const statusTypeMap = {
      'SHORTAGE': 'QUANTITY_SHORTAGE',
      'DAMAGE': 'QUALITY_DAMAGE',
      'REJECTED': 'OTHER'
    };
    row.abnormalType = statusTypeMap[row.status] as ReceiptAbnormalType;
  }
};

/**
 * 计算差异
 */
const getDifference = (row: ReceiptOrderItem): number => {
  return (row.actualReceiptQuantity || 0) - (row.shippedQuantity || 0);
};

/**
 * 获取行样式类名
 */
const getRowClassName = ({ row }: { row: ReceiptOrderItem }): string => {
  if (row.status === 'NORMAL') return 'row-normal';
  if (row.status === 'SHORTAGE') return 'row-shortage';
  if (row.status === 'DAMAGE') return 'row-damage';
  if (row.status === 'REJECTED') return 'row-rejected';
  return '';
};

/**
 * 一键全收
 */
const handleBatchReceiveAll = () => {
  receiptItems.value.forEach(item => {
    item.actualReceiptQuantity = item.shippedQuantity;
    item.status = 'NORMAL';
    item.abnormalType = undefined;
    item.abnormalReason = '';
  });
  ElMessage.success(t('messages.receiveAllSuccess'));
};

/**
 * 批量设为正常
 */
const handleBatchSetNormal = () => {
  receiptItems.value.forEach(item => {
    item.status = 'NORMAL';
    item.abnormalType = undefined;
    item.abnormalReason = '';
  });
  ElMessage.success(t('messages.setAllNormalSuccess'));
};

/**
 * 禁用日期
 */
const disabledDate = (time: Date): boolean => {
  // 不能选择未来日期
  return time.getTime() > Date.now();
};

/**
 * 格式化日期
 */
const formatDate = (dateStr: string): string => {
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
  } catch (error) {
    return dateStr;
  }
};

/**
 * 确认收货
 */
const handleConfirmReceipt = async () => {
  if (!formRef.value) return;
  
  try {
    // 表单验证
    await formRef.value.validate();
    
    // 检查是否有异常项目需要填写异常原因
    const invalidItems = receiptItems.value.filter(item => 
      item.status !== 'NORMAL' && (!item.abnormalReason || !item.abnormalReason.trim())
    );
    
    if (invalidItems.length > 0) {
      ElMessage.warning(t('messages.abnormalReasonRequired'));
      return;
    }
    
    // 确认对话框
    await ElMessageBox.confirm(
      t('messages.confirmReceiptItems', {
        total: receiptItems.value.length,
        normal: normalCount.value,
        abnormal: abnormalCount.value
      }),
      t('confirmReceipt'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'info'
      }
    );
    
    confirmLoading.value = true;
    
    // 构建请求数据
    const requestData: ReceiptConfirmRequest = {
      receiptOrderId: props.receiptOrderId,
      receiptDate: receiptForm.receiptDate,
      handler: receiptForm.handler,
      warehouseId: receiptForm.warehouseId || '1',
      items: receiptItems.value.map(item => ({
        itemId: item.id,
        actualReceiptQuantity: item.actualReceiptQuantity,
        status: item.status,
        abnormalType: item.abnormalType,
        abnormalReason: item.abnormalReason,
        locationId: item.locationId || '',
        batchNo: item.batchNo || ''
      })),
      remark: receiptForm.remark
    };
    
    // 提交收货确认
    const result = await receiptApi.confirmReceipt(requestData);
    
    ElMessage.success(t('messages.receiptConfirmSuccess'));
    emit('confirm', result);
    handleClose();
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('收货确认失败:', error);
      ElMessage.error(t('messages.receiptConfirmFailed'));
    }
  } finally {
    confirmLoading.value = false;
  }
};

/**
 * 关闭弹窗
 */
const handleClose = () => {
  dialogVisible.value = false;
  // 重置数据
  selectedShipmentId.value = '';
  receiptItems.value = [];
  Object.assign(receiptForm, {
    receiptDate: new Date().toISOString().slice(0, 10),
    handler: '',
    warehouseId: '',
    remark: ''
  });
};

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible && props.receiptOrderId) {
      initializeData();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.receipt-confirm {
  .confirm-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;

      .section-title {
        margin: 0;
        padding: 0;
        border: none;
      }

      .batch-actions {
        display: flex;
        gap: 8px;
      }
    }

    .shipment-selection {
      .shipment-option {
        margin-bottom: 16px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        padding: 16px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          background: #f0f9ff;
        }

        .shipment-radio {
          width: 100%;

          :deep(.el-radio__label) {
            width: 100%;
          }

          .shipment-info {
            width: 100%;

            .shipment-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .shipment-no {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
              }
            }

            .shipment-details {
              display: flex;
              gap: 20px;
              font-size: 14px;
              color: #606266;
            }
          }
        }
      }

      .no-shipments {
        text-align: center;
        padding: 40px;
      }
    }

    .receipt-table {
      width: 100%;

      :deep(.row-normal) {
        background-color: #f0f9ff;
      }

      :deep(.row-shortage) {
        background-color: #fef0f0;
      }

      :deep(.row-damage) {
        background-color: #fdf6ec;
      }

      :deep(.row-rejected) {
        background-color: #fef0f0;
      }

      .diff-normal {
        color: #67c23a;
        font-weight: 600;
      }

      .diff-shortage {
        color: #f56c6c;
        font-weight: 600;
      }

      .diff-excess {
        color: #e6a23c;
        font-weight: 600;
      }
    }

    .receipt-form {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;

        label {
          font-size: 14px;
          color: #606266;
        }

        span {
          font-size: 18px;
          font-weight: 600;
          color: #303133;

          &.normal {
            color: #67c23a;
          }

          &.abnormal {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 1200px) {
  .receipt-confirm {
    .confirm-section {
      .summary-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .receipt-confirm {
    .confirm-section {
      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .shipment-selection {
        .shipment-option {
          .shipment-info {
            .shipment-header {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;
            }

            .shipment-details {
              flex-direction: column;
              gap: 4px;
            }
          }
        }
      }

      .summary-grid {
        grid-template-columns: 1fr;
        gap: 12px;

        .summary-item {
          flex-direction: row;
          justify-content: space-between;
        }
      }
    }
  }
}
</style>