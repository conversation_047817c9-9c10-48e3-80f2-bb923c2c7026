<template>
  <div class="receipt-header">
    <div class="header-title">
      <h3>{{ t('orderInfo') }}</h3>
    </div>
    
    <div class="info-grid">
      <div class="info-row">
        <div class="info-item">
          <label>{{ t('basicInfo.orderNo') }}：</label>
          <span class="value">{{ orderInfo.orderNo }}</span>
        </div>
        <div class="info-item">
          <label>{{ t('basicInfo.dealerName') }}：</label>
          <span class="value">{{ orderInfo.dealerName }}</span>
        </div>
        <div class="info-item">
          <label>{{ t('basicInfo.orderStatus') }}：</label>
          <el-tag 
            :type="getStatusTagType(orderInfo.status)" 
            size="small"
          >
            {{ getStatusText(orderInfo.status) }}
          </el-tag>
        </div>
      </div>

      <div class="info-row">
        <div class="info-item">
          <label>{{ t('basicInfo.warehouse') }}：</label>
          <span class="value">{{ orderInfo.warehouseName }}</span>
        </div>
        <div class="info-item">
          <label>{{ t('basicInfo.expectedDeliveryDate') }}：</label>
          <span class="value">{{ formatDate(orderInfo.expectedDeliveryDate) }}</span>
        </div>
        <div class="info-item">
          <label>{{ t('basicInfo.orderAmount') }}：</label>
          <span class="value amount">{{ formatCurrency(orderInfo.totalAmount) }}</span>
        </div>
      </div>

      <div class="info-row">
        <div class="info-item">
          <label>{{ t('basicInfo.applyTime') }}：</label>
          <span class="value">{{ formatDateTime(orderInfo.createTime) }}</span>
        </div>
        <div class="info-item">
          <label>{{ t('basicInfo.lastReceiptTime') }}：</label>
          <span class="value">{{ formatDateTime(orderInfo.lastReceiptDate) }}</span>
        </div>
        <div class="info-item">
          <label>{{ t('basicInfo.nextExpectedDate') }}：</label>
          <span class="value">{{ formatDate(orderInfo.nextExpectedDate) }}</span>
        </div>
      </div>

      <div class="info-row" v-if="orderInfo.remark">
        <div class="info-item full-width">
          <label>{{ t('basicInfo.applyRemark') }}：</label>
          <span class="value remark">{{ orderInfo.remark }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { PurchaseOrderExtended, PurchaseOrderStatus } from '@/types/parts/purchase-dealer';
import { useModuleI18n } from '@/composables/useModuleI18n';

// Props
interface Props {
  orderInfo: PurchaseOrderExtended;
}

const props = defineProps<Props>();

// 国际化
const { t } = useModuleI18n('parts.purchase.receipt');
const { t: td } = useModuleI18n('parts.purchase.dealer');

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: PurchaseOrderStatus): string => {
  const typeMap = {
    'draft': 'info',
    'submitted': 'warning',
    'approved': 'primary',
    'rejected': 'danger',
    'shipped': 'success',
    'partiallyShipped': 'warning',
    'partiallyReceived': 'warning',
    'received': 'success',
    'cancelled': 'info'
  };
  return typeMap[status] || 'info';
};

/**
 * 获取状态文本
 */
const getStatusText = (status: PurchaseOrderStatus): string => {
  // 使用经销商购买管理的状态翻译
  return td(`status.${status}`) || status;
};

/**
 * 格式化日期
 */
const formatDate = (dateStr?: string): string => {
  if (!dateStr) return '-';
  
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
  } catch (error) {
    return dateStr;
  }
};

/**
 * 格式化日期时间
 */
const formatDateTime = (dateStr?: string): string => {
  if (!dateStr) return '-';
  
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
  } catch (error) {
    return dateStr;
  }
};

/**
 * 格式化金额
 */
const formatCurrency = (amount?: number): string => {
  if (typeof amount !== 'number') return '-';
  
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};
</script>

<style lang="scss" scoped>
.receipt-header {
  .header-title {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .info-grid {
    .info-row {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-item {
        display: flex;
        align-items: center;

        &.full-width {
          grid-column: 1 / -1;
        }

        label {
          font-size: 14px;
          color: #606266;
          margin-right: 8px;
          white-space: nowrap;
          min-width: 100px;
        }

        .value {
          font-size: 14px;
          color: #303133;
          flex: 1;

          &.amount {
            font-weight: 600;
            color: #e6a23c;
          }

          &.remark {
            background: #f5f7fa;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            word-break: break-all;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .receipt-header {
    .info-grid {
      .info-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .receipt-header {
    .info-grid {
      .info-row {
        grid-template-columns: 1fr;
        gap: 16px;

        .info-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;

          label {
            min-width: auto;
            margin-right: 0;
          }

          .value {
            width: 100%;

            &.remark {
              word-break: break-word;
            }
          }
        }
      }
    }
  }
}
</style>