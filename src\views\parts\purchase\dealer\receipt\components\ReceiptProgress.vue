<template>
  <div class="receipt-progress">
    <div class="header-title">
      <h3>{{ t('progress.receiptProgress') }}</h3>
    </div>

    <!-- 进度条区域 -->
    <div class="progress-section">
      <div class="progress-item">
        <div class="progress-header">
          <label>{{ t('progress.receiptProgress') }}</label>
          <span class="progress-text">
            {{ progress?.receivedQuantity || 0 }} / {{ progress?.totalQuantity || 0 }}
            ({{ formatPercentage(progress?.receivedRate || 0) }})
          </span>
        </div>
        <el-progress 
          :percentage="progress?.receivedRate || 0" 
          :stroke-width="8"
          :status="getProgressStatus(progress?.receivedRate || 0)"
        />
      </div>

      <div class="progress-item">
        <div class="progress-header">
          <label>{{ t('progress.inTransitGoods') }}</label>
          <span class="progress-text">{{ progress?.inTransitQuantity || 0 }} {{ t('progress.items') }}</span>
        </div>
        <el-progress 
          :percentage="getInTransitPercentage()"
          :stroke-width="8"
          color="#909399"
        />
      </div>
    </div>

    <!-- 统计信息网格 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-header">
          <el-icon class="stats-icon pending"><Clock /></el-icon>
          <span class="stats-label">{{ t('progress.pendingReceiptOrders') }}</span>
        </div>
        <div class="stats-value">{{ statistics.pendingReceipt }}</div>
        <div class="stats-desc">{{ t('progress.orderQuantity') }}</div>
      </div>

      <div class="stats-card">
        <div class="stats-header">
          <el-icon class="stats-icon completed"><Check /></el-icon>
          <span class="stats-label">{{ t('progress.completedReceiptOrders') }}</span>
        </div>
        <div class="stats-value">{{ statistics.completedReceipt }}</div>
        <div class="stats-desc">{{ t('progress.orderQuantity') }}</div>
      </div>

      <div class="stats-card">
        <div class="stats-header">
          <el-icon class="stats-icon abnormal"><Warning /></el-icon>
          <span class="stats-label">{{ t('progress.abnormalReceipt') }}</span>
        </div>
        <div class="stats-value">{{ statistics.abnormalReceipt }}</div>
        <div class="stats-desc">{{ t('progress.orderQuantity') }}</div>
      </div>

      <div class="stats-card">
        <div class="stats-header">
          <el-icon class="stats-icon amount"><Money /></el-icon>
          <span class="stats-label">{{ t('progress.receiptAmount') }}</span>
        </div>
        <div class="stats-value amount">{{ formatCurrency(statistics.receivedAmount) }}</div>
        <div class="stats-desc">
          {{ t('progress.totalAmountPercentage', { percentage: formatPercentage((statistics.receivedAmount / statistics.totalAmount) * 100) }) }}
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-header">
          <el-icon class="stats-icon rate"><TrendCharts /></el-icon>
          <span class="stats-label">{{ t('progress.onTimeRate') }}</span>
        </div>
        <div class="stats-value">{{ formatPercentage(statistics.onTimeRate) }}</div>
        <div class="stats-desc">{{ t('progress.onTimeReceiptRate') }}</div>
      </div>

      <div class="stats-card">
        <div class="stats-header">
          <el-icon class="stats-icon total"><List /></el-icon>
          <span class="stats-label">{{ t('progress.totalOrders') }}</span>
        </div>
        <div class="stats-value">{{ statistics.totalOrders }}</div>
        <div class="stats-desc">{{ t('progress.allReceiptOrders') }}</div>
      </div>
    </div>

    <!-- 异常情况提示 -->
    <div v-if="hasAbnormalSituation" class="abnormal-alert">
      <el-alert
        :title="t('progress.abnormalAlert')"
        type="warning"
        :description="getAbnormalDescription()"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  Clock, 
  Check, 
  Warning, 
  Money, 
  TrendCharts, 
  List 
} from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { ReceiptStatistics } from '@/types/parts/purchase-dealer';

// Props
interface Props {
  statistics: ReceiptStatistics;
  progress?: {
    totalQuantity: number;
    receivedQuantity: number;
    receivedRate: number;
    inTransitQuantity: number;
    abnormalQuantity: number;
  };
}

const props = defineProps<Props>();

// 国际化
const { t } = useModuleI18n('parts.purchase.receipt');

/**
 * 是否存在异常情况
 */
const hasAbnormalSituation = computed(() => {
  return props.statistics.abnormalReceipt > 0 || 
         (props.progress?.abnormalQuantity || 0) > 0;
});

/**
 * 获取进度条状态
 */
const getProgressStatus = (percentage: number): string => {
  if (percentage >= 100) return 'success';
  if (percentage >= 80) return 'warning';
  return '';
};

/**
 * 获取在途货物百分比
 */
const getInTransitPercentage = (): number => {
  const totalQuantity = props.progress?.totalQuantity || 0;
  const inTransitQuantity = props.progress?.inTransitQuantity || 0;
  
  if (totalQuantity === 0) return 0;
  return Math.round((inTransitQuantity / totalQuantity) * 100);
};

/**
 * 格式化百分比
 */
const formatPercentage = (value: number): string => {
  return `${Math.round(value)}%`;
};

/**
 * 格式化金额
 */
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

/**
 * 获取异常情况描述
 */
const getAbnormalDescription = (): string => {
  const abnormalOrders = props.statistics.abnormalReceipt;
  const abnormalQuantity = props.progress?.abnormalQuantity || 0;
  
  if (abnormalOrders > 0 && abnormalQuantity > 0) {
    return t('progress.abnormalDescriptionBoth', {
      orders: abnormalOrders,
      quantity: abnormalQuantity
    });
  } else if (abnormalOrders > 0) {
    return t('progress.abnormalDescriptionOrders', {
      orders: abnormalOrders
    });
  } else if (abnormalQuantity > 0) {
    return t('progress.abnormalDescriptionQuantity', {
      quantity: abnormalQuantity
    });
  }
  
  return '';
};
</script>

<style lang="scss" scoped>
.receipt-progress {
  .header-title {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .progress-section {
    margin-bottom: 24px;

    .progress-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        label {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }

        .progress-text {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;

    .stats-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .stats-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;
        gap: 6px;

        .stats-icon {
          font-size: 18px;

          &.pending {
            color: #e6a23c;
          }

          &.completed {
            color: #67c23a;
          }

          &.abnormal {
            color: #f56c6c;
          }

          &.amount {
            color: #409eff;
          }

          &.rate {
            color: #909399;
          }

          &.total {
            color: #606266;
          }
        }

        .stats-label {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }
      }

      .stats-value {
        font-size: 24px;
        font-weight: 700;
        color: #303133;
        margin-bottom: 4px;

        &.amount {
          color: #409eff;
          font-size: 20px;
        }
      }

      .stats-desc {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .abnormal-alert {
    margin-top: 20px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .receipt-progress {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 768px) {
  .receipt-progress {
    .stats-grid {
      grid-template-columns: 1fr;
      gap: 12px;

      .stats-card {
        padding: 16px;

        .stats-value {
          font-size: 20px;

          &.amount {
            font-size: 18px;
          }
        }
      }
    }

    .progress-section {
      .progress-item {
        .progress-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }
      }
    }
  }
}
</style>