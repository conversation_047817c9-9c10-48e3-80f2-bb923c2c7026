<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="t('detail.title')" 
    width="900px"
    @close="handleClose"
  >
    <div v-loading="loading" class="approval-content">
      <!-- 订单基础信息 -->
      <el-card shadow="never" class="info-card">
        <template #header>
          <span class="card-title">{{ t('detail.orderBasicInfo') }}</span>
        </template>
        
        <div class="info-grid" v-if="orderDetail">
          <div class="info-item">
            <label>{{ t('detail.dealer') }}：</label>
            <span>{{ orderDetail.orderInfo.dealerName }}</span>
          </div>
          <div class="info-item">
            <label>{{ t('detail.applicant') }}：</label>
            <span>{{ orderDetail.orderInfo.dealerContact }}</span>
          </div>
          <div class="info-item">
            <label>{{ t('detail.applicantPhone') }}：</label>
            <span>{{ orderDetail.orderInfo.dealerPhone }}</span>
          </div>
          <div class="info-item">
            <label>{{ t('detail.applyDate') }}：</label>
            <span>{{ formatDate(orderDetail.orderInfo.createdAt) }}</span>
          </div>
          <div class="info-item full-width">
            <label>{{ t('detail.applyRemark') }}：</label>
            <span>{{ orderDetail.orderInfo.remarks || '无' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 申请明细 -->
      <el-card shadow="never" class="details-card">
        <template #header>
          <span class="card-title">{{ t('detail.orderDetails') }}</span>
        </template>
        
        <el-table :data="orderDetail?.items || []" border style="width: 100%">
          <el-table-column type="index" :label="t('detail.detailTable.index')" width="60" align="center" />
          <el-table-column :label="t('detail.detailTable.partCode')" prop="partCode" min-width="100" align="center" />
          <el-table-column :label="t('detail.detailTable.partName')" prop="partName" min-width="120" align="center" />
          <el-table-column :label="t('detail.detailTable.requestQty')" prop="orderQuantity" width="100" align="center" />
          <el-table-column :label="t('detail.detailTable.factoryStock')" prop="factoryStock" width="100" align="center" />
          <el-table-column :label="t('detail.detailTable.status')" width="100" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="row.status === 'SUFFICIENT' ? 'success' : row.status === 'INSUFFICIENT' ? 'warning' : 'danger'" 
                size="small"
              >
                {{ row.status === 'SUFFICIENT' ? t('detail.detailTable.stockSufficient') : 
                   row.status === 'INSUFFICIENT' ? t('detail.detailTable.stockInsufficient') : '缺货' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 审核区域 -->
      <el-card shadow="never" class="approval-card">
        <template #header>
          <span class="card-title">{{ t('detail.approvalSection') }}</span>
        </template>
        
        <el-form ref="formRef" :model="approvalForm" :rules="approvalRules" label-width="120px">
          <el-form-item :label="t('detail.approvalResult')" prop="isApproved" required>
            <el-radio-group v-model="approvalForm.isApproved">
              <el-radio :value="true">{{ t('detail.pass') }}</el-radio>
              <el-radio :value="false">{{ t('detail.reject') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item :label="t('detail.approvalNote')" prop="remarks">
            <el-input
              v-model="approvalForm.remarks"
              type="textarea"
              :rows="3"
              :placeholder="t('detail.approvalNotePlaceholder')"
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ t('detail.confirmApproval') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { purchaseOemApi } from '@/api/modules/parts/purchase-oem'
import type { OemOrderDetail, OemApprovalParams } from '@/types/parts/purchase-oem'

// Props
interface Props {
  modelValue: boolean
  orderId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.oem')

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const orderDetail = ref<OemOrderDetail | null>(null)
const formRef = ref<FormInstance>()

// 表单数据
const approvalForm = reactive({
  isApproved: true as boolean,
  remarks: ''
})

// 表单验证规则
const approvalRules: FormRules = {
  isApproved: [
    { required: true, message: t('validation.approvalResultRequired'), trigger: 'change' }
  ],
  remarks: [
    { required: true, message: t('validation.approvalNoteRequired'), trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载订单详情
const loadOrderDetail = async () => {
  if (!props.orderId) return
  
  try {
    loading.value = true
    orderDetail.value = await purchaseOemApi.getOrderDetail(props.orderId)
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  } finally {
    loading.value = false
  }
}

// 提交审批
const handleSubmit = async () => {
  if (!formRef.value || !props.orderId) return

  try {
    await formRef.value.validate()
    
    const confirmMessage = approvalForm.isApproved 
      ? t('messages.confirmApprove') 
      : t('messages.confirmReject')
    
    await ElMessageBox.confirm(confirmMessage, '确认', {
      type: 'warning'
    })
    
    submitting.value = true
    
    const params: OemApprovalParams = {
      orderId: props.orderId,
      isApproved: approvalForm.isApproved,
      remarks: approvalForm.remarks,
      auditorId: 1 // TODO: 从用户信息获取
    }
    
    await purchaseOemApi.approveOrder(params)
    
    ElMessage.success(
      approvalForm.isApproved ? t('messages.approveSuccess') : '驳回成功'
    )
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审批失败:', error)
      ElMessage.error('审批失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  approvalForm.isApproved = true
  approvalForm.remarks = ''
  formRef.value?.clearValidate()
}

// 监听弹窗打开
watch(() => props.modelValue, (visible) => {
  if (visible) {
    loadOrderDetail()
  }
})
</script>

<style scoped lang="scss">
.approval-content {
  .info-card,
  .details-card,
  .approval-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    
    .info-item {
      display: flex;
      align-items: center;
      
      &.full-width {
        grid-column: 1 / -1;
      }
      
      label {
        font-weight: 500;
        color: var(--el-text-color-regular);
        margin-right: 8px;
        min-width: 80px;
      }
      
      span {
        color: var(--el-text-color-primary);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-table) {
  font-size: 14px;
}
</style>