<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="dialogTitle" 
    width="800px"
    @close="handleClose"
  >
    <div class="batch-content">
      <!-- 选中订单列表 -->
      <el-card shadow="never" class="orders-card">
        <template #header>
          <span class="card-title">已选中订单 ({{ selectedOrders.length }})</span>
        </template>
        
        <el-table :data="selectedOrders" max-height="300" border>
          <el-table-column prop="orderNo" label="采购单号" width="140" align="center" />
          <el-table-column prop="dealerName" label="经销商" min-width="160" align="center" />
          <el-table-column label="订单金额" width="120" align="center">
            <template #default="{ row }">
              ¥{{ formatAmount(row.totalAmount) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 批量审批表单 -->
      <el-card v-if="batchType === 'approval'" shadow="never" class="form-card">
        <template #header>
          <span class="card-title">批量审批设置</span>
        </template>
        
        <el-form ref="approvalFormRef" :model="approvalForm" :rules="approvalRules" label-width="120px">
          <el-form-item label="审批结果" prop="isApproved" required>
            <el-radio-group v-model="approvalForm.isApproved">
              <el-radio :value="true">通过</el-radio>
              <el-radio :value="false">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="审批说明" prop="remarks">
            <el-input
              v-model="approvalForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入批量审批说明..."
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 批量发货表单 -->
      <el-card v-if="batchType === 'shipment'" shadow="never" class="form-card">
        <template #header>
          <span class="card-title">批量发货设置</span>
        </template>
        
        <el-form ref="shipmentFormRef" :model="shipmentForm" :rules="shipmentRules" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="承运商" prop="carrier" required>
                <el-select 
                  v-model="shipmentForm.carrier" 
                  placeholder="请选择承运商"
                  style="width: 100%"
                >
                  <el-option 
                    v-for="carrier in carriers" 
                    :key="carrier.carrierId" 
                    :label="carrier.carrierName" 
                    :value="carrier.carrierName" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发货日期" prop="shippingDate" required>
                <el-date-picker
                  v-model="shipmentForm.shippingDate"
                  type="date"
                  placeholder="选择发货日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="发货备注" prop="remarks">
            <el-input
              v-model="shipmentForm.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入批量发货备注..."
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitButtonText }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { purchaseOemApi } from '@/api/modules/parts/purchase-oem'
import type { 
  OemPurchaseOrder, 
  BatchApprovalParams, 
  BatchShipmentParams,
  CarrierInfo 
} from '@/types/parts/purchase-oem'

// Props
interface Props {
  modelValue: boolean
  batchType: 'approval' | 'shipment'
  selectedOrders: OemPurchaseOrder[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.oem')

// 响应式数据
const submitting = ref(false)
const carriers = ref<CarrierInfo[]>([])
const approvalFormRef = ref<FormInstance>()
const shipmentFormRef = ref<FormInstance>()

// 审批表单数据
const approvalForm = reactive({
  isApproved: true as boolean,
  remarks: ''
})

// 发货表单数据
const shipmentForm = reactive({
  carrier: '',
  shippingDate: '',
  remarks: ''
})

// 表单验证规则
const approvalRules: FormRules = {
  isApproved: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  remarks: [
    { required: true, message: '请输入审批说明', trigger: 'blur' }
  ]
}

const shipmentRules: FormRules = {
  carrier: [
    { required: true, message: '请选择承运商', trigger: 'change' }
  ],
  shippingDate: [
    { required: true, message: '请选择发货日期', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  return props.batchType === 'approval' ? '批量审批' : '批量发货'
})

const submitButtonText = computed(() => {
  return props.batchType === 'approval' ? '确认审批' : '确认发货'
})

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING_APPROVAL': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'PENDING_SHIPMENT': 'primary',
    'PARTIALLY_SHIPPED': 'warning',
    'SHIPPED_ALL': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'PENDING_APPROVAL': '待审核',
    'APPROVED': '已审核',
    'REJECTED': '已驳回',
    'PENDING_SHIPMENT': '待发货',
    'PARTIALLY_SHIPPED': '部分发货',
    'SHIPPED_ALL': '全部发货'
  }
  return textMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

// 加载承运商列表
const loadCarriers = async () => {
  try {
    carriers.value = await purchaseOemApi.getCarriers()
  } catch (error) {
    console.error('加载承运商列表失败:', error)
    ElMessage.error('加载承运商列表失败')
  }
}

// 提交批量操作
const handleSubmit = async () => {
  const formRef = props.batchType === 'approval' ? approvalFormRef.value : shipmentFormRef.value
  if (!formRef) return

  try {
    await formRef.validate()
    
    const orderIds = props.selectedOrders.map(order => order.orderId)
    const confirmMessage = props.batchType === 'approval' 
      ? `确认${approvalForm.isApproved ? '通过' : '驳回'}所选的${orderIds.length}个订单？`
      : `确认发货所选的${orderIds.length}个订单？`
    
    await ElMessageBox.confirm(confirmMessage, '确认', {
      type: 'warning'
    })
    
    submitting.value = true
    
    if (props.batchType === 'approval') {
      const params: BatchApprovalParams = {
        orderIds,
        isApproved: approvalForm.isApproved,
        remarks: approvalForm.remarks,
        auditorId: 1 // TODO: 从用户信息获取
      }
      
      const result = await purchaseOemApi.batchApprove(params)
      
      if (result.failCount > 0) {
        ElMessage.warning(`批量审批完成，成功${result.successCount}个，失败${result.failCount}个`)
      } else {
        ElMessage.success(`批量审批成功，共处理${result.successCount}个订单`)
      }
    } else {
      const params: BatchShipmentParams = {
        orderIds,
        carrier: shipmentForm.carrier,
        shippingDate: shipmentForm.shippingDate,
        remarks: shipmentForm.remarks
      }
      
      const result = await purchaseOemApi.batchShip(params)
      
      if (result.failCount > 0) {
        ElMessage.warning(`批量发货完成，成功${result.successCount}个，失败${result.failCount}个`)
      } else {
        ElMessage.success(`批量发货成功，共处理${result.successCount}个订单`)
      }
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  approvalForm.isApproved = true
  approvalForm.remarks = ''
  shipmentForm.carrier = ''
  shipmentForm.shippingDate = ''
  shipmentForm.remarks = ''
  approvalFormRef.value?.clearValidate()
  shipmentFormRef.value?.clearValidate()
}

// 监听弹窗打开
watch(() => props.modelValue, (visible) => {
  if (visible) {
    // 设置默认发货日期为今天
    if (props.batchType === 'shipment') {
      shipmentForm.shippingDate = new Date().toISOString().split('T')[0]
    }
  }
})

// 初始化
onMounted(() => {
  loadCarriers()
})
</script>

<style scoped lang="scss">
.batch-content {
  .orders-card,
  .form-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-table) {
  font-size: 14px;
}
</style>