<template>
  <el-card class="dashboard-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span class="card-title">{{ t('title') }}</span>
      </div>
    </template>

    <div class="dashboard-content">
      <!-- 统计数据 -->
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ dashboardData.pendingApprovalCount }}</div>
            <div class="stat-label">{{ t('dashboard.pendingApproval') }}</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon approved">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ dashboardData.approvedTodayCount }}</div>
            <div class="stat-label">{{ t('dashboard.approvedToday') }}</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon shipping">
            <el-icon><Van /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ dashboardData.pendingShipmentCount }}</div>
            <div class="stat-label">{{ t('dashboard.pendingShipment') }}</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon amount">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥{{ formatAmount(dashboardData.last7DaysTotalAmount) }}</div>
            <div class="stat-label">{{ t('dashboard.weeklyTotal') }}</div>
          </div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <div class="actions-header">
          <span class="actions-title">{{ t('dashboard.quickActions') }}</span>
        </div>
        <div class="actions-grid">
          <el-button type="primary" @click="$emit('batchApproval')">
            <el-icon><CircleCheck /></el-icon>
            {{ t('dashboard.batchApproval') }}
          </el-button>
          <el-button type="success" @click="$emit('batchShipment')">
            <el-icon><Van /></el-icon>
            {{ t('dashboard.batchShipment') }}
          </el-button>
          <el-button type="info" @click="$emit('dealerAnalysis')">
            <el-icon><DataAnalysis /></el-icon>
            {{ t('dashboard.dealerAnalysis') }}
          </el-button>
          <el-button type="warning" @click="$emit('popularParts')">
            <el-icon><TrendCharts /></el-icon>
            {{ t('dashboard.popularParts') }}
          </el-button>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, Check, Van, Money, CircleCheck, DataAnalysis, TrendCharts } from '@element-plus/icons-vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { purchaseOemApi } from '@/api/modules/parts/purchase-oem'
import type { OemDashboard } from '@/types/parts/purchase-oem'

// Emits
defineEmits<{
  batchApproval: []
  batchShipment: []
  dealerAnalysis: []
  popularParts: []
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.oem')

// 响应式数据
const dashboardData = ref<OemDashboard>({
  pendingApprovalCount: 0,
  approvedTodayCount: 0,
  pendingShipmentCount: 0,
  last7DaysTotalAmount: 0
})

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    dashboardData.value = await purchaseOemApi.getStatistics()
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
    ElMessage.error('加载仪表盘数据失败')
  }
}

// 初始化
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped lang="scss">
.dashboard-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.dashboard-content {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
  }

  .stat-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;

      .el-icon {
        font-size: 24px;
        color: white;
      }

      &.pending {
        background: linear-gradient(135deg, #f39c12, #e67e22);
      }

      &.approved {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
      }

      &.shipping {
        background: linear-gradient(135deg, #3498db, #2980b9);
      }

      &.amount {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
      }
    }

    .stat-content {
      flex: 1;

      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .quick-actions {
    .actions-header {
      margin-bottom: 16px;

      .actions-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .actions-grid {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      .el-button {
        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }
}
</style>
