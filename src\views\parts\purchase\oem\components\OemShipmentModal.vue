<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="t('detail.title')" 
    width="900px"
    @close="handleClose"
  >
    <div v-loading="loading" class="shipment-content">
      <!-- 订单基础信息 -->
      <el-card shadow="never" class="info-card">
        <template #header>
          <span class="card-title">{{ t('detail.orderBasicInfo') }}</span>
        </template>
        
        <div class="info-grid" v-if="orderDetail">
          <div class="info-item">
            <label>{{ t('detail.dealer') }}：</label>
            <span>{{ orderDetail.orderInfo.dealerName }}</span>
          </div>
          <div class="info-item">
            <label>采购单号：</label>
            <span>{{ orderDetail.orderInfo.orderNo }}</span>
          </div>
          <div class="info-item">
            <label>订单状态：</label>
            <el-tag :type="getStatusType(orderDetail.orderInfo.status)" size="small">
              {{ getStatusText(orderDetail.orderInfo.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>订单金额：</label>
            <span>¥{{ formatAmount(orderDetail.orderInfo.totalAmount) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 发货明细 -->
      <el-card shadow="never" class="details-card">
        <template #header>
          <span class="card-title">{{ t('detail.orderDetails') }}</span>
        </template>
        
        <el-table :data="shipmentItems" border style="width: 100%">
          <el-table-column type="index" :label="t('detail.detailTable.index')" width="60" align="center" />
          <el-table-column :label="t('detail.detailTable.partCode')" prop="partCode" min-width="100" align="center" />
          <el-table-column :label="t('detail.detailTable.partName')" prop="partName" min-width="120" align="center" />
          <el-table-column :label="t('detail.detailTable.requestQty')" prop="orderQuantity" width="100" align="center" />
          <el-table-column :label="t('detail.detailTable.factoryStock')" prop="factoryStock" width="100" align="center" />
          <el-table-column :label="t('detail.detailTable.shipQty')" width="120" align="center">
            <template #default="{ row, $index }">
              <el-input-number
                v-model="row.shippedQuantity"
                :min="0"
                :max="Math.min(row.orderQuantity - row.originalShippedQuantity, row.availableStock)"
                size="small"
                style="width: 100px"
                @change="updateShipmentQuantity($index, row.shippedQuantity)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="t('detail.detailTable.status')" width="100" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="row.status === 'SUFFICIENT' ? 'success' : row.status === 'INSUFFICIENT' ? 'warning' : 'danger'" 
                size="small"
              >
                {{ row.status === 'SUFFICIENT' ? t('detail.detailTable.stockSufficient') : 
                   row.status === 'INSUFFICIENT' ? t('detail.detailTable.stockInsufficient') : '缺货' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 发货信息 -->
      <el-card shadow="never" class="shipment-card">
        <template #header>
          <span class="card-title">{{ t('detail.shipmentSection') }}</span>
        </template>
        
        <el-form ref="formRef" :model="shipmentForm" :rules="shipmentRules" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item :label="t('detail.carrier')" prop="carrier" required>
                <el-select 
                  v-model="shipmentForm.carrier" 
                  :placeholder="t('detail.carrierPlaceholder')"
                  style="width: 100%"
                >
                  <el-option 
                    v-for="carrier in carriers" 
                    :key="carrier.carrierId" 
                    :label="carrier.carrierName" 
                    :value="carrier.carrierName" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('detail.trackingNo')" prop="trackingNumber" required>
                <el-input
                  v-model="shipmentForm.trackingNumber"
                  :placeholder="t('detail.trackingNoPlaceholder')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item :label="t('detail.shipmentNote')" prop="remarks">
            <el-input
              v-model="shipmentForm.remarks"
              type="textarea"
              :rows="3"
              :placeholder="t('detail.shipmentNotePlaceholder')"
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ t('detail.confirmShipment') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { purchaseOemApi } from '@/api/modules/parts/purchase-oem'
import type { 
  OemOrderDetail, 
  OemShipmentParams, 
  CarrierInfo,
  OemPurchaseOrderItem 
} from '@/types/parts/purchase-oem'

// Props
interface Props {
  modelValue: boolean
  orderId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.oem')

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const orderDetail = ref<OemOrderDetail | null>(null)
const carriers = ref<CarrierInfo[]>([])
const shipmentItems = ref<(OemPurchaseOrderItem & { originalShippedQuantity: number })[]>([])
const formRef = ref<FormInstance>()

// 表单数据
const shipmentForm = reactive({
  carrier: '',
  trackingNumber: '',
  remarks: ''
})

// 表单验证规则
const shipmentRules: FormRules = {
  carrier: [
    { required: true, message: t('validation.carrierRequired'), trigger: 'change' }
  ],
  trackingNumber: [
    { required: true, message: t('validation.trackingNoRequired'), trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING_SHIPMENT': 'primary',
    'PARTIALLY_SHIPPED': 'warning',
    'SHIPPED_ALL': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'PENDING_SHIPMENT': '待发货',
    'PARTIALLY_SHIPPED': '部分发货',
    'SHIPPED_ALL': '全部发货'
  }
  return textMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

// 加载承运商列表
const loadCarriers = async () => {
  try {
    carriers.value = await purchaseOemApi.getCarriers()
  } catch (error) {
    console.error('加载承运商列表失败:', error)
    ElMessage.error('加载承运商列表失败')
  }
}

// 加载订单详情
const loadOrderDetail = async () => {
  if (!props.orderId) return
  
  try {
    loading.value = true
    orderDetail.value = await purchaseOemApi.getOrderDetail(props.orderId)
    
    // 初始化发货明细
    if (orderDetail.value?.items) {
      shipmentItems.value = orderDetail.value.items.map(item => ({
        ...item,
        originalShippedQuantity: item.shippedQuantity,
        shippedQuantity: Math.min(
          item.orderQuantity - item.shippedQuantity, 
          item.availableStock
        )
      }))
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  } finally {
    loading.value = false
  }
}

// 更新发货数量
const updateShipmentQuantity = (index: number, quantity: number) => {
  shipmentItems.value[index].shippedQuantity = quantity
}

// 提交发货
const handleSubmit = async () => {
  if (!formRef.value || !props.orderId) return

  try {
    await formRef.value.validate()
    
    // 检查是否有发货数量
    const hasShipment = shipmentItems.value.some(item => item.shippedQuantity > 0)
    if (!hasShipment) {
      ElMessage.warning('请设置发货数量')
      return
    }
    
    await ElMessageBox.confirm(t('messages.confirmShip'), '确认', {
      type: 'warning'
    })
    
    submitting.value = true
    
    const params: OemShipmentParams = {
      orderId: props.orderId,
      carrier: shipmentForm.carrier,
      trackingNumber: shipmentForm.trackingNumber,
      remarks: shipmentForm.remarks,
      items: shipmentItems.value
        .filter(item => item.shippedQuantity > 0)
        .map(item => ({
          partId: item.partId,
          shippedQuantity: item.shippedQuantity
        }))
    }
    
    await purchaseOemApi.shipOrder(params)
    
    ElMessage.success(t('messages.shipSuccess'))
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发货失败:', error)
      ElMessage.error('发货失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  shipmentForm.carrier = ''
  shipmentForm.trackingNumber = ''
  shipmentForm.remarks = ''
  shipmentItems.value = []
  formRef.value?.clearValidate()
}

// 监听弹窗打开
watch(() => props.modelValue, (visible) => {
  if (visible) {
    loadOrderDetail()
  }
})

// 初始化
onMounted(() => {
  loadCarriers()
})
</script>

<style scoped lang="scss">
.shipment-content {
  .info-card,
  .details-card,
  .shipment-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    
    .info-item {
      display: flex;
      align-items: center;
      
      label {
        font-weight: 500;
        color: var(--el-text-color-regular);
        margin-right: 8px;
        min-width: 80px;
      }
      
      span {
        color: var(--el-text-color-primary);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-table) {
  font-size: 14px;
}
</style>