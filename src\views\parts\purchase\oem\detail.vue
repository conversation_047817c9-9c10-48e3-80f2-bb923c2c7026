<template>
  <div class="oem-order-detail-page">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ t('detail.title') }}：{{ orderDetail?.orderInfo.orderNo }}</span>
          <el-button @click="handleBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="detail-content">
        <!-- 订单状态 -->
        <div v-if="orderDetail" class="status-banner">
          <span class="status-label">订单状态：</span>
          <el-tag :type="getStatusType(orderDetail.orderInfo.status)" size="large">
            {{ getStatusText(orderDetail.orderInfo.status) }}
          </el-tag>
        </div>

        <!-- 订单基础信息 -->
        <el-card shadow="never" class="info-card">
          <template #header>
            <span class="card-title">{{ t('detail.orderBasicInfo') }}</span>
          </template>
          
          <div class="info-grid" v-if="orderDetail">
            <div class="info-item">
              <label>{{ t('detail.dealer') }}：</label>
              <span>{{ orderDetail.orderInfo.dealerName }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('detail.applicant') }}：</label>
              <span>{{ orderDetail.orderInfo.dealerContact }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('detail.applicantPhone') }}：</label>
              <span>{{ orderDetail.orderInfo.dealerPhone }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('detail.applyDate') }}：</label>
              <span>{{ formatDate(orderDetail.orderInfo.createdAt) }}</span>
            </div>
            <div class="info-item">
              <label>订单金额：</label>
              <span class="amount">¥{{ formatAmount(orderDetail.orderInfo.totalAmount) }}</span>
            </div>
            <div class="info-item">
              <label>优先级：</label>
              <el-tag 
                :type="getPriorityType(orderDetail.orderInfo.priority)" 
                size="small"
                effect="plain"
              >
                {{ getPriorityText(orderDetail.orderInfo.priority) }}
              </el-tag>
            </div>
            <div class="info-item full-width">
              <label>{{ t('detail.applyRemark') }}：</label>
              <span>{{ orderDetail.orderInfo.remarks || '无' }}</span>
            </div>
          </div>
        </el-card>

        <!-- 申请明细 -->
        <el-card shadow="never" class="details-card">
          <template #header>
            <span class="card-title">{{ t('detail.orderDetails') }}</span>
          </template>
          
          <el-table :data="editableItems" border style="width: 100%">
            <el-table-column type="index" :label="t('detail.detailTable.index')" width="60" align="center" />
            <el-table-column :label="t('detail.detailTable.partCode')" prop="partCode" min-width="100" align="center" />
            <el-table-column :label="t('detail.detailTable.partName')" prop="partName" min-width="120" align="center" />
            <el-table-column :label="t('detail.detailTable.requestQty')" prop="orderQuantity" width="100" align="center" />
            <el-table-column :label="t('detail.detailTable.factoryStock')" prop="factoryStock" width="100" align="center" />
            <el-table-column 
              v-if="canShip" 
              :label="t('detail.detailTable.shipQty')" 
              width="120" 
              align="center"
            >
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.shippedQuantity"
                  :min="0"
                  :max="Math.min(row.orderQuantity - row.originalShippedQuantity, row.availableStock)"
                  size="small"
                  style="width: 100px"
                  @change="updateShipmentQuantity($index, row.shippedQuantity)"
                />
              </template>
            </el-table-column>
            <el-table-column :label="t('detail.detailTable.status')" width="100" align="center">
              <template #default="{ row }">
                <el-tag 
                  :type="row.status === 'SUFFICIENT' ? 'success' : row.status === 'INSUFFICIENT' ? 'warning' : 'danger'" 
                  size="small"
                >
                  {{ row.status === 'SUFFICIENT' ? t('detail.detailTable.stockSufficient') : 
                     row.status === 'INSUFFICIENT' ? t('detail.detailTable.stockInsufficient') : '缺货' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 处理操作 -->
        <el-card shadow="never" class="operation-card">
          <template #header>
            <span class="card-title">处理操作</span>
          </template>
          
          <!-- 审核区 -->
          <div v-if="canApprove" class="approval-section">
            <h4>{{ t('detail.approvalSection') }}</h4>
            <el-form ref="approvalFormRef" :model="approvalForm" :rules="approvalRules" label-width="120px">
              <el-form-item :label="t('detail.approvalResult')" prop="isApproved" required>
                <el-radio-group v-model="approvalForm.isApproved">
                  <el-radio :value="true">{{ t('detail.pass') }}</el-radio>
                  <el-radio :value="false">{{ t('detail.reject') }}</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item :label="t('detail.approvalNote')" prop="remarks">
                <el-input
                  v-model="approvalForm.remarks"
                  type="textarea"
                  :rows="3"
                  :placeholder="t('detail.approvalNotePlaceholder')"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" :loading="approvalSubmitting" @click="handleApproval">
                  {{ t('detail.confirmApproval') }}
                </el-button>
              </el-form-item>
            </el-form>
            
            <el-divider />
          </div>

          <!-- 发货区 -->
          <div v-if="canShip" class="shipment-section">
            <h4>{{ t('detail.shipmentSection') }}</h4>
            <el-form ref="shipmentFormRef" :model="shipmentForm" :rules="shipmentRules" label-width="120px">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item :label="t('detail.carrier')" prop="carrier" required>
                    <el-select 
                      v-model="shipmentForm.carrier" 
                      :placeholder="t('detail.carrierPlaceholder')"
                      style="width: 100%"
                    >
                      <el-option 
                        v-for="carrier in carriers" 
                        :key="carrier.carrierId" 
                        :label="carrier.carrierName" 
                        :value="carrier.carrierName" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('detail.trackingNo')" prop="trackingNumber" required>
                    <el-input
                      v-model="shipmentForm.trackingNumber"
                      :placeholder="t('detail.trackingNoPlaceholder')"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item :label="t('detail.shipmentNote')" prop="remarks">
                <el-input
                  v-model="shipmentForm.remarks"
                  type="textarea"
                  :rows="3"
                  :placeholder="t('detail.shipmentNotePlaceholder')"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" :loading="shipmentSubmitting" @click="handleShipment">
                  {{ t('detail.confirmShipment') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { purchaseOemApi } from '@/api/modules/parts/purchase-oem'
import type { 
  OemOrderDetail, 
  OemApprovalParams, 
  OemShipmentParams,
  CarrierInfo,
  OemPurchaseOrderItem 
} from '@/types/parts/purchase-oem'

// 国际化
const { t } = useModuleI18n('parts.purchase.oem')

// 路由
const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const approvalSubmitting = ref(false)
const shipmentSubmitting = ref(false)
const orderDetail = ref<OemOrderDetail | null>(null)
const carriers = ref<CarrierInfo[]>([])
const editableItems = ref<(OemPurchaseOrderItem & { originalShippedQuantity: number })[]>([])
const approvalFormRef = ref<FormInstance>()
const shipmentFormRef = ref<FormInstance>()

// 获取订单ID
const orderId = route.params.id as string

// 审批表单
const approvalForm = reactive({
  isApproved: true as boolean,
  remarks: ''
})

// 发货表单
const shipmentForm = reactive({
  carrier: '',
  trackingNumber: '',
  remarks: ''
})

// 表单验证规则
const approvalRules: FormRules = {
  isApproved: [
    { required: true, message: t('validation.approvalResultRequired'), trigger: 'change' }
  ],
  remarks: [
    { required: true, message: t('validation.approvalNoteRequired'), trigger: 'blur' }
  ]
}

const shipmentRules: FormRules = {
  carrier: [
    { required: true, message: t('validation.carrierRequired'), trigger: 'change' }
  ],
  trackingNumber: [
    { required: true, message: t('validation.trackingNoRequired'), trigger: 'blur' }
  ]
}

// 计算属性
const canApprove = computed(() => {
  return orderDetail.value?.orderInfo.status === 'PENDING_APPROVAL'
})

const canShip = computed(() => {
  return orderDetail.value?.orderInfo.status === 'PENDING_SHIPMENT' ||
         orderDetail.value?.orderInfo.status === 'PARTIALLY_SHIPPED'
})

// 状态相关函数
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING_APPROVAL': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'PENDING_SHIPMENT': 'primary',
    'PARTIALLY_SHIPPED': 'warning',
    'SHIPPED_ALL': 'success',
    'CANCELLED': 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'PENDING_APPROVAL': '待审核',
    'APPROVED': '已审核',
    'REJECTED': '已驳回',
    'PENDING_SHIPMENT': '待发货',
    'PARTIALLY_SHIPPED': '部分发货',
    'SHIPPED_ALL': '全部发货',
    'CANCELLED': '已取消'
  }
  return textMap[status] || status
}

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    'LOW': 'info',
    'NORMAL': 'primary',
    'HIGH': 'warning',
    'URGENT': 'danger'
  }
  return typeMap[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    'LOW': '低',
    'NORMAL': '普通',
    'HIGH': '高',
    'URGENT': '紧急'
  }
  return textMap[priority] || priority
}

// 格式化函数
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载数据
const loadOrderDetail = async () => {
  try {
    loading.value = true
    orderDetail.value = await purchaseOemApi.getOrderDetail(Number(orderId))
    
    // 初始化可编辑明细
    if (orderDetail.value?.items) {
      editableItems.value = orderDetail.value.items.map(item => ({
        ...item,
        originalShippedQuantity: item.shippedQuantity,
        shippedQuantity: Math.min(
          item.orderQuantity - item.shippedQuantity, 
          item.availableStock
        )
      }))
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const loadCarriers = async () => {
  try {
    carriers.value = await purchaseOemApi.getCarriers()
  } catch (error) {
    console.error('加载承运商列表失败:', error)
    ElMessage.error('加载承运商列表失败')
  }
}

// 更新发货数量
const updateShipmentQuantity = (index: number, quantity: number) => {
  editableItems.value[index].shippedQuantity = quantity
}

// 处理审批
const handleApproval = async () => {
  if (!approvalFormRef.value) return

  try {
    await approvalFormRef.value.validate()
    
    const confirmMessage = approvalForm.isApproved 
      ? t('messages.confirmApprove') 
      : t('messages.confirmReject')
    
    await ElMessageBox.confirm(confirmMessage, '确认', {
      type: 'warning'
    })
    
    approvalSubmitting.value = true
    
    const params: OemApprovalParams = {
      orderId: Number(orderId),
      isApproved: approvalForm.isApproved,
      remarks: approvalForm.remarks,
      auditorId: 1 // TODO: 从用户信息获取
    }
    
    await purchaseOemApi.approveOrder(params)
    
    ElMessage.success(
      approvalForm.isApproved ? t('messages.approveSuccess') : '驳回成功'
    )
    
    // 重新加载订单详情
    await loadOrderDetail()
    
    // 重置表单
    approvalForm.isApproved = true
    approvalForm.remarks = ''
    approvalFormRef.value?.clearValidate()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审批失败:', error)
      ElMessage.error('审批失败')
    }
  } finally {
    approvalSubmitting.value = false
  }
}

// 处理发货
const handleShipment = async () => {
  if (!shipmentFormRef.value) return

  try {
    await shipmentFormRef.value.validate()
    
    // 检查是否有发货数量
    const hasShipment = editableItems.value.some(item => item.shippedQuantity > 0)
    if (!hasShipment) {
      ElMessage.warning('请设置发货数量')
      return
    }
    
    await ElMessageBox.confirm(t('messages.confirmShip'), '确认', {
      type: 'warning'
    })
    
    shipmentSubmitting.value = true
    
    const params: OemShipmentParams = {
      orderId: Number(orderId),
      carrier: shipmentForm.carrier,
      trackingNumber: shipmentForm.trackingNumber,
      remarks: shipmentForm.remarks,
      items: editableItems.value
        .filter(item => item.shippedQuantity > 0)
        .map(item => ({
          partId: item.partId,
          shippedQuantity: item.shippedQuantity
        }))
    }
    
    await purchaseOemApi.shipOrder(params)
    
    ElMessage.success(t('messages.shipSuccess'))
    
    // 重新加载订单详情
    await loadOrderDetail()
    
    // 重置表单
    shipmentForm.carrier = ''
    shipmentForm.trackingNumber = ''
    shipmentForm.remarks = ''
    shipmentFormRef.value?.clearValidate()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发货失败:', error)
      ElMessage.error('发货失败')
    }
  } finally {
    shipmentSubmitting.value = false
  }
}

// 返回
const handleBack = () => {
  router.back()
}

// 初始化
onMounted(() => {
  loadOrderDetail()
  loadCarriers()
})
</script>

<style scoped lang="scss">
.oem-order-detail-page {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.detail-content {
  .status-banner {
    background: var(--el-bg-color-page);
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    
    .status-label {
      font-weight: 500;
      margin-right: 12px;
      color: var(--el-text-color-primary);
    }
  }

  .info-card,
  .details-card,
  .operation-card {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    
    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
    
    .info-item {
      display: flex;
      align-items: center;
      
      &.full-width {
        grid-column: 1 / -1;
      }
      
      label {
        font-weight: 500;
        color: var(--el-text-color-regular);
        margin-right: 8px;
        min-width: 80px;
      }
      
      span {
        color: var(--el-text-color-primary);
        
        &.amount {
          font-weight: 600;
          color: var(--el-color-primary);
        }
      }
    }
  }

  .approval-section,
  .shipment-section {
    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-divider) {
  margin: 24px 0;
}
</style>