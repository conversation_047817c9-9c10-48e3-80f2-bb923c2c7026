<template>
  <div class="add-prospect-page">
    <h1 class="page-title">新增潜客</h1>

    <div class="form-container">
      <el-form ref="formRef" :model="formModel" :rules="rules" label-position="left" label-width="120px">

        <div class="form-section">
          <h2 class="section-title">潜客信息</h2>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="潜客来源" prop="prospectSource">
                <el-input v-model="formModel.prospectSource" placeholder="请输入潜客来源" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="潜客名称" prop="prospectName">
                <el-input v-model="formModel.prospectName" placeholder="请输入潜客名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="潜客手机号" prop="prospectPhone">
                <el-input v-model="formModel.prospectPhone" placeholder="请输入潜客手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证件类别" prop="prospectIdType">
                <el-select
                  v-model="formModel.prospectIdType"
                  placeholder="请选择证件类别"
                  style="width: 100%"
                  :loading="dictionaryLoading"
                >
                  <el-option
                    v-for="option in idTypeOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证件号" prop="prospectIdNumber">
                <el-input v-model="formModel.prospectIdNumber" placeholder="请输入证件号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="prospectEmail">
                <el-input v-model="formModel.prospectEmail" placeholder="请输入邮箱" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地区" prop="prospectRegion">
                <el-input v-model="formModel.prospectRegion" placeholder="请输入地区" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地址" prop="prospectAddress">
                <el-input v-model="formModel.prospectAddress" placeholder="请输入详细地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <h2 class="section-title">试驾信息</h2>
          <n-grid :cols="2" :x-gap="24">
            <n-form-item-gi label="试驾单号">
              <n-input disabled placeholder="保存时自动生成" />
            </n-form-item-gi>
            <n-form-item-gi label="销售顾问" path="salesAdvisorName">
              <n-input v-model:value="formModel.salesAdvisorName" placeholder="请输入销售顾问" />
            </n-form-item-gi>
            <n-form-item-gi label="试驾车型 (Model)" path="testDriveModel">
              <n-select
                v-model:value="formModel.testDriveModel"
                :options="modelOptions"
                placeholder="请选择车型"
                :loading="masterDataLoading"
              />
            </n-form-item-gi>
            <n-form-item-gi label="试驾配置 (Variant)" path="testDriveVariant">
              <n-select
                v-model:value="formModel.testDriveVariant"
                :options="variantOptions"
                placeholder="请选择配置"
                :loading="masterDataLoading"
              />
            </n-form-item-gi>
            <n-form-item-gi label="试驾人" path="driverName">
              <n-input v-model:value="formModel.driverName" placeholder="默认带入潜客信息，可更改" />
            </n-form-item-gi>
            <n-form-item-gi label="试驾人手机号" path="driverPhone">
              <n-input v-model:value="formModel.driverPhone" placeholder="默认带入潜客信息，可更改" />
            </n-form-item-gi>
            <n-form-item-gi label="试驾人证件类别" path="driverIdType">
               <n-select
                 v-model:value="formModel.driverIdType"
                 :options="idTypeOptions.map(item => ({ label: item.name, value: item.code }))"
                 placeholder="请选择证件类别"
                 :loading="dictionaryLoading"
               />
            </n-form-item-gi>
            <n-form-item-gi label="试驾人证件号" path="driverIdNumber">
              <n-input v-model:value="formModel.driverIdNumber" placeholder="请输入试驾人证件号" />
            </n-form-item-gi>
            <n-form-item-gi label="试驾人驾照号" path="driverLicenseNumber">
              <n-input v-model:value="formModel.driverLicenseNumber" placeholder="请输入驾照号" />
            </n-form-item-gi>
            <n-form-item-gi label="试驾开始里程数" path="startMileage">
              <n-input-number v-model:value="formModel.startMileage" :min="0" placeholder="请输入" style="width: 100%;" />
            </n-form-item-gi>
            <n-form-item-gi label="试驾结束里程数" path="endMileage">
              <n-input-number v-model:value="formModel.endMileage" :min="formModel.startMileage || 0" placeholder="请输入" style="width: 100%;" />
            </n-form-item-gi>
            <n-form-item-gi label="试驾开始时间" path="startTime">
              <n-date-picker v-model:value="formModel.startTime" type="datetime" placeholder="请选择开始时间" style="width: 100%;" />
            </n-form-item-gi>
            <n-form-item-gi label="试驾结束时间" path="endTime">
              <n-date-picker v-model:value="formModel.endTime" type="datetime" placeholder="请选择结束时间" style="width: 100%;" />
            </n-form-item-gi>
            <n-form-item-gi :span="2" label="试驾反馈">
              <n-input v-model:value="formModel.testDriveFeedback" type="textarea" placeholder="请输入试驾反馈" />
            </n-form-item-gi>
          </n-grid>
        </div>

        <el-row>
          <el-col :span="24" style="text-align: center; margin-top: 24px;">
            <el-space>
              <el-button type="primary" @click="handleSubmit" :loading="loading" size="large">保存</el-button>
              <el-button @click="handleReset" size="large">重置</el-button>
            </el-space>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { useDictionary, useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { getVehicleModelList, getVehicleVariantList } from '@/api/modules/masterData';
import type { VehicleModel, VehicleVariant } from '@/api/modules/masterData';
const formRef = ref<FormInstance | null>(null);
const loading = ref(false);

// 使用字典数据
const {
  options: idTypeOptions,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.ID_TYPE);

// 主数据
const vehicleModelOptions = ref<VehicleModel[]>([]);
const vehicleVariantOptions = ref<VehicleVariant[]>([]);
const masterDataLoading = ref(false);

const createInitialFormModel = () => ({
  // Prospect Info
  prospectSource: '',
  prospectName: '',
  prospectPhone: '',
  prospectIdType: null,
  prospectIdNumber: '',
  prospectEmail: '',
  prospectRegion: '',
  prospectAddress: '',

  // Test Drive Info
  salesAdvisorName: '',
  testDriveModel: null,
  testDriveVariant: null,
  driverName: '',
  driverPhone: '',
  driverIdType: null,
  driverIdNumber: '',
  driverLicenseNumber: '',
  startMileage: 0,
  endMileage: 0,
  startTime: null as number | null,
  endTime: null as number | null,
  testDriveFeedback: ''
});

const formModel = reactive(createInitialFormModel());

// Sync prospect info to driver info
watch(() => formModel.prospectName, (newName) => {
  if (!formModel.driverName) formModel.driverName = newName;
});
watch(() => formModel.prospectPhone, (newPhone) => {
  if (!formModel.driverPhone) formModel.driverPhone = newPhone;
});

const rules: FormRules = {
  prospectName: { required: true, message: '请输入潜客名称', trigger: 'blur' },
  prospectPhone: { required: true, message: '请输入潜客手机号', trigger: 'blur' },
  testDriveModel: { required: true, message: '请选择试驾车型', trigger: 'change' },
  testDriveVariant: { required: true, message: '请选择试驾配置', trigger: 'change' },
  driverName: { required: true, message: '请输入试驾人姓名', trigger: 'blur' },
  driverPhone: { required: true, message: '请输入试驾人手机号', trigger: 'blur' },
  driverIdType: { required: true, message: '请选择试驾人证件类别', trigger: 'change' },
  driverIdNumber: { required: true, message: '请输入试驾人证件号', trigger: 'blur' },
  driverLicenseNumber: { required: true, message: '请输入试驾人驾照号', trigger: 'blur' },
  startMileage: { required: true, type: 'number', message: '请输入开始里程数', trigger: 'blur' },
  endMileage: { required: true, type: 'number', message: '请输入结束里程数', trigger: 'blur' },
  startTime: { required: true, type: 'number', message: '请选择开始时间', trigger: 'blur' },
  endTime: { required: true, type: 'number', message: '请选择结束时间', trigger: 'blur' },
};

// 获取车型数据
const fetchVehicleModelData = async () => {
  try {
    masterDataLoading.value = true;
    const models = await getVehicleModelList();
    vehicleModelOptions.value = models;
  } catch (error) {
    console.error('获取车型数据失败:', error);
    ElMessage.error('获取车型数据失败');
    vehicleModelOptions.value = [];
  } finally {
    masterDataLoading.value = false;
  }
};

// 获取车型配置数据
const fetchVehicleVariantData = async () => {
  try {
    const variants = await getVehicleVariantList();
    vehicleVariantOptions.value = variants;
  } catch (error) {
    console.error('获取车型配置数据失败:', error);
    ElMessage.error('获取车型配置数据失败');
    vehicleVariantOptions.value = [];
  }
};

// 选项数据 - 使用字典和主数据接口
const modelOptions = computed(() => vehicleModelOptions.value.map(model => ({
  label: model.name,
  value: model.id
})));

const variantOptions = computed(() => vehicleVariantOptions.value.map(variant => ({
  label: variant.name,
  value: variant.id
})));

const handleSubmit = (e: MouseEvent) => {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      loading.value = true;
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      loading.value = false;
      ElMessage.success('新增潜客成功！');
      Object.assign(formModel, createInitialFormModel());
    } else {
      ElMessage.error('请检查表单是否完整');
    }
  });
};

const handleReset = () => {
  Object.assign(formModel, createInitialFormModel());
  ElMessage.info('表单已重置');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchVehicleModelData();
  fetchVehicleVariantData();
});
</script>

<style scoped lang="scss">
.add-prospect-page {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;

  .page-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .form-container {
    background-color: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  }

  .form-section {
    margin-bottom: 20px;
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      border-left: 4px solid #1890ff;
      padding-left: 12px;
    }
  }
}
</style>
