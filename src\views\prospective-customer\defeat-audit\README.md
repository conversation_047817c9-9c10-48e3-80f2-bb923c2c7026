# 战败审核列表页面

## 功能概述

战败审核列表页面用于销售经理审核销售顾问提交的潜客战败申请。页面支持查看申请详情、潜客跟进历史，并进行审核决策。

## 页面结构

### 1. 筛选区域
- **申请人**: 文本框，支持模糊查询
- **潜客姓名**: 文本框，支持模糊查询  
- **潜客手机号**: 文本框，支持模糊查询
- **申请时间**: 日期选择器，选择申请日期范围
- **审核结果**: 下拉框，选项：全部/待审核/审核通过/审核驳回

### 2. 功能按钮区域
- **左侧**: 导出按钮，支持导出当前筛选结果的Excel文件
- **右侧**: 标签切换按钮（待审核/已审核）

### 3. 数据表格
#### 待审核列表
- 选择框、序号、申请单号、申请人、潜客姓名、潜客手机号、申请时间、战败原因、操作（审核）

#### 已审核列表  
- 选择框、序号、申请单号、申请人、潜客姓名、潜客手机号、申请时间、战败原因、审核结果、审核时间、操作（详情）

### 4. 审核弹窗
- **申请信息区域**: 显示申请的基本信息
- **潜客跟进历史区域**: 显示该潜客的历史跟进记录
- **审核操作区域**: 选择审核结果（通过/驳回）和填写审核意见

### 5. 详情查看弹窗
- 显示战败申请的完整信息，包括审核结果和审核意见

## 文件结构

```
defeat-audit/
├── index.vue                    # 主页面
├── components/
│   ├── AuditModal.vue          # 审核弹窗组件
│   └── DetailModal.vue         # 详情查看弹窗组件
└── README.md                   # 说明文档
```

## API接口

### 类型定义
- `@/api/types/defeat-audit.ts` - 包含所有相关的TypeScript类型定义

### API模块
- `@/api/modules/defeat-audit.ts` - 包含所有API接口调用

### 主要接口
1. `getDefeatApplicationList` - 获取战败申请列表
2. `getDefeatApplicationDetail` - 获取战败申请详情
3. `getProspectFollowUpHistory` - 获取潜客跟进历史
4. `submitAudit` - 提交审核结果
5. `exportDefeatApplications` - 导出战败申请列表

## 权限控制

- **销售经理**: 可查看本门店所有销售顾问的战败申请，可进行审核操作
- **销售顾问**: 无权限访问此页面
- **厂端管理人员**: 可查看但不可操作

## 数据权限

- 按门店隔离：销售经理只能看到本门店的战败申请
- 历史记录：可查看已审核的历史记录，但不可修改

## 使用说明

1. 页面默认显示"待审核"标签页
2. 可通过筛选条件过滤数据
3. 点击"审核"按钮打开审核弹窗进行审核操作
4. 点击"详情"按钮查看已审核申请的详细信息
5. 支持导出当前筛选条件下的数据为Excel文件 