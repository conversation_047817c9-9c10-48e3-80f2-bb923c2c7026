<template>
  <el-dialog
    v-model="showModal"
    title="战败申请审核"
    width="900px"
    :close-on-click-modal="false"
    :modal="false"
  >
    <div class="audit-modal-content">
      <!-- 申请信息区域 -->
      <el-card class="info-card">
        <template #header>申请信息</template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请单号">
            {{ applicationData?.applicationNo || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ applicationData?.applicantName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ applicationData?.applicationTime ? formatDate(applicationData.applicationTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="潜客姓名">
            {{ applicationData?.prospectName ? maskName(applicationData.prospectName) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="潜客手机号">
            {{ applicationData?.prospectPhone ? maskPhone(applicationData.prospectPhone) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="战败原因">
            {{ applicationData?.defeatReason || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="详细说明">
            {{ applicationData?.defeatDescription || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 潜客跟进历史区域 -->
      <el-card class="history-card">
        <template #header>潜客跟进历史</template>
        <el-table
          :data="historyData"
          v-loading="historyLoading"
          size="small"
          max-height="300px"
          style="width: 100%"
        >
          <el-table-column prop="advisorName" label="顾问" width="100" />
          <el-table-column prop="followUpMethod" label="方式" width="80" />
          <el-table-column label="时间" width="140">
            <template #default="{ row }">
              {{ formatDate(row.followUpTime) }}
            </template>
          </el-table-column>
          <el-table-column label="级别" width="80">
            <template #default="{ row }">
              <el-tag :type="getTagType(row.prospectLevel)" size="small">
                {{ row.prospectLevel }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="followUpContent" label="跟进情况" min-width="200" show-overflow-tooltip />
        </el-table>
      </el-card>

      <!-- 审核操作区域 -->
      <el-card class="audit-card">
        <template #header>审核操作</template>
        <el-form
          ref="auditFormRef"
          :model="auditForm"
          :rules="auditRules"
          label-position="left"
          label-width="100px"
        >
          <el-form-item label="审核结果" prop="auditStatus" required>
            <el-radio-group v-model="auditForm.auditStatus">
              <el-space>
                <el-radio :label="AuditResult.APPROVED">通过</el-radio>
                <el-radio :label="AuditResult.REJECTED">驳回</el-radio>
              </el-space>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="审核意见"
            prop="auditComments"
            :required="auditForm.auditStatus === AuditResult.REJECTED"
          >
            <el-input
              v-model="auditForm.auditComments"
              type="textarea"
              placeholder="请输入审核意见（驳回时必填，最多500字）"
              :maxlength="500"
              show-word-limit
              :rows="4"
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ t('common.submit') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElForm } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { defeatAuditApi } from '@/api/modules/defeat-audit'
import {
  type DefeatApplication,
  AuditResult,
  type AuditDefeatApplicationRequest,
  type ApiResponse
} from '@/api/types/defeat-audit'
import { formatDate } from '@/utils/date-filter'
import { maskPhone, maskName } from '@/utils/data-mask'

// 定义跟进历史记录类型
interface ProspectFollowUpHistory {
  advisorName: string;
  followUpMethod: string;
  followUpTime: string;
  prospectLevel: string;
  followUpContent: string;
}

// 扩展API，添加获取跟进历史的方法
const getProspectHistory = async (prospectId: number): Promise<ApiResponse<ProspectFollowUpHistory[]>> => {
  // 这里使用Mock数据，实际项目中应该替换为真实API调用
  console.log(`获取潜客ID为${prospectId}的跟进历史`);
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData: ProspectFollowUpHistory[] = [
        {
          advisorName: '张顾问',
          followUpMethod: '电话',
          followUpTime: '2024-06-10 14:30:00',
          prospectLevel: 'H',
          followUpContent: '客户对价格有顾虑，需要进一步沟通'
        },
        {
          advisorName: '李顾问',
          followUpMethod: '短信',
          followUpTime: '2024-06-12 10:15:00',
          prospectLevel: 'M',
          followUpContent: '客户表示需要再考虑一下'
        }
      ];

      resolve({
        code: '200',
        message: '操作成功',
        result: mockData
      });
    }, 500);
  });
};

// Props定义
interface Props {
  show: boolean
  applicationData: DefeatApplication | null
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  applicationData: null
})

// Emits定义
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 国际化
const { t } = useI18n()

// 响应式数据
const auditFormRef = ref<InstanceType<typeof ElForm>>()
const submitLoading = ref(false)
const historyLoading = ref(false)
const historyData = ref<ProspectFollowUpHistory[]>([])

// 审核表单
const auditForm = reactive({
  auditStatus: AuditResult.APPROVED,
  auditComments: ''
})

// 表单验证规则
const auditRules = {
  auditStatus: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  auditComments: [
    {
      validator: (_: unknown, value: string, callback: (error?: Error) => void) => {
        if (auditForm.auditStatus === AuditResult.REJECTED && (!value || value.trim() === '')) {
          callback(new Error('驳回时必须填写审核意见'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取标签类型
const getTagType = (level: string) => {
  switch (level) {
    case 'H': return 'danger'
    case 'A': return 'warning'
    case 'B': return 'info'
    case 'C': return 'primary'
    case 'F': return 'danger'
    case 'O': return 'success'
    default: return 'primary'
  }
}

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 监听弹窗显示状态
watch(() => props.show, (newShow) => {
  if (newShow && props.applicationData) {
    resetForm()
    console.log('打开审核弹窗，申请数据:', props.applicationData)
    loadProspectHistory()
  }
})

// 重置表单
const resetForm = () => {
  auditForm.auditStatus = AuditResult.APPROVED
  auditForm.auditComments = ''
  auditFormRef.value?.resetFields()
}

// 加载潜客跟进历史
const loadProspectHistory = async () => {
  if (!props.applicationData) return

  if (!props.applicationData.prospectId) {
    console.error('无法加载跟进历史：缺少潜客ID')
    ElMessage.error('无法加载跟进历史：缺少潜客ID')
    return
  }

  historyLoading.value = true

  try {
    // 使用新增的方法获取历史记录
    const response = await getProspectHistory(props.applicationData.prospectId)

    if (response && response.code === '200' && response.result) {
      historyData.value = response.result
    } else {
      console.error('API返回错误:', response?.message || '未知错误')
      ElMessage.error(response?.message || '加载跟进历史失败')
      historyData.value = []
    }
  } catch (error) {
    console.error('加载跟进历史失败:', error)
    ElMessage.error('加载跟进历史失败')
    historyData.value = []
  } finally {
    historyLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  showModal.value = false
}

// 提交审核
const handleSubmit = async () => {
  if (!auditFormRef.value) return

  try {
    await auditFormRef.value.validate()

    if (!props.applicationData?.id) {
      ElMessage.error('缺少申请ID，无法提交审核')
      return
    }

    submitLoading.value = true

    // 构建审核请求参数
    const auditRequest: AuditDefeatApplicationRequest = {
      applicationId: props.applicationData.id,
      auditStatus: auditForm.auditStatus,
      auditComments: auditForm.auditComments
    }

    const response = await defeatAuditApi.auditDefeatApplication(auditRequest)

    if (response && response.code === '200') {
      ElMessage.success('审核提交成功')
      showModal.value = false
      emit('success')
    } else {
      ElMessage.error(response?.message || '审核提交失败')
    }
  } catch (error) {
    console.error('审核提交失败:', error)
    if (error instanceof Error && error.message !== 'Validation failed') {
      ElMessage.error('审核提交失败')
    }
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.audit-modal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card,
.history-card,
.audit-card {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
