<template>
  <el-dialog
    v-model="showModal"
    :title="'潜客详情'"
    :before-close="handleClose"
    width="80%"
    style="max-width: 1200px"
  >
    <!-- 详情标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane name="basic" label="基本信息">
        <div class="info-grid">
          <div class="info-item" v-for="(item, index) in basicInfo" :key="index">
            <span class="info-label">{{ item.label }}:</span>
            <span class="info-value">{{ item.value }}</span>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane name="stores" label="门店关联">
        <div class="store-association">
          <h4>关联门店记录 (共{{ storeAssociations.associatedStoreCount || 0 }}家门店)</h4>
          <div class="association-item" v-for="(item, index) in storeAssociations.storeAssociationRecords" :key="index">
            <div class="store-info">
              <div class="store-name">{{ item.storeName }}</div>
              <div class="store-details">
                关联时间: {{ item.leadAssociationTime }} | 关联原因: {{ item.associationReason }} | 当前顾问: {{ item.currentSalesAdvisor }}
              </div>
            </div>
            <div class="followup-status">
              <div>
                <el-tag :type="getIntentLevelType(item.currentIntentLevel)">{{ item.currentIntentLevel }}级</el-tag>
              </div>
              <div class="last-followup">
                {{ item.lastFollowUpTime ? `最后跟进: ${item.lastFollowUpTime}`  : '暂无跟进' }}
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane name="followup" label="跟进记录">
        <el-table
          :data="followUpRecords.followUpRecords"
          border
          stripe
          style="width: 100%"
          :default-sort="{prop: 'followUpTime', order: 'descending'}"
        >
          <el-table-column prop="storeName" label="门店" />
          <el-table-column prop="salesAdvisor" label="销售顾问" />
          <el-table-column prop="followUpMethod" label="跟进方式" />
          <el-table-column prop="followUpTime" label="跟进时间" sortable />
          <el-table-column prop="intentLevel" label="意向级别">
            <template #default="{ row }">
              <el-tag :type="getIntentLevelType(row.intentLevel)">{{ row.intentLevel }}级</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="followUpDetails" label="跟进情况" />
        </el-table>
        <el-pagination
          v-if="followUpRecords.followUpRecords && followUpRecords.followUpRecords.length > 10"
          layout="prev, pager, next"
          :total="followUpRecords.totalCount"
          :page-size="10"
          class="mt-20"
        />
      </el-tab-pane>

      <el-tab-pane name="testdrive" label="试驾记录">
        <el-table
          :data="testDriveRecords.testDriveRecords"
          border
          stripe
          style="width: 100%"
          :default-sort="{prop: 'testDriveTime', order: 'descending'}"
        >
          <el-table-column prop="storeName" label="门店" />
          <el-table-column prop="driverName" label="试驾人" />
          <el-table-column prop="phoneNumber" label="手机号" />
          <el-table-column prop="model" label="试驾车型Model" />
          <el-table-column prop="variant" label="配置Variant" />
          <el-table-column prop="试驾时间" label="testDriveTime" />
          <el-table-column prop="customerFeedback" label="客户反馈" />
        </el-table>
        <el-pagination
          v-if="testDriveRecords.testDriveRecords && testDriveRecords.testDriveRecords.length > 10"
          layout="prev, pager, next"
          :total="testDriveRecords.totalCount"
          :page-size="10"
          class="mt-20"
        />
      </el-tab-pane>

      <el-tab-pane name="failed" label="战败记录">
        <el-table
          :data="defeatRecords.defeatRecords"
          border
          stripe
          style="width: 100%"
          :default-sort="{prop: 'defeatTime', order: 'descending'}"
        >
          <el-table-column prop="applyTime" label="申请时间" />
          <el-table-column prop="storeName" label="门店" />
          <el-table-column prop="applicantName" label="申请人" />
          <el-table-column prop="applicationStatus" label="审核状态">
            <template #default="{ row }">
              <el-tag :type="row.applicationStatus === '已批准' ? 'success' : row.applicationStatus === '已拒绝' ? 'danger' : 'info'">
                {{ row.applicationStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="auditTime" label="审核时间" />
          <el-table-column prop="auditorName" label="审核人" />
          <el-table-column prop="defeatReason" label="战败原因" />
        </el-table>
        <el-pagination
          v-if="defeatRecords.defeatRecords && defeatRecords.defeatRecords.length > 10"
          layout="prev, pager, next"
          :total="defeatRecords.totalCount"
          :page-size="10"
          class="mt-20"
        />
      </el-tab-pane>

      <el-tab-pane name="history" label="变更历史">
        <el-table
          :data="changeHistory.changeHistoryRecords"
          border
          stripe
          style="width: 100%"
          :default-sort="{prop: 'changedTime', order: 'descending'}"
        >
          <el-table-column prop="changeTime" label="变更时间" sortable />
          <el-table-column prop="storeName" label="门店" />
          <el-table-column prop="changeType" label="变更类型" />
          <el-table-column prop="operatorName" label="操作人" />
          <el-table-column prop="originalValue" label="变更前" />
          <el-table-column prop="newValue" label="变更后" />
          <el-table-column prop="changeReason" label="变更原因" />
        </el-table>
        <el-pagination
          v-if="changeHistory.changeHistoryRecords && changeHistory.changeHistoryRecords.length > 10"
          layout="prev, pager, next"
          :total="changeHistory.totalCount"
          :page-size="10"
          class="mt-20"
        />
      </el-tab-pane>

      <el-tab-pane name="analytics" label="效果分析">
        <div class="stats-container">
          <div class="stats-card">
            <h3>跟进总次数</h3>
            <div class="number">{{ performanceAnalysis.totalFollowUpCount || 0 }}</div>
            <div class="trend">
              <span v-if="performanceAnalysis.totalAssociatedStoreCount > 0">跨{{ performanceAnalysis.totalAssociatedStoreCount }}家门店</span>
              <span v-else>暂无门店数据</span>
            </div>
          </div>
          <div class="stats-card">
            <h3>最活跃门店</h3>
            <div class="number">{{ performanceAnalysis.mostActiveStoreName || '无' }}</div>
            <div class="trend">
              {{ performanceAnalysis.mostActiveStoreFollowUpCount ? `跟进${performanceAnalysis.mostActiveStoreFollowUpCount}次` : '暂无跟进' }}
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineExpose } from 'vue';
import { ElMessage } from 'element-plus';
import {
  getCustomerBasicInfo,
  getStoreAssociations,
  getFollowUpRecords,
  getTestDriveRecords,
  getDefeatRecords,
  getChangeHistory,
  getPerformanceAnalysis
} from '@/api/modules/factory-prospect';
import type {
  CustomerBasicInfoResponse,
  StoreAssociationsResponse,
  FollowUpRecordsResponse,
  TestDriveRecordsResponse,
  DefeatRecordsResponse,
  ChangeHistoryResponse,
  PerformanceAnalysisResponse
} from '@/api/types/factory-prospect';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  customerId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:show']);

const showModal = ref(props.show);
const activeTab = ref('basic');

// 监听 show 属性变化
watch(() => props.show, async (newVal) => {
  showModal.value = newVal;
  if (newVal && props.customerId) {
    activeTab.value = 'basic';
    await loadBasicInfo();
  }
});

// 监听 showModal 变化，通知父组件
watch(showModal, (newVal) => {
  emit('update:show', newVal);
});

// 监听 customerId 变化，加载数据
watch(() => props.customerId, async (newVal) => {
  if (newVal && showModal.value) {
    activeTab.value = 'basic';
    await loadBasicInfo();
  }
}, { immediate: true });

// 监听activeTab变化，加载对应数据
watch(activeTab, async () => {
  if (props.customerId && showModal.value) {
    await handleTabChange();
  }
});

// 基本信息
const basicInfo = ref<{ label: string; value: string }[]>([]);
const customerBasicInfo = ref<CustomerBasicInfoResponse>({
  customerId: '',
  customerName: '',
  phoneNumber: '',
  idType: '',
  idNumber: '',
  email: '',
  registerTime: '',
  registerSource: '',
  currentStatus: ''
});

// 门店关联
const storeAssociations = ref<StoreAssociationsResponse>({
  associatedStoreCount: 0,
  storeAssociationRecords: []
});

// 跟进记录
const followUpRecords = ref<FollowUpRecordsResponse>({
  totalCount: 0,
  earliestTime: '',
  latestTime: '',
  followUpRecords: []
});

// 试驾记录
const testDriveRecords = ref<TestDriveRecordsResponse>({
  totalCount: 0,
  testDriveRecords: []
});

// 战败记录
const defeatRecords = ref<DefeatRecordsResponse>({
  totalCount: 0,
  defeatRecords: []
});

// 变更历史
const changeHistory = ref<ChangeHistoryResponse>({
  totalCount: 0,
  changeHistoryRecords: []
});

// 绩效分析
const performanceAnalysis = ref<PerformanceAnalysisResponse>({
  totalFollowUpCount: 0,
  totalAssociatedStoreCount: 0,
  mostActiveStoreName: '',
  mostActiveStoreFollowUpCount: 0
});

// 加载基本信息
const loadBasicInfo = async () => {
  try {
    const res = await getCustomerBasicInfo({ globalCustomerId: props.customerId }) as any;
    console.log('基本信息数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    customerBasicInfo.value = data;

    basicInfo.value = [
      { label: '客户ID', value: data.globalCustomerId || '' },
      { label: '客户姓名', value: data.customerName || '' },
      { label: '手机号', value: data.customerPhone || '' },
      { label: '证件类型', value: data.idType || '' },
      { label: '证件号码', value: data.idNumber || '' },
      { label: '邮箱', value: data.email || '' },
      { label: '注册时间', value: data.registrationTime || '' },
      { label: '注册来源', value: data.sourceChannel || '' },
      { label: '当前状态', value: data.currentStatus || '' }
    ];
  } catch (error) {
    console.error('获取客户基本信息失败', error);
    ElMessage.error('获取客户基本信息失败');
  }
};

// 加载门店关联
const loadStoreAssociations = async () => {
  try {
    const res = await getStoreAssociations({ globalCustomerId: props.customerId }) as any;
    console.log('门店关联数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    storeAssociations.value = data;
  } catch (error) {
    console.error('获取门店关联信息失败', error);
    ElMessage.error('获取门店关联信息失败');
  }
};

// 根据选项卡变化加载对应数据
const handleTabChange = async () => {
  switch (activeTab.value) {
    case 'basic':
      await loadBasicInfo();
      break;
    case 'stores':
      await loadStoreAssociations();
      break;
    case 'followup':
      await loadFollowUpRecords();
      break;
    case 'testdrive':
      await loadTestDriveRecords();
      break;
    case 'failed':
      await loadDefeatRecords();
      break;
    case 'history':
      await loadChangeHistory();
      break;
    case 'analytics':
      await loadPerformanceAnalysis();
      break;
  }
};

// 加载跟进记录
const loadFollowUpRecords = async () => {
  try {
    const res = await getFollowUpRecords({ storeProspectId: props.customerId }) as any;
    console.log('跟进记录数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    followUpRecords.value = data;
  } catch (error) {
    console.error('获取跟进记录失败', error);
    ElMessage.error('获取跟进记录失败');
  }
};

// 加载试驾记录
const loadTestDriveRecords = async () => {
  try {
    const res = await getTestDriveRecords({ storeProspectId: props.customerId }) as any;
    console.log('试驾记录数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    testDriveRecords.value = data;
  } catch (error) {
    console.error('获取试驾记录失败', error);
    ElMessage.error('获取试驾记录失败');
  }
};

// 加载战败记录
const loadDefeatRecords = async () => {
  try {
    const res = await getDefeatRecords({ storeProspectId: props.customerId }) as any;
    console.log('战败记录数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    defeatRecords.value = data;
  } catch (error) {
    console.error('获取战败记录失败', error);
    ElMessage.error('获取战败记录失败');
  }
};

// 加载变更历史
const loadChangeHistory = async () => {
  try {
    const res = await getChangeHistory({ storeProspectId: props.customerId }) as any;
    console.log('变更历史数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    changeHistory.value = data;
  } catch (error) {
    console.error('获取变更历史失败', error);
    ElMessage.error('获取变更历史失败');
  }
};

// 加载绩效分析
const loadPerformanceAnalysis = async () => {
  try {
    const res = await getPerformanceAnalysis({ globalCustomerId: props.customerId }) as any;
    console.log('绩效分析数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    performanceAnalysis.value = data;
  } catch (error) {
    console.error('获取绩效分析失败', error);
    ElMessage.error('获取绩效分析失败');
  }
};

// 关闭模态框
const handleClose = () => {
  showModal.value = false;
};

// 获取意向级别对应的标签类型
const getIntentLevelType = (level: string): 'success' | 'warning' | 'danger' | '' => {
  switch (level) {
    case 'H级':
      return 'success';
    case 'A级':
      return 'warning';
    case 'B级':
      return '';
    case 'C级':
      return 'danger';
    default:
      return '';
  }
};

// 暴露方法给父组件
defineExpose({
  loadBasicInfo,
  loadStoreAssociations
});
</script>

<style scoped>
.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.info-item {
  margin-bottom: 12px;
}

.info-label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.info-value {
  color: #303133;
}

.store-association {
  margin-top: 16px;
}

.association-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 12px;
}

.store-name {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  margin-bottom: 4px;
}

.store-details {
  font-size: 13px;
  color: #606266;
}

.followup-status {
  text-align: right;
}

.last-followup {
  margin-top: 6px;
  font-size: 12px;
  color: #909399;
}

.stats-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 16px;
}

.stats-card {
  flex-basis: calc(25% - 20px);
  min-width: 250px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-card h3 {
  margin-top: 0;
  color: #606266;
  font-size: 14px;
  font-weight: normal;
}

.stats-card .number {
  font-size: 28px;
  color: #303133;
  margin: 10px 0;
}

.stats-card .trend {
  font-size: 13px;
  color: #909399;
}

.mt-20 {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .stats-card {
    flex-basis: 100%;
  }
}
</style>
