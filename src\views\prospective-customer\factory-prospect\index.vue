<template>
  <div class="factory-prospect-container">
    <h1 class="page-title">厂端潜客池管理</h1>

    <!-- 统计概览 -->
    <div class="stats-container">
      <div class="stats-card">
        <h3>总线索数</h3>
        <div class="number">{{ stats.totalLeadCount || 0 }}</div>
        <div class="trend">
          较上月 {{ stats.totalLeadCountVsLastMonth > 0 ? '+' : '' }}{{ stats.totalLeadCountVsLastMonth || 0 }}%
        </div>
      </div>
      <div class="stats-card">
        <h3>H级潜客</h3>
        <div class="number">{{ stats.hLevelProspectCount || 0 }}</div>
        <div class="trend">
          较上月 {{ stats.hLevelProspectCountVsLastMonth > 0 ? '+' : '' }}{{ stats.hLevelProspectCountVsLastMonth || 0 }}%
        </div>
      </div>
      <div class="stats-card">
        <h3>本月转化</h3>
        <div class="number">{{ stats.monthlyConversionProspectCount || 0 }}</div>
        <div class="trend">转化率 {{ stats.monthlyConversionRate || '0' }}%</div>
      </div>
      <div class="stats-card">
        <h3>跨门店潜客</h3>
        <div class="number">{{ stats.crossStoreCustomerCount || 0 }}</div>
        <div class="trend">占比 {{ stats.crossStoreCustomerRatio || '0' }}%</div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-section">
      <el-form
        ref="formRef"
        :model="filterForm"
        label-position="top"
        :show-message="false"
      >
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="潜客ID">
              <el-input v-model="filterForm.leadId" placeholder="请输入潜客ID" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="门店">
              <el-select
                v-model="filterForm.storeId"
              placeholder="全部门店"
              clearable
              >
                <el-option
                  v-for="option in storeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="关联门店数">
              <el-select
                v-model="filterForm.storeCount"
              placeholder="全部"
              clearable
              >
                <el-option
                  v-for="option in storeCountOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="注册时间">
              <el-date-picker
                v-model="dateRange"
              type="daterange"
              clearable
              style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
            />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20">
    <div class="action-section">
      <div>
          <el-radio-group v-model="filterForm.viewType" @change="changeViewType">
            <el-radio-button label="all">全部潜客</el-radio-button>
            <el-radio-button label="cross_store">跨门店潜客</el-radio-button>
            <el-radio-button label="defeated">战败潜客</el-radio-button>
            <el-radio-button label="converted">已转化</el-radio-button>
          </el-radio-group>
      </div>
      <div>
          <el-space>
            <el-button type="primary" @click="handleSearch" :icon="Search">
          查询
            </el-button>
            <el-button @click="resetForm" :icon="Refresh">
          重置
            </el-button>
            <el-button @click="exportData" :icon="Download">
              导出数据
            </el-button>
          </el-space>
        </div>
      </div>
    </el-card>

    <!-- 潜客列表 -->
    <el-card class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        stripe
        height="600"
        style="width: 100%"
        :row-key="(row: any) => row.globalCustomerId"
      >
        <el-table-column type="index" label="序号" width="60" fixed="left" />
        <el-table-column prop="leadId" label="潜客ID" min-width="120" />
        <el-table-column prop="customerName" label="潜客姓名" min-width="100" />
        <el-table-column prop="phoneNumber" label="手机号" min-width="130">
          <template #default="{ row }">
            {{ row.phoneNumber || '暂无' }}
          </template>
        </el-table-column>
        <el-table-column prop="associatedStoreCount" label="关联门店数" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.associatedStoreCount > 1 ? 'warning' : 'info'">
              {{ row.associatedStoreCount }}家门店
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="registrationTime" label="注册时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.registrationTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="prospectStatus" label="潜客状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.prospectStatus)">
              {{ row.prospectStatus || '未转化' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              link
              @click="showLeadDetail(row.globalCustomerId)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section mt-20">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.itemCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 潜客详情模态框 -->
    <customer-detail-modal
      v-model:show="showDetailModal"
      :customer-id="currentCustomerId"
    />

    <!-- 意向级别配置模态框 -->
    <intent-level-config-modal
      v-model:show="showIntentLevelConfigModal"
    />
  </div>
</template>

<script setup lang="ts" name="FactoryProspectManagement">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Refresh, Download } from '@element-plus/icons-vue';
import { getFactoryOverviewStats, getFactoryProspectList, exportFactoryProspectData } from '@/api/modules/factory-prospect';
import type { FactoryOverviewStatsResponse, FactoryProspectItem } from '@/api/types/factory-prospect';
import CustomerDetailModal from './components/CustomerDetailModal.vue';
import IntentLevelConfigModal from './components/IntentLevelConfigModal.vue';
// import { formatDate } from '@/utils/dateUtils';

// 选择框选项类型
interface SelectOption {
  label: string;
  value: string | number;
}

// 分页前缀参数类型
interface PaginationPrefixParams {
  itemCount: number;
}

// 保留状态标签类型函数
const getStatusTagType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'following': 'warning',     // 跟进中
    'converted': 'success',     // 已成交
    'no_intention': 'danger'    // 无意向
  };
  return typeMap[status] || 'info';  // 确保总是返回有效的type值
}; // 删除 getLevelTagType 函数，因为不再需要潜客级别显示

// 统计数据
const stats = ref<FactoryOverviewStatsResponse>({
  totalLeadCount: 0,
  hLevelProspectCount: 0,
  monthlyConversionProspectCount: 0,
  crossStoreCustomerCount: 0,
  totalLeadCountVsLastMonth: 0,
  hLevelProspectCountVsLastMonth: 0,
  monthlyConversionRate: '0%',
  crossStoreCustomerRatio: '0%'
});

// 筛选表单
const filterForm = reactive({
  leadId: '',
  storeId: '',
  storeCount: '',
  registrationTimeStart: '',
  registrationTimeEnd: '',
  viewType: 'all' as 'all' | 'cross_store' | 'defeated' | 'converted',
  pageNum: 1,
  pageSize: 20
});

// 日期范围
const dateRange = ref<[number, number] | null>(null);

// 监听日期范围变化（用watch实现）
watch(dateRange, (val) => {
  if (val) {
    filterForm.registrationTimeStart = new Date(val[0]).toISOString().split('T')[0];
    filterForm.registrationTimeEnd = new Date(val[1]).toISOString().split('T')[0];
  } else {
    filterForm.registrationTimeStart = '';
    filterForm.registrationTimeEnd = '';
  }
});

// 门店选项
const storeOptions = ref<SelectOption[]>([
  { label: '全部门店', value: '' },
  { label: '北京朝阳店', value: 1 },
  { label: '上海浦东店', value: 2 },
  { label: '深圳南山店', value: 3 }
]);

// 关联门店数选项
const storeCountOptions = ref<SelectOption[]>([
  { label: '全部', value: '' },
  { label: '单店', value: 1 },
  { label: '多店', value: 2 }
]);

// 表格数据
const tableData = ref<FactoryProspectItem[]>([]);
const loading = ref(false);

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  pageSizes: [10, 20, 50, 100],
  showSizePicker: true,
  prefix({ itemCount }: PaginationPrefixParams) {
    return `共 ${itemCount} 条记录`;
  }
});

// 表格列配置已移至模板中

// 模态框状态
const showDetailModal = ref(false);
const showIntentLevelConfigModal = ref(false);
const currentCustomerId = ref('');

// 获取统计数据
const fetchStats = async () => {
  try {
    // 构建统计请求参数
    const statsParams = {
      storeId: filterForm.storeId || undefined,
      startDate: filterForm.registrationTimeStart || undefined,
      endDate: filterForm.registrationTimeEnd || undefined
    };

    const res = await getFactoryOverviewStats(statsParams);

    // 检查返回数据格式，适配不同的后端返回结构
    if (res && typeof res === 'object') {
      if ('result' in res && res.result) {
        stats.value = res.result as FactoryOverviewStatsResponse;
      } else if ('data' in res && res.data) {
        stats.value = res.data as FactoryOverviewStatsResponse;
      } else {
        console.log('API统计数据返回结构:', res);
        // 保持默认值
      }
    }
  } catch (error) {
    console.error('获取统计数据失败', error);
    ElMessage.error('获取统计数据失败');
  }
};

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  try {
    const res = await getFactoryProspectList({
      ...filterForm,
      pageNum: pagination.page,
      pageSize: pagination.pageSize
    });

    // 检查返回数据格式，适配不同的后端返回结构
    if (res && typeof res === 'object') {
      if ('result' in res && res.result) {
        const result = res.result as Record<string, any>;
        if ('records' in result) {
          // 标准返回格式
          tableData.value = result.records as FactoryProspectItem[];
          pagination.itemCount = result.total as number || 0;
          pagination.page = result.current as number || 1;
          pagination.pageSize = result.size as number || 20;
        }
      } else if ('data' in res && res.data) {
        const data = res.data as Record<string, any>;
        if ('records' in data) {
          // 另一种可能的返回格式
          tableData.value = data.records as FactoryProspectItem[];
          pagination.itemCount = data.total as number || 0;
          pagination.page = data.current as number || 1;
          pagination.pageSize = data.size as number || 20;
        } else if (Array.isArray(res.data)) {
          // 直接返回数组的情况
          tableData.value = res.data as FactoryProspectItem[];
          pagination.itemCount = res.data.length;
        }
      } else {
        // 其他情况，尝试解析
        console.log('API返回数据结构:', res);
        tableData.value = [];
        pagination.itemCount = 0;
      }
    }
  } catch (error) {
    console.error('获取潜客列表失败', error);
    ElMessage.error('获取潜客列表失败');
  } finally {
    loading.value = false;
  }
};

// 切换视图类型
const changeViewType = (type: 'all' | 'cross_store' | 'defeated' | 'converted') => {
  filterForm.viewType = type;
  pagination.page = 1;
  fetchTableData();
};

// 查询
const handleSearch = () => {
  pagination.page = 1;
  fetchTableData();
  fetchStats(); // 同时更新统计数据
};

// 重置表单
const resetForm = () => {
  filterForm.leadId = '';
  filterForm.storeId = '';
  filterForm.storeCount = '';
  dateRange.value = null;
  filterForm.registrationTimeStart = '';
  filterForm.registrationTimeEnd = '';
  ElMessage.success('筛选条件已重置');

  // 重置后重新加载数据
  fetchTableData();
  fetchStats();
};

// 导出数据
const exportData = async () => {
  try {
    ElMessage.info('开始导出数据...');

    const exportFields = [
      { key: 'leadId', label: '潜客ID' },
      { key: 'customerName', label: '潜客姓名' },
      { key: 'phoneNumber', label: '手机号' },
      { key: 'associatedStoreCount', label: '关联门店数' },
      { key: 'registrationTime', label: '注册时间' },
      { key: 'prospectStatus', label: '潜客状态' }
      // 确保没有 currentIntentLevel 字段
    ];

    await exportFactoryProspectData({
      ...filterForm,
      fields: exportFields
    });

    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败', error);
    ElMessage.error('导出失败');
  }
};

// 分页变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchTableData();
};

// 每页条数变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchTableData();
};

// 显示潜客详情
const showLeadDetail = (id: string) => {
  currentCustomerId.value = id;
  showDetailModal.value = true;
};

// 初始化
onMounted(() => {
  fetchStats();
  fetchTableData();
});

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  // if (!dateTime) return '暂无';
  // return formatDate(dateTime, 'YYYY-MM-DD HH:mm');
  return dateTime;
};
</script>

<style scoped>
.factory-prospect-container {
  padding: 0;
  width: 100%;
}

.page-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--text-primary, #333333);
}

/* 统计卡片样式 */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  background: #ffffff;
  border-radius: 6px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid #1890ff;
}

.stats-card h3 {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #666666;
  font-weight: normal;
}

.stats-card .number {
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 5px;
}

.stats-card .trend {
  font-size: 12px;
  color: #52c41a;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 16px;
  width: 100%;
}

/* 功能按钮区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  width: 100%;
}

.ml-2 {
  margin-left: 8px;
}

/* 表格容器 */
.table-container {
  margin-bottom: 16px;
  width: 100%;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 添加一些辅助类 */
.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: flex-end;
}
</style>
