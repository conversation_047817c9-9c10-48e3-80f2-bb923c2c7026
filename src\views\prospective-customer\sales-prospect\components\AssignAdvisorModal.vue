<template>
  <el-dialog v-model="visible"     :title="t('changeAdvisorModalTitle')" width="600px" :close-on-click-modal="false">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('prospectName')">
            <el-input :value="prospectData?.customerName || '-'" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('prospectPhone')">
            <el-input :value="prospectData?.customerPhone || '-'" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('currentAdvisorName')">
            <el-input
              :value="prospectData?.currentSalesAdvisorName || '-'"
              readonly
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('currentAdvisorId')">
            <el-input
              :value="prospectData?.currentSalesAdvisorId || '-'"
              readonly
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('changeAdvisorName')" prop="newAdvisorId">
            <el-select
              v-model="formData.newAdvisorId"
              :placeholder="t('selectNewAdvisor')"
              filterable
              clearable
              :loading="advisorLoading"
              style="width: 100%"
            >
              <el-option
                v-for="option in filteredAdvisorOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('changeAdvisorId')">
            <el-input
              :value="formData.newAdvisorId || ''"
              readonly
              :placeholder="t('selectNewAdvisor')"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item :label="t('changeReason')" prop="changeReason">
        <el-input
          v-model="formData.changeReason"
          type="textarea"
          :rows="3"
          :placeholder="t('inputChangeReason')"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">{{ tc('cancel') }}</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">{{ tc('save') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { storeProspectApi } from '@/api/modules/prospective-customer'
import { type ProspectBaseInfo } from '@/types/prospective-customer.d'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales')

// Props定义
interface Props {
  show: boolean
  prospectData: ProspectBaseInfo | null
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  prospectData: null
})

// Emits定义
interface Emits {
  (event: 'update:show', value: boolean): void
  (event: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const formRef = ref<FormInstance | null>(null)
const loading = ref(false)
const advisorLoading = ref(false)

// 表单数据
const formData = reactive({
  newAdvisorId: '',
  changeReason: ''
})

// 销售顾问选项（模拟数据，实际应从API获取）
const advisorOptions = ref([
  { label: '张顾问', value: 'EMP001' },
  { label: '李顾问', value: 'EMP002' },
  { label: '王顾问', value: 'EMP003' },
  { label: '赵顾问', value: 'EMP004' },
  { label: '刘顾问', value: 'EMP005' }
])

// 过滤掉当前顾问
const filteredAdvisorOptions = computed(() => {
  if (!props.prospectData?.currentSalesAdvisorId) {
    return advisorOptions.value
  }
  return advisorOptions.value.filter(
    option => option.value !== props.prospectData?.currentSalesAdvisorId
  )
})

// 表单验证规则
const rules: FormRules = {
  newAdvisorId: [
    { required: true, message: t('selectNewAdvisorRequired'), trigger: 'change' }
  ],
  changeReason: [
    { required: true, message: t('inputChangeReasonRequired'), trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  formData.newAdvisorId = ''
  formData.changeReason = ''
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || !props.prospectData) return

  try {
    await formRef.value.validate()

    // 检查是否选择了相同的顾问
    if (formData.newAdvisorId === props.prospectData.currentSalesAdvisorId) {
      ElMessage.error(t('newAdvisorCannotBeSame'))
      return
    }

    loading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(t('changeAdvisorSuccess'))
    emit('success')
    visible.value = false
  } catch (error) {
    if (error instanceof Error) {
      console.error('变更顾问失败:', error)
      ElMessage.error(t('changeFailed'))
    }
  } finally {
    loading.value = false
  }
}

// 监听对话框显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 加载销售顾问列表（实际应该从API获取）
const loadAdvisorOptions = async () => {
  advisorLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    // advisorOptions.value = await api.getAdvisorList()
  } catch (error) {
    ElMessage.error(tc('getDataFailed'))
  } finally {
    advisorLoading.value = false
  }
}

onMounted(() => {
  loadAdvisorOptions()
})
</script>

<style scoped>
.current-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.change-info {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
}
</style>
