<template>
  <el-dialog v-model="visible" :title="t('prospectDetails')" width="80%" :close-on-click-modal="false" class="prospect-detail-dialog">
    <div v-loading="loading" class="prospect-detail">
      <div v-if="detailData">
        <!-- 潜客信息 section -->
        <div class="section">
          <div class="section-title">{{ t('prospectInfo') }}</div>

          <div class="field-grid">
            <div class="field-item">
              <div class="field-label">{{ t('prospectId') }}</div>
              <div class="field-value">{{ detailData.id || '' }}</div>
            </div>

            <div class="field-item">
              <div class="field-label">{{ t('prospectSource') }}</div>
              <div class="field-value">{{ getSourceChannelName(detailData.sourceChannel || '') || 'Super App' }}</div>
            </div>

            <div class="field-item">
              <div class="field-label">{{ t('prospectStatus') }}</div>
              <div class="field-value">{{ getNameByCode(DICTIONARY_TYPES.PROSPECT_STATUS, detailData.prospectStatus || '') }}</div>
            </div>

            <div class="field-item">
              <div class="field-label">{{ t('prospectName') }}</div>
              <div class="field-value">{{ detailData.customerName  }}</div>
            </div>

            <div class="field-item">
              <div class="field-label">{{ t('prospectPhone') }}</div>
              <div class="field-value">{{ detailData.customerPhone  }}</div>
            </div>

            <div class="field-item">
              <div class="field-label">{{ t('idDocumentType') }}</div>
              <div class="field-value">{{ getNameByCode(DICTIONARY_TYPES.ID_TYPE, detailData.idType || '')  }}</div>
            </div>

            <div class="field-item">
              <div class="field-label">{{ t('idDocumentNumber') }}</div>
              <div class="field-value">{{ detailData.idNumber }}</div>
            </div>

            <div class="field-item">
              <div class="field-label">{{ t('email') }}</div>
              <div class="field-value">{{ detailData.email  }}</div>
            </div>

            <div class="field-item">
              <div class="field-label">{{ t('region') }}</div>
              <div class="field-value">{{ detailData.region  }}</div>
            </div>
          </div>

          <div class="field-item address">
            <div class="field-label">{{ t('address') }}</div>
            <div class="field-value">{{ detailData.address }}</div>
          </div>
        </div>

        <!-- 潜客意向 section -->
        <div class="section">
          <div class="section-title">{{ t('prospectIntention') }}</div>
          <table class="custom-table">
            <thead>
              <tr>
                <th>{{ t('intentModel') }}</th>
                <th>{{ t('intentVariant') }}</th>
                <th>{{ t('intentColor') }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{{ detailData.intentModel  }}</td>
                <td>{{ detailData.intentVariant  }}</td>
                <td>{{ detailData.intentColor  }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 试驾记录 section -->
        <div class="section">
          <div class="section-title">{{ t('testDriveRecord') }}</div>
          <table class="custom-table">
            <thead>
              <tr>
                <th>{{ t('driver') }}</th>
                <th>{{ t('phoneNumber') }}</th>
                <th>{{ t('model') }}</th>
                <th>{{ t('time') }}</th>
                <th>{{ t('feedback') }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(record, index) in testDriveRecords" :key="index">
                <td>{{ record.driverName }}</td>
                <td>{{ record.phoneNumber }}</td>
                <td>{{ record.testDriveModel }}</td>
                <td>{{ formatDate(record.testDriveTime) }}</td>
                <td>{{ record.testDriveFeedback }}</td>
              </tr>
              <tr v-if="testDriveRecords.length === 0">
                <td class="empty-data" colspan="5">{{ t('noTestDriveRecord') }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 跟进记录 section -->
        <div class="section">
          <div class="section-title">{{ t('followUpRecord') }}</div>
          <table class="custom-table">
            <thead>
              <tr>
                <th>{{ t('currentAdvisorId') }}</th>
                <th>{{ t('currentAdvisorName') }}</th>
                <th>{{ t('followUpMethod') }}</th>
                <th>{{ t('time') }}</th>
                <th>{{ t('prospectLevel') }}</th>
                <th>{{ t('followUpDetails') }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(record, index) in followUpRecords" :key="index">
                <td>{{ record.salesAdvisorId }}</td>
                <td>{{ record.salesAdvisorName }}</td>
                <td>{{ record.followUpMethod }}</td>
                <td>{{ formatDate(record.followUpTime) }}</td>
                <td>
                  <div class="level-tag" :class="'level-' + record.intentLevelAfterFollowUp">
                    {{ record.intentLevelAfterFollowUp }}
                  </div>
                </td>
                <td>{{ record.followUpDetails }}</td>
              </tr>
              <tr v-if="followUpRecords.length === 0">
                <td class="empty-data" colspan="7">{{ t('noFollowUpRecord') }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 变更日志 section -->
        <div class="section" v-if="changeLogs && changeLogs.length > 0">
          <div class="section-title">{{ t('changeLog') }}</div>
          <table class="custom-table">
            <thead>
              <tr>
                <th>{{ t('changeContent') }}</th>
                <th>{{ t('originalInfo') }}</th>
                <th>{{ t('changedInfo') }}</th>
                <th>{{ t('operator') }}</th>
                <th>{{ t('operationTime') }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(log, index) in changeLogs" :key="index">
                <td>{{ log.changeType }}</td>
                <td>{{ log.originalValue }}</td>
                <td>{{ log.newValue }}</td>
                <td>{{ log.operatorName }}</td>
                <td>{{ formatDate(log.changeTime) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { storeProspectApi } from '@/api/modules/prospective-customer'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales')

// 定义接口
interface DetailData {
  [key: string]: any;
  sourceChannel?: string;
  prospectStatus?: string;
  customerName?: string;
  customerPhone?: string;
  idType?: string;
  idNumber?: string;
  email?: string;
  region?: string;
  address?: string;
  intentModel?: string;
  intentVariant?: string;
  intentColor?: string;
}

interface Record {
  [key: string]: any;
}

const props = defineProps<{
  visible: boolean
  prospectId: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
}>()

// 使用字典数据
const {
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS,
  DICTIONARY_TYPES.ID_TYPE
])

// 获取来源渠道名称
const getSourceChannelName = (code: string) => {
  return getNameByCode(DICTIONARY_TYPES.CUSTOMER_SOURCE, code) || code
}

// 数据与加载状态
const loading = ref(false)
const detailData = ref<DetailData | null>(null)
const testDriveRecords = ref<Record[]>([])
const followUpRecords = ref<Record[]>([])
const changeLogs = ref<Record[]>([])

// 格式化日期方法
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听visible变化
const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 加载详情数据
const loadDetail = async () => {
  if (!props.prospectId) return
  try {
    loading.value = true
    // 根据API定义，参数应该是数字类型
    const response = await storeProspectApi.getProspectDetail(Number(props.prospectId))
    const data = response.result

    // 设置主数据
    detailData.value = data.prospectBaseInfo || {}

    // 设置关联数据
    testDriveRecords.value = data.testDriveRecords || []
    followUpRecords.value = data.followUpRecords || []
    changeLogs.value = data.changeLogs || []
  } catch (error) {
    console.error(t('getDataFailed'), error)
  } finally {
    loading.value = false
  }
}

// 当visible或prospectId变化时重新加载
watch(
  () => [props.visible, props.prospectId],
  ([newVisible, newId]) => {
    if (newVisible && newId) {
      loadDetail()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.prospect-detail {
  padding: 20px;
  font-size: 14px;
  color: #333;
  overflow-x: auto; /* 添加水平滚动条以确保小屏幕上内容可见 */
}

.section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: normal;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  margin-bottom: 15px;
}

.field-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 15px;
}

/* 响应式设计 - 中等屏幕 */
@media (max-width: 1200px) {
  .field-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 响应式设计 - 小屏幕 */
@media (max-width: 768px) {
  .field-grid {
    grid-template-columns: 1fr;
  }

  .prospect-detail {
    padding: 10px;
  }
}

.field-item {
  display: flex;
  flex-direction: column;
}

.field-item.address {
  margin-top: 5px;
}

.field-label {
  font-weight: normal;
  color: #666;
  margin-bottom: 5px;
}

.field-value {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  min-height: 24px;
  display: flex;
  align-items: center;
  word-break: break-word;
}

.custom-table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
  /* 添加响应式表格支持 */
  display: block;
  overflow-x: auto;
  white-space: nowrap; /* 在小屏幕上防止表格内容换行 */
}

@media (min-width: 769px) {
  /* 大屏幕上可以考虑取消白空间不换行，让表格更自然 */
  .custom-table {
    display: table;
    white-space: normal;
  }
}

.custom-table th,
.custom-table td {
  border: 1px solid #ddd;
  padding: 10px;
}

.custom-table th {
  background-color: #f5f7fa;
  font-weight: normal;
  color: #333;
}

.custom-table tr:nth-child(even) {
  background-color: #fafafa;
}

.empty-data {
  text-align: center;
  color: #999;
  padding: 20px 0;
}

.level-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 2px;
  color: #fff;
  text-align: center;
  font-size: 12px;
}

.level-H {
  background-color: #f56c6c;
}

.level-A {
  background-color: #e6a23c;
}

.level-B {
  background-color: #67c23a;
}

.level-C {
  background-color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
