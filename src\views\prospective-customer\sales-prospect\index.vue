<template>
  <div class="sales-prospect-management">
    <h1 class="page-title">{{ t('salesProspectManagement') }}</h1>

    <!-- 筛选区域 -->
    <el-card class="mb-20">
      <el-form ref="filterFormRef" :model="filterForm" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('prospectId')">
              <el-input
                v-model="filterForm.prospectId"
                :placeholder="t('inputProspectId')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospectName')">
              <el-input
                v-model="filterForm.customerName"
                :placeholder="t('inputProspectName')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospectPhone')">
              <el-input
                v-model="filterForm.customerPhone"
                :placeholder="t('inputPhoneNumber')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('sourceChannel')">
              <el-select
                v-model="filterForm.sourceChannel"
                :placeholder="tc('all')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in sourceChannelOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('prospectLevel')">
              <el-select
                v-model="filterForm.customerLevel"
                :placeholder="tc('all')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in prospectLevelOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospectStatus')">
              <el-select
                v-model="filterForm.customerStatus"
                :placeholder="tc('all')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in prospectStatusOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" style="display: flex; justify-content: flex-end;">
            <el-form-item>
              <el-space>
                <el-button type="primary" @click="handleSearch" :icon="Search">
                  {{ tc('query') }}
                </el-button>
                <el-button @click="handleReset" :icon="Refresh">
                  {{ tc('reset') }}
                </el-button>
              </el-space>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20">
      <div class="action-section">
        <div>
          <el-space>
            <el-button type="primary" @click="showAddModal" :icon="Plus">
              {{ tc('add') }}
            </el-button>
            <el-button @click="handleExport" :icon="Download">
              {{ tc('export') }}
            </el-button>
          </el-space>
        </div>
        <div>
          <el-radio-group v-model="dateFilterType" @change="handleDateFilterChange">
            <el-radio-button label="all">{{ tc('all') }}</el-radio-button>
            <el-radio-button label="today">{{ tc('today') }}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card>
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        border
        height="600"
        :scroll-x="2900"
        style="width: 100%"
      >
        <el-table-column type="index" :label="tc('serialNumber')" width="60" fixed="left" />
        <el-table-column prop="id" :label="t('prospectId')" width="120" />
        <el-table-column prop="name" :label="t('prospectName')" width="120" />
        <el-table-column prop="phoneNumber" :label="t('prospectPhone')" width="130" />
        <el-table-column prop="sourceChannel" :label="t('sourceChannel')" width="120">
          <template #default="{ row }">
            {{ getDictionaryI18nName(DICTIONARY_TYPES.CUSTOMER_SOURCE, row.sourceChannel) }}
          </template>
        </el-table-column>
        <el-table-column prop="currentIntentLevel" :label="t('prospectLevel')" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.currentIntentLevel)">
              {{ getDictionaryI18nName(DICTIONARY_TYPES.INTENT_LEVEL, row.currentIntentLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="prospectStatus" :label="t('prospectStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.prospectStatus)">
              {{ getDictionaryI18nName(DICTIONARY_TYPES.PROSPECT_STATUS, row.prospectStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="intentModel" :label="t('intentModel')" width="120" />
        <el-table-column prop="intentVariant" :label="t('intentVariant')" width="150" />
        <el-table-column prop="intentColor" :label="t('intentColor')" width="100" />
        <el-table-column prop="currentSalesAdvisorId" :label="t('salesAdvisorId')" width="120" />
        <el-table-column prop="currentSalesAdvisorName" :label="t('salesAdvisorName')" width="120" />
        <el-table-column prop="leadAssociationTime" :label="t('prospectCreationTime')" width="180" >
          <template #default="{ row }">
              {{ formatDate(row.leadAssociationTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastFollowUpTime" :label="t('lastFollowUpTime')" width="180" >
          <template #default="{ row }">
              {{ formatDate(row.lastFollowUpTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="nextFollowUpTime" :label="t('nextFollowUpTime')" width="180">
          <template #default="{ row }">
              {{ formatDate(row.nextFollowUpTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="tc('actions')" width="200" fixed="right">
          <template #default="{ row }">
            <el-space>
              <el-button type="primary" link @click="handleFollowUp(row)">
                {{ tc('followUp') }}
              </el-button>
              <el-button type="info" link @click="handleViewDetail(row)">
                {{ tc('details') }}
              </el-button>
              <el-button type="warning" link @click="handleMarkNoIntention(row)">
                {{ t('markNoIntention') }}
              </el-button>
              <el-button type="success" link @click="handleAssignAdvisor(row)">
                {{ t('changeAdvisor') }}
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section mt-20">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 新增潜客模态框 -->
    <AddProspectModal
      v-model:show="showAddProspectModal"
      :preset-data="addProspectPresetData"
      @success="handleAddSuccess"
    />

    <!-- 跟进记录模态框 -->
    <FollowUpModal
      v-model:show="showFollowUpModal"
      :prospect-data="currentProspectData"
      @success="handleFollowUpSuccess"
    />

    <!-- 标记无意向模态框 -->
    <DefeatModal
      v-model:show="showDefeatModal"
      :prospect-data="currentProspectData"
      @success="handleDefeatSuccess"
    />

    <!-- 潜客详情模态框 -->
    <ProspectDetailModal
      v-model:visible="showDetailModal"
      :prospectId="currentProspectData?.id ? String(currentProspectData.id) : ''"
    />

    <!-- 变更顾问模态框 -->
    <AssignAdvisorModal
      v-model:show="showAssignAdvisorModal"
      :prospect-data="currentProspectData"
      @success="handleAssignAdvisorSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Plus, Download } from '@element-plus/icons-vue'
import { storeProspectApi } from '@/api/modules/prospective-customer'
import {
  type ProspectBaseInfo,
  type GetStoreProspectListRequest,
  ProspectLevel,
  ProspectStatus,
  SourceChannel
} from '@/types/prospective-customer.d'
import { maskPhone, maskIdNumber, maskName } from '@/utils/data-mask'
import { formatDate, isOverdue, isToday } from '@/utils/date-filter'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales')
import AddProspectModal from './components/AddProspectModal.vue'
import FollowUpModal from './components/FollowUpModal.vue'
import DefeatModal from './components/DefeatModal.vue'
import ProspectDetailModal from './components/ProspectDetailModal.vue'
import AssignAdvisorModal from './components/AssignAdvisorModal.vue'

// 响应式数据
const loading = ref(false)
const tableRef = ref()
const filterFormRef = ref()
const tableData = ref<ProspectBaseInfo[]>([])

// 筛选表单
const filterForm = reactive<GetStoreProspectListRequest>({
  prospectId: '',
  customerName: '',
  customerPhone: '',
  sourceChannel: undefined,
  customerLevel: undefined,
  customerStatus: undefined,
  page: 1,
  pageSize: 20
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 日期筛选
const dateFilterType = ref('all')

// 模态框状态
const showAddProspectModal = ref(false)
const showFollowUpModal = ref(false)
const showDefeatModal = ref(false)
const showDetailModal = ref(false)
const showAssignAdvisorModal = ref(false)

// 当前操作的潜客数据
const currentProspectData = ref<ProspectBaseInfo | null>(null)
const addProspectPresetData = ref({})

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS
])

const sourceChannelOptions = computed(() => getOptions(DICTIONARY_TYPES.CUSTOMER_SOURCE))

// 获取来源渠道名称
// 新增辅助函数，用于获取字典项的国际化名称
const getDictionaryI18nName = (type: string, code: string) => {
  // 尝试从国际化文件中获取，键名格式为 `dictionary.<type>.<code_value>`
  // 注意：这里需要将 DICTIONARY_TYPES 中的值（如 '0106'）映射到更具可读性的字符串，
  // 例如 'CUSTOMER_SOURCE'，以便在国际化键中使用。
  // 暂时先直接使用 type，如果后续发现问题再调整。
  const typeKeyMap: Record<string, string> = {
    [DICTIONARY_TYPES.CUSTOMER_SOURCE]: 'customerSource',
    [DICTIONARY_TYPES.INTENT_LEVEL]: 'intentLevel',
    [DICTIONARY_TYPES.PROSPECT_STATUS]: 'prospectStatus',
    // ... 其他需要国际化的字典类型
  };
  const mappedType = typeKeyMap[type] || type; // 如果没有映射，则使用原始type

  const i18nKey = `dictionary.${mappedType}.${code}`;
  const translatedText = t(i18nKey);

  // 如果国际化键存在且翻译结果不是键名本身，则返回翻译结果
  if (translatedText !== i18nKey) {
    return translatedText;
  }
  // 否则，回退到从字典数据中获取的名称
  return getNameByCode(type, code) || code;
};

// 修改 getSourceChannelName
const getSourceChannelName = (code: string) => {
  return getDictionaryI18nName(DICTIONARY_TYPES.CUSTOMER_SOURCE, code);
};

const prospectLevelOptions = computed(() => getOptions(DICTIONARY_TYPES.INTENT_LEVEL))
const prospectStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PROSPECT_STATUS))

// 获取标签类型 - 使用字典编码
const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    '01160001': 'danger',  // H级
    '01160002': 'warning', // A级
    '01160003': 'success', // B级
    '01160004': 'info'     // C级
  }
  return typeMap[level] || 'info'
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01380001': 'info',    // 新建
    '01380002': 'warning', // 跟进中
    '01380003': 'success', // 已成交
    '01380004': 'danger'   // 无意向
  }
  return typeMap[status] || 'info'
}

// 事件处理函数
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  filterFormRef.value?.resetFields()
  Object.assign(filterForm, {
    prospectId: '',
    customerName: '',
    customerPhone: '',
    sourceChannel: undefined,
    customerLevel: undefined,
    customerStatus: undefined,
    page: 1,
    pageSize: 20
  })
  pagination.page = 1
  loadData()
}

const handleDateFilterChange = (value: string) => {
  dateFilterType.value = value
  loadData()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  filterForm.page = page
  loadData()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  filterForm.pageSize = pageSize
  filterForm.page = 1
  loadData()
}

const showAddModal = () => {
  addProspectPresetData.value = {}
  showAddProspectModal.value = true
}

const handleExport = () => {
  ElMessage.info(tc('exportingFeatureDeveloping'))
}

const handleFollowUp = (row: ProspectBaseInfo) => {
  currentProspectData.value = row
  showFollowUpModal.value = true
}

const handleViewDetail = (row: ProspectBaseInfo) => {
  // 只传递必要的ID，其他详细数据将由详情组件通过API获取
  currentProspectData.value = { id: row.id } as ProspectBaseInfo
  showDetailModal.value = true
}

const handleMarkNoIntention = (row: ProspectBaseInfo) => {
  currentProspectData.value = row
  showDefeatModal.value = true
}

const handleAssignAdvisor = (row: ProspectBaseInfo) => {
  currentProspectData.value = row
  showAssignAdvisorModal.value = true
}

const handleAddSuccess = () => {
  showAddProspectModal.value = false
  loadData()
  ElMessage.success(t('addProspectSuccess'))
}

const handleFollowUpSuccess = () => {
  showFollowUpModal.value = false
  loadData()
  ElMessage.success(t('followUpRecordAddSuccess'))
}

const handleDefeatSuccess = () => {
  showDefeatModal.value = false
  loadData()
  ElMessage.success(t('markNoIntentionApplySuccess'))
}

const handleAssignAdvisorSuccess = () => {
  showAssignAdvisorModal.value = false
  loadData()
  ElMessage.success(t('changeAdvisorSuccess'))
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      ...filterForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    }




    // 根据日期筛选类型设置其他过滤条件
    if (dateFilterType.value === 'today') {
      params.isToday = true;
    }

    const response = await storeProspectApi.getProspectList(params)
    console.log(response);
    tableData.value = response.result.records
    console.log('tableData:', tableData.value);
    pagination.total = response.result.total
  } catch (error) {
    ElMessage.error(tc('getDataFailed'))
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.sales-prospect-management {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.text-danger {
  color: #f56c6c;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}
</style>
