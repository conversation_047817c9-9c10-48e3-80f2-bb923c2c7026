<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">质检单管理</h1>

    <!-- 搜索区域卡片 -->
    <el-card class="mb-20 search-card">
      <el-form :model="filterForm" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item label="工单编号">
              <el-input
                v-model="filterForm.workOrderNo"
                placeholder="请输入工单编号"
                clearable
                @keyup.enter="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="质检单编号">
              <el-input
                v-model="filterForm.qualityCheckNo"
                placeholder="请输入质检单编号"
                clearable
                @keyup.enter="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="质检状态">
              <el-select
                v-model="filterForm.status"
                placeholder="请选择质检状态"
                clearable
                multiple
                style="width: 100%;"
              >
                <el-option
                  v-for="status in statusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="工单类型">
              <el-select
                v-model="filterForm.workOrderType"
                placeholder="请选择工单类型"
                clearable
                style="width: 100%;"
              >
                <el-option label="保养" value="maintenance" />
                <el-option label="维修" value="repair" />
                <el-option label="保险" value="insurance" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="送修人名称">
              <el-input
                v-model="filterForm.serviceCustomerName"
                placeholder="请输入送修人名称"
                clearable
                @keyup.enter="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="送修人手机号">
              <el-input
                v-model="filterForm.serviceCustomerPhone"
                placeholder="请输入送修人手机号"
                clearable
                @keyup.enter="handleFilter"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item label="技师">
              <el-select
                v-model="filterForm.technicianId"
                placeholder="请选择技师"
                clearable
                style="width: 100%;"
              >
                <el-option
                  v-for="technician in technicianOptions"
                  :key="technician.id"
                  :label="technician.name"
                  :value="technician.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="filterForm.createTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
                :shortcuts="dateShortcuts"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16" class="buttons-col">
            <el-button type="primary" @click="handleFilter">
              查询
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域卡片 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20">
        <el-col :span="12">
          <span class="filter-summary">
            共找到 {{ pagination.total }} 条记录
          </span>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button
            v-if="canExport"
            type="primary"
            :icon="Download"
            @click="handleExport"
            :loading="exportLoading"
          >
            导出Excel
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表格区域卡片 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <!-- 序号 -->
        <el-table-column type="index" label="序号" width="60" align="center" />

        <!-- 质检单编号 -->
        <el-table-column prop="qualityCheckNo" label="质检单编号" width="150" sortable>
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              {{ row.qualityCheckNo }}
            </el-button>
          </template>
        </el-table-column>

        <!-- 质检状态 -->
        <el-table-column prop="status" label="质检状态" width="100" align="center" sortable>
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" class="status-tag">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 工单编号 -->
        <el-table-column prop="workOrderNo" label="工单编号" width="150" sortable>
          <template #default="{ row }">
            <el-button type="primary" link @click="viewWorkOrder(row.workOrderNo)">
              {{ row.workOrderNo }}
            </el-button>
          </template>
        </el-table-column>

        <!-- 工单类型 -->
        <el-table-column prop="workOrderType" label="工单类型" width="80" align="center" sortable>
          <template #default="{ row }">
            <el-tag :type="getWorkOrderTypeTagType(row.workOrderType)">
              {{ getWorkOrderTypeText(row.workOrderType) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 是否涉及索赔 -->
        <el-table-column prop="isClaimRelated" label="是否涉及索赔" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isClaimRelated ? 'warning' : 'info'" size="small">
              {{ row.isClaimRelated ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 是否涉及委外 -->
        <el-table-column prop="isOutsourceRelated" label="是否涉及委外" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isOutsourceRelated ? 'warning' : 'info'" size="small">
              {{ row.isOutsourceRelated ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 送修人名称 -->
        <el-table-column prop="serviceCustomerName" label="送修人名称" width="120" />

        <!-- 送修人手机号 -->
        <el-table-column prop="serviceCustomerPhone" label="送修人手机号" width="130">
          <template #default="{ row }">
            {{ formatPhoneNumber(row.serviceCustomerPhone) }}
          </template>
        </el-table-column>

        <!-- 车牌号 -->
        <el-table-column prop="plateNumber" label="车牌号" width="100" align="center">
          <template #default="{ row }">
            <span class="plate-number">{{ row.plateNumber }}</span>
          </template>
        </el-table-column>

        <!-- 车型 -->
        <el-table-column prop="vehicleModel" label="车型" width="150" show-overflow-tooltip />

        <!-- 配置 -->
        <el-table-column prop="vehicleConfig" label="配置" width="120" show-overflow-tooltip />

        <!-- 颜色 -->
        <el-table-column prop="vehicleColor" label="颜色" width="80" align="center">
          <template #default="{ row }">
            <el-tag size="small">{{ row.vehicleColor }}</el-tag>
          </template>
        </el-table-column>

        <!-- 技师 -->
        <el-table-column prop="technicianName" label="技师" width="100" align="center" sortable />

        <!-- 开工时间 -->
        <el-table-column prop="startTime" label="开工时间" width="130" align="center" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.startTime) }}
          </template>
        </el-table-column>

        <!-- 完工时间 -->
        <el-table-column prop="finishTime" label="完工时间" width="130" align="center" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.finishTime) }}
          </template>
        </el-table-column>

        <!-- 预计工时 -->
        <el-table-column prop="estimatedHours" label="预计工时" width="80" align="right">
          <template #default="{ row }">
            {{ row.estimatedHours || 0 }}h
          </template>
        </el-table-column>

        <!-- 实际工时 -->
        <el-table-column prop="actualHours" label="实际工时" width="80" align="right">
          <template #default="{ row }">
            {{ row.actualHours || '-' }}h
          </template>
        </el-table-column>

        <!-- 返工原因 -->
        <el-table-column prop="reworkReason" label="返工原因" width="150" show-overflow-tooltip />

        <!-- 返工要求 -->
        <el-table-column prop="reworkRequirement" label="返工要求" width="150" show-overflow-tooltip />

        <!-- 返工开始时间 -->
        <el-table-column prop="reworkStartTime" label="返工开始时间" width="130" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.reworkStartTime) }}
          </template>
        </el-table-column>

        <!-- 返工完成时间 -->
        <el-table-column prop="reworkFinishTime" label="返工完成时间" width="130" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.reworkFinishTime) }}
          </template>
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column prop="createTime" label="创建时间" width="130" align="center" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>

        <!-- 更新时间 -->
        <el-table-column prop="updateTime" label="更新时间" width="130" align="center" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button
              v-if="canEdit(row.status)"
              type="warning"
              link
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="canRework(row.status)"
              type="info"
              link
              @click="handleRework(row)"
            >
              返工
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-card>

    <!-- 质检详情模态框 -->
    <QualityCheckDetailModal
      v-model:visible="detailModalVisible"
      :quality-check-id="selectedQualityCheckId"
      @refresh="handleRefresh"
    />

    <!-- 质检编辑模态框 -->
    <QualityCheckEditModal
      v-model:visible="editModalVisible"
      :quality-check-id="selectedQualityCheckId"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type {
  QualityCheckListItem,
  QualityCheckListParams,
  QualityCheckStatus,
  WorkOrderType
} from '@/types/module'
import { qualityCheckApi } from '@/api/modules/qualityCheck'
import { formatDateTime } from '@/utils/dateTime'
import QualityCheckDetailModal from './components/QualityCheckDetailModal.vue'
import QualityCheckEditModal from './components/QualityCheckEditModal.vue'

const { t } = useI18n()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref<QualityCheckListItem[]>([])
const detailModalVisible = ref(false)
const editModalVisible = ref(false)
const selectedQualityCheckId = ref('')

// 筛选表单
const filterForm = reactive<QualityCheckListParams>({
  qualityCheckNo: '',
  workOrderNo: '',
  status: undefined,
  workOrderType: undefined,
  serviceCustomerName: '',
  serviceCustomerPhone: '',
  technicianId: '',
  createTimeRange: []
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 状态选项
const statusOptions = computed(() => [
  { value: 'pending_check', label: t('qualityCheck.status.pending_check') },
  { value: 'checking', label: t('qualityCheck.status.checking') },
  { value: 'pending_review', label: t('qualityCheck.status.pending_review') },
  { value: 'passed', label: t('qualityCheck.status.passed') },
  { value: 'rework', label: t('qualityCheck.status.rework') }
])

// 技师选项
const technicianOptions = ref([
  { id: '1', name: '张师傅' },
  { id: '2', name: '李师傅' },
  { id: '3', name: '王师傅' }
])

// 日期快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
      return [start, end]
    }
  },
  {
    text: '昨天',
    value: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
      const end = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59)
      return [start, end]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 6)
      const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
      return [start, end]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 29)
      const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
      return [start, end]
    }
  }
]

// 权限控制
const canExport = computed(() => {
  // 这里应该根据用户角色判断，技师经理可见
  return true // 暂时返回true，实际应该从用户权限获取
})

// 获取状态标签类型
const getStatusTagType = (status: QualityCheckStatus) => {
  const typeMap = {
    'pending_check': 'info',
    'checking': 'warning',
    'pending_review': 'primary',
    'passed': 'success',
    'rework': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: QualityCheckStatus) => {
  return t(`qualityCheck.status.${status}`)
}

// 获取工单类型文本
const getWorkOrderTypeText = (type: WorkOrderType) => {
  return t(`qualityCheck.workOrderType.${type}`)
}

// 判断是否可以编辑
const canEdit = (status: QualityCheckStatus) => {
  return ['pending_check', 'checking', 'rework'].includes(status)
}



// 加载数据
const loadData = async () => {
  try {
    loading.value = true

    const params = {
      ...filterForm,
      page: pagination.page,
      size: pagination.size,
      startDate: filterForm.createTimeRange?.[0],
      endDate: filterForm.createTimeRange?.[1]
    }

    // 移除createTimeRange以避免发送不必要的参数
    delete params.createTimeRange

    const response = await qualityCheckApi.getQualityCheckList(params)

    tableData.value = response.list || []
    pagination.total = response.total
  } catch (error) {
    console.error('Load quality check list failed:', error)
    ElMessage.error(t('common.loadDataFailed'))
  } finally {
    loading.value = false
  }
}

// 筛选处理
const handleFilter = () => {
  pagination.page = 1
  loadData()
}

// 重置筛选
const handleReset = () => {
  Object.assign(filterForm, {
    qualityCheckNo: '',
    workOrderNo: '',
    status: undefined,
    workOrderType: undefined,
    serviceCustomerName: '',
    serviceCustomerPhone: '',
    technicianId: '',
    createTimeRange: []
  })
  handleFilter()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true

    const params = {
      ...filterForm,
      startDate: filterForm.createTimeRange?.[0],
      endDate: filterForm.createTimeRange?.[1]
    }

    // 移除createTimeRange以避免发送不必要的参数
    const exportParams = { ...params }
    delete (exportParams as Record<string, unknown>).createTimeRange

    await qualityCheckApi.exportQualityCheckData(exportParams)
    ElMessage.success(t('common.exportSuccess'))
  } catch (error) {
    console.error('Export failed:', error)
    ElMessage.error(t('common.exportFailed'))
  } finally {
    exportLoading.value = false
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 查看详情
const handleViewDetail = (row: QualityCheckListItem) => {
  selectedQualityCheckId.value = row.id
  detailModalVisible.value = true
}

// 编辑质检
const handleEdit = (row: QualityCheckListItem) => {
  selectedQualityCheckId.value = row.id
  editModalVisible.value = true
}



// 格式化手机号
const formatPhoneNumber = (phone: string) => {
  if (!phone) return '-'
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
}

// 获取工单类型标签类型
const getWorkOrderTypeTagType = (type: string) => {
  switch (type) {
    case 'maintenance':
      return 'success'
    case 'repair':
      return 'warning'
    case 'insurance':
      return 'info'
    default:
      return 'info'
  }
}

// 查看工单详情
const viewWorkOrder = (workOrderNo: string) => {
  // 跳转到工单详情页面
  console.log('查看工单:', workOrderNo)
}

// 刷新数据
const handleRefresh = () => {
  loadData()
}

// 返工操作
const handleRework = (row: QualityCheckListItem) => {
  console.log('返工操作:', row)
  // 这里应该打开返工对话框或跳转到返工页面
}



// 判断是否可以返工
const canRework = (status: string) => {
  return status === 'completed' || status === 'audited'
}

// 组件挂载
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
// 导入 SASS 变量文件
@use '@/assets/styles/_variables.scss' as *;

/* 页面容器样式 */
.page-container {
  padding: 20px;
}

/* 页面标题样式 */
.page-title {
  margin-bottom: 20px;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 20px;
}

/* 操作卡片样式 */
.operation-card {
  margin-bottom: 20px;
  .text-right {
    text-align: right;
  }
  .filter-summary {
    font-size: 14px;
    color: #606266;
  }
}

/* 搜索表单样式 */
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

/* 按钮列样式 */
.buttons-col {
  text-align: right;
}

/* 表格卡片样式 */
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td {
      white-space: nowrap;
    }
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 工时信息样式 */
.hours-info {
  font-size: 12px;
  line-height: 1.4;
}

/* 标签信息样式 */
.tags-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .el-tag {
    align-self: flex-start;
  }
}

/* 状态标签样式 */
.status-tag {
  font-weight: 500;
}

/* 车牌号样式 */
.plate-number {
  font-weight: 600;
  color: #409eff;
}

/* 通用样式 */
.mb-20 {
  margin-bottom: 20px;
}

.text-right {
  text-align: right;
}

/* 响应式隐藏 */
@media (max-width: 768px) {
  .hidden-sm-and-down {
    display: none !important;
  }
}
</style>
