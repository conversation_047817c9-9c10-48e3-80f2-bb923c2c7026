<template>
  <el-dialog
    v-model="visible"
    :title="`质检单详情 - ${detailData?.qualityCheck?.qualityCheckNo || ''}`"
    width="1000px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    destroy-on-close
    class="quality-check-detail-modal"
  >
    <!-- 标题栏状态标签 -->
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header">
        <span :id="titleId" :class="titleClass">
          质检单详情 - {{ detailData?.qualityCheck?.qualityCheckNo || '' }}
        </span>
        <el-tag
          v-if="detailData"
          :type="getStatusTagType(detailData.qualityCheck.status)"
          class="header-status-tag"
          size="large"
        >
          {{ getStatusText(detailData.qualityCheck.status) }}
        </el-tag>
      </div>
    </template>
    <div v-loading="loading" class="detail-content">
      <template v-if="detailData">
        <!-- 基础信息区域 -->
        <div class="detail-section">
          <h3 class="section-title">基础信息</h3>
          <el-descriptions :column="2" border class="detail-descriptions">
            <el-descriptions-item label="质检单编号">
              {{ detailData.qualityCheck.qualityCheckNo }}
            </el-descriptions-item>
            <el-descriptions-item label="质检状态">
              <el-tag :type="getStatusTagType(detailData.qualityCheck.status)" class="status-tag">
                {{ getStatusText(detailData.qualityCheck.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="工单编号">
              <el-button type="primary" link @click="viewWorkOrder(detailData.qualityCheck.workOrderNo)">
                {{ detailData.qualityCheck.workOrderNo }}
              </el-button>
            </el-descriptions-item>
            <el-descriptions-item label="工单类型">
              <div class="work-order-type">
                <el-icon class="work-order-icon">
                  <component :is="getWorkOrderTypeIcon(detailData.qualityCheck.workOrderType)" />
                </el-icon>
                <span class="work-order-text">{{ getWorkOrderTypeText(detailData.qualityCheck.workOrderType) }}</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="是否涉及索赔">
              <el-tag :type="detailData.qualityCheck.isClaimRelated ? 'warning' : 'info'" size="small">
                {{ detailData.qualityCheck.isClaimRelated ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="是否涉及委外">
              <el-tag :type="detailData.qualityCheck.isOutsourceRelated ? 'warning' : 'info'" size="small">
                {{ detailData.qualityCheck.isOutsourceRelated ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="开工时间">
              {{ formatDateTime(detailData.qualityCheck.startTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="完工时间">
              {{ formatDateTime(detailData.qualityCheck.finishTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="预计工时">
              <span class="hours-value">{{ detailData.qualityCheck.estimatedHours || 0 }}h</span>
            </el-descriptions-item>
            <el-descriptions-item label="实际工时">
              <span class="hours-value">{{ detailData.qualityCheck.actualHours || '-' }}h</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(detailData.qualityCheck.createTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDateTime(detailData.qualityCheck.updateTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 客户车辆信息区域 -->
        <div class="detail-section">
          <h3 class="section-title">客户车辆信息</h3>

          <!-- 客户信息部分 -->
          <div class="sub-section">
            <h4 class="sub-title">客户信息</h4>
            <el-descriptions :column="2" border class="detail-descriptions">
              <el-descriptions-item label="预约人名称">
                {{ detailData.customerVehicleInfo.appointmentCustomerName || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="预约人手机号">
                {{ formatPhoneNumber(detailData.customerVehicleInfo.appointmentCustomerPhone) }}
              </el-descriptions-item>
              <el-descriptions-item label="送修人名称">
                <span class="important-text">{{ detailData.customerVehicleInfo.serviceCustomerName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="送修人手机号">
                {{ formatPhoneNumber(detailData.customerVehicleInfo.serviceCustomerPhone) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 车辆信息部分 -->
          <div class="sub-section">
            <h4 class="sub-title">车辆信息</h4>
            <el-descriptions :column="2" border class="detail-descriptions">
              <el-descriptions-item label="车牌号">
                <span class="plate-number">{{ detailData.customerVehicleInfo.plateNumber }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="VIN码">
                <span class="vin-code">{{ detailData.customerVehicleInfo.vin }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="车型">
                {{ detailData.customerVehicleInfo.vehicleModel }}
              </el-descriptions-item>
              <el-descriptions-item label="配置">
                <span class="vehicle-config" :title="detailData.customerVehicleInfo.vehicleConfig">
                  {{ detailData.customerVehicleInfo.vehicleConfig }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="颜色">
                <el-tag size="small" class="color-tag">
                  {{ detailData.customerVehicleInfo.vehicleColor }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="里程数">
                <span class="mileage-value">{{ formatMileage(detailData.customerVehicleInfo.mileage) }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="车龄">
                <span class="vehicle-age">{{ formatVehicleAge(detailData.customerVehicleInfo.vehicleAge) }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 质检信息区域 -->
        <div class="detail-section">
          <h3 class="section-title">质检信息</h3>
          <div class="check-items-container">
            <el-collapse v-model="activeCollapseNames" class="check-collapse">
              <el-collapse-item
                v-for="category in groupedCheckItems"
                :key="category.categoryCode"
                :title="category.categoryName"
                :name="category.categoryCode"
                class="category-collapse"
              >
                <div class="check-items-list">
                  <div
                    v-for="item in category.items"
                    :key="item.id"
                    class="check-item"
                  >
                    <div class="item-header">
                      <span class="item-name">{{ item.itemName }}</span>
                      <div class="item-result">
                        <!-- 布尔型检查项 -->
                        <template v-if="item.itemType === 'BOOLEAN'">
                          <el-icon
                            v-if="item.checkResult === 'PASS'"
                            class="result-icon pass-icon"
                            color="#67c23a"
                          >
                            <Check />
                          </el-icon>
                          <el-icon
                            v-else
                            class="result-icon fail-icon"
                            color="#f56c6c"
                          >
                            <Close />
                          </el-icon>
                          <span class="result-text" :class="item.checkResult === 'PASS' ? 'pass-text' : 'fail-text'">
                            {{ item.checkResult === 'PASS' ? '合格' : '不合格' }}
                          </span>
                        </template>

                        <!-- 数值型检查项 -->
                        <template v-else-if="item.itemType === 'NUMERIC'">
                          <span class="numeric-result">
                            {{ item.numericValue }}{{ item.unit || '' }}
                          </span>
                        </template>

                        <!-- 文本型检查项 -->
                        <template v-else>
                          <span class="text-result">
                            {{ item.textValue || '-' }}
                          </span>
                        </template>
                      </div>
                    </div>

                    <!-- 标准值说明 -->
                    <div v-if="item.standardValue" class="item-standard">
                      <span class="standard-label">标准值：</span>
                      <span class="standard-value">{{ item.standardValue }}</span>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>

        <!-- 工时零件详情区域 -->
        <div class="detail-section">
          <h3 class="section-title">工时零件详情</h3>

          <!-- 工时详情表格 -->
          <div class="sub-section">
            <h4 class="sub-title">工时详情</h4>
            <el-table :data="detailData.laborHourDetails || []" border size="small" class="detail-table">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column label="类型" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getWorkTypeTagType(row.laborType)" size="small">
                    {{ getWorkTypeText(row.laborType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="laborCode" label="工时代码" width="120" class-name="code-column" />
              <el-table-column prop="laborName" label="工时名称" width="200" show-overflow-tooltip />
              <el-table-column label="是否委外" width="80" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.isOutsource ? 'warning' : 'info'" size="small">
                    {{ row.isOutsource ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="是否增项" width="80" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.isAdditional ? 'danger' : 'success'" size="small">
                    {{ row.isAdditional ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="标准工时" width="100" align="center">
                <template #default="{ row }">
                  <span class="hours-value">{{ row.standardHours }}h</span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 零件详情表格 -->
          <div class="sub-section">
            <h4 class="sub-title">零件详情</h4>
            <el-table :data="detailData.partsDetails || []" border size="small" class="detail-table">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column label="类型" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getWorkTypeTagType(row.partType)" size="small">
                    {{ getWorkTypeText(row.partType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="partCode" label="零件代码" width="120" class-name="code-column" />
              <el-table-column prop="partName" label="零件名称" width="200" show-overflow-tooltip />
              <el-table-column label="是否增项" width="80" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.isAdditional ? 'danger' : 'success'" size="small">
                    {{ row.isAdditional ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="零件数量" width="100" align="center">
                <template #default="{ row }">
                  <span class="quantity-value">{{ row.quantity }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 审核信息区域（如果已审核） -->
        <div v-if="detailData.qualityCheck.auditResult" class="detail-section">
          <h3 class="section-title">审核信息</h3>
          <el-descriptions :column="2" border class="detail-descriptions">
            <el-descriptions-item label="审核结果">
              <el-tag
                :type="detailData.qualityCheck.auditResult === 'passed' ? 'success' : 'danger'"
                size="large"
                class="audit-result-tag"
              >
                {{ detailData.qualityCheck.auditResult === 'passed' ? '质检通过' : '返工' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="审核人">
              {{ detailData.qualityCheck.auditorName }}
            </el-descriptions-item>
            <el-descriptions-item label="审核时间">
              {{ formatDateTime(detailData.qualityCheck.auditTime) }}
            </el-descriptions-item>
            <el-descriptions-item v-if="detailData.qualityCheck.auditRemark" label="审核备注" :span="2">
              <div class="remark-content">{{ detailData.qualityCheck.auditRemark }}</div>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 返工信息（仅返工时显示） -->
          <div v-if="detailData.qualityCheck.auditResult === 'rework'" class="rework-info">
            <h4 class="sub-title">返工信息</h4>
            <el-descriptions :column="2" border class="detail-descriptions">
              <el-descriptions-item label="返工原因" :span="2">
                <div class="remark-content">{{ detailData.qualityCheck.reworkReason }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="返工要求" :span="2">
                <div class="remark-content">{{ detailData.qualityCheck.reworkRequirement }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="返工开始时间">
                {{ formatDateTime(detailData.qualityCheck.reworkStartTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="返工完成时间">
                {{ formatDateTime(detailData.qualityCheck.reworkFinishTime) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 操作日志 -->
        <div class="detail-section">
          <h3 class="section-title">{{ $t('qualityCheck.detail.operationLogs') }}</h3>
          <el-timeline>
            <el-timeline-item
              v-for="log in detailData.operationLogs"
              :key="log.id"
              :timestamp="formatDateTime(log.operationTime)"
              placement="top"
            >
              <div class="log-content">
                <div class="log-header">
                  <span class="operator">{{ log.operatorName }}</span>
                  <el-tag size="small" :type="getOperationTagType(log.operationType)">
                    {{ getOperationText(log.operationType) }}
                  </el-tag>
                </div>
                <p class="log-description">{{ log.operationContent }}</p>
                <div class="log-status" v-if="log.beforeStatus !== log.afterStatus">
                  <span>{{ getStatusText(log.beforeStatus) }}</span>
                  <el-icon class="status-arrow"><Right /></el-icon>
                  <span>{{ getStatusText(log.afterStatus) }}</span>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </template>
    </div>

    <!-- 操作按钮区域 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          关闭
        </el-button>
        <el-button @click="handlePrint" size="large">
          打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Right, Check, Close } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type {
  QualityCheckDetail,
  QualityCheckStatus,
  WorkOrderType,
  QualityCheckItem
} from '@/types/module'
import { qualityCheckApi } from '@/api/modules/qualityCheck'
import { formatDateTime } from '@/utils/dateTime'

interface Props {
  visible: boolean
  qualityCheckId: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

const loading = ref(false)
const detailData = ref<QualityCheckDetail | null>(null)
const activeCollapseNames = ref<string[]>([])

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})



const groupedCheckItems = computed(() => {
  if (!detailData.value?.checkItems) return []

  const groups = new Map<string, { categoryCode: string; categoryName: string; items: QualityCheckItem[] }>()

  detailData.value.checkItems.forEach(item => {
    if (!groups.has(item.categoryCode)) {
      groups.set(item.categoryCode, {
        categoryCode: item.categoryCode,
        categoryName: item.categoryName,
        items: []
      })
    }
    groups.get(item.categoryCode)!.items.push(item)
  })

  return Array.from(groups.values()).map(group => ({
    ...group,
    items: group.items.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
  }))
})

// 工具方法
const getStatusTagType = (status: QualityCheckStatus) => {
  const typeMap = {
    'pending_check': 'info',
    'checking': 'warning',
    'pending_review': 'primary',
    'passed': 'success',
    'rework': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: QualityCheckStatus) => {
  return t(`qualityCheck.status.${status}`)
}

const getWorkOrderTypeText = (type: WorkOrderType) => {
  const typeMap = {
    'maintenance': '保养',
    'repair': '维修',
    'insurance': '保险'
  }
  return typeMap[type] || type
}

const getWorkOrderTypeIcon = (type: WorkOrderType) => {
  const iconMap = {
    'maintenance': 'Tools',
    'repair': 'Setting',
    'insurance': 'Shield'
  }
  return iconMap[type] || 'Document'
}

const viewWorkOrder = (workOrderNo: string) => {
  // 跳转到工单详情页面
  console.log('查看工单:', workOrderNo)
}

// 格式化手机号
const formatPhoneNumber = (phone: string) => {
  if (!phone) return '-'
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
}

// 格式化里程数
const formatMileage = (mileage: number) => {
  if (!mileage) return '-'
  return `${mileage.toLocaleString()}km`
}

// 格式化车龄
const formatVehicleAge = (vehicleAge: number) => {
  if (!vehicleAge) return '-'
  if (vehicleAge >= 12) {
    const years = Math.floor(vehicleAge / 12)
    const months = vehicleAge % 12
    return months > 0 ? `${years}年${months}月` : `${years}年`
  } else {
    return `${vehicleAge}月`
  }
}

// 获取工作类型标签类型
const getWorkTypeTagType = (type: string) => {
  switch (type) {
    case 'maintenance':
      return 'success'
    case 'repair':
      return 'warning'
    case 'insurance':
      return 'info'
    default:
      return 'info'
  }
}

// 获取工作类型文本
const getWorkTypeText = (type: string) => {
  const typeMap = {
    'maintenance': '保养',
    'repair': '维修',
    'insurance': '保险'
  }
  return typeMap[type] || type
}



const getOperationTagType = (type: string) => {
  const typeMap = {
    'edit': 'primary',
    'submit_review': 'warning',
    'approve_pass': 'success',
    'rework': 'danger'
  }
  return typeMap[type] || 'info'
}

const getOperationText = (type: string) => {
  return t(`qualityCheck.operation.${type}`)
}

// 加载详情数据
const loadDetail = async () => {
  if (!props.qualityCheckId) return

  try {
    loading.value = true
    const response = await qualityCheckApi.getQualityCheckDetail(props.qualityCheckId)
    detailData.value = response
  } catch (error) {
    console.error('Load quality check detail failed:', error)
    ElMessage.error(t('common.loadDataFailed'))
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleClose = () => {
  visible.value = false
}

const handlePrint = () => {
  // 打印质检单
  console.log('打印质检单')
  ElMessage.info('打印功能待实现')
}



// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal && props.qualityCheckId) {
    loadDetail()
  }
})

watch(() => props.qualityCheckId, (newVal) => {
  if (newVal && props.visible) {
    loadDetail()
  }
})
</script>

<style scoped lang="scss">
.quality-check-detail-modal {
  // 对话框头部样式
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-status-tag {
      font-weight: 600;
    }
  }

  .detail-content {
    max-height: 70vh;
    overflow-y: auto;
    padding: 0 4px;
  }

  // 详情区域样式
  .detail-section {
    margin-bottom: 24px;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .sub-section {
      margin-bottom: 20px;

      .sub-title {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #606266;
        padding-left: 8px;
        border-left: 3px solid #409eff;
      }
    }
  }

  // 描述列表样式
  .detail-descriptions {
    margin-bottom: 16px;

    :deep(.el-descriptions__label) {
      font-weight: 500;
      color: #606266;
      width: 120px;
    }

    :deep(.el-descriptions__content) {
      color: #303133;
    }
  }

  // 状态标签样式
  .status-tag {
    font-weight: 500;
  }

  // 工单类型样式
  .work-order-type {
    display: flex;
    align-items: center;
    gap: 6px;

    .work-order-icon {
      font-size: 16px;
      color: #409eff;
    }

    .work-order-text {
      font-weight: 500;
    }
  }

  // 数值样式
  .hours-value {
    font-weight: 600;
    color: #409eff;
  }

  // 重要文本样式
  .important-text {
    font-weight: 600;
    color: #303133;
  }

  // 车牌号样式
  .plate-number {
    font-size: 16px;
    font-weight: 700;
    color: #409eff;
    font-family: 'Courier New', monospace;
  }

  // VIN码样式
  .vin-code {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #606266;
    letter-spacing: 1px;
  }

  // 车辆配置样式
  .vehicle-config {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 颜色标签样式
  .color-tag {
    font-weight: 500;
  }

  // 里程数样式
  .mileage-value {
    font-weight: 600;
    color: #67c23a;
  }

  // 车龄样式
  .vehicle-age {
    font-weight: 500;
    color: #e6a23c;
  }

  // 表格样式
  .detail-table {
    margin-bottom: 16px;

    :deep(.code-column) {
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }

    .quantity-value {
      font-weight: 600;
      color: #409eff;
    }
  }

  // 质检项目折叠面板样式
  .check-collapse {
    border: none;

    :deep(.el-collapse-item__header) {
      background-color: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      margin-bottom: 8px;
      padding: 0 16px;
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-collapse-item__content) {
      padding: 16px 0;
      border: none;
    }
  }

  // 质检项目列表样式
  .check-items-list {
    .check-item {
      padding: 12px 16px;
      margin-bottom: 8px;
      background: #fafafa;
      border-radius: 6px;
      border-left: 4px solid #e4e7ed;

      &:hover {
        background: #f0f9ff;
        border-left-color: #409eff;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .item-name {
          font-weight: 500;
          color: #303133;
        }

        .item-result {
          display: flex;
          align-items: center;
          gap: 6px;

          .result-icon {
            font-size: 18px;
          }

          .result-text {
            font-weight: 500;

            &.pass-text {
              color: #67c23a;
            }

            &.fail-text {
              color: #f56c6c;
            }
          }

          .numeric-result {
            font-weight: 600;
            color: #409eff;
            font-size: 16px;
          }

          .text-result {
            color: #606266;
            font-style: italic;
          }
        }
      }

      .item-standard {
        font-size: 12px;
        color: #909399;

        .standard-label {
          font-weight: 500;
        }

        .standard-value {
          color: #606266;
        }
      }
    }
  }

  // 审核结果标签样式
  .audit-result-tag {
    font-weight: 600;
    font-size: 14px;
  }

  // 备注内容样式
  .remark-content {
    padding: 12px;
    background: #f5f7fa;
    border-radius: 4px;
    color: #606266;
    line-height: 1.6;
    border-left: 4px solid #409eff;
  }

  // 返工信息样式
  .rework-info {
    margin-top: 16px;
    padding: 16px;
    background: #fef0f0;
    border-radius: 6px;
    border: 1px solid #fbc4c4;
  }

  // 对话框底部样式
  .dialog-footer {
    text-align: center;
    padding: 16px 0;

    .el-button {
      margin: 0 8px;
      min-width: 100px;
    }
  }
}
</style>
