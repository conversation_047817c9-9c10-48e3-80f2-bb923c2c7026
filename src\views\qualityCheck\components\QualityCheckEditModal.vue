<template>
  <el-dialog
    v-model="visible"
    :title="`质检自检 - ${detailData?.qualityCheck?.qualityCheckNo || ''}`"
    width="1200px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    destroy-on-close
    class="quality-check-edit-modal"
  >
    <!-- 标题栏进度指示 -->
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header">
        <div class="header-left">
          <span :id="titleId" :class="titleClass">
            质检自检 - {{ detailData?.qualityCheck?.qualityCheckNo || '' }}
          </span>
          <div class="progress-info">
            <span class="progress-text">已完成 {{ completedCount }}/{{ totalCount }} 项检查</span>
            <el-progress
              :percentage="progressPercentage"
              :stroke-width="6"
              :show-text="false"
              class="progress-bar"
            />
          </div>
        </div>
        <div class="header-right">
          <div class="auto-save-status">
            <el-icon v-if="autoSaving" class="saving-icon"><Loading /></el-icon>
            <span v-else-if="lastSaveTime" class="save-time">
              最后保存: {{ formatSaveTime(lastSaveTime) }}
            </span>
          </div>
        </div>
      </div>
    </template>

    <div v-loading="loading" class="edit-content">
      <template v-if="detailData">
        <!-- 基础信息区域（只读） -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">基础信息</span>
          </template>
          <!-- 使用描述列表组件，2列布局显示 -->
          <el-descriptions :column="2" border>
            <el-descriptions-item label="质检单编号">
              <span class="value-text bold">{{ detailData.qualityCheck.qualityCheckNo }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="质检状态">
              <el-tag :type="getQualityCheckStatusTagType(detailData.qualityCheck.status)">
                {{ getQualityCheckStatusText(detailData.qualityCheck.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="工单编号">
              <el-button type="primary" link @click="viewWorkOrder">
                {{ detailData.qualityCheck.workOrderNo }}
              </el-button>
            </el-descriptions-item>
            <el-descriptions-item label="工单类型">
              <div class="work-order-type">
                <el-icon class="type-icon"><Tools /></el-icon>
                <el-tag :type="getWorkOrderTypeTagType(detailData.qualityCheck.workOrderType)">
                  {{ getWorkOrderTypeText(detailData.qualityCheck.workOrderType) }}
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="是否涉及索赔">
              <el-tag :type="detailData.qualityCheck.isClaimRelated ? 'warning' : 'info'" size="small">
                {{ detailData.qualityCheck.isClaimRelated ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="是否涉及委外">
              <el-tag :type="detailData.qualityCheck.isOutsourceRelated ? 'warning' : 'info'" size="small">
                {{ detailData.qualityCheck.isOutsourceRelated ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="开工时间">
              <span class="value-text">{{ formatDateTime(detailData.qualityCheck.startTime) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="完工时间">
              <span class="value-text">{{ formatDateTime(detailData.qualityCheck.finishTime) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="预计工时">
              <span class="value-text">{{ detailData.qualityCheck.estimatedHours || 0 }}h</span>
            </el-descriptions-item>
            <el-descriptions-item label="实际工时">
              <span class="value-text">{{ detailData.qualityCheck.actualHours || 0 }}h</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              <span class="value-text">{{ formatDateTime(detailData.qualityCheck.createTime) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              <span class="value-text">{{ formatDateTime(detailData.qualityCheck.updateTime) }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 客户车辆信息区域（只读） -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">客户车辆信息</span>
          </template>

          <!-- 客户信息部分 -->
          <div class="customer-section">
            <h5 class="section-subtitle">客户信息</h5>
            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="预约人名称">
                <span class="value-text">{{ detailData.customerVehicleInfo.appointmentCustomerName || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="预约人手机号">
                <span class="value-text">{{ formatPhoneNumber(detailData.customerVehicleInfo.appointmentCustomerPhone) }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="送修人名称">
                <span class="value-text highlight">{{ detailData.customerVehicleInfo.serviceCustomerName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="送修人手机号">
                <span class="value-text">{{ formatPhoneNumber(detailData.customerVehicleInfo.serviceCustomerPhone) }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 车辆信息部分 -->
          <div class="vehicle-section">
            <h5 class="section-subtitle">车辆信息</h5>
            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="车牌号">
                <span class="plate-number">{{ detailData.customerVehicleInfo.plateNumber }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="VIN码">
                <span class="vin-code">{{ detailData.customerVehicleInfo.vinCode || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="车型">
                <span class="value-text">{{ detailData.customerVehicleInfo.vehicleModel }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="配置">
                <span class="value-text" :title="detailData.customerVehicleInfo.vehicleConfig">
                  {{ truncateText(detailData.customerVehicleInfo.vehicleConfig, 20) }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="颜色">
                <div class="color-display">
                  <span class="color-dot" :style="{ backgroundColor: getVehicleColorCode(detailData.customerVehicleInfo.vehicleColor) }"></span>
                  <span class="color-text">{{ detailData.customerVehicleInfo.vehicleColor || '-' }}</span>
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="里程数">
                <span class="value-text">{{ formatMileage(detailData.customerVehicleInfo.mileage) }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="车龄">
                <span class="value-text">{{ calculateVehicleAge(detailData.customerVehicleInfo.manufactureDate) }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 工时零件详情区域（只读，可折叠） -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="collapsible-header" @click="toggleWorkDetails">
              <span class="card-title">工时零件详情</span>
              <el-icon class="collapse-icon" :class="{ 'is-active': showWorkDetails }">
                <ArrowDown />
              </el-icon>
            </div>
          </template>
          <el-collapse-transition>
            <div v-show="showWorkDetails">
              <!-- 工时详情表格 -->
              <div class="detail-section">
                <h5>工时详情</h5>
                <el-table :data="detailData?.laborHourDetails || []" size="small" border>
                  <el-table-column type="index" label="序号" width="60" />
                  <el-table-column label="类型" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getWorkTypeTagType(row.laborType)" size="small">
                        {{ getWorkTypeText(row.laborType) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="laborCode" label="工时代码" width="120" />
                  <el-table-column prop="laborName" label="工时名称" width="200" />
                  <el-table-column label="是否委外" width="80">
                    <template #default="{ row }">
                      <el-tag :type="row.isOutsource ? 'warning' : 'info'" size="small">
                        {{ row.isOutsource ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="是否增项" width="80">
                    <template #default="{ row }">
                      <el-tag :type="row.isAdditional ? 'danger' : 'success'" size="small">
                        {{ row.isAdditional ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="标准工时" width="100">
                    <template #default="{ row }">
                      {{ row.standardHours }}h
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 零件详情表格 -->
              <div class="detail-section">
                <h5>零件详情</h5>
                <el-table :data="detailData?.partsDetails || []" size="small" border>
                  <el-table-column type="index" label="序号" width="60" />
                  <el-table-column label="类型" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getWorkTypeTagType(row.partType)" size="small">
                        {{ getWorkTypeText(row.partType) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="partCode" label="零件代码" width="120" />
                  <el-table-column prop="partName" label="零件名称" width="200" />
                  <el-table-column label="是否增项" width="80">
                    <template #default="{ row }">
                      <el-tag :type="row.isAdditional ? 'danger' : 'success'" size="small">
                        {{ row.isAdditional ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="零件数量" width="100">
                    <template #default="{ row }">
                      {{ row.quantity }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-collapse-transition>
        </el-card>

        <!-- 质检项目编辑区域（核心功能） -->
        <div class="check-items-section">
          <!-- 快捷操作工具栏 -->
          <div class="quick-actions-toolbar">
            <div class="toolbar-left">
              <span class="section-title">质检项目</span>
              <span class="progress-indicator">{{ completedCount }}/{{ totalCount }} 项已完成</span>
            </div>
            <div class="toolbar-right">
              <el-button size="small" @click="handleBatchOperation">
                批量设置
              </el-button>
              <el-button size="small" @click="handleTemplateApply">
                应用模板
              </el-button>
            </div>
          </div>

          <el-form :model="editForm" :rules="rules" ref="editFormRef">
            <div class="check-categories">
              <div
                v-for="category in groupedCheckItems"
                :key="category.categoryCode"
                class="check-category-card"
              >
                <!-- 卡片头部 -->
                <div class="category-header">
                  <div class="category-icon">{{ getCategoryIcon(category.categoryCode) }}</div>
                  <h4 class="category-title">{{ getCategoryName(category.categoryCode) }}</h4>
                  <div class="category-progress">
                    <el-icon v-if="isCategoryCompleted(category)" class="completed-icon" color="#67c23a">
                      <Check />
                    </el-icon>
                    <el-icon v-else class="pending-icon" color="#c0c4cc">
                      <Clock />
                    </el-icon>
                  </div>
                </div>

                <!-- 检查项目列表 -->
                <div class="check-items-list">
                  <div
                    v-for="item in category.items"
                    :key="item.id"
                    class="check-item"
                    :class="{ 'is-required': item.isRequired, 'is-completed': isItemCompleted(item) }"
                  >
                    <div class="item-header">
                      <div class="item-checkbox">
                        <el-checkbox
                          :model-value="isItemCompleted(item)"
                          disabled
                          class="completion-checkbox"
                        />
                      </div>
                      <span class="item-name">{{ item.itemName }}</span>
                      <span v-if="item.isRequired" class="required-mark">*</span>
                    </div>

                    <div class="item-control">
                      <!-- 数值型检查项 -->
                      <template v-if="item.itemType === 'NUMERIC'">
                        <div class="numeric-input" v-if="getItemFormIndex(item.id) !== -1">
                          <el-input-number
                            v-model="editForm.checkItems[getItemFormIndex(item.id)].numericValue"
                            :placeholder="item.standardValue || '请输入数值'"
                            :precision="getNumericPrecision(item.itemCode)"
                            :min="getNumericMin(item.itemCode)"
                            :max="getNumericMax(item.itemCode)"
                            size="small"
                            controls-position="right"
                            @change="(value) => onNumericValueChange(item.id, item.itemCode, value)"
                          />
                          <span class="unit">{{ getItemUnit(item.itemCode) }}</span>
                        </div>
                      </template>

                      <!-- 布尔型检查项（合格/不合格） -->
                      <template v-else-if="item.itemType === 'BOOLEAN'">
                        <div class="boolean-input" v-if="getItemFormIndex(item.id) !== -1">
                          <div class="result-buttons">
                            <el-button
                              :type="editForm.checkItems[getItemFormIndex(item.id)].checkResult === 'PASS' ? 'success' : 'default'"
                              :class="{ 'is-selected': editForm.checkItems[getItemFormIndex(item.id)].checkResult === 'PASS' }"
                              class="result-button pass-button"
                              @click="setCheckResult(item.id, 'PASS')"
                            >
                              合格
                            </el-button>
                            <el-button
                              :type="editForm.checkItems[getItemFormIndex(item.id)].checkResult === 'FAIL' ? 'danger' : 'default'"
                              :class="{ 'is-selected': editForm.checkItems[getItemFormIndex(item.id)].checkResult === 'FAIL' }"
                              class="result-button fail-button"
                              @click="setCheckResult(item.id, 'FAIL')"
                            >
                              不合格
                            </el-button>
                          </div>
                        </div>
                      </template>

                      <!-- 文本型检查项 -->
                      <template v-else>
                        <div class="text-input" v-if="getItemFormIndex(item.id) !== -1">
                          <el-input
                            v-model="editForm.checkItems[getItemFormIndex(item.id)].textValue"
                            type="textarea"
                            :placeholder="item.standardValue || '请输入描述'"
                            :rows="2"
                            maxlength="200"
                            show-word-limit
                            @change="onItemValueChange(item.id)"
                          />
                        </div>
                      </template>
                    </div>

                    <!-- 标准值参考和操作提示 -->
                    <div v-if="item.standardValue || getItemTip(item.itemCode)" class="item-footer">
                      <div v-if="item.standardValue" class="standard-reference">
                        <span class="reference-label">参考标准:</span>
                        <span class="reference-value">{{ item.standardValue }}</span>
                      </div>
                      <div v-if="getItemTip(item.itemCode)" class="operation-tip">
                        <el-icon class="tip-icon"><InfoFilled /></el-icon>
                        <span class="tip-text">{{ getItemTip(item.itemCode) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form>
        </div>
      </template>
    </div>

    <!-- 操作按钮区域 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="handleSave" :loading="saving">
            暂存
          </el-button>
        </div>
        <div class="footer-right">
          <el-button type="info" @click="handlePreview">
            预览
          </el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitting"
            :disabled="!canSubmit"
          >
            提交审核
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Check, Clock, Loading, InfoFilled, Tools } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type {
  QualityCheckDetail,
  QualityCheckSubmitForm,
  WorkOrderType,
  QualityCheckItem
} from '@/types/module'
import { qualityCheckApi } from '@/api/modules/qualityCheck'
import { formatDateTime } from '@/utils/dateTime'

interface Props {
  visible: boolean
  qualityCheckId: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

const loading = ref(false)
const saving = ref(false)
const submitting = ref(false)
const autoSaving = ref(false)
const detailData = ref<QualityCheckDetail | null>(null)
const editFormRef = ref<FormInstance>()
const showWorkDetails = ref(false)
const lastSaveTime = ref<Date | null>(null)
const hasUnsavedChanges = ref(false)

// 表单数据
const editForm = reactive<QualityCheckSubmitForm>({
  qualityCheckId: '',
  actualHours: 0,
  remarks: '',
  checkItems: []
})

// 表单验证规则
const rules = reactive<FormRules>({
  actualHours: [
    { required: true, message: t('common.required'), trigger: 'blur' },
    { type: 'number', min: 0, max: 24, message: t('qualityCheck.edit.hoursRange'), trigger: 'blur' }
  ]
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 分组的检查项目 - 按照文档要求的8个检查大类
const groupedCheckItems = computed(() => {
  if (!detailData.value?.checkItems) return []

  // 定义标准的8个检查大类 - 严格按照文档要求
  const standardCategories = [
    { code: 'brake_system', name: '制动系统检查', icon: '🔧' },
    { code: 'steering_system', name: '转向系统检查', icon: '🚗' },
    { code: 'tire_check', name: '轮胎检查', icon: '🛞' },
    { code: 'motor_performance', name: '电机性能检查', icon: '⚡' },
    { code: 'aircon_system', name: '空调系统检查', icon: '❄️' },
    { code: 'light_system', name: '灯光系统检查', icon: '💡' },
    { code: 'body_appearance', name: '车身外观检查', icon: '🚘' },
    { code: 'interior_cleaning', name: '内饰清洁检查', icon: '🧽' }
  ]

  const groups = new Map<string, { categoryCode: string; categoryName: string; items: QualityCheckItem[] }>()

  // 初始化所有标准分类
  standardCategories.forEach(category => {
    groups.set(category.code, {
      categoryCode: category.code,
      categoryName: category.name,
      items: []
    })
  })

  // 将检查项目分配到对应分类 - 按照文档要求的具体检查项目
  detailData.value.checkItems.forEach(item => {
    const categoryCode = item.categoryCode.toLowerCase()

    // 映射分类代码到标准分类 - 支持多种格式
    const categoryMapping = {
      // 制动系统检查
      'brake_system': 'brake_system',
      'brake': 'brake_system',
      'braking': 'brake_system',

      // 转向系统检查
      'steering_system': 'steering_system',
      'steering': 'steering_system',

      // 轮胎检查
      'tire_check': 'tire_check',
      'tire': 'tire_check',
      'wheel': 'tire_check',

      // 电机性能检查
      'motor_performance': 'motor_performance',
      'motor': 'motor_performance',
      'engine': 'motor_performance',

      // 空调系统检查
      'aircon_system': 'aircon_system',
      'aircon': 'aircon_system',
      'ac': 'aircon_system',

      // 灯光系统检查
      'light_system': 'light_system',
      'light': 'light_system',
      'lighting': 'light_system',

      // 车身外观检查
      'body_appearance': 'body_appearance',
      'body': 'body_appearance',
      'appearance': 'body_appearance',

      // 内饰清洁检查
      'interior_cleaning': 'interior_cleaning',
      'interior': 'interior_cleaning',
      'cleaning': 'interior_cleaning'
    }

    const mappedCategory = categoryMapping[categoryCode] || 'brake_system'

    if (groups.has(mappedCategory)) {
      groups.get(mappedCategory)!.items.push(item)
    }
  })

  // 返回所有分类（包括空的），保持文档要求的8个分类结构
  return Array.from(groups.values())
    .map(group => ({
      ...group,
      items: group.items.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
    }))
})

// 完成进度计算
const totalCount = computed(() => {
  return detailData.value?.checkItems?.length || 0
})

const completedCount = computed(() => {
  if (!editForm.checkItems) return 0
  return editForm.checkItems.filter(item => {
    if (item.itemType === 'BOOLEAN') {
      return !!item.checkResult
    } else if (item.itemType === 'NUMERIC') {
      return item.numericValue !== undefined && item.numericValue !== null
    } else {
      return !!item.textValue?.trim()
    }
  }).length
})

// 进度百分比
const progressPercentage = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((completedCount.value / totalCount.value) * 100)
})

// 是否可以提交
const canSubmit = computed(() => {
  const requiredItems = editForm.checkItems.filter(item => item.isRequired)
  return requiredItems.every(item => {
    if (item.itemType === 'BOOLEAN') {
      return !!item.checkResult
    } else if (item.itemType === 'NUMERIC') {
      return item.numericValue !== undefined && item.numericValue !== null
    } else {
      return !!item.textValue?.trim()
    }
  })
})

// 工具方法
const getWorkOrderTypeText = (type: WorkOrderType) => {
  return t(`qualityCheck.workOrderType.${type}`)
}

const getWorkOrderTypeTagType = (type: WorkOrderType) => {
  const typeMap = {
    'maintenance': 'success',
    'repair': 'warning',
    'insurance': 'info'
  }
  return typeMap[type] || 'info'
}

// 质检状态相关方法
const getQualityCheckStatusText = (status: string) => {
  const statusMap = {
    'pending_check': '待质检',
    'checking': '质检中',
    'pending_review': '质检待审批',
    'passed': '质检通过',
    'rework': '返工'
  }
  return statusMap[status] || status
}

const getQualityCheckStatusTagType = (status: string) => {
  const typeMap = {
    'pending_check': 'info',
    'checking': 'warning',
    'pending_review': 'primary',
    'passed': 'success',
    'rework': 'danger'
  }
  return typeMap[status] || 'info'
}

// 客户车辆信息相关方法
const formatPhoneNumber = (phone: string) => {
  if (!phone) return '-'
  // 中间4位显示*号
  if (phone.length === 11) {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
  return phone
}

const truncateText = (text: string, maxLength: number) => {
  if (!text) return '-'
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const getVehicleColorCode = (colorName: string) => {
  const colorMap = {
    '白色': '#FFFFFF',
    '黑色': '#000000',
    '银色': '#C0C0C0',
    '灰色': '#808080',
    '红色': '#FF0000',
    '蓝色': '#0000FF',
    '绿色': '#008000',
    '黄色': '#FFFF00'
  }
  return colorMap[colorName] || '#CCCCCC'
}

const formatMileage = (mileage: number) => {
  if (!mileage) return '-'
  return `${mileage.toLocaleString()} km`
}

const calculateVehicleAge = (manufactureDate: string) => {
  if (!manufactureDate) return '-'
  const now = new Date()
  const manufacture = new Date(manufactureDate)
  const diffTime = Math.abs(now.getTime() - manufacture.getTime())
  const diffYears = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365))
  const diffMonths = Math.floor((diffTime % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24 * 30))

  if (diffYears > 0) {
    return `${diffYears}年${diffMonths}月`
  } else {
    return `${diffMonths}月`
  }
}

const getWorkTypeText = (type: string) => {
  const typeMap = {
    'maintenance': '保养',
    'repair': '维修',
    'insurance': '索赔'
  }
  return typeMap[type] || type
}

const getWorkTypeTagType = (type: string) => {
  const typeMap = {
    'maintenance': 'primary',
    'repair': 'warning',
    'insurance': 'danger'
  }
  return typeMap[type] || 'info'
}

const getCategoryIcon = (categoryCode: string) => {
  const iconMap = {
    'brake_system': '🔧',
    'steering_system': '🚗',
    'tire_check': '🛞',
    'motor_performance': '⚡',
    'aircon_system': '❄️',
    'light_system': '💡',
    'body_appearance': '🚘',
    'interior_cleaning': '🧽'
  }
  return iconMap[categoryCode] || '🔧'
}

// 获取分类名称
const getCategoryName = (categoryCode: string) => {
  const nameMap = {
    'brake_system': '制动系统检查',
    'steering_system': '转向系统检查',
    'tire_check': '轮胎检查',
    'motor_performance': '电机性能检查',
    'aircon_system': '空调系统检查',
    'light_system': '灯光系统检查',
    'body_appearance': '车身外观检查',
    'interior_cleaning': '内饰清洁检查'
  }
  return nameMap[categoryCode] || categoryCode
}

// 获取检查项目操作提示 - 按照文档要求的具体检查项目
const getItemTip = (itemCode: string) => {
  const tipMap = {
    // 制动系统检查
    'brake_pedal_travel': '踩下制动踏板，检查行程是否在正常范围内',
    'brake_fluid_check': '检查制动液液位，确保在MIN-MAX之间',
    'brake_disc_thickness': '使用游标卡尺测量制动盘片厚度',
    'brake_pedal_stroke': '踩下制动踏板，检查行程是否在正常范围内',
    'brake_fluid_level': '检查制动液液位，确保在MIN-MAX之间',
    'brake_disc_pad_check': '使用游标卡尺测量制动盘片厚度',

    // 转向系统检查
    'steering_free_play': '左右转动方向盘，检查自由行程',
    'steering_wheel_play': '左右转动方向盘，检查自由行程',

    // 轮胎检查
    'tire_wear': '使用轮胎花纹深度尺测量磨损程度',
    'tire_pressure': '使用胎压计测量，建议在冷胎状态下检查',
    'tire_bolt_tightness': '使用扭力扳手检查轮胎螺栓紧固力矩',
    'tire_wear_depth': '使用轮胎花纹深度尺测量磨损程度',
    'tire_air_pressure': '使用胎压计测量，建议在冷胎状态下检查',
    'tire_bolt_torque': '使用扭力扳手检查轮胎螺栓紧固力矩',

    // 电机性能检查
    'motor_acceleration': '测试电机加速响应性能',
    'motor_acceleration_response': '测试电机加速响应性能',

    // 空调系统检查
    'aircon_cooling': '检查空调制冷效果',
    'aircon_airflow': '检查风量调节功能',
    'ac_cooling_effect': '检查空调制冷效果',
    'ac_airflow_control': '检查风量调节功能',

    // 灯光系统检查
    'headlight_check': '检查前照灯亮度和光束角度',
    'turn_signal_check': '检查转向灯闪烁频率',
    'brake_light_check': '检查刹车灯亮度',
    'hazard_light_check': '检查危险报警灯功能',
    'front_headlight': '检查前照灯亮度和光束角度',
    'turn_signal_light': '检查转向灯闪烁频率',
    'brake_light': '检查刹车灯亮度',
    'hazard_warning_light': '检查危险报警灯功能',

    // 车身外观检查
    'paint_integrity': '检查漆面完整性和光泽度',
    'scratch_repair': '检查划痕修复效果',
    'paint_surface_integrity': '检查漆面完整性和光泽度',
    'scratch_repair_effect': '检查划痕修复效果',

    // 内饰清洁检查
    'seat_cleaning': '检查座椅清洁度',
    'dashboard_cleaning': '检查仪表台清洁度',
    'carpet_cleaning': '检查地毯清洁度',
    'seat_cleanliness': '检查座椅清洁度',
    'dashboard_cleanliness': '检查仪表台清洁度',
    'carpet_cleanliness': '检查地毯清洁度'
  }
  return tipMap[itemCode] || ''
}

// 格式化保存时间
const formatSaveTime = (time: Date | null) => {
  if (!time) return ''
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else {
    return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }
}

const getItemFormIndex = (itemId: string) => {
  return editForm.checkItems.findIndex(item => item.id === itemId)
}



// 按照文档要求的数值验证规则
const getNumericMin = (itemCode: string) => {
  const minMap = {
    // 制动系统检查
    'brake_disc_thickness': 0,        // 制动盘片检查: 0-50mm
    'brake_disc_pad_check': 0,        // 制动盘片检查: 0-50mm
    'brake_pedal_travel': 0,          // 制动踏板行程: 0-200mm
    'brake_pedal_stroke': 0,          // 制动踏板行程: 0-200mm

    // 轮胎检查
    'tire_wear': 0,                   // 轮胎磨损: 0-20mm
    'tire_wear_depth': 0,             // 轮胎磨损: 0-20mm
    'tire_pressure': 1.5,             // 气压: 1.5-3.0bar
    'tire_air_pressure': 1.5          // 气压: 1.5-3.0bar
  }
  return minMap[itemCode] || 0
}

const getNumericMax = (itemCode: string) => {
  const maxMap = {
    // 制动系统检查
    'brake_disc_thickness': 50,       // 制动盘片检查: 0-50mm
    'brake_disc_pad_check': 50,       // 制动盘片检查: 0-50mm
    'brake_pedal_travel': 200,        // 制动踏板行程: 0-200mm
    'brake_pedal_stroke': 200,        // 制动踏板行程: 0-200mm

    // 轮胎检查
    'tire_wear': 20,                  // 轮胎磨损: 0-20mm
    'tire_wear_depth': 20,            // 轮胎磨损: 0-20mm
    'tire_pressure': 3.0,             // 气压: 1.5-3.0bar
    'tire_air_pressure': 3.0          // 气压: 1.5-3.0bar
  }
  return maxMap[itemCode] || 999
}

// 获取数值精度（小数位数）- 文档要求保留1-2位小数
const getNumericPrecision = (itemCode: string) => {
  const precisionMap = {
    // 制动系统检查
    'brake_disc_thickness': 1,        // 1位小数
    'brake_disc_pad_check': 1,        // 1位小数
    'brake_pedal_travel': 0,          // 整数
    'brake_pedal_stroke': 0,          // 整数

    // 轮胎检查
    'tire_wear': 1,                   // 1位小数
    'tire_wear_depth': 1,             // 1位小数
    'tire_pressure': 1,               // 1位小数
    'tire_air_pressure': 1            // 1位小数
  }
  return precisionMap[itemCode] || 1  // 默认1位小数
}

// 获取单位
const getItemUnit = (itemCode: string) => {
  const unitMap = {
    // 制动系统检查
    'brake_disc_thickness': 'mm',
    'brake_disc_pad_check': 'mm',
    'brake_pedal_travel': 'mm',
    'brake_pedal_stroke': 'mm',

    // 轮胎检查
    'tire_wear': 'mm',
    'tire_wear_depth': 'mm',
    'tire_pressure': 'bar',
    'tire_air_pressure': 'bar'
  }
  return unitMap[itemCode] || ''
}

// 数值验证 - 按照文档要求的业务逻辑验证
const validateNumericValue = (itemCode: string, value: number) => {
  const min = getNumericMin(itemCode)
  const max = getNumericMax(itemCode)
  const unit = getItemUnit(itemCode)

  if (value < min || value > max) {
    ElMessage.warning(`数值应在 ${min}-${max} ${unit} 范围内`)
    return false
  }

  // 特殊业务逻辑验证 - 按照文档要求的安全提醒

  // 制动系统安全检查
  if ((itemCode === 'brake_disc_thickness' || itemCode === 'brake_disc_pad_check') && value < 2) {
    ElMessage.warning('制动盘片厚度过低，请注意安全！')
  }

  if ((itemCode === 'brake_pedal_travel' || itemCode === 'brake_pedal_stroke') && value > 150) {
    ElMessage.warning('制动踏板行程过大，可能影响制动效果！')
  }

  // 轮胎安全检查
  if ((itemCode === 'tire_wear' || itemCode === 'tire_wear_depth') && value > 15) {
    ElMessage.warning('轮胎磨损严重，建议更换！')
  }

  if ((itemCode === 'tire_wear' || itemCode === 'tire_wear_depth') && value < 1.6) {
    ElMessage.error('轮胎花纹深度低于安全标准（1.6mm），必须更换！')
  }

  if ((itemCode === 'tire_pressure' || itemCode === 'tire_air_pressure')) {
    if (value < 2.0 || value > 2.8) {
      ElMessage.info('轮胎气压建议保持在 2.0-2.8 bar 范围内以获得最佳性能')
    }
    if (value < 1.8) {
      ElMessage.warning('轮胎气压过低，可能影响行车安全！')
    }
    if (value > 3.2) {
      ElMessage.warning('轮胎气压过高，可能导致爆胎风险！')
    }
  }

  return true
}





const isCategoryCompleted = (category: { categoryCode: string; categoryName: string; items: QualityCheckItem[] }) => {
  return category.items.every((item: QualityCheckItem) => isItemCompleted(item))
}

const isItemCompleted = (item: QualityCheckItem) => {
  const formIndex = getItemFormIndex(item.id)
  if (formIndex === -1) return false

  const formItem = editForm.checkItems[formIndex]
  if (!formItem) return false

  if (item.itemType === 'BOOLEAN') {
    return !!formItem.checkResult
  } else if (item.itemType === 'NUMERIC') {
    return formItem.numericValue !== undefined && formItem.numericValue !== null
  } else {
    return !!formItem.textValue?.trim()
  }
}

const toggleWorkDetails = () => {
  showWorkDetails.value = !showWorkDetails.value
}

const viewWorkOrder = () => {
  if (detailData.value?.qualityCheck?.workOrderNo) {
    // 跳转到工单详情页面
    console.log('查看工单详情:', detailData.value.qualityCheck.workOrderNo)
    // 这里应该实现跳转到工单详情页面的逻辑
    ElMessage.info('查看工单详情功能待实现')
  }
}

const onItemValueChange = (itemId: string) => {
  console.log('Item value changed:', itemId)
  hasUnsavedChanges.value = true
  // 触发自动保存
  debouncedAutoSave()
}

// 设置检查结果
const setCheckResult = (itemId: string, result: 'PASS' | 'FAIL') => {
  const index = getItemFormIndex(itemId)
  if (index !== -1) {
    editForm.checkItems[index].checkResult = result
    onItemValueChange(itemId)
  }
}

// 数值输入变化处理
const onNumericValueChange = (itemId: string, itemCode: string, value: number | undefined) => {
  if (value !== undefined && value !== null) {
    if (validateNumericValue(itemCode, value)) {
      onItemValueChange(itemId)
    }
  } else {
    onItemValueChange(itemId)
  }
}

// 防抖的自动保存
let autoSaveTimer: number | null = null
const debouncedAutoSave = () => {
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer)
  }
  autoSaveTimer = setTimeout(() => {
    autoSave()
  }, 2000) // 2秒后自动保存
}

// 自动保存方法
const autoSave = async () => {
  if (autoSaving.value || !detailData.value) return

  try {
    autoSaving.value = true
    await qualityCheckApi.submitQualityCheck({
      ...editForm,
      isDraft: true
    })
    lastSaveTime.value = new Date()
  } catch (error) {
    console.error('Auto save failed:', error)
  } finally {
    autoSaving.value = false
  }
}



// 初始化表单数据
const initFormData = () => {
  if (!detailData.value) return

  editForm.qualityCheckId = props.qualityCheckId
  editForm.actualHours = detailData.value.qualityCheck.actualHours || 0
  editForm.remarks = ''

  // 初始化检查项目表单数据
  editForm.checkItems = detailData.value.checkItems.map(item => ({
    id: item.id,
    qualityCheckId: props.qualityCheckId,
    itemCode: item.itemCode,
    itemType: item.itemType,
    checkResult: item.checkResult,
    numericValue: item.numericValue,
    textValue: item.textValue,
    isRequired: item.isRequired
  }))
}

// 加载详情数据
const loadDetail = async () => {
  if (!props.qualityCheckId) return
  
  try {
    loading.value = true
    const response = await qualityCheckApi.getQualityCheckDetail(props.qualityCheckId)
    detailData.value = response
    await nextTick()
    initFormData()
  } catch (error) {
    console.error('Load quality check detail failed:', error)
    ElMessage.error(t('common.loadDataFailed'))
  } finally {
    loading.value = false
  }
}

// 验证表单
const validateForm = async () => {
  if (!editFormRef.value) return false
  
  try {
    await editFormRef.value.validate()
    return true
  } catch (error) {
    console.error('Form validation failed:', error)
    return false
  }
}

// 事件处理
const handleClose = async () => {
  // 检查是否有未保存的更改
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要离开吗？',
        '确认离开',
        {
          type: 'warning',
          confirmButtonText: '确定离开',
          cancelButtonText: '继续编辑'
        }
      )
    } catch {
      return // 用户取消离开
    }
  }

  visible.value = false
  hasUnsavedChanges.value = false
}

const handleSave = async () => {
  try {
    saving.value = true
    await qualityCheckApi.submitQualityCheck({
      ...editForm,
      isDraft: true
    })

    lastSaveTime.value = new Date()
    hasUnsavedChanges.value = false
    ElMessage.success('暂存成功')
  } catch (error) {
    console.error('Save quality check failed:', error)
    ElMessage.error('暂存失败')
  } finally {
    saving.value = false
  }
}

const handlePreview = () => {
  // 预览功能
  ElMessage.info('预览功能待实现')
}

// 批量操作 - 文档要求的效率提升功能
const handleBatchOperation = () => {
  ElMessage.info('批量操作功能待实现')
  // 这里可以实现批量设置相同类型项目为合格/不合格
}

// 模板应用 - 文档要求的效率提升功能
const handleTemplateApply = () => {
  ElMessage.info('模板应用功能待实现')
  // 这里可以实现常用配置快速应用
}

const handleSubmit = async () => {
  const valid = await validateForm()
  if (!valid) return

  // 检查必填项是否都已填写
  const requiredItems = editForm.checkItems.filter(item => item.isRequired)
  const missingRequired = requiredItems.some(item => {
    if (item.itemType === 'BOOLEAN') {
      return !item.checkResult
    } else if (item.itemType === 'NUMERIC') {
      return item.numericValue === undefined || item.numericValue === null
    } else {
      return !item.textValue?.trim()
    }
  })

  if (missingRequired) {
    ElMessage.error('请完成所有必填项目的检查')
    return
  }

  // 业务逻辑验证
  const failedItems = editForm.checkItems.filter(item => item.checkResult === 'FAIL')
  const totalItems = editForm.checkItems.length
  const failedPercentage = (failedItems.length / totalItems) * 100

  // 不合格项目过多提醒
  if (failedPercentage > 30) {
    try {
      await ElMessageBox.confirm(
        `检测到 ${failedItems.length} 项不合格（占比 ${failedPercentage.toFixed(1)}%），请确认是否继续提交？`,
        '不合格项目较多',
        {
          type: 'warning',
          confirmButtonText: '确认提交',
          cancelButtonText: '重新检查'
        }
      )
    } catch {
      return
    }
  }

  // 关键安全项目检查 - 按照文档要求的安全项目
  const criticalItems = [
    'brake_pedal_travel', 'brake_pedal_stroke',
    'brake_fluid_check', 'brake_fluid_level',
    'brake_disc_thickness', 'brake_disc_pad_check',
    'tire_bolt_tightness', 'tire_bolt_torque',
    'tire_pressure', 'tire_air_pressure'
  ]
  const failedCriticalItems = failedItems.filter(item =>
    criticalItems.some(critical => item.itemCode?.includes(critical))
  )

  if (failedCriticalItems.length > 0) {
    try {
      await ElMessageBox.confirm(
        '检测到关键安全项目不合格，这可能影响行车安全，请确认是否继续提交？',
        '安全警告',
        {
          type: 'error',
          confirmButtonText: '确认提交',
          cancelButtonText: '重新检查'
        }
      )
    } catch {
      return
    }
  }
  
  try {
    await ElMessageBox.confirm(
      t('qualityCheck.edit.submitConfirm'),
      t('common.confirm'),
      {
        type: 'warning'
      }
    )
    
    submitting.value = true
    await qualityCheckApi.submitQualityCheck({
      ...editForm,
      isDraft: false
    })

    ElMessage.success(t('qualityCheck.edit.submitSuccess'))
    emit('refresh')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Submit quality check failed:', error)
      ElMessage.error(t('qualityCheck.edit.submitFailed'))
    }
  } finally {
    submitting.value = false
  }
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+S 快速保存
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    handleSave()
  }
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal && props.qualityCheckId) {
    loadDetail()
    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeydown)
  } else {
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeydown)
  }
})

watch(() => props.qualityCheckId, (newVal) => {
  if (newVal && props.visible) {
    loadDetail()
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped lang="scss">
.quality-check-edit-modal {
  :deep(.el-dialog__body) {
    padding: 0 20px 20px;
  }

  // 对话框头部样式
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;

    .header-left {
      flex: 1;

      .progress-info {
        margin-top: 8px;
        display: flex;
        flex-direction: column;
        gap: 6px;

        .progress-text {
          font-size: 14px;
          color: #409eff;
          font-weight: 500;
        }

        .progress-bar {
          width: 200px;
        }
      }
    }

    .header-right {
      .auto-save-status {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #909399;

        .saving-icon {
          animation: spin 1s linear infinite;
          color: #409eff;
        }

        .save-time {
          color: #67c23a;
        }
      }
    }
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .edit-content {
    max-height: 75vh;
    overflow-y: auto;
  }

  .info-card {
    margin-bottom: 20px;

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .info-item {
      margin-bottom: 12px;

      label {
        display: inline-block;
        min-width: 100px;
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
      }

      .value-text {
        color: #303133;

        &.bold {
          font-weight: 600;
        }

        &.highlight {
          font-weight: 600;
          color: #409eff;
        }
      }
    }

    // 工单类型样式
    .work-order-type {
      display: flex;
      align-items: center;
      gap: 6px;

      .type-icon {
        color: #909399;
        font-size: 14px;
      }
    }

    // 客户车辆信息区域样式
    .customer-section,
    .vehicle-section {
      margin-bottom: 20px;

      .section-subtitle {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #409eff;
        border-left: 3px solid #409eff;
        padding-left: 8px;
      }
    }

    // 车牌号突出显示
    .plate-number {
      font-size: 16px;
      font-weight: 700;
      color: #e6a23c;
      background: #fdf6ec;
      padding: 2px 8px;
      border-radius: 4px;
      border: 1px solid #f5dab1;
    }

    // VIN码等宽字体
    .vin-code {
      font-family: 'Courier New', monospace;
      font-size: 14px;
      color: #606266;
      background: #f5f7fa;
      padding: 2px 6px;
      border-radius: 3px;
    }

    // 颜色显示
    .color-display {
      display: flex;
      align-items: center;
      gap: 6px;

      .color-dot {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 1px solid #dcdfe6;
        flex-shrink: 0;
      }

      .color-text {
        color: #303133;
      }
    }
  }

  .collapsible-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .collapse-icon {
      transition: transform 0.3s;

      &.is-active {
        transform: rotate(180deg);
      }
    }
  }

  .detail-section {
    margin-bottom: 20px;

    h5 {
      margin: 0 0 10px 0;
      font-size: 14px;
      font-weight: 500;
      color: #409eff;
    }
  }

  .check-items-section {
    margin-bottom: 20px;

    .quick-actions-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: #f8f9fa;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 20px;

      .toolbar-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .progress-indicator {
          font-size: 14px;
          color: #409eff;
          background: #ecf5ff;
          padding: 4px 12px;
          border-radius: 12px;
        }
      }

      .toolbar-right {
        display: flex;
        gap: 8px;
      }
    }
  }

  .check-categories {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .check-category-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #fff;
    overflow: hidden;

    .category-header {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;

      .category-icon {
        font-size: 20px;
        margin-right: 10px;
      }

      .category-title {
        flex: 1;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .category-progress {
        .completed-icon,
        .pending-icon {
          font-size: 18px;
        }
      }
    }

    .check-items-list {
      padding: 20px;
    }

    .check-item {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background: #fafbfc;
      transition: all 0.3s ease;

      &.is-required {
        border-left: 4px solid #f56c6c;
      }

      &.is-completed {
        background: #f0f9ff;
        border-color: #409eff;

        .item-name {
          color: #409eff;
          font-weight: 600;
        }
      }

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      .item-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .item-checkbox {
          margin-right: 8px;

          .completion-checkbox {
            :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
              background-color: #67c23a;
              border-color: #67c23a;
            }

            :deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
              background-color: #f5f7fa;
              border-color: #dcdfe6;

              &::after {
                border-color: #c0c4cc;
              }
            }
          }
        }

        .item-name {
          flex: 1;
          font-weight: 500;
          color: #303133;
        }

        .required-mark {
          color: #f56c6c;
          font-weight: bold;
          margin-left: 4px;
        }
      }

      .item-control {
        margin-bottom: 10px;

        .numeric-input {
          display: flex;
          align-items: center;
          gap: 8px;

          .el-input-number {
            flex: 1;
          }

          .unit {
            color: #909399;
            font-size: 12px;
            white-space: nowrap;
          }
        }

        .boolean-input {
          .result-buttons {
            display: flex;
            gap: 12px;
          }

          .result-button {
            flex: 1;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;

            &.pass-button {
              &:not(.is-selected) {
                background-color: #f5f7fa;
                border-color: #dcdfe6;
                color: #606266;

                &:hover {
                  background-color: #f0f9ff;
                  border-color: #67c23a;
                  color: #67c23a;
                }
              }

              &.is-selected {
                background-color: #67c23a;
                border-color: #67c23a;
                color: #fff;
                box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
              }
            }

            &.fail-button {
              &:not(.is-selected) {
                background-color: #f5f7fa;
                border-color: #dcdfe6;
                color: #606266;

                &:hover {
                  background-color: #fef0f0;
                  border-color: #f56c6c;
                  color: #f56c6c;
                }
              }

              &.is-selected {
                background-color: #f56c6c;
                border-color: #f56c6c;
                color: #fff;
                box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
              }
            }
          }
        }

        .text-input {
          .el-textarea {
            :deep(.el-textarea__inner) {
              resize: none;
            }
          }
        }
      }

      .item-footer {
        margin-top: 12px;

        .standard-reference {
          margin-bottom: 6px;
          font-size: 12px;
          color: #909399;

          .reference-label {
            font-weight: 500;
            margin-right: 4px;
          }

          .reference-value {
            color: #409eff;
          }
        }

        .operation-tip {
          display: flex;
          align-items: flex-start;
          gap: 4px;
          padding: 6px 8px;
          background: #f0f9ff;
          border-radius: 4px;
          border-left: 3px solid #409eff;

          .tip-icon {
            color: #409eff;
            font-size: 14px;
            margin-top: 1px;
            flex-shrink: 0;
          }

          .tip-text {
            font-size: 12px;
            color: #606266;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;

    .footer-left,
    .footer-right {
      display: flex;
      gap: 12px;
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .quality-check-edit-modal {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }

    .check-categories {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .check-category-card {
      .check-item {
        .item-control {
          .result-buttons {
            .result-button {
              padding: 8px 16px;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .quality-check-edit-modal {
    :deep(.el-dialog) {
      width: 100% !important;
      margin: 0;
      height: 100vh;
      border-radius: 0;
    }

    .dialog-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .header-left {
        width: 100%;

        .progress-info {
          .progress-bar {
            width: 100%;
          }
        }
      }
    }

    .check-categories {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .check-category-card {
      .check-item {
        padding: 12px;

        .item-control {
          .result-buttons {
            flex-direction: column;
            gap: 8px;

            .result-button {
              width: 100%;
              padding: 12px;
              font-size: 16px;
            }
          }

          .numeric-input {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;

            .el-input-number {
              width: 100%;
            }
          }
        }
      }
    }

    .dialog-footer {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      border-top: 1px solid #e4e7ed;
      padding: 16px;
      flex-direction: column;
      gap: 12px;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

      .footer-left,
      .footer-right {
        width: 100%;
        justify-content: center;

        .el-button {
          flex: 1;
          max-width: 200px;
        }
      }
    }

    .edit-content {
      padding-bottom: 120px; // 为底部固定按钮留出空间
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style> 