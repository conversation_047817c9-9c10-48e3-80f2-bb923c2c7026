<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('orderDetailTitle')"
    width="90%"
    top="5vh"
    :close-on-click-modal="false"
    class="factory-order-detail-dialog"
  >
    <div v-loading="loading" style="min-height: 400px;">
      <div v-if="orderDetail" class="order-detail-content">
        <!-- 订单概要信息 -->
        <div class="order-summary">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="summary-item">
                <span class="label">{{ t('orderNumber') }}：</span>
                <span class="value">{{ orderDetail.orderNo }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="summary-item">
                <span class="label">{{ t('creationTime') }}：</span>
                <span class="value">{{ formatDateTime(orderDetail.createTime) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息区域 -->
        <el-card class="info-card">
          <template #header>
            <span>{{ t('customerInfo') }} - {{ t('personalDetails') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('ordererNameField') }}：</span>
                <span class="value">{{ orderDetail.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('ordererPhoneField') }}：</span>
                <span class="value">{{ orderDetail.customerPhone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('buyerNameField') }}：</span>
                <span class="value">{{ orderDetail.customerName }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('buyerPhoneField') }}：</span>
                <span class="value">{{ orderDetail.customerPhone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('buyerIdType') }}：</span>
                <span class="value">{{ orderDetail.idType || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('buyerIdNumber') }}：</span>
                <span class="value">{{ orderDetail.idNumber || '-' }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('buyerEmail') }}：</span>
                <span class="value">{{ orderDetail.customerInfo?.email || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('buyerState') }}：</span>
                <span class="value">{{ orderDetail.state || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('buyerCity') }}：</span>
                <span class="value">{{ orderDetail.city || '-' }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="16">
              <div class="info-item">
                <span class="label">{{ t('buyerAddress') }}：</span>
                <span class="value">{{ orderDetail.customerInfo?.address || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('buyerPostcode') }}：</span>
                <span class="value">{{ orderDetail.zipCode || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 购车门店信息 -->
        <el-card class="info-card">
          <template #header>
            <span>{{ t('storeInfo') }} - {{ t('preferredOutletSalesAdvisor') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ t('region') }}：</span>
                <span class="value">{{ orderDetail.region || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ t('dealerCity') }}：</span>
                <span class="value">{{ orderDetail.dealerCity || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ t('dealerName') }}：</span>
                <span class="value">{{ getNameByCode(DICTIONARY_TYPES.STORE, orderDetail.storeName) }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ t('salesConsultant') }}：</span>
                <span class="value">{{ orderDetail.salesAdvisorName || orderDetail.salesman || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 购车信息区域 -->
        <el-card class="info-card">
          <template #header>
            <span>{{ t('purchaseInfo') }} - {{ t('purchaseDetails') }}</span>
          </template>

          <el-tabs v-model="activeTab" type="border-card">
            <!-- 车辆信息Tab -->
            <el-tab-pane :label="t('vehicleInfoTab')" name="vehicle">
              <div class="tab-content">
                <div class="section-title">{{ t('vehicleInfo') }}</div>
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('model') }}：</span>
                      <span class="value">{{ getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, orderDetail.model) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('variant') }}：</span>
                      <span class="value">{{ getNameByCode(DICTIONARY_TYPES.VEHICLE_VARIANT, orderDetail.variant) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ tc('color') }}：</span>
                      <span class="value">{{ getNameByCode(DICTIONARY_TYPES.VEHICLE_COLOR, orderDetail.color) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('vin') }}：</span>
                      <span class="value">{{ orderDetail.vin || '-' }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('engineNumber') }}：</span>
                      <span class="value">{{ orderDetail.engineNumber || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('chassisNumber') }}：</span>
                      <span class="value">{{ orderDetail.chassisNumber || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('vehiclePrice') }}：</span>
                      <span class="value">RM {{ orderDetail.vehiclePrice?.toLocaleString() || '0' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('deliveryDate') }}：</span>
                      <span class="value">{{ formatDateTime(orderDetail.deliveryDate) }}</span>
                    </div>
                  </el-col>
                </el-row>

                <div class="section-title">{{ t('accessoriesInfo') }}</div>
                <el-table :data="orderDetail.accessories || []" border style="width: 100%; margin-bottom: 20px;">
                  <el-table-column prop="name" :label="t('accessoryName')" />
                  <el-table-column prop="quantity" :label="t('quantity')" width="100" />
                  <el-table-column prop="unitPrice" :label="t('unitPrice')" width="120">
                    <template #default="scope">
                      RM {{ scope.row.unitPrice?.toLocaleString() || '0' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="totalPrice" :label="t('totalPrice')" width="120">
                    <template #default="scope">
                      RM {{ scope.row.totalPrice?.toLocaleString() || '0' }}
                    </template>
                  </el-table-column>
                </el-table>

                <div class="total-amount">
                  <span class="label">{{ t('accessoriesTotalAmount') }}：</span>
                  <span class="value amount">RM {{ (orderDetail.totalAccessoriesAmount || 0).toLocaleString() }}</span>
                </div>
              </div>
            </el-tab-pane>

            <!-- 开票信息Tab -->
            <el-tab-pane :label="t('invoiceInfoTab')" name="invoice">
              <div class="tab-content">
                <div class="section-title">{{ t('invoiceInfo') }}</div>
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('invoiceNumber') }}：</span>
                      <span class="value">{{ orderDetail.invoiceNumber || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('invoiceDate') }}：</span>
                      <span class="value">{{ formatDateTime(orderDetail.invoiceDate) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('invoiceAmount') }}：</span>
                      <span class="value">RM {{ orderDetail.invoiceAmount?.toLocaleString() || '0' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('invoiceStatus') }}：</span>
                      <span class="value">{{ getNameByCode(DICTIONARY_TYPES.INVOICE_STATUS, orderDetail.invoiceStatus) }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">{{ t('invoiceCompany') }}：</span>
                      <span class="value">{{ orderDetail.invoiceCompany || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">{{ t('invoiceAddress') }}：</span>
                      <span class="value">{{ orderDetail.invoiceAddress || '-' }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>

            <!-- 服务&权益信息Tab -->
            <el-tab-pane :label="t('serviceRightsTab')" name="service">
              <div class="tab-content">
                <div class="section-title">{{ t('serviceRightsInfo') }}</div>
                <el-table :data="orderDetail.services || []" border style="width: 100%; margin-bottom: 20px;">
                  <el-table-column prop="serviceName" :label="t('serviceName')" />
                  <el-table-column prop="serviceType" :label="t('serviceType')" />
                  <el-table-column prop="servicePrice" :label="t('servicePrice')" width="120">
                    <template #default="scope">
                      RM {{ scope.row.servicePrice?.toLocaleString() || '0' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="validityPeriod" :label="t('validityPeriod')" />
                  <el-table-column prop="serviceStatus" :label="t('serviceStatus')" />
                </el-table>

                <div class="total-amount">
                  <span class="label">{{ t('servicesTotalAmount') }}：</span>
                  <span class="value amount">RM {{ (orderDetail.totalServicesAmount || 0).toLocaleString() }}</span>
                </div>
              </div>
            </el-tab-pane>

            <!-- 付款信息Tab -->
            <el-tab-pane :label="t('paymentInfoTab')" name="payment">
              <div class="tab-content">
                <div class="section-title">{{ t('paymentInfo') }}</div>
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('totalInvoicePrice') }}：</span>
                      <span class="value">RM {{ orderDetail.totalInvoicePrice?.toLocaleString() || '0' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('downPaymentAmount') }}：</span>
                      <span class="value">RM {{ orderDetail.downPaymentAmount?.toLocaleString() || '0' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('loanAmount') }}：</span>
                      <span class="value">RM {{ orderDetail.loanAmount?.toLocaleString() || '0' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('balanceAmount') }}：</span>
                      <span class="value">RM {{ orderDetail.finalPaymentAmount?.toLocaleString() || '0' }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>

            <!-- 保险信息Tab -->
            <el-tab-pane :label="t('insuranceInfoTab')" name="insurance">
              <div class="tab-content">
                <div class="section-title">{{ t('insuranceInfo') }}</div>
                <el-table :data="orderDetail.policies || []" border style="width: 100%; margin-bottom: 20px;">
                  <el-table-column prop="policyNumber" :label="t('policyNumber')" />
                  <el-table-column prop="insuranceType" :label="t('insuranceType')" />
                  <el-table-column prop="insuranceCompany" :label="t('insuranceCompany')" />
                  <el-table-column prop="effectiveDate" :label="t('effectiveDateField')" />
                  <el-table-column prop="expiryDate" :label="t('expirationDateField')" />
                  <el-table-column prop="price" :label="t('insurancePrice')">
                    <template #default="scope">
                      RM {{ scope.row.price?.toLocaleString() || '0' }}
                    </template>
                  </el-table-column>
                </el-table>

                <div class="total-amount">
                  <span class="label">{{ t('insurancesTotalAmount') }}：</span>
                  <span class="value amount">RM {{ orderDetail.totalInsuranceAmount?.toLocaleString() || '0' }}</span>
                </div>

                <div class="notes-section">
                  <div class="info-item">
                    <span class="label">{{ t('insuranceNotes') }}：</span>
                    <span class="value">{{ orderDetail.remarks || '-' }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- OTR费用信息Tab -->
            <el-tab-pane :label="t('otrFeesTab')" name="otr">
              <div class="tab-content">
                <div class="section-title">{{ t('otrFeesInfo') }}</div>
                <el-table :data="orderDetail.otrFees || []" border style="width: 100%; margin-bottom: 20px;">
                  <el-table-column prop="invoiceNumber" :label="t('ticketNumber')" />
                  <el-table-column prop="item" :label="t('feeItem')" />
                  <el-table-column prop="price" :label="t('feePrice')" width="120">
                    <template #default="scope">
                      RM {{ scope.row.price?.toLocaleString() || '0' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="effectiveDate" :label="t('effectiveDateField')" />
                  <el-table-column prop="expiryDate" :label="t('expirationDateField')" />
                </el-table>

                <div class="total-amount">
                  <span class="label">{{ t('otrFeesTotalAmount') }}：</span>
                  <span class="value amount">RM {{ (orderDetail.totalOtrAmount || 0).toLocaleString() }}</span>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <!-- 订单变更记录 -->
        <el-card class="info-card">
          <template #header>
            <span>{{ t('changeRecordsTab') }} - {{ t('changeRecordsInfo') }}</span>
          </template>

          <el-table :data="orderDetail.changeRecords || []" border style="width: 100%;">
            <el-table-column prop="originalContent" :label="t('originalContent')" />
            <el-table-column prop="changedContent" :label="t('changedContent')" />
            <el-table-column prop="createdBy" :label="t('operator')" width="120" />
            <el-table-column prop="createdAt" :label="t('operationTime')" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.createdAt) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <!-- 底部价格栏 -->
    <div v-if="orderDetail" class="footer-bar">
      <div class="price-info">
        <span class="price-item">
          <span class="label">{{ t('vehicleInvoicePrice') }}：</span>
          <span class="value">RM {{ orderDetail.totalInvoicePrice?.toLocaleString() || '0' }}</span>
        </span>
        <span class="price-item">
          <span class="label">{{ t('remainingReceivable') }}：</span>
          <span class="value amount">RM {{ orderDetail.remainingReceivableAmount?.toLocaleString() || '0' }}</span>
        </span>
      </div>
      <el-button @click="handleClose">{{ t('backToList') }}</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { FactoryOrderDetail } from '@/types/sales/factoryOrderManagement';
import { getFactoryOrderDetail } from '@/api/modules/sales/factoryOrderManagement';

// Props
interface Props {
  visible: boolean;
  orderNumber: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 国际化
const { t } = useModuleI18n('sales.factoryOrderManagement');
const { t: tc } = useModuleI18n('common');

// 数据字典
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.VEHICLE_VARIANT,
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.LOAN_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS
]);

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const activeTab = ref('vehicle')
const orderDetail = ref<FactoryOrderDetail | null>(null)

// 标签类型映射函数（复用主页面的逻辑）
const getOrderStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01040001': 'warning',
    '01040002': 'info',
    '01040003': 'success',
    '01040004': 'danger'
  };
  return typeMap[status] || 'info';
};

const getPaymentStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01060001': 'warning',
    '01060002': 'primary',
    '01060003': 'success',
    '01060004': 'danger'
  };
  return typeMap[status] || 'info';
};

const getLoanStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01070001': 'warning',
    '01070002': 'success',
    '01070003': 'danger'
  };
  return typeMap[status] || 'info';
};

const getInsuranceStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01080001': 'warning',
    '01080002': 'success',
    '01080003': 'danger'
  };
  return typeMap[status] || 'info';
};

const getJpjStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01090001': 'warning',
    '01090002': 'success',
    '01090003': 'danger'
  };
  return typeMap[status] || 'info';
};

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-';
  try {
    return new Date(dateString).toLocaleString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return dateString;
  }
};

// 监听弹窗显示
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.orderNumber) {
    loadOrderDetail()
  }
})

// 监听弹窗关闭
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    orderDetail.value = null
    activeTab.value = 'vehicle'
  }
})

// 加载订单详情
const loadOrderDetail = async () => {
  loading.value = true
  try {
    const response = await getFactoryOrderDetail(props.orderNumber)
    console.log('订单详情响应:', response)
    // 根据API响应结构，数据在response.result中
    orderDetail.value = response.result
  } catch (error) {
    console.error('Failed to load order detail:', error)
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.factory-order-detail-dialog {
  .order-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .order-summary {
    padding: 20px;
    background: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 20px;

    .summary-item {
      .label {
        font-weight: bold;
        color: #303133;
      }

      .value {
        color: #606266;
      }
    }
  }

  .info-card {
    margin-bottom: 20px;

    .info-item {
      margin-bottom: 15px;

      .label {
        display: inline-block;
        width: 120px;
        font-weight: bold;
        color: #303133;
      }

      .value {
        color: #606266;

        &.amount {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }
  }

  .detail-tabs {
    .tab-content {
      padding: 20px 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
    }

    .total-amount {
      text-align: right;
      padding: 15px 0;
      border-top: 1px solid #e4e7ed;

      .label {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .value.amount {
        font-size: 18px;
        font-weight: bold;
        color: #f56c6c;
        margin-left: 10px;
      }
    }

    .notes-section {
      margin-top: 20px;
      padding: 15px;
      background: #f5f7fa;
      border-radius: 4px;
    }
  }

  .footer-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;

    .price-info {
      display: flex;
      gap: 30px;

      .price-item {
        .label {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
        }

        .value {
          font-size: 16px;
          color: #606266;
          margin-left: 10px;

          &.amount {
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
