<template>
  <el-dialog
    v-model="showModal"
    :title="t('customerDetail.title')"
    :before-close="handleClose"
    width="80%"
    style="max-width: 1200px"
  >
    <!-- 详情标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane name="basic" :label="t('customerDetail.tabs.basic')">
        <div class="info-grid">
          <div class="info-item" v-for="(item, index) in basicInfo" :key="index">
            <span class="info-label">{{ item.label }}:</span>
            <span class="info-value">{{ item.value }}</span>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane name="stores" :label="t('customerDetail.tabs.stores')">
        <div class="store-association">
          <h4>{{ t('customerDetail.storeAssociation.title') }} ({{ t('customerDetail.storeAssociation.storeCount', { count: storeAssociations.associatedStoreCount || 0 }) }})</h4>
          <div class="association-item" v-for="(item, index) in storeAssociations.storeAssociationRecords" :key="index">
            <div class="store-info">
              <div class="store-name">{{ item.storeName }}</div>
              <div class="store-details">
                {{ t('customerDetail.storeAssociation.associationTime') }}: {{ item.leadAssociationTime }} |
                {{ t('customerDetail.storeAssociation.associationReason') }}: {{ item.associationReason }} |
                {{ t('customerDetail.storeAssociation.currentAdvisor') }}: {{ item.currentSalesAdvisor }}
              </div>
            </div>
            <div class="followup-status">
              <div>
                <el-tag >{{ getNameByCode(DICTIONARY_TYPES.INTENT_LEVEL, item.currentIntentLevel )}}{{ t('customerDetail.storeAssociation.level') }}</el-tag>
              </div>
              <div class="last-followup">
                {{ item.lastFollowUpTime ? `${t('customerDetail.storeAssociation.lastFollowUp')}: ${item.lastFollowUpTime}` : t('customerDetail.storeAssociation.noFollowUp') }}
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane name="followup" :label="t('customerDetail.tabs.followup')">
        <el-table
          :data="followUpRecords.followUpRecords"
          border
          stripe
          style="width: 100%"
          :default-sort="{prop: 'followUpTime', order: 'descending'}"
        >
          <el-table-column prop="storeName" :label="t('customerDetail.followUpTable.store')" />
          <el-table-column prop="salesAdvisor" :label="t('customerDetail.followUpTable.advisor')" />
          <el-table-column prop="followUpMethod" :label="t('customerDetail.followUpTable.method')" >
            <template #default="{ row }">
              <el-tag>{{ getNameByCode(DICTIONARY_TYPES.FOLLOW_UP_METHOD, row.followUpMethod ) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="followUpTime" :label="t('customerDetail.followUpTable.time')" sortable />
          <el-table-column prop="currentIntentLevel" :label="t('customerDetail.followUpTable.intentLevel')">
            <template #default="{ row }">
              <el-tag>{{ getNameByCode(DICTIONARY_TYPES.INTENT_LEVEL, row.currentIntentLevel ) }}{{ t('customerDetail.storeAssociation.level') }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="followUpDetails" :label="t('customerDetail.followUpTable.details')" />
        </el-table>
        <el-pagination
          v-if="followUpRecords.followUpRecords && followUpRecords.followUpRecords.length > 10"
          layout="prev, pager, next"
          :total="followUpRecords.totalCount"
          :page-size="10"
          class="mt-20"
        />
      </el-tab-pane>

      <el-tab-pane name="testdrive" :label="t('customerDetail.tabs.testdrive')">
        <el-table
          :data="testDriveRecords.testDriveRecords"
          border
          stripe
          style="width: 100%"
          :default-sort="{prop: 'testDriveTime', order: 'descending'}"
        >
          <el-table-column prop="storeName" :label="t('customerDetail.testDriveTable.store')" />
          <el-table-column prop="driverName" :label="t('customerDetail.testDriveTable.driver')" />
          <el-table-column prop="phoneNumber" :label="t('customerDetail.testDriveTable.phone')" />
          <el-table-column prop="model" :label="t('customerDetail.testDriveTable.model')" />
          <el-table-column prop="variant" :label="t('customerDetail.testDriveTable.variant')" />
          <el-table-column prop="testDriveTime" :label="t('customerDetail.testDriveTable.time')" />
          <el-table-column prop="customerFeedback" :label="t('customerDetail.testDriveTable.feedback')" />
        </el-table>
        <el-pagination
          v-if="testDriveRecords.testDriveRecords && testDriveRecords.testDriveRecords.length > 10"
          layout="prev, pager, next"
          :total="testDriveRecords.totalCount"
          :page-size="10"
          class="mt-20"
        />
      </el-tab-pane>

      <el-tab-pane name="failed" :label="t('customerDetail.tabs.failed')">
        <el-table
          :data="defeatRecords.defeatRecords"
          border
          stripe
          style="width: 100%"
          :default-sort="{prop: 'defeatTime', order: 'descending'}"
        >
          <el-table-column prop="markTime" :label="t('customerDetail.defeatTable.markTime')" />
          <el-table-column prop="storeName" :label="t('customerDetail.defeatTable.store')" />
          <el-table-column prop="markerName" :label="t('customerDetail.defeatTable.marker')" />
          <el-table-column prop="defeatReason" :label="t('customerDetail.defeatTable.reason')" />
        </el-table>
        <el-pagination
          v-if="defeatRecords.defeatRecords && defeatRecords.defeatRecords.length > 10"
          layout="prev, pager, next"
          :total="defeatRecords.totalCount"
          :page-size="10"
          class="mt-20"
        />
      </el-tab-pane>

      <el-tab-pane name="history" :label="t('customerDetail.tabs.history')">
        <el-table
          :data="changeHistory.changeHistoryRecords"
          border
          stripe
          style="width: 100%"
          :default-sort="{prop: 'changedTime', order: 'descending'}"
        >
          <el-table-column prop="changeTime" :label="t('customerDetail.historyTable.changeTime')" sortable />
          <el-table-column prop="storeName" :label="t('customerDetail.historyTable.store')" />
          <el-table-column prop="changeType" :label="t('customerDetail.historyTable.changeType')" />
          <el-table-column prop="operatorName" :label="t('customerDetail.historyTable.operator')" />
          <el-table-column prop="originalValue" :label="t('customerDetail.historyTable.oldValue')" />
          <el-table-column prop="newValue" :label="t('customerDetail.historyTable.newValue')" />
        </el-table>
        <el-pagination
          v-if="changeHistory.changeHistoryRecords && changeHistory.changeHistoryRecords.length > 10"
          layout="prev, pager, next"
          :total="changeHistory.totalCount"
          :page-size="10"
          class="mt-20"
        />
      </el-tab-pane>

      <el-tab-pane name="analytics" :label="t('customerDetail.tabs.analytics')">
        <div class="stats-container">
          <div class="stats-card">
            <h3>{{ t('customerDetail.analytics.totalFollowUp') }}</h3>
            <div class="number">{{ performanceAnalysis.totalFollowUpCount || 0 }}</div>
            <div class="trend">
              <span v-if="performanceAnalysis.totalAssociatedStoreCount > 0">
                {{ t('customerDetail.analytics.crossStores', { count: performanceAnalysis.totalAssociatedStoreCount }) }}
              </span>
              <span v-else>{{ t('customerDetail.analytics.noStoreData') }}</span>
            </div>
          </div>
          <div class="stats-card">
            <h3>{{ t('customerDetail.analytics.mostActiveStore') }}</h3>
            <div class="number">{{ performanceAnalysis.mostActiveStoreName || t('customerDetail.analytics.noStore') }}</div>
            <div class="trend">
              {{ performanceAnalysis.mostActiveStoreFollowUpCount ?
                t('customerDetail.analytics.followUpTimes', { count: performanceAnalysis.mostActiveStoreFollowUpCount }) :
                t('customerDetail.analytics.noFollowUp') }}
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ t('customerDetail.close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineExpose } from 'vue';
import { ElMessage } from 'element-plus';
import {
  getCustomerBasicInfo,
  getStoreAssociations,
  getFollowUpRecords,
  getTestDriveRecords,
  getDefeatRecords,
  getChangeHistory,
  getPerformanceAnalysis
} from '@/api/modules/sales/factoryProspect';
import type {
  CustomerBasicInfoResponse,
  StoreAssociationsResponse,
  FollowUpRecordsResponse,
  TestDriveRecordsResponse,
  DefeatRecordsResponse,
  ChangeHistoryResponse,
  PerformanceAnalysisResponse
} from '@/types/sales/factoryProspect';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 数据字典
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.ID_TYPE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.FOLLOW_UP_METHOD
]);

// 国际化
const { t } = useModuleI18n('sales.factoryProspect');

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  customerId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:show']);

const showModal = ref(props.show);
const activeTab = ref('basic');

// 监听 show 属性变化
watch(() => props.show, async (newVal) => {
  showModal.value = newVal;
  if (newVal && props.customerId) {
    activeTab.value = 'basic';
    await loadBasicInfo();
  }
});

// 监听 showModal 变化，通知父组件
watch(showModal, (newVal) => {
  emit('update:show', newVal);
});

// 监听 customerId 变化，加载数据
watch(() => props.customerId, async (newVal) => {
  if (newVal && showModal.value) {
    activeTab.value = 'basic';
    await loadBasicInfo();
  }
}, { immediate: true });

// 监听activeTab变化，加载对应数据
watch(activeTab, async () => {
  if (props.customerId && showModal.value) {
    await handleTabChange();
  }
});

// 基本信息
const basicInfo = ref<{ label: string; value: string }[]>([]);
const customerBasicInfo = ref<CustomerBasicInfoResponse>({
  globalCustomerId: '',
  customerName: '',
  customerPhone: '',
  idType: '',
  idNumber: '',
  email: '',
  registrationTime: '',
  sourceChannel: '',
  currentStatus: ''
});

// 门店关联
const storeAssociations = ref<StoreAssociationsResponse>({
  associatedStoreCount: 0,
  storeAssociationRecords: []
});

// 跟进记录
const followUpRecords = ref<FollowUpRecordsResponse>({
  totalCount: 0,
  earliestTime: '',
  latestTime: '',
  followUpRecords: []
});

// 试驾记录
const testDriveRecords = ref<TestDriveRecordsResponse>({
  totalCount: 0,
  testDriveRecords: []
});

// 战败记录
const defeatRecords = ref<DefeatRecordsResponse>({
  totalCount: 0,
  defeatRecords: []
});

// 变更历史
const changeHistory = ref<ChangeHistoryResponse>({
  totalCount: 0,
  changeHistoryRecords: []
});

// 绩效分析
const performanceAnalysis = ref<PerformanceAnalysisResponse>({
  totalFollowUpCount: 0,
  totalAssociatedStoreCount: 0,
  mostActiveStoreName: '',
  mostActiveStoreFollowUpCount: 0
});

// 加载基本信息
const loadBasicInfo = async () => {
  try {
    const res = await getCustomerBasicInfo({ globalCustomerId: props.customerId }) as any;
    console.log('基本信息数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    customerBasicInfo.value = data;

    basicInfo.value = [
      { label: t('customerDetail.basicInfo.customerId'), value: data.globalCustomerId || '' },
      { label: t('customerDetail.basicInfo.customerName'), value: data.customerName || '' },
            { label: t('customerDetail.basicInfo.phoneNumber'), value: data.customerPhone || '' },
      { label: t('customerDetail.basicInfo.idType'), value: getNameByCode(DICTIONARY_TYPES.ID_TYPE, data.idType)  || '' },
      { label: t('customerDetail.basicInfo.idNumber'), value: data.idNumber || '' },
      { label: t('customerDetail.basicInfo.email'), value: data.email || '' },
      { label: t('customerDetail.basicInfo.registerTime'), value: data.registrationTime || '' },
      { label: t('customerDetail.basicInfo.registerSource'), value: getNameByCode(DICTIONARY_TYPES.CUSTOMER_SOURCE, data.sourceChannel)  || '' },
      { label: t('customerDetail.basicInfo.currentStatus'), value: data.currentStatus || '' }
    ];
  } catch (error) {
    console.error('获取客户基本信息失败', error);
    ElMessage.error(t('customerDetail.messages.fetchDetailFailed'));
  }
};

// 加载门店关联
const loadStoreAssociations = async () => {
  try {
    const res = await getStoreAssociations({ globalCustomerId: props.customerId }) as any;
    console.log('门店关联数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    storeAssociations.value = data;
  } catch (error) {
    console.error('获取门店关联信息失败', error);
    ElMessage.error(t('customerDetail.messages.fetchDetailFailed'));
  }
};

// 加载跟进记录
const loadFollowUpRecords = async () => {
  try {
    const res = await getFollowUpRecords({ storeProspectId: props.customerId }) as any;
    console.log('跟进记录数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    followUpRecords.value = data;
  } catch (error) {
    console.error('获取跟进记录失败', error);
    ElMessage.error(t('customerDetail.messages.fetchDetailFailed'));
  }
};

// 加载试驾记录
const loadTestDriveRecords = async () => {
  try {
    const res = await getTestDriveRecords({ storeProspectId: props.customerId }) as any;
    console.log('试驾记录数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    testDriveRecords.value = data;
  } catch (error) {
    console.error('获取试驾记录失败', error);
    ElMessage.error(t('customerDetail.messages.fetchDetailFailed'));
  }
};

// 加载战败记录
const loadDefeatRecords = async () => {
  try {
    const res = await getDefeatRecords({ storeProspectId: props.customerId }) as any;
    console.log('战败记录数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    defeatRecords.value = data;
  } catch (error) {
    console.error('获取战败记录失败', error);
    ElMessage.error(t('customerDetail.messages.fetchDetailFailed'));
  }
};

// 加载变更历史
const loadChangeHistory = async () => {
  try {
    const res = await getChangeHistory({ storeProspectId: props.customerId }) as any;
    console.log('变更历史数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    changeHistory.value = data;
  } catch (error) {
    console.error('获取变更历史失败', error);
    ElMessage.error(t('customerDetail.messages.fetchDetailFailed'));
  }
};

// 加载绩效分析
const loadPerformanceAnalysis = async () => {
  try {
    const res = await getPerformanceAnalysis({ globalCustomerId: props.customerId }) as any;
    console.log('绩效分析数据:', res);

    // 处理不同的返回数据结构
    const data = res.data || res.result || res;
    performanceAnalysis.value = data;
  } catch (error) {
    console.error('获取绩效分析失败', error);
    ElMessage.error(t('customerDetail.messages.fetchDetailFailed'));
  }
};

// 根据选项卡变化加载对应数据
const handleTabChange = async () => {
  switch (activeTab.value) {
    case 'basic':
      await loadBasicInfo();
      break;
    case 'stores':
      await loadStoreAssociations();
      break;
    case 'followup':
      await loadFollowUpRecords();
      break;
    case 'testdrive':
      await loadTestDriveRecords();
      break;
    case 'failed':
      await loadDefeatRecords();
      break;
    case 'history':
      await loadChangeHistory();
      break;
    case 'analytics':
      await loadPerformanceAnalysis();
      break;
  }
};



// 关闭模态框
const handleClose = () => {
  showModal.value = false;
};



// 获取战败状态标签类型
const getDefeatStatusTagType = (status: string): 'success' | 'danger' | 'info' => {
  if (status === '已批准' || status === 'Approved') return 'success';
  if (status === '已拒绝' || status === 'Rejected') return 'danger';
  return 'info';
};

// 获取战败状态文本
const getDefeatStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '已批准': t('customerDetail.defeatTable.approved'),
    '已拒绝': t('customerDetail.defeatTable.rejected'),
    '待审核': t('customerDetail.defeatTable.pending'),
    'Approved': t('customerDetail.defeatTable.approved'),
    'Rejected': t('customerDetail.defeatTable.rejected'),
    'Pending': t('customerDetail.defeatTable.pending')
  };
  return statusMap[status] || status;
};

// 暴露方法给父组件
defineExpose({
  loadBasicInfo,
  loadStoreAssociations
});
</script>

<style scoped>
.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.info-item {
  margin-bottom: 12px;
}

.info-label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.info-value {
  color: #303133;
}

.store-association {
  margin-top: 16px;
}

.association-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 12px;
}

.store-name {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  margin-bottom: 4px;
}

.store-details {
  font-size: 13px;
  color: #606266;
}

.followup-status {
  text-align: right;
}

.last-followup {
  margin-top: 6px;
  font-size: 12px;
  color: #909399;
}

.stats-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 16px;
}

.stats-card {
  flex-basis: calc(25% - 20px);
  min-width: 250px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-card h3 {
  margin-top: 0;
  color: #606266;
  font-size: 14px;
  font-weight: normal;
}

.stats-card .number {
  font-size: 28px;
  color: #303133;
  margin: 10px 0;
}

.stats-card .trend {
  font-size: 13px;
  color: #909399;
}

.mt-20 {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .stats-card {
    flex-basis: 100%;
  }
}
</style>
