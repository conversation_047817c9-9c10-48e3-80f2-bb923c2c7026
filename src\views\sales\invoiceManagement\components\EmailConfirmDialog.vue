<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('emailConfirm')"
    width="420px"
  >
    <div v-if="invoiceData" class="email-confirm">
      <el-row :gutter="16">
        <el-col :span="12">
          <div class="confirm-item">
            <label>{{ t('invoiceNumber') }}:</label>
            <span>{{ invoiceData.invoiceNumber }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="confirm-item">
            <label>{{ t('orderNumber') }}:</label>
            <span>{{ invoiceData.orderNumber }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="confirm-item">
            <label>{{ t('customerName') }}:</label>
            <span>{{ invoiceData.customerName }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="confirm-item">
            <label>{{ t('customerEmail') }}:</label>
            <span>{{ invoiceData.customerEmail }}</span>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="confirm-item">
            <label>{{ t('invoiceAmount') }}:</label>
            <span class="amount">RM {{ invoiceData.invoiceAmount }}</span>
          </div>
        </el-col>
      </el-row>
      <div class="confirm-message">
        {{ t('confirmSendEmail') }}
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InvoiceItem } from '@/types/sales/invoiceManagement';

// Props
interface Props {
  visible: boolean;
  invoiceData?: InvoiceItem | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  invoiceData: null,
  loading: false
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [];
}>();

// 使用国际化
const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 确认发送
const handleConfirm = () => {
  emit('confirm');
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<style scoped lang="scss">
.email-confirm {
  .confirm-item {
    margin-bottom: 12px;

    label {
      display: inline-block;
      width: 80px;
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
    }

    span {
      color: #303133;

      &.amount {
        font-weight: 600;
        color: #409eff;
      }
    }
  }

  .confirm-message {
    margin-top: 20px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #606266;
    text-align: center;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
