<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('exportTitle')"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div class="export-form">
      <el-form :model="exportForm" :rules="exportRules" ref="formRef" label-width="120px">
        <el-form-item :label="t('exportFormat')" prop="format">
          <el-radio-group v-model="exportForm.format">
            <el-radio value="excel">Excel (.xlsx)</el-radio>
            <el-radio value="csv">CSV (.csv)</el-radio>
            <el-radio value="pdf">PDF (.pdf)</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item :label="t('exportScope')" prop="scope">
          <el-radio-group v-model="exportForm.scope">
            <el-radio value="current">{{ t('currentPage') }}</el-radio>
            <el-radio value="filtered">{{ t('filteredData') }}</el-radio>
            <el-radio value="all">{{ t('allData') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item :label="t('exportFields')" prop="fields">
          <el-checkbox-group v-model="exportForm.fields">
            <div class="checkbox-grid">
              <el-checkbox value="invoiceNumber">{{ t('invoiceNumber') }}</el-checkbox>
              <el-checkbox value="invoiceDate">{{ t('invoiceDate') }}</el-checkbox>
              <el-checkbox value="orderNumber">{{ t('orderNumber') }}</el-checkbox>
              <el-checkbox value="customerName">{{ t('customerName') }}</el-checkbox>
              <el-checkbox value="customerPhone">{{ t('customerPhone') }}</el-checkbox>
              <el-checkbox value="customerEmail">{{ t('customerEmail') }}</el-checkbox>
              <el-checkbox value="customerAddress">{{ t('customerAddress') }}</el-checkbox>
              <el-checkbox value="vin">{{ t('vin') }}</el-checkbox>
              <el-checkbox value="model">{{ t('model') }}</el-checkbox>
              <el-checkbox value="variant">{{ t('variant') }}</el-checkbox>
              <el-checkbox value="color">{{ t('color') }}</el-checkbox>
              <el-checkbox value="salesStore">{{ t('salesStore') }}</el-checkbox>
              <el-checkbox value="salesConsultant">{{ t('salesConsultant') }}</el-checkbox>
              <el-checkbox value="paymentMethod">{{ t('paymentMethod') }}</el-checkbox>
              <el-checkbox value="financeCompany">{{ t('financeCompany') }}</el-checkbox>
              <el-checkbox value="loanAmount">{{ t('loanAmount') }}</el-checkbox>
              <el-checkbox value="invoiceAmount">{{ t('invoiceAmount') }}</el-checkbox>
              <el-checkbox value="createdTime">{{ t('createdTime') }}</el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item>
          <div class="field-actions">
            <el-button size="small" @click="selectAllFields">{{ t('selectAll') }}</el-button>
            <el-button size="small" @click="clearAllFields">{{ t('clearAll') }}</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ t('startExport') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import type { ExportConfig } from '@/types/sales/invoiceManagement';

// Props
interface Props {
  visible: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [config: ExportConfig];
}>();

// 使用国际化
const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

// 表单引用
const formRef = ref<FormInstance>();

// 默认导出字段
const defaultFields = [
  'invoiceNumber',
  'invoiceDate',
  'orderNumber',
  'customerName',
  'customerPhone',
  'customerEmail',
  'vin',
  'model',
  'salesStore',
  'salesConsultant',
  'paymentMethod',
  'invoiceAmount',
  'createdTime'
];

// 导出表单数据
const exportForm = reactive({
  format: 'excel' as 'excel' | 'csv' | 'pdf',
  scope: 'current' as 'current' | 'filtered' | 'all',
  fields: [...defaultFields]
});

// 表单验证规则
const exportRules: FormRules = {
  format: [
    { required: true, message: t('formatRequired'), trigger: 'change' }
  ],
  scope: [
    { required: true, message: t('scopeRequired'), trigger: 'change' }
  ],
  fields: [
    { 
      type: 'array', 
      min: 1, 
      message: t('fieldsRequired'), 
      trigger: 'change' 
    }
  ]
};

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm();
  }
});

// 全选字段
const selectAllFields = () => {
  exportForm.fields = [
    'invoiceNumber',
    'invoiceDate',
    'orderNumber',
    'customerName',
    'customerPhone',
    'customerEmail',
    'customerAddress',
    'vin',
    'model',
    'variant',
    'color',
    'salesStore',
    'salesConsultant',
    'paymentMethod',
    'financeCompany',
    'loanAmount',
    'invoiceAmount',
    'createdTime'
  ];
};

// 清空字段
const clearAllFields = () => {
  exportForm.fields = [];
};

// 确认导出
const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    
    const config: ExportConfig = {
      format: exportForm.format,
      scope: exportForm.scope,
      fields: exportForm.fields
    };
    
    emit('confirm', config);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};

// 重置表单
const resetForm = () => {
  Object.assign(exportForm, {
    format: 'excel',
    scope: 'current',
    fields: [...defaultFields]
  });
  
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};
</script>

<style scoped lang="scss">
.export-form {
  padding: 20px 0;
}

.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  
  .el-checkbox {
    margin-right: 0;
  }
}

.field-actions {
  display: flex;
  gap: 12px;
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .checkbox-grid {
    grid-template-columns: 1fr;
  }
}
</style>
