<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('logTitle')"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading" class="log-container">
      <el-table :data="logData" border style="width: 100%">
        <el-table-column :label="tc('index')" type="index" width="70" />
        <el-table-column :label="t('operationType')" prop="operationType" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTagType(row.operationType)">
              {{ formatOperationType(row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('operator')" prop="operator" min-width="100" />
        <el-table-column :label="t('operationTime')" prop="operationTime" min-width="150" />
        <el-table-column :label="t('operationDescription')" prop="operationDescription" min-width="200" show-overflow-tooltip />
        <el-table-column :label="t('operationResult')" prop="operationResult" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getResultTagType(row.operationResult)">
              {{ formatOperationResult(row.operationResult) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('errorMessage')" prop="errorMessage" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.errorMessage" class="error-message">{{ row.errorMessage }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-if="logData.length === 0 && !loading" class="empty-data">
        <el-empty :description="t('noLogData')" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('close') }}</el-button>
        <el-button type="primary" @click="handleRefresh">{{ tc('refresh') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ElMessage } from 'element-plus';
import type { InvoiceOperationLog } from '@/types/sales/invoiceManagement';
import { getInvoiceOperationLogs } from '@/api/modules/sales/invoiceManagement';

// Props
interface Props {
  visible: boolean;
  invoiceId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  invoiceId: ''
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 使用国际化
const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

// 数据定义
const loading = ref(false);
const logData = ref<InvoiceOperationLog[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.invoiceId) {
    loadOperationLogs();
  }
});

// 监听发票ID变化
watch(() => props.invoiceId, (newId) => {
  if (newId && props.visible) {
    loadOperationLogs();
  }
});

// 加载操作日志
const loadOperationLogs = async () => {
  if (!props.invoiceId) return;

  try {
    loading.value = true;
    const response = await getInvoiceOperationLogs(props.invoiceId);
    
    if (response.code === '200' || response.code === 200) {
      logData.value = response.result || [];
    } else {
      throw new Error(response.message || '获取操作日志失败');
    }
  } catch (error) {
    console.error('获取操作日志失败:', error);
    ElMessage.error(tc('loadFailed'));
    logData.value = [];
  } finally {
    loading.value = false;
  }
};

// 格式化操作类型
const formatOperationType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'VIEW_DETAIL': t('viewDetail'),
    'PRINT': t('print'),
    'EMAIL_SEND': t('emailSend'),
    'EXPORT_DATA': t('exportData'),
    'CREATE': t('create'),
    'UPDATE': t('update'),
    'DELETE': t('delete')
  };
  return typeMap[type] || type;
};

// 获取操作类型标签类型
const getOperationTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'VIEW_DETAIL': 'info',
    'PRINT': 'success',
    'EMAIL_SEND': 'warning',
    'EXPORT_DATA': 'primary',
    'CREATE': 'success',
    'UPDATE': 'warning',
    'DELETE': 'danger'
  };
  return typeMap[type] || 'info';
};

// 格式化操作结果
const formatOperationResult = (result: string): string => {
  const resultMap: Record<string, string> = {
    'SUCCESS': t('success'),
    'FAILED': t('failed'),
    'PENDING': t('pending')
  };
  return resultMap[result] || result;
};

// 获取结果标签类型
const getResultTagType = (result: string): string => {
  const resultMap: Record<string, string> = {
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'PENDING': 'warning'
  };
  return resultMap[result] || 'info';
};

// 刷新数据
const handleRefresh = () => {
  loadOperationLogs();
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  logData.value = [];
};
</script>

<style scoped lang="scss">
.log-container {
  min-height: 300px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.error-message {
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }
}
</style>
