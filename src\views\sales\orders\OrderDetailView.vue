<template>
  <div class="page-container" v-loading="loading">
    <h1 class="page-title">{{ t('detail.title') }}</h1>

    <!-- 订单基本信息 -->
    <el-card class="mb-20">
      <el-row :gutter="20" class="order-summary-header">
        <el-col :span="12">
          {{ t('detail.orderNumber') }}: <strong id="detailOrderNo">{{ orderDetails.orderNo || '-' }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('detail.createTime') }}: <strong id="detailCreateTime">{{ orderDetails.createTime || '-' }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('detail.orderStatus') }}: <strong>{{ formatOrderStatus(orderDetails.orderStatus) }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('detail.approvalStatus') }}: <strong>{{ formatApprovalStatus(orderDetails.approvalStatus) }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('detail.paymentStatus') }}: <strong>{{ formatPaymentStatus(orderDetails.paymentStatus || '') }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('detail.insuranceStatus') }}: <strong>{{ formatInsuranceStatus(orderDetails.insuranceStatus || '') }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('detail.jpjRegistrationStatus') }}: <strong>{{ formatJpjRegistrationStatus(orderDetails.jpjRegistrationStatus || '') }}</strong>
        </el-col>
      </el-row>
    </el-card>

    <!-- 客户信息 - Personal Details -->
    <el-card class="mb-20 section">
      <h2>{{ t('detail.customerInfo') }}</h2>
      <el-row :gutter="20" class="form-row">
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.ordererName') }}: <span class="form-control-static">{{ orderDetails.customerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.ordererPhone') }}: <span class="form-control-static">{{ orderDetails.customerPhone || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerName') }}: <span class="form-control-static">{{ orderDetails.customerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerPhone') }}: <span class="form-control-static">{{ orderDetails.customerPhone || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerIdType') }}: <span class="form-control-static">{{ orderDetails.idType || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerIdNumber') }}: <span class="form-control-static">{{ orderDetails.idNumber || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerEmail') }}: <span class="form-control-static">{{ orderDetails.email || '-' }}</span></div>
        </el-col>
        <el-col :span="24">
          <div class="form-item-static">{{ t('detail.buyerAddress') }}: <span class="form-control-static">{{ orderDetails.address || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerState') }}: <span class="form-control-static">{{ orderDetails.state || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerCity') }}: <span class="form-control-static">{{ orderDetails.city || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerPostcode') }}: <span class="form-control-static">{{ orderDetails.zipCode || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.buyerType') }}: <span class="form-control-static">{{ formatCustomerType(orderDetails.customerType) || '-' }}</span></div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 购车门店信息 - Preferred Outlet & Sales Advisor -->
    <el-card class="mb-20 section">
      <h2>{{ t('detail.storeInfo') }}</h2>
      <el-row :gutter="20" class="form-row">
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.storeRegion') }}: <span class="form-control-static">{{ orderDetails.storeRegion || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.storeCity') }}: <span class="form-control-static">{{ orderDetails.dealerCity || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.storeName') }}: <span class="form-control-static">{{ orderDetails.dealerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('detail.salesConsultantName') }}: <span class="form-control-static">{{ orderDetails.salesConsultantName || '-' }}</span></div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 购车信息 - Purchase Details -->
    <el-card class="mb-20 section">
      <h2>{{ t('detail.purchaseInfo') }}</h2>
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane :label="t('detail.vehicleInfoTab')" name="vehicle"></el-tab-pane>
        <el-tab-pane :label="t('detail.invoicingInfoTab')" name="invoice"></el-tab-pane>
        <el-tab-pane :label="t('detail.rightsInfoTab')" name="rights"></el-tab-pane>
        <el-tab-pane :label="t('detail.paymentInfoTab')" name="payment"></el-tab-pane>
        <el-tab-pane :label="t('detail.insuranceInfoTab')" name="insurance"></el-tab-pane>
        <el-tab-pane :label="t('detail.otrFeesTab')" name="otr"></el-tab-pane>
      </el-tabs>

      <div v-if="activeTab === 'vehicle'" class="tab-content-section">
        <h3>{{ t('detail.vehicleInfoTab') }}</h3>
        <el-row :gutter="20" class="form-row">
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.model') }}: <span class="form-control-static">{{ orderDetails.model || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.variant') }}: <span class="form-control-static">{{ orderDetails.variant || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.color') }}: <span class="form-control-static">{{ orderDetails.color || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.salesSubtotal') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.vehiclePrice) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.numberPlatesFee') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.numberPlatesFee) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.vin') }}: <span class="form-control-static">{{ orderDetails.vin || '-' }}</span></div>
          </el-col>
        </el-row>

        <h4>{{ t('detail.accessoriesInformation') }}</h4>
        <el-table :data="orderDetails.accessories" style="width: 100%" border class="mb-10">
          <el-table-column prop="category" :label="t('detail.accessoryCategory')" width="120"></el-table-column>
          <el-table-column prop="name" :label="t('detail.accessoryName')"></el-table-column>
          <el-table-column prop="price" :label="t('detail.unitPrice')" width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.price) }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" :label="t('detail.quantity')" width="100"></el-table-column>
          <el-table-column prop="total" :label="t('detail.totalPrice')" width="120">
            <template #default="scope">
              {{ formatCurrency(scope.row.total) }}
            </template>
          </el-table-column>
        </el-table>
        <div class="total-amount">{{ t('detail.accessoriesTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalAccessoryAmount) }}</span></div>
      </div>

      <div v-if="activeTab === 'invoice'" class="tab-content-section">
        <h3>{{ t('detail.invoicingInfoTab') }}</h3>
        <el-row :gutter="20" class="form-row">
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.invoiceTypeLabel') }}: <span class="form-control-static">{{ orderDetails.invoiceType || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.invoiceNameLabel') }}: <span class="form-control-static">{{ orderDetails.invoiceName || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.invoicePhoneLabel') }}: <span class="form-control-static">{{ orderDetails.invoicePhone || '-' }}</span></div>
          </el-col>
          <el-col :span="24">
            <div class="form-item-static">{{ t('detail.invoiceAddressLabel') }}: <span class="form-control-static">{{ orderDetails.invoiceAddress || '-' }}</span></div>
          </el-col>
        </el-row>
      </div>

      <div v-if="activeTab === 'rights'" class="tab-content-section">
        <h3>{{ t('detail.rightsInfo') }}</h3>
        <el-table :data="orderDetails.rights" style="width: 100%" border class="mb-10">
          <el-table-column prop="code" :label="t('detail.rightCode')" width="120"></el-table-column>
          <el-table-column prop="name" :label="t('detail.rightName')"></el-table-column>
          <el-table-column prop="mode" :label="t('detail.rightMode')" width="100"></el-table-column>
          <el-table-column prop="discountPrice" :label="t('detail.discountAmount')" width="120">
            <template #default="scope">
              {{ formatCurrency(scope.row.discountPrice) }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" :label="t('detail.effectiveDate')" width="120"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('detail.expiryDate')" width="120"></el-table-column>
        </el-table>
        <div class="total-amount">{{ t('detail.rightsDiscountTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalRightsDiscountAmount) }}</span></div>
      </div>

      <div v-if="activeTab === 'payment'" class="tab-content-section">
        <h3>{{ t('detail.paymentInfoTab') }}</h3>
        <el-row :gutter="20" class="form-row">
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.paymentMethod') }}: <span class="form-control-static">{{ formatPaymentType(orderDetails.paymentMethod) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.loanStatusLabel') }}: <span class="form-control-static">{{ formatLoanStatus(orderDetails.loanApprovalStatus || '') || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.depositAmountLabel') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.depositAmount) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.loanAmountLabel') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.loanAmount) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('detail.finalPaymentLabel') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.balanceAmount) || '-' }}</span></div>
          </el-col>
        </el-row>
      </div>

      <div v-if="activeTab === 'insurance'" class="tab-content-section">
        <h3>{{ t('detail.insuranceInfo') }}</h3>
        <el-table :data="orderDetails.policies" style="width: 100%" border class="mb-10">
          <el-table-column prop="policyNumber" :label="t('detail.policyNumber')" width="150"></el-table-column>
          <el-table-column prop="insuranceType" :label="t('detail.insuranceType')" width="100"></el-table-column>
          <el-table-column prop="insuranceCompany" :label="t('detail.insuranceCompany')"></el-table-column>
          <el-table-column prop="effectiveDate" :label="t('detail.effectiveDate')" width="120"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('detail.expiryDate')" width="120"></el-table-column>
          <el-table-column prop="price" :label="t('detail.insurancePrice')" width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.price) }}
            </template>
          </el-table-column>
        </el-table>
        <el-form-item :label="t('detail.remarksLabel')">
          <el-input type="textarea" v-model="orderDetails.remarks" readonly></el-input>
        </el-form-item>
        <div class="total-amount">{{ t('detail.insuranceTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalInsuranceAmount) }}</span></div>
      </div>

      <div v-if="activeTab === 'otr'" class="tab-content-section">
        <h3>{{ t('detail.otrFeesInfo') }}</h3>
        <el-table :data="orderDetails.otrFees" style="width: 100%" border class="mb-10">
          <el-table-column prop="invoiceNumber" :label="t('detail.ticketNumber')" width="150"></el-table-column>
          <el-table-column prop="item" :label="t('detail.feeItem')"></el-table-column>
          <el-table-column prop="price" :label="t('detail.feePrice')" width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.price) }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" :label="t('detail.effectiveDate')" width="120"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('detail.expiryDate')" width="120"></el-table-column>
        </el-table>
        <div class="total-amount">{{ t('detail.otrFeesTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalOtrAmount) }}</span></div>
      </div>
    </el-card>

    <!-- 订单变更记录 - Order Change Records -->
    <el-card class="mb-20 section">
      <h2>{{ t('detail.changeRecords') }}</h2>
      <el-table :data="orderDetails.changeRecords" style="width: 100%" stripe>
        <el-table-column prop="createdAt" :label="t('detail.operationTime')" width="180"></el-table-column>
        <el-table-column prop="createdBy" :label="t('detail.operator')" width="100"></el-table-column>
        <el-table-column prop="originalContent" :label="t('detail.originalContent')"></el-table-column>
        <el-table-column prop="changedContent" :label="t('detail.changedContent')"></el-table-column>
      </el-table>
    </el-card>

    <!-- 底部价格栏 -->
    <div class="footer-bar">
      <div class="price-summary">
        <span>{{ t('detail.totalAmount') }}: <span class="total-price">{{ formatCurrency(orderDetails.totalInvoicePrice) }}</span></span>
        <span>{{ t('detail.remainingReceivable') }}: <span class="remaining-amount">{{ formatCurrency(orderDetails.remainingAmount) }}</span></span>
      </div>
      <div class="buttons">
        <el-button type="primary" @click="goToList">{{ t('detail.returnToList') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  ElButton,
  ElCard,
  ElCol,
  ElFormItem,
  ElInput,
  ElMessage,
  ElRow,
  ElTable,
  ElTableColumn,
  ElTabPane,
  ElTabs,
} from 'element-plus';
import type { SalesOrderDetail, BuyerType, PaymentMethod, LoanApprovalStatus, SalesOrderStatus, SalesOrderApprovalStatus, SalesOrderPaymentStatus, InsuranceStatus, JPJRegistrationStatus } from '@/types/sales/orders';
import { getSalesOrderDetail } from '@/api/modules/sales/orders';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { t, tc } = useModuleI18n('sales.orders');
const route = useRoute();
const router = useRouter();

// 使用字典数据
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.BUYER_TYPE,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS
]);

const activeTab = ref('vehicle'); // 当前激活的Tab
const loading = ref(false);

// 使用真实的SalesOrderDetail接口
const orderDetails = ref<SalesOrderDetail>({
  // 订单基本信息
  id: '',
  orderNo: '',
  createTime: '',
  updateTime: '',

  // 客户信息
  customerName: '',
  customerPhone: '',
  buyerName: '',
  buyerPhone: '',
  buyerType: '',
  idType: '',
  idNumber: '',
  email: '',
  address: '',
  state: '',
  city: '',
  zipCode: '',
  customerType: '',

  // 门店信息
  storeRegion: '',
  dealerCity: '',
  dealerName: '',
  salesConsultantName: '',

  // 车辆信息
  model: '',
  variant: '',
  color: '',
  vin: '',
  vehiclePrice: 0,
  numberPlatesFee: 0,

  // 选配件信息
  accessories: [],
  totalAccessoryAmount: 0,

  // 开票信息
  invoiceType: '',
  invoiceName: '',
  invoicePhone: '',
  invoiceAddress: '',

  // 权益信息
  rights: [],
  totalRightsDiscountAmount: 0,

  // 支付信息
  paymentMethod: '',
  totalAmount: 0,
  depositAmount: 0,
  balanceAmount: 0,

  // 保险信息
  policies: [],
  totalInsuranceAmount: 0,

  // OTR费用信息
  otrFees: [],
  totalOtrAmount: 0,

  // 订单变更记录
  changeRecords: [],

  // 状态信息
  orderStatus: '',
  approvalStatus: '',
  paymentStatus: '',
  insuranceStatus: '',
  jpjRegistrationStatus: '',

  // 计算字段
  totalInvoicePrice: 0,
  remainingAmount: 0,

  // 其他信息
  selectedRights: [],
  remarks: '',
  attachments: []
});

// 格式化方法
const formatCustomerType = (type: string) => {
  return getNameByCode(DICTIONARY_TYPES.BUYER_TYPE, type) || type;
};

const formatPaymentType = (type: string) => {
  return getNameByCode(DICTIONARY_TYPES.PAYMENT_METHOD, type) || type;
};



const formatOrderStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;
};

const formatApprovalStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.APPROVAL_STATUS, status) || status;
};

const formatPaymentStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;
};

const formatInsuranceStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.INSURANCE_STATUS, status) || status;
};

const formatJpjRegistrationStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS, status) || status;
};

const formatLoanStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.APPROVAL_STATUS, status) || status;
};

const formatCurrency = (amount?: number) => {
  if (amount === undefined || amount === null) return '-';
  return `RM ${amount.toLocaleString('en-MY', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

const goToList = () => {
  router.push('/sales/orders');
};

onMounted(() => {
  const orderNo = route.params.orderNo as string;
  if (orderNo) {
    // 使用API接口获取订单详情
    fetchOrderDetail(orderNo);
  } else {
    ElMessage.error(t('detail.missingOrderNumber'));
    router.push('/sales/orders');
  }
});

// 获取订单详情的方法
const fetchOrderDetail = async (orderNo: string) => {
  loading.value = true;
  try {
    // 调用真实的API接口
    const data = await getSalesOrderDetail(orderNo);

    // 直接使用API返回的数据，数据结构已经匹配SalesOrderDetail接口
    orderDetails.value = data.result;
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error(t('detail.fetchDetailError'));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-title {
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
  color: #303133;
}

.mb-20 {
  margin-bottom: 20px;
}

.order-summary-header {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.order-summary-header strong {
  font-weight: bold;
  color: #333;
  margin-right: 20px;
}

.section h2 {
  font-size: 22px;
  color: #303133;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.form-row .form-item-static {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.form-row .form-control-static {
  font-weight: bold;
  color: #333;
}

.custom-tabs .el-tabs__item {
  font-size: 16px;
}

.tab-content-section {
  padding: 20px 0;
}

.tab-content-section h3 {
  font-size: 18px;
  color: #606266;
  margin-top: 20px;
  margin-bottom: 15px;
}

.tab-content-section h4 {
  font-size: 16px;
  color: #606266;
  margin-top: 15px;
  margin-bottom: 10px;
}

.total-amount {
  text-align: right;
  margin-top: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.footer-bar {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 15px 20px;
  border-top: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 1000;
}

.price-summary span {
  font-size: 18px;
  margin-right: 30px;
  color: #303133;
}

.total-price {
  color: #409EFF;
  font-weight: bold;
}

.remaining-amount {
  color: #F56C6C;
  font-weight: bold;
}

.buttons .el-button {
  margin-left: 10px;
}
</style>
