<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('orders.list.title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" label-position="top" @submit.prevent="handleSearch">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.buyerName')">
              <el-input v-model="searchParams.buyerName" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.buyerPhone')">
              <el-input v-model="searchParams.customerPhone" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.buyerType')">
              <el-select v-model="searchParams.buyerType" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in buyerTypeOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.model')">
              <el-select v-model="searchParams.model" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="model in vehicleModelOptions"
                  :key="model.id"
                  :value="model.code"
                  :label="model.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.orderNumber')">
              <el-input v-model="searchParams.orderNumber" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.orderStatus')">
              <el-select v-model="searchParams.orderStatus" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in orderStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.approvalStatus')">
              <el-select v-model="searchParams.approvalStatus" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in approvalStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.paymentStatus')">
              <el-select v-model="searchParams.paymentStatus" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in paymentStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.insuranceStatus')">
              <el-select v-model="searchParams.insuranceStatus" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in insuranceStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.loanApprovalStatus')">
              <el-select v-model="searchParams.loanApprovalStatus" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in approvalStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.jpjRegistrationStatus')">
              <el-select v-model="searchParams.jpjRegistrationStatus" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in jpjRegistrationStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.createTime')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">

          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.ordererName')">
              <el-input v-model="extraSearchParams.ordererName" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orders.list.search.ordererPhone')">
              <el-input v-model="extraSearchParams.ordererPhone" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12" class="buttons-col">
            <el-button @click="resetSearch">{{ t('orders.list.search.reset') }}</el-button>
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ t('orders.list.search.search') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 订单列表区域 -->
    <el-card>
      <el-table :data="tableData" v-loading="loading" style="width: 100%" stripe>
        <el-table-column type="index" :label="tc('index')" width="60" />
        <el-table-column prop="orderNo" :label="t('orders.list.columns.orderNumber')" width="180" />
        <el-table-column prop="createTime" :label="t('orders.list.columns.createTime')" width="180" sortable />
        <el-table-column prop="customerName" :label="t('orders.list.columns.customerName')" width="120" />
        <el-table-column prop="customerPhone" :label="t('orders.list.columns.customerPhone')" width="150" />
        <el-table-column prop="customerName" :label="t('orders.list.columns.buyerName')" width="120" />
        <el-table-column prop="customerPhone" :label="t('orders.list.columns.buyerPhone')" width="150" />
        <el-table-column prop="customerType" :label="t('orders.list.columns.buyerType')" width="100">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.BUYER_TYPE, row.customerType) }}
          </template>
        </el-table-column>
        <el-table-column prop="model" :label="t('orders.list.columns.model')" width="100" />
        <el-table-column prop="variant" :label="t('orders.list.columns.variant')" width="100" />
        <el-table-column prop="color" :label="t('orders.list.columns.color')" width="100" />
        <el-table-column prop="vin" :label="t('orders.list.columns.vin')" width="150" />
        <el-table-column prop="paymentMethod" :label="t('orders.list.columns.paymentMethod')" width="100">
          <template #default="{ row }">
            <el-tag :type="row.paymentMethod === 'full_payment' ? 'success' : 'info'">
              {{ getNameByCode(DICTIONARY_TYPES.PAYMENT_METHOD, row.paymentMethod) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus" :label="t('orders.list.columns.orderStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.orderStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="approvalStatus" :label="t('orders.list.columns.approvalStatus')" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.approvalStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.APPROVAL_STATUS, row.approvalStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="t('orders.list.columns.paymentStatus')" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.paymentStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="insuranceStatus" :label="t('orders.list.columns.insuranceStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.insuranceStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.INSURANCE_STATUS, row.insuranceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="jpjRegistrationStatus" :label="t('orders.list.columns.jpjRegistrationStatus')" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.jpjRegistrationStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS, row.jpjRegistrationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" :label="t('orders.list.columns.totalAmount')" width="120">
          <template #default="{ row }">
            {{ row.totalAmount?.toFixed(2) || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column :label="tc('actions')" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="goToDetail(row.orderNo)">{{ t('orders.list.actions.view') }}</el-button>
            <el-button link type="primary" @click="goToEdit(row.orderNo)">{{ t('orders.list.actions.edit') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { SalesOrderSearchParams, SalesOrderListItem, SalesOrderListResponse } from '@/types/sales/orders';
import { getSalesOrdersList } from '@/api/modules/sales/orders';

const router = useRouter();
const { t, tc } = useModuleI18n('sales');

// 使用字典数据
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.BUYER_TYPE,
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.VEHICLE_MODEL
]);

// 车型选项（Mock数据）
const vehicleModelOptions = ref([
  { id: '1', code: 'AXIA', name: 'Axia' },
  { id: '2', code: 'BEZZA', name: 'Bezza' },
  { id: '3', code: 'MYVI', name: 'Myvi' },
  { id: '4', code: 'ALZA', name: 'Alza' },
  { id: '5', code: 'ARUZ', name: 'Aruz' }
]);

// 响应式数据
const loading = ref(false);
const tableData = ref<SalesOrderListItem[]>([]);
const total = ref(0);
const dateRange = ref<string[]>([]);

// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
});

// 搜索参数
const searchParams = reactive<SalesOrderSearchParams>({
  orderNumber: '',
  buyerName: '',
  customerPhone: '',
  buyerType: '',
  model: '',
  orderStatus: '',
  approvalStatus: '',
  paymentStatus: '',
  insuranceStatus: '',
  loanApprovalStatus: '',
  jpjRegistrationStatus: '',
  createTimeStart: '',
  createTimeEnd: ''
});

// 额外的搜索参数（不在API接口中）
const extraSearchParams = reactive({
  ordererName: '',
  ordererPhone: ''
});

// 获取字典选项的计算属性
const buyerTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.BUYER_TYPE));
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS));
const approvalStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_STATUS));
const paymentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PAYMENT_STATUS));
const insuranceStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.INSURANCE_STATUS));
const jpjRegistrationStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS));

// 获取订单列表数据
const fetchOrderList = async () => {
  loading.value = true;
  try {
    // 设置日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      searchParams.createTimeStart = dateRange.value[0];
      searchParams.createTimeEnd = dateRange.value[1];
    } else {
      searchParams.createTimeStart = '';
      searchParams.createTimeEnd = '';
    }

    const response: SalesOrderListResponse = await getSalesOrdersList({
      ...searchParams,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    });

    tableData.value = response.result.records;
    total.value = response.result.total;
  } catch (error) {
    console.error('获取订单列表失败:', error);
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理查询按钮点击
const handleSearch = () => {
  pagination.pageNum = 1; // 搜索时重置回第一页
  fetchOrderList();
};

// 处理重置按钮点击
const resetSearch = () => {
  // 重置搜索参数
  Object.assign(searchParams, {
    orderNumber: '',
    buyerName: '',
    customerPhone: '',
    buyerType: '',
    model: '',
    orderStatus: '',
    approvalStatus: '',
    paymentStatus: '',
    insuranceStatus: '',
    loanApprovalStatus: '',
    jpjRegistrationStatus: '',
    createTimeStart: '',
    createTimeEnd: ''
  });
  // 重置额外搜索参数
  Object.assign(extraSearchParams, {
    ordererName: '',
    ordererPhone: ''
  });
  dateRange.value = [];
  pagination.pageNum = 1;
  fetchOrderList();
};

// 处理分页页码改变
const handlePageChange = (page: number) => {
  pagination.pageNum = page;
  fetchOrderList();
};

// 处理分页每页数量改变
const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1; // 改变每页数量时重置回第一页
  fetchOrderList();
};

// 跳转到订单详情页
const goToDetail = (orderNo: string) => {
  router.push(`/sales/orders/${orderNo}/detail`);
};

// 跳转到订单编辑页
const goToEdit = (orderNo: string) => {
  router.push(`/sales/orders/${orderNo}/edit`);
};

// 状态样式方法
const getStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'submitted': 'info',
    'confirmed': 'success',
    'pending_delivery': 'warning',
    'completed': 'success',
    'canceled': 'danger',
    'pending_approval': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'fully_paid': 'success',
    'pending_deposit': 'warning',
    'refund_completed': 'danger',
    'not_insured': 'danger',
    'pending': 'warning',
    'insured': 'success',
    'pending_registration': 'warning',
    'registering': 'warning',
    'registered': 'success',
    'registration_failed': 'danger'
  };
  return typeMap[status] || 'info';
};

// 监听日期范围变化
watch(dateRange, () => {
  if (dateRange.value && dateRange.value.length === 2) {
    searchParams.createTimeStart = dateRange.value[0];
    searchParams.createTimeEnd = dateRange.value[1];
  } else {
    searchParams.createTimeStart = '';
    searchParams.createTimeEnd = '';
  }
});

// 组件挂载时获取数据
onMounted(() => {
  fetchOrderList();
});
</script>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-title {
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.buttons-col {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  align-items: flex-end;

  .el-button {
    margin-left: 0;
  }
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
