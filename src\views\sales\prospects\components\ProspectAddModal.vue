<template>
  <el-dialog
    v-model="modalVisible"
    :title="t('prospects.addProspect')"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="add-prospect-modal">
      <!-- 线索查询区域 -->
      <div class="search-section">
        <el-form label-position="top">
          <el-row :gutter="16">
            <el-col :span="10">
              <el-form-item :label="t('prospects.prospectName')">
                <el-input
                  v-model="searchForm.customerName"
                  :placeholder="t('prospects.inputProspectName')"
                  :disabled="leadSearched"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item :label="t('prospects.prospectPhone')">
                <el-input
                  v-model="searchForm.phoneNumber"
                  :placeholder="t('prospects.inputPhoneNumber')"
                  :disabled="leadSearched"
                >
                  <template #prepend>
                    <el-select
                      v-model="searchForm.countryCode"
                      style="width: 80px"
                      placeholder="区号"
                      :disabled="leadSearched"
                    >
                      <el-option label="+60" value="+60" />
                      <el-option label="+65" value="+65" />
                      <el-option label="+86" value="+86" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="&nbsp;">
                <el-button
                  v-if="!leadSearched"
                  type="primary"
                  @click="handleSearchLead"
                  :loading="searchLoading"
                >
                  {{ tc('search') }}
                </el-button>
                <el-button
                  v-else
                  @click="handleResetSearch"
                >
                  {{ tc('reset') }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 线索查询结果 -->
      <div v-if="leadInfo" class="lead-result-section">
        <el-alert
          :title="t('prospects.existingLeadFound')"
          type="success"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>{{ t('prospects.existingLeadInfo') }}</p>
            <el-descriptions :column="2" size="small">
              <el-descriptions-item :label="t('prospects.prospectName')">
                {{ leadInfo.name }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('prospects.prospectPhone')">
                {{ leadInfo.phoneNumber }}
              </el-descriptions-item>
              <el-descriptions-item :label="t('prospects.sourceChannel')">
                {{ getSourceChannelName(leadInfo.sourceChannel || '') }}
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </el-alert>
      </div>

      <!-- 新增表单 -->
      <div  class="form-section">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectName')" prop="customerName">
                <el-input
                  v-model="formData.customerName"
                  :placeholder="t('prospects.inputProspectName')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectPhone')" prop="customerPhone">
                <el-input
                  v-model="formData.phoneNumber"
                  :placeholder="t('prospects.inputPhoneNumber')"
                >
                  <template #prepend>
                    <el-select
                      v-model="formData.countryCode"
                      style="width: 80px"
                      placeholder="区号"
                    >
                      <el-option label="+60" value="+60" />
                      <el-option label="+65" value="+65" />
                      <el-option label="+86" value="+86" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.idType')" prop="idType">
                <el-select
                  v-model="formData.idType"
                  :placeholder="t('prospects.selectIdType')"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in idTypeOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.idNumber')" prop="idNumber">
                <el-input
                  v-model="formData.idNumber"
                  :placeholder="t('prospects.inputIdNumber')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.email')" prop="email">
                <el-input
                  v-model="formData.email"
                  :placeholder="t('prospects.inputEmail')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectLevel')" prop="intentionLevel">
                <el-select
                  v-model="formData.intentionLevel"
                  :placeholder="t('prospects.selectProspectLevel')"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in intentLevelOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ tc('save') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { searchExistingLeads, createNewProspect } from '@/api/modules/sales/prospects';
import type {
  SearchExistingLeadRequest,
  ExistingLeadInfo,
  CreateProspectRequest
} from '@/types/sales/prospects';

const { t, tc } = useModuleI18n('sales');

// 组件属性
interface Props {
  show: boolean;
  presetData?: {
    customerName?: string;
    customerPhone?: string;
    sourceChannel?: string;
  };
}

interface Emits {
  (e: 'update:show', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  presetData: () => ({})
});

const emit = defineEmits<Emits>();

// 使用字典数据
const {
  getOptions,
  getNameByCode
} = useBatchDictionary([
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.ID_TYPE
]);

// 获取来源渠道名称
const getSourceChannelName = (code: string) => {
  return getNameByCode(DICTIONARY_TYPES.CUSTOMER_SOURCE, code) || code;
};

// 响应式状态
const formRef = ref<FormInstance>();

const searchLoading = ref(false);
const submitLoading = ref(false);
const leadSearched = ref(false);
const leadInfo = ref<ExistingLeadInfo | null>(null);

// 模态框显示状态
const modalVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

// 查询表单
const searchForm = reactive<SearchExistingLeadRequest>({
  customerName: '',
  countryCode: '+60', // 设置默认区号
  phoneNumber: '',
  customerPhone: ''
});

// 新增表单
const formData = reactive<Omit<CreateProspectRequest, 'sourceChannel' | 'region'>>({
  customerName: '',
  countryCode: '+60', // 区号字段
  phoneNumber: '', // 本地号码
  customerPhone: '',
  intentionLevel: '01160002', // 默认高意向 (A级)
  idType: '01140001', // 默认身份证
  idNumber: '',
  email: '',
});

// 字典选项
const sourceChannelOptions = computed(() => getOptions(DICTIONARY_TYPES.CUSTOMER_SOURCE));
const intentLevelOptions = computed(() => getOptions(DICTIONARY_TYPES.INTENT_LEVEL));
const idTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.ID_TYPE));

// 表单验证规则
const formRules = reactive<FormRules>({
  customerName: [
    { required: true, message: t('prospects.nameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('prospects.nameLength'), trigger: 'blur' }
  ],
  customerPhone: [
    { required: true, message: t('prospects.phoneRequired'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('prospects.phoneFormat'), trigger: 'blur' }
  ],
  intentionLevel: [
    { required: true, message: t('prospects.levelRequired'), trigger: 'change' }
  ],
  email: [
    { pattern: /^["\w-]+(\.["\w-]+)*@["\w-]+(\.["\w-]+)+$/, message: t('prospects.emailFormat'), trigger: 'blur' }
  ]
});

// 事件处理函数
const handleSearchLead = async () => {
  if (!searchForm.customerName && !searchForm.customerPhone) {
    ElMessage.warning(t('prospects.searchCriteriaRequired'));
    return;
  }

  try {
    searchLoading.value = true;
    const result = await searchExistingLeads(searchForm);
    console.log('搜索线索结果:', result);
    if (result) {
      console.log('搜索线索结果:', 1);
      leadInfo.value = result;
      formData.customerName = result?.name || '';
      formData.customerPhone = result?.phoneNumber || '';
      formData.idType = result?.idType || '';
      formData.idNumber = result?.idNumber || '';
      formData.email = result?.email || '';
      ElMessage.success(t('prospects.existingLeadFound'));
    } else {
      console.log('搜索线索结果:', 2);
      // 未找到线索，填充表单
      formData.customerName = searchForm.customerName || '';
      formData.customerPhone = searchForm.customerPhone || '';
      leadInfo.value = null;
      ElMessage.warning(t('prospects.noExistingLeadFound'));
    }

    leadSearched.value = true;
  } catch (error) {
    console.error('搜索线索失败:', error);
    ElMessage.error(tc('searchFailed'));
  } finally {
    searchLoading.value = false;
  }
};

const handleResetSearch = () => {
  searchForm.customerName = '';
  searchForm.countryCode = '+60'; // 重置时保持默认区号
  searchForm.phoneNumber = '';
  searchForm.customerPhone = '';
  leadSearched.value = false;
  leadInfo.value = null;
};

const resetAll = () => {
  handleResetSearch();
  formData.customerName = '';
  formData.countryCode = '+60'; // 重置时保持默认区号
  formData.phoneNumber = '';
  formData.customerPhone = '';
  formData.intentionLevel = '01160002';
  formData.idType = '01140001';
  formData.idNumber = '';
  formData.email = '';
};

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    submitLoading.value = true;
    await createNewProspect(formData);

    ElMessage.success(t('prospects.addProspectSuccess'));
    emit('success');
    modalVisible.value = false;
  } catch (error) {
    console.error('新增潜客失败:', error);
    ElMessage.error(tc('saveFailed'));
  } finally {
    submitLoading.value = false;
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  resetAll();
};

// 监听对话框显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 如果有预设数据，填充搜索表单
    if (props.presetData) {
      searchForm.customerName = props.presetData.customerName || '';
      searchForm.customerPhone = props.presetData.customerPhone || '';

      if (searchForm.customerName || searchForm.customerPhone) {
        handleSearchLead();
      }
    }
  } else {
    resetAll();
  }
});

// 监听区号和号码变化，自动组合完整号码
watch([() => formData.countryCode, () => formData.phoneNumber], () => {
  if (formData.countryCode && formData.phoneNumber) {
    formData.customerPhone = formData.countryCode + formData.phoneNumber;
  }
});
</script>

<style scoped>
.add-prospect-modal {
  padding: 0 20px;
}

.search-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #dcdfe6;
}

.lead-result-section {
  margin-bottom: 20px;
}

.form-section {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
