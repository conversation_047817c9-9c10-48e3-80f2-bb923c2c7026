<template>
  <el-dialog
    v-model="visible"
    :title="t('prospects.changeAdvisorModalTitle')"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('prospects.prospectName')">
            <el-input :value="prospectData?.name || '-'" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('prospects.prospectPhone')">
            <el-input :value="prospectData?.phoneNumber || '-'" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 当前顾问信息 -->
      <div class="current-info">
        <h4>{{ t('prospects.currentAdvisorInfo') }}</h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="t('prospects.currentAdvisor')">
              <el-input :value="prospectData?.currentSalesAdvisorName || t('prospects.noAdvisorAssigned')" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 变更信息 -->
      <div class="change-info">
        <h4>{{ t('prospects.changeAdvisorInfo') }}</h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="t('prospects.newAdvisor')" prop="newAdvisorId">
              <el-select
                v-model="formData.newAdvisorId"
                :placeholder="t('prospects.selectNewAdvisor')"
                style="width: 100%"
                :loading="advisorLoading"
              >
                <el-option
                  v-for="advisor in advisorOptions"
                  :key="advisor.id"
                  :label="advisor.name"
                  :value="advisor.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="t('prospects.changeReason')" prop="reason">
              <el-input
                v-model="formData.reason"
                type="textarea"
                :rows="3"
                :placeholder="t('prospects.inputChangeReason')"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
        >
          {{ tc('confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { assignAdvisorToProspect } from '@/api/modules/sales/prospects';
import type { ProspectItem, AssignAdvisorRequest } from '@/types/sales/prospects';

const { t, tc } = useModuleI18n('sales');

// Props定义
interface Props {
  show: boolean;
  prospectData: ProspectItem | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  prospectData: null
});

// Emits定义
interface Emits {
  (event: 'update:show', value: boolean): void;
  (event: 'success'): void;
}

const emit = defineEmits<Emits>();

// 响应式状态
const formRef = ref<FormInstance>();
const loading = ref(false);
const advisorLoading = ref(false);

// 表单数据
const formData = reactive<AssignAdvisorRequest>({
  prospectId: '',
  newAdvisorId: '',
  reason: ''
});

// 模态框显示状态
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

// 销售顾问选项（实际应该从API获取）
const advisorOptions = ref([
  { id: '1001', name: '张三' },
  { id: '1002', name: '李四' },
  { id: '1003', name: '王五' },
  { id: '1004', name: '赵六' }
]);

// 表单验证规则
const rules = reactive<FormRules>({
  newAdvisorId: [
    { required: true, message: t('prospects.newAdvisorRequired'), trigger: 'change' }
  ],
  reason: [
    { required: true, message: t('prospects.changeReasonRequired'), trigger: 'blur' },
    { min: 5, max: 200, message: t('prospects.changeReasonLength'), trigger: 'blur' }
  ]
});

// 重置表单
const resetForm = () => {
  formData.prospectId = props.prospectData?.id || '';
  formData.newAdvisorId = '';
  formData.reason = '';
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || !props.prospectData) return;

  try {
    await formRef.value.validate();

    // 检查是否选择了相同的顾问
    if (formData.newAdvisorId === props.prospectData.currentSalesAdvisorId) {
      ElMessage.error(t('prospects.newAdvisorCannotBeSame'));
      return;
    }

    loading.value = true;

    // 调用API分配顾问
    await assignAdvisorToProspect(formData);

    ElMessage.success(t('prospects.changeAdvisorSuccess'));
    emit('success');
    visible.value = false;
  } catch (error) {
    if (error instanceof Error) {
      console.error('变更顾问失败:', error);
      ElMessage.error(t('prospects.changeFailed'));
    }
  } finally {
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
};

// 监听对话框显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 加载销售顾问列表（实际应该从API获取）
const loadAdvisorOptions = async () => {
  advisorLoading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    // 实际项目中应该调用API获取顾问列表
    // advisorOptions.value = await getAdvisorList();
  } catch (error) {
    ElMessage.error(tc('getDataFailed'));
  } finally {
    advisorLoading.value = false;
  }
};

onMounted(() => {
  loadAdvisorOptions();
});
</script>

<style scoped>
.current-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.change-info {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
}
</style>
