<template>
  <el-dialog
    v-model="modalVisible"
    :title="t('prospects.markNoIntentionConfirm')"
    width="600px"
    :before-close="handleCancel"
  >
    <div class="defeat-modal">
      <!-- 潜客信息区域 -->
      <div class="prospect-info-section">
        <el-form label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectId')">
                <el-input :value="prospectInfo.prospectId || '-'" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectName')">
                <el-input :value="prospectInfo.customerName || '-'" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectPhone')">
                <el-input :value="prospectInfo.customerPhone || '-'" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.salesAdvisorName')">
                <el-input :value="prospectInfo.advisorName || '-'" readonly />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 申请信息区域 -->
      <div class="application-info-section">
        <h4>{{ t('prospects.applicationInfo') }}</h4>
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <el-form-item :label="t('prospects.defeatReason')" prop="defeatReason">
            <el-select
              v-model="formData.defeatReason"
              :placeholder="t('prospects.selectDefeatReason')"
              style="width: 100%"
              :loading="dictionaryLoading"
            >
              <el-option
                v-for="option in defeatReasonOptions"
                :key="option.code"
                :label="option.name"
                :value="option.code"
              />
            </el-select>
          </el-form-item>

          <el-form-item :label="t('prospects.defeatDetails')" prop="defeatDetails">
            <el-input
              v-model="formData.defeatDetails"
              type="textarea"
              :rows="4"
              :placeholder="t('prospects.inputDefeatDetails')"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item :label="t('prospects.applicationTime')">
            <el-input :value="currentTime" readonly />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="danger"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ t('prospects.confirmMarkNoIntention') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { markProspectNoIntention } from '@/api/modules/sales/prospects';
import type { ProspectItem, MarkNoIntentionRequest } from '@/types/sales/prospects';

const { t, tc } = useModuleI18n('sales');



// 组件属性
interface Props {
  show: boolean;
  prospectData: ProspectItem | null;
}

interface Emits {
  (e: 'update:show', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 使用字典数据
const {
  options: defeatReasonOptions,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.DEFEAT_REASON);

// 响应式状态
const formRef = ref<FormInstance>();

// 状态变量
const submitLoading = ref(false);
const currentTime = ref('');

// 潜客信息
const prospectInfo = reactive({
  prospectId: '',
  customerName: '',
  customerPhone: '',
  advisorName: ''
});

// 表单数据
const formData = reactive({
  prospectId: '',
  defeatReason: '',
  defeatDetails: ''
});

// 模态框显示状态
const modalVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

// 表单验证规则
const formRules = reactive<FormRules>({
  defeatReason: [
    { required: true, message: t('prospects.defeatReasonRequired'), trigger: 'change' }
  ],
  defeatDetails: [
    { required: true, message: t('prospects.defeatDetailsRequired'), trigger: 'blur' },
    { min: 10, max: 500, message: t('prospects.defeatDetailsLength'), trigger: 'blur' }
  ]
});

// 事件处理函数
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    submitLoading.value = true;

    // 构建请求数据
    const requestData: MarkNoIntentionRequest = {
      prospectId: formData.prospectId,
      reason: formData.defeatReason,
      description: formData.defeatDetails
    };

    // 调用API提交战败申请
    await markProspectNoIntention(requestData);

    ElMessage.success(t('prospects.markNoIntentionSubmitSuccess'));
    emit('success');
    handleCancel();
  } catch (error) {
    console.error('标记无意向申请提交失败:', error);
    ElMessage.error(t('prospects.formValidationFailed'));
  } finally {
    submitLoading.value = false;
  }
};

const handleCancel = () => {
  modalVisible.value = false;
};

// 重置表单
const resetForm = () => {
  formData.prospectId = '';
  formData.defeatReason = '';
  formData.defeatDetails = '';

  prospectInfo.prospectId = '';
  prospectInfo.customerName = '';
  prospectInfo.customerPhone = '';
  prospectInfo.advisorName = '';

  // 设置当前时间
  currentTime.value = new Date().toLocaleString();
};

// 初始化表单数据
const initFormData = () => {
  if (props.prospectData) {
    formData.prospectId = props.prospectData.id;

    prospectInfo.prospectId = props.prospectData.id;
    prospectInfo.customerName = props.prospectData.name;
    prospectInfo.customerPhone = props.prospectData.phoneNumber;
    prospectInfo.advisorName = props.prospectData.currentSalesAdvisorName || '';
  }

  // 设置当前时间
  currentTime.value = new Date().toLocaleString();
};

// 监听对话框显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    resetForm();
    initFormData();
  }
});

// 监听潜客数据变化
watch(() => props.prospectData, () => {
  if (props.show) {
    initFormData();
  }
});
</script>

<style scoped>
.defeat-modal {
  padding: 0 20px;
}

.prospect-info-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.application-info-section {
  margin-bottom: 20px;
}

.application-info-section h4 {
  margin-bottom: 16px;
  color: #303133;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  cursor: not-allowed;
}
</style>
