<template>
  <el-dialog
    :model-value="show"
    :title="t('detailTitle')"
    width="800px"
    :modal="false"
    @update:model-value="emit('update:show', $event)"
  >
    <el-form label-position="left" :label-width="120">
        <h3>{{ t('customerInfo') }}</h3>
        <el-row :gutter="16">
            <el-col :span="12">
                <el-form-item :label="t('customerSource')">
                    {{ getNameByCode(DICTIONARY_TYPES.CUSTOMER_SOURCE, safeRecordData.sourceChannel || safeRecordData.source) || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('customerName')">
                    {{ maskName(safeRecordData.customerName || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('customerPhone')">
                    {{ maskPhone(safeRecordData.customerPhone || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('idType')">
                    {{ getNameByCode(DICTIONARY_TYPES.ID_TYPE, safeRecordData.customerIdType || safeRecordData.idType) || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('idNumber')">
                    {{ maskIdNumber(safeRecordData.customerIdNumber || safeRecordData.idNumber || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('email')">
                    {{ maskEmail(safeRecordData.email || '') }}
                </el-form-item>
            </el-col>
        </el-row>

        <h3>{{ t('testDriveInfo') }}</h3>
        <el-row :gutter="16">
             <el-col :span="12">
                <el-form-item :label="t('testDriveNo')">
                    {{ safeRecordData.testDriveNo }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('driverName')">
                    {{ maskName(safeRecordData.driverName || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('driverPhone')">
                    {{ maskPhone(safeRecordData.driverPhone || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('driverIdType')">
                    {{ getNameByCode(DICTIONARY_TYPES.ID_TYPE, safeRecordData.driverIdType) || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item :label="t('driverIdNumber')">
                    {{ maskIdNumber(safeRecordData.driverIdNumber || '') }}
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item :label="t('vehicleInfo')">
                    {{ `${getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, safeRecordData.model) || safeRecordData.model || '-'} ${safeRecordData.variant ? '- ' + safeRecordData.variant : ''}` }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('startMileage')">
                    {{ safeRecordData.startMileage || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('endMileage')">
                    {{ safeRecordData.endMileage || '-' }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('startTime')">
                    {{ formatDate(safeRecordData.startTime) }}
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="t('endTime')">
                    {{ formatDate(safeRecordData.endTime) }}
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item :label="t('feedback')">
                    <div style="white-space: pre-wrap;">{{ safeRecordData.feedback || '-' }}</div>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="emit('update:show', false)">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { TestDriveItem } from '@/types/sales/testDriveReport';
import { maskPhone, maskName, maskIdNumber } from '@/utils/data-mask';

// 国际化
const { t } = useModuleI18n('sales.testDriveReport');
const { t: tc } = useModuleI18n('common');

// 数据字典
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.ID_TYPE,
  DICTIONARY_TYPES.VEHICLE_MODEL
]);

// 添加 maskEmail 函数
const maskEmail = (email: string): string => {
  if (!email || email.length < 5) return email;
  const parts = email.split('@');
  if (parts.length !== 2) return email;

  const name = parts[0];
  const domain = parts[1];

  // 保留名称的第一个字符，其余用 * 替换
  const maskedName = name.charAt(0) + '*'.repeat(Math.max(name.length - 1, 2));

  return `${maskedName}@${domain}`;
};

const props = defineProps<{
  show: boolean
  recordData: TestDriveItem | null
}>();

const emit = defineEmits(['update:show']);

const safeRecordData = computed<TestDriveItem>(() => {
  return props.recordData || ({} as TestDriveItem);
});

const formatDate = (dateString?: string) => {
  if (!dateString) return '-';
  try {
    return new Date(dateString).toLocaleString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/,/g, '');
  } catch {
    return dateString;
  }
};
</script>

<style scoped>
h3 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  padding-left: 8px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
