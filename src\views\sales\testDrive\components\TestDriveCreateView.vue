<template>
  <el-dialog
    v-model="visible"
    :title="t('testDriveCreate')"
    width="800px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="120px">
      <h3 class="section-title">{{ t('prospectSearch') }}</h3>
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item :label="t('prospectName')">
            <el-input v-model="prospectSearch.name" :placeholder="t('pleaseEnterProspectName')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('prospectPhone')">
            <el-input v-model="prospectSearch.phone" :placeholder="t('pleaseEnterProspectPhone')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" @click="handleSearchProspect">{{ t('searchProspect') }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <h3 class="section-title">{{ t('prospectInfo') }}</h3>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item :label="'*' + t('sourceChannel')" prop="source">
            <el-input v-model="formModel.source" disabled :placeholder="t('autoFillAfterSearch')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('prospectName')" prop="customerName">
            <el-input v-model="formModel.customerName" disabled :placeholder="t('autoFillAfterSearch')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('prospectPhone')" prop="customerPhone">
            <el-input v-model="formModel.customerPhone" disabled :placeholder="t('autoFillAfterSearch')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('idType')" prop="customerIdType">
            <el-input v-model="formModel.customerIdType" disabled :placeholder="t('autoFillAfterSearch')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('idNumber')" prop="customerIdNumber">
            <el-input v-model="formModel.customerIdNumber" disabled :placeholder="t('autoFillAfterSearch')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('email')" prop="email">
            <el-input v-model="formModel.email" disabled :placeholder="t('autoFillAfterSearch')" />
          </el-form-item>
        </el-col>
      </el-row>

      <h3 class="section-title">{{ t('testDriveInfo') }}</h3>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item :label="t('testDriveNo')" prop="testDriveNo">
            <el-input v-model="formModel.testDriveNo" disabled :placeholder="t('autoGenerateOnSave')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('salesConsultant')" prop="consultantName">
            <el-input v-model="formModel.consultantName" disabled :placeholder="t('belongingConsultant')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDriveModel')" prop="model">
            <el-select v-model="formModel.model" :placeholder="t('pleaseSelectModel')" style="width: 100%">
              <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDriveVariant')" prop="variant">
            <el-select v-model="formModel.variant" :placeholder="t('pleaseSelectVariant')" style="width: 100%">
              <el-option v-for="item in variantOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDrivePerson')" prop="driverName">
            <el-input v-model="formModel.driverName" :placeholder="t('defaultFromProspect')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDrivePersonPhone')" prop="driverPhone">
            <el-input v-model="formModel.driverPhone" :placeholder="t('defaultFromProspect')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('idType')" prop="driverIdType">
            <el-select v-model="formModel.driverIdType" :placeholder="t('pleaseSelectIdType')" style="width: 100%" :loading="dictionaryLoading">
              <el-option v-for="item in idTypeOptions" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDrivePersonIdCard')" prop="driverIdNumber">
            <el-input v-model="formModel.driverIdNumber" :placeholder="t('pleaseEnterIdNumber')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDrivePersonLicense')" prop="driverLicenseNumber">
            <el-input v-model="formModel.driverLicenseNumber" :placeholder="t('pleaseEnterLicenseNumber')" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDriveStartMileage')" prop="startMileage">
            <el-input-number v-model="formModel.startMileage" :min="0" :placeholder="t('pleaseEnterStartMileage')" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDriveEndMileage')" prop="endMileage">
            <el-input-number v-model="formModel.endMileage" :min="formModel.startMileage || 0" :placeholder="t('pleaseEnterEndMileage')" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDriveStartTime')" prop="startTime">
            <el-date-picker
              v-model="formModel.startTime"
              type="datetime"
              :placeholder="t('pleaseSelectStartTime')"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="'*' + t('testDriveEndTime')" prop="endTime">
            <el-date-picker
              v-model="formModel.endTime"
              type="datetime"
              :placeholder="t('pleaseSelectEndTime')"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('testDriveFeedback')" prop="feedback">
            <el-input v-model="formModel.feedback" type="textarea" :placeholder="t('pleaseEnterFeedback')" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">{{ tc('confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { testDriveApi } from '@/api/modules/sales/testDrive'
// import type { TestDriveRecord } from '@/types/sales/testDrive'
import { useDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'

const { t, tc } = useModuleI18n('sales')

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// Refs
const formRef = ref<FormInstance>()
const loading = ref(false)

// 使用字典数据
const {
  options: idTypeOptions,
  // getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.ID_TYPE)

// Computed
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 创建初始表单模型
const createInitialFormModel = () => ({
  testDriveNo: '',
  source: '',
  customerName: '',
  customerPhone: '',
  customerIdType: '',
  customerIdNumber: '',
  email: '',
  consultantName: '',
  model: null as number | null,
  variant: null as number | null,
  driverName: '',
  driverPhone: '',
  driverIdType: null as number | null,
  driverIdNumber: '',
  driverLicenseNumber: '',
  startMileage: null as number | null,
  endMileage: null as number | null,
  startTime: null as Date | null,
  endTime: null as Date | null,
  feedback: '',
  // 隐藏字段
  customerId: undefined as number | undefined,
  consultantId: undefined as number | undefined,
  storeId: undefined as number | undefined,
  prospectId: undefined as number | undefined
})

// 表单数据
const formModel = reactive(createInitialFormModel())

// 潜客搜索数据
const prospectSearch = reactive({
  name: '',
  phone: ''
})

// 表单验证规则
const rules: FormRules = {
  customerName: { required: true, message: t('searchProspectFirst'), trigger: 'change' },
  consultantName: { required: true, message: t('needSalesConsultant'), trigger: 'change' },
  model: { required: true, message: t('pleaseSelectModel'), trigger: 'change' },
  variant: { required: true, message: t('pleaseSelectVariant'), trigger: 'change' },
  driverName: { required: true, message: t('pleaseEnterDriverName'), trigger: 'blur' },
  driverPhone: { required: true, message: t('pleaseEnterDriverPhone'), trigger: 'blur' },
  driverIdType: { required: true, message: t('pleaseSelectIdType'), trigger: 'change' },
  driverIdNumber: { required: true, message: t('pleaseEnterIdNumber'), trigger: 'blur' },
  driverLicenseNumber: { required: true, message: t('pleaseEnterLicenseNumber'), trigger: 'blur' },
  startMileage: { required: true, type: 'number', message: t('pleaseEnterStartMileage'), trigger: 'blur' },
  endMileage: { required: true, type: 'number', message: t('pleaseEnterEndMileage'), trigger: 'blur' },
  startTime: { required: true, message: t('pleaseSelectStartTime'), trigger: 'change' },
  endTime: { required: true, message: t('pleaseSelectEndTime'), trigger: 'change' }
}

// 临时静态选项 - 实际项目中应该从后端获取
const modelOptions = [
  { label: 'Model S', value: 1 },
  { label: 'Model 3', value: 2 },
  { label: 'Model X', value: 3 },
  { label: 'Model Y', value: 4 }
]

const variantOptions = [
  { label: '标准版', value: 1 },
  { label: '长续航版', value: 2 },
  { label: '性能版', value: 3 }
]

// 方法
const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(formModel, createInitialFormModel())
  prospectSearch.name = ''
  prospectSearch.phone = ''
  formRef.value?.clearValidate()
}

const handleSearchProspect = async () => {
  if (!prospectSearch.name && !prospectSearch.phone) {
    ElMessage.warning(t('pleaseEnterNameOrPhone'))
    return
  }

  loading.value = true
  try {
    const params = {
      customerName: prospectSearch.name || undefined,
      customerPhone: prospectSearch.phone || undefined
    }

    const response = await testDriveApi.searchLeads(params)
    console.log('搜索潜客结果:', response)

    // 处理搜索结果
    if (response && response.result) {
      const lead = response.result // 取第一个结果
      console.log('搜索潜客结果2:', lead)
      // 填充潜客信息
      formModel.source = lead.sourceChannel || ''
      formModel.customerName = lead.name || ''
      formModel.customerPhone = lead.phoneNumber || ''
      formModel.customerIdType = lead.idType || ''
      formModel.customerIdNumber = lead.idNumber || ''
      formModel.email = lead.email || ''
      formModel.customerId = lead.prospectId
      formModel.prospectId = lead.prospectId || ''


      // 默认填充试驾人信息
      formModel.driverName = lead.name || ''
      formModel.driverPhone = lead.phone || ''
      formModel.driverIdNumber = lead.idNumber || ''
      formModel.driverIdType = lead.idType || ''
      formModel.consultantName = lead.currentSalesAdvisorId || ''

      ElMessage.success(t('searchSuccess'))
    } else {
      ElMessage.warning(t('noMatchingProspect'))
    }
  } catch (error: unknown) {
    console.error('搜索潜客出错:', error)
    ElMessage.error(`${tc('searchFailed')}: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:modelValue', false)
}

const handleConfirm = () => {
  console.log('表单验证前的数据:', JSON.stringify(formModel, null, 2))

  formRef.value?.validate(async (valid) => {
    if (!valid) {
      return
    }

    if ((formModel.endMileage || 0) < (formModel.startMileage || 0)) {
      ElMessage.error(t('endMileageError'))
      return
    }

    if (formModel.endTime && formModel.startTime && formModel.endTime <= formModel.startTime) {
      ElMessage.error(t('endTimeError'))
      return
    }

    loading.value = true
    try {
      const payload = {
        customerId: formModel.customerId,
        prospectId: formModel.prospectId,
        customerName: formModel.customerName,
        customerPhone: formModel.customerPhone,
        customerIdType: formModel.customerIdType,
        customerIdNumber: formModel.customerIdNumber,
        source: formModel.source,
        email: formModel.email,
        driverName: formModel.driverName,
        driverPhone: formModel.driverPhone,
        driverIdType: formModel.driverIdType,
        driverIdNumber: formModel.driverIdNumber,
        driverLicenseNumber: formModel.driverLicenseNumber,
        model: formModel.model,
        variant: formModel.variant,
        startMileage: formModel.startMileage,
        endMileage: formModel.endMileage,
        startTime: formModel.startTime?.toISOString(),
        endTime: formModel.endTime?.toISOString(),
        feedback: formModel.feedback,
        consultantId: formModel.consultantId,
        consultantName: formModel.consultantName,
        storeId: formModel.storeId
      }
      console.log('提交的数据:', JSON.stringify(payload, null, 2))

      await testDriveApi.createTestDrive(payload)
      ElMessage.success(t('registrationSuccess'))
      emit('success')
      emit('update:modelValue', false)
    } catch (error: unknown) {
      ElMessage.error(`${tc('registrationFailed')}: ${error.message}`)
    } finally {
      loading.value = false
    }
  })
}

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.section-title {
  margin-top: 10px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
