<template>
  <el-dialog
    v-model="visible"
    :title="t('testDriveDetail')"
    width="800px"
  >
    <el-form label-position="left" label-width="120px">
      <h3 class="section-title">{{ t('prospectInfo') }}</h3>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item :label="t('sourceChannel')">
            {{ getName(DICTIONARY_TYPES.CUSTOMER_SOURCE,testDriveData.sourceChannel)  }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('prospectName')">
            {{ testDriveData?.customerName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('prospectPhone')">
            {{ testDriveData?.customerPhone }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('idType')">
            {{ getName(DICTIONARY_TYPES.ID_TYPE,testDriveData.idType)}}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('idNumber')">
            {{ testDriveData?.idNumber }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('email')">
            {{ testDriveData?.email }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('region')">
            {{ testDriveData?.region }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('address')">
            {{ testDriveData?.address }}
          </el-form-item>
        </el-col>
      </el-row>

      <h3 class="section-title">{{ t('testDriveInfo') }}</h3>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item :label="t('testDriveNo')">
            {{ testDriveData?.testDriveNo }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('salesConsultant')">
            {{ testDriveData?.consultantName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDriveModel')">
            {{ testDriveData?.model }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDriveVariant')">
            {{ testDriveData?.variant }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDrivePerson')">
            {{ testDriveData?.driverName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDrivePersonPhone')">
            {{ testDriveData?.driverPhone }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('idType')">
            {{ getName(DICTIONARY_TYPES.ID_TYPE,testDriveData.driverIdType)}}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDrivePersonIdCard')">
            {{ testDriveData?.driverIdNumber }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDrivePersonLicense')">
            {{ testDriveData?.driverLicenseNumber }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDriveStartMileage')">
            {{ testDriveData?.startMileage }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDriveEndMileage')">
            {{ testDriveData?.endMileage }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDriveStartTime')">
            {{ formatDate(testDriveData?.startTime) }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('testDriveEndTime')">
            {{ formatDate(testDriveData?.endTime) }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('testDriveFeedback')">
            <div style="white-space: pre-wrap;">{{ testDriveData?.feedback }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { TestDriveRecord } from '@/types/sales/testDrive'
import {DICTIONARY_TYPES, type DictionaryType} from "@/constants/dictionary.ts";
import {useBatchDictionary} from "@/composables/useDictionary.ts";

// 字典数据
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.ID_TYPE,
]);

const { t, tc } = useModuleI18n('sales')

// Props
const props = defineProps<{
  modelValue: boolean
  testDriveData: TestDriveRecord | null
}>()

// Emits
const emit = defineEmits(['update:modelValue'])

// Computed properties
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Methods
const closeDialog = () => {
  emit('update:modelValue', false)
}

const formatDate = (dateString?: string | number | null) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/,/g, '')
}

// 获取来源渠道名称
const getName = (dictionaryType: DictionaryType,code: string) => {
  return getNameByCode(dictionaryType, code) || code;
};

</script>

<style scoped>
.section-title {
  margin-top: 20px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
