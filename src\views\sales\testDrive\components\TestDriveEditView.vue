<template>
  <el-dialog
    :model-value="show"
    :title="t('testDriveEdit')"
    width="800px"
    @update:model-value="handleUpdateShow"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-position="left" label-width="120px">
      <h3 class="section-title">{{ t('prospectInfo') }}</h3>
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item :label="t('sourceChannel')" prop="source">
            <el-input :value="getName(DICTIONARY_TYPES.CUSTOMER_SOURCE, formModel.source)" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('prospectName')" prop="customerName">
            <el-input v-model="formModel.customerName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('prospectPhone')" prop="customerPhone">
            <el-input v-model="formModel.customerPhone" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('idType')" prop="customerIdType">
            <el-input  :value="getName(DICTIONARY_TYPES.ID_TYPE, formModel.customerIdType)"  disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('idNumber')" prop="customerIdNumber">
            <el-input v-model="formModel.customerIdNumber" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('email')" prop="email">
            <el-input v-model="formModel.email" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('region')" prop="region">
            <el-input v-model="formModel.region" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('address')" prop="address">
            <el-input v-model="formModel.address" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <h3 class="section-title">{{ t('testDriveInfo') }}</h3>
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item :label="t('testDriveNo')" prop="testDriveNo">
            <el-input v-model="formModel.testDriveNo" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('salesConsultant')" prop="consultantId">
            <el-select
              v-model="formModel.consultantId"
              placeholder="t('salesConsultant')"
            >
              <el-option
                v-for="item in salesAdvisorOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDriveModel')" prop="model">
            <el-select
              v-model="formModel.model"
              placeholder="t('testDriveModel')"
            >
              <el-option
                v-for="item in modelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDriveVariant')" prop="variant">
            <el-select
              v-model="formModel.variant"
              placeholder="t('testDriveVariant')"
            >
              <el-option
                v-for="item in variantOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDrivePerson')" prop="driverName">
            <el-input v-model="formModel.driverName" placeholder="t('testDrivePerson')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDrivePersonPhone')" prop="driverPhone">
            <el-input v-model="formModel.driverPhone" placeholder="t('testDrivePersonPhone')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('idType')" prop="driverIdType">
            <el-select
              v-model="formModel.driverIdType"
              placeholder="t('idType')"
              clearable
              :loading="dictionaryLoading"
            >
              <el-option
                v-for="item in idTypeOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDrivePersonIdCard')" prop="driverIdNumber">
            <el-input v-model="formModel.driverIdNumber" placeholder="t('testDrivePersonIdCard')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDrivePersonLicense')" prop="driverLicenseNumber">
            <el-input v-model="formModel.driverLicenseNumber" placeholder="t('testDrivePersonLicense')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDriveStartMileage')" prop="startMileage">
            <el-input-number
              v-model="formModel.startMileage"
              :min="0"
              :precision="2"
              placeholder="请输入"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDriveEndMileage')" prop="endMileage">
            <el-input-number
              v-model="formModel.endMileage"
              :min="formModel.startMileage || 0"
              :precision="2"
              placeholder=""
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDriveStartTime')" prop="startTime">
            <el-date-picker
              v-model="formModel.startTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('testDriveEndTime')" prop="endTime">
            <el-date-picker
              v-model="formModel.endTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="t('testDriveFeedback')" prop="feedback">
            <el-input
              v-model="formModel.feedback"
              type="textarea"
              placeholder="t('testDriveFeedback')"
              :maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">{{ tc('confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { testDriveApi } from '@/api/modules/sales/testDrive'
import type { TestDriveRecord } from '@/types/sales/testDrive'
import { useDictionary } from '@/composables/useDictionary'
import {DICTIONARY_TYPES, type DictionaryType} from "@/constants/dictionary.ts";
import {useBatchDictionary} from "@/composables/useDictionary.ts";

// 字典数据
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.ID_TYPE,
]);

const { t, tc } = useModuleI18n('sales')

// 定义组件 Props
interface Props {
  show: boolean
  testDriveData: TestDriveRecord | null
}

// 定义组件 Emits
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 使用字典数据
const {
  options: idTypeOptions,
  // getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.ID_TYPE)

// 表单数据模型 - 遵循后端 DTO 结构
interface TestDriveFormModel {
  testDriveNo: string
  // 潜客信息 (只读)
  customerId?: number
  customerName: string
  customerPhone: string
  customerIdType?: string | number
  customerIdNumber?: string
  email?: string
  source?: string
  region?: string
  address?: string

  // 试驾信息 (可编辑)
  consultantId?: number
  consultantName?: string | number | null
  model: string | number
  variant: string | number
  driverName: string
  driverPhone: string
  driverIdType?: string | number
  driverIdNumber: string
  driverLicenseNumber: string
  startMileage: number
  endMileage: number
  startTime: string | Date
  endTime: string | Date
  feedback?: string
}

// 初始化表单数据
const formModel = reactive<TestDriveFormModel>({
  testDriveNo: '',
  customerName: '',
  customerPhone: '',
  customerIdNumber: '',
  email: '',
  source: '',
  region: '',
  address: '',
  model: '',
  variant: '',
  driverName: '',
  driverPhone: '',
  driverIdType: '',
  driverIdNumber: '',
  driverLicenseNumber: '',
  startMileage: 0,
  endMileage: 0,
  startTime: '',
  endTime: '',
  feedback: ''
})

// 表单验证规则
const rules: FormRules = {
  consultantId: [
    { required: true, message: '请选择销售顾问', trigger: 'change' }
  ],
  model: [
    { required: true, message: '请选择试驾车型', trigger: 'change' }
  ],
  variant: [
    { required: true, message: '请选择试驾配置', trigger: 'change' }
  ],
  driverName: [
    { required: true, message: '请输入试驾人姓名', trigger: 'blur' }
  ],
  driverPhone: [
    { required: true, message: '请输入试驾人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  driverIdType: [
    { required: true, message: '请选择试驾人证件类别', trigger: 'change' }
  ],
  driverIdNumber: [
    { required: true, message: '请输入试驾人证件号', trigger: 'blur' }
  ],
  driverLicenseNumber: [
    { required: true, message: '请输入试驾人驾照号', trigger: 'blur' }
  ],
  startMileage: [
    { required: true, message: '请输入试驾开始里程数', trigger: 'blur' }
  ],
  endMileage: [
    { required: true, message: '请输入试驾结束里程数', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择试驾开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择试驾结束时间', trigger: 'change' }
  ]
}

// 选项数据 - 实际项目中应该从后端获取
const salesAdvisorOptions = ref([
  { value: 1, label: '张三' },
  { value: 2, label: '李四' },
  { value: 3, label: '王五' }
])

const modelOptions = ref([
  { value: 'AXIA', label: 'AXIA' },
  { value: 'BEZZA', label: 'BEZZA' },
  { value: 'ALZA', label: 'ALZA' },
  { value: 'ARUZ', label: 'ARUZ' }
])

const variantOptions = ref([
  { value: 'E', label: 'E' },
  { value: 'G', label: 'G' },
  { value: 'AV', label: 'AV' }
])

// 获取来源渠道名称
const getName = (dictionaryType: DictionaryType,code: string) => {
  return getNameByCode(dictionaryType, code) || code;
};

// 填充表单数据的函数
const fillFormData = () => {
  console.log('=== fillFormData 被调用 ===')
  console.log('props.testDriveData:', JSON.stringify(props.testDriveData, null, 2))
  console.log('props.show:', props.show)
  console.log('当前 formModel:', JSON.stringify(formModel, null, 2))

  if (props.testDriveData) {
    console.log('TestDriveEditView - 接收到的数据:', props.testDriveData)

    // 使用nextTick确保DOM更新后再填充数据
    nextTick(() => {
      const data = props.testDriveData!
      console.log('TestDriveEditView - 填充表单数据...',data)
      // 填充表单数据
      Object.assign(formModel, {
        testDriveNo: data.testDriveNo || '',
        customerId: data.customerId,
        customerName: data.customerName || '',
        customerPhone: data.customerPhone || '',
        customerIdType: data.idType || '',
        customerIdNumber: data.idNumber || '',
        email: data.email || '',
        source:  data.sourceChannel || '',
        region: data.region || '',
        address: data.address || '',
        consultantId: data.consultantId,
        consultantName: data.consultantName,
        model: data.model || '',
        variant: data.variant || '',
        driverName: data.driverName || '',
        driverPhone: data.driverPhone || '',
        driverIdType: data.driverIdType,
        driverIdNumber: data.driverIdNumber || '',
        driverLicenseNumber: data.driverLicenseNumber || '',
        startMileage: data.startMileage || 0,
        endMileage: data.endMileage || 0,
        startTime: data.startTime ? new Date(data.startTime) : '',
        endTime: data.endTime ? new Date(data.endTime) : '',
        feedback: data.feedback || ''
      })

      console.log('=== TestDriveEditView - 填充后的表单数据 ===')
      console.log('填充后的 formModel:', JSON.stringify(formModel, null, 2))
    })
  } else {
    console.log('props.testDriveData 为空，无法填充数据')
  }
}

// 监听弹窗显示状态，初始化表单数据
watch(() => props.show, (newVal, oldVal) => {
  console.log('watch props.show 触发:', { newVal, oldVal })
  if (newVal) {
    console.log('弹窗显示，准备填充数据')
    fillFormData()
  }
})

// 监听数据变化，重新填充表单
watch(() => props.testDriveData, (newVal, oldVal) => {
  console.log('watch props.testDriveData 触发:', { newVal, oldVal })
  if (props.show && newVal) {
    console.log('数据变化且弹窗显示，准备填充数据')
    fillFormData()
  }
}, { deep: true })

// 同时监听两个属性的组合变化
watch([() => props.show, () => props.testDriveData], ([show, data]) => {
  console.log('watch 组合监听触发:', { show, data })
  if (show && data) {
    console.log('弹窗显示且数据存在，填充数据')
    fillFormData()
  }
})

// 组件挂载时的调试信息
onMounted(() => {
  console.log('TestDriveEditView 组件已挂载')
  console.log('挂载时 props.show:', props.show)
  console.log('挂载时 props.testDriveData:', props.testDriveData)

  // 如果挂载时就有数据和显示状态，直接填充
  if (props.show && props.testDriveData) {
    console.log('挂载时就有数据，直接填充')
    fillFormData()
  }
})

// 处理弹窗显示状态变化
const handleUpdateShow = (value: boolean) => {
  emit('update:show', value)
}

// 处理取消
const handleCancel = () => {
  handleUpdateShow(false)
}

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证里程数
    if ((formModel.endMileage || 0) < (formModel.startMileage || 0)) {
      ElMessage.error(t('endMileageError'))
      return
    }

    // 验证时间
    if (formModel.endTime && formModel.startTime) {
      const startTime = formModel.startTime instanceof Date ? formModel.startTime : new Date(formModel.startTime)
      const endTime = formModel.endTime instanceof Date ? formModel.endTime : new Date(formModel.endTime)
      if (endTime <= startTime) {
        ElMessage.error(t('endTimeError'))
        return
      }
    }

    loading.value = true

    // 调用更新接口
    const updateData = {
      id: props.testDriveData?.id || '',
      testDriveNo: formModel.testDriveNo,
      customerId: formModel.customerId,
      customerName: formModel.customerName,
      customerPhone: formModel.customerPhone,
      customerIdType: formModel.customerIdType,
      customerIdNumber: formModel.customerIdNumber,
      email: formModel.email,
      source: formModel.source,
      region: formModel.region,
      address: formModel.address,
      consultantId: formModel.consultantId,
      consultantName: formModel.consultantName,
      model: formModel.model,
      variant: formModel.variant,
      driverName: formModel.driverName,
      driverPhone: formModel.driverPhone,
      driverIdType: formModel.driverIdType,
      driverIdNumber: formModel.driverIdNumber,
      driverLicenseNumber: formModel.driverLicenseNumber,
      startMileage: formModel.startMileage,
      endMileage: formModel.endMileage,
      startTime: typeof formModel.startTime === 'object' && formModel.startTime instanceof Date
        ? formModel.startTime.toISOString()
        : formModel.startTime,
      endTime: typeof formModel.endTime === 'object' && formModel.endTime instanceof Date
        ? formModel.endTime.toISOString()
        : formModel.endTime,
      feedback: formModel.feedback
    }
    await testDriveApi.updateTestDrive(updateData)

    ElMessage.success(t('updateSuccess'))
    emit('success')
    handleUpdateShow(false)
  } catch (error) {
    console.error('更新试驾单失败:', error)
    ElMessage.error(tc('updateFailed'))
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.section-title {
  margin-top: 20px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
