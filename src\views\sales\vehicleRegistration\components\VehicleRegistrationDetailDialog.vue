<template>
  <el-dialog
    :model-value="visible"
    :title="t('dialog.detail.title')"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="detail-content" v-loading="loading" :element-loading-text="t('common.loading')">
      <template v-if="detail">
        <!-- 订单信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ t('dialog.detail.orderInfo') }}</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.orderInfo.orderNumber') }}:</span>
                <span class="value">{{ detail.orderNumber || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.orderInfo.orderStatus') }}:</span>
                <span class="value">{{ detail.orderStatus || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.orderInfo.paymentStatus') }}:</span>
                <span class="value">{{ detail.paymentStatus || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.orderInfo.createdAt') }}:</span>
                <span class="value">{{ detail.createdAt ? formatDateTime(detail.createdAt) : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.orderInfo.lastPushTime') }}:</span>
                <span class="value">{{ detail.lastPushTime ? formatDateTime(detail.lastPushTime) : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.orderInfo.pushCount') }}:</span>
                <span class="value">{{ detail.pushCount || 0 }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ t('dialog.detail.customerInfo') }}</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.name') }}:</span>
                <span class="value">{{ detail.customerName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.idType') }}:</span>
                <span class="value">{{ detail.customerIdType || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.idNumber') }}:</span>
                <span class="value">{{ detail.customerIdNumber || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.phone') }}:</span>
                <span class="value">{{ detail.customerPhone || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.email') }}:</span>
                <span class="value">{{ detail.customerEmail || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.type') }}:</span>
                <span class="value">{{ detail.customerType || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.city') }}:</span>
                <span class="value">{{ detail.customerCity || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.state') }}:</span>
                <span class="value">{{ detail.customerState || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.postcode') }}:</span>
                <span class="value">{{ detail.customerPostcode || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <span class="label">{{ t('fields.customerInfo.address') }}:</span>
                <span class="value">{{ detail.customerAddress || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ t('dialog.detail.vehicleInfo') }}</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.vehicleInfo.vin') }}:</span>
                <span class="value">{{ detail.vin || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.vehicleInfo.model') }}:</span>
                <span class="value">{{ detail.vehicleModel || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.vehicleInfo.variant') }}:</span>
                <span class="value">{{ detail.vehicleVariant || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.vehicleInfo.color') }}:</span>
                <span class="value">{{ detail.vehicleColor || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.vehicleInfo.engineNumber') }}:</span>
                <span class="value">{{ detail.engineNo || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.vehicleInfo.otrAmount') }}:</span>
                <span class="value">{{ detail.otrAmount ? t('units.currency') + detail.otrAmount : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.vehicleInfo.totalAmount') }}:</span>
                <span class="value">{{ detail.totalAmount ? t('units.currency') + detail.totalAmount : '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 保险信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ t('dialog.detail.insuranceInfo') }}</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.insuranceInfo.status') }}:</span>
                <span class="value">{{ detail.insuranceStatus || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.insuranceInfo.company') }}:</span>
                <span class="value">{{ detail.insuranceCompany || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.insuranceInfo.number') }}:</span>
                <span class="value">{{ detail.insuranceNumber || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.insuranceInfo.startDate') }}:</span>
                <span class="value">{{ detail.insuranceStartDate || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.insuranceInfo.endDate') }}:</span>
                <span class="value">{{ detail.insuranceEndDate || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- JPJ登记信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ t('dialog.detail.jpjInfo') }}</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.status') }}:</span>
                <el-tag :type="getStatusTagType(detail.status, DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS)">
                  {{ detail.statusName || detail.status || '-' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.certificateNumber') }}:</span>
                <span class="value">{{ detail.certificateNumber || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.jpjCertificateNumber') }}:</span>
                <span class="value">{{ detail.jpjCertificateNumber || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.jpjReferenceNumber') }}:</span>
                <span class="value">{{ detail.jpjReferenceNumber || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.pushTime') }}:</span>
                <span class="value">{{ detail.lastPushTime ? formatDateTime(detail.lastPushTime) : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.completionTime') }}:</span>
                <span class="value">{{ detail.completionTime ? formatDateTime(detail.completionTime) : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.registrationFee') }}:</span>
                <span class="value">{{ detail.registrationFee ? t('units.currency') + detail.registrationFee : '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.operator') }}:</span>
                <span class="value">{{ detail.operatorName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="24" v-if="detail.failureReason">
              <div class="info-item">
                <span class="label">{{ t('fields.jpjInfo.failureReason') }}:</span>
                <span class="value error-text">{{ detail.failureReason }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 费用明细 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ t('dialog.detail.feeDetails') }}</span>
          </template>
          <div class="fee-summary">
            <div class="total-fee">
              <span class="label">{{ t('fields.feeDetails.totalAmount') }}:</span>
              <span class="value highlight">{{ t('units.currency') }}{{ detail.totalFeeAmount || 0 }}</span>
            </div>
          </div>
          <el-table :data="detail.feeDetails || []" stripe size="small">
            <el-table-column
              :label="t('fields.feeDetails.feeType')"
              prop="feeTypeDisplay"
              width="200"
            />
            <el-table-column
              :label="t('fields.feeDetails.amount')"
              prop="amount"
              width="150"
              align="right"
            >
              <template #default="{ row }">
                {{ t('units.currency') }}{{ row.amount || 0 }}
              </template>
            </el-table-column>
            <el-table-column
              :label="t('fields.feeDetails.createdAt')"
              prop="createdAt"
              width="180"
            >
              <template #default="{ row }">
                {{ row.createdAt ? formatDateTime(row.createdAt) : '-' }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 操作日志 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <span class="card-title">{{ t('dialog.detail.operationLogs') }}</span>
          </template>
          <el-table :data="detail.operationLogs || []" stripe>
            <el-table-column
              :label="t('fields.operationLog.operationTime')"
              prop="operationTime"
              width="180"
            >
              <template #default="{ row }">
                {{ row.operationTime ? formatDateTime(row.operationTime) : '-' }}
              </template>
            </el-table-column>
            <el-table-column
              :label="t('fields.operationLog.operationType')"
              prop="operationTypeName"
              width="150"
            />
            <el-table-column
              :label="t('fields.operationLog.operatorName')"
              prop="operatorName"
              width="120"
            />
            <el-table-column
              :label="t('fields.operationLog.operationSource')"
              prop="operationSource"
              width="100"
            />
            <el-table-column
              :label="t('fields.operationLog.result')"
              prop="resultName"
              width="100"
            >
              <template #default="{ row }">
                <el-tag :type="row.result === 'SUCCESS' ? 'success' : 'danger'">
                  {{ row.resultName || row.result || '-' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              :label="t('fields.operationLog.statusChange')"
              width="200"
            >
              <template #default="{ row }">
                <span v-if="row.beforeStatusName && row.afterStatusName">
                  {{ row.beforeStatusName }} → {{ row.afterStatusName }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="t('fields.operationLog.remark')"
              prop="remark"
              min-width="200"
            />
          </el-table>
        </el-card>
      </template>

      <!-- 空状态显示 -->
      <template v-else-if="!loading">
        <div class="empty-state">
          <el-empty :description="t('common.noData')" />
        </div>
      </template>
    </div>

    <template #footer>
      <el-button @click="handleClose">
        {{ t('dialog.detail.closeButton') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { getVehicleRegistrationDetail } from '@/api/modules/sales/vehicleRegistration';
import type { VehicleRegistrationDetail } from '@/types/sales/vehicleRegistration';
import { formatDateTime } from '@/utils/date';

interface Props {
  visible: boolean;
  id?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
// 国际化
const { t, tc } = useModuleI18n('sales.vehicleRegistration');

// 使用标准数据字典
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS
]);

const loading = ref(false);
const detail = ref<VehicleRegistrationDetail | null>(null);

// 状态标签样式映射
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS]: {
    'pending': 'primary',
    'processing': 'warning',
    'success': 'success',
    'failed': 'danger'
  },
  [DICTIONARY_TYPES.INSURANCE_STATUS]: {
    'insured': 'success',
    'not_insured': 'info'
  }
};

// 获取状态标签类型
const getStatusTagType = (status: string, dictionaryType: string) =>
  STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';

// 标准字典转义函数
const formatRegistrationStatus = (status: string) =>
  getNameByCode(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS, status) || status;

const formatInsuranceStatus = (status: string) =>
  getNameByCode(DICTIONARY_TYPES.INSURANCE_STATUS, status) || status;

// 加载详情数据
const loadDetail = async () => {
  if (!props.id) return;

  loading.value = true;
  try {
    const response = await getVehicleRegistrationDetail(props.id);
    console.log('详情响应:', response);
    if (response.code === '200') {
      detail.value = response.result;
    } else {
      ElMessage.error(response.message || t('vehicleRegistration.messages.error.detailLoadFailed'));
    }
  } catch (error) {
    console.error('加载详情失败:', error);
    ElMessage.error(t('vehicleRegistration.messages.error.detailLoadFailed'));
  } finally {
    loading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
  detail.value = null;
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.id) {
    loadDetail();
  }
});
</script>

<style scoped>
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 20px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
  flex: 1;
}

.error-text {
  color: #f56c6c;
}

.fee-summary {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.total-fee {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 600;
}

.total-fee .label {
  color: #606266;
}

.total-fee .value.highlight {
  color: #67c23a;
  font-size: 18px;
}
</style>
