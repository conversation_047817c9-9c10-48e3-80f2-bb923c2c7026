<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ $t('settlement.title') }}</h1>

    <!-- 搜索区域卡片 -->
    <el-card class="mb-20 search-card">
      <el-form ref="filterFormRef" :model="filterForm" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item :label="$t('settlement.settlementNo')">
              <el-input
                v-model="filterForm.settlementNo"
                :placeholder="$t('settlement.placeholder.settlementNo')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.workOrderNo')">
              <el-input
                v-model="filterForm.workOrderNo"
                :placeholder="$t('settlement.placeholder.workOrderNo')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.settlementStatus')">
              <el-select v-model="filterForm.settlementStatus" clearable style="width: 100%;">
                <el-option :label="$t('common.all')" value="" />
                <el-option :label="$t('settlement.status.preSettlement')" value="pre_settlement" />
                <el-option :label="$t('settlement.status.pendingSettlement')" value="pending_settlement" />
                <el-option :label="$t('settlement.status.completed')" value="completed" />
                <el-option :label="$t('settlement.status.cancelled')" value="cancelled" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.paymentStatus')">
              <el-select v-model="filterForm.paymentStatus" clearable style="width: 100%;">
                <el-option :label="$t('common.all')" value="" />
                <el-option :label="$t('settlement.paymentStatus.pending')" value="pending" />
                <el-option :label="$t('settlement.paymentStatus.depositPaid')" value="deposit_paid" />
                <el-option :label="$t('settlement.paymentStatus.fullyPaid')" value="fully_paid" />
                <el-option :label="$t('settlement.paymentStatus.refunding')" value="refunding" />
                <el-option :label="$t('settlement.paymentStatus.refunded')" value="refunded" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.workOrderType')">
              <el-select v-model="filterForm.workOrderType" clearable style="width: 100%;">
                <el-option :label="$t('common.all')" value="" />
                <el-option :label="$t('settlement.workOrderTypes.maintenance')" value="maintenance" />
                <el-option :label="$t('settlement.workOrderTypes.repair')" value="repair" />
                <el-option :label="$t('settlement.workOrderTypes.warranty')" value="warranty" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.customerName')">
              <el-input
                v-model="filterForm.customerName"
                :placeholder="$t('settlement.placeholder.customerName')"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item :label="$t('settlement.customerPhone')">
              <el-input
                v-model="filterForm.customerPhone"
                :placeholder="$t('settlement.placeholder.customerPhone')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.vehiclePlate')">
              <el-input
                v-model="filterForm.vehiclePlate"
                :placeholder="$t('settlement.placeholder.vehiclePlate')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.technician')">
              <el-select v-model="filterForm.technician" clearable style="width: 100%;">
                <el-option :label="$t('common.all')" value="" />
                <el-option :label="$t('settlement.technicians.technicianA')" value="tech_a" />
                <el-option :label="$t('settlement.technicians.technicianB')" value="tech_b" />
                <el-option :label="$t('settlement.technicians.technicianC')" value="tech_c" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.serviceAdvisor')">
              <el-select v-model="filterForm.serviceAdvisor" clearable style="width: 100%;">
                <el-option :label="$t('common.all')" value="" />
                <el-option :label="$t('settlement.serviceAdvisors.chen')" value="advisor_chen" />
                <el-option :label="$t('settlement.serviceAdvisors.li')" value="advisor_li" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item :label="$t('settlement.createdTime')">
              <el-date-picker
                v-model="filterForm.createdAtRange"
                type="daterange"
                :range-separator="$t('common.to')"
                :start-placeholder="$t('common.startDate')"
                :end-placeholder="$t('common.endDate')"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4" class="buttons-col">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ $t('common.search') }}
            </el-button>
            <el-button @click="handleReset">
              {{ $t('common.reset') }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域卡片 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20">
        <el-col :span="12">
          <span class="filter-summary">
            {{ $t('common.total', { total: pagination.total, current: pagination.page, pages: Math.ceil(pagination.total / pagination.pageSize) }) }}
          </span>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button
            v-if="canExport"
            type="primary"
            :icon="Download"
            @click="handleExport"
          >
            {{ $t('common.export') }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表格区域卡片 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <el-table-column prop="settlementNo" :label="$t('settlement.settlementNo')" min-width="140">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              {{ row.settlementNo }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="workOrderType" :label="$t('settlement.workOrderType')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.workOrderType === 'maintenance' ? 'success' : row.workOrderType === 'repair' ? 'warning' : 'info'">
              {{ $t(`settlement.workOrderTypes.${row.workOrderType}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="workOrderNo" :label="$t('settlement.workOrderNo')" min-width="140">
          <template #default="{ row }">
            <el-button type="primary" link>{{ row.workOrderNo }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="settlementStatus" :label="$t('settlement.settlementStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.settlementStatus === 'completed' ? 'success' :
                     row.settlementStatus === 'pending_settlement' ? 'warning' :
                     row.settlementStatus === 'pre_settlement' ? 'info' : 'danger'"
            >
              {{ $t(`settlement.status.${row.settlementStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="$t('settlement.paymentStatus.label')" min-width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.paymentStatus === 'fully_paid' ? 'success' :
                     row.paymentStatus === 'deposit_paid' ? 'warning' :
                     row.paymentStatus === 'pending' ? 'info' : 'danger'"
            >
              {{ $t(`settlement.paymentStatus.${row.paymentStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" :label="$t('settlement.customerName')" min-width="100" />
        <el-table-column prop="customerPhone" :label="$t('settlement.customerPhone')" min-width="120" />
        <el-table-column prop="vehiclePlate" :label="$t('settlement.vehiclePlate')" min-width="100" />
        <el-table-column prop="vehicleModel" :label="$t('settlement.vehicleModel')" min-width="120" />
        <el-table-column prop="technician" :label="$t('settlement.technician')" min-width="100" />
        <el-table-column prop="serviceAdvisor" :label="$t('settlement.serviceAdvisor')" min-width="100" />
        <el-table-column prop="totalAmount" :label="$t('settlement.totalAmount')" min-width="100" align="right">
          <template #default="{ row }">
            {{ formatAmount(row.totalAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="payableAmount" :label="$t('settlement.payableAmount')" min-width="100" align="right">
          <template #default="{ row }">
            <span style="color: #f56c6c; font-weight: bold;">
              {{ formatAmount(row.payableAmount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" :label="$t('settlement.createdTime')" min-width="150" />
        <el-table-column :label="$t('common.actions')" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="View" link @click="handleViewDetail(row)">
              {{ $t('common.detail') }}
            </el-button>
            <el-button
              v-if="canPush(row)"
              type="success"
              :icon="Document"
              link
              @click="handlePush(row)"
            >
              {{ $t('settlement.push') }}
            </el-button>
            <el-button
              v-if="canManagePayment(row)"
              type="warning"
              :icon="Money"
              link
              @click="handlePayment(row)"
            >
              {{ $t('settlement.payment') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-card>

    <!-- 结算单详情弹窗 -->
    <SettlementDetailModal
      v-model="detailVisible"
      :settlement-id="selectedSettlementId"
    />

    <!-- 收退款管理弹窗 -->
    <PaymentManagementModal
      v-model="paymentVisible"
      :settlement-id="selectedSettlementId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Download, Search, View, Document, Money } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type { SettlementListItem, SettlementListParams } from '@/types/module'
import { getSettlementList, pushSettlement, exportSettlementData } from '@/api/modules/settlement'
import SettlementDetailModal from './components/SettlementDetailModal.vue'
import PaymentManagementModal from './components/PaymentManagementModal.vue'

const { t: $t } = useI18n()

// 表单引用
const filterFormRef = ref<FormInstance>()

// 筛选表单
const filterForm = reactive<{
  settlementNo: string
  workOrderNo: string
  settlementStatus: string
  paymentStatus: string
  workOrderType: string
  customerName: string
  customerPhone: string
  vehiclePlate: string
  technician: string
  serviceAdvisor: string
  createdAtRange: string[]
}>({
  settlementNo: '',
  workOrderNo: '',
  settlementStatus: '',
  paymentStatus: '',
  workOrderType: '',
  customerName: '',
  customerPhone: '',
  vehiclePlate: '',
  technician: '',
  serviceAdvisor: '',
  createdAtRange: []
})

// 表格数据
const tableData = ref<SettlementListItem[]>([])
const loading = ref(false)

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 弹窗状态
const detailVisible = ref(false)
const paymentVisible = ref(false)
const selectedSettlementId = ref('')

// 权限控制 - 这里可以根据实际用户角色来控制
const userRole = ref('service_advisor') // 示例角色
const canExport = computed(() => ['service_manager', 'finance_staff'].includes(userRole.value))
const canPayment = computed(() => ['service_advisor', 'finance_staff', 'service_manager'].includes(userRole.value))

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: SettlementListParams = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      settlementNo: filterForm.settlementNo || undefined,
      workOrderNo: filterForm.workOrderNo || undefined,
      settlementStatus: filterForm.settlementStatus || undefined,
      paymentStatus: filterForm.paymentStatus || undefined,
      workOrderType: filterForm.workOrderType || undefined,
      customerName: filterForm.customerName || undefined,
      customerPhone: filterForm.customerPhone || undefined,
      vehiclePlate: filterForm.vehiclePlate || undefined,
      technician: filterForm.technician || undefined,
      serviceAdvisor: filterForm.serviceAdvisor || undefined,
      createdAtStart: filterForm.createdAtRange[0] || undefined,
      createdAtEnd: filterForm.createdAtRange[1] || undefined
    }

    const response = await getSettlementList(params)
    tableData.value = response.list || []
    pagination.total = response.total
  } catch (error) {
    console.error('获取结算单列表失败:', error)
    ElMessage.error($t('settlement.messages.loadDataFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  filterFormRef.value?.resetFields()
  Object.assign(filterForm, {
    settlementNo: '',
    workOrderNo: '',
    settlementStatus: '',
    paymentStatus: '',
    workOrderType: '',
    customerName: '',
    customerPhone: '',
    vehiclePlate: '',
    technician: '',
    serviceAdvisor: '',
    createdAtRange: []
  })
  pagination.page = 1
  fetchData()
}

// 排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  // 这里可以实现排序逻辑
  console.log('排序变化:', prop, order)
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchData()
}

// 页码变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 查看详情
const handleViewDetail = (row: SettlementListItem) => {
  selectedSettlementId.value = row.id
  detailVisible.value = true
}

// 推送结算单
const handlePush = async (row: SettlementListItem) => {
  try {
    await ElMessageBox.confirm(
      $t('settlement.messages.confirmPush', { settlementNo: row.settlementNo }),
      $t('settlement.messages.pushConfirmTitle'),
      {
        confirmButtonText: $t('common.confirm'),
        cancelButtonText: $t('common.cancel'),
        type: 'warning'
      }
    )

    await pushSettlement(row.id)
    ElMessage.success($t('settlement.messages.pushSuccess'))
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('推送失败:', error)
      ElMessage.error($t('settlement.messages.pushFailed'))
    }
  }
}

// 编辑结算单
const handleEdit = (row: SettlementListItem) => {
  // 这里可以打开编辑弹窗或跳转到编辑页面
  ElMessage.info($t('settlement.messages.editNotImplemented'))
}

// 收退款管理
const handlePayment = (row: SettlementListItem) => {
  selectedSettlementId.value = row.id
  paymentVisible.value = true
}

// 导出数据
const handleExport = async () => {
  try {
    const params: SettlementListParams = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      settlementNo: filterForm.settlementNo || undefined,
      workOrderNo: filterForm.workOrderNo || undefined,
      settlementStatus: filterForm.settlementStatus || undefined,
      paymentStatus: filterForm.paymentStatus || undefined,
      workOrderType: filterForm.workOrderType || undefined,
      customerName: filterForm.customerName || undefined,
      customerPhone: filterForm.customerPhone || undefined,
      vehiclePlate: filterForm.vehiclePlate || undefined,
      technician: filterForm.technician || undefined,
      serviceAdvisor: filterForm.serviceAdvisor || undefined,
      createdAtStart: filterForm.createdAtRange[0] || undefined,
      createdAtEnd: filterForm.createdAtRange[1] || undefined
    }

    const blob = await exportSettlementData(params)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `结算管理_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success($t('settlement.messages.exportSuccess'))
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error($t('settlement.messages.exportFailed'))
  }
}

// 权限判断
const canPush = (row: SettlementListItem) => {
  return userRole.value === 'service_advisor' &&
    row.settlementStatus === 'pending_settlement' &&
    row.inspectionStatus === '质检通过' &&
    row.payableAmount > 0
}

const canEdit = (row: SettlementListItem) => {
  return userRole.value === 'service_advisor' &&
    ['pre_settlement', 'pending_settlement'].includes(row.settlementStatus)
}

// 收退款权限判断
const canManagePayment = (row: SettlementListItem) => {
  // 只有已完成或待结算的订单才能进行收退款管理
  return ['pending_settlement', 'completed'].includes(row.settlementStatus) &&
    ['service_advisor', 'finance_staff', 'service_manager'].includes(userRole.value)
}

// 状态颜色
const getWorkOrderTypeColor = (type: string) => {
  const colors = {
    maintenance: '#52c41a', // 绿色
    repair: '#1890ff', // 蓝色
    warranty: '#fa8c16' // 橙色
  }
  return colors[type] || '#d9d9d9'
}

const getSettlementStatusColor = (status: string) => {
  const colors = {
    pre_settlement: '#fa8c16', // 橙色
    pending_settlement: '#1890ff', // 蓝色
    completed: '#52c41a', // 绿色
    cancelled: '#ff4d4f' // 红色
  }
  return colors[status] || '#d9d9d9'
}

const getPaymentStatusColor = (status: string) => {
  const colors = {
    pending: '#fa8c16', // 橙色
    deposit_paid: '#1890ff', // 蓝色
    fully_paid: '#52c41a', // 绿色
    refunding: '#ff4d4f', // 红色
    refunded: '#ff4d4f' // 红色
  }
  return colors[status] || '#d9d9d9'
}

// 金额格式化
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="scss">
/* 页面容器样式 */
.page-container {
  padding: 20px;
}

/* 页面标题样式 */
.page-title {
  margin-bottom: 20px;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 20px;
}

/* 操作卡片样式 */
.operation-card {
  margin-bottom: 20px;
  .text-right {
    text-align: right;
  }
  .filter-summary {
    font-size: 14px;
    color: #606266;
  }
}

/* 搜索表单样式 */
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

/* 按钮列样式 */
.buttons-col {
  text-align: right;
}

/* 表格卡片样式 */
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td {
      white-space: nowrap;
    }
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 工具类 */
.mb-20 {
  margin-bottom: 20px;
}

.text-right {
  text-align: right;
}

/* 响应式隐藏 */
@media (max-width: 768px) {
  .hidden-sm-and-down {
    display: none !important;
  }
}
</style>
