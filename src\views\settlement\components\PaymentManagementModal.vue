<template>
  <el-dialog
    v-model="visible"
    :title="$t('settlement.paymentManagement')"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading" class="payment-management">
      <!-- 结算信息区 -->
      <el-card class="settlement-info-card">
        <template #header>
          <h3>{{ $t('settlement.settlementInfo') }}</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <label>{{ $t('settlement.settlementNo') }}:</label>
              <span class="value-bold">{{ paymentData.settlementInfo.settlementNo }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>{{ $t('settlement.workOrderNo') }}:</label>
              <el-link type="primary">{{ paymentData.settlementInfo.workOrderNo }}</el-link>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>{{ $t('settlement.settlementStatus') }}:</label>
              <el-tag color="#1890ff">{{ $t(`settlement.status.${paymentData.settlementInfo.settlementStatus}`) }}</el-tag>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>{{ $t('settlement.paymentStatus.label') }}:</label>
              <el-tag color="#fa8c16">{{ $t(`settlement.paymentStatus.${paymentData.settlementInfo.paymentStatus}`) }}</el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item payable-amount">
              <label>{{ $t('settlement.payableAmount') }}:</label>
              <span class="red-text value-bold large-text">{{ formatAmount(paymentData.settlementInfo.payableAmount) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>{{ $t('settlement.paidAmount') }}:</label>
              <span>{{ formatAmount(paymentData.settlementInfo.paidAmount) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户信息区 -->
      <el-card class="customer-info-card">
        <template #header>
          <h3>{{ $t('settlement.customerInfo') }}</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <label>{{ $t('settlement.customerName') }}:</label>
              <span>{{ paymentData.customerInfo.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>{{ $t('settlement.customerPhone') }}:</label>
              <span>{{ paymentData.customerInfo.customerPhone }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>{{ $t('settlement.vehiclePlate') }}:</label>
              <span>{{ paymentData.customerInfo.vehiclePlate }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>{{ $t('settlement.vehicleModel') }}:</label>
              <span>{{ paymentData.customerInfo.vehicleModel }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 功能操作区 -->
      <div class="action-section">
        <el-button
          type="primary"
          :icon="Plus"
          @click="togglePaymentForm"
        >
          {{ $t('settlement.addPaymentRecord') }}
        </el-button>
      </div>

      <!-- 动态新增收退款表单区 -->
      <el-card v-if="showPaymentForm" class="payment-form-card">
        <template #header>
          <h3>{{ $t('settlement.addPaymentInfo') }}</h3>
        </template>
        <el-form
          ref="paymentFormRef"
          :model="paymentForm"
          :rules="paymentFormRules"
          label-position="top"
        >
          <!-- 第一行字段 -->
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="$t('settlement.businessType')" prop="businessType">
                <el-select v-model="paymentForm.businessType" style="width: 120px">
                  <el-option :label="$t('settlement.businessTypes.receive')" value="收款" />
                  <el-option :label="$t('settlement.businessTypes.refund')" value="退款" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('settlement.transactionNo')" prop="transactionNo">
                <el-input
                  v-model="paymentForm.transactionNo"
                  :placeholder="$t('settlement.placeholder.transactionNo')"
                  style="width: 200px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('settlement.paymentMethod')" prop="paymentMethod">
                <el-select v-model="paymentForm.paymentMethod" style="width: 150px">
                  <el-option :label="$t('settlement.paymentMethods.cash')" value="cash" />
                  <el-option :label="$t('settlement.paymentMethods.pos')" value="pos" />
                  <el-option :label="$t('settlement.paymentMethods.bankTransfer')" value="bank_transfer" />
                  <el-option :label="$t('settlement.paymentMethods.wechat')" value="wechat" />
                  <el-option :label="$t('settlement.paymentMethods.alipay')" value="alipay" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button
                type="danger"
                :icon="Delete"
                @click="hidePaymentForm"
              >
                {{ $t('common.delete') }}
              </el-button>
            </el-col>
          </el-row>

          <!-- 第二行字段 -->
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="$t('settlement.amount')" prop="amount">
                <el-input-number
                  v-model="paymentForm.amount"
                  :precision="2"
                  :min="0.01"
                  :max="*********"
                  style="width: 150px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('settlement.paymentType')" prop="paymentType">
                <el-select v-model="paymentForm.paymentType" style="width: 120px">
                  <el-option :label="$t('settlement.paymentTypes.tail')" value="尾款" />
                  <el-option :label="$t('settlement.paymentTypes.deposit')" value="定金" />
                  <el-option :label="$t('settlement.paymentTypes.full')" value="全款" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('settlement.paymentTime')" prop="paymentTime">
                <el-date-picker
                  v-model="paymentForm.paymentTime"
                  type="datetime"
                  :placeholder="$t('settlement.placeholder.paymentTime')"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm"
                  style="width: 200px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('settlement.remarks')" prop="remarks">
                <el-input
                  v-model="paymentForm.remarks"
                  :placeholder="$t('settlement.placeholder.remarks')"
                  :maxlength="100"
                  style="width: 200px"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 表单操作按钮 -->
          <el-row>
            <el-col :span="24">
              <div class="form-actions">
                <el-button
                  type="success"
                  @click="handleAddPayment"
                >
                  {{ $t('settlement.confirmAdd') }}
                </el-button>
                <el-button @click="handleCancelAdd">
                  {{ $t('common.cancel') }}
                </el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 收退款记录区 -->
      <el-card class="payment-records-card">
        <template #header>
          <h3>{{ $t('settlement.paymentRecords') }}</h3>
        </template>

        <!-- 空状态 -->
        <div v-if="!paymentData.paymentRecords.length" class="empty-state">
          <el-empty :description="$t('settlement.noPaymentRecords')" />
        </div>

        <!-- 记录表格 -->
        <el-table v-else :data="paymentData.paymentRecords" border>
          <el-table-column
            prop="paymentNo"
            :label="$t('settlement.paymentNo')"
            width="140"
          />
          <el-table-column
            prop="businessType"
            :label="$t('settlement.businessType')"
            width="80"
            align="center"
          >
            <template #default="{ row }">
              <el-tag :color="row.businessType === '收款' ? '#52c41a' : '#ff4d4f'">
                {{ row.businessType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="transactionNo"
            :label="$t('settlement.transactionNo')"
            width="120"
          />
          <el-table-column
            prop="paymentMethod"
            :label="$t('settlement.paymentMethod')"
            width="100"
            align="center"
          >
            <template #default="{ row }">
              {{ $t(`settlement.paymentMethods.${row.paymentMethod}`) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="amount"
            :label="$t('settlement.amount')"
            width="100"
            align="right"
          >
            <template #default="{ row }">
              <span :class="{ 'red-text': row.businessType === '退款' }">
                {{ row.businessType === '退款' ? '-' : '' }}{{ formatAmount(row.amount) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="paymentType"
            :label="$t('settlement.paymentType')"
            width="80"
            align="center"
          />
          <el-table-column
            prop="paymentTime"
            :label="$t('settlement.paymentTime')"
            width="150"
            align="center"
          />
          <el-table-column
            prop="remarks"
            :label="$t('settlement.remarks')"
            width="150"
          />
          <el-table-column
            :label="$t('common.actions')"
            width="80"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="danger"
                size="small"
                link
                @click="handleDeletePayment(row)"
              >
                {{ $t('common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSave">
          {{ $t('common.save') }}
        </el-button>
        <el-button @click="handleClose">
          {{ $t('common.cancel') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type { PaymentManagementData, PaymentForm, PaymentRecord } from '@/types/module'
import {
  getPaymentManagementData,
  addPaymentRecord,
  deletePaymentRecord,
  savePaymentRecords
} from '@/api/modules/settlement'

const { t: $t } = useI18n()

interface Props {
  modelValue: boolean
  settlementId: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单引用
const paymentFormRef = ref<FormInstance>()

// 数据加载状态
const loading = ref(false)

// 收退款管理数据
const paymentData = ref<PaymentManagementData>({
  settlementInfo: {
    settlementNo: '',
    workOrderNo: '',
    settlementStatus: '',
    paymentStatus: '',
    payableAmount: 0,
    paidAmount: 0
  },
  customerInfo: {
    customerName: '',
    customerPhone: '',
    vehiclePlate: '',
    vehicleModel: ''
  },
  paymentRecords: []
})

// 表单显示状态
const showPaymentForm = ref(false)

// 收退款表单
const paymentForm = reactive<PaymentForm>({
  businessType: '收款',
  transactionNo: '',
  paymentMethod: 'cash',
  amount: null,
  paymentType: '全款',
  paymentTime: '',
  remarks: ''
})

// 表单验证规则
const paymentFormRules = computed<FormRules>(() => ({
  businessType: [
    { required: true, message: $t('settlement.validation.paymentTypeRequired'), trigger: 'change' }
  ],
  transactionNo: [
    { required: true, message: $t('settlement.validation.transactionNoRequired'), trigger: 'blur' },
    { min: 1, max: 50, message: $t('settlement.validation.transactionNoLength'), trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: $t('settlement.validation.paymentMethodRequired'), trigger: 'change' }
  ],
  amount: [
    { required: true, message: $t('settlement.validation.amountRequired'), trigger: 'blur' },
    { type: 'number', min: 0.01, message: $t('settlement.validation.amountPositive'), trigger: 'blur' }
  ],
  paymentType: [
    { required: true, message: $t('settlement.validation.paymentTypeRequired'), trigger: 'change' }
  ],
  paymentTime: [
    { required: true, message: $t('settlement.validation.paymentDateRequired'), trigger: 'change' }
  ]
}))

// 获取收退款管理数据
const fetchPaymentData = async () => {
  if (!props.settlementId) return

  loading.value = true
  try {
    const response = await getPaymentManagementData(props.settlementId)
    paymentData.value = response
  } catch (error) {
    console.error('获取收退款管理数据失败:', error)
    ElMessage.error($t('settlement.messages.fetchPaymentRecordsFailed'))
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && props.settlementId) {
      fetchPaymentData()
    }
  },
  { immediate: true }
)

// 显示/隐藏收退款表单
const togglePaymentForm = () => {
  showPaymentForm.value = !showPaymentForm.value
  if (showPaymentForm.value) {
    resetPaymentForm()
  }
}

const hidePaymentForm = () => {
  showPaymentForm.value = false
}

// 重置收退款表单
const resetPaymentForm = () => {
  Object.assign(paymentForm, {
    businessType: '收款',
    transactionNo: '',
    paymentMethod: 'cash',
    amount: null,
    paymentType: '全款',
    paymentTime: new Date().toLocaleString('sv-SE', { timeZoneName: 'short' }).slice(0, 16),
    remarks: ''
  })
  paymentFormRef.value?.clearValidate()
}

// 添加收退款记录
const handleAddPayment = async () => {
  if (!paymentFormRef.value) return

  try {
    await paymentFormRef.value.validate()

    // 检查流水号唯一性
    const existingRecord = paymentData.value.paymentRecords.find(
      record => record.transactionNo === paymentForm.transactionNo
    )
    if (existingRecord) {
      ElMessage.error($t('settlement.validation.transactionNoExists'))
      return
    }

    const newRecord = await addPaymentRecord(props.settlementId, paymentForm)
    paymentData.value.paymentRecords.push(newRecord)

    ElMessage.success($t('settlement.payment.addSuccess'))
    showPaymentForm.value = false
    resetPaymentForm()
  } catch (error) {
    if (error !== false) { // 表单验证失败时不显示错误消息
      console.error('添加收退款记录失败:', error)
      ElMessage.error($t('settlement.messages.addPaymentRecordFailed'))
    }
  }
}

// 取消添加
const handleCancelAdd = () => {
  showPaymentForm.value = false
  resetPaymentForm()
}

// 删除收退款记录
const handleDeletePayment = async (record: PaymentRecord) => {
  try {
    await ElMessageBox.confirm(
      $t('settlement.payment.confirmDelete', { paymentNo: record.paymentNo }),
      $t('settlement.messages.confirmDelete'),
      {
        confirmButtonText: $t('common.confirm'),
        cancelButtonText: $t('common.cancel'),
        type: 'warning'
      }
    )

    await deletePaymentRecord(record.id)
    paymentData.value.paymentRecords = paymentData.value.paymentRecords.filter(
      item => item.id !== record.id
    )

    ElMessage.success($t('settlement.payment.deleteSuccess'))
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除收退款记录失败:', error)
      ElMessage.error($t('settlement.messages.deletePaymentRecordFailed'))
    }
  }
}

// 保存收退款记录
const handleSave = async () => {
  try {
    await savePaymentRecords(props.settlementId)
    ElMessage.success($t('settlement.messages.saveSuccess'))
    visible.value = false
  } catch (error) {
    console.error('保存收退款记录失败:', error)
    ElMessage.error($t('settlement.messages.saveFailed'))
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 金额格式化
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
</script>

<style scoped>
.payment-management {
  max-height: 650px;
  overflow-y: auto;
}

.settlement-info-card,
.customer-info-card,
.payment-form-card,
.payment-records-card {
  margin-bottom: 20px;
}

.settlement-info-card:last-child,
.customer-info-card:last-child,
.payment-form-card:last-child,
.payment-records-card:last-child {
  margin-bottom: 0;
}

.action-section {
  text-align: center;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-item label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.value-bold {
  font-weight: bold;
}

.large-text {
  font-size: 18px;
}

.red-text {
  color: #ff4d4f;
}

.payable-amount {
  border: 1px solid #ff4d4f;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff2f0;
}

.form-actions {
  text-align: center;
  margin-top: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: center;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.payment-form-card .el-card__body) {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
</style>
