<template>
  <el-dialog
    v-model="visible"
    :title="$t('settlement.detailTitle')"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading" class="settlement-detail">
      <!-- 结算基础信息区 -->
      <el-card class="info-card">
        <template #header>
          <h3>{{ $t('settlement.basicInfo') }}</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>{{ $t('settlement.settlementNo') }}:</label>
              <span class="value-bold">{{ detailData.settlementNo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>{{ $t('settlement.workOrderNo') }}:</label>
              <el-link type="primary">{{ detailData.workOrderNo }}</el-link>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>{{ $t('settlement.settlementStatus') }}:</label>
              <el-tag :color="getSettlementStatusColor(detailData.settlementStatus)">
                {{ $t(`settlement.status.${detailData.settlementStatus}`) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>{{ $t('settlement.paymentStatus.label') }}:</label>
              <el-tag :color="getPaymentStatusColor(detailData.paymentStatus)">
                {{ $t(`settlement.paymentStatus.${detailData.paymentStatus}`) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>{{ $t('settlement.createdTime') }}:</label>
              <span>{{ detailData.createdAt }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>{{ $t('settlement.serviceAdvisor') }}:</label>
              <span>{{ detailData.serviceAdvisor }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户信息和车辆信息区 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <h3>{{ $t('settlement.customerInfo') }}</h3>
            </template>
            <div class="info-item">
              <label>{{ $t('settlement.customerName') }}:</label>
              <span>{{ detailData.customerName }}</span>
            </div>
            <div class="info-item">
              <label>{{ $t('settlement.customerPhone') }}:</label>
              <span>{{ detailData.customerPhone }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <h3>{{ $t('settlement.vehicleInfo') }}</h3>
            </template>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ $t('settlement.vehiclePlate') }}:</label>
                  <span>{{ detailData.vehiclePlate }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ $t('settlement.vin') }}:</label>
                  <span>{{ detailData.vin }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ $t('settlement.vehicleModel') }}:</label>
                  <span>{{ detailData.vehicleModel }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ $t('settlement.vehicleConfig') }}:</label>
                  <span>{{ detailData.vehicleConfig }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ $t('settlement.vehicleColor') }}:</label>
                  <span>{{ detailData.vehicleColor }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ $t('settlement.vehicleAge') }}:</label>
                  <span>{{ detailData.vehicleAge }}</span>
                </div>
              </el-col>
            </el-row>
            <div class="info-item">
              <label>{{ $t('settlement.mileage') }}:</label>
              <span>{{ detailData.mileage }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 费用明细区 -->
      <el-card class="cost-detail-card">
        <template #header>
          <h3>{{ $t('settlement.costDetail') }}</h3>
        </template>

        <!-- 服务包信息 -->
        <div v-if="detailData.servicePackage" class="service-package">
          <h4>{{ $t('settlement.servicePackageInfo') }}</h4>
          <el-table :data="[detailData.servicePackage]" border>
            <el-table-column prop="packageCode" :label="$t('settlement.packageCode')" width="120" />
            <el-table-column prop="packageName" :label="$t('settlement.packageName')" width="300" />
          </el-table>
        </div>

        <!-- 工时费用明细 -->
        <div class="labor-cost-section">
          <h4>{{ $t('settlement.laborCostDetail') }}</h4>
          <el-table :data="detailData.laborItems" border>
            <el-table-column prop="laborCode" :label="$t('settlement.laborCode')" width="100" />
            <el-table-column prop="laborName" :label="$t('settlement.laborName')" width="200" />
            <el-table-column prop="standardHours" :label="$t('settlement.standardHours')" width="80" align="right">
              <template #default="{ row }">{{ row.standardHours }}h</template>
            </el-table-column>
            <el-table-column prop="unitPrice" :label="$t('settlement.unitPrice')" width="100" align="right">
              <template #default="{ row }">{{ formatAmount(row.unitPrice) }}</template>
            </el-table-column>
            <el-table-column prop="laborType" :label="$t('settlement.laborType')" width="100" align="center" />
            <el-table-column prop="subtotal" :label="$t('settlement.subtotal')" width="100" align="right">
              <template #default="{ row }">{{ formatAmount(row.subtotal) }}</template>
            </el-table-column>
            <el-table-column prop="receivableAmount" :label="$t('settlement.receivable')" width="100" align="right">
              <template #default="{ row }">
                <span :class="{ 'green-text': row.isPackageItem, 'red-text': !row.isPackageItem }">
                  {{ formatAmount(row.receivableAmount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" :label="$t('settlement.remarks')" width="150">
              <template #default="{ row }">
                <span :class="{ 'green-text': row.isPackageItem, 'red-text': !row.isPackageItem }">
                  {{ row.remarks }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          <div class="cost-summary">
            <span>{{ $t('settlement.laborTotalAmount') }}: {{ formatAmount(detailData.laborTotalAmount) }}</span>
            <span class="green-text">{{ $t('settlement.laborReceivableAmount') }}: {{ formatAmount(detailData.laborReceivableAmount) }}</span>
          </div>
        </div>

        <!-- 零件费用明细 -->
        <div class="part-cost-section">
          <h4>{{ $t('settlement.partCostDetail') }}</h4>
          <el-table :data="detailData.partItems" border>
            <el-table-column prop="partCode" :label="$t('settlement.partCode')" width="100" />
            <el-table-column prop="partName" :label="$t('settlement.partName')" width="200" />
            <el-table-column prop="quantity" :label="$t('settlement.quantity')" width="80" align="right" />
            <el-table-column prop="unitPrice" :label="$t('settlement.unitPrice')" width="100" align="right">
              <template #default="{ row }">{{ formatAmount(row.unitPrice) }}</template>
            </el-table-column>
            <el-table-column prop="partType" :label="$t('settlement.partType')" width="100" align="center" />
            <el-table-column prop="subtotal" :label="$t('settlement.subtotal')" width="100" align="right">
              <template #default="{ row }">{{ formatAmount(row.subtotal) }}</template>
            </el-table-column>
            <el-table-column prop="receivableAmount" :label="$t('settlement.receivable')" width="100" align="right">
              <template #default="{ row }">
                <span :class="{ 'green-text': row.isPackageItem, 'red-text': !row.isPackageItem }">
                  {{ formatAmount(row.receivableAmount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" :label="$t('settlement.remarks')" width="150">
              <template #default="{ row }">
                <span :class="{ 'green-text': row.isPackageItem, 'red-text': !row.isPackageItem }">
                  {{ row.remarks }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          <div class="cost-summary">
            <span>{{ $t('settlement.partTotalAmount') }}: {{ formatAmount(detailData.partTotalAmount) }}</span>
            <span class="red-text">{{ $t('settlement.partReceivableAmount') }}: {{ formatAmount(detailData.partReceivableAmount) }}</span>
          </div>
        </div>

        <!-- 费用汇总 -->
        <div class="total-summary">
          <div class="summary-item">
            <label>{{ $t('settlement.warrantyAmount') }}:</label>
            <span>{{ formatAmount(detailData.warrantyAmount) }}</span>
          </div>
          <div class="summary-item">
            <label>{{ $t('settlement.totalAmount') }}:</label>
            <span class="value-bold">{{ formatAmount(detailData.totalAmount) }}</span>
          </div>
          <div class="summary-item">
            <label>{{ $t('settlement.packageRightsDeduction') }}:</label>
            <span class="green-text">{{ formatAmount(detailData.packageRightsDeduction) }}</span>
          </div>
          <div class="summary-item">
            <label>{{ $t('settlement.receivableTotal') }}:</label>
            <span class="red-text value-bold">{{ formatAmount(detailData.receivableTotal) }}</span>
          </div>
          <div class="summary-item payable-amount">
            <label>{{ $t('settlement.payableAmount') }}:</label>
            <span class="red-text value-bold large-text">{{ formatAmount(detailData.payableAmount) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 支付信息区 -->
      <el-card class="payment-info-card">
        <template #header>
          <h3>{{ $t('settlement.paymentInfo') }}</h3>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>{{ $t('settlement.totalAmount') }}:</label>
              <span>{{ formatAmount(detailData.totalAmount) }}</span>
            </div>
            <div class="info-item">
              <label>{{ $t('settlement.paidAmount') }}:</label>
              <span>{{ formatAmount(detailData.paidAmount) }}</span>
            </div>
            <div class="info-item">
              <label>{{ $t('settlement.packageRightsDeduction') }}:</label>
              <span class="green-text">{{ formatAmount(detailData.packageRightsDeduction) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>{{ $t('settlement.discountAmount') }}:</label>
              <span>{{ formatAmount(detailData.discountAmount) }}</span>
            </div>
            <div class="info-item">
              <label>{{ $t('settlement.payableAmount') }}:</label>
              <span class="red-text value-bold">{{ formatAmount(detailData.payableAmount) }}</span>
            </div>
            <div class="info-item">
              <label>{{ $t('settlement.remarks') }}:</label>
              <span>{{ detailData.remarks }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 操作日志区 -->
      <el-card class="operation-log-card">
        <template #header>
          <h3>{{ $t('settlement.operationLog') }}</h3>
        </template>
        <el-table :data="detailData.operationLogs" border>
          <el-table-column prop="operation" :label="$t('settlement.operation')" width="300" />
          <el-table-column prop="operator" :label="$t('settlement.operator')" width="100" align="center" />
          <el-table-column prop="operatedAt" :label="$t('settlement.operatedAt')" width="150" align="center" />
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('common.close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { SettlementDetail } from '@/types/module'
import { getSettlementDetail } from '@/api/modules/settlement'

const { t: $t } = useI18n()

interface Props {
  modelValue: boolean
  settlementId: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 数据加载状态
const loading = ref(false)

// 详情数据
const detailData = ref<SettlementDetail>({
  id: '',
  settlementNo: '',
  workOrderNo: '',
  workOrderType: 'maintenance',
  settlementStatus: 'pre_settlement',
  paymentStatus: 'pending',
  createdAt: '',
  serviceAdvisor: '',
  customerName: '',
  customerPhone: '',
  vehiclePlate: '',
  vin: '',
  vehicleModel: '',
  vehicleConfig: '',
  vehicleColor: '',
  vehicleAge: '',
  mileage: '',
  servicePackage: undefined,
  laborItems: [],
  partItems: [],
  laborTotalAmount: 0,
  laborReceivableAmount: 0,
  partTotalAmount: 0,
  partReceivableAmount: 0,
  warrantyAmount: 0,
  totalAmount: 0,
  packageRightsDeduction: 0,
  receivableTotal: 0,
  payableAmount: 0,
  paidAmount: 0,
  discountAmount: 0,
  remarks: '',
  operationLogs: []
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.settlementId) return

  loading.value = true
  try {
    const response = await getSettlementDetail(props.settlementId)
    detailData.value = response
  } catch (error) {
    console.error('获取结算单详情失败:', error)
    ElMessage.error($t('settlement.messages.fetchSettlementDetailFailed'))
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && props.settlementId) {
      fetchDetail()
    }
  },
  { immediate: true }
)

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 状态颜色
const getSettlementStatusColor = (status: string) => {
  const colors = {
    pre_settlement: '#fa8c16',
    pending_settlement: '#1890ff',
    completed: '#52c41a',
    cancelled: '#ff4d4f'
  }
  return colors[status] || '#d9d9d9'
}

const getPaymentStatusColor = (status: string) => {
  const colors = {
    pending: '#fa8c16',
    deposit_paid: '#1890ff',
    fully_paid: '#52c41a',
    refunding: '#ff4d4f',
    refunded: '#ff4d4f'
  }
  return colors[status] || '#d9d9d9'
}

// 金额格式化
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
</script>

<style scoped>
.settlement-detail {
  max-height: 700px;
  overflow-y: auto;
}

.info-card,
.cost-detail-card,
.payment-info-card,
.operation-log-card {
  margin-bottom: 20px;
}

.info-card:last-child,
.cost-detail-card:last-child,
.payment-info-card:last-child,
.operation-log-card:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-item label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.value-bold {
  font-weight: bold;
}

.large-text {
  font-size: 18px;
}

.green-text {
  color: #52c41a;
}

.red-text {
  color: #ff4d4f;
}

.service-package,
.labor-cost-section,
.part-cost-section {
  margin-bottom: 20px;
}

.service-package h4,
.labor-cost-section h4,
.part-cost-section h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.cost-summary {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.total-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.summary-item label {
  font-weight: 500;
  color: #666;
  min-width: 120px;
  text-align: right;
}

.payable-amount {
  border-top: 1px solid #e8e8e8;
  padding-top: 10px;
  margin-top: 10px;
}

.dialog-footer {
  text-align: center;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>
