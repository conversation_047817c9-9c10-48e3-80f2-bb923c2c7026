<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('department.title') }}</h1>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchParams" inline>
        <el-form-item :label="t('department.departmentName')">
          <el-input
            v-model="searchParams.departmentName"
            :placeholder="t('department.enterDepartmentName')"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item :label="t('department.departmentStatus')">
          <el-select
            v-model="searchParams.departmentStatus"
            :placeholder="t('department.selectDepartmentStatus')"
            clearable
            style="width: 200px"
          >
            <el-option :label="tc('all')" value="" />
            <el-option
              v-for="option in departmentStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ tc('search') }}</el-button>
          <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏和表格 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-actions">
          <el-button
            v-permission="'system:department:create'"
            type="primary"
            @click="handleAdd"
          >
            {{ t('department.addDepartment') }}
          </el-button>
          <el-button @click="expandAll">{{ t('store.expandAll') }}</el-button>
          <el-button @click="collapseAll">{{ t('store.collapseAll') }}</el-button>
        </div>
      </div>

      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="defaultExpandAll"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="index" :label="tc('index')" width="80" />
        <el-table-column prop="departmentName" :label="t('department.departmentName')" min-width="150" />
        <el-table-column prop="departmentCode" :label="t('department.departmentCode')" width="120" />
        <el-table-column prop="departmentType" :label="t('department.departmentType')" width="120">
          <template #default="scope">
            {{ getDepartmentTypeText(scope.row.departmentType) }}
          </template>
        </el-table-column>
        <el-table-column prop="departmentStatus" :label="t('department.departmentStatus')" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.departmentStatus === 'normal' ? 'success' : 'danger'">
              {{ getDepartmentStatusText(scope.row.departmentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="departmentHead" :label="t('department.departmentHead')" width="100" />
        <el-table-column :label="tc('operations')" fixed="right" width="240">
          <template #default="scope">
            <el-button type="info" link @click="handleView(scope.row)">{{ tc('view') }}</el-button>
            <el-button
              v-permission="'system:department:update'"
              type="primary"
              link
              @click="handleEdit(scope.row)"
            >
              {{ tc('edit') }}
            </el-button>
            <el-button
              v-permission="'system:department:delete'"
              type="danger"
              link
              @click="handleDelete(scope.row)"
            >
              {{ tc('delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-if="pagination.total > 0"
        class="mt-20"
        :current-page="pagination.current"
        :page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 新增/编辑/查看弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isView ? tc('view') + t('department.title') : (isEdit ? t('department.editDepartment') : t('department.addDepartment'))"
      width="600px"
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <el-form-item :label="t('department.departmentName')" prop="departmentName">
          <el-input v-model="formData.departmentName" :placeholder="t('department.enterDepartmentName')" :disabled="isView" />
        </el-form-item>
        <el-form-item :label="t('department.departmentCode')" prop="departmentCode">
          <el-input v-model="formData.departmentCode" :placeholder="t('department.enterDepartmentCode')" :disabled="isEdit || isView"/>
        </el-form-item>
        <el-form-item :label="t('department.parentDepartment')" prop="parentId">
          <el-tree-select
            v-model="formData.parentId"
            :data="departmentTree"
            :render-after-expand="false"
            :placeholder="t('department.selectParentDepartment')"
            check-strictly
            clearable
            node-key="id"
            :props="{ label: 'departmentName', value: 'id' }"
            :disabled="isView"
          />
        </el-form-item>
        <el-form-item :label="t('department.departmentType')" prop="departmentType">
          <el-select v-model="formData.departmentType" :placeholder="t('department.selectDepartmentType')" :disabled="isView">
            <el-option :label="t('department.departmentTypeBusiness')" value="business" />
            <el-option :label="t('department.departmentTypeSupport')" value="support" />
            <el-option :label="t('department.departmentTypeManagement')" value="management" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('department.departmentStatus')" prop="departmentStatus">
          <el-radio-group v-model="formData.departmentStatus" :disabled="isView">
            <el-radio value="normal">{{ t('department.departmentStatusNormal') }}</el-radio>
            <el-radio value="disabled">{{ t('department.departmentStatusDisabled') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('department.departmentHead')" prop="departmentHead">
          <el-input v-model="formData.departmentHead" :placeholder="t('department.selectDepartmentHead')" :disabled="isView" />
        </el-form-item>
        <el-form-item :label="t('department.description')" prop="description">
          <el-input
            v-model="formData.description"
            :placeholder="t('department.enterDescription')"
            type="textarea"
            :rows="3"
            :disabled="isView"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ isView ? tc('close') : tc('cancel') }}</el-button>
          <el-button v-if="!isView" type="primary" @click="handleSubmit">{{ tc('confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { Department, CreateDepartmentRequest, UpdateDepartmentRequest, DepartmentQueryParams, SelectOption } from '@/types/permission'
import { getDepartmentList, addDepartment, updateDepartment, deleteDepartment, getDepartmentDetail, getDictionary } from '@/api/modules/permission'

// 国际化设置
const { t, tc } = useModuleI18n('base')

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const isView = ref(false)
const defaultExpandAll = ref(false)
const tableRef = ref()
const formRef = ref()

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
})

// 搜索参数
const searchParams = reactive({
  departmentName: '',
  departmentStatus: '',
})

// 表格数据
const tableData = ref<Department[]>([])
const departmentTree = ref<Department[]>([])
const departmentStatusOptions = ref<SelectOption[]>([])

// 表单数据
const formData = reactive({
  id: '',
  departmentName: '',
  departmentCode: '',
  parentId: '',
  departmentType: '',
  departmentStatus: 'normal',
  departmentHead: '',
  description: ''
})

// 表单验证规则
const formRules = {
  departmentName: [{ required: true, message: t('department.departmentNameRequired'), trigger: 'blur' }],
  departmentCode: [{ required: true, message: t('department.departmentCodeRequired'), trigger: 'blur' }],
  departmentType: [{ required: true, message: t('department.departmentTypeRequired'), trigger: 'change' }],
  departmentStatus: [{ required: true, message: t('department.departmentStatusRequired'), trigger: 'change' }]
}

// 状态文本映射
const getDepartmentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'normal': t('department.departmentStatusNormal'),
    'disabled': t('department.departmentStatusDisabled')
  }
  return statusMap[status] || status
}

const getDepartmentTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'business': t('department.departmentTypeBusiness'),
    'support': t('department.departmentTypeSupport'),
    'management': t('department.departmentTypeManagement')
  }
  return typeMap[type] || type
}

// 数据加载
const loadData = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const queryParams: DepartmentQueryParams = {
      current: pagination.current,
      size: pagination.size,
      departmentName: searchParams.departmentName || undefined,
      departmentStatus: searchParams.departmentStatus ? (searchParams.departmentStatus as 'normal' | 'disabled') : undefined
    }

    // 使用API获取部门数据
    const response = await getDepartmentList(queryParams)
    if (response.code == 200) {
      const allDepts = response.result.records

      // 检查后端是否已经返回了树形结构数据
      const hasTreeStructure = allDepts.some(dept => dept.children !== undefined);

      if (hasTreeStructure) {
        // 后端已经返回树形结构，直接使用
        tableData.value = allDepts;

        // 递归获取所有部门节点（包括子节点）用于父部门选择器
        const flattenDepts = (depts: Department[]): Department[] => {
          const result: Department[] = [];
          depts.forEach(dept => {
            result.push(dept);
            if (dept.children && dept.children.length > 0) {
              result.push(...flattenDepts(dept.children));
            }
          });
          return result;
        };
        departmentTree.value = flattenDepts(allDepts);
      } else {
        // 后端返回平铺数据，需要前端构建树形结构
        tableData.value = allDepts
        departmentTree.value = allDepts
      }

      // 使用真实的分页数据
      pagination.total = response.result.total
      pagination.current = response.result.current
      pagination.size = response.result.size
    } else {
      ElMessage.error(response.message || tc('loadFailed'))
    }

  } catch (error) {
    console.error('加载部门数据失败:', error)
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  searchParams.departmentName = ''
  searchParams.departmentStatus = ''
  pagination.current = 1
  loadData()
}

const handleAdd = () => {
  isEdit.value = false
  isView.value = false
  Object.assign(formData, {
    id: '',
    departmentName: '',
    departmentCode: '',
    parentId: '',
    departmentType: '',
    departmentStatus: 'normal',
    departmentHead: '',
    description: ''
  })
  dialogVisible.value = true
}

const handleEdit = (row: Department) => {
  isEdit.value = true
  isView.value = false
  // 使用 nextTick 确保 formRef 存在
  nextTick(() => {
    Object.assign(formData, row)
  })
  dialogVisible.value = true
}

const handleView = async (row: Department) => {
  try {
    loading.value = true
    const response = await getDepartmentDetail(row.id)

    if (response.code == 200) {
      isEdit.value = false
      isView.value = true
      Object.assign(formData, response.result)
      dialogVisible.value = true
    } else {
      ElMessage.error(response.message || tc('loadFailed'))
    }
  } catch (error) {
    console.error('获取部门详情失败:', error)
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row: Department) => {
  try {
    // 使用 i18n 的插值功能
    await ElMessageBox.confirm(
      tc('confirmDelete', { item: row.departmentName }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    )

    // 使用API删除部门
    const response = await deleteDepartment(row.id)

    if (response.code == 200) {
      ElMessage.success(tc('success'))
      loadData()
    } else {
      ElMessage.error(response.message || tc('deleteFailed'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除部门失败:', error)
      ElMessage.error(tc('deleteFailed'))
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        let response

        if (isEdit.value) {
          // 更新部门 - ID放入参数实体中
          const updateData: UpdateDepartmentRequest = {
            id: formData.id,
            departmentName: formData.departmentName,
            departmentCode: formData.departmentCode,
            parentId: formData.parentId || undefined,
            departmentType: formData.departmentType as 'business' | 'support' | 'management',
            departmentStatus: formData.departmentStatus as 'normal' | 'disabled',
            departmentHead: formData.departmentHead,
            description: formData.description
          }
          response = await updateDepartment(updateData)
        } else {
          // 新增部门
          const createData: CreateDepartmentRequest = {
            departmentName: formData.departmentName,
            departmentCode: formData.departmentCode,
            parentId: formData.parentId || undefined,
            departmentType: formData.departmentType as 'business' | 'support' | 'management',
            departmentStatus: formData.departmentStatus as 'normal' | 'disabled',
            departmentHead: formData.departmentHead,
            description: formData.description
          }
          response = await addDepartment(createData)
        }

        if (response.code == 200) {
          ElMessage.success(tc('success'))
          dialogVisible.value = false
          loadData()
        } else {
          ElMessage.error(response.message || (isEdit.value ? tc('editFailed') : tc('addFailed')))
        }
      } catch (error) {
        console.error('提交部门数据失败:', error)
        ElMessage.error(isEdit.value ? tc('editFailed') : tc('addFailed'))
      }
    }
  })
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadData()
}

const expandAll = () => {
  defaultExpandAll.value = true
  // This is a bit of a hack to force re-render, but it's effective for default-expand-all
  const data = [...tableData.value]
  tableData.value = []
  nextTick(() => {
    tableData.value = data
  })
}

const collapseAll = () => {
  defaultExpandAll.value = false
  const data = [...tableData.value]
  tableData.value = []
  nextTick(() => {
    tableData.value = data
  })
}

const handleExpandChange = (row: Department, expanded: boolean) => {
  // You can add logic here if you need to handle individual expand/collapse events
  console.log('Expand change:', row.departmentName, expanded)
}

// 加载字典数据
const loadDictionary = async () => {
  try {
    const response = await getDictionary('1001')
    if (response.code === 200) {
      departmentStatusOptions.value = response.result
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 生命周期钩子
onMounted(() => {
  loadData()
  loadDictionary()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.page-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}
.search-card {
  margin-bottom: 20px;
}
.table-card {
  margin-top: 20px;
}
.table-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.dialog-footer {
  text-align: right;
}
</style>
