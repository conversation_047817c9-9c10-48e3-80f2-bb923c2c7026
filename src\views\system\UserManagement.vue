<template>
  <div class="user-management-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('user.title') }}</span>
          <el-button
            v-permission="'system:user:create'"
            type="primary"
            @click="handleAddUser"
          >
            {{ t('user.addUser') }}
          </el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item :label="t('user.username') + '/' + t('user.fullName')" prop="username">
          <el-input v-model="queryParams.username" :placeholder="tc('inputPlaceholder')" clearable />
        </el-form-item>
        <el-form-item :label="t('user.userType')" prop="userType">
          <el-select v-model="queryParams.userType" :placeholder="tc('pleaseSelect')" clearable style="width: 200px">
            <el-option :label="t('user.userTypeFactory')" value="factory" />
            <el-option :label="t('user.userTypeStore')" value="store" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('user.primaryStore')" prop="storeId">
          <el-select v-model="queryParams.storeId" :placeholder="tc('pleaseSelect')" clearable style="width: 200px">
            <el-option v-for="store in storeOptions" :key="store.id" :label="store.storeName" :value="store.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">{{ tc('search') }}</el-button>
          <el-button @click="resetQuery">{{ tc('reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 用户列表 -->
      <el-table :data="userList" style="width: 100%" v-loading="loading">
        <el-table-column prop="username" :label="t('user.username')" width="150" />
        <el-table-column prop="fullName" :label="t('user.fullName')" width="150" />
        <el-table-column :label="t('user.userType')" width="120">
            <template #default="scope">
                <el-tag v-if="scope.row.userType === 'factory'" type="success">{{ t('user.userTypeFactory') }}</el-tag>
                <el-tag v-else-if="scope.row.userType === 'store'" type="info">{{ t('user.userTypeStore') }}</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="primaryStoreName" :label="t('user.primaryStore')" width="180" />
        <el-table-column prop="phone" :label="t('user.phone')" width="150" />
        <el-table-column prop="email" :label="t('user.email')" width="200" />
        <el-table-column :label="t('user.userStatus')" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.userStatus"
              active-value="normal"
              inactive-value="disabled"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" width="220" fixed="right">
          <template #default="scope">
            <el-button
              v-permission="'system:user:update'"
              type="primary"
              link
              @click="handleEditUser(scope.row)"
            >
              {{ tc('edit') }}
            </el-button>
            <el-button
              v-permission="'system:user:assign'"
              type="primary"
              link
              @click="handleAssignRole(scope.row)"
            >
              {{ t('user.assignRoles') }}
            </el-button>
            <el-button
              v-permission="'system:user:delete'"
              type="danger"
              link
              @click="handleDeleteUser(scope.row)"
            >
              {{ tc('delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="queryParams.current"
        v-model:page-size="queryParams.size"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
    </el-card>

    <!-- 用户新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" v-model="userDialogVisible" width="600px" append-to-body>
      <el-form :model="userForm" :rules="userFormRules" ref="userFormRef" label-width="100px">
        <el-form-item :label="t('user.username')" prop="username">
          <el-input v-model="userForm.username" :placeholder="t('user.enterUsername')" />
        </el-form-item>
        <el-form-item :label="t('user.fullName')" prop="fullName">
          <el-input v-model="userForm.fullName" :placeholder="t('user.enterFullName')" />
        </el-form-item>
        <el-form-item :label="t('user.phone')" prop="phone">
          <el-input v-model="userForm.phone" :placeholder="t('user.enterPhone')" />
        </el-form-item>
        <el-form-item :label="t('user.email')" prop="email">
          <el-input v-model="userForm.email" :placeholder="t('user.enterEmail')" />
        </el-form-item>
        <el-form-item :label="t('user.userType')" prop="userType">
          <el-select v-model="userForm.userType" :placeholder="t('user.selectUserType')" style="width: 100%;">
              <el-option :label="t('user.userTypeFactory')" value="factory" />
              <el-option :label="t('user.userTypeStore')" value="store" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('user.entryDate')" prop="entryDate">
            <el-date-picker v-model="userForm.entryDate" type="date" :placeholder="t('user.selectEntryDate')" style="width: 100%;" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item :label="t('user.userStatus')" prop="userStatus">
          <el-radio-group v-model="userForm.userStatus">
            <el-radio label="normal">{{ t('user.userStatusNormal') }}</el-radio>
            <el-radio label="disabled">{{ t('user.userStatusDisabled') }}</el-radio>
          </el-radio-group>
        </el-form-item>
         <el-form-item :label="tc('remark')" prop="remark">
          <el-input v-model="userForm.remark" type="textarea" :placeholder="tc('inputPlaceholder')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="userDialogVisible = false">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="submitUserForm">{{ tc('confirm') }}</el-button>
      </template>
    </el-dialog>

    <!-- 分配角色对话框 -->
    <el-dialog :title="t('user.assignRoles')" v-model="roleDialogVisible" width="80%">
      <div class="assign-role-header">
        <span v-if="currentUser">{{ t('user.currentUser') }}: <strong>{{ currentUser.fullName }}</strong></span>
        <el-button
          type="primary"
          @click="addStoreRole"
          :disabled="currentUser?.userType === 'factory' && userStoreRoles.length > 0"
        >
          {{ t('user.addAssignment') }}
        </el-button>
      </div>

      <el-table :data="userStoreRoles" border>
        <el-table-column :label="t('user.primaryRole')" width="100" align="center">
          <template #default="{ row }">
            <el-radio v-model="primaryStoreId" :label="row.id" @change="handleSetPrimary(row.id)">&nbsp;</el-radio>
          </template>
        </el-table-column>

        <el-table-column :label="t('user.belongStore')" width="200">
          <template #default="{ row }">
            <el-select v-model="row.storeId" @change="handleStoreChange(row)" :placeholder="tc('pleaseSelect')" filterable>
              <el-option v-for="store in storeOptions" :key="store.id" :label="store.storeName" :value="store.id" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column :label="t('user.department')" width="200">
            <template #default="{ row }">
                <el-select v-model="row.departmentId" :loading="row.deptLoading" :placeholder="tc('pleaseSelect')" filterable>
                    <el-option v-for="dept in row.deptOptions" :key="dept.id" :label="dept.departmentName" :value="dept.id" />
                </el-select>
            </template>
        </el-table-column>

        <el-table-column :label="t('user.roles')" min-width="200">
          <template #default="{ row }">
            <el-select v-model="row.roleIds" :loading="row.roleLoading" :placeholder="tc('pleaseSelect')" filterable multiple style="width: 100%;">
              <el-option v-for="role in row.roleOptions" :key="role.id" :label="role.roleName" :value="role.id" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column :label="t('user.position')" width="150">
          <template #default="{ row }">
            <el-input v-model="row.position" :placeholder="t('user.enterPosition')" />
          </template>
        </el-table-column>

        <el-table-column :label="tc('operations')" width="100" align="center">
          <template #default="{ row }">
            <el-button type="danger" link @click="removeStoreRole(row.id)">{{ tc('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="roleDialogVisible = false">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="submitRoleAssignment">{{ tc('confirm') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

  import type { FormInstance, FormRules } from 'element-plus'
  import { useModuleI18n } from '@/composables/useModuleI18n'
import {
  getUserListPage,
  addUserApi,
  updateUserApi,
  deleteUserApi,
  updateUserStatus,
  assignUserRolesApi,
  getUserAssignedRoles,
  getStoreOptions,
  getDepartmentTree,
  getRoles,
  getStoreDetail
} from '@/api/modules/permission'
import type { User, CreateUserRequest, UpdateUserRequest, Store, Department, UserStoreRole, UserType } from '@/types/permission'

const { t, tc } = useModuleI18n('base')

// =========== 本地接口定义 ===========
interface UserStoreRoleRow {
  id: string;
  userId?: string;
  storeId?: string;
  departmentId?: string;
  position?: string;
  roleIds: string[];
  isPrimary: boolean;
  deptLoading: boolean;
  roleLoading: boolean;
  deptOptions: Department[];
  roleOptions: { id: string; roleName: string; [key: string]: unknown }[]; // 角色选项数组
}

// =========== 查询和列表 ===========
const queryParams = reactive({
  username: '',
  userType: undefined as UserType | undefined,
  storeId: '',
  current: 1,
  size: 10
})
const userList = ref<User[]>([])
const total = ref(0)
const loading = ref(false)
const storeOptions = ref<Store[]>([])

const loadUserList = async () => {
  loading.value = true
  try {
    const res = await getUserListPage(queryParams)
    if (res.code == 200) {
      userList.value = res.result?.records || []
      total.value = res.result?.total || 0
      // 使用真实的分页数据
      queryParams.current = res.result?.current || 1
      queryParams.size = res.result?.size || 10
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const loadStoreOptions = async () => {
      try {
      const res = await getStoreOptions()
      if (res.code == 200) {
        // 根据实际返回的数据格式处理
        storeOptions.value = res.result?.map(option => ({
          id: option.id?.toString() || '',
          storeName: option.storeName || ''
        } as Store)) || []
      }
  } catch (error) {
    console.error('获取门店选项失败:', error)
  }
}

const handleQuery = () => {
  queryParams.current = 1
  loadUserList()
}

const resetQuery = () => {
  queryParams.username = ''
  queryParams.userType = undefined
  queryParams.storeId = ''
  handleQuery()
}

onMounted(() => {
  loadUserList()
  loadStoreOptions()
})

// =========== 新增/编辑用户 ===========
const dialogTitle = ref('')
const userDialogVisible = ref(false)
const userFormRef = ref<FormInstance>()
const userForm = reactive<CreateUserRequest & { id?: string }>({
  username: '',
  fullName: '',
  phone: '',
  email: '',
  userType: 'store',
  entryDate: '',
  userStatus: 'normal',
  remark: ''
})

const userFormRules = reactive<FormRules>({
  username: [{ required: true, message: t('user.usernameRequired'), trigger: 'blur' }],
  fullName: [{ required: true, message: t('user.fullNameRequired'), trigger: 'blur' }],
  userType: [{ required: true, message: t('user.userTypeRequired'), trigger: 'change' }],
  entryDate: [{ required: true, message: t('user.entryDateRequired'), trigger: 'change' }],
  email: [{ type: 'email', message: t('user.emailFormatError'), trigger: ['blur', 'change'] }]
})

const handleAddUser = () => {
    if (userFormRef.value) userFormRef.value.resetFields()
    Object.assign(userForm, {
        id: '',
        username: '',
        fullName: '',
        phone: '',
        email: '',
        userType: 'store',
        entryDate: '',
        userStatus: 'normal',
        remark: ''
    })
    dialogTitle.value = t('user.addUser')
    userDialogVisible.value = true
}

const handleEditUser = (row: User) => {
    if (userFormRef.value) userFormRef.value.resetFields()
    Object.assign(userForm, row)
    dialogTitle.value = t('user.editUser')
    userDialogVisible.value = true
}

const submitUserForm = async () => {
    try {
        await userFormRef.value?.validate()

        if (userForm.id) {
            // 编辑用户
            await updateUserApi(userForm as UpdateUserRequest & { id: string })
        } else {
            // 新增用户
            await addUserApi(userForm)
        }

        userDialogVisible.value = false
        loadUserList()
    } catch (error) {
        console.error('保存用户失败:', error)
    }
}

// =========== 分配角色 ===========
const roleDialogVisible = ref(false)
const currentUser = ref<User | null>(null)
const userStoreRoles = ref<UserStoreRoleRow[]>([])
const primaryStoreId = ref<string>('')

const handleAssignRole = async (user: User) => {
    currentUser.value = user;

    try {
        // 获取用户已分配的角色
        const res = await getUserAssignedRoles(user.id);
        console.log("用户已分配角色API响应:", res);
        if (res.result && res.result.length > 0) {
            console.log("第一条角色数据详情:", res.result[0]);
            console.log("roleId字段:", res.result[0].roleId);
            console.log("roleIds字段:", res.result[0].roleIds);
        }
        if (res.code == 200 && res.result) {
            const assignmentsMap = new Map<string, UserStoreRoleRow>();

            if (res.result && res.result.length > 0) {
                // 按 storeId 分组聚合数据
                res.result.forEach((role: UserStoreRole) => {
                    const storeIdStr = role.storeId?.toString() || '';
                    const departmentIdStr = role.departmentId?.toString() || '';

                    if (!assignmentsMap.has(storeIdStr)) {
                        // 如果是该门店的第一个角色，创建新行
                        let roleIdsArray: string[] = [];

                        // 处理不同的roleIds格式
                        if (role.roleIds && Array.isArray(role.roleIds) && role.roleIds.length > 0) {
                            // 如果是数组格式且不为空
                            roleIdsArray = role.roleIds.map(id => id.toString());
                        } else if (role.roleId) {
                            // 如果是单个roleId字段
                            roleIdsArray = [role.roleId.toString()];
                        } else {
                            // 如果都没有或为空，则初始化为空数组
                            roleIdsArray = [];
                        }

                        console.log(`门店${storeIdStr}的角色ID数组:`, roleIdsArray);

                        assignmentsMap.set(storeIdStr, {
                            id: storeIdStr, // 使用 storeId 作为临时唯一键
                            userId: user.id,
                            storeId: storeIdStr,
                            roleIds: roleIdsArray,
                            isPrimary: role.isPrimary === 1 || role.isPrimary === true,
                            // 其他字段可以取第一个角色的值
                            departmentId: departmentIdStr,
                            position: role.position || '',
                            // 初始化加载状态和选项
                            deptLoading: false,
                            roleLoading: false,
                            deptOptions: [],
                            roleOptions: []
                        });
                    } else {
                        // 如果该门店已存在，将角色ID推入数组
                        const existingRow = assignmentsMap.get(storeIdStr)!;
                        if (role.roleId) {
                            existingRow.roleIds.push(role.roleId.toString());
                        }
                        // 如果发现一个 isPrimary 的角色，则将该行标记为 Primary
                        if (role.isPrimary === 1 || role.isPrimary === true) {
                            existingRow.isPrimary = true;
                        }
                    }
                });
            }

            userStoreRoles.value = Array.from(assignmentsMap.values());

            // 找到主角色行并设置 primaryStoreId
            const primaryRow = userStoreRoles.value.find(r => r.isPrimary);
            primaryStoreId.value = primaryRow ? primaryRow.id : '';
            if (!primaryStoreId.value && userStoreRoles.value.length > 0) {
                // 如果没有主角色，默认第一个为主
                userStoreRoles.value[0].isPrimary = true;
                primaryStoreId.value = userStoreRoles.value[0].id;
            }

            console.log("处理后的用户角色数据:", userStoreRoles.value);

            // 为已有的条目加载下拉选项
            for (const row of userStoreRoles.value) {
                if (row.storeId) {
                    console.log('为行加载选项:', row.storeId, row.departmentId, row.roleIds);
                    await loadDeptAndRoleOptions(row);
                    console.log('加载完成后的选项:', {
                        storeId: row.storeId,
                        deptOptions: row.deptOptions.length,
                        roleOptions: row.roleOptions.length,
                        selectedRoleIds: row.roleIds
                    });
                }
            }
        } else {
            // 如果没有已分配的角色，初始化为空
            userStoreRoles.value = [];
            primaryStoreId.value = '';
        }
    } catch (error) {
        console.error('获取用户已分配角色失败:', error)
        ElMessage.error('获取用户已分配角色失败')
        // 发生错误时也初始化为空
        userStoreRoles.value = [];
        primaryStoreId.value = '';
    }

    roleDialogVisible.value = true;
};

const addStoreRole = () => {
    if (currentUser.value?.userType === 'factory' && userStoreRoles.value.length > 0) {
        ElMessage.warning(t('user.factoryUserRoleLimit'));
        return;
    }

    const newId = `new_${Date.now()}`;
    userStoreRoles.value.push({
        id: newId,
        userId: currentUser.value?.id,
        storeId: '',
        departmentId: '', // 保持为空
        position: '', // 保持为空
        roleIds: [], // 初始化为空数组
        isPrimary: userStoreRoles.value.length === 0,
        deptLoading: false,
        roleLoading: false,
        deptOptions: [],
        roleOptions: []
    });

    if (userStoreRoles.value.length === 1) {
      primaryStoreId.value = newId;
    }
};

const removeStoreRole = (id: string) => {
    userStoreRoles.value = userStoreRoles.value.filter(r => r.id !== id)
    if (primaryStoreId.value === id && userStoreRoles.value.length > 0) {
        userStoreRoles.value[0].isPrimary = true
        primaryStoreId.value = userStoreRoles.value[0].id
    }
}

const handleSetPrimary = (id: string) => {
    userStoreRoles.value.forEach(r => {
        r.isPrimary = r.id === id
    })
}

const handleStoreChange = (row: UserStoreRoleRow) => {
    row.departmentId = '';
    row.roleIds = []; // 清空角色ID数组
    loadDeptAndRoleOptions(row);
};

const loadDeptAndRoleOptions = async (row: UserStoreRoleRow) => {
    if (!row.storeId) return
    row.deptLoading = true
    row.roleLoading = true

    try {
        // 加载部门
        const deptRes = await getDepartmentTree({ departmentStatus: 'normal' })
        if (deptRes.code == 200 && deptRes.result) {
            row.deptOptions = flattenTree(deptRes.result)
            console.log('部门选项加载成功:', row.deptOptions)
        }

       // 获取门店详情以确定门店类型
    const storeRes = await getStoreDetail(row.storeId)
    if (storeRes.code == 200 && storeRes.result) {
        const store = storeRes.result

        // 根据门店类型确定角色查询参数
        const roleQueryParams: { size: number; roleSource?: string } = { size: 100 }

        // 如果是总部门店，可以查看所有厂端角色
        if (store.storeType === 'headquarter') {
            roleQueryParams.roleSource = 'factory'
        } else {
            // 其他门店类型，查看店端角色和该门店的自定义角色
            roleQueryParams.roleSource = 'store'
        }
        const roleRes = await getRoles(roleQueryParams)
        console.log('角色API响应:', roleRes)
        if (roleRes.code == 200 && roleRes.result) {
          row.roleOptions = roleRes.result.records.map(r => ({
            ...r,
            id: r.id.toString()
          }))
            // row.roleOptions = roleRes.result.records
            console.log('角色选项加载成功:', row.roleOptions)
            console.log('当前行的roleIds:', row.roleIds)
        } else {
            console.log('角色API响应异常:', roleRes)
        }
      }
    } catch (error) {
        console.error('加载选项失败:', error)
    } finally {
        row.deptLoading = false
        row.roleLoading = false
    }
}

const flattenTree = (tree: Department[]): Department[] => {
    let result: Department[] = []
    tree.forEach(node => {
        result.push({ ...node, children: undefined }) // 移除children，变成一维数组
        if (node.children && node.children.length > 0) {
            result = result.concat(flattenTree(node.children))
        }
    })
    return result
}

const submitRoleAssignment = async () => {
    if (!currentUser.value) return;

    const finalRoles: UserStoreRole[] = [];
    for (const row of userStoreRoles.value) {
        if (!row.storeId || row.roleIds.length === 0) {
            ElMessage.warning(t('user.roleAssignmentIncomplete'));
            return;
        }

        // 每行数据是一条记录，角色以数组形式传输
        const storeName = storeOptions.value.find(s => s.id === row.storeId)?.storeName || '';
        const roleNames = row.roleIds.map(roleId =>
            row.roleOptions.find(r => r.id === roleId)?.roleName || ''
        );

        finalRoles.push({
            id: row.id,
            userId: currentUser.value!.id,
            storeId: row.storeId!,
            storeName: storeName,
            departmentId: row.departmentId!,
            departmentName: row.deptOptions.find(d => d.id === row.departmentId)?.departmentName || '',
            position: row.position!,
            roleIds: row.roleIds, // 角色ID数组格式 [1,2,3]
            roleNames: roleNames, // 角色名称数组
            isPrimary: row.id === primaryStoreId.value
        });
    }

    // 确保只有一个主门店
    let primarySet = false;
    finalRoles.forEach(role => {
        if (role.isPrimary) {
            if (primarySet) {
                role.isPrimary = false;
            } else {
                primarySet = true;
            }
        }
    });

    try {
        await assignUserRolesApi(currentUser.value.id, finalRoles);
        roleDialogVisible.value = false;
        loadUserList();
    } catch (error) {
        console.error('分配角色失败:', error)
        ElMessage.error('分配角色失败')
    }
};

// =========== 其他操作 ===========
const handleStatusChange = async (row: User) => {
  try {
    await updateUserStatus(row.id, row.userStatus)
    loadUserList()
  } catch (error) {
    console.error('状态变更失败:', error)
    // 恢复原状态
    row.userStatus = row.userStatus === 'normal' ? 'disabled' : 'normal'
  }
}

const handleDeleteUser = (row: User) => {
  ElMessageBox.confirm(
    t('user.confirmDeleteUser', { fullName: row.fullName }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteUserApi(row.id)
      loadUserList()
    } catch (error) {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  })
}
</script>

<style scoped>
.user-management-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-form {
  margin-bottom: 20px;
}
.el-pagination {
  margin-top: 20px;
  justify-content: flex-end;
}
.assign-role-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}
</style>
