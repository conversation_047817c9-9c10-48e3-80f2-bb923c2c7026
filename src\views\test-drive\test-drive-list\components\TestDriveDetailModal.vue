<template>
  <el-dialog
    v-model="visible"
    title="试驾单详情"
    width="800px"
  >
    <el-form label-position="left" label-width="120px">
      <h3 class="section-title">潜客信息</h3>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="潜客来源">
            {{ testDriveData?.sourceChannel }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="潜客名称">
            {{ testDriveData?.customerName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="潜客手机号">
            {{ testDriveData?.customerPhone }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证件类别">
            {{ testDriveData?.idType }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证件号">
            {{ testDriveData?.idNumber }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱">
            {{ testDriveData?.email }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地区">
            {{ testDriveData?.region }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址">
            {{ testDriveData?.address }}
          </el-form-item>
        </el-col>
      </el-row>

      <h3 class="section-title">试驾信息</h3>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="试驾单号">
            {{ testDriveData?.testDriveNo }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="销售顾问">
            {{ testDriveData?.consultantName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾车型 (Model)">
            {{ testDriveData?.model }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾配置 (Variant)">
            {{ testDriveData?.variant }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾人">
            {{ testDriveData?.driverName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾人手机号">
            {{ testDriveData?.driverPhone }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾人证件类别">
            {{ testDriveData?.driverIdType }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾人证件号">
            {{ testDriveData?.driverIdNumber }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾人驾照号">
            {{ testDriveData?.driverLicenseNumber }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾开始里程数">
            {{ testDriveData?.startMileage }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾结束里程数">
            {{ testDriveData?.endMileage }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾开始时间">
            {{ formatDate(testDriveData?.startTime) }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试驾结束时间">
            {{ formatDate(testDriveData?.endTime) }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="试驾反馈">
            <div style="white-space: pre-wrap;">{{ testDriveData?.feedback }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TestDriveRecord } from '../../../../api/types/test-drive'

// Props
const props = defineProps<{
  modelValue: boolean
  testDriveData: TestDriveRecord | null
}>()

// Emits
const emit = defineEmits(['update:modelValue'])

// Computed properties
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Methods
const closeDialog = () => {
  emit('update:modelValue', false)
}

const formatDate = (dateString?: string | number | null) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/,/g, '')
}
</script>

<style scoped>
.section-title {
  margin-top: 20px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
