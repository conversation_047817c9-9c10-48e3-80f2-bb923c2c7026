<template>
  <div class="test-drive-list-management">
    <h1 class="page-title">试驾登记列表</h1>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-form ref="filterFormRef" :model="searchForm" label-position="top">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="试驾人">
              <el-input v-model="searchForm.customerName" placeholder="" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="试驾人手机号">
              <el-input v-model="searchForm.customerPhone" placeholder="" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="试驾车型 (Model)">
              <el-select
                v-model="searchForm.model"
                placeholder="全部"
                clearable
                style="width: 100%;"
                :loading="masterDataLoading"
              >
                <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="试驾配置 (Variant)">
              <el-select
                v-model="searchForm.variant"
                placeholder="全部"
                clearable
                style="width: 100%;"
                :loading="masterDataLoading"
              >
                <el-option v-for="item in variantOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16" style="align-items: flex-end;">
          <el-col :span="6">
            <el-form-item label="试驾日期">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="年/月/日"
                end-placeholder="年/月/日"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <div class="button-group">
              <el-button @click="handleReset">
                重置
              </el-button>
              <el-button type="primary" @click="handleSearch">
                查询
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 功能按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="showAddModal">
        <el-icon><Plus /></el-icon>登记试驾单
      </el-button>
      <el-button @click="handleExport">
        <el-icon><Download /></el-icon>导出
      </el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        :loading="loading"
        style="width: 100%"
        height="500px"
        border
        stripe
      >
        <el-table-column label="序号" width="60" type="index" :index="indexMethod" />
        <el-table-column prop="testDriveNo" label="试驾单号" width="180" />
        <el-table-column prop="createTime" label="试驾单录入时间" width="150" />
        <el-table-column prop="customerName" label="试驾人" width="100" />
        <el-table-column prop="customerPhone" label="试驾人手机号" width="120" />
        <el-table-column prop="driverIdNumber" label="试驾人证件号" width="180" />
        <el-table-column prop="driverLicenseNumber" label="试驾人驾照号" width="180" />
        <el-table-column prop="model" label="试驾车型 (Model)" width="150" />
        <el-table-column prop="variant" label="试驾配置 (Variant)" width="150" />
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="{ row }">
            <el-button type="success" size="small" @click="handleViewDetail(row)">详情</el-button>
            <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 模态框 -->
    <AddTestDriveModal v-if="showAddTestDriveModal" v-model="showAddTestDriveModal" @success="handleSuccess" />
    <EditTestDriveModal v-if="showEditTestDriveModal" :show="showEditTestDriveModal" :testDriveData="currentRecord" @update:show="showEditTestDriveModal = $event" @success="handleSuccess" />
    <TestDriveDetailModal v-model="showTestDriveDetailModal" :test-drive-data="currentRecord" />
  </div>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'TestDriveListView'
})

import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Download } from '@element-plus/icons-vue'
import { testDriveApi } from '@/api/modules/test-drive'
import type { TestDriveRecord, GetTestDriveListRequest } from '@/api/types/test-drive'
import { getVehicleModelList, getVehicleVariantList } from '@/api/modules/masterData'
import type { VehicleModel, VehicleVariant } from '@/api/modules/masterData'
import AddTestDriveModal from './components/AddTestDriveModal.vue'
import EditTestDriveModal from './components/EditTestDriveModal.vue'
import TestDriveDetailModal from './components/TestDriveDetailModal.vue'

// Refs
const filterFormRef = ref()
const loading = ref(false)
const tableData = ref<TestDriveRecord[]>([])
const currentRecord = ref<TestDriveRecord | null>(null)

// 主数据
const vehicleModelOptions = ref<VehicleModel[]>([])
const vehicleVariantOptions = ref<VehicleVariant[]>([])
const masterDataLoading = ref(false)

// 筛选表单
const createSearchForm = () => ({
  customerName: '',
  customerPhone: '',
  model: undefined,
  variant: undefined,
  dateRange: [] as Date[]
})
const searchForm = reactive(createSearchForm())

// 模态框状态
const showAddTestDriveModal = ref(false)
const showEditTestDriveModal = ref(false)
const showTestDriveDetailModal = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 获取车型数据
const fetchVehicleModelData = async () => {
  try {
    masterDataLoading.value = true
    const models = await getVehicleModelList()
    vehicleModelOptions.value = models
  } catch (error) {
    console.error('获取车型数据失败:', error)
    ElMessage.error('获取车型数据失败')
    vehicleModelOptions.value = []
  } finally {
    masterDataLoading.value = false
  }
}

// 获取车型配置数据
const fetchVehicleVariantData = async () => {
  try {
    const variants = await getVehicleVariantList()
    vehicleVariantOptions.value = variants
  } catch (error) {
    console.error('获取车型配置数据失败:', error)
    ElMessage.error('获取车型配置数据失败')
    vehicleVariantOptions.value = []
  }
}

// 下拉选项 - 使用主数据接口
const modelOptions = computed(() => vehicleModelOptions.value.map(model => ({
  label: model.name,
  value: model.id
})))

const variantOptions = computed(() => vehicleVariantOptions.value.map(variant => ({
  label: variant.name,
  value: variant.id
})))

// 序号计算方法
const indexMethod = (index: number) => {
  return (pagination.page - 1) * pagination.pageSize + index + 1
}

// 表格相关方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.page = 1
  loadTableData()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  loadTableData()
}

// 方法
const loadTableData = async () => {
  loading.value = true
  try {
    const params: GetTestDriveListRequest = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      customerName: searchForm.customerName || undefined,
      customerPhone: searchForm.customerPhone || undefined,
      model: searchForm.model,
      variant: searchForm.variant,
      startTimeBegin: searchForm.dateRange && searchForm.dateRange[0] ? searchForm.dateRange[0].toISOString() : undefined,
      startTimeEnd: searchForm.dateRange && searchForm.dateRange[1] ? searchForm.dateRange[1].toISOString() : undefined,
    }

    // 获取试驾列表数据
    const res = await testDriveApi.getTestDriveList(params)

    console.log('获取试驾列表结果:', res)

    if (res && res.result && res.result.records) {
      tableData.value = res.result.records
      pagination.total = res.result.total || 0
    } else {
      ElMessage.error('获取数据失败')
    }
  } catch (error: unknown) {
    const errorMsg = error instanceof Error ? error.message : '加载失败'
    ElMessage.error(`加载失败: ${errorMsg}`)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

const handleReset = () => {
  // 完全重置为初始状态
  Object.assign(searchForm, createSearchForm())
  // 重新加载数据
  handleSearch()
}

const handleExport = async () => {
  ElMessage.info('正在处理导出...')
  loading.value = true
  try {
    const params = {
      customerName: searchForm.customerName || undefined,
      customerPhone: searchForm.customerPhone || undefined,
      model: searchForm.model,
      variant: searchForm.variant,
    }

    // 获取导出数据
    const blobRes = await testDriveApi.exportTestDriveList(params)
    const blob = blobRes as unknown as Blob

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `试驾登记列表_${new Date().toISOString().slice(0, 10)}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error: unknown) {
    const errorMsg = error instanceof Error ? error.message : '导出失败'
    ElMessage.error(`导出失败: ${errorMsg}`)
  } finally {
    loading.value = false
  }
}

const showAddModal = () => {
  showAddTestDriveModal.value = true
}

const handleViewDetail = (row: TestDriveRecord) => {
  currentRecord.value = row
  showTestDriveDetailModal.value = true
}

const handleEdit = async (row: TestDriveRecord) => {
  try {
    loading.value = true
    console.log('点击编辑，行数据:', row)

    // 请求试驾详情接口获取完整数据
    const res = await testDriveApi.getTestDriveDetail(row.testDriveNo)
    console.log('API返回的详情数据:', res)

    if (res && res.result) {
      console.log('设置currentRecord前:', currentRecord.value)
      currentRecord.value = res.result
      console.log('设置currentRecord后:', currentRecord.value)

      console.log('显示模态框前 showEditTestDriveModal:', showEditTestDriveModal.value)
      showEditTestDriveModal.value = true
      console.log('显示模态框后 showEditTestDriveModal:', showEditTestDriveModal.value)
    } else {
      ElMessage.error('获取试驾详情失败')
    }
  } catch (error: unknown) {
    console.error('获取试驾详情错误:', error)
    const errorMsg = error instanceof Error ? error.message : '获取试驾详情失败'
    ElMessage.error(`获取详情失败: ${errorMsg}`)
  } finally {
    loading.value = false
  }
}

const handleSuccess = () => {
  ElMessage.success('操作成功')
  loadTableData() // 重新加载数据
}

onMounted(() => {
  loadTableData()
  fetchVehicleModelData()
  fetchVehicleVariantData()
})
</script>

<style scoped lang="scss">
.test-drive-list-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
  }

    .filter-section {
    background-color: white;
    padding: 24px;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);

    .el-form-item {
      margin-bottom: 16px;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      margin-bottom: 8px;
    }

    .button-group {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-bottom: 22px;
    }
  }

  .action-section {
    margin-bottom: 16px;

    .el-button {
      margin-right: 12px;
    }
  }

  .table-container {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    padding: 0;

    .el-table {
      font-size: 14px;

      .el-table__header-wrapper th {
        background-color: #fafafa;
        color: #333;
        font-weight: 500;
      }
    }
  }

  .pagination-container {
    margin-top: 0;
    padding: 16px 20px;
    display: flex;
    justify-content: flex-end;
    background-color: white;
    border-top: 1px solid #ebeef5;
  }
}
</style>
