<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('vehicleAllocation.title') }}</h1>

    <!-- 搜索筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('vehicleAllocation.orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.customerName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('vehicleAllocation.customerNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.customerPhone')">
              <el-input
                v-model="searchParams.customerPhone"
                :placeholder="t('vehicleAllocation.customerPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.allocationStatus')">
              <el-select
                v-model="searchParams.allocationStatus"
                :placeholder="t('vehicleAllocation.allocationStatusPlaceholder')"
                clearable
              >
                <el-option :label="t('common.pleaseSelect')" value="" />
                <el-option :label="t('vehicleAllocation.allocated')" value="allocated" />
                <el-option :label="t('vehicleAllocation.unallocated')" value="unallocated" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.orderStatus')">
              <el-select
                v-model="searchParams.orderStatus"
                :placeholder="t('vehicleAllocation.orderStatusPlaceholder')"
                clearable
              >
                <el-option :label="t('common.pleaseSelect')" value="" />
                <el-option
                  v-for="status in orderStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.vin')">
              <el-input
                v-model="searchParams.vin"
                :placeholder="t('vehicleAllocation.vinPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.factoryOrderNumber')">
              <el-input
                v-model="searchParams.factoryOrderNumber"
                :placeholder="t('vehicleAllocation.factoryOrderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.allocationTime')">
              <el-date-picker
                v-model="allocationTimeRange"
                type="datetimerange"
                :start-placeholder="t('common.startDate')"
                :end-placeholder="t('common.endDate')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.store')">
              <el-select
                v-model="searchParams.store"
                :placeholder="t('vehicleAllocation.storePlaceholder')"
                clearable
                @change="handleStoreChange"
              >
                <el-option :label="t('common.pleaseSelect')" value="" />
                <el-option
                  v-for="store in storeOptions"
                  :key="store.value"
                  :label="store.label"
                  :value="store.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.salesConsultant')">
              <el-select
                v-model="searchParams.salesConsultant"
                :placeholder="t('vehicleAllocation.salesConsultantPlaceholder')"
                clearable
              >
                <el-option :label="t('common.pleaseSelect')" value="" />
                <el-option
                  v-for="consultant in salesConsultantOptions"
                  :key="consultant.value"
                  :label="consultant.label"
                  :value="consultant.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.orderCreateTime')">
              <el-date-picker
                v-model="orderCreateTimeRange"
                type="datetimerange"
                :start-placeholder="t('common.startDate')"
                :end-placeholder="t('common.endDate')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ t('common.search') }}
              </el-button>
              <el-button @click="resetSearch">{{ t('common.reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20 operation-card">
      <div class="operation-buttons">
        <el-button type="primary" :icon="Download" @click="showExportModal">
          {{ t('common.export') }}
        </el-button>
      </div>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        :empty-text="t('common.noData')"
        style="width: 100%"
      >
        <el-table-column type="index" :label="t('common.index')" width="80" />
        <el-table-column :label="t('vehicleAllocation.orderNumber')" prop="orderNumber" min-width="120" />
        <el-table-column :label="t('vehicleAllocation.customerName')" prop="customerName" min-width="100" />
        <el-table-column :label="t('vehicleAllocation.customerPhone')" prop="customerPhone" min-width="120" />
        <el-table-column :label="t('vehicleAllocation.store')" prop="store" min-width="100" />
        <el-table-column :label="t('vehicleAllocation.salesConsultant')" prop="salesConsultant" min-width="100" />
        <el-table-column :label="t('vehicleAllocation.model')" prop="model" min-width="80" />
        <el-table-column :label="t('vehicleAllocation.variant')" prop="variant" min-width="100" />
        <el-table-column :label="t('vehicleAllocation.color')" prop="color" min-width="80" />
        <el-table-column :label="t('vehicleAllocation.factoryOrderNumber')" prop="factoryOrderNumber" min-width="120" />
        <el-table-column :label="t('vehicleAllocation.vin')" prop="vin" min-width="140" />
        <el-table-column :label="t('vehicleAllocation.allocationStatus')" prop="allocationStatus" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.allocationStatus === 'allocated' ? 'success' : 'info'">
              {{ row.allocationStatus === 'allocated' ? t('vehicleAllocation.allocated') : t('vehicleAllocation.unallocated') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleAllocation.orderStatus')" prop="orderStatus" min-width="100">
          <template #default="{ row }">
            {{ t(`vehicleAllocation.orderStatuses.${row.orderStatus}`) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleAllocation.orderCreateTime')" prop="orderCreateTime" min-width="140" />
        <el-table-column :label="t('common.operations')" width="220" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="canAllocate(row)"
              type="primary"
              :icon="CaretRight"
              link
              @click="showAllocationModal(row)"
            >
              {{ t('vehicleAllocation.allocate') }}
            </el-button>
            <el-button
              v-if="canCancelAllocate(row)"
              type="danger"
              :icon="Close"
              link
              @click="showCancelModal(row)"
            >
              {{ t('vehicleAllocation.cancelAllocate') }}
            </el-button>
            <el-button
              type="info"
              :icon="Document"
              link
              @click="showRecordModal(row)"
            >
              {{ t('vehicleAllocation.records') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 导出确认弹窗 -->
    <el-dialog
      v-model="exportModalVisible"
      :title="t('vehicleAllocation.exportData')"
      width="400px"
      :close-on-click-modal="false"
    >
      <p>{{ t('vehicleAllocation.confirmExport') }}</p>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="exportModalVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleExport" :loading="exportLoading">
            {{ t('common.export') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 配车确认弹窗 -->
    <el-dialog
      v-model="allocationModalVisible"
      :title="t('vehicleAllocation.allocationConfirm')"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div v-if="currentOrder">
        <!-- 订单信息展示 -->
        <div class="order-info-section">
          <h3>{{ t('vehicleAllocation.orderDetail') }}</h3>
          <el-row :gutter="20" class="order-info-grid">
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.orderNumber') }}:</strong> {{ currentOrder.orderNumber }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.customerName') }}:</strong> {{ currentOrder.customerName }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.customerPhone') }}:</strong> {{ currentOrder.customerPhone }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.model') }}:</strong> {{ currentOrder.model }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.variant') }}:</strong> {{ currentOrder.variant }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.color') }}:</strong> {{ currentOrder.color }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.salesConsultant') }}:</strong> {{ currentOrder.salesConsultant }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.store') }}:</strong> {{ currentOrder.store }}</div>
            </el-col>
          </el-row>
        </div>

        <!-- 可配车辆查询 -->
        <div class="vehicle-query-section">
          <h3>{{ t('vehicleAllocation.availableVehiclesQuery') }}</h3>
          <el-form :model="vehicleQueryParams" label-position="top">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="t('vehicleAllocation.vin')">
                  <el-input
                    v-model="vehicleQueryParams.vin"
                    :placeholder="t('vehicleAllocation.vinPlaceholder')"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('vehicleAllocation.factoryOrderNumber')">
                  <el-input
                    v-model="vehicleQueryParams.factoryOrderNumber"
                    :placeholder="t('vehicleAllocation.factoryOrderNumberPlaceholder')"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button type="primary" @click="queryAvailableVehicles" :loading="vehicleQueryLoading">
                    {{ t('common.search') }}
                  </el-button>
                  <el-button @click="resetVehicleQuery">{{ t('common.reset') }}</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 可配车辆列表 -->
        <div class="available-vehicles-section">
          <h3>{{ t('vehicleAllocation.availableVehicles') }}</h3>
          <el-table
            :data="availableVehicles"
            v-loading="vehicleQueryLoading"
            :empty-text="t('common.noData')"
            @selection-change="handleVehicleSelection"
          >
            <el-table-column type="radio" width="55">
              <template #default="{ $index }">
                <el-radio v-model="selectedVehicleIndex" :label="$index">{{ '' }}</el-radio>
              </template>
            </el-table-column>
            <el-table-column :label="t('vehicleAllocation.vin')" prop="vin" min-width="140" />
            <el-table-column :label="t('vehicleAllocation.factoryOrderNumber')" prop="factoryOrderNumber" min-width="120" />
            <el-table-column :label="t('vehicleAllocation.model')" prop="model" min-width="80" />
            <el-table-column :label="t('vehicleAllocation.variant')" prop="variant" min-width="100" />
            <el-table-column :label="t('vehicleAllocation.color')" prop="color" min-width="80" />
            <el-table-column :label="t('vehicleAllocation.warehouseName')" prop="warehouseName" min-width="100" />
            <el-table-column :label="t('vehicleAllocation.inStockTime')" prop="inStockTime" min-width="140" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="allocationModalVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmAllocation" :loading="allocationLoading">
            {{ t('common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 取消配车确认弹窗 -->
    <el-dialog
      v-model="cancelModalVisible"
      :title="t('common.tip')"
      width="400px"
      :close-on-click-modal="false"
    >
      <div style="display: flex; align-items: center;">
        <el-icon color="#E6A23C" style="font-size: 24px; margin-right: 10px;">
          <Warning />
        </el-icon>
        <span>{{ t('vehicleAllocation.confirmCancelAllocation') }}</span>
      </div>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="cancelModalVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmCancelAllocation" :loading="cancelLoading">
            {{ t('common.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 配车记录弹窗 -->
    <el-dialog
      v-model="recordModalVisible"
      :title="t('vehicleAllocation.allocationRecordDetail')"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentRecordOrder">
        <!-- 订单基本信息 -->
        <div class="record-order-info">
          <h3>{{ t('vehicleAllocation.orderDetail') }}</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.orderNumber') }}:</strong> {{ currentRecordOrder.orderNumber }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.customerName') }}:</strong> {{ currentRecordOrder.customerName }}</div>
            </el-col>
            <el-col :span="8">
              <div><strong>{{ t('vehicleAllocation.customerPhone') }}:</strong> {{ currentRecordOrder.customerPhone }}</div>
            </el-col>
          </el-row>
        </div>

        <!-- 配车历史时间轴 -->
        <div class="allocation-timeline">
          <h3>{{ t('vehicleAllocation.allocationHistory') }}</h3>
          <el-timeline v-if="allocationTimeline.length > 0">
            <el-timeline-item
              v-for="item in allocationTimeline"
              :key="item.id"
              :type="getTimelineType(item.processResult)"
              :timestamp="item.operationTime"
            >
              <div class="timeline-content">
                <div class="timeline-title">
                  {{ t(`vehicleAllocation.operationTypes.${item.operationType}`) }} -
                  {{ t(`vehicleAllocation.processResults.${item.processResult}`) }}
                </div>
                <div class="timeline-details">
                  <p><strong>{{ t('vehicleAllocation.operator') }}:</strong> {{ item.operator }}</p>
                  <p v-if="item.vin"><strong>{{ t('vehicleAllocation.vin') }}:</strong> {{ item.vin }}</p>
                  <p v-if="item.warehouseName"><strong>{{ t('vehicleAllocation.warehouseName') }}:</strong> {{ item.warehouseName }}</p>
                  <p v-if="item.remarks"><strong>{{ t('vehicleAllocation.remarks') }}:</strong> {{ item.remarks }}</p>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div v-else class="no-timeline">
            <el-empty :description="t('common.noData')" />
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="recordModalVisible = false">{{ t('common.cancel') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { Search, Download, CaretRight, Close, Document, Warning } from '@element-plus/icons-vue';

import {
  getVehicleAllocationOrderList,
  getAvailableVehicles,
  confirmAllocation as apiConfirmAllocation,
  cancelAllocation as apiCancelAllocation,
  getOrderAllocationTimeline,
  exportAllocationData,
  getStoreOptions,
  getSalesConsultantOptions,
  getOrderStatusOptions,
  getOperationTypeOptions
} from '@/api/modules/vehicleAllocation';

import type {
  VehicleAllocationOrderItem,
  VehicleAllocationOrderParams,
  AvailableVehicle,
  AvailableVehicleParams,
  OrderAllocationTimelineItem
} from '@/types/vehicleAllocation';

const { t } = useI18n();

// 响应式数据
const loading = ref(false);
const tableData = ref<VehicleAllocationOrderItem[]>([]);
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 搜索参数
const searchParams = reactive<Partial<VehicleAllocationOrderParams>>({
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  allocationStatus: undefined,
  orderStatus: undefined,
  vin: '',
  factoryOrderNumber: '',
  store: '',
  salesConsultant: '',
});

// 时间范围
const allocationTimeRange = ref<[string, string] | null>(null);
const orderCreateTimeRange = ref<[string, string] | null>(null);

// 选项数据
const storeOptions = ref<Array<{label: string, value: string}>>([]);
const salesConsultantOptions = ref<Array<{label: string, value: string}>>([]);
const orderStatusOptions = ref<Array<{label: string, value: string}>>([]);
const operationTypeOptions = ref<Array<{label: string, value: string}>>([]);

// 弹窗状态
const exportModalVisible = ref(false);
const exportLoading = ref(false);
const allocationModalVisible = ref(false);
const allocationLoading = ref(false);
const cancelModalVisible = ref(false);
const cancelLoading = ref(false);
const recordModalVisible = ref(false);

// 配车相关数据
const currentOrder = ref<VehicleAllocationOrderItem | null>(null);
const currentCancelOrder = ref<VehicleAllocationOrderItem | null>(null);
const currentRecordOrder = ref<VehicleAllocationOrderItem | null>(null);
const vehicleQueryParams = reactive<AvailableVehicleParams>({
  vin: '',
  factoryOrderNumber: ''
});
const vehicleQueryLoading = ref(false);
const availableVehicles = ref<AvailableVehicle[]>([]);
const selectedVehicleIndex = ref<number | null>(null);
const allocationTimeline = ref<OrderAllocationTimelineItem[]>([]);

// 计算属性
const selectedVehicle = computed(() => {
  if (selectedVehicleIndex.value !== null && availableVehicles.value[selectedVehicleIndex.value]) {
    return availableVehicles.value[selectedVehicleIndex.value];
  }
  return null;
});

// 监听时间范围变化
watch(allocationTimeRange, (newVal) => {
  if (newVal) {
    searchParams.allocationTimeStart = newVal[0];
    searchParams.allocationTimeEnd = newVal[1];
  } else {
    searchParams.allocationTimeStart = undefined;
    searchParams.allocationTimeEnd = undefined;
  }
});

watch(orderCreateTimeRange, (newVal) => {
  if (newVal) {
    searchParams.orderCreateTimeStart = newVal[0];
    searchParams.orderCreateTimeEnd = newVal[1];
  } else {
    searchParams.orderCreateTimeStart = undefined;
    searchParams.orderCreateTimeEnd = undefined;
  }
});

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const params: VehicleAllocationOrderParams = {
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize
    };

    const response = await getVehicleAllocationOrderList(params);
    tableData.value = response.data;
    pagination.total = response.total;
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error(t('common.networkError'));
  } finally {
    loading.value = false;
  }
};

const loadOptions = async () => {
  try {
    const [stores, consultants, orderStatuses, operationTypes] = await Promise.all([
      getStoreOptions(),
      getSalesConsultantOptions(),
      getOrderStatusOptions(),
      getOperationTypeOptions()
    ]);

    storeOptions.value = stores;
    salesConsultantOptions.value = consultants;
    orderStatusOptions.value = orderStatuses;
    operationTypeOptions.value = operationTypes; // For record modal if needed
  } catch (error) {
    console.error('加载选项失败:', error);
  }
};

const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

const resetSearch = () => {
  Object.assign(searchParams, {
    orderNumber: '',
    customerName: '',
    customerPhone: '',
    allocationStatus: undefined,
    orderStatus: undefined,
    vin: '',
    factoryOrderNumber: '',
    store: '',
    salesConsultant: '',
  });
  allocationTimeRange.value = null;
  orderCreateTimeRange.value = null;
  handleSearch();
};

const handleStoreChange = async (store: string) => {
  searchParams.salesConsultant = '';
  if (store) {
    try {
      salesConsultantOptions.value = await getSalesConsultantOptions(store);
    } catch (error) {
      console.error('加载销售顾问失败:', error);
    }
  } else {
    // 如果清空门店，则重新加载所有销售顾问
    loadOptions();
  }
};

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.page = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  pagination.page = val;
  loadData();
};

// 业务逻辑方法
const canAllocate = (row: VehicleAllocationOrderItem) => {
  return row.allocationStatus === 'unallocated' &&
         row.orderStatus === 'confirmed' &&
         !['ready_delivery', 'delivered', 'cancelled', 'cancel_approved'].includes(row.orderStatus);
};

const canCancelAllocate = (row: VehicleAllocationOrderItem) => {
  return row.allocationStatus === 'allocated' &&
         !['ready_delivery', 'delivered', 'cancelled', 'cancel_approved'].includes(row.orderStatus);
};

const showExportModal = () => {
  exportModalVisible.value = true;
};

const handleExport = async () => {
  exportLoading.value = true;
  try {
    const params = {
      searchParams: {
        ...searchParams,
        page: pagination.page,
        pageSize: pagination.pageSize
      },
      exportType: 'all_results' as const // 假设导出所有结果
    };

    const response = await exportAllocationData(params);
    if (response.success) {
      ElMessage.success(response.message);
      exportModalVisible.value = false;
      if (response.downloadUrl) {
        window.open(response.downloadUrl, '_blank');
      }
    } else {
      ElMessage.error(response.message || t('common.operationFailed'));
    }
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('common.operationFailed'));
  } finally {
    exportLoading.value = false;
  }
};

const showAllocationModal = async (row: VehicleAllocationOrderItem) => {
  currentOrder.value = row;
  allocationModalVisible.value = true;

  // 自动设置查询条件为订单要求的配置
  vehicleQueryParams.model = row.model;
  vehicleQueryParams.variant = row.variant;
  vehicleQueryParams.color = row.color;
  vehicleQueryParams.vin = ''; // 清空VIN和工厂订单号，以便重新查询
  vehicleQueryParams.factoryOrderNumber = '';

  // 自动查询可配车辆
  queryAvailableVehicles();
};

const queryAvailableVehicles = async () => {
  vehicleQueryLoading.value = true;
  try {
    const vehicles = await getAvailableVehicles(vehicleQueryParams);
    availableVehicles.value = vehicles;
    selectedVehicleIndex.value = null; // 清空已选车辆
  } catch (error) {
    console.error('查询可配车辆失败:', error);
    ElMessage.error(t('common.networkError'));
  } finally {
    vehicleQueryLoading.value = false;
  }
};

const resetVehicleQuery = () => {
  vehicleQueryParams.vin = '';
  vehicleQueryParams.factoryOrderNumber = '';
  if (currentOrder.value) {
    vehicleQueryParams.model = currentOrder.value.model;
    vehicleQueryParams.variant = currentOrder.value.variant;
    vehicleQueryParams.color = currentOrder.value.color;
  }
  queryAvailableVehicles();
};

const handleVehicleSelection = (selection: AvailableVehicle[]) => {
  // Element Plus table selection for radio type passes an array of selected rows
  // We only expect one, so we take the first one.
  if (selection.length > 0) {
    selectedVehicleIndex.value = availableVehicles.value.findIndex(v => v.id === selection[0].id);
  } else {
    selectedVehicleIndex.value = null;
  }
};

const confirmAllocation = async () => {
  if (!selectedVehicle.value) {
    ElMessage.warning(t('vehicleAllocation.noVehicleSelected'));
    return;
  }

  if (!currentOrder.value) return;

  // 再次验证车辆配置是否匹配，这是一个重要的业务规则
  const isConfigMatch = selectedVehicle.value.model === currentOrder.value.model &&
                        selectedVehicle.value.variant === currentOrder.value.variant &&
                        selectedVehicle.value.color === currentOrder.value.color;

  if (!isConfigMatch) {
    ElMessage.error(t('vehicleAllocation.vehicleConfigMismatch'));
    return;
  }

  allocationLoading.value = true;
  try {
    const params = {
      orderNumber: currentOrder.value.orderNumber,
      vin: selectedVehicle.value.vin
    };

    const response = await apiConfirmAllocation(params);
    if (response.success) {
      ElMessage.success(response.message);
      allocationModalVisible.value = false;
      loadData(); // 刷新列表
    } else {
      ElMessage.error(response.message || t('vehicleAllocation.allocationFailed'));
    }
  } catch (error) {
    console.error('配车失败:', error);
    ElMessage.error(t('vehicleAllocation.allocationFailed'));
  } finally {
    allocationLoading.value = false;
  }
};

const showCancelModal = (row: VehicleAllocationOrderItem) => {
  currentCancelOrder.value = row;
  cancelModalVisible.value = true;
};

const confirmCancelAllocation = async () => {
  if (!currentCancelOrder.value) return;

  cancelLoading.value = true;
  try {
    const params = {
      orderNumber: currentCancelOrder.value.orderNumber,
      reason: '用户手动取消配车'
    };

    const response = await apiCancelAllocation(params);
    if (response.success) {
      ElMessage.success(response.message);
      cancelModalVisible.value = false;
      loadData(); // 刷新列表
    } else {
      ElMessage.error(response.message || t('common.operationFailed'));
    }
  } catch (error) {
    console.error('取消配车失败:', error);
    ElMessage.error(t('common.operationFailed'));
  } finally {
    cancelLoading.value = false;
  }
};

const showRecordModal = async (row: VehicleAllocationOrderItem) => {
  currentRecordOrder.value = row;
  recordModalVisible.value = true;

  try {
    // 这里假设 getOrderAllocationDetail 可以获取到订单的基本信息，以便在记录详情弹窗中展示
    // 如果 API 不支持，可能需要从列表数据中直接取或者单独请求
    // currentRecordOrder.value = await getOrderAllocationDetail(row.orderNumber);

    const timeline = await getOrderAllocationTimeline(row.orderNumber);
    allocationTimeline.value = timeline;
  } catch (error) {
    console.error('加载配车记录失败:', error);
    ElMessage.error(t('common.networkError'));
  }
};

const getTimelineType = (result: string) => {
  return result === 'success' ? 'success' : 'danger';
};

// 组件挂载时执行
onMounted(() => {
  loadOptions();
  loadData();
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .operation-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.operation-buttons {
  text-align: right;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;

  .el-button {
    margin-left: 10px;
  }
}

.order-info-section, .vehicle-query-section, .available-vehicles-section {
  margin-bottom: 20px;

  h3 {
    margin-bottom: 15px;
    color: $primary-color;
  }
}

.order-info-grid {
  .el-col {
    margin-bottom: 10px;
  }
}

.record-order-info, .allocation-timeline {
  margin-bottom: 20px;

  h3 {
    margin-bottom: 15px;
    color: $primary-color;
  }
}

.timeline-content {
  .timeline-title {
    font-weight: bold;
    margin-bottom: 8px;
  }

  .timeline-details {
    p {
      margin: 4px 0;
      color: #666;
    }
  }
}

.no-timeline {
  text-align: center;
  padding: 20px;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
