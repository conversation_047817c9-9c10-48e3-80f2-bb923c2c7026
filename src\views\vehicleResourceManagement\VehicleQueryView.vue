<template>
  <div class="page-container">
    <h1 class="page-title">{{  t('vehicleQuery.title') }}</h1>

    <!-- 搜索/筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.vin')">
              <el-input v-model="searchParams.vin" :placeholder=" t('vehicleQuery.vinPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.factoryOrderNo')">
              <el-input v-model="searchParams.factoryOrderNo" :placeholder=" t('vehicleQuery.factoryOrderNoPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.warehouseName')">
              <el-select v-model="searchParams.warehouseName" :placeholder=" t('vehicleQuery.warehouseNamePlaceholder')" clearable>
                <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.model')">
              <el-select v-model="searchParams.model" :placeholder=" t('vehicleQuery.modelPlaceholder')" clearable @change="handleModelChange">
                <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.variant')">
              <el-select v-model="searchParams.variant" :placeholder=" t('vehicleQuery.variantPlaceholder')" clearable @change="handleVariantChange">
                <el-option v-for="item in variantOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.color')">
              <el-select v-model="searchParams.color" :placeholder=" t('vehicleQuery.colorPlaceholder')" clearable>
                <el-option v-for="item in colorOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleQuery.fmrId')">
              <el-input v-model="searchParams.fmrId" :placeholder="t('vehicleQuery.fmrIdPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.lockStatus')">
              <el-select v-model="searchParams.lockStatus" :placeholder=" t('vehicleQuery.lockStatusPlaceholder')" clearable>
                <el-option :label=" t('vehicleQuery.locked')" value="locked" />
                <el-option :label=" t('vehicleQuery.unlocked')" value="unlocked" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.invoiceStatus')">
              <el-select v-model="searchParams.invoiceStatus" :placeholder=" t('vehicleQuery.invoiceStatusPlaceholder')" clearable>
                <el-option :label=" t('vehicleQuery.invoiced')" value="invoiced" />
                <el-option :label=" t('vehicleQuery.notInvoiced')" value="notInvoiced" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.invoiceDate')">
              <el-date-picker
                v-model="dateRange.invoice"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="tCommon('startDate')"
                :end-placeholder="tCommon('endDate')"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.storageDate')">
              <el-date-picker
                v-model="dateRange.storage"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="tCommon('startDate')"
                :end-placeholder="tCommon('endDate')"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label=" t('vehicleQuery.productionDate')">
              <el-date-picker
                v-model="dateRange.production"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="tCommon('startDate')"
                :end-placeholder="tCommon('endDate')"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="buttons-col">
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ tCommon('search') }}</el-button>
            <el-button @click="resetSearch">{{ tCommon('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20 operation-card">
      <el-row>
        <el-col :span="24" class="operation-buttons-col">
          <el-button :icon="Download" @click="openExportDialog">{{ tCommon('export') }}</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <el-table :data="vehicleList" v-loading="loading" style="width: 100%">
        <el-table-column type="index" :label="tCommon('index')" width="60" />
        <el-table-column prop="factoryOrderNo" :label=" t('vehicleQuery.factoryOrderNo')" min-width="150" />
        <el-table-column prop="vin" :label=" t('vehicleQuery.vin')" min-width="150" />
        <el-table-column prop="model" :label=" t('vehicleQuery.model')" min-width="120" />
        <el-table-column prop="variant" :label=" t('vehicleQuery.variant')" min-width="120" />
        <el-table-column prop="color" :label=" t('vehicleQuery.color')" min-width="100" />
        <el-table-column prop="fmrId" :label="t('vehicleQuery.fmrId')" min-width="120" />
        <el-table-column prop="warehouseName" :label=" t('vehicleQuery.warehouseName')" min-width="120" />
        <el-table-column prop="stockStatus" :label=" t('vehicleQuery.stockStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getStockStatusTagType(row.stockStatus)">
              {{ t(`vehicleQuery.stockStatusOptions.${row.stockStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lockStatus" :label=" t('vehicleQuery.lockStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getLockStatusTagType(row.lockStatus)">
              {{ t(`vehicleQuery.lockStatusOptions.${row.lockStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceStatus" :label=" t('vehicleQuery.invoiceStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getInvoiceStatusTagType(row.invoiceStatus)">
              {{ t(`vehicleQuery.invoiceStatusOptions.${row.invoiceStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceDate" :label=" t('vehicleQuery.invoiceDate')" min-width="120" />
        <el-table-column prop="deliveryStatus" :label=" t('vehicleQuery.deliveryStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getDeliveryStatusTagType(row.deliveryStatus)">
              {{ t(`vehicleQuery.deliveryStatusOptions.${row.deliveryStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deliveryDate" :label=" t('vehicleQuery.deliveryDate')" min-width="120" />
        <el-table-column prop="storageDate" :label=" t('vehicleQuery.storageDate')" min-width="120" />
        <el-table-column prop="productionDate" :label=" t('vehicleQuery.productionDate')" min-width="120" />
        <el-table-column :label="tCommon('operations')" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="View" link @click="openDetailDialog(row)">{{ tCommon('detail') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态处理 -->
      <div v-if="!vehicleList.length && !loading" class="empty-state">
        <el-icon><el-icon-inbox /></el-icon>
        <h3>{{ tCommon('noData') }}</h3>
        <p>{{ tCommon('noDataTip') }}</p>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :layout="paginationLayout"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 车辆详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title=" t('vehicleQuery.detailDialogTitle')"
      width="600px"
      class="vehicle-detail-dialog"
    >
      <div v-if="currentVehicleDetail" class="detail-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.factoryOrderNo') }}:</strong> {{ currentVehicleDetail.factoryOrderNo }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.vin') }}:</strong> {{ currentVehicleDetail.vin }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.model') }}:</strong> {{ currentVehicleDetail.model }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.variant') }}:</strong> {{ currentVehicleDetail.variant }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.color') }}:</strong> {{ currentVehicleDetail.color }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{ t('vehicleQuery.fmrId') }}:</strong> {{ currentVehicleDetail.fmrId }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.warehouseName') }}:</strong> {{ currentVehicleDetail.warehouseName }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.stockStatus') }}:</strong>
              <el-tag :type="getStockStatusTagType(currentVehicleDetail.stockStatus)">
                {{ t(`vehicleQuery.stockStatusOptions.${currentVehicleDetail.stockStatus}`) }}
              </el-tag>
            </p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.lockStatus') }}:</strong>
              <el-tag :type="getLockStatusTagType(currentVehicleDetail.lockStatus)">
                {{ t(`vehicleQuery.lockStatusOptions.${currentVehicleDetail.lockStatus}`) }}
              </el-tag>
            </p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.invoiceStatus') }}:</strong>
              <el-tag :type="getInvoiceStatusTagType(currentVehicleDetail.invoiceStatus)">
                {{ t(`vehicleQuery.invoiceStatusOptions.${currentVehicleDetail.invoiceStatus}`) }}
              </el-tag>
            </p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.invoiceDate') }}:</strong> {{ currentVehicleDetail.invoiceDate }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.deliveryStatus') }}:</strong>
              <el-tag :type="getDeliveryStatusTagType(currentVehicleDetail.deliveryStatus)">
                {{ t(`vehicleQuery.deliveryStatusOptions.${currentVehicleDetail.deliveryStatus}`) }}
              </el-tag>
            </p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.deliveryDate') }}:</strong> {{ currentVehicleDetail.deliveryDate }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.storageDate') }}:</strong> {{ currentVehicleDetail.storageDate }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>{{  t('vehicleQuery.productionDate') }}:</strong> {{ currentVehicleDetail.productionDate }}</p>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="detailDialogVisible = false">{{ tCommon('close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导出数据弹窗 -->
    <el-dialog
      v-model="exportDialogVisible"
      :title=" t('vehicleQuery.exportDialogTitle')"
      width="400px"
      class="export-data-dialog"
    >
      <el-form :model="exportForm" label-position="top">
        <el-form-item :label=" t('vehicleQuery.exportFormat')">
          <el-select v-model="exportForm.format" :placeholder=" t('vehicleQuery.exportFormatPlaceholder')">
            <el-option :label=" t('vehicleQuery.excel')" value="excel" />
            <el-option :label=" t('vehicleQuery.csv')" value="csv" />
          </el-select>
        </el-form-item>
        <el-form-item :label=" t('vehicleQuery.exportScope')">
          <el-radio-group v-model="exportForm.scope">
            <el-radio label="current">{{  t('vehicleQuery.exportCurrentPage') }}</el-radio>
            <el-radio label="all">{{  t('vehicleQuery.exportAllData') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="exportDialogVisible = false">{{ tCommon('cancel') }}</el-button>
          <el-button type="primary" @click="confirmExport">{{ tCommon('confirmExport') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search, Download, View, Box as ElIconInbox } from '@element-plus/icons-vue';
import { ElMessage, ElNotification } from 'element-plus';
import type { VehicleListItem, VehicleSearchParams, VehicleDetail } from '@/types/vehicleQuery';
import { getVehicleList, getWarehouseInfo } from '@/api/modules/vehicleQuery';
import { getVehicleModelList, getVehicleVariantList } from '@/api/modules/masterData';


// 车辆配置类型定义
interface VehicleVariant {
  variant: string;
  colorOptions: string[];
}

interface VehicleConfiguration {
  model: string;
  variants: VehicleVariant[];
}

// 国际化
const { t } = useModuleI18n('sales');
const { t: tCommon } = useModuleI18n('common');

// --- 搜索条件相关 ---
const searchParams = reactive<VehicleSearchParams>({
  vin: '',
  factoryOrderNo: '',
  warehouseName: '',
  model: '',
  variant: '',
  color: '',
  fmrId: '', // 新增FMRID
  lockStatus: undefined,
  invoiceStatus: undefined,
});

const dateRange = reactive({
  invoice: null as [string, string] | null,
  storage: null as [string, string] | null,
  production: null as [string, string] | null,
});

const warehouseOptions = ref<{ label: string; value: string }[]>([]);
const modelOptions = ref<{ label: string; value: string }[]>([]);
const allConfigurations = ref<VehicleConfiguration[]>([]); // 存储所有配置数据用于级联
const variantOptions = ref<{ label: string; value: string }[]>([]);
const colorOptions = ref<{ label: string; value: string }[]>([]);

// 处理车型选择变化
const handleModelChange = (val: string) => {
  searchParams.variant = '';
  searchParams.color = '';
  updateVariantOptions(val);
  colorOptions.value = []; // 清空颜色选项
};

// 处理配置选择变化
const handleVariantChange = (val: string) => {
  searchParams.color = '';
  updateColorOptions(searchParams.model || '', val);
};

// 更新 Variant 选项
const updateVariantOptions = (model: string) => {
  const selectedModel = allConfigurations.value.find(config => config.model === model);
  if (selectedModel) {
    variantOptions.value = selectedModel.variants.map((v: VehicleVariant) => ({ label: v.variant, value: v.variant }));
  } else {
    variantOptions.value = [];
  }
};

// 更新 Color 选项
const updateColorOptions = (model: string, variant: string) => {
  const selectedModel = allConfigurations.value.find(config => config.model === model);
  if (selectedModel) {
    const selectedVariant = selectedModel.variants.find((v: VehicleVariant) => v.variant === variant);
    if (selectedVariant) {
      colorOptions.value = selectedVariant.colorOptions.map((c: string) => ({ label: c, value: c }));
    } else {
      colorOptions.value = [];
    }
  } else {
    colorOptions.value = [];
  }
};

// 搜索
const handleSearch = () => {
  console.log('搜索参数:', searchParams, dateRange);
  // 组装日期范围到 searchParams
  searchParams.invoiceDateStart = dateRange.invoice?.[0];
  searchParams.invoiceDateEnd = dateRange.invoice?.[1];
  searchParams.storageDateStart = dateRange.storage?.[0];
  searchParams.storageDateEnd = dateRange.storage?.[1];
  searchParams.productionDateStart = dateRange.production?.[0];
  searchParams.productionDateEnd = dateRange.production?.[1];

  pagination.pageNum = 1; // 搜索时重置页码
  fetchVehicleList();
};

// 重置
const resetSearch = () => {
  Object.assign(searchParams, {
    vin: '',
    factoryOrderNo: '',
    warehouseName: '',
    model: '',
    variant: '',
    color: '',
    fmrId: '', // 新增FMRID
    lockStatus: undefined,
    invoiceStatus: undefined,
    invoiceDateStart: undefined,
    invoiceDateEnd: undefined,
    storageDateStart: undefined,
    storageDateEnd: undefined,
    productionDateStart: undefined,
    productionDateEnd: undefined,
  });
  dateRange.invoice = null;
  dateRange.storage = null;
  dateRange.production = null;
  variantOptions.value = [];
  colorOptions.value = [];
  handleSearch(); // 重置后执行一次搜索
};

// --- 表格数据相关 ---
const vehicleList = ref<VehicleListItem[]>([]);
const loading = ref(false);

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
});

const paginationLayout = computed(() => {
  return tCommon('paginationLayout'); // 使用国际化获取布局字符串
});


// 获取车辆列表
const fetchVehicleList = async () => {
  console.log('开始获取车辆列表数据...');
  loading.value = true;
  try {
    // 过滤空值参数，只传递有值的参数
    const filteredSearchParams: Partial<VehicleSearchParams> = {};

    // 遍历搜索参数，只添加非空值
    (Object.keys(searchParams) as (keyof VehicleSearchParams)[]).forEach((key) => {
      const value = searchParams[key];
      if (value !== '' && value !== null && value !== undefined) {
        filteredSearchParams[key] = value;
      }
    });

    const requestParams = {
      ...filteredSearchParams,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    };

    console.log('过滤后的请求参数:', requestParams);
    const response = await getVehicleList(requestParams);
    console.log('获取到的车辆数据:', response);
    vehicleList.value = response.result.records;
    pagination.total = response.result.total;
    console.log('车辆列表更新完成, 数据长度:', vehicleList.value.length);
  } catch (error) {
    console.error('获取车辆列表失败:', error);
    ElMessage.error(tCommon('fetchFailed'));
  } finally {
    loading.value = false;
  }
};

// 处理每页显示条数变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  fetchVehicleList();
};

// 处理当前页码变化
const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  fetchVehicleList();
};

// 根据库存状态获取标签类型
const getStockStatusTagType = (status: VehicleListItem['stockStatus']) => {
  switch (status) {
    case 'inStock': return 'success'; // 在库
    case 'allocated': return 'warning'; // 配车
    case 'inTransit': return 'primary'; // 在途
    case 'transferred': return 'info'; // 调拨
    default: return 'primary';
  }
};

// 根据锁定状态获取标签类型
const getLockStatusTagType = (status: VehicleListItem['lockStatus']) => {
  switch (status) {
    case 'locked': return 'danger'; // 已锁定
    case 'unlocked': return 'success'; // 未锁定
    default: return 'primary';
  }
};

// 根据开票状态获取标签类型
const getInvoiceStatusTagType = (status: VehicleListItem['invoiceStatus']) => {
  switch (status) {
    case 'invoiced': return 'success'; // 已开票
    case 'notInvoiced': return 'info'; // 未开票
    default: return 'primary';
  }
};

// 根据交车状态获取标签类型
const getDeliveryStatusTagType = (status: VehicleListItem['deliveryStatus']) => {
  switch (status) {
    case 'delivered': return 'success'; // 已交车
    case 'notDelivered': return 'info'; // 未交车
    default: return '';
  }
};

// --- 详情弹窗相关 ---
const detailDialogVisible = ref(false);
const currentVehicleDetail = ref<VehicleDetail | null>(null);

const openDetailDialog = (row: VehicleListItem) => {
  currentVehicleDetail.value = row; // 在实际应用中，这里会根据row.vin调用API获取详细信息
  detailDialogVisible.value = true;
};

// --- 导出弹窗相关 ---
const exportDialogVisible = ref(false);
const exportForm = reactive({
  format: 'excel',
  scope: 'current',
});

const openExportDialog = () => {
  exportDialogVisible.value = true;
};

const confirmExport = () => {
  console.log('导出参数:', exportForm);
  // 模拟导出操作
  ElNotification({
    title: tCommon('success'),
    message:  t('vehicleQuery.exportSuccess', { format: exportForm.format }),
    type: 'success',
  });
  exportDialogVisible.value = false;
};

// --- 生命周期钩子 ---
onMounted(async () => {
  console.log('VehicleQueryView 组件已挂载，开始初始化...');

  // 初始化获取仓库信息
  try {
    console.log('正在获取仓库信息...');
    const warehouses = await getWarehouseInfo();
    console.log('获取到仓库信息:', warehouses);
    warehouseOptions.value = warehouses.result.map(w => ({ label: w.warehouseName, value: w.code }));
  } catch (error) {
    console.error('获取仓库信息失败:', error);
    ElMessage.error( t('vehicleQuery.fetchWarehouseFailed'));
  }

  // 初始化获取车型配置信息
  try {
    console.log('正在获取车型配置信息...');
    allConfigurations.value = await getVehicleModelList();
    console.log('获取到车型配置:', allConfigurations.value);
    modelOptions.value = allConfigurations.value.map(config => ({ label: config.modelName, value: config.code }));
  } catch (error) {
    console.error('获取车型配置失败:', error);
    ElMessage.error( t('vehicleQuery.fetchConfigFailed'));
  }

  console.log('开始获取初始车辆列表...');
  fetchVehicleList(); // 页面加载时获取初始列表
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;

  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    color: #333;
  }

  .search-card,
  .operation-card,
  .table-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .search-form {
    .el-form-item {
      margin-right: 20px;
      margin-bottom: 15px; // 表单项底部间距
      &:last-child {
        margin-right: 0;
      }
    }

    // 确保日期选择器宽度自适应
    .el-date-editor {
      width: 100%;
    }
  }

  .buttons-col {
    text-align: right;
    .el-button {
      margin-left: 10px;
      &:first-child {
        margin-left: 0; // 第一个按钮不需要左边距
      }
    }
  }

  .operation-buttons-col {
    text-align: right;
    .el-button {
      margin-left: 10px;
      &:first-child {
        margin-left: 0; // 第一个按钮不需要左边距
      }
    }
  }

  .table-card {
    margin-bottom: 20px;
    :deep(.el-table) {
      .el-table__body td,
      .el-table__header th {
        white-space: nowrap; // 禁止文本换行
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    color: #909399;
    .el-icon {
      font-size: 60px;
      margin-bottom: 20px;
    }
    h3 {
      margin: 0 0 10px 0;
      color: #909399;
    }
    p {
      margin: 0;
      font-size: 14px;
      color: #c0c4cc;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  // Dialog styles
  .el-dialog {
    border-radius: 8px;
    .el-dialog__header {
      border-bottom: 1px solid #eee;
      padding-bottom: 15px;
      margin-right: 0; // 移除 Element Plus 默认的右边距
    }
    .el-dialog__body {
      padding: 20px;
    }
    .el-dialog__footer {
      border-top: 1px solid #eee;
      padding-top: 15px;
    }
  }

  .detail-content {
    .el-col {
      margin-bottom: 10px;
    }
    strong {
      color: #606266;
      margin-right: 5px;
    }
    p {
      margin: 0;
      display: flex;
      align-items: center;
      flex-wrap: wrap; // 允许内容换行
    }
    .el-tag {
      margin-left: 5px; // 标签与文本之间的间距
    }
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
    .el-button {
      margin-left: 10px;
      &:first-child {
        margin-left: 0;
      }
    }
  }
}

// 统一表格字体不换行
:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap; // 禁止文本换行
  }
}
</style>
