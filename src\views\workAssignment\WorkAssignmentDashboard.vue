<template>
  <div class="work-assignment-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ $t('workAssignment.dashboard.title') }}</h1>
      <div class="header-actions">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          @click="refreshData"
          :loading="loading"
        >
          {{ $t('workAssignment.common.refresh') }}
        </el-button>
        <el-switch
          v-model="autoRefresh"
          :active-text="$t('workAssignment.dashboard.autoRefresh')"
          @change="handleAutoRefreshChange"
        />
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card pending">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.pendingAssignment }}</div>
              <div class="stat-label">{{ $t('workAssignment.dashboard.pendingAssignment') }}</div>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card assigned">
            <div class="stat-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.assignedOrders }}</div>
              <div class="stat-label">{{ $t('workAssignment.dashboard.assignedOrders') }}</div>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card progress">
            <div class="stat-icon">
              <el-icon><Tools /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.inProgressOrders }}</div>
              <div class="stat-label">{{ $t('workAssignment.dashboard.inProgressOrders') }}</div>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card completed">
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.completedOrders }}</div>
              <div class="stat-label">{{ $t('workAssignment.dashboard.completedOrders') }}</div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 第二行统计 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="6">
          <div class="stat-card technicians">
            <div class="stat-icon">
              <el-icon><Avatar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">
                {{ statistics.availableTechnicians }}/{{ statistics.totalTechnicians }}
              </div>
              <div class="stat-label">{{ $t('workAssignment.dashboard.availableTechnicians') }}</div>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card efficiency">
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.averageEfficiency }}%</div>
              <div class="stat-label">{{ $t('workAssignment.dashboard.averageEfficiency') }}</div>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card utilization">
            <div class="stat-icon">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.utilizationRate }}%</div>
              <div class="stat-label">{{ $t('workAssignment.dashboard.utilizationRate') }}</div>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="stat-card waiting-time">
            <div class="stat-icon">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statistics.averageWaitingTime }}{{ $t('workAssignment.common.minutes') }}</div>
              <div class="stat-label">{{ $t('workAssignment.dashboard.averageWaitingTime') }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 技师工作安排时间线 -->
    <div class="timeline-section">
      <div class="section-header">
        <h2 class="section-title">技师工作安排</h2>
        <div class="section-actions">
          <div class="legend">
            <span class="legend-item">
              <span class="legend-color urgent"></span>
              紧急
            </span>
            <span class="legend-item">
              <span class="legend-color maintenance"></span>
              保养
            </span>
            <span class="legend-item">
              <span class="legend-color repair"></span>
              维修
            </span>
            <span class="legend-item">
              <span class="legend-color insurance"></span>
              保险
            </span>
            <span class="legend-item">
              <span class="legend-color available"></span>
              空闲
            </span>
          </div>
          <el-button type="primary" size="small">今天</el-button>
          <el-button size="small">本周</el-button>
        </div>
      </div>

      <div class="schedule-container">
        <!-- 时间轴头部 -->
        <div class="schedule-header">
          <div class="technician-column">技师</div>
          <div class="time-grid">
            <div
              v-for="hour in timeSlots"
              :key="hour"
              class="time-slot"
            >
              {{ hour.toString().padStart(2, '0') }}:00
            </div>
          </div>
        </div>

        <!-- 技师工作安排 -->
        <div class="schedule-body">
          <div
            v-for="(technician, index) in mockTechnicianData"
            :key="technician.name"
            class="technician-row"
          >
            <div class="technician-info">
              <div class="technician-name">{{ technician.name }}</div>
              <div class="technician-status">
                <span class="status-text">{{ technician.status }}</span>
              </div>
              <div class="workload-info">
                <span class="workload-text">工作负荷: {{ technician.workload }}%</span>
                <div class="workload-bar">
                  <div
                    class="workload-fill"
                    :style="{ width: technician.workload + '%', backgroundColor: getWorkloadColor(technician.workload) }"
                  ></div>
                </div>
              </div>
            </div>


            <div class="work-timeline">
              <!-- 工作项目 -->
              <div
                v-for="workItem in technician.workItems"
                :key="workItem.id"
                class="work-item"
                :class="[workItem.type, workItem.priority]"
                :style="getWorkItemPosition(workItem)"
                @click="handleWorkItemClick(workItem)"
              >
                <div class="work-content">
                  <div class="work-order-no">{{ workItem.orderNo }}</div>
                  <div class="work-status">{{ workItem.status }}</div>
                </div>
              </div>


              <!-- 空闲时间段 -->
              <div
                v-for="(slot, slotIndex) in technician.availableSlots"
                :key="`available-${slotIndex}`"
                class="available-slot"
                :style="getAvailableSlotPosition(slot)"
              >
                <div class="available-text">空闲</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工单状态分布图表 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>{{ $t('workAssignment.dashboard.orderStatusDistribution') }}</h3>
            </div>
            <div class="chart-content">
              <div ref="statusChartRef" style="height: 300px;"></div>
            </div>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>{{ $t('workAssignment.dashboard.technicianWorkload') }}</h3>
            </div>
            <div class="chart-content">
              <div ref="workloadChartRef" style="height: 300px;"></div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 工单详情弹窗 -->
    <el-dialog
      v-model="workItemDialog.visible"
      :title="$t('workAssignment.dashboard.workOrderDetail')"
      width="600px"
    >
      <div v-if="workItemDialog.workItem">
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="$t('workAssignment.workOrder.workOrderNo')">
            {{ workItemDialog.workItem.workOrderId }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('workAssignment.workOrder.customerName')">
            {{ workItemDialog.workItem.customerName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('workAssignment.workOrder.vehicleInfo')">
            {{ workItemDialog.workItem.licensePlate }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('workAssignment.workOrder.type')">
            <el-tag :type="getWorkOrderTypeTagType(workItemDialog.workItem.workOrderType)">
              {{ getWorkOrderTypeLabel(workItemDialog.workItem.workOrderType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('workAssignment.workOrder.priority')">
            <el-tag :type="getPriorityTagType(workItemDialog.workItem.priority)">
              {{ getPriorityLabel(workItemDialog.workItem.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('workAssignment.workOrder.status')">
            <el-tag :type="getStatusTagType(workItemDialog.workItem.status)">
              {{ getStatusLabel(workItemDialog.workItem.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('workAssignment.workOrder.estimatedDuration')">
            {{ workItemDialog.workItem.estimatedWorkHours }}{{ $t('workAssignment.common.hours') }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('workAssignment.workOrder.scheduledTime')">
            {{ formatTime(workItemDialog.workItem.estimatedStartTime) }} - {{ formatTime(workItemDialog.workItem.estimatedFinishTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  Clock,
  User,
  Tools,
  CircleCheck,
  Avatar,
  TrendCharts,
  DataLine,
  Timer
} from '@element-plus/icons-vue'
import type {
  AssignmentStatistics,
  TechnicianSchedule,
  TechnicianTimelineItem,
  WorkOrderStatus,
  WorkOrderType,
  WorkOrderPriority
} from '@/types/module'
import * as workAssignmentApi from '@/api/modules/workAssignment'
import * as echarts from 'echarts'

const { t } = useI18n()

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)

// 统计数据
const statistics = ref<AssignmentStatistics>({
  statsDate: '',
  totalOrders: 0,
  pendingAssignment: 0,
  assignedOrders: 0,
  inProgressOrders: 0,
  completedOrders: 0,
  cancelledOrders: 0,
  totalTechnicians: 0,
  availableTechnicians: 0,
  busyTechnicians: 0,
  overloadedTechnicians: 0,
  averageAssignmentTime: 0,
  averageWaitingTime: 0,
  onTimeCompletionRate: 0,
  reassignmentRate: 0,
  totalEstimatedHours: 0,
  totalActualHours: 0,
  averageEfficiency: 0,
  utilizationRate: 0
})

// 技师时间安排
const technicianSchedules = ref<TechnicianSchedule[]>([])

// 时间轴相关
const timeSlots = Array.from({ length: 12 }, (_, i) => 8 + i) // 8:00 - 19:00

// 模拟技师数据
const mockTechnicianData = ref([
  {
    name: '张师傅',
    status: '忙碌',
    workload: 75,
    workItems: [
      {
        id: 'A12345',
        orderNo: 'A12345',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 8,
        endTime: 10,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'A12346',
        orderNo: 'A12346',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 10,
        endTime: 12,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'A12347',
        orderNo: 'A12347',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 12,
        endTime: 13,
        startMinute: 0,
        endMinute: 0
      }
    ],
    availableSlots: [
      { startTime: 13, endTime: 14, startMinute: 0, endMinute: 0 },
      { startTime: 14, endTime: 16, startMinute: 0, endMinute: 0 },
      { startTime: 16, endTime: 18, startMinute: 0, endMinute: 0 }
    ]
  },
  {
    name: '李师傅',
    status: '空闲',
    workload: 0,
    workItems: [],
    availableSlots: [
      { startTime: 8, endTime: 19, startMinute: 0, endMinute: 0 }
    ]
  },
  {
    name: '王师傅',
    status: '空闲',
    workload: 0,
    workItems: [
      {
        id: 'B67890',
        orderNo: 'B67890',
        type: 'maintenance',
        priority: 'normal',
        status: '保养',
        startTime: 9,
        endTime: 11,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'D22222',
        orderNo: 'D22222',
        type: 'repair',
        priority: 'normal',
        status: '维修',
        startTime: 14,
        endTime: 15,
        startMinute: 0,
        endMinute: 0
      }
    ],
    availableSlots: [
      { startTime: 8, endTime: 9, startMinute: 0, endMinute: 0 },
      { startTime: 11, endTime: 14, startMinute: 0, endMinute: 0 },
      { startTime: 15, endTime: 19, startMinute: 0, endMinute: 0 }
    ]
  },
  {
    name: '赵师傅',
    status: '空闲',
    workload: 0,
    workItems: [
      {
        id: 'E55555',
        orderNo: 'E55555',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 13,
        endTime: 15,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'E55556',
        orderNo: 'E55556',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 15,
        endTime: 17,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'E55557',
        orderNo: 'E55557',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 17,
        endTime: 19,
        startMinute: 0,
        endMinute: 0
      }
    ],
    availableSlots: [
      { startTime: 8, endTime: 13, startMinute: 0, endMinute: 0 }
    ]
  },
  {
    name: '孙师傅',
    status: '空闲',
    workload: 0,
    workItems: [
      {
        id: 'F66666',
        orderNo: 'F66666',
        type: 'maintenance',
        priority: 'normal',
        status: '保养',
        startTime: 8,
        endTime: 10,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'F66667',
        orderNo: 'F66667',
        type: 'maintenance',
        priority: 'normal',
        status: '保养',
        startTime: 10,
        endTime: 12,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'G77777',
        orderNo: 'G77777',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 12,
        endTime: 14,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'G77778',
        orderNo: 'G77778',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 14,
        endTime: 16,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'I99999',
        orderNo: 'I99999',
        type: 'maintenance',
        priority: 'normal',
        status: '保养',
        startTime: 16,
        endTime: 18,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'I99998',
        orderNo: 'I99998',
        type: 'maintenance',
        priority: 'normal',
        status: '保养',
        startTime: 18,
        endTime: 19,
        startMinute: 0,
        endMinute: 0
      }
    ],
    availableSlots: []
  },
  {
    name: '周师傅',
    status: '空闲',
    workload: 0,
    workItems: [
      {
        id: 'K11111',
        orderNo: 'K11111',
        type: 'maintenance',
        priority: 'normal',
        status: '保养',
        startTime: 8,
        endTime: 10,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'K11112',
        orderNo: 'K11112',
        type: 'maintenance',
        priority: 'normal',
        status: '保养',
        startTime: 10,
        endTime: 12,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'L22222',
        orderNo: 'L22222',
        type: 'urgent',
        priority: 'urgent',
        status: '紧急',
        startTime: 12,
        endTime: 14,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'M33333',
        orderNo: 'M33333',
        type: 'insurance',
        priority: 'normal',
        status: '保险',
        startTime: 14,
        endTime: 16,
        startMinute: 0,
        endMinute: 0
      },
      {
        id: 'M33334',
        orderNo: 'M33334',
        type: 'insurance',
        priority: 'normal',
        status: '保险',
        startTime: 16,
        endTime: 18,
        startMinute: 0,
        endMinute: 0
      }
    ],
    availableSlots: [
      { startTime: 18, endTime: 19, startMinute: 0, endMinute: 0 }
    ]
  }
])

// 工单详情弹窗
const workItemDialog = reactive({
  visible: false,
  workItem: null as TechnicianTimelineItem | null
})

// 图表引用
const statusChartRef = ref<HTMLDivElement>()
const workloadChartRef = ref<HTMLDivElement>()
let statusChart: echarts.ECharts | null = null
let workloadChart: echarts.ECharts | null = null

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null



// 获取工作负荷颜色
const getWorkloadColor = (percentage: number) => {
  if (percentage < 50) return '#67C23A'
  if (percentage < 80) return '#E6A23C'
  return '#F56C6C'
}

// 获取工单类型标签类型
const getWorkOrderTypeTagType = (type: WorkOrderType) => {
  const typeMap: Record<WorkOrderType, string> = {
    'maintenance': 'success',
    'repair': 'warning',
    'insurance': 'info'
  }
  return typeMap[type] || 'info'
}

// 获取工单类型标签文本
const getWorkOrderTypeLabel = (type: WorkOrderType) => {
  return t(`workAssignment.type.${type}`)
}

// 获取优先级标签类型
const getPriorityTagType = (priority: WorkOrderPriority) => {
  const typeMap: Record<WorkOrderPriority, string> = {
    'normal': 'info',
    'urgent': 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取优先级标签文本
const getPriorityLabel = (priority: WorkOrderPriority) => {
  return t(`workAssignment.priority.${priority}`)
}

// 获取状态标签类型
const getStatusTagType = (status: WorkOrderStatus) => {
  const typeMap: Record<WorkOrderStatus, string> = {
    'draft': 'info',
    'pending_confirm': 'warning',
    'confirmed': 'info',
    'pending_assign': 'warning',
    'pending_start': 'info',
    'in_progress': 'primary',
    'pending_qc': 'warning',
    'pending_settle': 'warning',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: WorkOrderStatus) => {
  const statusMap: Record<WorkOrderStatus, string> = {
    'draft': 'draft',
    'pending_confirm': 'pendingConfirm',
    'confirmed': 'confirmed',
    'pending_assign': 'pendingAssignment',
    'pending_start': 'assigned',
    'in_progress': 'inProgress',
    'pending_qc': 'pendingQc',
    'pending_settle': 'pendingSettle',
    'completed': 'completed',
    'cancelled': 'cancelled'
  }
  return t(`workAssignment.status.${statusMap[status]}`)
}

// 格式化时间
const formatTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 工作项接口
interface WorkItem {
  id: string
  orderNo: string
  type: string
  priority: string
  status: string
  startTime: number
  endTime: number
  startMinute: number
  endMinute: number
}

// 空闲时间段接口
interface AvailableSlot {
  startTime: number
  endTime: number
  startMinute: number
  endMinute: number
}

// 获取工作项位置样式
const getWorkItemPosition = (workItem: WorkItem) => {
  const totalHours = 11 // 8:00 - 19:00
  const startOffset = (workItem.startTime - 8) + (workItem.startMinute / 60)
  const duration = (workItem.endTime - workItem.startTime) + ((workItem.endMinute - workItem.startMinute) / 60)

  const left = (startOffset / totalHours) * 100
  const width = (duration / totalHours) * 100

  return {
    left: `${left}%`,
    width: `${width}%`
  }
}

// 获取空闲时间段位置样式
const getAvailableSlotPosition = (slot: AvailableSlot) => {
  const totalHours = 11 // 8:00 - 19:00
  const startOffset = (slot.startTime - 8) + (slot.startMinute / 60)
  const duration = (slot.endTime - slot.startTime) + ((slot.endMinute - slot.startMinute) / 60)

  const left = (startOffset / totalHours) * 100
  const width = (duration / totalHours) * 100

  return {
    left: `${left}%`,
    width: `${width}%`
  }
}

// 处理工作项点击
const handleWorkItemClick = (workItem: TechnicianTimelineItem) => {
  workItemDialog.workItem = workItem
  workItemDialog.visible = true
}



// 处理自动刷新变化
const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  refreshTimer = setInterval(() => {
    loadData()
  }, 30000) // 30秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const res = await workAssignmentApi.getAssignmentStatistics()
    statistics.value = res
  } catch (error) {
    console.error('Load statistics failed:', error)
  }
}

// 加载技师时间安排
const loadTechnicianSchedules = async () => {
  try {
    const res = await workAssignmentApi.getTechnicianSchedules({ date: selectedDate.value })
    technicianSchedules.value = res
  } catch (error) {
    console.error('Load technician schedules failed:', error)
  }
}

// 初始化状态分布图表
const initStatusChart = () => {
  if (!statusChartRef.value) return

  statusChart = echarts.init(statusChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: t('workAssignment.dashboard.orderStatus'),
        type: 'pie',
        radius: '60%',
        data: [
          { value: statistics.value.pendingAssignment, name: t('workAssignment.dashboard.pendingAssignment') },
          { value: statistics.value.assignedOrders, name: t('workAssignment.dashboard.assignedOrders') },
          { value: statistics.value.inProgressOrders, name: t('workAssignment.dashboard.inProgressOrders') },
          { value: statistics.value.completedOrders, name: t('workAssignment.dashboard.completedOrders') }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  statusChart.setOption(option)
}

// 初始化工作负荷图表
const initWorkloadChart = () => {
  if (!workloadChartRef.value) return

  workloadChart = echarts.init(workloadChartRef.value)

  const technicianNames = technicianSchedules.value.map(s => s.technicianInfo.technicianName)
  const workloadData = technicianSchedules.value.map(s => s.workloadPercentage)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: technicianNames,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: t('workAssignment.dashboard.workloadPercentage'),
      max: 100
    },
    series: [
      {
        name: t('workAssignment.dashboard.workload'),
        type: 'bar',
        data: workloadData,
        itemStyle: {
          color: function(params: { value: number }) {
            const value = params.value
            if (value < 50) return '#67C23A'
            if (value < 80) return '#E6A23C'
            return '#F56C6C'
          }
        }
      }
    ]
  }

  workloadChart.setOption(option)
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStatistics(),
      loadTechnicianSchedules()
    ])


    // 等待DOM更新后初始化图表
    await nextTick()
    initStatusChart()
    initWorkloadChart()
  } catch (error) {
    ElMessage.error(t('workAssignment.messages.loadDataFailed'))
    console.error('Load data failed:', error)
  } finally {
    loading.value = false
  }
}

// 更新图表数据
const updateCharts = () => {
  if (statusChart) {
    const option = statusChart.getOption()
    option.series[0].data = [
      { value: statistics.value.pendingAssignment, name: t('workAssignment.dashboard.pendingAssignment') },
      { value: statistics.value.assignedOrders, name: t('workAssignment.dashboard.assignedOrders') },
      { value: statistics.value.inProgressOrders, name: t('workAssignment.dashboard.inProgressOrders') },
      { value: statistics.value.completedOrders, name: t('workAssignment.dashboard.completedOrders') }
    ]
    statusChart.setOption(option)
  }

  if (workloadChart) {
    const technicianNames = technicianSchedules.value.map(s => s.technicianInfo.technicianName)
    const workloadData = technicianSchedules.value.map(s => s.workloadPercentage)
    const option = workloadChart.getOption()
    option.xAxis[0].data = technicianNames
    option.series[0].data = workloadData
    workloadChart.setOption(option)
  }
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 监听统计数据变化，更新图表
watch(statistics, () => {
  nextTick(() => {
    updateCharts()
  })
}, { deep: true })

// 监听技师安排数据变化，更新图表
watch(technicianSchedules, () => {
  nextTick(() => {
    updateCharts()
  })
}, { deep: true })

// 组件挂载时加载数据
onMounted(() => {
  loadData()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
  if (statusChart) {
    statusChart.dispose()
  }
  if (workloadChart) {
    workloadChart.dispose()
  }
})
</script>

<style scoped lang="scss">
.work-assignment-dashboard {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .stats-section {
    margin-bottom: 30px;

    .stat-card {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .el-icon {
          font-size: 24px;
          color: #fff;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }

      &.pending .stat-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.assigned .stat-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.progress .stat-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      &.completed .stat-icon {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }

      &.technicians .stat-icon {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      }

      &.efficiency .stat-icon {
        background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
      }

      &.utilization .stat-icon {
        background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
      }

      &.waiting-time .stat-icon {
        background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
      }
    }
  }

  .timeline-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;


    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #ebeef5;


      .section-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }


      .section-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .legend {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-right: 20px;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #666;

            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 2px;

              &.urgent {
                background: #f56c6c;
              }

              &.maintenance {
                background: #67c23a;
              }

              &.repair {
                background: #e6a23c;
              }

              &.insurance {
                background: #909399;
              }

              &.available {
                background: #409eff;
              }
            }
          }
        }
      }
    }

    .schedule-container {
      .schedule-header {
        display: flex;
        border-bottom: 2px solid #dcdfe6;
        background: #f8f9fa;

        .technician-column {
          width: 200px;
          padding: 12px 20px;
          font-weight: 600;
          color: #303133;
          border-right: 1px solid #dcdfe6;
          background: #f5f7fa;
        }

        .time-grid {
          flex: 1;
          display: flex;


          .time-slot {
            flex: 1;
            padding: 12px 8px;
            text-align: center;
            font-size: 12px;
            font-weight: 500;
            color: #606266;
            border-right: 1px solid #dcdfe6;
            background: #f8f9fa;

            &:last-child {
              border-right: none;
            }
          }
        }
      }

      .schedule-body {
        .technician-row {
          display: flex;
          border-bottom: 1px solid #ebeef5;
          min-height: 80px;


          &:last-child {
            border-bottom: none;
          }

          &:nth-child(even) {
            background: #fafafa;
          }

          .technician-info {
            width: 200px;
            padding: 16px 20px;
            border-right: 1px solid #ebeef5;
            display: flex;
            flex-direction: column;
            justify-content: center;


            .technician-name {
              font-weight: 600;
              margin-bottom: 6px;
              color: #303133;
            }


            .technician-status {
              margin-bottom: 8px;

              .status-text {
                font-size: 12px;
                color: #909399;
              }
            }


            .workload-info {
              .workload-text {
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
                display: block;
              }

              .workload-bar {
                width: 80px;
                height: 4px;
                background: #e4e7ed;
                border-radius: 2px;
                overflow: hidden;

                .workload-fill {
                  height: 100%;
                  transition: width 0.3s ease;
                }
              }
            }
          }


          .work-timeline {
            flex: 1;
            position: relative;
            padding: 8px 0;
            min-height: 64px;

            .work-item {
              position: absolute;
              top: 8px;
              height: 48px;
              border-radius: 4px;
              color: #fff;
              padding: 6px 8px;
              font-size: 11px;
              cursor: pointer;
              transition: all 0.3s;
              display: flex;
              flex-direction: column;
              justify-content: center;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                z-index: 10;
              }


              &.urgent {
                background: #f56c6c;
              }

              &.maintenance {
                background: #67c23a;
              }

              &.repair {
                background: #e6a23c;
              }

              &.insurance {
                background: #909399;
              }

              .work-content {
                .work-order-no {
                  font-weight: 600;
                  margin-bottom: 2px;
                  font-size: 10px;
                }

                .work-status {
                  font-size: 10px;
                  opacity: 0.9;
                }
              }
            }


            .available-slot {
              position: absolute;
              top: 8px;
              height: 48px;
              background: #409eff;
              border-radius: 4px;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 11px;
              opacity: 0.6;


              .available-text {
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }

  .charts-section {
    .chart-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .chart-header {
        padding: 20px 20px 0;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .chart-content {
        padding: 20px;
      }
    }
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.6;
  }
}

:deep(.el-switch__label) {
  color: #606266;
}
</style>
