<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="approval-detail-modal">
      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <el-result
          icon="error"
          :title="$t('workOrderApproval.loadDetailFailed')"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="loadApprovalDetail">
              {{ $t('common.retry') }}
            </el-button>
          </template>
        </el-result>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-state">
        <div style="text-align: center; padding: 50px;">
          <el-icon class="is-loading" size="30"><Loading /></el-icon>
          <p style="margin-top: 10px;">{{ $t('common.loading') }}</p>
        </div>
      </div>

      <!-- 索赔审批详情 -->
      <div v-else-if="approvalType === 'claim_approval' && claimDetail" class="approval-content">
        <!-- 基础信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.basicInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.approvalNo') }}:</label>
                <span>{{ claimDetail.approvalNo }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.submitter') }}:</label>
                <span>{{ claimDetail.submitterName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.submitTime') }}:</label>
                <span>{{ claimDetail.submitTime }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.orderNo') }}:</label>
                <span>{{ claimDetail.orderNo }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.currentLevel') }}:</label>
                <span>{{ getApprovalLevelText(claimDetail.currentLevel) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.approvalStatus') }}:</label>
                <el-tag :type="getApprovalStatusTagType(claimDetail.approvalStatus)">
                  {{ getApprovalStatusText(claimDetail.approvalStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.requestReason') }}:</label>
                <span>{{ claimDetail.requestReason }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.customerInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.customerName') }}:</label>
                <span>{{ claimDetail.customerInfo.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.customerPhone') }}:</label>
                <span>{{ claimDetail.customerInfo.phone }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.senderName') }}:</label>
                <span>{{ claimDetail.customerInfo.senderName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.senderPhone') }}:</label>
                <span>{{ claimDetail.customerInfo.senderPhone }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 车辆信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.vehicleInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.licensePlate') }}:</label>
                <span>{{ claimDetail.vehicleInfo.licensePlate }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vin') }}:</label>
                <span>{{ claimDetail.vehicleInfo.vin }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleModel') }}:</label>
                <span>{{ claimDetail.vehicleInfo.model }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleColor') }}:</label>
                <span>{{ claimDetail.vehicleInfo.color }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.saleTime') }}:</label>
                <span>{{ claimDetail.vehicleInfo.saleTime }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.mileage') }}:</label>
                <span>{{ claimDetail.vehicleInfo.mileage }} km</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleAge') }}:</label>
                <span>{{ claimDetail.vehicleInfo.vehicleAge }} {{ $t('workOrderApproval.months') }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.serviceTime') }}:</label>
                <span>{{ claimDetail.vehicleInfo.serviceTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 索赔内容 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.claimContent') }}</h3>

          <!-- 索赔工时 -->
          <div class="claim-subsection">
            <h4 class="subsection-title">{{ $t('workOrderApproval.claimLabor') }}</h4>
            <el-table :data="claimDetail.claimLaborList" border size="small">
              <el-table-column prop="itemCode" :label="$t('workOrderApproval.itemCode')" width="120" />
              <el-table-column prop="itemName" :label="$t('workOrderApproval.itemName')" min-width="200" />
              <el-table-column prop="quantity" :label="$t('workOrderApproval.quantity')" width="100" align="right">
                <template #default="{ row }">
                  {{ row.quantity }} {{ $t('workOrderApproval.hours') }}
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" :label="$t('workOrderApproval.unitPrice')" width="120" align="right">
                <template #default="{ row }">
                  ¥{{ row.unitPrice }}
                </template>
              </el-table-column>
              <el-table-column prop="claimAmount" :label="$t('workOrderApproval.claimAmount')" width="120" align="right">
                <template #default="{ row }">
                  ¥{{ row.claimAmount }}
                </template>
              </el-table-column>
            </el-table>
            <div class="claim-total">
              <span>{{ $t('workOrderApproval.laborTotal') }}: ¥{{ claimDetail.claimLaborTotal }}</span>
            </div>
          </div>

          <!-- 索赔零件 -->
          <div class="claim-subsection">
            <h4 class="subsection-title">{{ $t('workOrderApproval.claimParts') }}</h4>
            <el-table :data="claimDetail.claimPartsList" border size="small">
              <el-table-column prop="itemCode" :label="$t('workOrderApproval.itemCode')" width="120" />
              <el-table-column prop="itemName" :label="$t('workOrderApproval.itemName')" min-width="200" />
              <el-table-column prop="quantity" :label="$t('workOrderApproval.quantity')" width="100" align="right">
                <template #default="{ row }">
                  {{ row.quantity }} {{ $t('workOrderApproval.pieces') }}
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" :label="$t('workOrderApproval.unitPrice')" width="120" align="right">
                <template #default="{ row }">
                  ¥{{ row.unitPrice }}
                </template>
              </el-table-column>
              <el-table-column prop="claimAmount" :label="$t('workOrderApproval.claimAmount')" width="120" align="right">
                <template #default="{ row }">
                  ¥{{ row.claimAmount }}
                </template>
              </el-table-column>
            </el-table>
            <div class="claim-total">
              <span>{{ $t('workOrderApproval.partsTotal') }}: ¥{{ claimDetail.claimPartsTotal }}</span>
            </div>
          </div>

          <!-- 总计 -->
          <div class="claim-grand-total">
            <span>{{ $t('workOrderApproval.grandTotal') }}: ¥{{ claimDetail.claimTotalAmount }}</span>
          </div>
        </div>

        <!-- 审批历史 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.approvalHistory') }}</h3>
          <el-table :data="claimDetail.approvalProcessList" border size="small">
            <el-table-column prop="approvalLevel" :label="$t('workOrderApproval.approvalLevel')" width="100">
              <template #default="{ row }">
                {{ getApprovalLevelText(row.approvalLevel) }}
              </template>
            </el-table-column>
            <el-table-column prop="approverName" :label="$t('workOrderApproval.approver')" width="120" />
            <el-table-column prop="approvalTime" :label="$t('workOrderApproval.approvalTime')" width="160" />
            <el-table-column prop="approvalResult" :label="$t('workOrderApproval.approvalResult')" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.approvalResult" :type="getApprovalResultTagType(row.approvalResult)">
                  {{ getApprovalResultText(row.approvalResult) }}
                </el-tag>
                <span v-else class="pending-text">{{ $t('workOrderApproval.pending') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="approvalRemark" :label="$t('workOrderApproval.approvalRemark')" min-width="200" />
            <el-table-column prop="isOvertime" :label="$t('workOrderApproval.isOvertime')" width="100">
              <template #default="{ row }">
                <el-tag :type="row.isOvertime ? 'danger' : 'success'">
                  {{ row.isOvertime ? $t('common.yes') : $t('common.no') }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 取消审批详情 -->
      <div v-else-if="approvalType === 'cancel_approval' && cancelDetail" class="approval-content">
        <!-- 基础信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.basicInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.approvalNo') }}:</label>
                <span>{{ cancelDetail.approvalNo }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.submitter') }}:</label>
                <span>{{ cancelDetail.submitterName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.submitTime') }}:</label>
                <span>{{ cancelDetail.submitTime }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.orderNo') }}:</label>
                <span>{{ cancelDetail.orderNo }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.approvalStatus') }}:</label>
                <el-tag :type="getApprovalStatusTagType(cancelDetail.approvalStatus)">
                  {{ getApprovalStatusText(cancelDetail.approvalStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.currentOrderStatus') }}:</label>
                <el-tag :type="getOrderStatusTagType(cancelDetail.currentOrderStatus)">
                  {{ getOrderStatusText(cancelDetail.currentOrderStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.cancelReason') }}:</label>
                <span>{{ cancelDetail.cancelReason }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.customerInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.customerName') }}:</label>
                <span>{{ cancelDetail.customerInfo.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.customerPhone') }}:</label>
                <span>{{ cancelDetail.customerInfo.phone }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.senderName') }}:</label>
                <span>{{ cancelDetail.customerInfo.senderName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.senderPhone') }}:</label>
                <span>{{ cancelDetail.customerInfo.senderPhone }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 车辆信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.vehicleInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.licensePlate') }}:</label>
                <span>{{ cancelDetail.vehicleInfo.licensePlate }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vin') }}:</label>
                <span>{{ cancelDetail.vehicleInfo.vin }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleModel') }}:</label>
                <span>{{ cancelDetail.vehicleInfo.model }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleColor') }}:</label>
                <span>{{ cancelDetail.vehicleInfo.color }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.saleTime') }}:</label>
                <span>{{ cancelDetail.vehicleInfo.saleTime }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.mileage') }}:</label>
                <span>{{ cancelDetail.vehicleInfo.mileage }} km</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleAge') }}:</label>
                <span>{{ cancelDetail.vehicleInfo.vehicleAge }} {{ $t('workOrderApproval.months') }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.serviceTime') }}:</label>
                <span>{{ cancelDetail.vehicleInfo.serviceTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 工单状态信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.workOrderStatus') }}</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.currentOrderStatus') }}:</label>
                <el-tag :type="getOrderStatusTagType(cancelDetail.currentOrderStatus)">
                  {{ getOrderStatusText(cancelDetail.currentOrderStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8" v-if="cancelDetail.estimatedStartTime">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.estimatedStartTime') }}:</label>
                <span>{{ cancelDetail.estimatedStartTime }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="cancelDetail.assignedTechnicianName">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.assignedTechnician') }}:</label>
                <span>{{ cancelDetail.assignedTechnicianName }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 审批历史 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.approvalHistory') }}</h3>
          <el-table
            :data="cancelDetail.approvalProcessList"
            border
            size="small"
            :empty-text="$t('workOrderApproval.noApprovalHistory')"
          >
            <el-table-column prop="approvalLevel" :label="$t('workOrderApproval.approvalLevel')" width="100">
              <template #default="{ row }">
                {{ getApprovalLevelText(row.approvalLevel) }}
              </template>
            </el-table-column>
            <el-table-column prop="approverName" :label="$t('workOrderApproval.approver')" width="120" />
            <el-table-column prop="approvalTime" :label="$t('workOrderApproval.approvalTime')" width="160" />
            <el-table-column prop="approvalResult" :label="$t('workOrderApproval.approvalResult')" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.approvalResult" :type="getApprovalResultTagType(row.approvalResult)">
                  {{ getApprovalResultText(row.approvalResult) }}
                </el-tag>
                <span v-else class="pending-text">{{ $t('workOrderApproval.pending') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="approvalRemark" :label="$t('workOrderApproval.approvalRemark')" min-width="200" />
            <el-table-column prop="isOvertime" :label="$t('workOrderApproval.isOvertime')" width="100">
              <template #default="{ row }">
                <el-tag :type="row.isOvertime ? 'danger' : 'success'">
                  {{ row.isOvertime ? $t('common.yes') : $t('common.no') }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 调试信息 -->
      <div v-else class="debug-info" style="padding: 20px; background: #f0f0f0; margin: 10px; border-radius: 4px;">
        <h4>调试信息:</h4>
        <p>approvalType: {{ approvalType }}</p>
        <p>approvalNo: {{ approvalNo }}</p>
        <p>loading: {{ loading }}</p>
        <p>error: {{ error }}</p>
        <p>claimDetail: {{ claimDetail ? '已加载' : '未加载' }}</p>
        <p>cancelDetail: {{ cancelDetail ? '已加载' : '未加载' }}</p>
        <el-button @click="loadApprovalDetail" type="primary">手动加载数据</el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('common.close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type {
  ClaimApprovalDetail,
  CancelApprovalDetail,
  WorkOrderApprovalType,
  WorkOrderApprovalStatus,
  WorkOrderApprovalResult,
  ApprovalLevel,
  WorkOrderStatus
} from '@/types/module'
import { getClaimApprovalDetail, getCancelApprovalDetail } from '@/api/modules/workOrderApproval'

interface Props {
  visible: boolean
  approvalNo: string
  approvalType: WorkOrderApprovalType
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

// 响应式数据
const loading = ref(false)
const error = ref('')
const claimDetail = ref<ClaimApprovalDetail | null>(null)
const cancelDetail = ref<CancelApprovalDetail | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const loadApprovalDetail = async () => {
  if (!props.approvalNo) return

  try {
    loading.value = true
    error.value = ''

    if (props.approvalType === 'claim_approval') {
      claimDetail.value = await getClaimApprovalDetail(props.approvalNo)
      cancelDetail.value = null
    } else {
      cancelDetail.value = await getCancelApprovalDetail(props.approvalNo)
      claimDetail.value = null
    }
  } catch (err: any) {
    console.error('加载审批详情失败:', err)
    error.value = err.message || t('workOrderApproval.loadDetailFailed')
    ElMessage.error(t('workOrderApproval.loadDetailFailed'))
  } finally {
    loading.value = false
  }
}

const getDialogTitle = () => {
  if (props.approvalType === 'claim_approval') {
    return t('workOrderApproval.claimApprovalDetail')
  } else {
    return t('workOrderApproval.cancelApprovalDetail')
  }
}



const handleClose = () => {
  dialogVisible.value = false
  claimDetail.value = null
  cancelDetail.value = null
}

// 辅助方法
const getApprovalLevelText = (level: ApprovalLevel) => {
  const map = {
    first_level: t('workOrderApproval.firstLevel'),
    second_level: t('workOrderApproval.secondLevel')
  }
  return map[level] || level
}

const getApprovalStatusText = (status: WorkOrderApprovalStatus) => {
  const map = {
    pending_review: t('workOrderApproval.pendingReview'),
    reviewed: t('workOrderApproval.reviewed')
  }
  return map[status] || status
}

const getApprovalStatusTagType = (status: WorkOrderApprovalStatus) => {
  const map = {
    pending_review: 'warning',
    reviewed: 'success'
  }
  return map[status] || 'info'
}

const getOrderStatusText = (status: WorkOrderStatus) => {
  const map = {
    draft: t('workOrderApproval.draft'),
    waiting_approval: t('workOrderApproval.waitingApproval'),
    confirmed: t('workOrderApproval.confirmed'),
    cancelled: t('workOrderApproval.cancelled'),
    assigned: t('workOrderApproval.assigned'),
    in_progress: t('workOrderApproval.inProgress'),
    completed: t('workOrderApproval.completed')
  }
  return map[status] || status
}

const getOrderStatusTagType = (status: WorkOrderStatus) => {
  const map = {
    draft: 'info',
    waiting_approval: 'warning',
    confirmed: 'primary',
    cancelled: 'danger',
    assigned: 'success',
    in_progress: 'warning',
    completed: 'success'
  }
  return map[status] || 'info'
}

const getApprovalResultText = (result: WorkOrderApprovalResult) => {
  const map = {
    approved: t('workOrderApproval.approved'),
    rejected: t('workOrderApproval.rejected')
  }
  return map[result] || result
}

const getApprovalResultTagType = (result: WorkOrderApprovalResult) => {
  const map = {
    approved: 'success',
    rejected: 'danger'
  }
  return map[result] || 'info'
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    loadApprovalDetail()
  }
})
</script>

<style scoped lang="scss">
.approval-detail-modal {
  .approval-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .info-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .info-item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }

      span {
        color: #303133;
      }
    }
  }

  .claim-subsection {
    margin-bottom: 16px;

    .subsection-title {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
    }

    .claim-total {
      text-align: right;
      margin-top: 8px;
      font-weight: 600;
      color: #409eff;
    }
  }

  .claim-grand-total {
    text-align: right;
    margin-top: 16px;
    padding: 12px;
    background: #e6f7ff;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 700;
    color: #1890ff;
  }

  .pending-text {
    color: #909399;
    font-style: italic;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
