<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('workOrderApproval.claimApprovalDetail')"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="claim-approval-modal">
      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <el-result
          icon="error"
          :title="$t('workOrderApproval.loadDetailFailed')"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="loadApprovalDetail">
              {{ $t('common.retry') }}
            </el-button>
          </template>
        </el-result>
      </div>

      <!-- 正常内容 -->
      <div v-else-if="approvalDetail" class="approval-content">
        <!-- 基础信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.basicInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.approvalNo') }}:</label>
                <span>{{ approvalDetail.approvalNo }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.submitter') }}:</label>
                <span>{{ approvalDetail.submitterName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.submitTime') }}:</label>
                <span>{{ approvalDetail.submitTime }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.orderNo') }}:</label>
                <span>{{ approvalDetail.orderNo }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.currentLevel') }}:</label>
                <span>{{ getApprovalLevelText(approvalDetail.currentLevel) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.approvalStatus') }}:</label>
                <el-tag :type="getApprovalStatusTagType(approvalDetail.approvalStatus)">
                  {{ getApprovalStatusText(approvalDetail.approvalStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.requestReason') }}:</label>
                <span>{{ approvalDetail.requestReason }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.customerInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.customerName') }}:</label>
                <span>{{ approvalDetail.customerInfo.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.customerPhone') }}:</label>
                <span>{{ approvalDetail.customerInfo.phone }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.senderName') }}:</label>
                <span>{{ approvalDetail.customerInfo.senderName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.senderPhone') }}:</label>
                <span>{{ approvalDetail.customerInfo.senderPhone }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 车辆信息 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.vehicleInfo') }}</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.licensePlate') }}:</label>
                <span>{{ approvalDetail.vehicleInfo.licensePlate }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vin') }}:</label>
                <span>{{ approvalDetail.vehicleInfo.vin }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleModel') }}:</label>
                <span>{{ approvalDetail.vehicleInfo.model }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleColor') }}:</label>
                <span>{{ approvalDetail.vehicleInfo.color }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.saleTime') }}:</label>
                <span>{{ approvalDetail.vehicleInfo.saleTime }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.mileage') }}:</label>
                <span>{{ approvalDetail.vehicleInfo.mileage }} km</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.vehicleAge') }}:</label>
                <span>{{ approvalDetail.vehicleInfo.vehicleAge }} {{ $t('workOrderApproval.months') }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <label>{{ $t('workOrderApproval.serviceTime') }}:</label>
                <span>{{ approvalDetail.vehicleInfo.serviceTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 索赔内容 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.claimContent') }}</h3>

          <!-- 索赔工时 -->
          <div class="claim-subsection">
            <h4 class="subsection-title">{{ $t('workOrderApproval.claimLabor') }}</h4>
            <el-table :data="approvalDetail.claimLaborList" border size="small">
              <el-table-column prop="itemCode" :label="$t('workOrderApproval.itemCode')" width="120" />
              <el-table-column prop="itemName" :label="$t('workOrderApproval.itemName')" min-width="200" />
              <el-table-column prop="quantity" :label="$t('workOrderApproval.quantity')" width="100" align="right">
                <template #default="{ row }">
                  {{ row.quantity }} {{ $t('workOrderApproval.hours') }}
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" :label="$t('workOrderApproval.unitPrice')" width="120" align="right">
                <template #default="{ row }">
                  ¥{{ row.unitPrice }}
                </template>
              </el-table-column>
              <el-table-column prop="claimAmount" :label="$t('workOrderApproval.claimAmount')" width="120" align="right">
                <template #default="{ row }">
                  ¥{{ row.claimAmount }}
                </template>
              </el-table-column>
            </el-table>
            <div class="claim-total">
              <span>{{ $t('workOrderApproval.laborTotal') }}: ¥{{ approvalDetail.claimLaborTotal }}</span>
            </div>
          </div>

          <!-- 索赔零件 -->
          <div class="claim-subsection">
            <h4 class="subsection-title">{{ $t('workOrderApproval.claimParts') }}</h4>
            <el-table :data="approvalDetail.claimPartsList" border size="small">
              <el-table-column prop="itemCode" :label="$t('workOrderApproval.itemCode')" width="120" />
              <el-table-column prop="itemName" :label="$t('workOrderApproval.itemName')" min-width="200" />
              <el-table-column prop="quantity" :label="$t('workOrderApproval.quantity')" width="100" align="right">
                <template #default="{ row }">
                  {{ row.quantity }} {{ $t('workOrderApproval.pieces') }}
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" :label="$t('workOrderApproval.unitPrice')" width="120" align="right">
                <template #default="{ row }">
                  ¥{{ row.unitPrice }}
                </template>
              </el-table-column>
              <el-table-column prop="claimAmount" :label="$t('workOrderApproval.claimAmount')" width="120" align="right">
                <template #default="{ row }">
                  ¥{{ row.claimAmount }}
                </template>
              </el-table-column>
            </el-table>
            <div class="claim-total">
              <span>{{ $t('workOrderApproval.partsTotal') }}: ¥{{ approvalDetail.claimPartsTotal }}</span>
            </div>
          </div>

          <!-- 总计 -->
          <div class="claim-grand-total">
            <span>{{ $t('workOrderApproval.grandTotal') }}: ¥{{ approvalDetail.claimTotalAmount }}</span>
          </div>
        </div>

        <!-- 审批历史 -->
        <div class="info-section">
          <h3 class="section-title">{{ $t('workOrderApproval.approvalHistory') }}</h3>
          <el-table :data="approvalDetail.approvalProcessList" border size="small">
            <el-table-column prop="approvalLevel" :label="$t('workOrderApproval.approvalLevel')" width="100">
              <template #default="{ row }">
                {{ getApprovalLevelText(row.approvalLevel) }}
              </template>
            </el-table-column>
            <el-table-column prop="approverName" :label="$t('workOrderApproval.approver')" width="120" />
            <el-table-column prop="approvalTime" :label="$t('workOrderApproval.approvalTime')" width="160" />
            <el-table-column prop="approvalResult" :label="$t('workOrderApproval.approvalResult')" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.approvalResult" :type="getApprovalResultTagType(row.approvalResult)">
                  {{ getApprovalResultText(row.approvalResult) }}
                </el-tag>
                <span v-else class="pending-text">{{ $t('workOrderApproval.pending') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="approvalRemark" :label="$t('workOrderApproval.approvalRemark')" min-width="200" />
            <el-table-column prop="isOvertime" :label="$t('workOrderApproval.isOvertime')" width="100">
              <template #default="{ row }">
                <el-tag :type="row.isOvertime ? 'danger' : 'success'">
                  {{ row.isOvertime ? $t('common.yes') : $t('common.no') }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 审批操作 -->
        <div v-if="approvalDetail.approvalStatus === 'pending_review'" class="approval-action-section">
          <h3 class="section-title">{{ $t('workOrderApproval.approvalAction') }}</h3>
          <el-form :model="approvalForm" :rules="approvalRules" ref="approvalFormRef" label-width="100px">
            <el-form-item :label="$t('workOrderApproval.approvalResult')" prop="approvalResult">
              <el-radio-group v-model="approvalForm.approvalResult">
                <el-radio value="approved">{{ $t('workOrderApproval.approve') }}</el-radio>
                <el-radio value="rejected">{{ $t('workOrderApproval.reject') }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('workOrderApproval.approvalRemark')" prop="approvalRemark">
              <el-input
                v-model="approvalForm.approvalRemark"
                type="textarea"
                :rows="4"
                :placeholder="$t('workOrderApproval.approvalRemarkPlaceholder')"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 调试信息 -->
      <div v-else class="debug-info" style="padding: 20px; background: #f0f0f0; margin: 10px; border-radius: 4px;">
        <h4>调试信息:</h4>
        <p>approvalNo: {{ approvalNo }}</p>
        <p>loading: {{ loading }}</p>
        <p>error: {{ error }}</p>
        <p>approvalDetail: {{ approvalDetail ? '已加载' : '未加载' }}</p>
        <el-button @click="loadApprovalDetail" type="primary">手动加载数据</el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('common.cancel') }}</el-button>
        <el-button
          v-if="approvalDetail?.approvalStatus === 'pending_review'"
          type="primary"
          @click="handleSubmitApproval"
          :loading="submitting"
        >
          {{ $t('workOrderApproval.submitApproval') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'
import type {
  ClaimApprovalDetail,
  WorkOrderApprovalActionRequest,
  WorkOrderApprovalResult,
  WorkOrderApprovalStatus,
  ApprovalLevel
} from '@/types/module'
import { getClaimApprovalDetail, submitApproval } from '@/api/modules/workOrderApproval'

interface Props {
  visible: boolean
  approvalNo: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const error = ref('')
const approvalDetail = ref<ClaimApprovalDetail | null>(null)
const approvalFormRef = ref<FormInstance>()

// 审批表单
const approvalForm = reactive<{
  approvalResult: WorkOrderApprovalResult | ''
  approvalRemark: string
}>({
  approvalResult: '',
  approvalRemark: ''
})

// 表单验证规则
const approvalRules: FormRules = {
  approvalResult: [
    { required: true, message: t('workOrderApproval.approvalResultRequired'), trigger: 'change' }
  ],
  approvalRemark: [
    {
      validator: (rule, value, callback) => {
        if (approvalForm.approvalResult === 'rejected' && !value) {
          callback(new Error(t('workOrderApproval.approvalRemarkRequiredForReject')))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const loadApprovalDetail = async () => {
  if (!props.approvalNo) return

  try {
    loading.value = true
    error.value = ''
    approvalDetail.value = await getClaimApprovalDetail(props.approvalNo)
  } catch (err: any) {
    console.error('加载审批详情失败:', err)
    error.value = err.message || t('workOrderApproval.loadDetailFailed')
    ElMessage.error(t('workOrderApproval.loadDetailFailed'))
  } finally {
    loading.value = false
  }
}

const handleSubmitApproval = async () => {
  if (!approvalFormRef.value) return

  try {
    await approvalFormRef.value.validate()

    await ElMessageBox.confirm(
      t('workOrderApproval.confirmSubmitApproval'),
      t('common.confirm'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )

    submitting.value = true

    const requestData: WorkOrderApprovalActionRequest = {
      approvalNo: props.approvalNo,
      approvalResult: approvalForm.approvalResult as WorkOrderApprovalResult,
      approvalRemark: approvalForm.approvalRemark,
      approverId: 'current_user_id' // 实际应该从用户状态获取
    }

    await submitApproval(requestData)

    ElMessage.success(t('workOrderApproval.approvalSubmitSuccess'))
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(t('workOrderApproval.approvalSubmitFailed'))
    }
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  approvalForm.approvalResult = ''
  approvalForm.approvalRemark = ''
  approvalFormRef.value?.clearValidate()
}

// 辅助方法
const getApprovalLevelText = (level: ApprovalLevel) => {
  const map = {
    first_level: t('workOrderApproval.firstLevel'),
    second_level: t('workOrderApproval.secondLevel')
  }
  return map[level] || level
}

const getApprovalStatusText = (status: WorkOrderApprovalStatus) => {
  const map = {
    pending_review: t('workOrderApproval.pendingReview'),
    reviewed: t('workOrderApproval.reviewed')
  }
  return map[status] || status
}

const getApprovalStatusTagType = (status: WorkOrderApprovalStatus) => {
  const map = {
    pending_review: 'warning',
    reviewed: 'success'
  }
  return map[status] || 'info'
}

const getApprovalResultText = (result: WorkOrderApprovalResult) => {
  const map = {
    approved: t('workOrderApproval.approved'),
    rejected: t('workOrderApproval.rejected')
  }
  return map[result] || result
}

const getApprovalResultTagType = (result: WorkOrderApprovalResult) => {
  const map = {
    approved: 'success',
    rejected: 'danger'
  }
  return map[result] || 'info'
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    loadApprovalDetail()
  }
})
</script>

<style scoped lang="scss">
.claim-approval-modal {
  .approval-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .info-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .info-item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }

      span {
        color: #303133;
      }
    }
  }

  .claim-subsection {
    margin-bottom: 16px;

    .subsection-title {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
    }

    .claim-total {
      text-align: right;
      margin-top: 8px;
      font-weight: 600;
      color: #409eff;
    }
  }

  .claim-grand-total {
    text-align: right;
    margin-top: 16px;
    padding: 12px;
    background: #e6f7ff;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 700;
    color: #1890ff;
  }

  .approval-action-section {
    margin-top: 24px;
    padding: 20px;
    background: #fff7e6;
    border-radius: 6px;
    border: 1px solid #ffd591;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #fa8c16;
    }
  }

  .pending-text {
    color: #909399;
    font-style: italic;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
