# 用户管理API文档

## 概述
用户管理模块提供用户的增删改查、状态管理、角色分配等功能。支持Mock数据和真实API切换。

## Mock数据切换
通过环境变量 `VITE_APP_USE_MOCK_API` 控制是否使用Mock数据：
- `true`: 使用Mock数据
- `false`: 调用真实后端API

## 接口列表

### 1. 获取用户列表（分页）

- **接口地址**: `POST /users/page`
- **请求方式**: POST
- **描述**: 分页查询用户列表

#### 请求参数

```typescript
interface UserQueryParams {
  username?: string;           // 用户名（模糊搜索）
  fullName?: string;          // 姓名（模糊搜索）
  userType?: 'factory' | 'store'; // 用户类型
  storeId?: string;           // 门店ID
  current?: number;           // 当前页，默认1
  size?: number;              // 每页大小，默认10
}
```

#### 请求示例

```json
{
  "username": "admin",
  "userType": "factory",
  "current": 1,
  "size": 10
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "records": [
      {
        "id": "user_1",
        "username": "admin",
        "fullName": "系统管理员",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "userStatus": "normal",
        "userType": "factory",
        "entryDate": "2023-01-01",
        "primaryStoreId": "store_1",
        "primaryStoreName": "总部",
        "createTime": "2023-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "current": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": 1640995200000
}
```

### 2. 获取用户详情

- **接口地址**: `GET /users/detail`
- **请求方式**: GET
- **描述**: 根据用户ID获取用户详情

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 用户ID |

#### 请求示例

```
GET /users/detail?id=user_1
```

#### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": "user_1",
    "username": "admin",
    "fullName": "系统管理员",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "userStatus": "normal",
    "userType": "factory",
    "entryDate": "2023-01-01",
    "primaryStoreId": "store_1",
    "primaryStoreName": "总部",
    "storeRoles": [
      {
        "id": "usr_1",
        "userId": "user_1",
        "storeId": "store_1",
        "storeName": "总部",
        "departmentId": "dept_1",
        "departmentName": "技术部",
        "position": "系统管理员",
        "roleId": "role_1",
        "roleName": "系统管理员",
        "isPrimary": true
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### 3. 新增用户

- **接口地址**: `POST /users/create`
- **请求方式**: POST
- **描述**: 创建新用户

#### 请求参数

```typescript
interface CreateUserRequest {
  username: string;           // 用户名，必填
  fullName: string;          // 姓名，必填
  phone: string;             // 手机号
  email: string;             // 邮箱
  userType: 'factory' | 'store'; // 用户类型，必填
  entryDate: string;         // 入职日期，必填
  userStatus: 'normal' | 'disabled'; // 用户状态，必填
  remark?: string;           // 备注
}
```

#### 请求示例

```json
{
  "username": "newuser",
  "fullName": "新用户",
  "phone": "13900139000",
  "email": "<EMAIL>",
  "userType": "store",
  "entryDate": "2023-12-01",
  "userStatus": "normal",
  "remark": "新员工"
}
```

### 4. 编辑用户

- **接口地址**: `POST /users/update`
- **请求方式**: POST
- **描述**: 更新用户信息，ID包含在请求体中

#### 请求参数

```typescript
interface UpdateUserRequest {
  id: string;                // 用户ID，必填
  username?: string;         // 用户名
  fullName?: string;         // 姓名
  phone?: string;            // 手机号
  email?: string;            // 邮箱
  userType?: 'factory' | 'store'; // 用户类型
  entryDate?: string;        // 入职日期
  userStatus?: 'normal' | 'disabled'; // 用户状态
  remark?: string;           // 备注
}
```

#### 请求示例

```json
{
  "id": "user_2",
  "fullName": "更新后的姓名",
  "phone": "13901139000"
}
```

### 5. 删除用户

- **接口地址**: `GET /users/delete`
- **请求方式**: GET
- **描述**: 删除指定用户

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 用户ID |

#### 请求示例

```
GET /users/delete?id=user_2
```

### 6. 更新用户状态

- **接口地址**: `POST /users/status`
- **请求方式**: POST
- **描述**: 更新用户状态

#### 请求参数

```json
{
  "id": "user_1",
  "userStatus": "disabled"
}
```

### 7. 分配用户角色

- **接口地址**: `POST /users/assign-roles`
- **请求方式**: POST
- **描述**: 为用户分配门店角色

#### 请求参数

```typescript
interface AssignUserRolesRequest {
  userId: string;            // 用户ID
  storeRoles: UserStoreRole[]; // 门店角色列表
}

interface UserStoreRole {
  id: string;                // 关系ID
  userId: string;            // 用户ID
  storeId: string;           // 门店ID
  storeName?: string;        // 门店名称
  departmentId: string;      // 部门ID
  departmentName?: string;   // 部门名称
  position: string;          // 职位
  roleId: string;            // 角色ID
  roleName?: string;         // 角色名称
  isPrimary: boolean;        // 是否主角色
}
```

#### 请求示例

```json
{
  "userId": "user_3",
  "storeRoles": [
    {
      "id": "usr_3_1",
      "userId": "user_3",
      "storeId": "store_2",
      "storeName": "上海旗舰店",
      "departmentId": "dept_2_1",
      "departmentName": "销售部",
      "position": "销售顾问",
      "roleId": "role_3",
      "roleName": "销售顾问",
      "isPrimary": true
    }
  ]
}
```

### 8. 获取用户已分配角色

- **接口地址**: `GET /users/assigned-roles`
- **请求方式**: GET
- **描述**: 获取用户已分配的门店角色，用于角色分配时的数据回显

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | string | 是 | 用户ID |

#### 请求示例

```
GET /users/assigned-roles?userId=user_3
```

#### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "result": [
    {
      "id": "usr_3_1",
      "userId": "user_3",
      "storeId": "store_2",
      "storeName": "上海旗舰店",
      "departmentId": "dept_2_1",
      "departmentName": "销售部",
      "position": "销售顾问",
      "roleId": "role_3",
      "roleName": "销售顾问",
      "isPrimary": true,
      "createTime": "2023-01-10T09:00:00Z"
    },
    {
      "id": "usr_3_2",
      "userId": "user_3",
      "storeId": "store_2",
      "storeName": "上海旗舰店",
      "departmentId": "dept_2_1",
      "departmentName": "销售部",
      "position": "销售组长",
      "roleId": "role_5",
      "roleName": "上海店销售组长",
      "isPrimary": false,
      "createTime": "2023-01-16T11:40:00Z"
    }
  ],
  "timestamp": 1640995200000
}
```

## 响应状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 用户名必须唯一
2. 邮箱必须符合邮箱格式
3. 厂端用户只能分配一个门店角色
4. 删除用户前需要先取消其角色分配
5. 用户状态为禁用时，用户无法登录系统
6. Mock模式下，数据变更会保存在内存中，刷新页面后重置 