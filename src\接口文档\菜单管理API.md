# 菜单管理API接口文档

## 概述

菜单管理模块提供了完整的菜单信息管理功能，包括菜单的增删改查、树形结构展示、分页查询等功能。支持厂端和店端菜单的分别管理。

## 接口列表

### 1. 分页查询菜单列表（树形结构）

**接口描述：** 获取菜单分页列表数据，支持树形结构展示

**请求方式：** POST

**接口地址：** `/menus/page`

**请求参数：**

```json
{
  "current": 1,
  "size": 10,
  "menuName": "系统管理",
  "menuStatus": "normal",
  "menuSide": "factory",
  "menuType": "directory"
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| current | number | 是 | 当前页码 |
| size | number | 是 | 每页大小 |
| menuName | string | 否 | 菜单名称（模糊搜索） |
| menuStatus | string | 否 | 菜单状态：normal-正常，disabled-停用 |
| menuSide | string | 否 | 菜单所属端：factory-厂端，dealer-店端 |
| menuType | string | 否 | 菜单类型：directory-目录，menu-菜单，button-按钮 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": {
    "records": [
      {
        "id": "1",
        "menuName": "系统管理",
        "menuCode": "system",
        "menuType": "directory",
        "menuSide": "factory",
        "menuStatus": "normal",
        "menuIcon": "Setting",
        "menuPath": "/system",
        "component": "",
        "permission": "system",
        "sortOrder": 100,
        "isVisible": true,
        "isCache": false,
        "parentId": null,
        "createTime": "2024-01-01T10:00:00",
        "children": [
          {
            "id": "2",
            "menuName": "用户管理",
            "menuCode": "system:user",
            "menuType": "menu",
            "menuSide": "factory",
            "menuStatus": "normal",
            "menuIcon": "User",
            "menuPath": "/system/user",
            "component": "system/UserManagement",
            "permission": "system:user:list",
            "sortOrder": 1,
            "isVisible": true,
            "isCache": false,
            "parentId": "1",
            "createTime": "2024-01-01T10:00:00",
            "children": [
              {
                "id": "3",
                "menuName": "新增用户",
                "menuCode": "system:user:add",
                "menuType": "button",
                "menuSide": "factory",
                "menuStatus": "normal",
                "permission": "system:user:add",
                "sortOrder": 1,
                "isVisible": true,
                "isCache": false,
                "parentId": "2",
                "createTime": "2024-01-01T10:00:00",
                "children": []
              }
            ]
          }
        ]
      }
    ],
    "total": 15,
    "size": 10,
    "current": 1,
    "pages": 2
  },
  "timestamp": 1640995200000
}
```

### 2. 获取菜单树形结构（无分页）

**接口描述：** 获取所有菜单的树形结构数据，无分页限制

**请求方式：** GET

**接口地址：** `/menus/tree`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| menuName | string | 否 | 菜单名称（模糊搜索） |
| menuStatus | string | 否 | 菜单状态：normal-正常，disabled-停用 |
| menuSide | string | 否 | 菜单所属端：factory-厂端，dealer-店端 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": [
    {
      "id": "1",
      "menuName": "系统管理",
      "menuCode": "system",
      "menuType": "directory",
      "menuSide": "factory",
      "menuStatus": "normal",
      "menuIcon": "Setting",
      "menuPath": "/system",
      "component": "",
      "permission": "system",
      "sortOrder": 100,
      "isVisible": true,
      "isCache": false,
      "parentId": null,
      "createTime": "2024-01-01T10:00:00",
      "children": [
        {
          "id": "2",
          "menuName": "用户管理",
          "menuCode": "system:user",
          "menuType": "menu",
          "menuSide": "factory",
          "menuStatus": "normal",
          "menuIcon": "User",
          "menuPath": "/system/user",
          "component": "system/UserManagement",
          "permission": "system:user:list",
          "sortOrder": 1,
          "isVisible": true,
          "isCache": false,
          "parentId": "1",
          "createTime": "2024-01-01T10:00:00",
          "children": []
        }
      ]
    }
  ],
  "timestamp": 1640995200000
}
```

### 3. 获取菜单详情

**接口描述：** 根据ID获取菜单详细信息

**请求方式：** GET

**接口地址：** `/menus/detail`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| id | string | 是 | 菜单ID |

**请求示例：**
```
GET /menus/detail?id=1
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": {
    "id": "1",
    "menuName": "系统管理",
    "menuCode": "system",
    "menuType": "directory",
    "menuSide": "factory",
    "menuStatus": "normal",
    "menuIcon": "Setting",
    "menuPath": "/system",
    "component": "",
    "permission": "system",
    "sortOrder": 100,
    "isVisible": true,
    "isCache": false,
    "parentId": null,
    "createTime": "2024-01-01T10:00:00",
    "updateTime": "2024-01-01T10:00:00"
  },
  "timestamp": 1640995200000
}
```

### 4. 新增菜单

**接口描述：** 创建新的菜单

**请求方式：** POST

**接口地址：** `/menus/create`

**请求参数：**

```json
{
  "menuName": "系统管理",
  "menuCode": "system",
  "menuType": "directory",
  "menuSide": "factory",
  "menuIcon": "Setting",
  "menuPath": "/system",
  "component": "",
  "permission": "system",
  "sortOrder": 100,
  "menuStatus": "normal",
  "isVisible": true,
  "isCache": false,
  "parentId": null,
  "remark": "系统管理模块"
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| menuName | string | 是 | 菜单名称 |
| menuCode | string | 是 | 菜单编码 |
| menuType | string | 是 | 菜单类型：directory-目录，menu-菜单，button-按钮 |
| menuSide | string | 是 | 菜单所属端：factory-厂端，dealer-店端 |
| menuIcon | string | 否 | 菜单图标 |
| menuPath | string | 否 | 菜单路径 |
| component | string | 否 | 组件路径 |
| permission | string | 否 | 权限标识 |
| sortOrder | number | 是 | 排序 |
| menuStatus | string | 是 | 菜单状态：normal-正常，disabled-停用 |
| isVisible | boolean | 是 | 是否显示 |
| isCache | boolean | 是 | 是否缓存 |
| parentId | string | 否 | 上级菜单ID |
| remark | string | 否 | 备注 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": {
    "id": "1",
    "menuName": "系统管理",
    "menuCode": "system",
    "menuType": "directory",
    "menuSide": "factory",
    "menuStatus": "normal",
    "menuIcon": "Setting",
    "menuPath": "/system",
    "component": "",
    "permission": "system",
    "sortOrder": 100,
    "isVisible": true,
    "isCache": false,
    "parentId": null,
    "createTime": "2024-01-01T10:00:00"
  },
  "timestamp": 1640995200000
}
```

### 5. 编辑菜单

**接口描述：** 更新菜单信息，ID放入参数实体中

**请求方式：** POST

**接口地址：** `/menus/update`

**请求参数：**

```json
{
  "id": "1",
  "menuName": "系统管理",
  "menuCode": "system",
  "menuType": "directory",
  "menuSide": "factory",
  "menuIcon": "Setting",
  "menuPath": "/system",
  "component": "",
  "permission": "system",
  "sortOrder": 100,
  "menuStatus": "normal",
  "isVisible": true,
  "isCache": false,
  "parentId": null,
  "remark": "系统管理模块"
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| id | string | 是 | 菜单ID |
| menuName | string | 是 | 菜单名称 |
| menuCode | string | 是 | 菜单编码 |
| menuType | string | 是 | 菜单类型 |
| menuSide | string | 是 | 菜单所属端 |
| menuIcon | string | 否 | 菜单图标 |
| menuPath | string | 否 | 菜单路径 |
| component | string | 否 | 组件路径 |
| permission | string | 否 | 权限标识 |
| sortOrder | number | 是 | 排序 |
| menuStatus | string | 是 | 菜单状态 |
| isVisible | boolean | 是 | 是否显示 |
| isCache | boolean | 是 | 是否缓存 |
| parentId | string | 否 | 上级菜单ID |
| remark | string | 否 | 备注 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": {
    "id": "1",
    "menuName": "系统管理",
    "menuCode": "system",
    "menuType": "directory",
    "menuSide": "factory",
    "menuStatus": "normal",
    "menuIcon": "Setting",
    "menuPath": "/system",
    "component": "",
    "permission": "system",
    "sortOrder": 100,
    "isVisible": true,
    "isCache": false,
    "parentId": null,
    "updateTime": "2024-01-01T10:00:00"
  },
  "timestamp": 1640995200000
}
```

### 6. 删除菜单

**接口描述：** 根据ID删除菜单

**请求方式：** GET

**接口地址：** `/menus/delete`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| id | string | 是 | 菜单ID |

**请求示例：**
```
GET /menus/delete?id=1
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": null,
  "timestamp": 1640995200000
}
```

### 7. 获取字典数据

**接口描述：** 获取菜单状态字典数据

**请求方式：** GET

**接口地址：** `/api/v1/basic/dic`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| dicCategoryCode | string | 是 | 字典分类编码，菜单状态传值为"1002" |

**请求示例：**
```
GET /api/v1/basic/dic?dicCategoryCode=1002
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": [
    {
      "value": "normal",
      "label": "正常"
    },
    {
      "value": "disabled",
      "label": "停用"
    }
  ],
  "timestamp": 1640995200000
}
```

## 数据模型

### Menu 菜单信息

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | string | 菜单ID |
| menuName | string | 菜单名称 |
| menuCode | string | 菜单编码 |
| menuType | string | 菜单类型 |
| menuSide | string | 菜单所属端 |
| menuStatus | string | 菜单状态 |
| menuIcon | string | 菜单图标 |
| menuPath | string | 菜单路径 |
| component | string | 组件路径 |
| permission | string | 权限标识 |
| sortOrder | number | 排序 |
| isVisible | boolean | 是否显示 |
| isCache | boolean | 是否缓存 |
| parentId | string | 上级菜单ID |
| createTime | string | 创建时间 |
| updateTime | string | 更新时间 |
| children | Menu[] | 子菜单列表 |

### 枚举值说明

#### MenuType 菜单类型

| 值 | 说明 |
|---|-----|
| directory | 目录 |
| menu | 菜单 |
| button | 按钮 |

#### MenuSide 菜单所属端

| 值 | 说明 |
|---|-----|
| factory | 厂端 |
| dealer | 店端 |

#### MenuStatus 菜单状态

| 值 | 说明 |
|---|-----|
| normal | 正常 |
| disabled | 停用 |

### 状态码说明

| 状态码 | 说明 |
|-------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要在请求头中携带认证信息
2. 分页查询的分页参数不能固定为 1/9999
3. 树形结构数据支持无限级嵌套
4. 菜单编码在同一级别下不能重复
5. 删除菜单前需要确保没有子菜单和关联的用户权限
6. 菜单状态字典数据通过 dicCategoryCode=1002 获取
7. 按钮类型的菜单不显示菜单路径和组件路径
8. 厂端和店端菜单需要分别管理

## 版本更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2024-01-01 | 初始版本，包含基础的菜单管理功能 |
| v1.1 | 2024-01-15 | 增加树形结构支持和字典数据接口 |
| v1.2 | 2024-01-20 | 优化分页查询性能，增加查看详情功能 |
| v1.3 | 2024-01-25 | 支持厂端和店端菜单分别管理，增加菜单类型枚举 | 