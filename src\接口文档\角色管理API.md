# 角色管理API文档

## 版本信息
- 版本：v1.1
- 更新时间：2024-01-XX
- 更新内容：
  - v1.0: 角色管理模块API接口定义
  - v1.1: 新增角色菜单权限和数据权限的数据回显接口

## 1. 角色分页列表查询

### 接口描述
分页查询角色列表数据，支持多种查询条件

### 请求方式
POST

### 请求地址
/roles/page

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| current | number | 是 | 当前页码，从1开始 |
| size | number | 是 | 每页条数，建议10-100 |
| roleName | string | 否 | 角色名称，支持模糊查询 |
| roleSource | string | 否 | 角色类型：factory-厂端，store-店端 |
| roleStatus | string | 否 | 角色状态：normal-正常，disabled-禁用 |
| storeId | string | 否 | 所属门店ID，仅当roleSource为store时有效 |

### 请求示例
```json
{
  "current": 1,
  "size": 10,
  "roleName": "销售",
  "roleSource": "store",
  "roleStatus": "normal",
  "storeId": "store_001"
}
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |
| result | object | 响应数据 |
| result.records | array | 角色列表 |
| result.total | number | 总记录数 |
| result.current | number | 当前页码 |
| result.size | number | 每页条数 |
| result.pages | number | 总页数 |

#### 角色对象属性
| 参数名 | 类型 | 说明 |
|-------|------|------|
| id | string | 角色ID |
| roleName | string | 角色名称 |
| roleCode | string | 角色编码 |
| roleSource | string | 角色类型：factory-厂端，store-店端 |
| roleStatus | string | 角色状态：normal-正常，disabled-禁用 |
| roleScope | string | 数据权限范围 |
| description | string | 角色描述 |
| storeId | string | 所属门店ID（仅店端角色） |
| menuIds | array | 菜单权限ID列表 |
| deptIds | array | 数据权限部门ID列表 |
| createTime | string | 创建时间 |

### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "records": [
      {
        "id": "role_001",
        "roleName": "销售经理",
        "roleCode": "SALES_MANAGER",
        "roleSource": "store",
        "roleStatus": "normal",
        "roleScope": "department",
        "description": "销售部门经理角色",
        "storeId": "store_001",
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1,
    "current": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 2. 角色详情查询

### 接口描述
根据角色ID查询角色详细信息

### 请求方式
GET

### 请求地址
/roles/detail

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| id | string | 是 | 角色ID |

### 请求示例
```
GET /roles/detail?id=role_001
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |
| result | object | 角色详情对象 |

### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": "role_001",
    "roleName": "销售经理",
    "roleCode": "SALES_MANAGER",
    "roleSource": "store",
    "roleStatus": "normal",
    "roleScope": "department",
    "description": "销售部门经理角色",
    "storeId": "store_001",
    "menuIds": ["menu_001", "menu_002"],
    "deptIds": ["dept_001"],
    "createTime": "2024-01-01 10:00:00"
  }
}
```

## 3. 新增角色

### 接口描述
创建新的角色信息

### 请求方式
POST

### 请求地址
/roles/create

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| roleName | string | 是 | 角色名称 |
| roleCode | string | 是 | 角色编码，全局唯一 |
| roleSource | string | 是 | 角色类型：factory-厂端，store-店端 |
| roleStatus | string | 是 | 角色状态：normal-正常，disabled-禁用 |
| roleScope | string | 是 | 数据权限范围 |
| description | string | 否 | 角色描述 |
| storeId | string | 否 | 所属门店ID，当roleSource为store时必填 |
| remark | string | 否 | 备注信息 |

### 请求示例
```json
{
  "roleName": "销售经理",
  "roleCode": "SALES_MANAGER",
  "roleSource": "store",
  "roleStatus": "normal",
  "roleScope": "department",
  "description": "销售部门经理角色",
  "storeId": "store_001"
}
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |
| result | object | 新创建的角色对象 |

## 4. 编辑角色

### 接口描述
更新角色信息，ID包含在请求体中

### 请求方式
POST

### 请求地址
/roles/update

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| id | string | 是 | 角色ID |
| roleName | string | 是 | 角色名称 |
| roleCode | string | 是 | 角色编码 |
| roleSource | string | 是 | 角色类型：factory-厂端，store-店端 |
| roleStatus | string | 是 | 角色状态：normal-正常，disabled-禁用 |
| roleScope | string | 是 | 数据权限范围 |
| description | string | 否 | 角色描述 |
| storeId | string | 否 | 所属门店ID |
| remark | string | 否 | 备注信息 |

### 请求示例
```json
{
  "id": "role_001",
  "roleName": "高级销售经理",
  "roleCode": "SENIOR_SALES_MANAGER",
  "roleSource": "store",
  "roleStatus": "normal",
  "roleScope": "departmentAndBelow",
  "description": "高级销售部门经理角色",
  "storeId": "store_001"
}
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |
| result | object | 更新后的角色对象 |

## 5. 删除角色

### 接口描述
删除指定的角色信息

### 请求方式
GET

### 请求地址
/roles/delete

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| id | string | 是 | 角色ID |

### 请求示例
```
GET /roles/delete?id=role_001
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |

## 6. 获取角色菜单权限

### 接口描述
获取角色已配置的菜单权限，用于数据回显

### 请求方式
GET

### 请求地址
/roles/menu/permission

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| roleId | string | 是 | 角色ID |

### 请求示例
```
GET /roles/menu/permission?roleId=role_001
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |
| result | object | 响应数据 |
| result.menuIds | array | 菜单ID列表 |

### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "menuIds": ["menu_001", "menu_002", "menu_003"]
  }
}
```

## 7. 配置菜单权限

### 接口描述
为角色配置菜单权限，根据角色类型（厂端/店端）加载对应的菜单

### 请求方式
POST

### 请求地址
/roles/menu/configure

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| roleId | string | 是 | 角色ID |
| menuIds | array | 是 | 菜单ID列表，包含选中和半选中的菜单 |

### 请求示例
```json
{
  "roleId": "role_001",
  "menuIds": ["menu_001", "menu_002", "menu_003"]
}
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |

## 8. 获取角色数据权限

### 接口描述
获取角色已配置的数据权限，用于数据回显

### 请求方式
GET

### 请求地址
/roles/data-permission

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| roleId | string | 是 | 角色ID |

### 请求示例
```
GET /roles/data-permission?roleId=role_001
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |
| result | object | 响应数据 |
| result.dataScope | string | 数据权限类型 |
| result.deptIds | array | 部门ID列表 |

### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "dataScope": "custom",
    "deptIds": ["dept_001", "dept_002"]
  }
}
```

## 9. 配置数据权限

### 接口描述
为角色配置数据权限，支持全部数据、自定义部门数据、仅本人数据等权限类型

### 请求方式
POST

### 请求地址
/roles/data-permission/configure

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| roleId | string | 是 | 角色ID |
| dataScope | string | 是 | 数据权限类型 |
| deptIds | array | 否 | 部门ID列表，当dataScope为custom时必填 |

#### 数据权限类型说明
| 值 | 说明 |
|---|------|
| all | 全部数据权限 |
| custom | 自定义数据权限（指定部门） |
| department | 本部门数据权限 |
| departmentAndBelow | 本部门及下级部门数据权限 |
| onlyPersonal | 仅本人数据权限 |

### 请求示例
```json
{
  "roleId": "role_001",
  "dataScope": "custom",
  "deptIds": ["dept_001", "dept_002"]
}
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |

## 10. 获取字典数据

### 接口描述
获取角色状态等字典数据

### 请求方式
GET

### 请求地址
/api/v1/basic/dic

### 请求参数
| 参数名 | 类型 | 是否必须 | 说明 |
|-------|------|---------|------|
| dicCategoryCode | string | 是 | 字典类别编码，角色状态为1004 |

### 请求示例
```
GET /api/v1/basic/dic?dicCategoryCode=1004
```

### 响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | number | 响应码，200表示成功 |
| message | string | 响应信息 |
| result | array | 字典数据列表 |

#### 字典项属性
| 参数名 | 类型 | 说明 |
|-------|------|------|
| value | string | 字典项值 |
| label | string | 字典项显示名称 |
| disabled | boolean | 是否禁用 |

### 响应示例
```json
{
  "code": 200,
  "message": "Success",
  "result": [
    {
      "value": "normal",
      "label": "正常",
      "disabled": false
    },
    {
      "value": "disabled",
      "label": "禁用",
      "disabled": false
    }
  ]
}
```

## 数据模型说明

### 角色类型枚举
- `factory`: 厂端角色
- `store`: 店端角色

### 角色状态枚举
- `normal`: 正常
- `disabled`: 禁用

### 数据权限范围枚举
- `all`: 全部数据权限
- `custom`: 自定义数据权限
- `department`: 本部门数据权限
- `departmentAndBelow`: 本部门及下级部门数据权限
- `onlyPersonal`: 仅本人数据权限

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **分页参数**：current和size参数不能固定为1和9999，应根据实际需要设置
2. **权限控制**：厂端角色和店端角色加载不同的菜单权限
3. **数据权限**：自定义数据权限时必须选择具体的部门
4. **角色编码**：roleCode在系统中必须唯一
5. **删除限制**：正在使用中的角色不能删除
6. **店端角色**：店端角色必须关联具体的门店ID 