# 部门管理API接口文档

## 概述

部门管理模块提供了完整的部门信息管理功能，包括部门的增删改查、树形结构展示、分页查询等功能。

## 接口列表

### 1. 分页查询部门列表（树形结构）

**接口描述：** 获取部门分页列表数据，支持树形结构展示

**请求方式：** POST

**接口地址：** `/departments/page`

**请求参数：**

```json
{
  "current": 1,
  "size": 10,
  "departmentName": "技术部",
  "departmentCode": "TECH001",
  "departmentStatus": "normal",
  "departmentType": "business"
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| current | number | 是 | 当前页码 |
| size | number | 是 | 每页大小 |
| departmentName | string | 否 | 部门名称（模糊搜索） |
| departmentCode | string | 否 | 部门编码 |
| departmentStatus | string | 否 | 部门状态：normal-正常，disabled-停用 |
| departmentType | string | 否 | 部门类型：business-业务部门，support-支持部门，management-管理部门 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": {
    "records": [
      {
        "id": "1",
        "departmentName": "技术部",
        "departmentCode": "TECH001",
        "departmentType": "business",
        "departmentStatus": "normal",
        "departmentHead": "张三",
        "description": "负责技术开发工作",
        "parentId": null,
        "createTime": "2024-01-01T10:00:00",
        "children": [
          {
            "id": "2",
            "departmentName": "前端开发组",
            "departmentCode": "FRONTEND001",
            "departmentType": "business",
            "departmentStatus": "normal",
            "departmentHead": "李四",
            "description": "负责前端开发",
            "parentId": "1",
            "createTime": "2024-01-01T10:00:00",
            "children": []
          }
        ]
      }
    ],
    "total": 10,
    "size": 10,
    "current": 1,
    "pages": 1
  },
  "timestamp": 1640995200000
}
```

### 2. 获取部门树形结构（无分页）

**接口描述：** 获取所有部门的树形结构数据，无分页限制

**请求方式：** GET

**接口地址：** `/departments/tree`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| departmentName | string | 否 | 部门名称（模糊搜索） |
| departmentStatus | string | 否 | 部门状态：normal-正常，disabled-停用 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": [
    {
      "id": "1",
      "departmentName": "技术部",
      "departmentCode": "TECH001",
      "departmentType": "business",
      "departmentStatus": "normal",
      "departmentHead": "张三",
      "description": "负责技术开发工作",
      "parentId": null,
      "createTime": "2024-01-01T10:00:00",
      "children": [
        {
          "id": "2",
          "departmentName": "前端开发组",
          "departmentCode": "FRONTEND001",
          "departmentType": "business",
          "departmentStatus": "normal",
          "departmentHead": "李四",
          "description": "负责前端开发",
          "parentId": "1",
          "createTime": "2024-01-01T10:00:00",
          "children": []
        }
      ]
    }
  ],
  "timestamp": 1640995200000
}
```

### 3. 获取部门详情

**接口描述：** 根据ID获取部门详细信息

**请求方式：** GET

**接口地址：** `/departments/detail`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| id | string | 是 | 部门ID |

**请求示例：**
```
GET /departments/detail?id=1
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": {
    "id": "1",
    "departmentName": "技术部",
    "departmentCode": "TECH001",
    "departmentType": "business",
    "departmentStatus": "normal",
    "departmentHead": "张三",
    "description": "负责技术开发工作",
    "parentId": null,
    "createTime": "2024-01-01T10:00:00",
    "updateTime": "2024-01-01T10:00:00"
  },
  "timestamp": 1640995200000
}
```

### 4. 新增部门

**接口描述：** 创建新的部门

**请求方式：** POST

**接口地址：** `/departments`

**请求参数：**

```json
{
  "departmentName": "技术部",
  "departmentCode": "TECH001",
  "departmentType": "business",
  "departmentStatus": "normal",
  "departmentHead": "张三",
  "description": "负责技术开发工作",
  "parentId": null,
  "remark": "备注信息"
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| departmentName | string | 是 | 部门名称 |
| departmentCode | string | 是 | 部门编码 |
| departmentType | string | 是 | 部门类型：business-业务部门，support-支持部门，management-管理部门 |
| departmentStatus | string | 是 | 部门状态：normal-正常，disabled-停用 |
| departmentHead | string | 否 | 部门负责人 |
| description | string | 否 | 部门描述 |
| parentId | string | 否 | 上级部门ID |
| remark | string | 否 | 备注 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": {
    "id": "1",
    "departmentName": "技术部",
    "departmentCode": "TECH001",
    "departmentType": "business",
    "departmentStatus": "normal",
    "departmentHead": "张三",
    "description": "负责技术开发工作",
    "parentId": null,
    "createTime": "2024-01-01T10:00:00"
  },
  "timestamp": 1640995200000
}
```

### 5. 编辑部门

**接口描述：** 更新部门信息，ID放入参数实体中

**请求方式：** POST

**接口地址：** `/departments/update`

**请求参数：**

```json
{
  "id": "1",
  "departmentName": "技术部",
  "departmentCode": "TECH001",
  "departmentType": "business",
  "departmentStatus": "normal",
  "departmentHead": "张三",
  "description": "负责技术开发工作",
  "parentId": null,
  "remark": "备注信息"
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| id | string | 是 | 部门ID |
| departmentName | string | 是 | 部门名称 |
| departmentCode | string | 是 | 部门编码 |
| departmentType | string | 是 | 部门类型 |
| departmentStatus | string | 是 | 部门状态 |
| departmentHead | string | 否 | 部门负责人 |
| description | string | 否 | 部门描述 |
| parentId | string | 否 | 上级部门ID |
| remark | string | 否 | 备注 |

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": {
    "id": "1",
    "departmentName": "技术部",
    "departmentCode": "TECH001",
    "departmentType": "business",
    "departmentStatus": "normal",
    "departmentHead": "张三",
    "description": "负责技术开发工作",
    "parentId": null,
    "updateTime": "2024-01-01T10:00:00"
  },
  "timestamp": 1640995200000
}
```

### 6. 删除部门

**接口描述：** 根据ID删除部门

**请求方式：** GET

**接口地址：** `/departments/delete`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| id | string | 是 | 部门ID |

**请求示例：**
```
GET /departments/delete?id=1
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": null,
  "timestamp": 1640995200000
}
```

### 7. 获取字典数据

**接口描述：** 获取部门状态字典数据

**请求方式：** GET

**接口地址：** `/api/v1/basic/dic`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| dicCategoryCode | string | 是 | 字典分类编码，部门状态传值为"1001" |

**请求示例：**
```
GET /api/v1/basic/dic?dicCategoryCode=1001
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "result": [
    {
      "value": "normal",
      "label": "正常"
    },
    {
      "value": "disabled",
      "label": "停用"
    }
  ],
  "timestamp": 1640995200000
}
```

## 数据模型

### Department 部门信息

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | string | 部门ID |
| departmentName | string | 部门名称 |
| departmentCode | string | 部门编码 |
| departmentType | string | 部门类型 |
| departmentStatus | string | 部门状态 |
| departmentHead | string | 部门负责人 |
| description | string | 部门描述 |
| parentId | string | 上级部门ID |
| departmentLevel | number | 部门层级 |
| createTime | string | 创建时间 |
| updateTime | string | 更新时间 |
| children | Department[] | 子部门列表 |

### 状态码说明

| 状态码 | 说明 |
|-------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要在请求头中携带认证信息
2. 分页查询的分页参数不能固定为 1/9999
3. 树形结构数据支持无限级嵌套
4. 部门编码在同一级别下不能重复
5. 删除部门前需要确保没有子部门和关联的用户
6. 部门状态字典数据通过 dicCategoryCode=1001 获取

## 版本更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2024-01-01 | 初始版本，包含基础的部门管理功能 |
| v1.1 | 2024-01-15 | 增加树形结构支持和字典数据接口 |
| v1.2 | 2024-01-20 | 优化分页查询性能，增加查看详情功能 | 