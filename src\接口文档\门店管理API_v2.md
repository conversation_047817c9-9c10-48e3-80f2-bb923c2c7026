# 门店管理 API 文档 v2.0

## 接口基础信息

- **基础路径**: ``（相对路径）
- **认证方式**: Bearer Token
- **响应格式**: JSON

## 通用响应格式

```typescript
interface ApiResponse<T> {
  code: number;        // 状态码，200表示成功
  message: string;     // 响应消息
  data: T;            // 响应数据
  timestamp: number;   // 时间戳
}

interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总记录数
  current: number;     // 当前页码
  size: number;        // 每页大小
  pages: number;       // 总页数
}

interface SelectOption {
  value: string;       // 选项值
  label: string;       // 显示文本
}
```

## 1. 分页列表展示数据（树形结构）

- **接口地址**: `POST /stores/page`
- **请求方式**: POST
- **描述**: 分页查询门店列表，支持树形结构展示，有查询条件，分页参数可配置

### 请求参数

```typescript
interface StoreQueryParams {
  current: number;       // 当前页码，必填
  size: number;          // 每页大小，必填
  storeName?: string;    // 门店名称（模糊查询）
  storeCode?: string;    // 门店编码（模糊查询）
  storeStatus?: string;  // 门店状态
}
```

### 请求示例

```json
{
  "current": 1,
  "size": 10,
  "storeName": "北京",
  "storeCode": "BJ",
  "storeStatus": "active"
}
```

### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "records": [
      {
        "id": "store_001",
        "storeCode": "BJ001",
        "storeName": "北京总店",
        "storeType": "headquarter",
        "storeProperties": ["sales", "after_sales"],
        "storeStatus": "active",
        "manager": "张三",
        "contactPerson": "李四",
        "contactPhone": "13800138000",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "detailAddress": "三里屯街道1号",
        "parentId": null,
        "createTime": "2024-01-01T00:00:00.000Z",
        "children": []
      }
    ],
    "total": 1,
    "current": 1,
    "size": 10,
    "pages": 1
  },
  "timestamp": 1704067200000
}
```

## 2. 树形结构查询门店数据

- **接口地址**: `GET /stores/tree`
- **请求方式**: GET
- **描述**: 获取所有门店的树形结构数据，无分页限制

### 请求参数

```typescript
interface StoreTreeParams {
  storeName?: string;    // 门店名称（可选）
  storeStatus?: string;  // 门店状态（可选）
}
```

### 请求示例

```
GET /stores/tree?storeName=北京&storeStatus=active
```

### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "store_001",
      "storeCode": "BJ001",
      "storeName": "北京总店",
      "storeType": "headquarter",
      "storeStatus": "active",
      "children": [
        {
          "id": "store_002",
          "storeCode": "BJ002",
          "storeName": "北京分店1",
          "storeType": "branch",
          "parentId": "store_001"
        }
      ]
    }
  ],
  "timestamp": 1704067200000
}
```

## 3. 查询门店详情

- **接口地址**: `GET /stores/{id}`
- **请求方式**: GET
- **描述**: 根据门店ID获取门店详细信息

### 请求参数

- **路径参数**: `id` - 门店ID（必填）

### 请求示例

```
GET /stores/store_001
```

### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "store_001",
    "storeCode": "BJ001",
    "storeName": "北京总店",
    "storeType": "headquarter",
    "storeProperties": ["sales", "after_sales"],
    "storeStatus": "active",
    "manager": "张三",
    "contactPerson": "李四",
    "contactPhone": "13800138000",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "detailAddress": "三里屯街道1号",
    "parentId": null,
    "remark": "总店备注信息",
    "createTime": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": 1704067200000
}
```

## 4. 新增门店

- **接口地址**: `POST /stores`
- **请求方式**: POST
- **描述**: 创建新的门店

### 请求参数

```typescript
interface CreateStoreRequest {
  storeCode: string;                           // 门店编码，必填
  storeName: string;                           // 门店名称，必填
  storeType: 'headquarter' | 'main' | 'branch' | 'warehouse'; // 门店类型，必填
  storeProperties: ('sales' | 'after_sales')[]; // 门店属性，可多选
  storeStatus: 'active' | 'inactive';          // 门店状态，必填
  manager?: string;                            // 门店经理
  contactPerson?: string;                      // 联系人
  contactPhone?: string;                       // 联系电话
  province?: string;                           // 省份
  city?: string;                               // 城市
  district?: string;                           // 区县
  detailAddress?: string;                      // 详细地址
  parentId?: string;                          // 父门店ID
  remark?: string;                            // 备注
}
```

### 请求示例

```json
{
  "storeCode": "SH001",
  "storeName": "上海总店",
  "storeType": "main",
  "storeProperties": ["sales", "after_sales"],
  "storeStatus": "active",
  "manager": "王五",
  "contactPerson": "赵六",
  "contactPhone": "13900139000",
  "province": "上海市",
  "city": "上海市",
  "district": "黄浦区",
  "detailAddress": "南京路1号",
  "remark": "上海地区总店"
}
```

## 5. 编辑门店

- **接口地址**: `POST /stores/update`
- **请求方式**: POST
- **描述**: 更新指定门店信息，ID包含在请求体中

### 请求参数

```typescript
interface UpdateStoreRequest {
  id: string;                                  // 门店ID，必填
  storeName?: string;                          // 门店名称
  storeType?: 'headquarter' | 'main' | 'branch' | 'warehouse'; // 门店类型
  storeProperties?: ('sales' | 'after_sales')[]; // 门店属性
  storeStatus?: 'active' | 'inactive';         // 门店状态
  manager?: string;                            // 门店经理
  contactPerson?: string;                      // 联系人
  contactPhone?: string;                       // 联系电话
  province?: string;                           // 省份
  city?: string;                               // 城市
  district?: string;                           // 区县
  detailAddress?: string;                      // 详细地址
  parentId?: string;                          // 父门店ID
  remark?: string;                            // 备注
}
```

### 请求示例

```json
{
  "id": "store_003",
  "storeName": "上海旗舰店",
  "manager": "新经理",
  "contactPhone": "13901139000"
}
```

## 6. 删除门店

- **接口地址**: `GET /stores/delete`
- **请求方式**: GET
- **描述**: 删除指定门店

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 门店ID |

### 请求示例

```
GET /stores/delete?id=store_003
```

## 7. 字典查询接口

- **接口地址**: `GET /basic/dic`
- **请求方式**: GET
- **描述**: 查询字典数据，用于门店状态下拉框等

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| dicCategoryCode | string | 是 | 字典分类编码，门店状态传 "1000" |

### 请求示例

```
GET /basic/dic?dicCategoryCode=1000
```

### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "value": "active",
      "label": "正常"
    },
    {
      "value": "inactive",
      "label": "停用"
    }
  ],
  "timestamp": 1704067200000
}
```

## 门店数据模型

```typescript
interface Store {
  id: string;                                  // 门店ID
  storeCode: string;                           // 门店编码
  storeName: string;                           // 门店名称
  storeType?: 'headquarter' | 'main' | 'branch' | 'warehouse'; // 门店类型
  storeProperties?: ('sales' | 'after_sales')[]; // 门店属性
  storeStatus: 'active' | 'inactive';          // 门店状态
  manager?: string;                            // 门店经理
  contactPerson?: string;                      // 联系人
  contactPhone?: string;                       // 联系电话
  province?: string;                           // 省份
  city?: string;                               // 城市
  district?: string;                           // 区县
  detailAddress?: string;                      // 详细地址
  parentId?: string;                          // 父门店ID
  remark?: string;                            // 备注
  createTime: string;                         // 创建时间
  children?: Store[];                         // 子门店（仅树形结构时返回）
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 更新记录

| 日期 | 版本 | 更新内容 |
|------|------|----------|
| 2024-01-01 | v1.0 | 初始版本 |
| 2024-01-02 | v2.0 | 新增树形结构查询、详情查询、字典查询接口，支持查看详情功能 |
| 2024-01-03 | v2.1 | 修改编辑接口为POST方式且ID包含在请求体中，删除接口改为GET方式且ID作为查询参数，去除API基础路径前缀 | 