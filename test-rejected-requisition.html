<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已驳回叫料单编辑功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-rejected {
            background-color: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fbc4c4;
        }
        .status-submitted {
            background-color: #f0f9ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background: white;
        }
        .btn-primary {
            background-color: #409eff;
            color: white;
            border-color: #409eff;
        }
        .btn:disabled {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #c0c4cc;
            cursor: not-allowed;
        }
        .requisition-item {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-success {
            background-color: #f0f9ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        .test-warning {
            background-color: #fffbf0;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            text-align: center;
        }
        .modal-buttons {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>已驳回叫料单编辑功能测试</h1>
        
        <div class="test-section">
            <h2>测试场景</h2>
            <p>验证已驳回状态的叫料单编辑提交时会显示确认弹窗，提示用户此次修改会创建新的叫料单。</p>
        </div>
        
        <div class="test-section">
            <h3>模拟叫料单数据</h3>
            
            <div class="requisition-item">
                <h4>叫料单号: JL2023001 <span class="status-tag status-rejected">已驳回</span></h4>
                <p><strong>驳回原因:</strong> 零件规格不符合要求，请重新确认</p>
                <p><strong>叫料日期:</strong> 2025-06-20</p>
                <p><strong>零件信息:</strong></p>
                <ul>
                    <li>零件名称: 刹车片</li>
                    <li>零件编号: BP-001</li>
                    <li>数量: 4个</li>
                </ul>
                <button class="btn btn-primary" onclick="editRejectedRequisition('JL2023001')">编辑</button>
                <button class="btn" disabled>详情</button>
            </div>
            
            <div class="requisition-item">
                <h4>叫料单号: JL2023002 <span class="status-tag status-submitted">已提交</span></h4>
                <p><strong>叫料日期:</strong> 2025-06-21</p>
                <p><strong>零件信息:</strong></p>
                <ul>
                    <li>零件名称: 机油滤芯</li>
                    <li>零件编号: OF-002</li>
                    <li>数量: 2个</li>
                </ul>
                <button class="btn btn-primary" onclick="editSubmittedRequisition('JL2023002')">编辑</button>
                <button class="btn" disabled>详情</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试结果</h3>
            <div id="testResults">
                <div class="test-result test-warning">
                    ⏳ 请点击"已驳回"状态的叫料单的"编辑"按钮进行测试...
                </div>
            </div>
        </div>
    </div>

    <!-- 确认弹窗 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <h3>确认</h3>
            <p id="confirmMessage">此次修改会创建一条新的叫料单，原叫料单保持已驳回状态。确定要继续吗？</p>
            <div class="modal-buttons">
                <button class="btn btn-primary" onclick="confirmSubmit()">确定</button>
                <button class="btn" onclick="cancelSubmit()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let currentEditingRequisition = null;
        
        // 编辑已驳回的叫料单
        function editRejectedRequisition(requisitionNumber) {
            currentEditingRequisition = {
                number: requisitionNumber,
                status: 'rejected'
            };
            
            // 模拟编辑过程
            setTimeout(() => {
                // 模拟用户修改了数据并点击提交
                submitRejectedRequisition();
            }, 500);
            
            updateTestResults('开始编辑已驳回的叫料单: ' + requisitionNumber);
        }
        
        // 编辑已提交的叫料单
        function editSubmittedRequisition(requisitionNumber) {
            currentEditingRequisition = {
                number: requisitionNumber,
                status: 'submitted'
            };
            
            // 已提交状态的叫料单直接更新，不需要弹窗
            setTimeout(() => {
                updateTestResults('已提交状态的叫料单直接更新，无需确认弹窗');
                alert('叫料单更新成功');
            }, 500);
        }
        
        // 提交已驳回的叫料单
        function submitRejectedRequisition() {
            if (currentEditingRequisition && currentEditingRequisition.status === 'rejected') {
                // 显示确认弹窗
                document.getElementById('confirmModal').style.display = 'block';
                updateTestResults('✅ 检测到已驳回状态，显示确认弹窗');
            }
        }
        
        // 确认提交
        function confirmSubmit() {
            document.getElementById('confirmModal').style.display = 'none';
            
            // 模拟创建新的叫料单
            const newRequisitionNumber = 'JL' + Date.now().toString().slice(-6);
            
            updateTestResults('✅ 用户确认提交，创建新叫料单: ' + newRequisitionNumber);
            alert('已驳回的叫料单重新提交成功，已生成新的叫料单: ' + newRequisitionNumber);
            
            currentEditingRequisition = null;
        }
        
        // 取消提交
        function cancelSubmit() {
            document.getElementById('confirmModal').style.display = 'none';
            updateTestResults('❌ 用户取消提交，保持编辑状态');
            currentEditingRequisition = null;
        }
        
        // 更新测试结果
        function updateTestResults(message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultItem = document.createElement('div');
            resultItem.className = 'test-result test-success';
            resultItem.innerHTML = `[${timestamp}] ${message}`;
            
            resultsDiv.appendChild(resultItem);
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                modal.style.display = 'none';
                currentEditingRequisition = null;
            }
        }
    </script>
</body>
</html>
