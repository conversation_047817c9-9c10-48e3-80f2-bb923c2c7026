<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报损记录详情 - 到货单号列测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
        }
        .scrap-source-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        .source-receipt {
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        .source-repair {
            background-color: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        .source-other {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background: white;
            color: #409eff;
            text-decoration: none;
            font-size: 12px;
        }
        .btn:hover {
            background-color: #ecf5ff;
        }
        .highlight {
            background-color: #fff2e8;
            border: 2px solid #fa8c16;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-success {
            background-color: #f0f9ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 1000px;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .close {
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        .close:hover {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>报损记录详情 - 到货单号列功能测试</h1>
        
        <div class="test-section">
            <h2>测试场景</h2>
            <p>验证在报损记录详情页面中，当报损来源为"收货报损"时，表格会显示"到货单号"列。</p>
        </div>
        
        <div class="test-section">
            <h3>模拟报损记录列表</h3>
            <p>点击"详情"按钮查看报损单详情，观察是否显示到货单号列：</p>
            
            <table>
                <thead>
                    <tr>
                        <th>报损单号</th>
                        <th>报损日期</th>
                        <th>报损来源</th>
                        <th>总数量</th>
                        <th>项目数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>BS202301001</td>
                        <td>2023-01-05</td>
                        <td><span class="scrap-source-tag source-repair">维修</span></td>
                        <td>2</td>
                        <td>2</td>
                        <td><button class="btn" onclick="showDetails('BS202301001', 'repair')">详情</button></td>
                    </tr>
                    <tr class="highlight">
                        <td>BS202301002</td>
                        <td>2023-01-10</td>
                        <td><span class="scrap-source-tag source-receipt">收货</span></td>
                        <td>9</td>
                        <td>3</td>
                        <td><button class="btn" onclick="showDetails('BS202301002', 'receipt')">详情</button></td>
                    </tr>
                    <tr>
                        <td>BS202301003</td>
                        <td>2023-01-15</td>
                        <td><span class="scrap-source-tag source-other">其它</span></td>
                        <td>1</td>
                        <td>1</td>
                        <td><button class="btn" onclick="showDetails('BS202301003', 'other')">详情</button></td>
                    </tr>
                    <tr class="highlight">
                        <td>BS202301004</td>
                        <td>2023-01-20</td>
                        <td><span class="scrap-source-tag source-receipt">收货</span></td>
                        <td>5</td>
                        <td>2</td>
                        <td><button class="btn" onclick="showDetails('BS202301004', 'receipt')">详情</button></td>
                    </tr>
                </tbody>
            </table>
            
            <p><strong>说明：</strong>高亮的行表示收货报损，点击这些行的"详情"按钮应该显示包含"到货单号"列的表格。</p>
        </div>
        
        <div class="test-section">
            <h3>测试结果</h3>
            <div id="testResults">
                <div class="test-result test-success">
                    ⏳ 请点击上方表格中的"详情"按钮进行测试...
                </div>
            </div>
        </div>
    </div>

    <!-- 报损单详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">报损单详情</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="modalContent">
                <!-- 详情内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 模拟报损单详情数据
        const scrapOrderDetails = {
            'BS202301001': {
                scrapSource: 'repair',
                items: [
                    { partName: '刹车片', partNumber: 'BP001', quantity: 1, scrapSource: 'repair' },
                    { partName: '机油滤清器', partNumber: 'OF002', quantity: 1, scrapSource: 'repair' }
                ]
            },
            'BS202301002': {
                scrapSource: 'receipt',
                items: [
                    { partName: '火花塞', partNumber: 'SP001', quantity: 4, scrapSource: 'receipt', deliveryOrderNumber: 'DH20230001' },
                    { partName: '空气滤清器', partNumber: 'AF001', quantity: 2, scrapSource: 'receipt', deliveryOrderNumber: 'DH20230001' },
                    { partName: '机油', partNumber: 'OIL001', quantity: 3, scrapSource: 'receipt', deliveryOrderNumber: 'DH20230002' }
                ]
            },
            'BS202301003': {
                scrapSource: 'other',
                items: [
                    { partName: '轮胎', partNumber: 'TIRE001', quantity: 1, scrapSource: 'other' }
                ]
            },
            'BS202301004': {
                scrapSource: 'receipt',
                items: [
                    { partName: '变速箱油', partNumber: 'TO001', quantity: 2, scrapSource: 'receipt', deliveryOrderNumber: 'DH20230003' },
                    { partName: '冷却液', partNumber: 'CL001', quantity: 3, scrapSource: 'receipt', deliveryOrderNumber: 'DH20230003' }
                ]
            }
        };

        function showDetails(scrapOrderNumber, scrapSource) {
            const details = scrapOrderDetails[scrapOrderNumber];
            if (!details) return;

            const hasReceiptSource = details.items.some(item => item.scrapSource === 'receipt');
            
            document.getElementById('modalTitle').textContent = `报损单详情 - ${scrapOrderNumber}`;
            
            let tableHTML = '<table><thead><tr>';
            tableHTML += '<th>零件名称</th>';
            tableHTML += '<th>零件编号</th>';
            tableHTML += '<th>报损数量</th>';
            tableHTML += '<th>报损来源</th>';
            
            // 如果包含收货报损，添加到货单号列
            if (hasReceiptSource) {
                tableHTML += '<th style="background-color: #fff2e8; color: #fa8c16;">到货单号</th>';
            }
            
            tableHTML += '<th>操作</th>';
            tableHTML += '</tr></thead><tbody>';
            
            details.items.forEach(item => {
                tableHTML += '<tr>';
                tableHTML += `<td>${item.partName}</td>`;
                tableHTML += `<td>${item.partNumber}</td>`;
                tableHTML += `<td>${item.quantity}</td>`;
                tableHTML += `<td><span class="scrap-source-tag source-${item.scrapSource}">${getScrapSourceLabel(item.scrapSource)}</span></td>`;
                
                // 如果包含收货报损，显示到货单号列
                if (hasReceiptSource) {
                    tableHTML += `<td style="background-color: #fff2e8;">${item.deliveryOrderNumber || '-'}</td>`;
                }
                
                tableHTML += '<td><button class="btn">详情</button></td>';
                tableHTML += '</tr>';
            });
            
            tableHTML += '</tbody></table>';
            
            document.getElementById('modalContent').innerHTML = tableHTML;
            document.getElementById('detailModal').style.display = 'block';
            
            // 更新测试结果
            updateTestResults(scrapOrderNumber, hasReceiptSource);
        }

        function getScrapSourceLabel(source) {
            switch (source) {
                case 'receipt': return '收货';
                case 'repair': return '维修';
                case 'other': return '其它';
                default: return source;
            }
        }

        function closeModal() {
            document.getElementById('detailModal').style.display = 'none';
        }

        function updateTestResults(scrapOrderNumber, hasReceiptSource) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultItem = document.createElement('div');
            resultItem.className = 'test-result test-success';
            
            if (hasReceiptSource) {
                resultItem.innerHTML = `[${timestamp}] ✅ 测试通过：报损单 ${scrapOrderNumber} 包含收货报损，已显示"到货单号"列`;
            } else {
                resultItem.innerHTML = `[${timestamp}] ✅ 测试通过：报损单 ${scrapOrderNumber} 不包含收货报损，未显示"到货单号"列`;
            }
            
            resultsDiv.appendChild(resultItem);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('detailModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
