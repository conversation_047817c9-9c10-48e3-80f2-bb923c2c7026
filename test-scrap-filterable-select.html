<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报损记录 - 可输入筛选下拉框测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
        }
        .form-row {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            align-items: center;
        }
        .form-item {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }
        .form-item label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .select-box {
            position: relative;
            width: 220px;
        }
        .select-input {
            width: 100%;
            padding: 8px 30px 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
            cursor: pointer;
        }
        .select-input:focus {
            border-color: #409eff;
            outline: none;
        }
        .select-arrow {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #c0c4cc;
            pointer-events: none;
        }
        .select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
        .select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
        }
        .select-option:hover {
            background-color: #f5f7fa;
        }
        .select-option.selected {
            background-color: #409eff;
            color: white;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background: white;
            margin: 5px;
        }
        .btn-primary {
            background-color: #409eff;
            color: white;
            border-color: #409eff;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-success {
            background-color: #f0f9ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        .test-warning {
            background-color: #fffbf0;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        .highlight {
            background-color: #fff2e8;
            border: 2px solid #fa8c16;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>报损记录 - 可输入筛选下拉框功能测试</h1>
        
        <div class="test-section">
            <h2>测试场景</h2>
            <p>验证报损记录页面中的"零件名称"和"零件编号"字段改为可输入筛选下拉框，并且两个字段之间有联动效果。</p>
        </div>
        
        <div class="test-section">
            <h3>模拟报损记录筛选表单</h3>
            
            <div class="form-row">
                <div class="form-item">
                    <label>报损单号</label>
                    <input type="text" class="select-input" placeholder="请输入报损单号" />
                </div>
                
                <div class="form-item highlight">
                    <label>零件名称</label>
                    <div class="select-box">
                        <input 
                            type="text" 
                            class="select-input" 
                            id="partNameInput"
                            placeholder="可输入筛选下拉框" 
                            autocomplete="off"
                            oninput="filterPartNames(this.value)"
                            onfocus="showPartNameDropdown()"
                            onblur="hidePartNameDropdown()"
                        />
                        <span class="select-arrow">▼</span>
                        <div class="select-dropdown" id="partNameDropdown">
                            <!-- 选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
                
                <div class="form-item highlight">
                    <label>零件编号</label>
                    <div class="select-box">
                        <input 
                            type="text" 
                            class="select-input" 
                            id="partNumberInput"
                            placeholder="可输入筛选下拉框" 
                            autocomplete="off"
                            oninput="filterPartNumbers(this.value)"
                            onfocus="showPartNumberDropdown()"
                            onblur="hidePartNumberDropdown()"
                        />
                        <span class="select-arrow">▼</span>
                        <div class="select-dropdown" id="partNumberDropdown">
                            <!-- 选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-item">
                    <label>报损日期</label>
                    <input type="text" class="select-input" placeholder="开始日期 至 结束日期" />
                </div>
                
                <div class="form-item">
                    <label>报损来源</label>
                    <select class="select-input">
                        <option value="">请选择报损来源</option>
                        <option value="receipt">收货</option>
                        <option value="repair">维修</option>
                        <option value="other">其它</option>
                    </select>
                </div>
                
                <div class="form-item">
                    <label>单据状态</label>
                    <select class="select-input">
                        <option value="">请选择单据状态</option>
                        <option value="submitted">已提交</option>
                        <option value="approved">通过</option>
                        <option value="rejected">驳回</option>
                        <option value="voided">作废</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <button class="btn btn-primary" onclick="performSearch()">查询</button>
                <button class="btn" onclick="resetForm()">重置</button>
            </div>
            
            <p><strong>说明：</strong>高亮的字段为可输入筛选下拉框，支持输入筛选和联动选择。</p>
        </div>
        
        <div class="test-section">
            <h3>测试结果</h3>
            <div id="testResults">
                <div class="test-result test-warning">
                    ⏳ 请在上方的零件名称或零件编号字段中输入或选择内容进行测试...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟零件档案数据
        const partArchivesData = [
            { partName: '刹车片', partNumber: 'BP001' },
            { partName: '机油滤清器', partNumber: 'OF002' },
            { partName: '火花塞', partNumber: 'SP003' },
            { partName: '雨刮器', partNumber: 'WW004' },
            { partName: '空气滤芯', partNumber: 'AF005' },
            { partName: '轮胎', partNumber: 'TIRE001' },
            { partName: '变速箱油', partNumber: 'TO001' },
            { partName: '冷却液', partNumber: 'CL001' },
            { partName: '电池', partNumber: 'BAT001' },
            { partName: '大灯', partNumber: 'HL001' }
        ];

        let partNameDropdownTimeout;
        let partNumberDropdownTimeout;

        // 生成零件名称选项
        function generatePartNameOptions(filter = '') {
            const dropdown = document.getElementById('partNameDropdown');
            const filteredData = partArchivesData.filter(item => 
                item.partName.toLowerCase().includes(filter.toLowerCase())
            );
            
            dropdown.innerHTML = filteredData.map(item => 
                `<div class="select-option" onclick="selectPartName('${item.partName}')">${item.partName}</div>`
            ).join('');
        }

        // 生成零件编号选项
        function generatePartNumberOptions(filter = '') {
            const dropdown = document.getElementById('partNumberDropdown');
            const filteredData = partArchivesData.filter(item => 
                item.partNumber.toLowerCase().includes(filter.toLowerCase())
            );
            
            dropdown.innerHTML = filteredData.map(item => 
                `<div class="select-option" onclick="selectPartNumber('${item.partNumber}')">${item.partNumber}</div>`
            ).join('');
        }

        // 显示零件名称下拉框
        function showPartNameDropdown() {
            clearTimeout(partNameDropdownTimeout);
            generatePartNameOptions();
            document.getElementById('partNameDropdown').style.display = 'block';
        }

        // 隐藏零件名称下拉框
        function hidePartNameDropdown() {
            partNameDropdownTimeout = setTimeout(() => {
                document.getElementById('partNameDropdown').style.display = 'none';
            }, 200);
        }

        // 显示零件编号下拉框
        function showPartNumberDropdown() {
            clearTimeout(partNumberDropdownTimeout);
            generatePartNumberOptions();
            document.getElementById('partNumberDropdown').style.display = 'block';
        }

        // 隐藏零件编号下拉框
        function hidePartNumberDropdown() {
            partNumberDropdownTimeout = setTimeout(() => {
                document.getElementById('partNumberDropdown').style.display = 'none';
            }, 200);
        }

        // 筛选零件名称
        function filterPartNames(value) {
            generatePartNameOptions(value);
            updateTestResults('零件名称筛选', `输入内容: "${value}"`);
        }

        // 筛选零件编号
        function filterPartNumbers(value) {
            generatePartNumberOptions(value);
            updateTestResults('零件编号筛选', `输入内容: "${value}"`);
        }

        // 选择零件名称
        function selectPartName(partName) {
            document.getElementById('partNameInput').value = partName;
            
            // 联动设置零件编号
            const selectedPart = partArchivesData.find(item => item.partName === partName);
            if (selectedPart) {
                document.getElementById('partNumberInput').value = selectedPart.partNumber;
                updateTestResults('零件名称联动', `选择零件名称: "${partName}"，自动设置零件编号: "${selectedPart.partNumber}"`);
            }
            
            document.getElementById('partNameDropdown').style.display = 'none';
        }

        // 选择零件编号
        function selectPartNumber(partNumber) {
            document.getElementById('partNumberInput').value = partNumber;
            
            // 联动设置零件名称
            const selectedPart = partArchivesData.find(item => item.partNumber === partNumber);
            if (selectedPart) {
                document.getElementById('partNameInput').value = selectedPart.partName;
                updateTestResults('零件编号联动', `选择零件编号: "${partNumber}"，自动设置零件名称: "${selectedPart.partName}"`);
            }
            
            document.getElementById('partNumberDropdown').style.display = 'none';
        }

        // 执行搜索
        function performSearch() {
            const partName = document.getElementById('partNameInput').value;
            const partNumber = document.getElementById('partNumberInput').value;
            updateTestResults('执行搜索', `零件名称: "${partName}"，零件编号: "${partNumber}"`);
        }

        // 重置表单
        function resetForm() {
            document.getElementById('partNameInput').value = '';
            document.getElementById('partNumberInput').value = '';
            updateTestResults('重置表单', '已清空所有筛选条件');
        }

        // 更新测试结果
        function updateTestResults(action, details) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultItem = document.createElement('div');
            resultItem.className = 'test-result test-success';
            resultItem.innerHTML = `[${timestamp}] ✅ ${action}: ${details}`;
            
            resultsDiv.appendChild(resultItem);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            generatePartNameOptions();
            generatePartNumberOptions();
        });
    </script>
</body>
</html>
