import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      // 配置路径别名，@ 符号指向 src 目录
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
    extensions: ['.ts', '.d.ts', '.js', '.vue', '.json']
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 自动导入 SCSS 变量和 mixin
        additionalData: `@use "@/assets/styles/_variables.scss" as *; @use "@/assets/styles/_mixins.scss" as *;`
      }
    }
  },
  build: {
    // 确保动态导入的文件能正确被包含在构建中
    rollupOptions: {
      output: {
        // 确保动态导入的资源被正确处理
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    },
    // 增加块大小警告阈值
    chunkSizeWarningLimit: 1000
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: false,
    open: true,
    hmr: {
      host: 'localhost'
    },
    // **注意：由于没有独立的 Mock Server，这里移除了 proxy 配置**
    // 如果将来你需要代理真实后端接口，可以再添加回来，例如：
    // proxy: {
    //   '/api': {
    //     target: 'http://your-real-backend-ip:8080', // 真实后端接口地址
    //     changeOrigin: true,
    //     rewrite: (path) => path.replace(/^\/api/, '')
    //   }
    // }
  }
});
