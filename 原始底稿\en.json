{"common": {"confirm": "Confirm", "cancel": "Cancel", "search": "Search", "operationSuccessful": "Operation successful", "operationFailed": "Operation failed!", "reLoginPrompt": "<PERSON>gin expired, please re-login.", "warning": "Warning", "noPermission": "You have no permission for this operation.", "networkError": "Network error, please try again later.", "badRequest": "Bad Request.", "unauthorized": "Unauthorized, please re-login.", "forbidden": "Access Denied.", "notFound": "Request address error, resource not found.", "requestTimeout": "Request timed out.", "serverError": "Internal server error.", "notImplemented": "Service not implemented.", "badGateway": "Bad Gateway.", "serviceUnavailable": "Service Unavailable.", "gatewayTimeout": "Gateway Timeout.", "unknownError": "Unknown error.", "noResponse": "No response from server.", "requestSetupError": "Request setup error.", "reset": "Reset", "operations": "Operations", "yes": "Yes", "no": "No", "hour": "Hour", "hours": "Hours", "confirmDelete": "Are you sure to delete '{item}'?", "languageChanged": "Language changed successfully!", "edit": "Edit", "delete": "Delete", "startDate": "Start Date", "endDate": "End Date", "confirmSave": "Are you sure to save?", "confirmAdd": "Are you sure to add?", "exporting": "Exporting...", "noData": "No Data", "query": "Query", "queryItems": "Query Items", "sequence": "Sequence", "detail": "Detail", "to": "to", "fetchFailed": "Failed to fetch data", "exportReport": "Export Report", "exportingReport": "Exporting report...", "pleaseSelectOne": "Please select one item", "upload": "Upload", "good": "Good", "needsAttention": "Needs Attention", "bad": "Bad", "notApplicable": "N/A", "all": "All", "noDataTip": "No relevant data available, please try adjusting the filter conditions", "close": "Close", "confirmExport": "Confirm Export", "success": "Success", "paginationLayout": "total, sizes, prev, pager, next, jumper", "add": "Add", "loading": "Loading...", "total": "Total", "items": "items", "page": "page", "required": "Required", "pleaseSelect": "Please Select", "pleaseInput": "Please Input", "unknown": "Unknown", "vehicleAge": "Vehicle Age", "months": "months", "createWorkOrder": "Create Work Order", "submitConfirm": "Submit Confirmation", "assign": "Assign", "print": "Print", "recall": "Recall", "customerConfirm": "Customer Confirmation", "export": "Export", "void": "Void", "fillAllRequired": "Please fill in all required fields!"}, "login": {"title": "<PERSON><PERSON>", "username": "Username", "password": "Password", "loginButton": "<PERSON><PERSON>", "notFound": "Record not found", "ownerPhone": "Owner Phone", "confirmCreateRepairOrder": "Are you sure to create a repair order for record {checkinId}?"}, "sales": {"vehicleList": "Vehicle List", "id": "ID", "vin": "VIN", "model": "Model", "brand": "Brand", "color": "Color", "price": "Price", "statusLabel": "Status", "manufactureDate": "Manuf. Date", "engineNumber": "Engine No.", "status": {"in_stock": "In Stock", "sold": "Sold", "reserved": "Reserved"}, "addVehicle": "Add Vehicle", "editVehicle": "Edit Vehicle", "vinPlaceholder": "Enter VIN", "modelPlaceholder": "Enter Model", "brandPlaceholder": "Enter Brand", "statusPlaceholder": "Select Status", "colorPlaceholder": "Enter Color", "pricePlaceholder": "Enter Price", "manufactureDatePlaceholder": "Select Date", "engineNumberPlaceholder": "Enter Engine No.", "selectStatus": "Please select status"}, "customer": {"detailViewTitle": "Customer Details"}, "checkin": {"checkinList": "Check-in Records", "repairPersonName": "Repair Person Name", "repairPersonNamePlaceholder": "Enter repair person name", "repairPersonPhone": "Repair Person Phone", "repairPersonPhonePlaceholder": "Enter repair person phone", "createdAt": "Created At", "createdAtPlaceholder": "Select create date range", "export": "Export", "createRecord": "Create Record", "id": "No.", "checkinId": "Record No.", "licensePlate": "License Plate", "vehicleModelConfig": "Model Config", "color": "Color", "serviceAdvisor": "Service Advisor", "relatedRepairOrderId": "Related RO No.", "serviceType": "Service Type", "notes": "Notes", "viewDetails": "View Details", "edit": "Edit", "delete": "Delete", "createRepairOrder": "Create RO", "addCheckinRecord": "Add Check-in Record", "editCheckinRecord": "Edit Check-in Record", "licensePlatePlaceholder": "Enter license plate", "vehicleInfoNotFound": "No vehicle info found", "vehicleInfoAutoFill": "Auto-filled or manual entry", "vehicleAge": "Vehicle Age (Months)", "ownerInfo": "Owner Info", "serviceTypePlaceholder": "Select service type", "notesPlaceholder": "Enter notes", "save": "Save", "cancel": "Cancel", "serviceTypeOptions": {"repair": "Repair", "maintenance": "Maintenance", "inspection": "Inspection", "paint": "Paint"}, "repairOrderAlreadyExists": "Repair order already exists for this check-in record.", "repairOrderCreatedSuccess": "Repair order created successfully!", "notFound": "Record not found", "ownerPhone": "Owner Phone"}, "afterSales": {"appointmentManagement": "Appointment Management", "breadcrumb": "After Sales / Appointment Management", "status": "Appointment Status", "selectStatus": "Select Appointment Status", "serviceType": "Service Type", "selectServiceType": "Select Service Type", "serviceAdvisor": "Service Advisor", "selectServiceAdvisor": "Select Service Advisor", "appointmentNo": "Appointment No.", "appointmentNoPlaceholder": "Enter appointment no.", "customerName": "Customer Name", "customerNamePlaceholder": "Enter customer name", "customerPhone": "Customer Phone", "customerPhonePlaceholder": "Enter customer phone", "createTime": "Create Time", "createTimePlaceholder": "Select create time", "export": "Export", "createAppointment": "Create Appointment", "serialNumber": "No.", "licensePlate": "License Plate", "vehicleModel": "Vehicle Model", "vehicleColor": "Color", "expectedArrivalTime": "Expected Arrival Time", "actualArrivalTime": "Actual Arrival Time", "operations": "Operations", "viewDetails": "View Details", "edit": "Edit", "cancelAppointment": "<PERSON>cel Appointment", "checkIn": "Check-in", "statuses": {"not_arrived": "Not Arrived", "arrived": "Arrived", "cancelled": "Cancelled", "no_show": "No Show", "pending_payment": "Pending Payment"}, "serviceTypes": {"maintenance": "Maintenance", "repair": "Repair"}, "confirmCancel": "Are you sure to cancel this appointment?", "appointmentDetail": "Appointment Detail", "appointmentId": "Appointment ID", "appointmentTime": "Appointment Date", "customerInfo": "Customer Info", "vehicleInfo": "Vehicle Info", "mileage": "Mileage", "appointmentInfo": "Appointment Info", "store": "Store", "technician": "Technician", "serviceContent": "Service Content", "paymentInfo": "Payment Info", "paymentStatus": "Payment Status", "paymentAmount": "Payment Amount", "paymentOrderNumber": "Payment Order No.", "paymentStatuses": {"paid": "Paid", "unpaid": "Unpaid", "refunded": "Refunded"}}, "orderApproval": {"pageTitle": "Order Approval", "pendingTab": "To be approved", "approvedTab": "Approved", "searchTitle": "Filter Conditions", "approvalType": "Approval Type", "approvalTypePlaceholder": "Please select approval type", "cancelOrderApproval": "Cancel Order Approval", "modifyOrderApproval": "Modify Order Approval", "vehicleColorModificationApproval": "Vehicle Color Modification Approval", "orderNumber": "Order Number", "orderNumberPlaceholder": "Please enter order number", "submittedBy": "Submitted by", "submittedByPlaceholder": "Please enter submitter name", "submissionTime": "Submission Time", "submissionTimeEnd": "Submission Time End", "startDate": "Start Date", "endDate": "End Date", "approvalStatus": "Approval Status", "approvalStatusPlaceholder": "Please select approval status", "pendingInitialReview": "Pending Initial Review", "pendingFinalReview": "Pending Final Review", "aboutToTimeout": "About to Timeout", "approvalResult": "A<PERSON><PERSON><PERSON> Result", "approvalResultPlaceholder": "Please select approval result", "approved": "Approved", "rejected": "Rejected", "timeoutRejected": "Timeout Rejected", "store": "Store", "storePlaceholder": "Please select store", "serialNumber": "Serial Number", "approvalNumber": "Approval Number", "remainingTime": "Remaining Time", "approvalTime": "Approval Time", "approvedBy": "Approved By", "approve": "Approve", "reject": "Reject", "approvalHistory": "Approval History", "totalCount": "Total {count} records", "selectedCount": "Selected {count} records", "batchApprove": "<PERSON><PERSON> Approve", "batchReject": "<PERSON>ch Reject", "days": "days", "hours": "hours", "minutes": "minutes", "timeout": "Timeout", "urgent": "<PERSON><PERSON>", "aboutTimeout": "About Timeout", "highPriority": "High Priority", "initialReview": "Initial Review", "finalReview": "Final Review", "pendingApprovalList": "Pending Approval List", "approvedList": "Approved List", "fetchDataFailed": "Failed to fetch data", "orderDetailNotImplemented": "Order detail function not implemented yet", "approveSuccess": "Approval successful", "rejectSuccess": "Rejection successful", "operationFailed": "Operation failed", "batchOperationSuccess": "Batch operation successful", "batchOperationFailed": "Batch operation failed", "exportSuccess": "Export successful", "exportFailed": "Export failed"}, "invoice": {"title": "Invoice Management", "invoiceNumber": "Invoice Number", "invoiceNumberPlaceholder": "Enter invoice number", "customerName": "Customer Name", "customerNamePlaceholder": "Enter customer name", "customerPhone": "Customer Phone", "customerPhonePlaceholder": "Enter customer phone", "customerEmail": "Customer <PERSON><PERSON>", "customerEmailPlaceholder": "Enter customer email", "orderNumber": "Order Number", "orderNumberPlaceholder": "Enter order number", "vin": "VIN Code", "vinPlaceholder": "Enter VIN code", "salesType": "Sales Type", "salesStore": "Sales Store", "salesConsultant": "Sales Consultant", "invoiceDate": "Invoice Date", "invoiceDateRange": "Invoice Date Range", "batchPrint": "Batch Print", "export": "Export", "detail": "Detail", "print": "Print", "email": "Email", "log": "Log", "invoiceAmount": "Invoice Amount", "createdTime": "Created Time", "customerAddress": "Customer Address", "customerState": "Customer State", "customerCity": "Customer City", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "paymentMethod": "Payment Method", "financeCompany": "Finance Company", "loanAmount": "<PERSON><PERSON>", "emailConfirm": "Email Confirmation", "confirmSendEmail": "Confirm to send email to customer?", "exportConfig": "Export Configuration", "exportFormat": "Export Format", "exportScope": "Export <PERSON>", "currentPage": "Current Page", "allData": "All Data", "filteredData": "Filtered Data", "operationLog": "Operation Log", "operationType": "Operation Type", "operator": "Operator", "operationTime": "Operation Time", "operationResult": "Operation Result", "remark": "Remark", "printSuccess": "Print successful", "emailSentSuccess": "<PERSON>ail sent successfully", "exportSuccess": "Export successful", "batchPrintSuccess": "Batch print successful", "pleaseSelectRecords": "Please select records to operate", "invoiceDetail": "Invoice Detail", "basicInfo": "Invoice Basic Information", "customerInfo": "Customer Detailed Information", "vehicleInfo": "Vehicle Detailed Information", "financeInfo": "Finance Information Details", "insuranceInfo": "Insurance Information Details", "priceStructure": "Price Structure Details", "receipts": "Receipt Details Information", "otrFees": "OTR Fees Details", "companyName": "Company Name", "companyAddress": "Company Address", "gstNumber": "GST Number", "sstNumber": "SST Number", "engineNumber": "Engine Number", "chassisNumber": "<PERSON><PERSON><PERSON> Number", "engineCapacity": "Engine Capacity", "transmission": "Transmission", "loanTerm": "<PERSON>an <PERSON>", "months": "Months", "interestRate": "Interest Rate", "monthlyPayment": "Monthly Payment", "insuranceCompany": "Insurance Company", "agentCode": "Agent Code", "policyNumber": "Policy Number", "policyDate": "Policy Date", "insuranceAmount": "Insurance Amount", "vehiclePrice": "Vehicle Sales Price", "adjustmentAmount": "Adjustment Amount", "accessories": "Accessory Details", "category": "Category", "accessoryName": "Accessory Name", "unitPrice": "Unit Price", "quantity": "Quantity", "totalPrice": "Total Price", "feeType": "Fee Type", "description": "Description", "amount": "Amount", "receiptNumber": "Receipt Number", "effectiveDate": "Effective Date", "paymentDate": "Payment Date", "bankName": "Bank Name", "accountNumber": "Account Number", "status": "Status", "invoiceStatuses": {"issued": "Issued", "printed": "Printed", "sent": "<PERSON><PERSON>"}, "operationTypes": {"view": "View Detail", "print": "Print"}, "operationResults": {"success": "Success", "failed": "Failed"}, "operationRemarks": {"viewDetail": "View invoice detail", "printSuccess": "Invoice printed successfully"}, "messages": {"fetchListFailed": "Failed to fetch invoice list", "fetchStoreListFailed": "Failed to fetch store list", "fetchConsultantListFailed": "Failed to fetch consultant list", "fetchDetailFailed": "Failed to fetch invoice detail", "printFailed": "Print failed", "batchPrintFailed": "Batch print failed", "fetchInvoiceInfoFailed": "Failed to fetch invoice information", "emailSendFailed": "Email send failed", "exportFailed": "Export failed"}, "salesTypeOptions": {"PHP": "PHP", "CASH": "Cash", "FINANCE": "Finance"}}, "inspectionForm": {"status": {"pending": "Pending Inspection", "inProgress": "Inspecting", "pendingConfirm": "Pending Confirmation", "confirmed": "Confirmed", "undefined": "Unknown Status"}}, "menu": {"home": "DMS Dashboard", "customer-detail": "Customer Detail", "checkin-list": "Check-in List", "appointment-dashboard": "Appointment Dashboard", "appointment-management": "Appointment Management", "quota-management": "Quota Management", "sales-order": "Sales Order", "sales-order-management": "Sales Order Management", "inventory-report": "Inventory Report", "inventory-report-hq": "Inventory Report HQ", "part-management": "Part Management", "parts-management-hq": "Parts Management HQ", "parts-receipt": "Parts Receipt", "parts-receipt-query": "Parts Receipt Query", "part-archives": "Part Archives", "part-configuration": "Part Configuration", "inspection-form": "Inspection Form Management", "work-order": "Work Order Management", "dispatch-management": "Dispatch Management", "work-assignment-dashboard": "Work Assignment Dashboard", "approval": "Order Approval", "vehicle-query": "Vehicle Query", "vehicle-allocation": "Vehicle Allocation Management", "order-management": "Order Management", "order-approval-management": "Order Approval Management", "delivery-management": "Delivery Management", "order-vehicle-assignment-management": "Order Vehicle Assignment Management", "vehicle-registration": "Vehicle Registration", "invoice-management": "Invoice Management", "lead-pool-management": "Lead Pool Management", "test-drive-report": "Test Drive Report", "order-statistics-management": "Order Statistics Management", "factory-order-management": "Factory Order Management", "potential-customer-management": "Potential Customer Management", "potential-customer-defeat-approval-management": "Potential Customer Defeat Approval Management", "test-drive-registration": "Test Drive Registration", "whole-vehicle-collection-management": "Whole Vehicle Collection Management"}, "inventoryReport": {"title": "Inventory Information (Store)", "description": "Report page displaying total inventory, available inventory, locked inventory, defective products, and pending receipts for the store.", "partName": "Part Name", "partNamePlaceholder": "Enter part name", "partNumber": "Part Number", "partNumberPlaceholder": "Enter part number", "supplierName": "Supplier Name", "supplierNamePlaceholder": "Enter supplier name", "inventoryStatus": "Inventory Status", "inventoryStatusPlaceholder": "Select inventory status", "statusNormal": "Normal", "statusBelowSafety": "Below Safety Stock", "serialNumber": "Serial Number", "totalInventory": "Total Inventory", "availableInventory": "Available Inventory", "lockedInventory": "Locked In<PERSON>ory", "defectiveProducts": "Defective Products", "pendingReceipt": "Pending Receipt", "safetyStock": "Safety Stock"}, "inventoryReportHQ": {"title": "Inventory Report HQ", "description": "Report page displaying total inventory, available inventory, locked inventory, defective products, and pending receipts for HQ.", "partName": "Part Name", "partNamePlaceholder": "Enter part name", "partNumber": "Part Number", "partNumberPlaceholder": "Enter part number", "supplierName": "Supplier Name", "supplierNamePlaceholder": "Enter supplier name", "storeName": "Store Name", "storeNamePlaceholder": "Enter store name", "inventoryStatus": "Inventory Status", "inventoryStatusPlaceholder": "Select inventory status", "statusNormal": "Normal", "statusBelowSafety": "Below Safety Stock", "serialNumber": "Serial Number", "totalInventory": "Total Inventory", "availableInventory": "Available Inventory", "lockedInventory": "Locked In<PERSON>ory", "defectiveProducts": "Defective Products", "pendingReceipt": "Pending Receipt", "safetyStock": "Safety Stock"}, "parts": {"partsManagementHQ": "Parts Management HQ", "partName": "Part Name", "partNamePlaceholder": "Please enter part name", "partNumber": "Part Number", "partNumberPlaceholder": "Please enter part number", "requisitionNumber": "Requisition No.", "requisitionNumberPlaceholder": "Please enter requisition number", "purchaseOrderNumber": "Purchase Order No.", "purchaseOrderNumberPlaceholder": "Please enter purchase order number", "supplierName": "Supplier Name", "supplierNamePlaceholder": "Please enter supplier name", "requisitionDateRange": "Requisition Date Range", "arrivalDateRange": "Arrival Date Range", "requisitionStatus": "Requisition Status", "selectStatus": "Please select status", "stockStatus": "Stock Status", "selectStockStatus": "Please select stock status", "serialNumber": "Serial Number", "purchaseOrderNum": "Purchase Order No.", "status": {"submitted": "Submitted", "approved": "Approved", "rejected": "Rejected", "shipped": "Shipped", "received": "Received", "voided": "Voided", "normal": "Normal", "below_safe": "Below Safety Stock"}, "approve": "Approve", "selectOneForApproval": "Please select only one item for approval", "requisitionDetail": "Requisition Detail", "approveRequisition": "Approve Requisition", "approvalResult": "A<PERSON><PERSON><PERSON> Result", "rejectionReason": "Rejection Reason", "rejectionReasonRequired": "Rejection reason is required", "quantity": "Quantity", "unit": "Unit", "expectedArrivalTime": "Expected Arrival Time", "approvalResultRequired": "Approval result is required"}, "receiptManagement": {"title": "Receipt Management", "receiptOrderNumber": "Receipt Order No.", "receiptOrderNumberPlaceholder": "Enter receipt order no.", "supplierName": "Supplier Name", "supplierNamePlaceholder": "Enter supplier name", "receiptStatus": "Receipt Status", "receiptStatusPlaceholder": "Select receipt status", "statusPending": "Pending Receipt", "statusReceived": "Received", "statusCancelled": "Cancelled", "receiptDateRange": "Receipt Date Range", "deliveryDateRange": "Delivery Date Range", "newReceiptOrder": "New Receipt Order", "exportReport": "Export Report", "newReceiptOrderForm": {"supplierName": "Supplier Name", "receiptDate": "Receipt Date", "deliveryDate": "Delivery Date", "partName": "Part Name", "partNumber": "Part Number", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Total Price"}, "receiptOrderDetailTitle": "Receipt Order Details", "invalidateReceiptOrderSuccess": "Receipt order invalidated successfully!"}, "financialReport": {"title": "Financial Report", "reportType": "Report Type", "reportDateRange": "Report Date Range", "totalRevenue": "Total Revenue", "totalExpense": "Total Expense", "netProfit": "Net Profit", "reportTypes": {"daily": "Daily", "monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly"}}, "customerManagement": {"title": "Customer Management", "customerList": "Customer List", "customerName": "Customer Name", "customerPhone": "Customer Phone", "customerAddress": "Customer Address", "addCustomer": "Add Customer", "editCustomer": "Edit Customer", "customerNamePlaceholder": "Enter customer name", "customerPhonePlaceholder": "Enter customer phone", "customerAddressPlaceholder": "Enter customer address", "customerMobilePlaceholder": "Enter phone number", "vin": "VIN", "vinPlaceholder": "Enter VIN", "orderStatus": "Order Status", "orderStatusPlaceholder": "Select order status", "orderStatusPendingAllocation": "Pending Allocation", "orderStatusAllocating": "Allocating", "orderStatusAllocated": "Allocated", "orderStatusPendingDelivery": "Pending Delivery", "orderStatusDelivered": "Delivered", "dealerStore": "Store", "dealerStorePlaceholder": "Select store", "salesConsultant": "Sales Consultant", "salesConsultantPlaceholder": "Enter sales consultant", "deliveryStatus": "Delivery Status", "deliveryStatusPlaceholder": "Select delivery status", "statusPending": "Pending Delivery", "statusConfirming": "Pending Confirmation", "statusCompleted": "Delivered", "customerConfirmed": "Customer Confirmed", "customerConfirmedPlaceholder": "Select customer confirmed status", "confirmationType": "Confirmation Type", "confirmationTypePlaceholder": "Select confirmation type", "confirmationTypeApp": "APP", "confirmationTypeOffline": "Offline", "deliveryTime": "Delivery Time", "deliveryTimePlaceholder": "Select delivery time", "customerConfirmTime": "Customer Confirmation Time", "customerConfirmTimePlaceholder": "Select customer confirmation time", "invoiceTime": "Invoice Time", "invoiceTimePlaceholder": "Select invoice time", "listTitle": "Delivery Order List", "totalCount": "Total {count} items", "submitConfirm": "Submit Confirmation", "deliveryConfirm": "Delivery Confirmation", "submitConfirmSuccess": "Submit confirmation successful!", "submitConfirmFailed": "Submit confirmation failed!", "deliveryConfirmSuccess": "Delivery confirmation successful!", "deliveryConfirmFailed": "Delivery confirmation failed!", "fetchDataFailed": "Failed to fetch delivery order data!", "printFeatureNotImplemented": "Print feature not implemented!", "exportSuccess": "Export successful!", "exportFailed": "Export failed!", "orderStatusNormal": "Normal", "orderStatusCancelled": "Cancelled", "signaturePhoto": "Signature Photo", "deliveryNotes": "Delivery Notes"}, "payment": {"title": "Vehicle Payment Management", "orderNumber": "Order Number", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "dealerStore": "Dealer Store", "salesConsultant": "Sales Consultant", "vin": "VIN", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "orderCreateTime": "Order Create Time", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "vehicleSalesPrice": "Vehicle Sales Price", "insuranceAmount": "Insurance Amount", "otrAmount": "OTR Amount", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "unpaidAmount": "Unpaid Amount", "loanAmount": "<PERSON><PERSON>", "canInvoice": "Can Invoice", "invoiceTime": "Invoice Time", "invoiceNumber": "Invoice Number", "createTime": "Create Time", "updateTime": "Update Time", "paymentOperation": "Payment Operation", "orderDetail": "Order Detail", "paymentRecords": "Payment History", "addPaymentRecord": "Add Payment Record", "enterOrderNumber": "Enter order number", "enterBuyerName": "Enter buyer name", "enterBuyerPhone": "Enter buyer phone", "selectOrderStatus": "Select order status", "selectPaymentStatus": "Select payment status", "selectCanInvoice": "Select can invoice", "selectDateRange": "Select date range", "orderStatusSubmitted": "Submitted", "orderStatusCancelPending": "Cancel Pending", "orderStatusCancelApproved": "Cancel Approved", "orderStatusCancelled": "Cancelled", "orderStatusConfirmed": "Confirmed", "orderStatusPendingReview": "Pending Review", "orderStatusReviewed": "Reviewed", "orderStatusPendingDelivery": "Pending Delivery", "orderStatusDelivered": "Delivered", "paymentStatusPendingDeposit": "Pending Deposit", "paymentStatusDepositPaid": "<PERSON><PERSON><PERSON><PERSON>", "paymentStatusRefunding": "Refunding", "paymentStatusRefunded": "Refunded", "paymentStatusPendingFinal": "Pending Final Payment", "paymentStatusFullyPaid": "<PERSON>y Paid", "businessType": "Business Type", "transactionNumber": "Transaction Number", "channel": "Channel", "amount": "Amount", "paymentType": "Payment Type", "arrivalTime": "Arrival Time", "remark": "Remark", "creator": "Creator", "dataSource": "Data Source", "payment": "Payment", "refund": "Refund", "channelAPP": "APP", "channelBankCard": "Bank Card", "channelTransfer": "Transfer", "paymentTypeBookFee": "Book Fee", "paymentTypeLoan": "Loan", "paymentTypeFinal": "Final Payment", "dataSourceManual": "Manual Entry", "dataSourceApp": "APP Push", "paymentMethodFull": "Full Payment", "paymentMethodLoan": "Loan", "transactionNumberRequired": "Transaction number is required", "transactionNumberExists": "Transaction number already exists", "amountRequired": "Amount is required", "amountInvalid": "Please enter valid amount", "arrivalTimeRequired": "Arrival time is required", "operationNotAllowed": "Operation not allowed in current status", "appDataCannotDelete": "APP data cannot be deleted", "confirmDelete": "Confirm to delete this record?", "deleteSuccess": "Delete successful", "addSuccess": "Add successful", "orderInfo": "Order Information", "customerInfo": "Customer Information", "personalDetails": "Personal Details", "ordererInfo": "Orderer Information", "buyerInfo": "Buyer Information", "ordererName": "Orderer Name", "ordererPhone": "Orderer Phone", "buyerIdType": "ID Type", "buyerIdNumber": "ID Number", "buyerEmail": "Buyer Email", "buyerAddress": "Buyer Address", "buyerState": "Buyer State", "buyerCity": "Buyer City", "buyerPostcode": "Buyer Postcode", "storeInfo": "Store Information", "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor", "dealerRegion": "Dealer Region", "dealerCity": "Dealer City", "salesConsultantPhone": "Phone", "salesConsultantEmail": "Email", "vehicleInfo": "Vehicle Information", "options": "Options", "warehouseName": "Warehouse", "productionDate": "Production Date", "priceInfo": "Price Information", "salesSubtotal": "Sales Subtotal (Including GASA)", "consumptionTax": "Consumption Tax", "salesTax": "Sales Tax", "numberPlatesFee": "Number Plates Fee", "optionsPrice": "Options Price", "vehicleSalesSubtotal": "Vehicle Sales Subtotal", "otrAmountDetail": "OTR Amount", "paymentRecordNumber": "Payment Record Number", "totalPayment": "Total Payment Amount", "totalRefund": "Total Refund Amount", "netPayment": "Net Payment Amount", "recordCount": "Record Count", "enterTransactionNumber": "Enter transaction number", "enterAmount": "Enter amount", "enterRemark": "Enter remark", "selectBusinessType": "Select business type", "selectChannel": "Select channel", "selectPaymentType": "Select payment type", "selectArrivalTime": "Select arrival time"}, "factoryOrder": {"title": "Factory Order Management", "statisticsTitle": "Order Overview Statistics", "searchTitle": "Filter Conditions", "actionTitle": "Action Area", "listTitle": "Order List", "monthlyOrderCount": "Monthly Order Count", "dailyOrderCount": "Daily Order Count", "monthlyGrowthRate": "Compared to last month", "dailyGrowthRate": "Compared to yesterday", "topDealer": "Top Dealer by Orders", "topVehicle": "Best Selling Vehicle", "pendingDelivery": "Pending Delivery Orders", "priorityProcessing": "Priority Processing Required", "refreshStatistics": "Refresh Data", "lastUpdate": "Last Update Time", "dealerName": "Dealer", "dealerNamePlaceholder": "Select dealer", "model": "Model", "modelPlaceholder": "Select model", "variant": "<PERSON><PERSON><PERSON>", "variantPlaceholder": "Select variant", "orderStatus": "Order Status", "orderStatusPlaceholder": "Select order status", "paymentStatus": "Payment Status", "paymentStatusPlaceholder": "Select payment status", "orderDate": "Order Date", "orderDateStart": "Start Date", "orderDateEnd": "End Date", "orderNumber": "Order Number", "orderNumberPlaceholder": "Enter order number", "serialNumber": "No.", "dealerNameColumn": "Dealer", "creationTime": "Order Creation Time", "ordererName": "Orderer Name", "ordererPhone": "Orderer Phone", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "buyerCategory": "Buyer Category", "vin": "VIN", "paymentMethod": "Payment Method", "loanApprovalStatus": "<PERSON>an <PERSON>al <PERSON>", "orderApprovalStatus": "Order Approval Status", "insuranceStatus": "Insurance Status", "jpjRegistrationStatus": "JPJ Registration Status", "operations": "Operations", "details": "Details", "exportExcel": "Export Excel", "exportSuccess": "Export successful", "exportFailed": "Export failed", "refreshSuccess": "Data refreshed", "refreshFailed": "Data refresh failed", "detailTitle": "Factory Order Details", "customerInfo": "Customer Information", "personalDetails": "Personal Details", "storeInfo": "Store Information", "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor", "purchaseDetails": "Purchase Information", "purchaseDetailsTitle": "Purchase Details", "vehicleInfoTab": "Vehicle Information", "invoiceInfoTab": "Invoice Information", "benefitsInfoTab": "Benefits Information", "paymentInfoTab": "Payment Information", "insuranceInfoTab": "Insurance Information", "otrFeesTab": "OTR Fees", "changeRecordsTab": "Change Records", "ordererNameField": "Orderer Name", "ordererPhoneField": "Orderer Phone", "buyerNameField": "Buyer Name", "buyerPhoneField": "Buyer Phone", "buyerIdType": "ID Type", "buyerIdNumber": "ID Number", "buyerEmail": "Buyer Email", "buyerAddress": "Buyer Address", "buyerState": "Buyer State", "buyerCity": "Buyer City", "buyerPostcode": "Buyer Postcode", "dealerRegion": "Dealer Region", "dealerCity": "Dealer City", "salesConsultant": "Sales Consultant", "salesSubtotal": "Sales Subtotal (Including GASA, Consumption Tax, Sales Tax)", "numberPlatesFee": "Number Plates Fee", "accessoryInfo": "Accessory Information", "accessoryCategory": "Category", "accessoryName": "Accessory Name", "accessoryUnitPrice": "Unit Price", "accessoryQuantity": "Quantity", "accessoryTotalPrice": "Total Price", "accessoriesTotalAmount": "Accessories Total Amount", "invoiceType": "Invoice Type", "invoiceName": "Invoice Name", "invoicePhone": "Invoice Phone", "invoiceAddress": "Invoice Address", "benefitsInfo": "Benefits Information", "benefitCode": "Benefit Code", "benefitName": "Benefit Name", "benefitMode": "Benefit Mode", "discountPrice": "Discount Price", "effectiveDate": "Effective Date", "expirationDate": "Expiration Date", "benefitsTotalAmount": "Benefits Total Amount", "loanApprovalStatusField": "<PERSON>an <PERSON>al <PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "loanAmount": "<PERSON><PERSON>", "balanceAmount": "Balance Amount", "insuranceInfo": "Insurance Information", "policyNumber": "Policy Number", "insuranceType": "Insurance Type", "insuranceCompany": "Insurance Company", "effectiveDateField": "Effective Date", "expirationDateField": "Expiration Date", "insurancePrice": "Insurance Price", "insurancesTotalAmount": "Insurance Total Amount", "insuranceNotes": "Notes", "otrFeesInfo": "On The Road Registration Fees", "ticketNumber": "Ticket Number", "feeItem": "<PERSON><PERSON>em", "feePrice": "<PERSON><PERSON>", "otrFeesTotalAmount": "OTR Fees Total Amount", "changeRecordsInfo": "Order Change Records", "originalContent": "Original Content", "changedContent": "Changed Content", "operator": "Operator", "operationTime": "Operation Time", "vehicleInvoicePrice": "Vehicle Invoice Price", "remainingReceivable": "Remaining Receivable", "backToList": "Back", "statusPending": "Pending", "statusConfirmed": "Confirmed", "statusInProduction": "In Production", "statusCompleted": "Completed", "statusCancelled": "Cancelled", "statusPendingDelivery": "Pending Delivery", "paymentStatusPendingDeposit": "Pending Deposit", "paymentStatusDepositPaid": "<PERSON><PERSON><PERSON><PERSON>", "paymentStatusPendingBalance": "Pending Balance", "paymentStatusFullyPaid": "<PERSON>y Paid", "loanStatusNoNeed": "No Need", "loanStatusPending": "Pending", "loanStatusApproved": "Approved", "loanStatusRejected": "Rejected", "approvalStatusPending": "Pending Approval", "approvalStatusApproved": "Approved", "approvalStatusRejected": "Rejected", "insuranceStatusPending": "Pending Insurance", "insuranceStatusCompleted": "Insured", "jpjStatusPending": "Pending Registration", "jpjStatusCompleted": "Registered", "categoryPersonal": "Personal", "categoryBusiness": "Business", "invoiceTypePersonal": "Personal", "invoiceTypeBusiness": "Company"}, "workAssignment": {"title": "Work Assignment", "breadcrumb": "After Sales / Work Assignment", "searchForm": "Filter Conditions", "columnConfig": "<PERSON>umn <PERSON>", "exportData": "Export Data", "workOrderId": "Work Order ID", "priority": "Priority", "workOrderType": "Work Order Type", "customerName": "Customer Name", "licensePlate": "License Plate", "serviceAdvisor": "Service Advisor", "assignmentStatus": "Assignment Status", "assignedTechnician": "Assigned Technician", "creationTime": "Creation Time", "estimatedWorkHours": "Estimated Work Hours", "waitingDuration": "Waiting Duration", "totalAmount": "Work Order Amount", "priorities": {"urgent": "<PERSON><PERSON>", "normal": "Normal"}, "workOrderTypes": {"repair": "Repair", "maintenance": "Maintenance", "insurance": "Insurance"}, "assignmentStatuses": {"pending_assign": "Pending Assign", "assigned": "Assigned"}}, "quota": {"pageTitle": "Appointment Quota Management", "storeName": "Store Name", "storeCode": "Store Code", "permissionTip": "You are currently viewing the appointment quota configuration for the default store. Please contact the administrator to configure other stores.", "configuredListTitle": "Configured Quota List", "addNewQuota": "Add New Appointment Quota", "table": {"configDate": "Config Date", "timeSlotCount": "Time Slot Count", "totalQuota": "Total Quota", "bookedQuantity": "Booked Quantity", "lastUpdateTime": "Last Update Time", "expired": "Expired"}, "emptyState": "No appointment quota configured yet. <PERSON><PERSON> 'Add New Appointment Quota' to configure.", "unsavedChangesWarning": "You have unsaved changes, are you sure you want to close?", "dateMustBeFutureOrToday": "Date must be today or in the future", "selectDateFirst": "Please select a date first", "validation": {"dateRequired": "Date cannot be empty", "atLeastOneTimeSlot": "Please add at least one time slot", "timeRequired": "Time slot start/end time cannot be empty", "startBeforeEnd": "Time slot start time must be before end time", "quotaPositive": "Quota must be a positive integer", "timeSlotOverlap": "Time slot {slot1} overlaps with {slot2}, please adjust"}, "messages": {"savingConfig": "Saving configuration..."}, "summary": {"selectDate": "Please select date", "addTimeSlot": "Please add time slot", "timeSlotsUnit": " time slots", "totalQuota": "Total Quota: "}, "editQuotaTitle": "Edit Appointment Quota", "addNewQuotaTitle": "Add New Appointment Quota", "modal": {"selectDateTitle": "Select Date", "dateLabel": "Date", "datePlaceholder": "Select Date", "existingConfig": "Existing configuration for this date", "dateTip": "Date must be today or in the future", "timeSlotConfigTitle": "Time Slot Configuration", "addTimeSlot": "Add Time Slot", "noTimeSlots": "No time slots currently, please click the button above to add.", "clickAddPrompt": "Click the 'Add Time Slot' button to start configuring", "timeSlot": "Time Slot", "startTime": "Start Time", "startTimePlaceholder": "Select start time", "endTime": "End Time", "endTimePlaceholder": "Select end time", "quota": "<PERSON><PERSON><PERSON>", "quotaPlaceholder": "Enter quota", "configDescriptionTitle": "Configuration Description", "configDescription": {"item1": "The quota for each time slot represents the maximum number of vehicles that can be booked for that slot.", "item2": "Time slots must be continuous, with no gaps.", "item3": "Time slots cannot overlap.", "item4": "The total quota of all time slots will be the total quota for that day."}}, "timeSlotExceedsOperatingHours": "Time slot end time cannot exceed 18:00"}, "salesOrder": {"title": "Sales Order List", "breadcrumb": "Customer Order Management / Sales Order List", "searchForm": "Filter Conditions", "operationButtons": "Operations", "orderList": "Order List", "buyerName": "Buyer Name", "buyerNamePlaceholder": "Enter buyer name", "buyerPhone": "Buyer Phone", "buyerPhonePlaceholder": "Enter buyer phone", "buyerType": "Buyer Type", "buyerTypePlaceholder": "Select buyer type", "model": "Model", "modelPlaceholder": "Select model", "orderNumber": "Order Number", "orderNumberPlaceholder": "Enter order number", "orderStatus": "Order Status", "orderStatusPlaceholder": "Select order status", "approvalStatus": "Order Approval Status", "approvalStatusPlaceholder": "Select approval status", "paymentStatus": "Payment Status", "paymentStatusPlaceholder": "Select payment status", "createTime": "Order Creation Time", "createTimeRange": "Order Creation Time Range", "insuranceStatus": "Insurance Status", "insuranceStatusPlaceholder": "Select insurance status", "loanApprovalStatus": "<PERSON>an <PERSON>al <PERSON>", "loanApprovalStatusPlaceholder": "Select loan approval status", "jpjRegistrationStatus": "JPJ Registration Status", "jpjRegistrationStatusPlaceholder": "Select JPJ registration status", "index": "No.", "ordererName": "Orderer", "ordererPhone": "Orderer Phone", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "vin": "VIN", "paymentMethod": "Payment Method", "totalAmount": "Total Amount", "buyerTypes": {"individual": "Individual", "company": "Company"}, "models": {"AXIA": "AXIA", "BEZZA": "BEZZA", "MYVI": "MYVI"}, "orderStatuses": {"submitted": "Submitted", "confirmed": "Confirmed", "cancel_pending": "Cancel Pending", "cancel_approved": "Cancel Approved", "cancelled": "Cancelled", "pending_delivery": "Pending Delivery", "delivered": "Delivered"}, "approvalStatuses": {"pending_approval": "Pending Approval", "approved": "Approved"}, "paymentStatuses": {"pending_deposit": "Pending Deposit", "deposit_paid": "<PERSON><PERSON><PERSON><PERSON>", "pending_balance": "Pending Balance", "balance_paid": "Balance Paid", "refunding": "Refunding", "refunded": "Refunded"}, "insuranceStatuses": {"not_insured": "Not Insured", "insuring": "Insuring", "insured": "Insured"}, "loanApprovalStatuses": {"pending_review": "Pending Review", "approved": "Approved", "rejected": "Rejected"}, "jpjRegistrationStatuses": {"pending_registration": "Pending Registration", "registering": "Registering", "registered": "Registered", "registration_failed": "Registration Failed"}, "paymentMethods": {"full_payment": "Full Payment", "installment": "Installment"}, "viewDetail": "Detail", "editOrder": "Edit", "returnToList": "Return to List", "exportSuccess": "Export successful", "exportFailed": "Export failed", "noDataToExport": "No data to export", "confirmExportTitle": "Confirm Export", "confirmExportMessage": "Are you sure to export all order data under current filter conditions?", "orderDetail": "Sales Order Detail", "customerInfo": "Customer Information", "storeInfo": "Store Information", "purchaseInfo": "Purchase Information", "vehicleInfoTab": "Vehicle Information", "invoicingInfoTab": "Invoicing Information", "rightsInfoTab": "Service & Benefits Information", "paymentInfoTab": "Payment Information", "insuranceInfoTab": "Insurance Information", "otrFeesTab": "OTR Fees Information", "changeRecords": "Order Change Records", "buyerIdType": "Buyer ID Type", "buyerIdNumber": "Buyer ID Number", "buyerEmail": "Buyer Email", "buyerAddress": "Buyer Address", "buyerState": "Buyer State", "buyerCity": "Buyer City", "buyerPostcode": "Buyer Postcode", "storeRegion": "Store Region", "storeCity": "Store City", "storeName": "Store Name", "salesConsultantName": "Sales Consultant", "salesSubtotal": "Sales Subtotal (incl. GASA, GST, Sales Tax)", "numberPlatesFee": "Number Plates Fee", "vinCode": "VIN Code", "accessoryInfo": "Accessories Information", "accessoryCategory": "Category", "accessoryName": "Accessory Name", "unitPrice": "Unit Price", "quantity": "Quantity", "totalPrice": "Total Price", "accessoriesTotalAmount": "Accessories Total Amount", "invoicingType": "Invoicing Type", "invoicingName": "Invoicing Name", "invoicingPhone": "Invoicing Phone", "invoicingAddress": "Invoicing Address", "rightsInfo": "Service & Benefits Information", "rightCode": "Benefit Code", "rightName": "Benefit Name", "rightMode": "Benefit Mode", "discountAmount": "Discount Amount", "effectiveDate": "Effective Date", "expiryDate": "Expiry Date", "rightsDiscountAmount": "Benefits Total Discount", "loanApprovalStatusField": "<PERSON>an <PERSON>al <PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "loanAmount": "<PERSON><PERSON>", "balanceAmount": "Balance Amount", "insuranceInfo": "Insurance Information", "policyNumber": "Policy Number", "insuranceType": "Insurance Type", "insuranceCompany": "Insurance Company", "insurancePrice": "Insurance Price", "insuranceTotalAmount": "Insurance Total Amount", "insuranceNotes": "Notes", "otrFeesInfo": "On The Road Registration Fees", "ticketNumber": "Ticket Number", "feeItem": "<PERSON><PERSON>em", "feePrice": "<PERSON><PERSON>", "otrFeesTotalAmount": "OTR Fees Total Amount", "changeRecordIndex": "No.", "originalContent": "Original Content", "changedContent": "Changed Content", "operator": "Operator", "operationTime": "Operation Time", "totalInvoiceAmount": "Total Invoice Amount", "remainingAmount": "Remaining Amount", "editOrderTitle": "Sales Order Edit", "personalDetails": "Customer Information - Personal Details", "preferredOutlet": "Store Information - Preferred Outlet & Sales Advisor", "purchaseDetails": "Purchase Information - Purchase Details", "addRights": "Add Benefits", "selectRights": "Select Benefits", "rightCodeSearch": "Benefit Code Search", "rightNameSearch": "Benefit Name Search", "selectAll": "Select All", "addSelected": "Add Selected", "deleteRight": "Delete Benefit", "confirmDeleteRight": "Are you sure to delete this benefit?", "pushToInsurance": "Push to Insurance System", "insurancePushed": "Pushed", "confirmPushInsurance": "Are you sure to push to insurance system?", "pushInsuranceSuccess": "Push to insurance system successful", "pushInsuranceFailed": "Push to insurance system failed", "submitDelivery": "Submit Delivery", "confirmSubmitDelivery": "Are you sure to submit delivery application?", "submitDeliverySuccess": "Submit delivery successful", "submitDeliveryFailed": "Submit delivery failed", "deliveryConditionsNotMet": "Delivery conditions not met", "colorChangeNotice": "You have changed the vehicle color, do you want to submit for approval?", "colorChangeSubmitted": "Color change submitted for approval", "roadTax": "Road Tax", "registrationFee": "Registration/Transfer Fee", "ownershipClaimFee": "Ownership C<PERSON><PERSON>", "interchangeFee": "Interchange Fee", "otrFeeTotal": "Registration Fees Total", "loanTerm": "<PERSON><PERSON> (Months)", "loanTermPlaceholder": "Enter loan term", "loanAmountPlaceholder": "Enter loan amount"}, "technicians": {"technicianA": "Technician A", "technicianB": "Technician B", "technicianC": "Technician C"}, "mockData": {"registerTypes": {"appointment": "Appointment", "walkIn": "Walk-in"}, "serviceTypes": {"maintenance": "Maintenance", "repair": "Repair"}, "colors": {"white": "White", "black": "Black", "blue": "Blue", "gray": "<PERSON>"}, "names": {"zhangSan": "<PERSON>", "liHua": "<PERSON>", "wangDaChui": "<PERSON>", "linYiYi": "<PERSON>", "liSi": "<PERSON>", "wangWu": "<PERSON>", "zhaoLiu": "<PERSON>", "qianQi": "<PERSON><PERSON>", "sunBa": "Sun Ba", "zhouJiu": "<PERSON>", "wuShi": "<PERSON>"}, "carModels": {"modelY2023Long": "Model Y 2023 Long Range", "model32022Standard": "Model 3 2022 Standard Range", "modelX2024Performance": "Model X 2024 Performance", "modelY2023Performance": "Model Y 2023 Performance"}}}