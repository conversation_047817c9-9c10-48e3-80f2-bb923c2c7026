
## DMS 前端项目环境搭建：第七步 - 检查并修正路由配置



### 1. 检查并修改 `dms-frontend/src/router/index.ts` (路由配置文件)

这个文件定义了你的应用有哪些页面，以及它们对应的 URL 路径。我们需要确保 `/` 路径指向的是我们新的 `HomeView.vue`。

* **操作步骤：**
    1.  在 VS Code 中，打开文件：`dms-frontend/src/router/index.ts`。
    2.  找到该文件中定义路由数组 `routes` 的部分。
    3.  将该文件中的所有内容替换为以下代码。我已确保 `HomeView` 是根路径 `/` 对应的组件。

    ```typescript
    // dms-frontend/src/router/index.ts
    import { createRouter, createWebHistory } from 'vue-router';
    // 导入我们创建的 HomeView 组件
    import HomeView from '../views/HomeView.vue';

    const router = createRouter({
      history: createWebHistory(import.meta.env.BASE_URL),
      routes: [
        {
          path: '/',
          name: 'home',
          component: HomeView, // 确保这里指向的是你的 HomeView.vue
          meta: {
            title: 'DMS Dashboard' // 页面标题，可以用于设置浏览器标签页标题
          }
        },
        {
          path: '/about', // 默认生成的 About 页面，你可以选择保留或删除
          name: 'about',
          // route level code-splitting
          // this generates a separate chunk (About.[hash].js) for this route
          // which is lazy-loaded when the route is visited.
          component: () => import('../views/AboutView.vue')
        }
        // 你可以在这里添加更多的路由
        // {
        //   path: '/login',
        //   name: 'login',
        //   component: () => import('../views/auth/LoginPage.vue'), // 示例：登录页
        //   meta: {
        //     title: 'Login'
        //   }
        // }
      ]
    });

    // 路由守卫示例：每次路由切换时更新页面标题 (可选)
    router.beforeEach((to, from, next) => {
      if (to.meta.title) {
        document.title = to.meta.title as string;
      }
      next();
    });

    export default router;
    ```

* **验证方法：**
    1.  保存 `dms-frontend/src/router/index.ts` 文件。
    2.  确保你的 `pnpm dev` 服务器正在运行。如果终端有任何编译错误，请解决它们。
    3.  **强制刷新浏览器页面 (Ctrl + F5 或 Cmd + Shift + R)**，以确保浏览器加载的是最新的 JS 文件。
    4.  **预期结果：** 你应该能看到我们刚才编写的 DEMO 页面，而不是 Vue 的默认欢迎页面。

---

**可能还需要检查的地方 (如果修改路由后仍然不行)：**

* **`dms-frontend/src/App.vue`：** 确保 `App.vue` 中有 `<RouterView />` 组件，它是 Vue Router 渲染页面组件的占位符。Vue CLI/Vite 默认生成的项目通常会包含它。
    * 打开 `dms-frontend/src/App.vue`
    * 确认其 `<template>` 中包含类似 `<RouterView />` 的内容。
    * 例如，它可能看起来像这样（这是 Vite 默认生成的 `App.vue` 简化版）：
        ```vue
        <script setup lang="ts">
        import { RouterView } from 'vue-router';
        // import HelloWorld from './components/HelloWorld.vue'; // 默认组件，可以移除
        </script>

        <template>
          <RouterView /> </template>

        <style scoped>
        /* 默认的 App.vue 样式 */
        </style>
        ```
        为了干净地显示 DEMO 页面，你可以暂时注释掉 `<header>` 部分，只保留 `<RouterView />`。

