# AIDAD 操作手册 (Operational Playbook)

**版本: 1.0**
**创建日期: 2025-06-14**
**贡献者: <PERSON><PERSON>Wu & Gemini**

## 1. 摘要

本手册是《AIDAD方法论与核心原则》的配套战术执行指南。它详细定义了在AIDAD框架下，人类与AI进行高效、可控协作的标准作业流程 (Standard Operating Procedure, SOP)。

当"原则"文档回答了"我们为什么这么做"(Why)以及"我们要做什么"(What)之后，本手册将精确回答"我们具体如何做"(How)。

---

## 2. 核心交互循环 (The Core Interaction Cycle)

AIDAD的核心思想通过一个结构化的交互循环来落地。在接收到人类专家的 `go next` 指令后，AI将严格遵循以下步骤启动并完成下一个任务单元。

### **步骤 1/6: 任务定义与模式选择 (Task Definition & Mode Selection)**

*   **执行者**: 人类专家
*   **动作**: 下达一个清晰、聚焦的指令，并为该任务显式指定"思维模式"。
*   **指令模板**:
    ```
    下一步：[任务名称]。模式：[精确/创意]
    ```
*   **示例**:
    > 下一步：根据页面原型和功能点，设计Controller层接口。模式：精确

---

### **步骤 2/6: 动态上下文作用域构建 (Dynamic Context Scoping)**

*   **执行者**: AI
*   **动作**: 基于任务名称和模式，AI的第一步是提议构建一个完成该任务所必需的、临时的、纯净的"上下文工作区"。
*   **回应模板**:
    > "收到。为了以 **[精确/创意]** 模式完成 **[任务名称]**，我建议从我们的知识库中提取以下关键信息作为本次任务的上下文：
    > *   [相关文档A，如：页面原型.md]
    > *   [相关文档B，如：API设计规范.md]
    > *   [相关对话记录C，如：关于订单状态机的讨论]
    >
    > 请问是否需要补充或调整？"

*   **动作**: 人类专家对AI提议的上下文进行确认、补充或删减。
*   **确认指令**: `确认` 或 `补充：还需要XXX`。
*   **AI后续操作**:  AI在一个我们指定的临时文件中 比如：memory-bank/temp_workspace.md构建一个只包含这些确认后信息的、纯净的上下文。(在隔离环境中执行)：AI基于这个"纯净上下文"和任务指令，开始执行。

---

### **步骤 2.5/7 (新增): 核心资产与执行策略预对齐 (Asset & Strategy Pre-alignment)**

*   **触发条件**: 当任务属于"融合"、"重构"、"迁移"或"增强"现有文档或代码时，必须执行此额外步骤。
*   **目的**: 在AI投入实质性工作之前，精准对齐双方关于"什么不能动"和"打算怎么做"的核心共识，从根源上避免大规模的无效返工。

#### **子步骤 A: 核心资产声明 (Core Asset Declaration)**
*   **执行者**: AI主动提问，人类专家回答。
*   **AI提问模板**:
    > "在正式开始前，请进行**核心资产声明**：本次任务中，是否存在必须被100%原样保留、不可修改、不可抽象的'核心资产'（如特定的代码片段、配置文件、或需求描述）？如果存在，请明确指出。"
*   **人类专家回答模板**:
    > "核心资产是[某某文件]中的[某某部分]。" 或 "无核心资产，你可以进行归纳和重构。"

#### **子步骤 B: 执行策略对齐 (Execution Strategy Alignment)**
*   **执行者**: AI提议，人类专家确认。
*   **动作**: 在明确了核心资产后，AI必须提交一份清晰的、分步骤的执行计划（施工方案），并等待人类批准。
*   **AI提议模板**:
    > "收到。核心资产已锁定。我的执行策略是：
    > 1.  首先，[描述第一步做什么，例如：创建新的AIDAD框架结构]。
    > 2.  然后，[描述第二步做什么，例如：将声明的核心资产完整地'包裹'到新结构的指定位置]。
    > 3.  最后，[描述第三步做什么，例如：基于核心资产，完成其余部分的逻辑填充]。
    >
    > 请问此执行策略是否正确？"
*   **人类专家确认指令**: `策略正确，开始执行。`

---

### **步骤 3/7: 在隔离环境中执行 (Execution in Isolated Environment)**

*   **执行者**: AI
*   **动作**: AI严格基于"动态上下文作用域"中的信息、已对齐的"执行策略"和已设定的"任务模式"来执行任务，产出"草案"。
*   **核心优势**:
    *   **行为可预测**: 任务模式和执行策略设定了明确的行为边界。
    *   **抗干扰**: 隔离的上下文杜绝了被无关信息干扰而导致的"约束漂移"。

---

### **步骤 4/7: 产出草案与迭代精炼 (Draft Output & Iterative Refinement)**

*   **执行者**: AI & 人类专家
*   **动作**: AI提交结构化的"草案"供人类审查。双方进入"草案 → 讨论 → 定稿"的快速迭代循环。
*   **关键**: 由于前序步骤的铺垫，此处的"草案"质量会显著高于传统方式，使迭代过程更高效。

---

### **步骤 5/7: 闭环自检 (Closed-Loop Self-Correction)**

*   **执行者**: AI
*   **动作**: 在提交定稿产出物之前，人类专家可以要求AI执行一次标准化的自我检查。
*   **指令**: `请自检`
*   **回应模板**:
    > "自检开始...
    > 1.  **目标符合性检查**: 我的任务是'[任务名称]'。产出物是[产出物描述]，**符合**。
    > 2.  **模式符合性检查**: 模式为'[精确/创意]'。我检查了所有产出细节，[具体检查过程描述]，**符合**。
    > 3.  **信息源符合性检查**: 我对照了本次任务的上下文，所有信息来源均在作用域内，无任何外部信息泄漏，**符合**。
    >
    > 自检完毕，未发现偏差。"

---

### **步骤 6/7: 定稿与清理 (Finalization & Cleanup)**

*   **执行者**: 人类专家 & AI
*   **动作**:
    1.  人类专家对经过自检的产出物进行最终确认，形成"定稿"。
    2.  AI清理本次任务的临时上下文工作区。
    3.  AI输出 `[-- DONE --]` 标识，等待下一个 `go next` 指令。

---

## 附录A: 人机协作指令意图词典 (Lexicon of Intent)

这个词典旨在成为我们之间沟通的"协议层"，确保每个关键指令都有一个双方都共同遵守的、明确的定义。

| 指令 (Command) | 核心意图 (Core Intent) | 应用场景 (Use Case) | 使用范例 (Example) |
| :--- | :--- | :--- | :--- |
| **`包裹` (Wrap)** | **外部封装，内部保真**：将核心资产视为一个**不可变**的黑盒，用新的结构或逻辑在外部对其进行封装。**不改变**核心资产自身。 | 将旧的业务流程适配到新的方法论框架中；为一个老函数增加新的日志或事务能力，而不修改其内部代码。 | `下一步：请使用AIDAD框架**包裹**v01工作流文档。` |
| **`替换` (Replace)** | **完全取代，丢弃旧的**：将一个组件或模块完全移除，并用一个新的、功能不同的组件来替代它的位置。这是**破坏性**的。 | 用新的认证服务`NewAuth`**替换**掉已废弃的`OldAuth`模块；将占位的模拟数据**替换**为真实的API调用。 | `下一步：请用新的NewAuthService**替换**掉旧的LegacyAuthModule。` |
| **`填充` (Fill)** | **完善模板，补充信息**：在一个已有的、结构固定的模板中，填入缺失的具体信息。模板的**结构不变**。 | 将数据库连接信息**填充**到`config.yml.template`文件中；为一个类的构造函数**填充**必要的参数。 | `下一步：请用生产环境的配置**填充**这份部署脚本模板。` |
| **`重构` (Refactor)** | **外部不变，内部优化**：在**不改变**对外接口和行为的前提下，改善内部代码的结构、可读性或性能。 | 将一个长函数中的重复逻辑抽取成一个私有辅助方法；优化算法以减少时间复杂度，但函数签名和返回值不变。 | `下一步：请**重构**calculate_price函数，将优惠券逻辑抽取到独立的Util类中，但对外接口不变。` |
| **`增强` (Enhance)**| **增加功能，保留原有**：在现有组件的基础上，**添加**新的功能或数据，同时保留其全部原有功能。这是**非破坏性**的。 | 为`UserDTO`**增强**一个`lastLoginTime`字段；为一个Service方法**增强**缓存逻辑。 | `下一步：请**增强**UserAPI，增加一个/users/profile的新接口。` |
| **`验证` (Verify)** | **对比标准，报告差异**：将一个产出物与一个明确的"事实标准"（如规范文档、另一份代码）进行对比，并报告其是否合规以及所有**不一致**之处。 | **验证**生成的代码是否遵循了`团队编码规范.md`；**验证**API的返回结果是否与`接口契约.json`中定义的结构完全一致。 | `下一步：请**验证**此代码是否符合我们预定义的RESTful API设计规范。` | 