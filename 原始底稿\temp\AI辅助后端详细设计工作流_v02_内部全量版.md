# AI辅助后端详细设计工作流 (v02 - AIDAD全量内部专用版)

**版本：v02-Internal-Full-AIDAD**
**用途：仅限团队内部验证AIDAD方法论与操作手册的完整性与可控性，严禁对外泄露。本文档保留了v01版本中的所有业务逻辑细节，并使用AIDAD框架对其进行增强。**

---
## **核心输入**
*   **业务需求文档**：包含术语、实体、业务流程、用户旅程、功能清单、页面原型及描述。
*   **设计规则**：由人类专家预先定义的一系列规则文档，例如：
    *   `数据库设计规范`
    *   `API 设计规范 (RESTful)`
    *   `Service 设计规范`
    *   `Controller 设计规范`
    *   `设计模式应用指南`

## **核心输出**
*   **P01**: 领域模型与E-R图
*   **P02**: 数据库表结构设计 (含DDL语句)
*   **P03**: API接口契约 (含DTO定义和JSON样例)
*   **P04**: Service层设计 (含业务逻辑、事务、设计模式分析)
*   **P05**: Controller层设计 (含校验规则和服务调用关系)

---

### **执行步骤详解**
* **前置信息**
1.`temp_workspace.md` 位置在@memory-bank下。

---

### **步骤 1/7: 领域理解与差距分析 (Domain Understanding & Gap Analysis)**

*   **目标**：(v01精华) 确保AI对业务领域的理解与人类专家一致，并主动暴露需求中的模糊地带。
*   **优化点**：(v01精华) 从被动提问升级为主动总结和分析。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：领域理解与差距分析。模式：创意`。
    2.  **动态上下文作用域构建**: AI提议将 `全部需求输入` (业务需求文档、术语、实体、流程、功能清单、原型等) 和 `所有设计规则` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容执行以下业务动作：
        *   A. **AI分析与总结**: AI阅读全部需求输入，然后输出一份**「领域模型摘要」**，包含：
            *   **核心实体识别**：识别出的主要业务实体 (如：用户、商品、订单)。
            *   **关键参与者**：识别出的系统角色 (如：买家、卖家、平台管理员)。
            *   **核心业务流程**：用列表形式总结出1-3个最核心的业务流程 (如：用户注册流程、商品购买流程)。
        *   B. **AI提问与边界探索**: 基于上述分析，AI生成一份**「问题与边界清单」**，从以下角度提问：
            *   **模糊点**："当用户账户余额不足时，订单应该进入'待支付'状态还是直接创建失败？"
            *   **缺失的场景**："文档描述了成功支付的流程，但没有描述用户取消支付或支付超时的处理逻辑，是否需要补充？"
            *   **数据边界**："一个用户最多可以有多少个收货地址？商品名称的最大长度是多少？"
            *   **权限疑问**："普通用户是否可以查看其他用户的订单列表？"
    4.  **产出草案与迭代精炼**: 人类专家回答AI的问题清单。AI吸收答案后，更新其**「领域模型摘要」**。此过程可多次迭代，直到人类专家确认AI的理解准确无误。
    5.  **闭环自检**: AI自检其最终的摘要和问题清单是否全面覆盖了上下文中的所有关键信息，是否存在遗漏。
    6.  **定稿与交付**: `领域模型摘要` 与 `问题与边界清单` 定稿为 **P01** 的一部分。AI清理`temp_workspace.md`，并输出 `[-- DONE --]`。

---

### **步骤 2/7: 实体关系建模与数据库设计 (E-R Modeling & Database Design)**

*   **目标**: (v01精华) 基于已对齐的领域理解，创建可视化的实体关系模型，并产出详细的数据库表结构。
*   **优化点**: (v01精华) 增加可视化E-R图，使关系更直观；结构化输出，便于后续处理。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：实体关系建模与数据库设计。模式：精密`。
    2.  **动态上下文作用域构建**: AI提议将 `P01-领域模型摘要(定稿)` 和 `数据库设计规范` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容执行以下业务动作：
        *   A. **AI生成E-R图**：AI基于**「领域模型摘要」**和`数据库设计规范`，首先生成一份使用Mermaid.js语法的**「实体关系图 (E-R Diagram)」**。
        *   B. **AI设计表结构**：随后，为E-R图中的每一个实体，生成详细的**「数据库表结构」**，以Markdown表格形式呈现，并包含：
            | 字段名 (Field) | 数据类型 (Type) | 长度 (Length) | 主键 (PK) | 外键 (FK) | 非空 (Not Null) | 默认值 (Default) | 注释 (Comment) |
            | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
            | `id` | `bigint` | 20 | Y | | Y | | 主键ID |
            | `user_name` | `varchar`| 50 | | | Y | | 用户名 |
            | `create_time` | `datetime`| | | | Y | `CURRENT_TIMESTAMP` | 创建时间 |
        *   C. **AI生成DDL**：为每张表生成对应的SQL DDL（数据定义语言）语句。
    4.  **产出草案与迭代精炼**: 人类专家审查E-R图、表结构、DDL，进行微调直至定稿。
    5.  **闭环自检**: AI自检所有表和字段是否都能在领域模型中找到对应，且完全符合数据库设计规范。
    6.  **定稿与交付**: `E-R图`、`数据库表结构`和`DDL`定稿为 **P02**。AI清理`temp_workspace.md`，并输出 `[-- DONE --]`。

---

### **步骤 3/7: API 接口契约设计 (API Contract Design)**

*   **目标**: (v01精华) 定义清晰、规范、对前后端都友好的API接口，作为服务开发的契约。
*   **优化点**: (v01精华) 强调从用户旅程和业务动作出发，并强制定义DTO（数据传输对象）结构。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：API接口契约设计。模式：精密`。
    2.  **动态上下文作用域构建**: AI提议将 `用户旅程和功能清单`、`P02-数据库表结构(定稿)` 和 `API设计规范` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容执行以下业务动作：
        *   A. **AI分析与设计**：AI基于`用户旅程和功能清单`以及`P02`表结构，并严格遵循`API设计规范 (RESTful)`，开始设计API。分析维度从"页面功能"升级为**"用户旅程中的业务动作"**。
        *   B. **AI生成API清单**：为每一个业务动作生成详细的API描述，格式如下：
            *   **业务动作**: 用户注册
            *   **接口描述**: 提供新用户注册功能。
            *   **请求方法**: `POST`
            *   **接口路径**: `/api/v1/users/register`
            *   **请求DTO (Request DTO)**: `UserRegisterRequest.java`
                ```json
                {
                  "username": "string (用户名)",
                  "password": "string (密码)",
                  "email": "string (邮箱)"
                }
                ```
            *   **响应DTO (Response DTO)**: `UserRegisterResponse.java`
                ```json
                {
                  "userId": "long (用户ID)",
                  "username": "string (用户名)",
                  "welcomeMessage": "string (欢迎信息)"
                }
                ```
            *   **参数映射**：详细说明每个DTO字段与数据库表字段的来源/去向关系。
        *   C. **AI更新数据库**：在设计API的过程中，如果AI发现当前表结构无法满足API的数据需求（例如，缺少某个逻辑判断字段），它会提出对`P02`的修改建议。
    4.  **产出草案与迭代精炼**: 人类专家审查API的RESTful风格、路径、DTO结构等，进行微调直至定稿。
    5.  **闭环自检**: AI自检所有API是否都与功能规格1:1对应，所有DTO字段是否都能在PO或需求中找到来源。
    6.  **定稿与交付**: `API接口契约`定稿为 **P03**。AI清理`temp_workspace.md`，并输出 `[-- DONE --]`。

---

### **步骤 4/7: 服务层初步设计 (Initial Service Layer Design)**

*   **目标**: (v01精华) 将API契约快速转化为业务逻辑层的骨架，追求**"覆盖度"**而非**"优雅度"**。
*   **优化点**: (v01精华) 强制面向接口编程，并产出初步的服务拓扑图。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：服务层初步设计。模式：精密`。
    2.  **动态上下文作用域构建**: AI提议将 `P03-API接口契约(定稿)` 和 `Service设计规范` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容执行以下业务动作：
        *   A. **AI分析与映射**：AI遍历`P03-API接口契约`中的每一个接口，并遵循`Service设计规范`。
        *   B. **AI生成Service接口与实现骨架**：
            *   **接口定义**：根据业务领域（如User, Order, Product）创建Service接口（如`UserService`, `OrderService`）。
            *   **方法签名**：在接口中为每个API动作定义一个方法。方法名应清晰反映业务意图（如`registerNewUser`, `placeOrder`），参数和返回值**必须**是之前定义的DTO对象。
            *   **初步实现类**：为每个接口创建一个`Impl`实现类，其中包含所有方法的空实现或抛出`UnsupportedOperationException`的存根。
        *   C. **AI生成初步服务拓扑图**：AI生成一份简单的服务调用关系图（可用Mermaid.js表示），展示哪些Controller预计会调用哪些Service。
    4.  **产出草案与迭代精炼**: 人类专家审查服务划分、方法签名等，确认后定稿。
    5.  **闭环自检**: AI自检每个Service方法是否都与一个API对应，方法签名中的DTO是否完全一致。
    6.  **定稿与交付**: `Service层设计（草稿）`定稿。AI清理`temp_workspace.md`，并输出 `[-- DONE --]`。

---

### **步骤 5/7: 服务层深度设计与重构 (In-depth Service Layer Design & Refactoring)**

*   **目标**: (v01精华) AI扮演资深架构师，对初步的服务层设计进行重构、优化和深化，注入设计的灵魂。
*   **优化点**: (v01精华) 将"思考"过程结构化、显式化，让AI不仅给出结果，更要解释"为什么"。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：服务层深度设计与重构。模式：创意`。
    2.  **动态上下文作用域构建**: AI提议将 `Service层设计（草稿）`、`Service设计规范` 和 `设计模式应用指南` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容，从以下维度进行分析，并以**方案权衡对比表**的形式提供优化建议：
        *   A. **抽象与复用分析**：
            *   **识别重复逻辑**：扫描所有方法实现思路，找出重复的代码块（如：检查用户权限、计算订单总价、校验库存）。
            *   **提出重构建议**：将重复逻辑抽取为`private`的辅助方法或独立的`Util`工具类。
        *   B. **设计模式应用分析**：
            *   **场景匹配**：分析是否存在适合应用设计模式的复杂场景。
                *   *状态流转* (如订单状态：待支付->已支付->已发货) -> 建议**状态模式**。
                *   *多类型条件分支* (如支付方式：支付宝、微信、银行卡) -> 建议**策略模式**。
                *   *事件通知* (如下单后需通知库存、物流、积分系统) -> 建议**观察者模式/Spring事件**。
            *   **提供方案**：如果建议使用，必须详细说明**为什么**以及**如何实现**的伪代码。
        *   C. **事务边界分析**：
            *   **标记事务**：为每个public方法标记所需的事务传播行为（如`@Transactional`）。
            *   **解释原因**：解释为什么某个方法需要事务（因为它涉及多个写操作），或者为什么需要新的事务（因为它是一个独立的、必须成功的子任务，如记录日志）。
        *   D. **健壮性设计**：
            *   **并发控制**：识别并发场景（如秒杀扣库存），提出锁方案（**乐观锁**+`version`字段，或**分布式锁**）并说明选型理由。
            *   **幂等性保障**：识别需要保证幂等性的接口（如支付回调），提出实现机制（如在Redis中检查请求ID）。
            *   **异常体系**：定义业务异常（如`InsufficientStockException`），并明确它们在何处被抛出。
        *   E. 如果设计引入新字段（如`version`），自动提出对`P02`数据库设计的更新请求。
    4.  **产出草案与迭代精炼**: 这是最关键的人机协作环节，人类专家基于经验对AI的架构建议进行决策和优化。
    5.  **闭环自检**: AI自检其建议是否解决了特定问题（如代码重复、并发风险），以及方案对比是否清晰、客观。
    6.  **定稿与交付**: `Service层设计（最终版）`定稿为 **P04**。AI清理`temp_workspace.md`，并输出 `[-- DONE --]`。

---

### **步骤 6/7: 控制层设计与关联 (Controller Layer Design & Association)**

*   **目标**: (v01精华) 完成API入口的最后一公里设计，确保Controller是一个职责清晰、轻量、且高度规范的"薄层"。
*   **优化点**: (v01精华) 自动化生成校验规则和API文档注解，使设计文档无限接近于最终代码。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：控制层设计与关联。模式：精密`。
    2.  **动态上下文作用域构建**: AI提议将 `P04-Service层设计(最终版)`、`P03-API接口契约(定稿)` 和 `Controller设计规范` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容执行以下业务动作：
        *   A. **职责委派**：为每个Controller方法编写实现逻辑，核心就是一行代码：调用对应的Service方法。
        *   B. **参数校验**：为每个请求DTO的字段，自动生成`JSR 303/JSR 380 (javax.validation)`的校验注解（如`@NotNull`, `@Size`, `@Email`）。
        *   C. **API文档注解**：为每个方法和DTO，自动生成详细的**Swagger/OpenAPI**注解（如`@Operation`, `@Parameter`, `@ApiResponse`），确保文档与设计100%同步。
        *   D. **统一响应封装**：明确所有方法都将Service的返回结果用统一的`ApiResponse`结构体进行包装后返回。
    4.  **产出草案与迭代精炼**: 人类专家审查校验规则、API文档等，进行微调直至定稿。
    5.  **闭环自检**: AI自检校验注解是否覆盖所有应校验字段，API文档是否完整、无遗漏。
    6.  **定稿与交付**: `Controller层设计`定稿为 **P05**。AI清理`temp_workspace.md`，并输出 `[-- DONE --]`。

---

### **步骤 7/7: 交付物合成 (Deliverable Synthesis)**

*   **目标**: (新增) 将所有阶段性产出物整合成一份统一的、可交付的最终设计文档。
*   **优化点**: (新增) 确保最终交付物的完整性和一致性。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：交付物合成。模式：精密`。
    2.  **动态上下文作用域构建**: AI提议将 `P01` 到 `P05` 的所有定稿产出物作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容执行以下业务动作：
        *   A. 将所有定稿产出整合到一份统一的`后端技术设计文档 (BTD)`中。
        *   B. 为整合后的文档生成摘要和目录。
    4.  **产出草案与迭代精炼**: 人类专家对最终的整合文档进行最后审查。
    5.  **闭环自检**: AI自检最终文档是否包含了所有步骤的定稿内容，无一遗漏。
    6.  **定稿与交付**: `后端技术设计文档 (BTD)`最终定稿。AI清理`temp_workspace.md`。流程结束。 