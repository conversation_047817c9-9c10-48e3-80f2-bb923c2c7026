### API 设计规范 (RESTful) (V2.0 - 公司标准版)

本规范定义了项目对外提供RESTful API的标准，旨在确保接口的规范性、一致性和易用性。**本文档根据公司后端代码相关规范进行编写。**

#### 1. URL 设计
- **版本化**：API **必须**包含版本号，置于路径开头。例如：`/api/v1/...`。
- **资源导向**：URL代表一种资源，**必须**使用名词复数形式，并用小写字母和破折号。
  - **推荐**: `/api/v1/users`, `/api/v1/purchase-orders`
  - **禁止**: `/api/v1/getAllUsers`, `/api/v1/createOrder`
- **路径参数**：使用路径参数定位特定资源：`/api/v1/users/{userId}`。

#### 2. HTTP 方法 (动词)
严格按照HTTP方法的语义使用动词来操作资源：
- **`GET`**：读取/检索资源（查询）。
- **`POST`**：创建新资源。
- **`PUT`**：**完整更新**一个已存在的资源。
- **`DELETE`**：删除一个资源（逻辑删除）。

#### 3. 统一响应结构 (`ResultData`)
所有API的响应体**必须**使用公司基础架构中定义的`ResultData<T>`进行包装。

```java
// 公司通用响应对象 ResultData<T>
public class ResultData<T> {
    private int code;      // 响应码
    private String msg;    // 响应消息
    private T data;        // 响应数据
    private boolean success; // 是否成功
}
```
- **`code` (响应码)**：
  - `200`: 代表成功。
  - `其他值`: 代表各类业务异常或系统错误。需有统一的错误码定义。
- **`msg` (响应消息)**：对`code`的文本描述。
- **`data` (数据体)**：存放返回的业务数据，可以是VO对象或`PageResult`。
- **`success` (是否成功)**：`true` 或 `false`。

#### 4. 数据对象 (DTO / VO)
- **分层明确**：严格区分不同场景的数据对象。
  - **DTO (Data Transfer Object)**: 用于服务层之间的数据传输。
  - **VO (View Object)**: 用于Controller层返回给前端的视图对象。
- **强制使用VO**：Controller的响应体**必须**是VO对象，严禁直接返回数据库实体(`Entity/PO`)。
- **命名规范**：
  - 请求参数对象：`...Query`, `...Form`。
  - 响应视图对象：`...VO`。
- **字段规范**：JSON字段名**必须**使用小驼峰命名法 (`camelCase`)。

#### 5. 过滤、排序与分页
- **分页查询**：对于返回资源列表的接口，**必须**支持分页。
  - **请求**：使用统一的`PageQuery`对象作为参数。
  - **响应**：响应体`data`中**必须**使用公司定义的`PageResult<T>`对象。
    ```java
    // 公司通用分页响应对象 PageResult<T>
    public class PageResult<T> {
        private List<T> list; // 当前页数据列表
        private Long total;   // 总记录数
    }
    ```
- **其他查询**：
  - **列表查询**：使用`...ListQuery`对象。
  - **详情查询**：使用ID作为路径参数。

#### 6. API 文档 (Swagger)
- 所有API接口**必须**使用 `springdoc-openapi` (Swagger) 提供实时、准确的文档。
- **必须**使用注解 (`@Operation`, `@Parameter`, `@Tag` 等)清晰地描述接口功能、参数、标签和响应模型。
- Controller类应使用`@Tag`注解进行分组。

#### 7. HTTP 状态码
- **业务成功**: 统一返回HTTP `200 OK`，具体的业务成功或失败通过`ResultData`中的`code`和`success`字段来体现。
- **客户端/服务端技术错误**:
  - `401 Unauthorized`: 用户未认证。
  - `403 Forbidden`: 用户无权访问。
  - `404 Not Found`: 请求的API路径不存在。
  - `500 Internal Server Error`: 服务器内部发生未被捕获的未知异常。
