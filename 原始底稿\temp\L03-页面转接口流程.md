### **AIDAD驱动的“页面到API逆向设计 (P2A)”工作流**。

此工作流旨在将一个或多个前端页面/组件，系统性地转化为一份包含**实体模型**和**API接口契约**的、可直接交付给后端团队的高质量设计文档。

---

### **AIDAD P2A 工作流详解**

#### **核心输出**
*   **P01**: 页面元素与数据分析清单
*   **P02**: 领域实体模型 (含E-R图)
*   **P03**: API接口契约 (含DTO与完整示例)

---

### **步骤 1/3: 页面解构与精准提取 (Page Deconstruction & Precision Extraction)**

*   **目标**: 绝对忠实地将前端页面中所有可见、可交互的元素，1:1地提取为结构化的原始数据。**此阶段只做“搬运工”，不做“设计师”**。
*   **AIDAD原则应用**: `任务模式切换(精密模式)`、`动态上下文作用域`。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：解构页面[页面/组件名]，提取数据元素。模式：精密`。
    2.  **动态上下文作用域构建**: AI提议将 `[页面/组件源码文件]`、`[页面截图]`、`[相关需求描述]` 和 `API设计规范(用于格式)` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容，对页面进行地毯式扫描，并生成一份 **「P01: 页面元素与数据分析清单」**，包含两部分：
        *   **A. 数据元素清单**: 以表格形式，列出所有用于**展示**或**输入**数据的UI组件及其包含的字段。
            | UI组件类型 | 组件位置/标识 | 字段名 (原始) | 中文标签 | 备注 (如：下拉选项) |
            |:---|:---|:---|:---|:---|
            | 表单输入框 | 搜索区 | `appointmentId` | 预约ID | |
            | 表格列 | 结果表格 | `patientName` | 车主姓名 | |
        *   **B. 用户动作清单**: 以列表形式，列出所有可能触发后端交互的用户动作。
            *   **动作名称**: 点击“查询”按钮
                *   **输入数据**: `appointmentId`, `patientName`, ... (来自搜索表单)
                *   **期望输出/更新的UI**: 刷新“结果表格”
            *   **动作名称**: 点击“删除”按钮
                *   **输入数据**: `appointmentId` (来自当前行)
                *   **期望输出/更新的UI**: 移除当前行，弹出成功提示
    4.  **产出草案与迭代精炼**: 人类专家快速审查P01清单，确保**无任何遗漏**。由于是精密模式，此处的迭代会非常快。
    5.  **闭环自检**: AI自检是否清单中的每一项都能在页面源码或截图中找到精确对应。
    6.  **定稿与交付**: `P01`定稿。AI清理`temp_workspace.md`，并输出 `[-- DONE --]`。

---

### **步骤 2/3: 实体建模与关系梳理 (Entity Modeling & Relationship Refinement)**

*   **目标**: 基于上一步提取的原始数据，进行归纳、抽象和设计，构建出稳定、规范的后端领域实体模型。**此阶段AI从“搬运工”转变为“架构师”**。
*   **AIDAD原则应用**: `任务模式切换(创意模式)`、`信息降噪与范式化`。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：基于P01进行实体建模。模式：创意`。
    2.  **动态上下文作用域构建**: AI提议将 `P01(定稿)` 和 `数据库设计规范` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **在隔离环境中执行 (AI核心业务动作)**: AI严格基于`temp_workspace.md`中的内容，对P01中的所有原始字段进行分析，并生成一份 **「P02: 领域实体模型」** 草案，包含：
        *   A. **实体聚类与提炼**:
            *   **分析**: "原始字段`patientName`、`patientGender`、`patientAge`总是同时出现，它们应归属于`Patient`实体。"
            *   **合并**: "`AppointmentListItem`和`AppointmentDetail`中的字段高度重合，建议合并为统一的`Appointment`实体。"
        *   B. **生成E-R图**: AI生成一份使用Mermaid.js语法的**「实体关系图 (E-R Diagram)」**，可视化实体间的关系（一对多，多对多等）。
        *   C. **生成实体定义表**: 为每个实体生成详细的定义表。
            | 实体名 (Entity) | 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
            |:---|:---|:---|:---|:---|
            | `Appointment` | `id` | `long` | 主键ID | |
            | `Appointment` | `patientId` | `long` | 患者ID | 外键关联Patient |
            | `Patient` | `id` | `long` | 主键ID | |
            | `Patient` | `name` | `string` | 患者姓名 | |
    4.  **产出草案与迭代精炼**: 这是关键的人机协作环节。人类专家利用经验，对AI的实体划分、关系定义进行决策和微调。AI以顾问角色提供备选方案。
    5.  **闭环自检**: AI自检P01中的每个核心业务字段是否都已归入P02的某个实体中，是否存在无法归类的“孤儿”字段。
    6.  **定稿与交付**: `P02`定稿，**它将成为下一步的“核心资产”**。AI清理`temp_workspace.md`，并输出 `[-- DONE --]`。

---

### **步骤 3/3: 接口契约生成与交叉验证 (API Contract Generation & Cross-Validation)**

*   **目标**: 以步骤2的实体模型为“唯一事实来源”，生成最终的、包含完整细节的API接口契约，并确保其与最初的页面分析结果完全对齐。**此阶段是设计的收官与质量保证**。
*   **AIDAD原则应用**: `资产包裹与保真`、`核心资产与策略预对齐`、`指令意图词典(Verify, Fill)`。
*   **执行过程 (AIDAD核心交互循环)**:
    1.  **任务定义与模式选择**: 人类专家下达指令 `下一步：生成API契约。使用P02作为事实标准，来**验证**和**填充**P01中的动作清单。模式：精密`。
    2.  **动态上下文作用域构建**: AI提议将 `P01(定稿)`、`P02(定稿)` 和 `API设计规范` 作为上下文写入`temp_workspace.md`，待人类确认。
    3.  **核心资产与策略预对齐**:
        *   **AI进行资产声明**: "核心资产声明：本次任务的'核心资产'是 `P02-领域实体模型`。所有接口的返回结构、DTO定义和字段的`归属表`必须严格以P02为准，不可修改或演绎。请确认。"
        *   **人类专家确认**: `确认`。
        *   **AI提交执行策略**: "收到。我的执行策略是：1. 遍历P01中的每个'用户动作'。2. 为每个动作设计一个RESTful接口。3. 遍历该动作涉及的每个输入/输出字段，在P02中查找其归属实体，并**填充**`归属表`列。4. 如果在P02中找不到，则标记为`不适用 (计算字段)`。5. 根据P02的实体结构，生成完整的请求/返回JSON示例。请确认此策略。"
        *   **人类专家确认**: `策略正确，开始执行。`
    4.  **在隔离环境中执行 (AI核心业务动作)**: AI严格执行上述策略，生成 **「P03: API接口契约」**，为P01中的每个用户动作，产出完整的API定义。
        *   **业务动作**: 查询预约列表
        *   **接口描述**: 根据条件查询预约列表。
        *   **请求方法**: `GET`
        *   **接口路径**: `/api/v1/appointments`
        *   **请求参数**:
            | 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 |
            |:---|:---|:---|:---|:---|
            | `appointmentId` | `long`| `false` | `Appointment` | 预约ID |
        *   **响应DTO**: `AppointmentDTO` (结构严格遵循P02定义)
        *   **完整响应示例 (JSON)**:
            ```json
            // ... (根据P02实体结构生成的完整JSON示例) ...
            ```
    5.  **产出草案与迭代精炼**: 人类专家进行最终审查，由于所有规则已预先对齐，此处的修改将非常少。
    6.  **闭环自检**: AI自检：1. 是否所有API都源自P01的动作？ 2. 是否所有DTO结构和`归属表`都与P02完全一致？ 3. 是否所有接口都符合API设计规范？
    7.  **定稿与交付**: `P03`定稿。AI清理`temp_workspace.md`。流程结束。