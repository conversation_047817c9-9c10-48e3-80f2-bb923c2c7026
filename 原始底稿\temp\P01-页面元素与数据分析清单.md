# P01: 页面元素与数据分析清单

## A. 数据元素清单

### AppointmentManagementView.vue (预约管理页面)

| UI组件类型 | 组件位置/标识 | 字段名 (原始) | 中文标签 | 备注 |
|:---|:---|:---|:---|:---|
| 页面标题 | 页面顶部 | - | 售后预约管理 | h1.page-title |
| 表单输入框 | 搜索区域第1行第1列 | `appointmentId` | 预约单号 | placeholder="请输入预约单号" |
| 表单输入框 | 搜索区域第1行第2列 | `licensePlate` | 车牌号 | placeholder="请输入车牌号" |
| 表单输入框 | 搜索区域第1行第3列 | `reservationPhone` | 预约人手机号 | placeholder="请输入预约人手机号" |
| 表单输入框 | 搜索区域第1行第4列 | `servicePhone` | 送修人手机号 | placeholder="请输入送修人手机号" |
| 日期选择器 | 搜索区域第1行第5列 | `dateRange` | 创建时间 | type="daterange", 默认值为今天 |
| 下拉选择框 | 搜索区域第1行第6列 | `status` | 状态 | 选项: 未到店/已到店/已取消/未履约/待支付 |
| 下拉选择框 | 搜索区域第2行第1列 | `serviceType` | 维修类型 | 选项: 保养/维修 |
| 下拉选择框 | 搜索区域第2行第2列 | `serviceAdvisorId` | 服务顾问 | 动态获取服务顾问列表 |
| 搜索按钮 | 搜索区域第2行第3列 | - | 搜索 | type="primary" |
| 重置按钮 | 搜索区域第2行第3列 | - | 重置 | - |
| 统计文本 | 操作区域左侧 | `total` | 共找到 X 条记录 | - |
| 导出按钮 | 操作区域右侧 | - | 导出Excel | type="primary" |
| 表格列 | 表格区域 | `id` | 预约单号 | 可点击链接 |
| 表格列 | 表格区域 | `licensePlate` | 车牌号 | - |
| 表格列 | 表格区域 | `reservationContactName` | 预约人姓名 | - |
| 表格列 | 表格区域 | `reservationContactPhone` | 预约人手机号 | - |
| 表格列 | 表格区域 | `serviceContactName` | 送修人姓名 | - |
| 表格列 | 表格区域 | `serviceContactPhone` | 送修人手机号 | - |
| 表格列 | 表格区域 | `appointmentTime` | 预约日期 | - |
| 表格列 | 表格区域 | `timeSlot` | 预约时间段 | - |
| 表格列 | 表格区域 | `serviceType` | 服务类型 | 标签形式：保养(绿色)/维修(橙色) |
| 表格列 | 表格区域 | `status` | 预约状态 | 标签形式：已到店(绿色)/未到店(灰色)/已取消(灰色)/待支付(橙色)/未履约(红色) |
| 表格列 | 表格区域 | `serviceAdvisor.name` | 服务顾问 | 显示"未分配"如果为空 |
| 表格列 | 表格区域 | `qualityInspectionId` | 环检单编号 | 显示"未生成"如果为空 |
| 表格列 | 表格区域 | `createdAt` | 创建时间 | - |
| 操作按钮 | 表格操作列 | - | 详情 | type="primary", link |
| 操作按钮 | 表格操作列 | - | 创建环检单 | type="success", 条件显示 |

### 详情对话框数据元素

| UI组件类型 | 组件位置/标识 | 字段名 (原始) | 中文标签 | 备注 |
|:---|:---|:---|:---|:---|
| 对话框标题 | 详情对话框 | `id` | 预约详情 - {预约单号} | - |
| 描述列表项 | 预约信息区域 | `id` | 预约单号 | - |
| 描述列表项 | 预约信息区域 | `status` | 状态 | 标签形式 |
| 描述列表项 | 预约信息区域 | `appointmentTime`, `timeSlot` | 预约时间 | 组合显示 |
| 描述列表项 | 预约信息区域 | `serviceType` | 服务类型 | 标签形式 |
| 描述列表项 | 预约信息区域 | `customerDescription` | 客户描述 | 可选字段 |
| 描述列表项 | 客户信息区域 | `reservationContactName` | 预约下单人姓名 | - |
| 描述列表项 | 客户信息区域 | `reservationContactPhone` | 预约下单人手机号 | - |
| 描述列表项 | 客户信息区域 | `serviceContactName` | 送修人姓名 | - |
| 描述列表项 | 客户信息区域 | `serviceContactPhone` | 送修人手机号 | - |
| 描述列表项 | 车辆信息区域 | `licensePlate` | 车牌号 | - |
| 描述列表项 | 车辆信息区域 | `vin` | VIN码 | - |
| 描述列表项 | 车辆信息区域 | `model` | 车型 | - |
| 描述列表项 | 车辆信息区域 | `variant` | 车型变体 | - |
| 描述列表项 | 车辆信息区域 | `color` | 颜色 | - |
| 描述列表项 | 车辆信息区域 | `mileage` | 里程数 | 单位km |
| 描述列表项 | 预约信息区域 | `store.name` | 门店 | - |
| 描述列表项 | 预约信息区域 | `serviceAdvisor.name` | 服务顾问 | - |
| 表格 | 服务内容区域 | `maintenancePackage.items` | 服务包列表 | 保养类型显示 |
| 表格列 | 服务包表格 | `code` | 服务包Code | - |
| 表格列 | 服务包表格 | `name` | 服务包名称 | - |
| 表格列 | 服务包表格 | `quantity` | 数量 | - |
| 表格列 | 服务包表格 | `price` | 价格 | 格式化显示 |
| 描述列表项 | 服务内容区域 | `maintenancePackage.totalAmount` | 总金额 | - |
| 描述列表项 | 支付信息区域 | `paymentStatus` | 支付状态 | 标签形式 |
| 描述列表项 | 支付信息区域 | `paymentAmount` | 支付金额 | - |
| 描述列表项 | 支付信息区域 | `paymentOrderNumber` | 支付流水号 | - |

### 环检单确认对话框数据元素

| UI组件类型 | 组件位置/标识 | 字段名 (原始) | 中文标签 | 备注 |
|:---|:---|:---|:---|:---|
| 输入框 | 客户信息区域 | `editableServiceContact.name` | 送修人姓名 | 可编辑 |
| 输入框 | 客户信息区域 | `editableServiceContact.phone` | 送修人手机号 | 可编辑 |
| 确认按钮 | 对话框底部 | - | 确认创建 | type="primary" |
| 取消按钮 | 对话框底部 | - | 取消 | - |

### AppointmentDashboardView.vue (预约看板页面)

| UI组件类型 | 组件位置/标识 | 字段名 (原始) | 中文标签 | 备注 |
|:---|:---|:---|:---|:---|
| 页面标题 | 页面顶部 | - | 预约看板 | h1.page-title |
| 统计卡片 | 统计区域第1个 | `stats.totalAppointments` | 今日总预约 | - |
| 统计卡片 | 统计区域第2个 | `stats.arrivedCount` | 今日已到店 | - |
| 统计卡片 | 统计区域第3个 | `stats.notArrivedCount` | 今日未到店 | - |
| 统计卡片 | 统计区域第4个 | `stats.arrivalRate` | 今日到店率 | 百分比显示 |
| 筛选按钮 | 筛选区域左侧 | - | 未到店({数量}) | activeFilter='notArrived' |
| 筛选按钮 | 筛选区域左侧 | - | 已到店({数量}) | activeFilter='arrived' |
| 筛选按钮 | 筛选区域左侧 | - | 未履约({数量}) | activeFilter='notFulfilled' |
| 筛选按钮 | 筛选区域右侧 | - | 明天预约({数量}) | activeFilter='tomorrow' |
| 表格列 | 表格区域 | `plateNumber` | 车牌号 | - |
| 表格列 | 表格区域 | `appointmentDate` | 预约日期 | - |
| 表格列 | 表格区域 | `timeSlot` | 预约时间段 | - |
| 表格列 | 表格区域 | `serviceType` | 维修类型 | 标签形式：保养(绿色)/维修(橙色) |
| 表格列 | 表格区域 | `status` | 预约状态 | 标签形式：已到店(绿色)/未到店(灰色)/未履约(红色) |

## B. 用户动作清单

### AppointmentManagementView.vue 用户动作

1. **动作名称**: 点击"搜索"按钮
   - **输入数据**: `appointmentId`, `licensePlate`, `reservationPhone`, `servicePhone`, `dateRange`, `status`, `serviceType`, `serviceAdvisorId`
   - **期望输出/更新的UI**: 刷新预约列表表格，更新记录总数

2. **动作名称**: 点击"重置"按钮
   - **输入数据**: 无
   - **期望输出/更新的UI**: 清空所有搜索条件，重置为默认值，刷新预约列表

3. **动作名称**: 点击"导出Excel"按钮
   - **输入数据**: 当前筛选条件下的数据
   - **期望输出/更新的UI**: 下载Excel文件

4. **动作名称**: 点击预约单号链接
   - **输入数据**: `row.id` (预约单号)
   - **期望输出/更新的UI**: 打开预约详情对话框

5. **动作名称**: 点击"详情"按钮
   - **输入数据**: `row.id` (预约单号)
   - **期望输出/更新的UI**: 打开预约详情对话框，显示完整预约信息

6. **动作名称**: 点击"创建环检单"按钮
   - **输入数据**: `row` (预约单对象)
   - **期望输出/更新的UI**: 打开环检单确认对话框

7. **动作名称**: 确认创建环检单
   - **输入数据**: `currentInspectionAppointment.value.id`, `editableServiceContact.name`, `editableServiceContact.phone`
   - **期望输出/更新的UI**: 创建环检单，更新预约状态，关闭对话框，显示成功消息

8. **动作名称**: 取消预约
   - **输入数据**: `row.id` (预约单号), `reason` (取消原因)
   - **期望输出/更新的UI**: 更新预约状态为"已取消"，刷新列表，显示成功消息

9. **动作名称**: 更新预约状态
   - **输入数据**: `row.id` (预约单号), `newStatus` (新状态)
   - **期望输出/更新的UI**: 更新预约状态，刷新列表，显示成功消息

10. **动作名称**: 分页页码改变
    - **输入数据**: `page` (页码)
    - **期望输出/更新的UI**: 刷新当前页数据

11. **动作名称**: 分页每页数量改变
    - **输入数据**: `size` (每页数量)
    - **期望输出/更新的UI**: 重置到第一页并刷新数据

12. **动作名称**: 页面初始化加载
    - **输入数据**: 默认搜索条件 (今日日期范围)
    - **期望输出/更新的UI**: 加载预约列表和服务顾问/技师列表

### AppointmentDashboardView.vue 用户动作

1. **动作名称**: 点击"未到店"筛选按钮
   - **输入数据**: `activeFilter = 'notArrived'`
   - **期望输出/更新的UI**: 过滤显示未到店预约列表，按钮高亮

2. **动作名称**: 点击"已到店"筛选按钮
   - **输入数据**: `activeFilter = 'arrived'`
   - **期望输出/更新的UI**: 过滤显示已到店预约列表，按钮高亮

3. **动作名称**: 点击"未履约"筛选按钮
   - **输入数据**: `activeFilter = 'notFulfilled'`
   - **期望输出/更新的UI**: 过滤显示未履约预约列表，按钮高亮

4. **动作名称**: 点击"明天预约"筛选按钮
   - **输入数据**: `activeFilter = 'tomorrow'`
   - **期望输出/更新的UI**: 切换到明日预约数据，显示明日预约列表

5. **动作名称**: 页面初始化加载
   - **输入数据**: 默认今日数据，默认未到店状态
   - **期望输出/更新的UI**: 加载今日统计数据和未到店预约列表

### 系统自动化动作

1. **动作名称**: APP扫码签到数据同步
   - **输入数据**: 车牌号、签到时间、门店标识、设备信息
   - **期望输出/更新的UI**: 预约状态从"未到店"更新为"已到店"，实时更新看板统计数据

2. **动作名称**: 预约状态自动维护
   - **输入数据**: 当前时间、预约时间
   - **期望输出/更新的UI**: 超时预约自动更新为"未履约"状态 