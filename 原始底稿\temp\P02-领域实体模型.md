# P02: 领域实体模型

## A. 实体聚类与提炼分析

### 分析过程：

**1. 字段归属分析**：
- **预约核心字段**：`appointmentId`, `appointmentTime`, `timeSlot`, `serviceType`, `status`, `customerDescription`, `createdAt` 等都明确属于预约单实体
- **客户字段分离**：`reservationContactName/Phone` 和 `serviceContactName/Phone` 代表两个不同角色的人员，应分别建模
- **车辆字段聚合**：`licensePlate`, `vin`, `model`, `variant`, `color`, `mileage` 均为车辆属性，归属车辆实体
- **服务套餐解耦**：`maintenancePackage` 相关字段应独立为套餐实体，与预约单形成关联关系

**2. 实体合并决策**：
- **统一Staff实体**：服务顾问和技师具有相似的基础属性，通过角色字段区分，避免重复建模
- **支付信息内嵌**：支付信息与预约单强关联，采用内嵌模式，简化关系复杂度

## B. 实体关系图 (E-R Diagram)

```mermaid
erDiagram
    APPOINTMENT {
        bigint id PK "主键ID"
        varchar appointment_number UK "预约单号"
        bigint customer_id FK "预约人ID"
        bigint service_contact_id FK "送修人ID"
        bigint vehicle_id FK "车辆ID"
        bigint store_id FK "门店ID"
        bigint service_advisor_id FK "服务顾问ID"
        bigint technician_id FK "技师ID"
        date appointment_date "预约日期"
        varchar time_slot "预约时间段"
        varchar service_type "服务类型"
        varchar status "预约状态"
        text customer_description "客户描述"
        varchar quality_inspection_id "环检单编号"
        varchar payment_status "支付状态"
        decimal payment_amount "支付金额"
        varchar payment_order_number "支付流水号"
        timestamp check_in_time "签到时间"
        varchar check_in_device "签到设备"
        tinyint is_deleted "是否删除"
        varchar created_by "创建人ID"
        timestamp created_at "创建时间"
        varchar updated_by "更新人ID"
        timestamp updated_at "更新时间"
    }

    CUSTOMER {
        bigint id PK "主键ID"
        varchar name "客户姓名"
        varchar phone "手机号"
        varchar one_id UK "OneID"
        tinyint is_deleted "是否删除"
        varchar created_by "创建人ID"
        timestamp created_at "创建时间"
        varchar updated_by "更新人ID"
        timestamp updated_at "更新时间"
    }

    VEHICLE {
        bigint id PK "主键ID"
        varchar vin UK "VIN码"
        varchar license_plate UK "车牌号"
        varchar model "车型"
        varchar variant "车型变体"
        varchar color "颜色"
        int mileage "里程数"
        tinyint is_deleted "是否删除"
        varchar created_by "创建人ID"
        timestamp created_at "创建时间"
        varchar updated_by "更新人ID"
        timestamp updated_at "更新时间"
    }

    STORE {
        bigint id PK "主键ID"
        varchar store_code UK "门店编码"
        varchar name "门店名称"
        varchar address "门店地址"
        varchar qr_code_content "二维码内容"
        varchar qr_code_url "二维码图片地址"
        tinyint is_deleted "是否删除"
        varchar created_by "创建人ID"
        timestamp created_at "创建时间"
        varchar updated_by "更新人ID"
        timestamp updated_at "更新时间"
    }

    STAFF {
        bigint id PK "主键ID"
        varchar staff_code UK "员工编号"
        varchar name "员工姓名"
        varchar role_type "角色类型"
        bigint store_id FK "所属门店ID"
        varchar phone "联系电话"
        tinyint is_deleted "是否删除"
        varchar created_by "创建人ID"
        timestamp created_at "创建时间"
        varchar updated_by "更新人ID"
        timestamp updated_at "更新时间"
    }

    MAINTENANCE_PACKAGE {
        bigint id PK "主键ID"
        varchar package_code UK "套餐编码"
        varchar name "套餐名称"
        decimal total_amount "总金额"
        varchar vehicle_model "适用车型"
        tinyint status "套餐状态"
        tinyint is_deleted "是否删除"
        varchar created_by "创建人ID"
        timestamp created_at "创建时间"
        varchar updated_by "更新人ID"
        timestamp updated_at "更新时间"
    }

    MAINTENANCE_PACKAGE_ITEM {
        bigint id PK "主键ID"
        bigint maintenance_package_id FK "保养套餐ID"
        varchar item_code "服务包Code"
        varchar name "服务包名称"
        int quantity "数量"
        decimal price "价格"
        tinyint is_deleted "是否删除"
        varchar created_by "创建人ID"
        timestamp created_at "创建时间"
        varchar updated_by "更新人ID"
        timestamp updated_at "更新时间"
    }

    APPOINTMENT_PACKAGE {
        bigint id PK "主键ID"
        bigint appointment_id FK "预约单ID"
        bigint maintenance_package_id FK "保养套餐ID"
        tinyint is_deleted "是否删除"
        varchar created_by "创建人ID"
        timestamp created_at "创建时间"
        varchar updated_by "更新人ID"
        timestamp updated_at "更新时间"
    }

    APPOINTMENT ||--|| CUSTOMER : "预约人"
    APPOINTMENT ||--|| CUSTOMER : "送修人"
    APPOINTMENT ||--|| VEHICLE : "预约车辆"
    APPOINTMENT ||--|| STORE : "预约门店"
    APPOINTMENT ||--o| STAFF : "服务顾问"
    APPOINTMENT ||--o| STAFF : "技师"
    APPOINTMENT ||--o{ APPOINTMENT_PACKAGE : "关联套餐"
    APPOINTMENT_PACKAGE ||--|| MAINTENANCE_PACKAGE : "保养套餐"
    MAINTENANCE_PACKAGE ||--o{ MAINTENANCE_PACKAGE_ITEM : "套餐明细"
    STAFF ||--|| STORE : "所属门店"
```

## C. 实体定义表

### 1. Appointment (预约单) - 核心实体

| 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|
| `id` | `BIGINT` | 主键ID | 自增主键 |
| `appointment_number` | `VARCHAR(32)` | 预约单号 | 业务唯一标识 |
| `customer_id` | `BIGINT` | 预约人ID | 外键关联Customer |
| `service_contact_id` | `BIGINT` | 送修人ID | 外键关联Customer |
| `vehicle_id` | `BIGINT` | 车辆ID | 外键关联Vehicle |
| `store_id` | `BIGINT` | 门店ID | 外键关联Store |
| `service_advisor_id` | `BIGINT` | 服务顾问ID | 外键关联Staff，可为空 |
| `technician_id` | `BIGINT` | 技师ID | 外键关联Staff，可为空 |
| `appointment_date` | `DATE` | 预约日期 | 预约服务日期 |
| `time_slot` | `VARCHAR(20)` | 预约时间段 | 如"09:00-10:00" |
| `service_type` | `VARCHAR(20)` | 服务类型 | maintenance/repair |
| `status` | `VARCHAR(20)` | 预约状态 | not_arrived/arrived/no_show |
| `customer_description` | `TEXT` | 客户描述 | 客户填写的具体需求 |
| `quality_inspection_id` | `VARCHAR(32)` | 环检单编号 | 关联的质检单号 |
| `payment_status` | `VARCHAR(20)` | 支付状态 | paid/unpaid/refunded |
| `payment_amount` | `DECIMAL(10,2)` | 支付金额 | 实际支付金额 |
| `payment_order_number` | `VARCHAR(64)` | 支付流水号 | 支付系统订单号 |
| `check_in_time` | `TIMESTAMP` | 签到时间 | APP扫码签到时间 |
| `check_in_device` | `VARCHAR(100)` | 签到设备 | 签到时的设备信息 |

### 2. Customer (客户) - 独立实体

| 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|
| `id` | `BIGINT` | 主键ID | 自增主键 |
| `name` | `VARCHAR(50)` | 客户姓名 | 客户真实姓名 |
| `phone` | `VARCHAR(20)` | 手机号 | 联系手机号 |
| `one_id` | `VARCHAR(36)` | OneID | 客户统一标识 |

### 3. Vehicle (车辆) - 独立实体

| 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|
| `id` | `BIGINT` | 主键ID | 自增主键 |
| `vin` | `VARCHAR(17)` | VIN码 | 车辆识别号，全局唯一 |
| `license_plate` | `VARCHAR(20)` | 车牌号 | 车牌号码 |
| `model` | `VARCHAR(50)` | 车型 | 车辆型号 |
| `variant` | `VARCHAR(50)` | 车型变体 | 具体配置版本 |
| `color` | `VARCHAR(30)` | 颜色 | 车身颜色 |
| `mileage` | `INT` | 里程数 | 当前行驶里程 |

### 4. Store (门店) - 独立实体

| 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|
| `id` | `BIGINT` | 主键ID | 自增主键 |
| `store_code` | `VARCHAR(20)` | 门店编码 | 门店业务编码 |
| `name` | `VARCHAR(100)` | 门店名称 | 门店显示名称 |
| `address` | `VARCHAR(200)` | 门店地址 | 详细地址 |
| `qr_code_content` | `VARCHAR(500)` | 二维码内容 | 用于APP扫码的内容 |
| `qr_code_url` | `VARCHAR(200)` | 二维码图片地址 | 二维码图片存储路径 |

### 5. Staff (员工) - 统一实体

| 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|
| `id` | `BIGINT` | 主键ID | 自增主键 |
| `staff_code` | `VARCHAR(20)` | 员工编号 | 员工业务编码 |
| `name` | `VARCHAR(50)` | 员工姓名 | 员工真实姓名 |
| `role_type` | `VARCHAR(20)` | 角色类型 | service_advisor/technician |
| `store_id` | `BIGINT` | 所属门店ID | 外键关联Store |
| `phone` | `VARCHAR(20)` | 联系电话 | 员工联系方式 |

### 6. MaintenancePackage (保养套餐) - 独立实体

| 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|
| `id` | `BIGINT` | 主键ID | 自增主键 |
| `package_code` | `VARCHAR(20)` | 套餐编码 | 套餐业务编码 |
| `name` | `VARCHAR(100)` | 套餐名称 | 套餐显示名称 |
| `total_amount` | `DECIMAL(10,2)` | 总金额 | 套餐总价格 |
| `vehicle_model` | `VARCHAR(50)` | 适用车型 | 适用的车型范围 |
| `status` | `TINYINT` | 套餐状态 | 0:停用 1:启用 |

### 7. MaintenancePackageItem (保养套餐明细) - 明细实体

| 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|
| `id` | `BIGINT` | 主键ID | 自增主键 |
| `maintenance_package_id` | `BIGINT` | 保养套餐ID | 外键关联MaintenancePackage |
| `item_code` | `VARCHAR(20)` | 服务包Code | 服务项目编码 |
| `name` | `VARCHAR(100)` | 服务包名称 | 服务项目名称 |
| `quantity` | `INT` | 数量 | 服务数量 |
| `price` | `DECIMAL(10,2)` | 价格 | 单项价格 |

### 8. AppointmentPackage (预约套餐关联) - 关联实体

| 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|
| `id` | `BIGINT` | 主键ID | 自增主键 |
| `appointment_id` | `BIGINT` | 预约单ID | 外键关联Appointment |
| `maintenance_package_id` | `BIGINT` | 保养套餐ID | 外键关联MaintenancePackage |

## D. 实体关系描述

1. **Appointment ↔ Customer**: 一个预约单关联两个客户角色（预约人和送修人），通过不同外键字段区分
2. **Appointment → Vehicle**: 一个预约单对应一辆车（多对一）
3. **Appointment → Store**: 一个预约单对应一个门店（多对一）
4. **Appointment → Staff**: 一个预约单可分配给服务顾问和技师（多对一，可选）
5. **Appointment ↔ MaintenancePackage**: 通过中间表AppointmentPackage建立多对多关系（保养类预约）
6. **MaintenancePackage → MaintenancePackageItem**: 一个套餐包含多个服务项目（一对多）
7. **Staff → Store**: 员工归属于特定门店（多对一）

## E. 闭环自检

✅ **字段完整性检查**: P01中的所有核心业务字段都已归入相应实体
- 预约相关 → Appointment实体
- 客户信息 → Customer实体（分离预约人和送修人）
- 车辆信息 → Vehicle实体
- 门店信息 → Store实体
- 人员信息 → Staff实体（统一服务顾问和技师）
- 保养套餐 → MaintenancePackage & MaintenancePackageItem实体
- 支付信息 → 内嵌至Appointment实体

✅ **关系一致性检查**: 所有实体间关系都能支持P01中的业务动作需求

✅ **数据库规范符合性检查**: 所有实体均遵循公司数据库设计规范
- 表命名：采用tt_前缀的业务表命名
- 字段命名：小写+下划线格式
- 必需字段：包含完整的审计字段
- 外键命名：遵循{关联表名}_id规范 