# 到店登记-实体信息及实体关系

## 1. 实体定义与属性

### 1.1 到店登记单 (Check-in Record)
-   **定义：** 记录自然进店客户及其车辆基本信息、送修信息的业务单据。
-   **主要属性：**
    -   `登记单号 (checkin_id)`: 唯一标识符。
    -   `车牌号 (license_plate)`: 关联车辆信息。
    -   `车型配置 (vehicle_model_config)`: 车辆详细型号配置。
    -   `颜色 (color)`: 车辆颜色。
    -   `送修人名称 (repair_person_name)`: 实际送修人姓名。
    -   `送修人手机号 (repair_person_phone)`: 实际送修人手机号。
    -   `服务顾问 (service_advisor)`: 负责该登记单的服务顾问姓名/ID。
    -   `关联环检单号 (related_repair_order_id)`: 关联的环检单号（如果已创建）。
    -   `服务类型 (service_type)`: 例如"维修"（默认）。
    -   `创建时间 (created_at)`: 登记单创建时间。
    -   `更新时间 (updated_at)`: 登记单最后更新时间。
    -   `备注 (notes)`: 其他需要记录的信息。
    -   `逻辑删除标志 (is_deleted)`: 布尔值，标识是否逻辑删除。

### 1.2 客户 (Customer)
-   **定义：** 接受服务的个体，可以是车主或实际送修人。
-   **主要属性：**
    -   `客户姓名 (customer_name)`: 客户名称。
    -   `客户手机号 (customer_phone)`: 客户联系电话。
    -   `车主信息 (owner_info)`: （如果送修人不是车主，需记录车主信息，或从车辆信息中带出）。

### 1.3 车辆 (Vehicle)
-   **定义：** 客户送修的车辆信息。
-   **主要属性：**
    -   `车牌号 (license_plate)`: 车辆唯一标识。
    -   `车型 (model)`: 车辆型号。
    -   `车龄 (vehicle_age)`: 从交车时间计算的车辆使用年限（月）。
    -   `交车时间 (delivery_date)`: 车辆交付日期。
    -   `销售状态 (sales_status)`: 是否为已销售车辆（用于查询时过滤）。

### 1.4 环检单 (Repair Order)
-   **定义：** 基于到店登记单创建的后续服务单据。
-   **主要属性：**
    -   `环检单号 (repair_order_id)`: 环检单的唯一标识。
    -   `关联登记单号 (related_checkin_id)`: 关联的到店登记单号。
    -   `其他环检相关属性...`

### 1.5 角色 (Roles)
-   **服务顾问 (Service Advisor):**
    -   **权限：** 查看、创建、编辑、删除（逻辑删除）到店登记单；创建关联环检单。
-   **服务经理 (Service Manager):**
    -   **权限：** 查看全店所有到店登记单。

## 2. 实体关系

```mermaid
erDiagram
    CUSTOMER ||--o{ CHECKIN_RECORD : "拥有/送修"
    VEHICLE ||--o{ CHECKIN_RECORD : "属于/关联"
    SERVICE_ADVISOR }|--|{ CHECKIN_RECORD : "负责"
    CHECKIN_RECORD ||--o{ REPAIR_ORDER : "生成"

    CUSTOMER {
        varchar customer_name
        varchar customer_phone
        varchar owner_info
    }
    VEHICLE {
        varchar license_plate PK
        varchar model
        int vehicle_age
        date delivery_date
        boolean sales_status
    }
    CHECKIN_RECORD {
        varchar checkin_id PK
        varchar license_plate FK
        varchar vehicle_model_config
        varchar color
        varchar repair_person_name
        varchar repair_person_phone
        varchar service_advisor
        varchar related_repair_order_id FK
        varchar service_type
        datetime created_at
        datetime updated_at
        text notes
        boolean is_deleted
    }
    REPAIR_ORDER {
        varchar repair_order_id PK
        varchar related_checkin_id FK
    }
    SERVICE_ADVISOR {
        varchar advisor_id PK
        varchar advisor_name
    }
    SERVICE_MANAGER {
        varchar manager_id PK
        varchar manager_name
    }
}
```

### 关系描述：

-   **客户 (CUSTOMER) 与 到店登记单 (CHECKIN_RECORD):** 一位客户可以有多个到店登记单 (一对多关系)。`CHECKIN_RECORD` 中的 `送修人名称` 和 `送修人手机号` 直接关联客户信息。
-   **车辆 (VEHICLE) 与 到店登记单 (CHECKIN_RECORD):** 一辆车可以有多个到店登记单 (一对多关系)。`CHECKIN_RECORD` 通过 `车牌号` 关联 `VEHICLE`。
-   **服务顾问 (SERVICE_ADVISOR) 与 到店登记单 (CHECKIN_RECORD):** 一位服务顾问可以负责多个到店登记单 (一对多关系)。`CHECKIN_RECORD` 中的 `服务顾问` 字段关联 `SERVICE_ADVISOR`。
-   **到店登记单 (CHECKIN_RECORD) 与 环检单 (REPAIR_ORDER):** 一张到店登记单可以生成一张环检单 (一对一或一对零/一关系)。`REPAIR_ORDER` 通过 `related_checkin_id` 关联 `CHECKIN_RECORD`。这里的关系是可选的，因为不是所有登记单都会立即生成环检单。
-   **服务经理 (SERVICE_MANAGER):** 主要职责是查看数据，与具体登记单无直接创建/编辑关系，通过系统权限进行数据访问。

## 3. 补充说明

-   **逻辑删除：** `到店登记单` 采用逻辑删除，`is_deleted` 字段标识记录是否有效，页面不显示 `is_deleted = true` 的记录。不记录删除操作日志。
-   **编辑不记录变更：** `到店登记单` 的编辑操作不记录详细的历史变更信息。
-   **车龄计算：** 车辆信息中的车龄为计算字段，基于 `当前时间 - 交车时间`。 