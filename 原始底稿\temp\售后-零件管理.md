**售后业务流程：零件管理阶段详细分析输出 (最终整合版)**

售后零件管理是确保经销商门店维修服务顺利进行的关键后台流程。它涵盖了零件从计划、采购、入库到最终出库领用的全生命周期管理，旨在优化库存，提高效率，并支持维修工单的及时完成。

## **1. 用户旅程：不同角色参与的业务动作**

*   **采购人员 (Purchaser):**
    1.  接收**系统以门店为单位在消息通知中心发出的**低于安全库存的提示，或根据库存水平、预测需求及自身经验，制定零件叫料计划。
    2.  登录 DMS 系统，进入零件叫料界面。
    3.  搜索并选择需要采购的零件，批量添加到叫料清单。
    4.  为清单中的每个零件指定大于0的正整数采购数量和期望到货日期。
    5.  提交叫料单，系统触发内部审核流程，叫料单进入待审核状态。
    6.  在系统中跟踪叫料单的状态（待审核、已提交ERP、已发货、已到货）。
    7.  查看采购订单详情和预计到货时间（通过DMS查询或接收ERP推送）。
    8.  处理与采购相关的异常或差异通知，具体场景包括：
        8.1. 叫料单提交失败（数据格式错误、网络异常、必填项缺失）。
        8.2. ERP 订单生成失败。
        8.3. 叫料单内部审核不通过。
    9.  查看和导出历史采购记录报表进行分析。

*   **仓库管理员 (Warehouse Manager):**
    1.  接收来自 ERP 或物流的到货通知（包含发货单号）。
    2.  在 DMS 系统中输入到货单号，获取对应的发货清单信息（通过接口查询ERP）。
    3.  对实际到货零件进行清点和核对，与系统中的发货清单进行比对。
    4.  记录实际收货数量和存在的差异（如少发、多发、零件损坏）并提交系统。
    5.  在 DMS 系统中确认完成入库操作（PS_03接口），系统自动更新库存。
    6.  根据系统记录更新零件库存，确保账实一致。
    7.  处理入库过程中产生的差异单或异常记录（如到货单号无效、差异处理流程、质量问题处理）。
    8.  监控系统中的库存预警信息，及时通知采购人员或相关部门。
    9.  执行定期或不定期的库存盘点工作，并根据盘点结果更新系统库存。

*   **门店技师 (Store Technician):**
    1.  在创建维修工单时，根据维修项目选择并关联所需的零件清单。
    2.  查看系统显示的零件实时库存状态，确认所需零件是否充足（PS_04接口）。
    3.  提交工单进行开立，系统自动进行零件库存检查和扣减（PS_05接口）。
    4.  如遇库存不足，接收系统提示，无法成功开立工单，并生成缺货记录。
    5.  从仓库领用工单所需的零件，并在系统中记录领用情况。
    6.  维修过程中未使用的零件退回仓库时，通过退料流程更新库存并关联回原工单或库存调整单。

*   **ERP系统 (External System):**
    1.  接收来自 DMS 系统的叫料单（通过ERP_01接口），在ERP生成正式采购订单。
    2.  根据订单安排发货，生成发货单。
    3.  将生成的发货单信息（包含对应ERP订单号/DMS叫料单号、零件明细、数量、发货日期等）推送给 DMS 系统（通过ERP_03接口）。
    4.  接收 DMS 系统同步的入库差异信息（通过ERP_04接口），处理库存差异（可能需要人工介入进行索赔、补发等）。
    5.  提供零件主数据查询接口（供 DMS 调用获取最新零件信息）。
    6.  接收 DMS 系统同步的盘点差异信息（通过ERP_04接口，如果需要双边同步）。

## **2. 系统功能点 (总结DMS能力)**

| 功能模块         | 具体功能点                                                                      | 描述                                                                 |
| :--------------- | :------------------------------------------------------------------------------ | :------------------------------------------------------------------- |
| **零件主数据管理** | 1. 零件基础信息查询<br>2. 零件价格管理                                                                    | 1. 提供按编号、名称、规格等查询零件详细信息的功能<br>2. 管理零件的成本价、销售价等信息                                       |
| **采购管理**     | 1. 叫料单创建<br>2. 叫料单提交 (对接ERP)<br>3. 采购单状态跟踪<br>4. 预计到货日期跟踪<br>5. 采购历史查询                                                                  | 1. 支持采购人员在线创建零件叫料单<br>2. 将审核**通过**的叫料单信息通过接口发送给 ERP 系统<br>3. 显示叫料单在整个采购链路中的状态（如：**待审核、已提交ERP、已发货、已到货**）<br>4. 记录和跟踪供应商/ERP提供的预计到货日期<br>5. 提供已完成或取消的采购单查询功能                                     |
| **入库管理**     | 1. 到货单信息获取<br>2. 入库验收记录<br>3. 入库确认与库存更新<br>4. 入库差异管理                                                              | 1. 根据到货单号从 ERP 系统获取发货清单信息<br>2. 支持仓库管理员记录实际收货数量和与发货清单的差异<br>3. 确认入库操作，自动更新 DMS 系统中的零件库存<br>4. 记录、查询和处理入库环节产生的差异信息                               |
| **库存管理**     | 1. 实时库存查询<br>2. 库存预警设置与通知<br>3. 库存盘点支持<br>4. 库存调整管理                                                                | 1. 提供按零件编号、仓库位置等查询当前实时库存数量<br>2. 根据设定的安全库存量进行预警，并通过PC端DMS系统的消息通知中心（小铃铛图标）触发补货提醒，**小铃铛上会显示消息数量提示，点击可查看消息内容**；在库存报表中，低于安全库存的零件，其名称、零件号和数量会显示为红色，以便快速识别。<br>3. 提供支持库存盘点的数据核对和调整功能<br>4. 支持对库存数量进行手动调整，并记录原因和操作日志                               |
| **出库管理**     | 1. 工单关联零件清单<br>2. 库存自动扣减<br>3. 缺货提示与处理<br>4. 零件领用记录<br>5. 退料处理                                                            | 1. 支持在创建维修工单时关联所需的零件列表<br>2. 工单开立时，系统自动从库存中扣减相应零件数量<br>3. 库存不足时禁止开单，并生成缺货记录或补货建议<br>4. 记录零件被领用出库的信息，关联至具体工单<br>5. 支持零件退回仓库时的处理流程和库存更新                               |
| **报表分析**     | 1. 采购报表<br>2. 入库报表<br>3. 库存报表<br>4. 出库报表                                                                        | 1. 提供采购相关的统计报表，主要指标包括**采购量**。报表字段可能包含：<br>- 零件编号、名称<br>- 叫料单号、采购订单号<br>- 供应商名称<br>- 叫料日期、发货日期、实际到货日期<br>- 叫料数量、发货数量、实际入库数量<br>- 差异数量和类型<br>- 单价、总价<br>- 叫料单状态<br>2. 提供入库相关的统计报表（如入库量、差异率）<br>3. 提供库存相关的统计报表（如预警处理及时率）<br>4. 提供出库相关的统计报表（如零件使用量、缺货率、退料率）                       |

## **3. 业务功能点 (总结参与方行为)**

| 角色             | 业务动作 / 目标                                                                                                                               |
| :--------------- | :-------------------------------------------------------------------------------------------------------------------------------------------- |
| **采购人员**     | 1. 完成零件需求分析和叫料操作。<br>2. 跟踪采购单直至零件到货并入库。<br>3. 处理采购过程中的各类异常（叫料提交、订单生成、发货延迟等）。<br>4. 分析采购数据优化采购策略和库存水平。                                           |
| **仓库管理员**   | 1. 接收并准确核对到货零件。<br>2. 准确、及时完成零件的入库操作。<br>3. 确保系统库存与实物一致，并处理盘点差异。<br>4. 处理入库差异和质量问题，发起相关后续流程。<br>5. 监控库存预警，及时通知相关人员进行补货。<br>6. 完成零件的出库（领用）和退库操作。                                  |
| **门店技师**     | 1. 准确选择维修工单所需零件。<br>2. 在创建工单前确认零件库存是否满足维修需求。<br>3. 完成工单所需的零件领用和使用。<br>4. 准确记录零件使用情况或退库情况，协助库存准确。<br>5. 遇到缺货时，了解缺货信息并配合处理（等待、替代）。                                 |
| **系统 (DMS)** | 1. 提供零件基础信息管理和查询功能。<br>2. 支持叫料单的创建、提交和全生命周期跟踪。<br>3. 处理 ERP 推送的发货信息，支持入库操作和差异记录。<br>4. 实时管理库存，支持库存查询、预警设置与通知、盘点和调整。<br>5. 支持工单关联零件时的库存检查、自动扣减和缺货处理。<br>6. 记录完整的零件出入库、领用、退库日志。<br>7. 提供多维度报表支持业务分析。 |
| **ERP 系统**     | 1. 接收并处理来自 DMS 的采购订单。<br>2. 生成、管理并推送发货单给 DMS。<br>3. 与 DMS 协同处理采购、入库、盘点环节的库存差异。<br>4. 提供零件主数据同步或查询服务。                                                             |

## **4. 交互时序 (零件管理核心流程)**

此图涵盖了叫料、入库和出库三个核心流程的主要系统交互。

```mermaid
sequenceDiagram
    participant P as 采购人员
    participant W as 仓库管理员
    participant T as 门店技师
    participant DMS as DMS系统
    participant ERP as ERP系统

    %% 采购流程
    P->>DMS: 1. 登录并创建叫料单 (选择零件, 填数量/日期)
    DMS->>DMS: 2. 验证叫料单数据
    DMS->>DMS: 3. 触发内部审核流程 (叫料单状态: 待审核)
    opt DMS审核通过
        DMS->>ERP: 4. 提交采购订单 (审核通过后推送, PS_01 / ERP_01接口)
        ERP->>ERP: 5. 生成ERP内部订单
        ERP-->>DMS: 6. 返回ERP订单号和状态
        DMS->>DMS: 7. 更新叫料单状态, 关联ERP订单号 (叫料单状态: 已提交ERP)
        DMS-->>P: 8. 显示叫料成功通知
    else DMS审核不通过
        DMS->>DMS: 4. 更新叫料单状态 (叫料单状态: 审核不通过)
        DMS-->>P: 5. 通知采购人员审核结果
    end

    Note over DMS,ERP: ERP根据订单安排发货，并推送发货单给DMS (通过ERP_03接口)
    DMS->>DMS: 9. 接收并存储发货单信息，生成待入库记录

    %% 入库流程
    W->>DMS: 10. 登录并查询待入库清单 (通过发货单号)
    DMS->>DMS: 11. 显示发货清单
    W->>DMS: 12. 核对实收数量，记录差异
    W->>DMS: 13. 确认入库 (PS_03接口)
    DMS->>DMS: 14. 更新库存数量
    DMS->>DMS: 15. 生成入库单
    opt 存在差异
        DMS->>DMS: 16. 生成差异记录/单据
        DMS->>ERP: 17. 同步差异信息 (如果需要，ERP_04接口)
    end
    DMS-->>W: 18. 提示入库完成

    %% 出库流程 (工单用料)
    T->>DMS: 19. 创建维修工单，关联所需零件
    DMS->>DMS: 20. 检查零件库存 (PS_04接口)
    alt 库存充足
        DMS->>DMS: 21. 自动扣减库存
        DMS->>DMS: 22. 生成出库记录 (PS_05接口)
        DMS-->>T: 23. 提示工单创建成功
    else 库存不足
        DMS-->>T: 21. 提示库存不足，工单创建失败
        DMS->>DMS: 22. 生成缺货记录/补货建议
        Note over DMS: 可能触发采购提醒给采购人员
    end

    Note over DMS: 采购人员可通过DMS查询采购单状态 (PS_01 / ERP_02接口)
    Note over W: 仓库管理员可通过DMS查询库存 (PS_04接口)
    Note over T: 技师领用零件时，DMS需记录领用明细 (PS_05接口)
```

---

**DMS 系统相关接口清单**

以下表格总结了在售后零件管理流程中，DMS 系统对内或对外提供的关键接口：

| 接口编号   | 接口名称             | 调用方/提供方 | 业务描述                                         | 逻辑描述（输入/输出）                                                                                                   |
| :--------- | :------------------- | :------------ | :----------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------- |
| PS_01      | `创建叫料单`         | DMS内部/前端  | 采购人员在DMS中创建并提交叫料单                  | 1. **输入:** 叫料单详情 (零件清单含编号、数量、期望日期、备注、内部审核信息等) <br>2. **输出:** 叫料单号，提交状态，可能的错误信息                                  |
| PS_02      | `查询零件信息`       | 前端/DMS内部  | 采购人员搜索和选择零件，或DMS内部关联零件时获取基础信息             | 1. **输入:** 搜索关键词 (编号/名称/规格/品牌)，过滤条件 (如是否在售)<br>2. **输出:** 符合条件的零件列表 (含基础信息，如编号、名称、规格、单位、当前库存概要、成本价/销售价 - 需权限控制)                            |
| PS_03      | `入库确认`           | 前端          | 仓库管理员在完成核对后确认入库操作，系统更新库存               | 1. **输入:** 到货单号，实际收货零件清单 (含零件编号、实收数量、批次/库位信息 - 如果需要跟踪)，差异记录 (差异类型、数量、原因、图片等)<br>2. **输出:** 入库处理结果 (成功/失败)，更新后的库存状态概要，生成的入库单号，可能的差异单号                                |
| PS_04      | `库存查询`           | 前端/DMS内部  | 查询零件当前实时可用库存及详细库存信息                             | 1. **输入:** 零件编号列表，仓库位置(可选)<br>2. **输出:** 零件库存列表 (含零件编号、总库存、可用库存、占用库存、锁定库存、库存状态，可能包含库位、批次信息)                                  |
| PS_05      | `出库确认/工单扣减/领用记录`  | DMS内部/前端  | 工单开立时自动扣减库存，或技师手动领用零件时记录出库               | 1. **输入:** 工单号，出库/领用零件清单 (含零件编号、数量)，操作人员<br>2. **输出:** 出库处理结果 (成功/失败)，剩余库存信息，生成的出库/领用记录单号，可能的错误信息 (如库存不足)                                            |
| ERP_01     | `提交采购订单`       | DMS系统 -> ERP | DMS将审核通过的叫料单信息推送给ERP，在ERP生成正式采购订单       | 1. **输入:** 采购订单信息 (源自DMS叫料单，含供应商信息、零件清单、数量、期望到货日期、DMS叫料单号等)<br>2. **输出:** ERP订单号，处理结果 (成功/失败)，ERP系统返回的确认信息                                                     |
| ERP_02     | `查询采购订单状态 (from ERP)` | DMS系统 -> ERP | DMS定期或根据事件触发向ERP查询特定采购订单的最新状态和发货信息     | 1. **输入:** ERP订单号列表或DMS叫料单号列表<br>2. **输出:** 采购订单状态 (如待发货、已发货、已完成、已取消)，关联的发货单号列表，已发货零件明细及数量，预计到货日期                                             |
| ERP_03     | `接收发货单推送`     | ERP系统 -> DMS | ERP在生成发货单后，主动将发货单详情推送给DMS系统进行入库准备        | 1. **输入:** 发货单详情 (含发货单号、对应ERP订单号/DMS叫料单号，发货零件明细及数量，发货日期，物流信息等)<br>2. **输出:** DMS系统接收确认（如HTTP状态码 200，DMS内部处理状态） |
| ERP_04     | `同步库存差异 (to ERP)` | DMS系统 -> ERP | DMS将入库环节产生的差异信息或盘点调整信息同步给ERP (如果业务流程需要两边库存或财务一致) | 1. **输入:** 差异详情 (关联的入库单/盘点单号、涉及零件、差异类型、数量、原因、责任方判断等)<br>2. **输出:** ERP系统接收确认及处理结果                                                        |
| DMS_NOTIFY | `库存预警通知`       | DMS系统 -> 门店人员 (PC端DMS系统) | DMS在库存低于安全库存时，向**以门店为单位的**仓库管理员或采购人员发送通知，**通知将通过PC端DMS系统的消息通知中心（小铃铛图标）送达，小铃铛上会显示消息数量提示，点击可查看消息内容**。 | 1. **输入:** 通知内容 (预警类型 - 如低库存，涉及零件清单，当前库存数量)，通知接收人/群组<br>2. **输出:** 通知发送结果 (成功/失败)，发送时间                                            |

## **5. 业务规则说明 - 零件管理**

1.  **采购叫料规则**
    1.1. 叫料操作必须通过 DMS 系统进行。
    1.2. 叫料单中的每个零件必须指定**大于0的正整数**采购数量。
    1.3. 叫料单必须填写**期望到货日期**，用于指导后续的物流和入库计划。
    1.4. 提交叫料单前，系统需对所有必填项和数据格式进行严格校验。
    1.5. 叫料单提交后，**系统触发内部审核流程，叫料单状态变为待审核**。
    1.6. 叫料单一旦成功提交至 ERP 并生成正式订单，原则上不允许在 DMS 端随意修改或取消，任何变更需遵循特定的**变更或取消流程**（可能涉及ERP协同）。
    1.7. 叫料单应自动关联到叫料的**采购人员**和提交时间。
    1.8. 支持按供应商或品牌进行叫料单的创建和管理。

2.  **入库验收规则**
    2.1. 收货时必须有对应的**发货单信息**可供核对，通常通过到货单号从 ERP 系统获取发货清单。
    2.2. 仓库管理员需**如实核对**实际到货的零件型号、数量和质量，并与系统中的发货清单进行比对。
    2.3. 对于存在的**数量或质量差异**，必须在系统中**详细记录**差异类型、数量、原因，并上传必要的证明图片（如损坏照片）。
    2.4. 入库确认操作（PS_03接口）将**触发系统库存的实时更新**，增加相应零件的可用库存。
    2.5. 差异信息（ERP_04接口）需要**同步**给相关系统（如 ERP）进行后续处理（如供应商索赔、财务调整、补发流程）。
    2.6. 入库操作需记录详细的**操作日志**，包括操作人员、操作时间、涉及的单据（发货单、入库单）、零件和数量。
    2.7. 对于部分高值或重要零件，入库时可能需要记录**批次号或序列号**。
    2.8. 支持按照**库位**进行入库操作，更新零件所在的存放位置信息。

3.  **库存管理规则**
    3.1. DMS 系统需**实时、准确**反映零件的可用库存数量。
    3.2. 库存数量低于安全库存时，系统应自动触发**低库存预警**，并通过**PC端DMS系统的消息通知中心（小铃铛图标）**通知给**以门店为单位的**仓库管理员和采购人员；**小铃铛上会显示消息数量提示，点击可查看消息内容**；在库存报表中，低于安全库存的零件，其名称、零件号和数量会显示为红色，以便快速识别。
    3.3. 工单创建时进行的库存检查和扣减必须基于**可用库存**（总库存减去占用、锁定等）。
    3.4. 定期进行**物理盘点**，盘点结果用于调整系统库存，确保账实相符，盘点差异需记录原因。
    3.5. 库存调整（如盘盈盘亏、报损）需要通过**库存调整功能**进行，并记录详细的操作日志和调整原因。
    3.6. 系统应能记录零件的**批次信息和库位信息**（如果启用）。
    3.7. 支持设置不同零件的安全库存。

4.  **出库（工单用料）规则**
    4.1. 只有当维修工单关联了所需零件且系统检查到**可用库存充足**的情况下，工单才能**成功开立**并自动**锁定或扣减**相应库存（PS_05接口）。
    4.2. 如遇**库存不足**，系统应**禁止工单开立**，并向技师或服务顾问提示具体缺货零件信息。
    4.3. 系统应自动生成**缺货零件清单**，供采购人员参考进行补货或紧急叫料。
    4.4. 零件领用出库记录必须**关联至具体维修工单、车辆和领用技师**。
    4.5. 维修过程中未使用的零件退回仓库时，需通过**退料流程**进行，退料成功后**更新库存**，并关联回原工单或生成退料单据。

## **6. 异常处理流程 - 零件管理**

1.  **采购环节异常**
    1.1. **叫料单提交失败**：系统应向采购人员提示明确的错误信息（如数据格式错误、网络异常、必填项缺失），允许用户修正后重试提交。
    1.2. **ERP 订单生成失败**：DMS 接收到 ERP 返回的失败通知后，更新对应叫料单的状态为"ERP生成失败"，并通知采购人员，可能需要人工排查 ERP 端原因或根据错误信息修正叫料单后重新提交。
    1.3. **叫料单审核不通过**：如果启用了内部审核流程，审核不通过时系统应反馈原因给提交人，允许修改后再次提交或作废。

2.  **入库环节异常**
    2.1. **到货单号无效/查询失败**：提示仓库管理员到货单号错误或系统与ERP接口通信异常，无法获取发货清单，需核实单号或联系系统管理员检查接口状态。
    2.2. **实际到货与清单数量差异**：仓库管理员在系统中详细记录差异，系统生成**差异单**。差异单需进入专门的**差异处理流程**，可能涉及责任判定（供应商、物流）、发起索赔、补发货物或进行财务调整。差异处理流程的状态需可在系统中跟踪。
    2.3. **零件损坏/质量问题**：记录问题详情（含图片、描述），系统生成**质量异常单**。质量异常单进入**质量处理流程**，可能涉及退货、换货、报损等操作，并相应调整库存。
    2.4. **无法完成入库确认**：如遇系统异常、数据校验不通过（如关联的差异单未处理完成），系统应提示具体原因，需排除故障或先处理前置问题后重试入库确认。
    2.5. **无法按指定库位入库**：提示仓库管理员该库位不存在或不可用，需选择其他有效库位。

3.  **库存管理异常**
    3.1. DMS 系统需**实时、准确**反映零件的可用库存数量。
    3.2. 库存数量**低于预设的最低库存阈值**时，系统应自动触发**低库存预警**通知给仓库管理员和采购人员。
    3.3. 工单创建时进行的库存检查和扣减必须基于**可用库存**（总库存减去占用、锁定等）。
    3.4. 定期进行**物理盘点**，盘点结果用于调整系统库存，确保账实相符，盘点差异需记录原因。
    3.5. 库存调整（如盘盈盘亏、报损）需要通过**库存调整功能**进行，并记录详细的操作日志和调整原因。
    3.6. 系统应能记录零件的**批次信息和库位信息**（如果启用）。
    3.7. 支持设置不同零件的**最低库存和最高库存**阈值。

4.  **出库环节异常**
    4.1. 只有当维修工单关联了所需零件且系统检查到**可用库存充足**的情况下，工单才能**成功开立**并自动**锁定或扣减**相应库存（PS_05接口）。
    4.2. 如遇**库存不足**，系统应**禁止工单开立**，并向技师或服务顾问提示具体缺货零件信息。
    4.3. 系统应自动生成**缺货零件清单**，供采购人员参考进行补货或紧急叫料。
    4.4. 零件领用出库记录必须**关联至具体维修工单、车辆和领用技师**。
    4.5. 维修过程中未使用的零件退回仓库时，需通过**退料流程**进行，退料成功后**更新库存**，并关联回原工单或生成退料单据。

## **7. 非功能需求 - 零件管理**

1.  **性能需求**
    1.1. 零件基础信息查询、叫料单创建、入库确认等核心操作的响应时间应稳定在 ≤ 3 秒以内。
    1.2. 涉及批量数据处理的操作，如发货单接收解析、盘点数据导入，其处理时间应控制在 ≤ 5 秒以内。
    1.3. 系统应支持至少 50 个并发用户同时进行零件管理相关操作，并保持稳定性能。
    1.4. 零件实时库存查询接口（供前端和内部调用）的响应时间应 ≤ 1 秒，确保库存信息的及时性。
    1.5. 报表生成时间：对于常用报表，应在数秒内生成；对于复杂报表，生成时间不应超过 30 秒。

2.  **安全需求**
    2.1. 所有零件管理相关的核心操作（包括但不限于叫料、入库、出库、库存调整、盘点）必须进行严格的**权限控制**，确保只有授权人员才能执行相应操作。
    2.2. 所有关键操作必须记录详细的**操作日志**，日志内容应包含操作人、操作时间、操作类型、涉及的零件、单据编号以及关键数据变更等信息，日志应可追溯和审计。
    2.3. 敏感数据，如零件的成本价、供应商信息等，在系统中需进行适当的**保护和脱敏处理**，仅对特定权限人员可见。
    2.4. 系统必须具备完善的**数据备份和恢复机制**，定期对零件管理相关数据进行备份，并能快速有效地进行数据恢复，确保数据安全和业务连续性。
    2.5. 系统应防止未经授权的访问和恶意的数据篡改。

3.  **可用性需求**
    3.1. 零件管理模块的核心功能（叫料、入库、库存查询、出库）的可用性目标应 ≥ 99.9%。
    3.2. 系统应支持 7*24 小时稳定运行，满足经销商门店随时进行零件管理操作的需求。
    3.3. 提供**清晰、直观、易于操作的用户界面**，简化操作流程，降低用户学习成本。
    3.4. 对于关键或复杂的操作（如入库验收、盘点），应提供**必要的操作指引、帮助文档或系统提示**。
    3.5. 系统在发生异常时，应给出**明确的错误提示**，指导用户进行下一步操作或联系支持。

4.  **可维护性需求**
    4.1. 系统设计应采用**模块化、高内聚、低耦合的架构**，便于功能的扩展和维护。
    4.2. 与外部系统（特别是 ERP）的接口应遵循**标准化、易于理解和维护的接口规范**，提供清晰的接口文档。
    4.3. 提供**详细的技术设计文档、代码注释和部署文档**，便于开发人员理解和维护系统。
    4.4. 系统应具备**规范、全面、可配置的日志记录机制**，便于开发和运维人员进行问题排查和系统监控。
    4.5. 数据库结构设计应合理，便于数据查询和维护。

5.  **可扩展性需求**
    5.1. 系统架构应具备良好的可扩展性，能够支持未来业务量的增长和新功能的加入。
    5.2. 考虑支持未来增加新的零件管理场景（如零件调拨、寄售等）。
    5.3. 接口设计应具备一定的灵活性，便于对接新的外部系统或服务。

6.  **兼容性需求**
    6.1. 系统应兼容主流的浏览器和操作系统。
    6.2. 与 ERP 系统或其他外部系统对接时，需确保数据格式和协议的兼容性。

# DMS系统页面描述规范

## 一、页面基本信息

```
【页面类型】：列表页/弹窗
【页面标题】：零件管理
【页面描述】：管理门店零件的叫料、采购、到货和库存状态，并支持新建和查看叫料清单。
```

## 二、表单元素描述

【表单分组】：查询项

【表单项】
- 字段名称：零件名称
  - 控件类型：可输入筛选下拉框
- 字段名称：零件编号
  - 控件类型：可输入筛选下拉框
- 字段名称：叫料单号
  - 控件类型：输入框
  - 校验要求：全模糊查询
- 字段名称：采购单号
  - 控件类型：输入框
  - 校验要求：全模糊查询
- 字段名称：供应商名称
  - 控件类型：可输入筛选下拉框
- 字段名称：叫料日期区间
  - 控件类型：双日历组件
  - 页面描述：分别选择所选区间的起始时间和结束时间。（可筛选出一段时间内的所有叫料单，如果需要仅筛选出当天叫料单，日历中选择的起始和结束时间可点击同一天）
- 字段名称：到货日期区间
  - 控件类型：双日历组件
  - 页面描述：分别选择所选区间的起始时间和结束时间。（可筛选出一段时间内的所有叫料单，如果需要仅筛选出当天到货单，日历中选择的起始和结束时间可点击同一天）
- 字段名称：叫料单状态
  - 控件类型：下拉框
  - 选项值：已提交、通过、驳回、已发货、已收货、已作废
- 字段名称：库存状态
  - 控件类型：下拉框
  - 选项值：正常、低于安全库存

## 三、功能区按钮描述

【功能按钮】
- 按钮名称：查询
  - 按钮重要性：主要按钮
  - 点击行为：点击按钮，可对查询项中的输入或填写的内容进行查询
- 按钮名称：重置
  - 按钮重要性：次要按钮
  - 点击行为：点击按钮，可清空全部查询项
- 按钮名称：新建叫料清单
  - 按钮重要性：主要按钮
  - 点击行为：点击后右边出现抽屉页，页面可以添加多条零件种类和数量
- 按钮名称：导出报表
  - 按钮重要性：次要按钮
  - 点击行为：对查询结果进行导出，导出为Excel文件格式且字段与列表页面一致

## 四、列表描述

【列表筛选区】
- 描述同"查询项"的表单项描述

【列表列定义】
- 列名：勾选框
  - 特殊样式：勾选数据前的勾选框，进而可进行操作栏中的操作
- 列名：序号
  - 格式化：对叫料清单按叫料日期倒序进行排列
- 列名：叫料单号
  - 页面描述：在新建叫料清单后，DMS系统自动生成的叫料单号
- 列名：采购单号
  - 页面描述：ERP接收到审核通过的叫料单后，生成采购订单并传给DMS。如未接收到来自ERP的采购订单，此字段显示为空
- 列名：叫料日期
  - 页面描述：以新建叫料清单日期为叫料日期（如过对单据有修改操作，更新保存修改的日期为叫料日期）
- 列名：叫料单状态
  - 页面描述：状态来源于ERP，视为叫料的零件已发货

【列表状态标签】
- 叫料单状态=已提交：普通标签，完成新建叫料清单
- 叫料单状态=通过：成功标签，HQ控制审核按钮，判定此叫料单审核通过
- 叫料单状态=驳回：危险标签，HQ控制审核按钮，判定此叫料单需要被驳回
- 叫料单状态=已发货：普通标签，状态来源于ERP，视为叫料的零件已发货
- 叫料单状态=已收货：成功标签，门店人员在系统中确认收货
- 叫料单状态=已作废：危险标签，叫料单已作废，终止后续全部流程

【操作列】
- 按钮名称：详情
  - 点击行为：点击详情，会在侧面弹出抽屉页，可查看叫料单明细
- 按钮名称：修改
  - 点击行为：勾选数据，按钮亮起，否则保持置灰。点击后会跳转新建叫料清单页面，页面自动带出之前填写的内容。（仅针单据在已提交的状态可进行修改操作）除门店名称外，所有字段均可以修改
- 按钮名称：作废
  - 点击行为：勾选数据，按钮亮起，否则保持置灰。点击后叫料单状态改为已作废（仅在单据状态为已提交的状态可以进行作废操作）

## 五、交互行为描述

【弹窗描述】
- 弹窗标题：新建叫料清单
- 弹窗大小：中
- 弹窗内容：
  【表单项】
  - *字段名称：零件名称
    - 控件类型：可输入筛选下拉框
  - *字段名称：零件号
    - 控件类型：可输入筛选下拉框
    - 联动规则：如果先填写零件名称，零件号会被自动带出
  - *字段名称：数量
    - 控件类型：文本框
  - 字段名称：门店名称
    - 控件类型：文本展示
    - 页面描述：系统自动显示此门店的名称且不可更改
  - *字段名称：期望到货日期
    - 控件类型：日期选择

  【功能按钮】
  - 按钮名称：Add
    - 按钮重要性：主要按钮
    - 点击行为：当输入全部必填字段后点击"Add"按钮，列表页面中会按所填写的字段内容新增一条数据，且列表页面中可以有多条数据
  - 按钮名称：重置
    - 按钮重要性：次要按钮
    - 点击行为：点击重置按钮，可以清空全部筛选框
  - 按钮名称：删除
    - 按钮重要性：危险按钮
    - 点击行为：只有勾选数据后此按钮才会亮起，否则保持置灰状态。如果勾选某条数据后，可点击删除按钮删除此条数据
  - 按钮名称：提交
    - 按钮重要性：主要按钮
    - 点击行为：点击提交按钮，视为完成了一次新建叫料清单，系统自动生成叫料清单号，如果提交成功的话页面中间会出现"提交成功"提示框且保持2s
  - 按钮名称：取消
    - 按钮重要性：次要按钮
    - 点击行为：点击取消按钮，即关闭弹窗（如果页面有填写的内容会触发弹窗提示：关闭后内容将不会被保存，请再次确认是否关闭弹窗？如果页面没有填写的内容可以直接关闭

  【列表列定义】
  - 列名：勾选框
  - 列名：序号
    - 格式化：按增加的数据先后顺序自动正序排
  - 列名：零件名称
  - 列名：零件号
  - 列名：数量
  - 列名：期望到货日期

【弹窗描述】
- 弹窗标题：叫料单详情
- 弹窗大小：中
- 弹窗内容：
  【表单项】
  - 字段名称：叫料单号
    - 控件类型：文本展示
  - 字段名称：采购单号
    - 控件类型：文本展示
  - 字段名称：叫料日期
    - 控件类型：文本展示

  【列表列定义】
  - 列名：序号
  - 列名：零件名称
  - 列名：零件号
  - 列名：数量
  - 列名：单位
  - 列名：叫料状态
  - 列名：叫料日期
  - 列名：预计到货时间

## **8. 零件档案管理**

### 8.1 功能描述
零件档案管理模块用于维护和管理零件的基础信息，作为整个零件管理系统的数据基础。

### 8.2 查询功能
- **零件名称**：支持按零件名称进行查询
- **零件编码**：支持按零件编码进行查询
- **供应商名称**：支持按供应商名称进行查询

### 8.3 列表展示
- **零件名称**：来自ERP系统
- **零件编码**：来自ERP系统
- **单位**：来自ERP系统
- **供应商名称**：来自ERP系统
- **采购单价**：来自ERP系统

### 8.4 功能按钮
- **查询**：执行查询操作
- **重置**：清空查询条件

## **9. 零件配置管理**

### 9.1 功能描述
零件配置管理模块用于设置和管理零件的安全库存等配置信息。

### 9.2 查询功能
- **零件名称**：支持按零件名称进行查询
- **零件编码**：支持按零件编码进行查询
- **供应商名称**：支持按供应商名称进行查询

### 9.3 列表展示
- **零件名称**：来自零件档案
- **零件编码**：来自零件档案
- **单位**：来自零件档案
- **供应商名称**：来自零件档案
- **安全库存**：来自零件配置

### 9.4 功能按钮
- **查询**：执行查询操作
- **重置**：清空查询条件
- **配置**：打开零件配置模态框

### 9.5 配置模态框
- **字段**：
  - 零件名称（只读）
  - 零件编号（只读）
  - 单位（只读）
  - 安全库存（可编辑）
- **按钮**：
  - 确定：保存配置
  - 取消：关闭模态框

## **10. 库存报表管理**

### 10.1 店端库存报表

#### 10.1.1 查询功能
- **零件名称**：可输入筛选下拉框
- **零件编号**：可输入筛选下拉框
- **供应商名称**：可输入筛选下拉框
- **库存状态**：下拉框（正常、低于安全库存）

#### 10.1.2 列表展示
- **序号**：按照库存总数变动的时间默认倒序排列
- **零件名称**：来自零件档案
- **零件编号**：来自零件档案
- **供应商名称**：来自零件档案
- **库存总数**：收货时所确认的收货数量的累积
- **可用库存数**：可用库存数=库存总数-锁定库存数-残次品数
- **锁定库存数**：工单开出同时，工单内所需零件的库存被锁定
- **残次品数**：来源1: 收货时直接被判定为残次品；来源2: 在使用中被退料并被标记为残次品
- **待收数**：当叫料单状态变为已发货，叫料单内包含的零件相应的叫料数量在此页面中以待收数来展示
- **安全库存数**：来源零件配置页面中配置
- **库存状态**：默认显示正常，当库存总数低于安全库存数时，显示低于安全库存

### 10.2 HQ端库存报表

#### 10.2.1 查询功能
- **零件名称**：可输入筛选下拉框
- **零件编号**：可输入筛选下拉框
- **供应商名称**：可输入筛选下拉框
- **门店名称**：可输入筛选下拉框
- **库存状态**：下拉框（正常、低于安全库存）

#### 10.2.2 列表展示
- **序号**：按照库存总数变动的时间默认倒序排列
- **门店名称**：来自经销商档案
- **零件名称**：来自零件档案
- **零件编号**：来自零件档案
- **供应商名称**：来自零件档案
- **库存总数**：收货时所确认的收货数量的累积
- **可用库存数**：可用库存数=库存总数-锁定库存数-残次品数
- **锁定库存数**：工单开出同时，工单内所需零件的库存被锁定
- **残次品数**：来源1: 收货时直接被判定为残次品；来源2: 在使用中被退料并被标记为残次品
- **待收数**：当叫料单状态变为已发货，叫料单内包含的零件相应的叫料数量在此页面中以待收数来展示
- **安全库存数**：来源零件配置页面中配置
- **库存状态**：默认显示正常，当库存总数低于安全库存数时，显示低于安全库存

### 10.3 库存预警机制
1. 当库存数量低于安全库存时，系统自动触发预警
2. 预警信息通过PC端DMS系统的消息通知中心（小铃铛图标）发送
3. 小铃铛上显示消息数量提示，点击可查看消息内容
4. 在库存报表中，低于安全库存的零件，其名称、零件号和数量显示为红色

 