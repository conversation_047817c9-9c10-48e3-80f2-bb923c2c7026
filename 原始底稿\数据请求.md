
### Axios 封装与统一配置

为了更好地管理和维护所有的 API 请求，我们通常会对 Axios 进行一层封装，而不是在每个组件中直接使用 `axios.get()` 或 `axios.post()`。

**封装的目标：**

1.  **统一配置：** 设置基础 URL、超时时间、请求头等。
2.  **请求拦截器：** 在请求发送前做一些处理，例如：
    * 添加 **认证 Token**（这是 DMS 系统中最重要的安全机制）。
    * 显示 **Loading 状态**（例如，显示全局的加载指示器）。
    * 对请求参数进行统一处理。
3.  **响应拦截器：** 在接收到响应后做一些处理，例如：
    * 统一处理后端返回的 **业务错误码** 和 **提示信息**。
    * 处理 **认证过期** 或 **无效 Token** 的情况，引导用户重新登录。
    * 隐藏 Loading 状态。
    * 数据格式的统一处理（例如，后端返回的数据都在 `data` 字段下）。
4.  **错误处理：** 捕获网络错误、超时错误等。
5.  **模块化：** 将不同业务模块的 API 请求分开管理。

**建议的项目结构（`src/api` 目录）：**

```
src/
├── api/
│   ├── index.ts                # Axios 实例的创建、配置、拦截器
│   ├── modules/                # 各个业务模块的 API 接口定义
│   │   ├── auth.ts             # 认证相关 API
│   │   ├── sales.ts            # 销售模块 API (车辆、订单等)
│   │   ├── customer.ts         # 客户模块 API
│   │   └── ...
│   └── types.ts                # API 请求和响应的 TypeScript 类型定义 (可选，也可以放在全局 types 目录)
```

#### **1. `src/api/index.ts` - Axios 实例与拦截器**

这是核心的 Axios 封装文件。

```typescript
// src/api/index.ts
import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse, type AxiosError } from 'axios';
import { ElMessage, ElLoading } from 'element-plus';
import { useUserStore } from '@/stores/user'; // 假设你有一个用户 store 来管理 token
import router from '@/router'; // 引入路由实例

// 定义后端返回的数据结构（泛型 T 为实际业务数据类型）
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 创建 Axios 实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API, // 从环境变量获取基础URL，例如 '/api' 或 'http://your-backend.com/api'
  timeout: 10000, // 请求超时时间 10 秒
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
  },
});

let loadingInstance: ReturnType<typeof ElLoading.service> | null = null; // 用于控制全局 loading

// 请求拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 1. 添加认证 Token
    const userStore = useUserStore();
    if (userStore.token && config.headers) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`; // 假设使用 Bearer Token
    }

    // 2. 显示全局 Loading (可选)
    // 根据具体需求决定哪些请求需要显示 loading
    // 也可以通过 config.headers 来控制，例如 config.headers['X-No-Loading'] = true
    if (!config.headers || !(config.headers as any)['X-No-Loading']) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      });
    }

    return config;
  },
  (error: AxiosError) => {
    // 请求错误处理
    console.error('请求发送失败：', error);
    ElMessage.error('请求发送失败，请检查网络！');
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 隐藏 Loading
    loadingInstance?.close();

    const { code, message, data } = response.data;

    // 根据后端返回的业务状态码进行处理
    if (code === 200) {
      // 业务成功
      return data; // 直接返回业务数据
    } else if (code === 401) {
      // 认证失败或 Token 过期
      ElMessage.error('登录状态已过期，请重新登录！');
      const userStore = useUserStore();
      userStore.clearToken(); // 清除过期的 token
      router.push('/login'); // 跳转到登录页
      return Promise.reject(new Error('认证失败'));
    } else {
      // 其他业务错误
      ElMessage.error(message || '操作失败！');
      return Promise.reject(new Error(message || 'Error'));
    }
  },
  (error: AxiosError) => {
    // 隐藏 Loading
    loadingInstance?.close();

    // HTTP 状态码错误处理
    if (error.response) {
      const { status, data } = error.response;
      let errorMessage = '未知错误';
      switch (status) {
        case 400:
          errorMessage = data.message || '请求参数错误';
          break;
        case 403:
          errorMessage = '无权访问，请联系管理员！';
          break;
        case 404:
          errorMessage = '请求的资源不存在！';
          break;
        case 500:
          errorMessage = '服务器内部错误，请稍后再试！';
          break;
        default:
          errorMessage = data.message || `请求失败，HTTP 状态码：${status}`;
          break;
      }
      ElMessage.error(errorMessage);
    } else if (error.request) {
      // 请求已发出但没有收到响应
      ElMessage.error('服务器没有响应，请检查网络或稍后再试！');
    } else {
      // 在设置请求时发生了一些事情，触发了一个错误
      ElMessage.error('请求配置错误：' + error.message);
    }

    return Promise.reject(error);
  }
);

// 暴露请求方法
const request = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return service.get(url, config);
  },
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return service.post(url, data, config);
  },
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return service.put(url, data, config);
  },
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return service.delete(url, config);
  },
  // 还可以添加其他方法，例如 patch
};

export default request;
```

**环境变量配置 (`.env.development`, `.env.production`)：**

在项目根目录创建 `.env.development` 和 `.env.production` 文件：

```bash
# .env.development
VITE_APP_BASE_API = /api # 开发环境代理地址
```

```bash
# .env.production
VITE_APP_BASE_API = http://your-production-backend.com/api # 生产环境真实后端地址
```

并在 `vite.config.ts` 中配置代理：

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 8080,
    open: true,
    proxy: {
      '/api': { // 当请求以 /api 开头时，代理到目标地址
        target: 'http://localhost:3000', // 你的后端服务地址
        changeOrigin: true, // 改变源，发送请求头中的Host字段
        rewrite: (path) => path.replace(/^\/api/, ''), // 重写路径，去掉 /api
      },
    },
  },
});
```

#### **2. `src/api/modules/sales.ts` - 业务模块 API 接口定义**

将不同业务模块的 API 接口封装成函数，并在函数中定义请求参数和响应数据的 TypeScript 类型。

```typescript
// src/api/modules/sales.ts
import request from '../index'; // 引入封装好的 request 实例

// 定义车辆列表项和详情的类型
export interface VehicleListItem {
  id: string;
  vin: string;
  model: string;
  brand: string;
  color: string;
  price: number;
  status: 'in_stock' | 'sold';
  arrivalTime: string;
  // ... 其他列表字段
}

export interface VehicleDetail extends VehicleListItem {
  productionDate: string;
  engineNum: string;
  transmissionType: string;
  images: string[];
  salesRecords: Array<{
    orderId: string;
    orderNum: string;
    saleDate: string;
    customerName: string;
    salesperson: string;
    salePrice: number;
  }>;
  maintenanceRecords: Array<{
    serviceId: string;
    serviceNum: string;
    serviceDate: string;
    serviceType: string;
    totalCost: number;
  }>;
  customer?: {
    name: string;
    phone: string;
    level: string;
  };
  // ... 更多详情字段
}

// 定义列表请求参数
export interface VehicleListParams {
  model?: string;
  status?: string;
  page: number;
  pageSize: number;
}

// 定义列表响应数据结构
export interface VehicleListResponse {
  list: VehicleListItem[];
  total: number;
}

// 定义新增/更新车辆的请求参数 (可以复用 VehicleDetail 的部分字段)
export interface VehicleFormData {
  id?: string; // 编辑时有ID
  vin: string;
  brand: string;
  model: string;
  color: string;
  price: number;
  status: 'in_stock' | 'sold';
  arrivalTime: string;
  productionDate: string;
  engineNum: string;
  transmissionType: string;
  images: string[];
}


// --- 车辆管理 API ---

/**
 * 获取车辆列表
 * @param params 查询参数
 */
export function getVehicleList(params: VehicleListParams): Promise<VehicleListResponse> {
  return request.get('/vehicles', { params });
}

/**
 * 获取车辆详情
 * @param id 车辆ID
 */
export function getVehicleDetail(id: string): Promise<VehicleDetail> {
  return request.get(`/vehicles/${id}`);
}

/**
 * 新增车辆
 * @param data 车辆数据
 */
export function addVehicle(data: VehicleFormData): Promise<string> { // 返回新增车辆的ID
  return request.post('/vehicles', data);
}

/**
 * 更新车辆
 * @param id 车辆ID
 * @param data 更新数据
 */
export function updateVehicle(id: string, data: VehicleFormData): Promise<null> { // 通常更新操作返回null或成功标识
  return request.put(`/vehicles/${id}`, data);
}

/**
 * 删除车辆
 * @param id 车辆ID
 */
export function deleteVehicle(id: string): Promise<null> {
  return request.delete(`/vehicles/${id}`);
}

/**
 * 批量删除车辆
 * @param ids 车辆ID数组
 */
export function batchDeleteVehicles(ids: string[]): Promise<null> {
    return request.post('/vehicles/batchDelete', { ids }); // 假设批量删除是 POST 请求带 IDs
}

// ... 更多销售模块的 API，例如订单、客户、合同等
```

#### **3. 在组件中使用 API**

在组件中，我们只需要导入对应的 API 函数并调用即可，无需关心 Axios 的具体配置和拦截器逻辑。

```typescript
// views/sales/OrderList.vue (简化示例)
import { ref, onMounted } from 'vue';
import { getVehicleList, deleteVehicle, type VehicleListItem, type VehicleListParams } from '@/api/modules/sales';
import { ElMessage, ElMessageBox } from 'element-plus';

const tableData = ref<VehicleListItem[]>([]);
const loading = ref(false);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const fetchVehicles = async () => {
  loading.value = true;
  try {
    const params: VehicleListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
    };
    const res = await getVehicleList(params); // 直接调用封装的 API 函数
    tableData.value = res.list;
    pagination.total = res.total;
  } catch (error) {
    // 错误已经在拦截器中处理并提示，这里可以选择性地做一些额外处理
    console.error('获取车辆列表失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleDelete = async (row: VehicleListItem) => {
  ElMessageBox.confirm(`确定删除车辆 ${row.model} (${row.vin}) 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteVehicle(row.id); // 调用封装的删除 API
      ElMessage.success('删除成功！');
      fetchVehicles(); // 刷新列表
    } catch (error) {
      // 错误已处理
      console.error('删除失败:', error);
    }
  }).catch(() => {
    ElMessage.info('取消删除');
  });
};

onMounted(() => {
  fetchVehicles();
});
```

### **数据请求的注意事项与最佳实践：**

* **错误处理颗粒度：** 统一的错误处理在 `index.ts` 中完成，但如果某个请求需要特殊的错误处理逻辑（例如，某个请求失败不弹窗，而是显示在页面某个区域），可以在调用时通过 `try...catch` 捕获 Promise.reject 的错误，并进行自定义处理。
* **请求取消：** 对于快速频繁的操作（如搜索框输入），考虑使用 `AbortController` 取消旧的请求，避免不必要的网络负载和数据错乱。
* **并发请求：** 如果需要同时发送多个请求并在所有请求完成后进行处理，可以使用 `Promise.all()`。
* **接口 Mock：** 在后端接口尚未准备好时，可以使用 `mockjs` 或 `msw` (Mock Service Worker) 来模拟 API 响应，让前端可以独立进行开发。
* **数据缓存：** 对于不经常变动且请求量大的数据，可以考虑在前端进行一些简单的内存缓存或配合后端使用 HTTP 缓存策略。
* **用户体验优化：**
    * **Skeleton 骨架屏：** 在数据加载时显示骨架屏而不是简单的 Loading 遮罩，能提供更好的用户感知。
    * **Loading 动画：** 选择 Element Plus 的 Loading 动画效果，或者自定义更符合DMS风格的动画。
    * **空数据状态：** 提供友好的空数据提示，引导用户进行下一步操作。
