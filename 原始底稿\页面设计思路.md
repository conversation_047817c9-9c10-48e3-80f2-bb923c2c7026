
Element Plus 提供了丰富的组件，我们将聚焦于以下几个关键组件：

* **`ElTable` (表格)**：用于列表页的数据展示。
* **`ElForm` / `ElFormItem` (表单)**：用于数据录入和编辑。
* **`ElDialog` (对话框/模态框)**：用于简单的详情或快速编辑。
* **`ElDrawer` (抽屉)**：用于复杂详情或编辑，同时保留列表上下文。
* **其他辅助组件**：如 `ElPagination` (分页), `ElButton` (按钮), `ElInput` (输入框), `ElSelect` (选择器), `ElDatePicker` (日期选择器) 等。

---

### **1. 列表页 (List Page)：数据密集型展示的核心**

DMS 系统中会存在大量的列表页，例如车辆库存列表、客户列表、订单列表、维修工单列表等。

**核心组件：`ElTable`**

**现代化和用户友好的交互要点：**

* **清晰的表格布局：**
    * **列的可见性和顺序定制：** 对于列数较多的表格，提供“列设置”功能，让用户可以自定义显示哪些列以及它们的顺序。Element Plus 的 `ElTableColumn` 可以通过 `v-if` 或动态 `columns` 数组来实现。
    * **固定列：** 对于左右滚动较长的表格，固定住最重要的几列（如编号、名称、操作），提升用户体验。
    * **斑马纹 / 悬浮高亮：** `stripe` 属性（斑马纹）和默认的悬浮高亮能增加可读性。
    * **列宽调整：** 如果需要，可以集成第三方库实现列宽拖拽调整功能。
* **强大的搜索和筛选功能：**
    * **顶部搜索区域：** 在表格上方设计一个简洁的搜索表单，包含常用搜索条件（输入框、选择器、日期范围选择器）。
    * **高级搜索/筛选：** 对于复杂或不常用的搜索条件，可以将其折叠起来，通过“高级搜索”按钮展开。
    * **筛选器集成到表格列头：** 对于某些列（如状态、类型），可以直接在 `ElTableColumn` 中使用 `filters` 属性进行列内筛选，更直观。
    * **实时搜索/防抖：** 对于输入框搜索，使用防抖 (debounce) 处理，避免用户每输入一个字符就发送请求，浪费资源。
* **分页 (`ElPagination`)：**
    * **前端分页 vs 后端分页：** 大多数DMS场景下，数据量大，必须采用**后端分页**。前端只需传递 `currentPage` 和 `pageSize` 给后端，并接收 `total` 和当前页数据。
    * **布局与选项：** 通常包含“总条数”、“每页显示条数选择器”和“页码跳转”功能。
* **批量操作：**
    * **多选 (`type="selection"`)：** 启用表格多选功能，当用户勾选多条数据时，在表格上方或底部显示批量操作按钮（如批量删除、批量导出、批量审批等）。
    * **操作状态显示：** 选中多少条数据，按钮是否可点击等。
* **Loading 状态 (`ElLoading`)：**
    * 在数据加载时显示 `ElLoading` 遮罩，告知用户数据正在加载，提升感知。
    * 可以通过 `ElTable` 的 `v-loading` 指令直接实现。
* **空数据提示：** 当没有数据时，友好地提示“暂无数据”。
* **导出功能：** 常见需求，导出当前列表数据为 Excel 或 CSV。

**代码示例（列表页骨架）：**

```vue
<template>
  <div class="list-page">
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="车辆型号">
        <el-input v-model="searchForm.model" placeholder="请输入型号" clearable></el-input>
      </el-form-item>
      <el-form-item label="车辆状态">
        <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
          <el-option label="在库" value="in_stock"></el-option>
          <el-option label="已售" value="sold"></el-option>
          </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handleAdd">新增车辆</el-button>
      </el-form-item>
    </el-form>

    <div v-if="multipleSelection.length > 0" class="batch-operations">
      已选中 {{ multipleSelection.length }} 项
      <el-button type="danger" size="small" @click="handleBatchDelete">批量删除</el-button>
      </div>

    <el-table
      :data="tableData"
      v-loading="loading"
      border
      stripe
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="vin" label="VIN码" width="180" fixed></el-table-column>
      <el-table-column prop="model" label="型号" width="150"></el-table-column>
      <el-table-column prop="color" label="颜色" width="100"></el-table-column>
      <el-table-column prop="price" label="售价" width="120" sortable></el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'in_stock' ? 'success' : 'info'">
            {{ row.status === 'in_stock' ? '在库' : '已售' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="arrivalTime" label="到店日期" width="180" sortable></el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleView(row)">详情</el-button>
          <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pagination.pageSize"
      :layout="pagination.layout"
      :total="pagination.total"
      background
      class="pagination"
    ></el-pagination>

    <VehicleDetailModal
      v-if="dialogVisible && dialogType === 'modal'"
      :visible="dialogVisible"
      :vehicleId="currentVehicleId"
      @close="dialogVisible = false"
      @refresh="fetchTableData"
    />
    <VehicleDetailDrawer
      v-if="drawerVisible && drawerType === 'drawer'"
      :visible="drawerVisible"
      :vehicleId="currentVehicleId"
      @close="drawerVisible = false"
      @refresh="fetchTableData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 假设这里引入了API服务
import { getVehicleList, deleteVehicle, batchDeleteVehicles } from '@/api/sales';
import VehicleDetailModal from './components/VehicleDetailModal.vue'; // 示例组件
import VehicleDetailDrawer from './components/VehicleDetailDrawer.vue'; // 示例组件

interface Vehicle {
  id: string;
  vin: string;
  model: string;
  color: string;
  price: number;
  status: 'in_stock' | 'sold';
  arrivalTime: string;
}

const searchForm = reactive({
  model: '',
  status: '',
});

const tableData = ref<Vehicle[]>([]);
const loading = ref(false);
const multipleSelection = ref<Vehicle[]>([]);
const currentVehicleId = ref<string | null>(null);

const dialogVisible = ref(false); // 控制模态框显示
const drawerVisible = ref(false); // 控制抽屉显示
const dialogType = ref<'modal' | 'drawer'>('modal'); // 动态决定使用模态框还是抽屉

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  layout: 'total, sizes, prev, pager, next, jumper',
});

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
    };
    const res = await getVehicleList(params); // 调用API
    tableData.value = res.data.list;
    pagination.total = res.data.total;
  } catch (error) {
    ElMessage.error('获取车辆列表失败！');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchTableData();
});

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1; // 搜索时重置回第一页
  fetchTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.model = '';
  searchForm.status = '';
  handleSearch(); // 重置后重新查询
};

// 新增
const handleAdd = () => {
  currentVehicleId.value = null; // 新增时ID为空
  // 根据业务场景选择打开模态框或抽屉
  dialogType.value = 'modal'; // 假设新增用模态框
  dialogVisible.value = true;
  // 或者 drawerVisible.value = true;
};

// 多选变化
const handleSelectionChange = (val: Vehicle[]) => {
  multipleSelection.value = val;
};

// 批量删除
const handleBatchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的项！');
    return;
  }
  ElMessageBox.confirm(
    `确定要删除选中的 ${multipleSelection.value.length} 项车辆吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const ids = multipleSelection.value.map(item => item.id);
      await batchDeleteVehicles(ids); // 调用批量删除API
      ElMessage.success('批量删除成功！');
      fetchTableData(); // 刷新列表
    })
    .catch(() => {
      ElMessage.info('取消删除');
    });
};

// 查看详情
const handleView = (row: Vehicle) => {
  currentVehicleId.value = row.id;
  // 对于复杂详情，建议使用抽屉或新页面
  dialogType.value = 'drawer'; // 假设详情用抽屉
  drawerVisible.value = true;
};

// 编辑
const handleEdit = (row: Vehicle) => {
  currentVehicleId.value = row.id;
  // 编辑也可能用抽屉或新页面，如果表单复杂
  dialogType.value = 'modal'; // 假设编辑用模态框
  dialogVisible.value = true;
};

// 删除单条
const handleDelete = (row: Vehicle) => {
  ElMessageBox.confirm(`确定要删除VIN码为 ${row.vin} 的车辆吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await deleteVehicle(row.id); // 调用删除API
      ElMessage.success('删除成功！');
      fetchTableData(); // 刷新列表
    })
    .catch(() => {
      ElMessage.info('取消删除');
    });
};

// 分页：每页条数变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1; // 改变每页条数后回到第一页
  fetchTableData();
};

// 分页：当前页码变化
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  fetchTableData();
};
</script>

<style lang="scss" scoped>
.list-page {
  padding: 20px;

  .search-form {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .batch-operations {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    padding: 10px 20px;
    margin-bottom: 15px;
    border-radius: 4px;
    color: #1890ff;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .el-table {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .pagination {
    justify-content: flex-end; // 右对齐
  }
}
</style>
```

---

### **2. 详情页 (Detail Page)：展示数据全貌**

详情页是展示单个数据实体所有相关信息的页面。根据信息的复杂度，我们可以选择模态框、抽屉或新页面。

#### **方案一：模态框 (`ElDialog`) - 适用于简单详情或快速编辑**

* **优点：** 保持上下文，操作快捷，无需页面跳转。
* **缺点：** 空间有限，不适合信息量大或复杂交互。

**现代化和用户友好的交互要点：**

* **简洁的布局：** 信息分组展示，利用 Element Plus 的 `ElDescriptions` 组件（描述列表）或 `ElCard` 进行内容区隔。
* **明确的标题：** `ElDialog` 的 `title` 属性。
* **关闭按钮/Esc关闭：** 默认支持，符合用户习惯。
* **Loading 状态：** 详情数据加载时，在模态框内显示 loading。
* **操作按钮：** 如“编辑”、“关闭”。

**代码示例（`VehicleDetailModal.vue` 组件骨架）：**

```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="60%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-scrollbar max-height="60vh" v-loading="loading">
      <div v-if="vehicle" class="vehicle-detail">
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="VIN码">{{ vehicle.vin }}</el-descriptions-item>
          <el-descriptions-item label="型号">{{ vehicle.model }}</el-descriptions-item>
          <el-descriptions-item label="品牌">{{ vehicle.brand }}</el-descriptions-item>
          <el-descriptions-item label="颜色">{{ vehicle.color }}</el-descriptions-item>
          <el-descriptions-item label="售价">{{ vehicle.price }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="vehicle.status === 'in_stock' ? 'success' : 'info'">
              {{ vehicle.status === 'in_stock' ? '在库' : '已售' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="到店日期">{{ vehicle.arrivalTime }}</el-descriptions-item>
          <el-descriptions-item label="生产日期">{{ vehicle.productionDate }}</el-descriptions-item>
        </el-descriptions>

        <el-divider>客户信息</el-divider>
        <el-descriptions v-if="vehicle.customer" :column="2" border>
          <el-descriptions-item label="客户姓名">{{ vehicle.customer.name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ vehicle.customer.phone }}</el-descriptions-item>
          </el-descriptions>
        <el-empty v-else description="暂无客户信息"></el-empty>

        <el-divider>其他信息</el-divider>
        </div>
      <el-empty v-else description="车辆信息加载失败或不存在"></el-empty>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEditFromDetail">编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getVehicleDetail } from '@/api/sales'; // 假设有获取详情的API

interface VehicleDetail {
  id: string;
  vin: string;
  model: string;
  brand: string;
  color: string;
  price: number;
  status: 'in_stock' | 'sold';
  arrivalTime: string;
  productionDate: string;
  customer?: {
    name: string;
    phone: string;
  };
  // 更多详情字段
}

const props = defineProps<{
  visible: boolean;
  vehicleId: string | null;
}>();

const emit = defineEmits(['close', 'edit']);

const dialogVisible = ref(props.visible);
const vehicle = ref<VehicleDetail | null>(null);
const loading = ref(false);
const title = ref('车辆详情');

watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal && props.vehicleId) {
      fetchVehicleDetail(props.vehicleId);
    } else {
      vehicle.value = null; // 关闭时清空数据
    }
  }
);

watch(
  () => props.vehicleId,
  (newVal) => {
    if (newVal) {
      title.value = `车辆详情 - ${newVal}`;
    } else {
      title.value = '车辆详情';
    }
  }, { immediate: true }
);

const fetchVehicleDetail = async (id: string) => {
  loading.value = true;
  try {
    const res = await getVehicleDetail(id);
    vehicle.value = res.data;
  } catch (error) {
    ElMessage.error('获取车辆详情失败！');
    console.error(error);
    vehicle.value = null;
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  emit('close');
};

const handleEditFromDetail = () => {
  if (vehicle.value) {
    emit('edit', vehicle.value.id); // 触发编辑事件，可以由父组件处理打开编辑表单
  }
};
</script>

<style lang="scss" scoped>
.vehicle-detail {
  padding-right: 15px; // 留出滚动条空间
  // 可以根据需要调整详情页的布局和样式
  .el-descriptions {
    margin-bottom: 20px;
  }
}
</style>
```

#### **方案二：抽屉 (`ElDrawer`) - 适用于复杂详情或编辑，保留上下文**

* **优点：** 比模态框更大的空间，更适合展示复杂信息和多Tab内容。用户仍然可以看到列表页，有更好的上下文感知。
* **缺点：** 仍然有空间限制，不适合需要全屏工作流的场景。

**现代化和用户友好的交互要点：**

* **方向选择：** `direction` 属性可以选择从左/右/上/下弹出，右侧弹出是最常用的。
* **可调整宽度：** `size` 属性可以控制抽屉的宽度（或高度），方便调整展示空间。
* **多层抽屉：** 理论上可以嵌套，但和模态框类似，不建议过多层嵌套，容易混乱。
* **Loading 状态：** 同模态框。
* **Tab 切换：** 如果详情内容分为多个模块，可以使用 `ElTabs` 进行组织。

**代码示例（`VehicleDetailDrawer.vue` 组件骨架）：**

```vue
<template>
  <el-drawer
    v-model="drawerVisible"
    :title="title"
    :direction="direction"
    :size="drawerSize"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-scrollbar max-height="calc(100vh - 120px)" v-loading="loading">
      <div v-if="vehicle" class="vehicle-detail-drawer">
        <el-tabs v-model="activeTab" class="demo-tabs">
          <el-tab-pane label="基本信息" name="baseInfo">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="VIN码">{{ vehicle.vin }}</el-descriptions-item>
              <el-descriptions-item label="型号">{{ vehicle.model }}</el-descriptions-item>
              <el-descriptions-item label="品牌">{{ vehicle.brand }}</el-descriptions-item>
              <el-descriptions-item label="颜色">{{ vehicle.color }}</el-descriptions-item>
              <el-descriptions-item label="售价">{{ vehicle.price }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="vehicle.status === 'in_stock' ? 'success' : 'info'">
                  {{ vehicle.status === 'in_stock' ? '在库' : '已售' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="到店日期">{{ vehicle.arrivalTime }}</el-descriptions-item>
              <el-descriptions-item label="生产日期">{{ vehicle.productionDate }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <el-tab-pane label="客户信息" name="customerInfo">
            <el-descriptions v-if="vehicle.customer" :column="2" border>
              <el-descriptions-item label="客户姓名">{{ vehicle.customer.name }}</el-descriptions-item>
              <el-descriptions-item label="联系电话">{{ vehicle.customer.phone }}</el-descriptions-item>
              <el-descriptions-item label="客户级别">{{ vehicle.customer.level }}</el-descriptions-item>
            </el-descriptions>
            <el-empty v-else description="暂无客户信息"></el-empty>
          </el-tab-pane>

          <el-tab-pane label="销售记录" name="salesRecords">
            <el-table :data="vehicle.salesRecords" border style="width: 100%">
              <el-table-column prop="orderNum" label="订单号"></el-table-column>
              <el-table-column prop="saleDate" label="销售日期"></el-table-column>
              <el-table-column prop="salesperson" label="销售员"></el-table-column>
            </el-table>
            <el-empty v-if="!vehicle.salesRecords || vehicle.salesRecords.length === 0" description="暂无销售记录"></el-empty>
          </el-tab-pane>
          </el-tabs>
      </div>
      <el-empty v-else description="车辆信息加载失败或不存在"></el-empty>
    </el-scrollbar>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEditFromDrawer">编辑</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getVehicleDetail } from '@/api/sales'; // 假设有获取详情的API

interface VehicleDetail {
  id: string;
  vin: string;
  model: string;
  brand: string;
  color: string;
  price: number;
  status: 'in_stock' | 'sold';
  arrivalTime: string;
  productionDate: string;
  customer?: {
    name: string;
    phone: string;
    level: string;
  };
  salesRecords?: Array<{
    orderNum: string;
    saleDate: string;
    salesperson: string;
  }>;
  // 更多详情字段
}

const props = defineProps<{
  visible: boolean;
  vehicleId: string | null;
  direction?: 'rtl' | 'ltr' | 'ttb' | 'btt'; // rtl: right to left
  size?: string | number; // '50%' or 800
}>();

const emit = defineEmits(['close', 'edit']);

const drawerVisible = ref(props.visible);
const vehicle = ref<VehicleDetail | null>(null);
const loading = ref(false);
const title = ref('车辆详情');
const activeTab = ref('baseInfo'); // 默认激活的Tab

const direction = ref(props.direction || 'rtl');
const drawerSize = ref(props.size || '50%'); // 默认宽度

watch(
  () => props.visible,
  (newVal) => {
    drawerVisible.value = newVal;
    if (newVal && props.vehicleId) {
      fetchVehicleDetail(props.vehicleId);
      activeTab.value = 'baseInfo'; // 每次打开重置到第一个Tab
    } else {
      vehicle.value = null;
    }
  }
);

watch(
  () => props.vehicleId,
  (newVal) => {
    if (newVal) {
      title.value = `车辆详情 - ${newVal}`;
    } else {
      title.value = '车辆详情';
    }
  }, { immediate: true }
);

const fetchVehicleDetail = async (id: string) => {
  loading.value = true;
  try {
    const res = await getVehicleDetail(id);
    vehicle.value = res.data;
  } catch (error) {
    ElMessage.error('获取车辆详情失败！');
    console.error(error);
    vehicle.value = null;
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  drawerVisible.value = false;
  emit('close');
};

const handleEditFromDrawer = () => {
  if (vehicle.value) {
    emit('edit', vehicle.value.id);
  }
};
</script>

<style lang="scss" scoped>
.vehicle-detail-drawer {
  padding-right: 15px; // 留出滚动条空间
  // 抽屉内内容样式
  .el-descriptions {
    margin-bottom: 20px;
  }
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
    border-top: 1px solid var(--el-border-color-light);
  }
}
</style>
```

#### **方案三：新页面跳转 - 适用于全屏工作流的复杂详情**

* **优点：** 拥有独立且无限的页面空间，可以设计最复杂的布局和交互，URL可分享。
* **缺点：** 失去上下文，需要额外的导航操作（如面包屑、返回按钮）。

**现代化和用户友好的交互要点：**

* **面包屑导航 (`ElBreadcrumb`)：** 帮助用户了解当前位置，方便返回。
* **统一的页面标题和操作栏：** 保持与列表页的统一风格。
* **丰富的图表和可视化：** 如果DMS详情包含大量数据分析，可以在新页面中集成 Echarts 或 G2Plot (如果选择了 Ant Design Vue 生态)。
* **侧边导航或锚点：** 如果详情页内容非常长，可以使用页面内的侧边导航或锚点链接快速跳转到不同区域。

**代码示例（`views/sales/VehicleDetailPage.vue` 骨架）：**

```vue
<template>
  <div class="vehicle-detail-page">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/sales/vehicles' }">车辆管理</el-breadcrumb-item>
      <el-breadcrumb-item>{{ title }}</el-breadcrumb-item>
    </el-breadcrumb>

    <el-card class="page-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>{{ title }}</span>
          <div>
            <el-button @click="handleBack">返回</el-button>
            <el-button type="primary" @click="handleEdit">编辑</el-button>
            <el-button type="danger" @click="handleDelete">删除</el-button>
          </div>
        </div>
      </template>

      <div v-if="vehicle" class="detail-content">
        <el-tabs v-model="activeTab" class="demo-tabs">
          <el-tab-pane label="基本信息" name="baseInfo">
            <el-descriptions :column="3" border class="mt-4">
              <el-descriptions-item label="VIN码">{{ vehicle.vin }}</el-descriptions-item>
              <el-descriptions-item label="型号">{{ vehicle.model }}</el-descriptions-item>
              <el-descriptions-item label="品牌">{{ vehicle.brand }}</el-descriptions-item>
              <el-descriptions-item label="颜色">{{ vehicle.color }}</el-descriptions-item>
              <el-descriptions-item label="售价">{{ vehicle.price }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="vehicle.status === 'in_stock' ? 'success' : 'info'">
                  {{ vehicle.status === 'in_stock' ? '在库' : '已售' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="到店日期">{{ vehicle.arrivalTime }}</el-descriptions-item>
              <el-descriptions-item label="生产日期">{{ vehicle.productionDate }}</el-descriptions-item>
              <el-descriptions-item label="发动机号">{{ vehicle.engineNum }}</el-descriptions-item>
              <el-descriptions-item label="变速箱型号">{{ vehicle.transmissionType }}</el-descriptions-item>
            </el-descriptions>

            <el-divider>车辆图片</el-divider>
            <div class="image-gallery">
              <el-image v-for="img in vehicle.images" :key="img" :src="img" fit="cover"
                        style="width: 150px; height: 100px; margin: 5px; border-radius: 4px;"
                        :preview-src-list="vehicle.images"></el-image>
            </div>
          </el-tab-pane>

          <el-tab-pane label="销售记录" name="salesRecords">
            <el-table :data="vehicle.salesRecords" border style="width: 100%">
              <el-table-column prop="orderNum" label="订单号" width="180"></el-table-column>
              <el-table-column prop="saleDate" label="销售日期" width="180"></el-table-column>
              <el-table-column prop="customerName" label="客户姓名"></el-table-column>
              <el-table-column prop="salesperson" label="销售员"></el-table-column>
              <el-table-column prop="salePrice" label="成交价"></el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button link type="primary" size="small" @click="viewOrder(row.orderId)">查看订单</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-empty v-if="!vehicle.salesRecords || vehicle.salesRecords.length === 0" description="暂无销售记录"></el-empty>
          </el-tab-pane>

          <el-tab-pane label="维修保养记录" name="maintenanceRecords">
            <el-table :data="vehicle.maintenanceRecords" border style="width: 100%">
              <el-table-column prop="serviceNum" label="服务单号" width="180"></el-table-column>
              <el-table-column prop="serviceDate" label="服务日期" width="180"></el-table-column>
              <el-table-column prop="serviceType" label="服务类型"></el-table-column>
              <el-table-column prop="totalCost" label="总费用"></el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button link type="primary" size="small" @click="viewService(row.serviceId)">查看服务</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-empty v-if="!vehicle.maintenanceRecords || vehicle.maintenanceRecords.length === 0" description="暂无维修保养记录"></el-empty>
          </el-tab-pane>
          </el-tabs>
      </div>
      <el-empty v-else description="车辆信息加载失败或不存在"></el-empty>
    </el-card>

    <VehicleEditFormDrawer
      v-if="editDrawerVisible"
      :visible="editDrawerVisible"
      :vehicleId="vehicleId"
      @close="editDrawerVisible = false"
      @success="fetchVehicleDetail(vehicleId as string)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getVehicleDetail, deleteVehicle } from '@/api/sales'; // 假设有获取详情和删除的API
import VehicleEditFormDrawer from './components/VehicleEditFormDrawer.vue'; // 示例编辑组件

interface VehicleDetail {
  id: string;
  vin: string;
  model: string;
  brand: string;
  color: string;
  price: number;
  status: 'in_stock' | 'sold';
  arrivalTime: string;
  productionDate: string;
  engineNum: string;
  transmissionType: string;
  images: string[];
  salesRecords: Array<{
    orderId: string;
    orderNum: string;
    saleDate: string;
    customerName: string;
    salesperson: string;
    salePrice: number;
  }>;
  maintenanceRecords: Array<{
    serviceId: string;
    serviceNum: string;
    serviceDate: string;
    serviceType: string;
    totalCost: number;
  }>;
  // 更多详情字段
}

const route = useRoute();
const router = useRouter();

const vehicleId = ref<string | null>(null);
const vehicle = ref<VehicleDetail | null>(null);
const loading = ref(false);
const title = ref('车辆详情');
const activeTab = ref('baseInfo');
const editDrawerVisible = ref(false);

onMounted(() => {
  vehicleId.value = route.params.id as string;
  if (vehicleId.value) {
    fetchVehicleDetail(vehicleId.value);
  } else {
    ElMessage.error('缺少车辆ID');
    router.back();
  }
});

watch(
  () => route.params.id,
  (newId) => {
    if (newId && newId !== vehicleId.value) {
      vehicleId.value = newId as string;
      fetchVehicleDetail(vehicleId.value);
    }
  }
);

const fetchVehicleDetail = async (id: string) => {
  loading.value = true;
  try {
    const res = await getVehicleDetail(id);
    vehicle.value = res.data;
    title.value = `车辆详情 - ${res.data.vin}`;
  } catch (error) {
    ElMessage.error('获取车辆详情失败！');
    console.error(error);
    vehicle.value = null;
  } finally {
    loading.value = false;
  }
};

const handleBack = () => {
  router.back();
};

const handleEdit = () => {
  // 可以选择打开抽屉，或者跳转到编辑页面
  editDrawerVisible.value = true;
  // router.push({ name: 'VehicleEdit', params: { id: vehicleId.value } });
};

const handleDelete = () => {
  if (!vehicle.value) return;
  ElMessageBox.confirm(`确定要删除VIN码为 ${vehicle.value.vin} 的车辆吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await deleteVehicle(vehicleId.value as string);
      ElMessage.success('删除成功！');
      router.push({ name: 'VehicleList' }); // 删除成功后返回列表页
    })
    .catch(() => {
      ElMessage.info('取消删除');
    });
};

const viewOrder = (orderId: string) => {
  // 跳转到订单详情页
  router.push({ name: 'OrderDetail', params: { id: orderId } });
};

const viewService = (serviceId: string) => {
  // 跳转到服务详情页
  router.push({ name: 'ServiceDetail', params: { id: serviceId } });
};
</script>

<style lang="scss" scoped>
.vehicle-detail-page {
  padding: 20px;

  .el-breadcrumb {
    margin-bottom: 20px;
  }

  .page-card {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
    }

    .detail-content {
      padding: 10px;
      .el-descriptions {
        margin-bottom: 20px;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
      }
    }
  }
}
</style>
```

---

### **3. 表单页 (Form Page)：数据录入和编辑**

表单页是DMS中数据录入和编辑的核心。

**核心组件：`ElForm` / `ElFormItem`，各种输入组件如 `ElInput`, `ElSelect`, `ElDatePicker`, `ElUpload` 等。**

**现代化和用户友好的交互要点：**

* **清晰的表单布局：**
    * **栅格布局 (`ElCol` / `ElRow`)：** 用于实现复杂的两列、三列表单布局，使其在PC端看起来更美观。
    * **分组：** 使用 `ElCard` 或 `ElDivider` 对表单字段进行逻辑分组，减少用户认知负担。
    * **内联表单 (`inline`)：** 适用于简单的搜索表单。
    * **响应式：** 使用 Element Plus 的栅格系统，在小屏幕时自动调整为单列布局。
* **实时表单验证 (`rules` 属性)：**
    * `ElForm` 的 `rules` 属性配合 `ElFormItem` 的 `prop` 属性，实现实时验证。
    * **友好的错误提示：** Element Plus 默认的验证提示是直观的。
* **输入提示 / 帮助文本：** `placeholder` 属性，或 `ElFormItem` 内部的 `tip` 文本，帮助用户理解字段含义。
* **输入限制：** `maxlength`, `minlength`, `type="number"` 等。
* **文件上传 (`ElUpload`)：**
    * **进度显示：** 告知用户上传进度。
    * **预览功能：** 图片上传后可以预览。
    * **拖拽上传：** 提升用户体验。
* **提交和重置：**
    * **提交按钮：** 禁用状态（表单未通过验证或正在提交中）。
    * **重置按钮：** 清空表单。
    * **取消按钮：** 返回上一页或关闭弹窗。
* **成功/失败提示：** `ElMessage` 或 `ElNotification`。

**代码示例（`VehicleEditFormDrawer.vue` 组件骨架 - 作为抽屉中的编辑表单）：**

```vue
<template>
  <el-drawer
    v-model="drawerVisible"
    :title="title"
    direction="rtl"
    size="60%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-scrollbar max-height="calc(100vh - 120px)" v-loading="loading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="vehicle-form"
      >
        <el-card shadow="never" class="form-section">
          <template #header>
            <div class="card-header">基本信息</div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="VIN码" prop="vin">
                <el-input v-model="formData.vin" placeholder="请输入VIN码" :disabled="!!vehicleId"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="品牌" prop="brand">
                <el-select v-model="formData.brand" placeholder="请选择品牌" style="width: 100%;">
                  <el-option label="奔驰" value="benz"></el-option>
                  <el-option label="宝马" value="bmw"></el-option>
                  <el-option label="奥迪" value="audi"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号" prop="model">
                <el-input v-model="formData.model" placeholder="请输入车辆型号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="颜色" prop="color">
                <el-input v-model="formData.color" placeholder="请输入颜色"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="售价" prop="price">
                <el-input-number v-model="formData.price" :min="0" :precision="2" controls-position="right" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formData.status">
                  <el-radio label="in_stock">在库</el-radio>
                  <el-radio label="sold">已售</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="到店日期" prop="arrivalTime">
                <el-date-picker v-model="formData.arrivalTime" type="date" placeholder="选择日期" style="width: 100%;"></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="生产日期" prop="productionDate">
                <el-date-picker v-model="formData.productionDate" type="date" placeholder="选择日期" style="width: 100%;"></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card shadow="never" class="form-section mt-4">
          <template #header>
            <div class="card-header">发动机和变速箱信息</div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发动机号" prop="engineNum">
                <el-input v-model="formData.engineNum" placeholder="请输入发动机号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="变速箱型号" prop="transmissionType">
                <el-input v-model="formData.transmissionType" placeholder="请输入变速箱型号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card shadow="never" class="form-section mt-4">
          <template #header>
            <div class="card-header">车辆图片</div>
          </template>
          <el-form-item label="图片上传">
            <el-upload
              v-model:file-list="fileList"
              action="YOUR_UPLOAD_API_URL" // 替换为你的上传API地址
              list-type="picture-card"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-success="handleUploadSuccess"
              :before-upload="beforeUpload"
              :on-error="handleUploadError"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
            <el-dialog v-model="dialogImageVisible">
              <img w-full :src="dialogImageUrl" alt="Preview Image" style="max-width: 100%;" />
            </el-dialog>
            <div class="el-upload__tip">支持JPG/PNG文件，且不超过5MB</div>
          </el-form-item>
        </el-card>

      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit(formRef)" :loading="submitLoading">提交</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage, type FormInstance, type FormRules, type UploadProps, type UploadRawFile } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { getVehicleDetail, addVehicle, updateVehicle } from '@/api/sales'; // 假设有增删改查API

interface VehicleForm {
  id?: string; // 编辑时有ID
  vin: string;
  brand: string;
  model: string;
  color: string;
  price: number;
  status: 'in_stock' | 'sold';
  arrivalTime: string;
  productionDate: string;
  engineNum: string;
  transmissionType: string;
  images: string[]; // 图片URL数组
}

const props = defineProps<{
  visible: boolean;
  vehicleId: string | null; // 如果是编辑，则有ID
}>();

const emit = defineEmits(['close', 'success']);

const drawerVisible = ref(props.visible);
const loading = ref(false);
const submitLoading = ref(false);
const title = ref('');

const formRef = ref<FormInstance>();
const formData = reactive<VehicleForm>({
  vin: '',
  brand: '',
  model: '',
  color: '',
  price: 0,
  status: 'in_stock',
  arrivalTime: '',
  productionDate: '',
  engineNum: '',
  transmissionType: '',
  images: [],
});

const formRules = reactive<FormRules<VehicleForm>>({
  vin: [
    { required: true, message: '请输入VIN码', trigger: 'blur' },
    { min: 17, max: 17, message: 'VIN码长度为17位', trigger: 'blur' },
  ],
  brand: [{ required: true, message: '请选择品牌', trigger: 'change' }],
  model: [{ required: true, message: '请输入型号', trigger: 'blur' }],
  price: [{ required: true, message: '请输入售价', trigger: 'blur' }],
  arrivalTime: [{ required: true, message: '请选择到店日期', trigger: 'change' }],
});

// 图片上传相关
const fileList = ref<UploadProps['fileList']>([]);
const dialogImageUrl = ref('');
const dialogImageVisible = ref(false);

watch(
  () => props.visible,
  (newVal) => {
    drawerVisible.value = newVal;
    if (newVal) {
      if (props.vehicleId) {
        title.value = '编辑车辆信息';
        fetchVehicleDetail(props.vehicleId);
      } else {
        title.value = '新增车辆';
        resetForm();
      }
    }
  }
);

const fetchVehicleDetail = async (id: string) => {
  loading.value = true;
  try {
    const res = await getVehicleDetail(id);
    Object.assign(formData, res.data); // 填充表单数据
    // 填充图片列表
    fileList.value = res.data.images.map((url: string, index: number) => ({
      name: `image-${index}`,
      url: url,
      uid: Date.now() + index, // 确保唯一UID
      status: 'success',
    }));
  } catch (error) {
    ElMessage.error('获取车辆信息失败！');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  formRef.value?.resetFields(); // 重置表单验证和字段
  // 手动清空数据，因为resetFields只对有prop的字段有效
  Object.assign(formData, {
    id: undefined,
    vin: '',
    brand: '',
    model: '',
    color: '',
    price: 0,
    status: 'in_stock',
    arrivalTime: '',
    productionDate: '',
    engineNum: '',
    transmissionType: '',
    images: [],
  });
  fileList.value = []; // 清空图片
};

const handleClose = () => {
  drawerVisible.value = false;
  resetForm(); // 关闭时重置表单
  emit('close');
};

const handleSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      submitLoading.value = true;
      try {
        formData.images = fileList.value.map(file => file.url || ''); // 将上传成功的图片URL收集起来
        if (formData.id) {
          await updateVehicle(formData.id, formData);
          ElMessage.success('车辆信息更新成功！');
        } else {
          await addVehicle(formData);
          ElMessage.success('车辆信息新增成功！');
        }
        emit('success'); // 通知父组件刷新列表
        handleClose(); // 提交成功后关闭抽屉
      } catch (error) {
        ElMessage.error('操作失败！');
        console.error(error);
      } finally {
        submitLoading.value = false;
      }
    } else {
      console.log('表单验证失败', fields);
      ElMessage.error('请检查表单填写是否完整和正确！');
    }
  });
};

// 图片上传处理
const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles);
  // 从 formData.images 中移除对应的URL
  formData.images = uploadFiles.map(file => file.url || '');
};

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!;
  dialogImageVisible.value = true;
};

const handleUploadSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  // 假设后端返回的response.data就是图片URL
  if (response && response.code === 200 && response.data.url) {
    uploadFile.url = response.data.url; // 将实际的图片URL赋值给file.url
    formData.images.push(response.data.url); // 添加到 formData 中
    ElMessage.success('图片上传成功！');
  } else {
    ElMessage.error('图片上传失败！');
  }
};

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const isJPGorPNG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png';
  const isLt5M = rawFile.size / 1024 / 1024 < 5;

  if (!isJPGorPNG) {
    ElMessage.error('图片只能是 JPG/PNG 格式!');
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
  }
  return isJPGorPNG && isLt5M;
};

const handleUploadError: UploadProps['onError'] = (error) => {
  ElMessage.error('图片上传失败，请重试！' + error.message);
};
</script>

<style lang="scss" scoped>
.vehicle-form {
  padding-right: 15px; // 留出滚动条空间
  .form-section {
    margin-bottom: 20px;
    border-radius: 6px;
    .card-header {
      font-size: 16px;
      font-weight: bold;
      color: var(--el-text-color-regular);
    }
  }
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
    border-top: 1px solid var(--el-border-color-light);
  }
  .el-upload__tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}
</style>
```

---

### **总结和建议：**

* **列表页、详情页、表单页的连接：**
    * **从列表页点击“详情”：** 如果详情内容不复杂且希望保留列表上下文，打开 **`ElDialog`**。
    * **从列表页点击“详情”：** 如果详情内容复杂但仍希望保留列表上下文，或者需要展示多Tab信息，打开 **`ElDrawer`**。
    * **从列表页点击“详情”：** 如果详情内容极其复杂，需要一个独立的工作区，或者需要分享URL，**跳转到新页面**。
    * **从列表页点击“编辑”或“新增”：** 通常使用 **`ElDialog`** 或 **`ElDrawer`** 来承载表单，这样用户可以快速完成操作并返回列表。对于非常复杂的，多步骤的表单，也可以考虑新页面。
    * **从详情页点击“编辑”：** 可以在详情页中打开一个 **`ElDrawer`** 来承载编辑表单，或者跳转到独立的编辑页面。
* **组件化与复用：** 列表页中的“详情”/“编辑”弹窗/抽屉应该被抽离成独立的 Vue 组件，如 `VehicleDetailModal.vue`，`VehicleDetailDrawer.vue`，`VehicleEditFormDrawer.vue`，这样可以更好地复用和维护。
* **统一风格：** 通过 Element Plus 的主题定制能力，统一DMS系统的颜色、字体、圆角等视觉元素，确保现代化和一致的用户体验。
* **响应式布局：** 在 PC 端，利用 Element Plus 的栅格系统（`ElRow`, `ElCol`）构建灵活的布局。同时，需要思考未来移动端如何进行裁剪和调整，例如在移动端可以隐藏部分列，或者将复杂的表单布局调整为单列。
* **用户反馈：** 善用 `ElMessage` 和 `ElNotification` 及时告知用户操作结果。
* **性能优化：** 对于DMS中数据量大的表格，考虑虚拟列表或分页来优化渲染性能。
