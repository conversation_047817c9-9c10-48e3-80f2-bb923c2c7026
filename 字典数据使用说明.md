# DMS前端项目字典数据使用说明

## 概述
本文档说明了基于项目下拉框数据使用情况分析报告整理出的字典数据结构和使用方法。

## 字典编码规则

### 字典类别编码规则
- **格式**: 4位数字
- **前2位**: 系统标识（销售系统固定为 `01`）
- **后2位**: 字典类型，从 `01` 开始依次递增

### 字典值编码规则
- **格式**: 8位数字
- **前4位**: 字典类别编码
- **后4位**: 字典值序号，从 `0001` 开始依次递增

## 字典类别列表

| 类别编码 | 中文名称 | 英文名称 | 马来文名称 | 描述 |
|---------|---------|---------|-----------|------|
| 0101 | 语言类型 | Language Type | Jenis Bahasa | 系统支持的语言类型 |
| 0102 | 项目类型 | Project Type | Jenis Projek | 项目管理中的项目类型分类 |
| 0103 | 零件类型 | Parts Type | Jenis Bahagian | 零件管理中的零件类型分类 |
| 0104 | 工单优先级 | Work Order Priority | Keutamaan Pesanan Kerja | 工单的优先级分类 |
| 0105 | 工单状态 | Work Order Status | Status Pesanan Kerja | 工单的状态分类 |
| 0106 | 客户来源 | Customer Source | Sumber Pelanggan | 客户的来源渠道分类 |
| 0107 | 工单类型 | Work Order Type | Jenis Pesanan Kerja | 工单的类型分类 |
| 0108 | 预约状态 | Appointment Status | Status Temujanji | 预约的状态分类 |
| 0109 | 买方类型 | Buyer Type | Jenis Pembeli | 买方的类型分类 |
| 0110 | 车型 | Vehicle Model | Model Kenderaan | 车辆型号分类 【主数据】 |
| 0111 | 订单状态 | Order Status | Status Pesanan | 订单的状态分类 |
| 0112 | 审批状态 | Approval Status | Status Kelulusan | 审批的状态分类 |
| 0113 | 试驾配置 | Test Drive Configuration | Konfigurasi Pandu Uji | 试驾车辆的配置分类 |
| 0114 | 证件类别 | ID Type | Jenis Pengenalan | 身份证件的类别分类 |
| 0115 | 申请状态 | Application Status | Status Permohonan | 申请的状态分类 |
| 0116 | 意向级别 | Intent Level | Tahap Niat | 客户意向的级别分类 |
| 0117 | 配车状态 | Allocation Status | Status Peruntukan | 车辆配置的状态分类 |
| 0118 | 销售类型 | Sales Type | Jenis Jualan | 销售的类型分类 |
| 0119 | 支付状态 | Payment Status | Status Bayaran | 支付的状态分类 |
| 0120 | 库存状态 | Inventory Status | Status Inventori | 库存的状态分类 |
| 0121 | 保险状态 | Insurance Status | Status Insurans | 保险的状态分类 |
| 0122 | 交车状态 | Delivery Status | Status Penghantaran | 交车的状态分类 |
| 0123 | 确认方式 | Confirmation Method | Kaedah Pengesahan | 确认的方式分类 |
| 0124 | 工单分配状态 | Work Assignment Status | Status Tugasan Kerja | 工单分配的状态分类 |
| 0125 | 审批类型 | Approval Type | Jenis Kelulusan | 审批的类型分类 |
| 0126 | 布尔值类型 | Boolean Type | Jenis Boolean | 是否类型的选项 【字典配置项】 |
| 0127 | 零件名称 | Parts Name | Nama Bahagian | 零件的名称分类 |
| 0128 | 经销商 | Dealer | Pengedar | 经销商信息分类 【主数据】 |
| 0129 | 门店 | Store | Kedai | 门店信息分类 【主数据】 |
| 0130 | 销售顾问 | Sales Consultant | Perunding Jualan | 销售顾问信息分类 【主数据】 |
| 0131 | 服务顾问 | Service Advisor | Penasihat Perkhidmatan | 服务顾问信息分类 【主数据】 |
| 0132 | 技师 | Technician | Juruteknik | 技师信息分类 【主数据】 |
| 0133 | 车辆颜色 | Vehicle Color | Warna Kenderaan | 车辆颜色分类 【主数据】 |
| 0134 | 仓库 | Warehouse | Gudang | 仓库信息分类 【主数据】 |
| 0135 | 供应商 | Supplier | Pembekal | 供应商信息分类 【主数据】 |
| 0136 | 车型配置 | Vehicle Configuration | Konfigurasi Kenderaan | 车型配置分类 【主数据】 |
| 0137 | 时间段 | Time Slot | Slot Masa | 时间段分类 |
| 0138 | 潜客状态 | Prospect Status | Status Prospek | 潜客的状态分类 |
| 0139 | 跟进方式 | Follow-up Method | Kaedah Susulan | 跟进的方式分类 |
| 0140 | 地区 | Region | Wilayah | 地区分类 |
| 0141 | 战败原因 | Defeat Reason | Sebab Kekalahan | 战败的原因分类 |

## 主要字典数据示例

### 1. 语言类型 (0101)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01010001 | 中文 | Chinese | Cina |
| 01010002 | 英文 | English | Inggeris |

### 2. 工单状态 (0105)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01050001 | 草稿 | Draft | Draf |
| 01050002 | 待确认 | Pending Confirmation | Menunggu Pengesahan |
| 01050003 | 已确认 | Confirmed | Disahkan |
| 01050004 | 进行中 | In Progress | Sedang Berlangsung |
| 01050005 | 待质检 | Pending QC | Menunggu QC |
| 01050006 | 已完成 | Completed | Selesai |
| 01050007 | 已取消 | Cancelled | Dibatalkan |

### 3. 车型 (0110)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01100001 | AXIA | AXIA | AXIA |
| 01100002 | BEZZA | BEZZA | BEZZA |
| 01100003 | MYVI | MYVI | MYVI |
| 01100004 | ALZA | ALZA | ALZA |
| 01100005 | ARUZ | ARUZ | ARUZ |

### 4. 支付状态 (0119)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01190001 | 待支付定金 | Pending Deposit | Menunggu Deposit |
| 01190002 | 已支付定金 | Deposit Paid | Deposit Dibayar |
| 01190003 | 退款中 | Refunding | Sedang Dikembalikan |
| 01190004 | 退款完成 | Refund Completed | Pemulangan Selesai |
| 01190005 | 待支付尾款 | Pending Final Payment | Menunggu Bayaran Akhir |
| 01190006 | 已支付尾款 | Final Payment Paid | Bayaran Akhir Dibayar |

### 5. 客户来源 (0106)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01060001 | Super App | Super App | Super App |
| 01060002 | 门店创建 | Store Created | Dicipta Kedai |

### 6. 意向级别 (0116)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01160001 | H级 | H Level | Tahap H |
| 01160002 | A级 | A Level | Tahap A |
| 01160003 | B级 | B Level | Tahap B |
| 01160004 | C级 | C Level | Tahap C |

### 7. 潜客状态 (0138)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01380001 | 新建 | New | Baru |
| 01380002 | 跟进中 | Following | Sedang Diikuti |
| 01380003 | 已成交 | Closed | Ditutup |
| 01380004 | 已战败 | Defeated | Kalah |

### 8. 跟进方式 (0139)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01390001 | 电话 | Phone | Telefon |
| 01390002 | 微信 | WeChat | WeChat |
| 01390003 | 到店 | Visit | Lawatan |
| 01390004 | 邮件 | Email | E-mel |
| 01390005 | 短信 | SMS | SMS |

### 9. 地区 (0140)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01400001 | 吉隆坡 | Kuala Lumpur | Kuala Lumpur |
| 01400002 | 雪兰莪 | Selangor | Selangor |
| 01400003 | 柔佛 | Johor | Johor |
| 01400004 | 槟城 | Penang | Pulau Pinang |
| 01400005 | 马六甲 | Malacca | Melaka |
| 01400006 | 其他 | Other | Lain-lain |

### 10. 战败原因 (0141)
| 字典编码 | 中文名称 | 英文名称 | 马来文名称 |
|---------|---------|---------|-----------|
| 01410001 | 价格原因 | Price Reason | Sebab Harga |
| 01410002 | 竞品原因 | Competitor Reason | Sebab Pesaing |
| 01410003 | 库存原因 | Inventory Reason | Sebab Inventori |
| 01410004 | 服务原因 | Service Reason | Sebab Perkhidmatan |
| 01410005 | 销售技巧不足 | Sales Skill Insufficient | Kemahiran Jualan Tidak Mencukupi |
| 01410006 | 区域原因 | Regional Reason | Sebab Wilayah |
| 01410007 | 虚假客户 | Fake Customer | Pelanggan Palsu |
| 01410008 | 其他原因 | Other Reason | Sebab Lain |

## 数据库表结构

### 字典类别表 (tc_dic_category)
```sql
CREATE TABLE `tc_dic_category` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类别编码',
  `category_zh_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类别中文名称',
  `category_en_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类别英文名称',
  `category_ms_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类别马来西亚名称',
  `category_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '类别描述',
  `category_sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `is_active` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否启用（0-禁用 1-启用）',
  `versions` tinyint NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '删除标识（0-未删除 1-已删除）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人ID',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`,`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='字典类别表';
```

### 字典数据表 (tc_dic_data)
```sql
CREATE TABLE `tc_dic_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dic_category_code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典类别编码',
  `dic_code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典编码',
  `dic_zh_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典中文名称',
  `dic_en_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典英文名称',
  `dic_ms_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典马来西亚名称',
  `dic_sort` int NOT NULL COMMENT '排序',
  `versions` tinyint NOT NULL DEFAULT '1' COMMENT '乐观锁',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '删除标识（0-未删除 1-已删除）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人ID',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dic_code` (`dic_code`,`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='字典表';
```

## 使用方法

### 1. 前端调用示例
```javascript
// 获取字典数据
const getDictionaryData = async (categoryCode) => {
  const response = await api.get(`/dictionary/category/${categoryCode}`);
  return response.result;
};

// 使用示例
const workOrderStatusOptions = await getDictionaryData('0105');
```

### 2. API接口设计建议
```javascript
// 获取指定类别的字典数据
GET /api/dictionary/category/{categoryCode}

// 获取多个类别的字典数据
POST /api/dictionary/categories
Body: {
  "categoryCodes": ["0105", "0110", "0119"]
}

// 获取所有字典数据
GET /api/dictionary/all
```

### 3. 响应格式建议
```json
{
  "code": 200,
  "message": "success",
  "result": [
    {
      "dicCode": "01050001",
      "dicZhName": "草稿",
      "dicEnName": "Draft",
      "dicMsName": "Draf",
      "dicSort": 1
    },
    {
      "dicCode": "01050002",
      "dicZhName": "待确认",
      "dicEnName": "Pending Confirmation",
      "dicMsName": "Menunggu Pengesahan",
      "dicSort": 2
    }
  ]
}
```

## 维护说明

### 1. 新增字典类别
- 按照编码规则分配新的类别编码
- 在 `tc_dic_category` 表中添加类别信息
- 在 `tc_dic_data` 表中添加对应的字典值

### 2. 新增字典值
- 在对应类别下按序号递增添加新的字典编码
- 确保三种语言的翻译准确性

### 3. 数据迁移
- 执行 `dictionary_category.sql` 创建类别数据
- 执行 `dictionary_data.sql` 创建字典数据
- 验证数据完整性和编码唯一性

## 主数据与字典配置项分类

### 主数据 (Master Data)
以下字典类别属于主数据，是系统的核心基础数据：

| 字典类别 | 编码 | 说明 | 特点 |
|---------|------|------|------|
| 车型 | 0110 | 车辆型号基础数据 | 影响销售、试驾、查询等多个模块 |
| 经销商 | 0128 | 经销商基础信息 | 组织架构核心数据 |
| 门店 | 0129 | 门店基础信息 | 业务操作的基础单位 |
| 销售顾问 | 0130 | 销售顾问基础信息 | 人员主数据 |
| 服务顾问 | 0131 | 服务顾问基础信息 | 人员主数据 |
| 技师 | 0132 | 技师基础信息 | 人员主数据 |
| 车辆颜色 | 0133 | 车辆颜色基础数据 | 产品属性主数据 |
| 仓库 | 0134 | 仓库基础信息 | 库存管理基础数据 |
| 供应商 | 0135 | 供应商基础信息 | 供应链主数据 |
| 车型配置 | 0136 | 车型配置基础数据 | 产品配置主数据 |

### 字典配置项 (Dictionary Configuration)
以下字典类别属于通用配置项：

| 字典类别 | 编码 | 说明 | 特点 |
|---------|------|------|------|
| 布尔值类型 | 0126 | 是/否选择项 | 通用配置，多处复用 |

### 业务状态字典 (Business Status Dictionary)
其他字典类别主要用于业务流程状态管理，不属于主数据范畴。

## 数据维护策略

### 主数据维护原则
1. **集中管理**: 通过主数据管理系统统一维护
2. **权限控制**: 严格的权限管理，只有授权人员可以修改
3. **变更审批**: 重要主数据变更需要经过审批流程
4. **影响分析**: 变更前需要分析对业务系统的影响
5. **同步机制**: 确保主数据在各系统间的一致性

### 字典配置项维护原则
1. **灵活配置**: 支持业务人员根据需要进行配置
2. **版本管理**: 记录配置变更历史
3. **生效控制**: 支持配置的启用/禁用
4. **多语言**: 确保所有配置项支持多语言

## 注意事项

1. **编码唯一性**: 确保字典编码在系统中唯一
2. **多语言支持**: 所有字典值都需要提供中文、英文、马来文三种语言
3. **排序字段**: 使用 `dic_sort` 字段控制下拉框选项的显示顺序
4. **软删除**: 使用 `is_deleted` 字段实现软删除，避免数据丢失
5. **版本控制**: 使用 `versions` 字段实现乐观锁，防止并发更新冲突
6. **主数据标识**: 明确区分主数据和普通字典，采用不同的维护策略
7. **数据质量**: 主数据需要确保高质量，避免脏数据影响业务

### 字典总分类说明
以下字典总分类说明，是系统的核心基础数据：

| 字典类别 | 编码开始 | 编码结束 | 特点 |
|---------|------|------|------|
| 系统 | 0001 | 0099 | 系统基础数据 |
| 销售 | 0100 | 0199 | 销售相关数据 |
| 基础 | 0200 | 0299 | 基础数据 |
| 售后 | 0300 | 0399 | 售后相关数据 |
| 配件 | 0400 | 0499 | 配件相关数据 |
| 主数据 | 0500 | 0599 | 主数据 |
