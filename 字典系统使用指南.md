# 字典系统使用指南

## 概述

本项目已将所有非主数据的下拉框选项统一改为从数据字典接口获取，提供了统一的字典管理系统。

## 字典类型定义

所有字典类型定义在 `src/constants/dictionary.ts` 中：

```typescript
export const DICTIONARY_TYPES = {
  // 业务状态字典
  PROJECT_TYPE: '0102',           // 项目类型
  PARTS_TYPE: '0103',             // 零件类型
  WORK_ORDER_PRIORITY: '0104',    // 工单优先级
  WORK_ORDER_STATUS: '0105',      // 工单状态
  CUSTOMER_SOURCE: '0106',        // 客户来源
  WORK_ORDER_TYPE: '0107',        // 工单类型
  APPOINTMENT_STATUS: '0108',     // 预约状态
  BUYER_TYPE: '0109',             // 买方类型
  ORDER_STATUS: '0111',           // 订单状态
  APPROVAL_STATUS: '0112',        // 审批状态
  // ... 更多类型
} as const;
```

## 使用方式

### 1. 使用通用字典组件（推荐）

#### DictionarySelect 组件

```vue
<template>
  <!-- 基本用法 -->
  <DictionarySelect
    v-model="formData.priority"
    :dictionary-type="DICTIONARY_TYPES.WORK_ORDER_PRIORITY"
    placeholder="请选择优先级"
  />
  
  <!-- 多选 -->
  <DictionarySelect
    v-model="formData.statuses"
    :dictionary-type="DICTIONARY_TYPES.WORK_ORDER_STATUS"
    multiple
    placeholder="请选择状态"
  />
  
  <!-- 带"全部"选项 -->
  <DictionarySelect
    v-model="formData.buyerType"
    :dictionary-type="DICTIONARY_TYPES.BUYER_TYPE"
    show-all
    all-label="全部类型"
    all-value=""
  />
</template>

<script setup>
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
</script>
```

#### DictionaryRadio 组件

```vue
<template>
  <!-- 基本用法 -->
  <DictionaryRadio
    v-model="formData.customerSource"
    :dictionary-type="DICTIONARY_TYPES.CUSTOMER_SOURCE"
  />
  
  <!-- 带禁用选项 -->
  <DictionaryRadio
    v-model="formData.workOrderType"
    :dictionary-type="DICTIONARY_TYPES.WORK_ORDER_TYPE"
    :disabled-options="['maintenance']"
  />
</template>

<script setup>
import DictionaryRadio from '@/components/common/DictionaryRadio.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
</script>
```

### 2. 使用组合函数

#### 单个字典类型

```vue
<template>
  <el-select v-model="selectedStatus" :loading="loading">
    <el-option
      v-for="option in options"
      :key="option.code"
      :value="option.code"
      :label="option.name"
    />
  </el-select>
  
  <!-- 显示选中项的名称 -->
  <p>选中的状态：{{ getNameByCode(selectedStatus) }}</p>
</template>

<script setup>
import { ref } from 'vue';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const selectedStatus = ref('');

const { 
  options, 
  loading, 
  getNameByCode 
} = useDictionary(DICTIONARY_TYPES.WORK_ORDER_STATUS);
</script>
```

#### 多个字典类型

```vue
<template>
  <div v-loading="loading">
    <!-- 优先级选择 -->
    <el-select v-model="formData.priority">
      <el-option
        v-for="option in getOptions(DICTIONARY_TYPES.WORK_ORDER_PRIORITY)"
        :key="option.code"
        :value="option.code"
        :label="option.name"
      />
    </el-select>
    
    <!-- 状态选择 -->
    <el-select v-model="formData.status">
      <el-option
        v-for="option in getOptions(DICTIONARY_TYPES.WORK_ORDER_STATUS)"
        :key="option.code"
        :value="option.code"
        :label="option.name"
      />
    </el-select>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const formData = ref({
  priority: '',
  status: ''
});

const { 
  loading, 
  getOptions, 
  getNameByCode 
} = useBatchDictionary([
  DICTIONARY_TYPES.WORK_ORDER_PRIORITY,
  DICTIONARY_TYPES.WORK_ORDER_STATUS
]);
</script>
```

## API 接口

### 获取字典数据

```typescript
// 单个字典类型
GET /api/v1/basic/dic/list?dictionaryType=0104

// 返回格式
{
  "success": true,
  "message": "操作成功",
  "code": "200",
  "traceId": "15efaf0f0051c225",
  "result": [
    {
      "code": "urgent",
      "name": "紧急"
    },
    {
      "code": "normal",
      "name": "普通"
    }
  ],
  "timestamp": 1751012536857
}
```

### 请求头

- `Accept-Language`: 当前选择的语言（zh/en/ms）

## 组合函数 API

### useDictionary

用于单个字典类型的数据获取。

```typescript
const {
  loading,           // 加载状态
  options,           // 字典选项数组
  error,             // 错误信息
  codes,             // 所有code列表
  names,             // 所有name列表
  hasData,           // 是否有数据
  fetchDictionary,   // 手动获取数据
  refresh,           // 刷新数据
  getNameByCode,     // 根据code获取name
  getCodeByName,     // 根据name获取code
  hasCode,           // 检查是否包含指定code
  hasName,           // 检查是否包含指定name
} = useDictionary(DICTIONARY_TYPES.WORK_ORDER_PRIORITY);
```

### useBatchDictionary

用于多个字典类型的批量数据获取。

```typescript
const {
  loading,              // 加载状态
  optionsMap,           // 字典选项映射
  error,                // 错误信息
  allHasData,           // 所有类型是否都有数据
  fetchBatchDictionary, // 手动获取数据
  refresh,              // 刷新数据
  getOptions,           // 获取指定类型的选项
  getNameByCode,        // 根据类型和code获取name
  getCodeByName,        // 根据类型和name获取code
  hasData,              // 检查指定类型是否有数据
} = useBatchDictionary([
  DICTIONARY_TYPES.WORK_ORDER_PRIORITY,
  DICTIONARY_TYPES.WORK_ORDER_STATUS
]);
```

## 工具函数

```typescript
import { 
  createOptionsWithAll, 
  createBooleanOptions, 
  createBooleanOptionsWithAll 
} from '@/composables/useDictionary';

// 创建带"全部"选项的字典选项
const optionsWithAll = createOptionsWithAll(options, '全部', '');

// 创建布尔值选项
const booleanOptions = createBooleanOptions('是', '否');

// 创建带"全部"的布尔值选项
const booleanOptionsWithAll = createBooleanOptionsWithAll('全部', '是', '否');
```

## 迁移指南

### 从静态配置迁移

**之前的代码：**
```vue
<template>
  <el-select v-model="priority">
    <el-option value="urgent" label="紧急" />
    <el-option value="normal" label="普通" />
  </el-select>
</template>
```

**迁移后的代码：**
```vue
<template>
  <DictionarySelect
    v-model="priority"
    :dictionary-type="DICTIONARY_TYPES.WORK_ORDER_PRIORITY"
  />
</template>

<script setup>
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
</script>
```

## 注意事项

1. **主数据字典**：车型、门店、销售顾问等主数据字典保持原有的API接口不变
2. **缓存机制**：字典数据会自动缓存，避免重复请求
3. **错误处理**：组合函数会自动处理错误并显示用户友好的提示
4. **国际化**：字典数据支持多语言，通过请求头 `Accept-Language` 控制
5. **类型安全**：所有字典类型都有TypeScript类型定义，确保类型安全

## 示例页面

查看 `src/components/examples/DictionaryUsageExample.vue` 获取完整的使用示例。
