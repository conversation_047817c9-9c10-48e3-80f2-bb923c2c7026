**销售业务流程：厂端潜客总览功能详细分析输出 (最终整合版)**

本文档详细分析厂端潜客总览业务功能，为厂端管理人员提供全局范围内的线索与门店潜客数据查看、跨门店关联分析和统计监控的能力。该功能是对现有门店级潜客管理的重要补充，旨在厂端层面进行数据洞察与决策支持，其数据来源于线索池及各门店的门店潜客库。

## 1. 用户旅程：不同角色参与的业务动作

* **厂端管理人员/区域经理 (Factory Manager):**
  - **查看全局统计概览数据**
    - Given: 厂端管理人员已登录并进入厂端潜客总览页面。
    - When: 厂端管理人员查看页面顶部的统计概览区域。
    - Then: 系统展示当前的线索总数、H级门店潜客数、本月成交门店潜客数、跨门店客户数等关键指标及其与上月对比的数据。
  - **查询和筛选列表**
    - Given: 厂端管理人员正在查看线索与门店潜客列表页面。
    - When: 厂端管理人员在筛选区域输入或选择查询条件（如客户ID/线索ID、门店、关联门店数、线索注册时间范围等），并点击"查询"按钮。
    - Then: 系统根据筛选条件更新并展示符合条件的线索或门店潜客列表。
  - **切换列表视图**
    - Given: 厂端管理人员正在查看线索与门店潜客列表页面。
    - When: 厂端管理人员点击不同的列表视图按钮（如"全部潜在客户"、"跨门店客户"、"无意向潜客"、"已成交"）。
    - Then: 系统根据选择的视图类型更新并展示相应的线索或门店潜客列表。
  - **重置筛选条件**
    - Given: 厂端管理人员已经在筛选区域输入或选择了查询条件。
    - When: 厂端管理人员点击"重置"按钮。
    - Then: 系统清空筛选区域的所有输入项，并将列表恢复到默认的展示状态。
  - **导出列表数据**
    - Given: 厂端管理人员已经应用了特定的查询或筛选条件，并查看符合条件的列表。
    - When: 厂端管理人员点击导出按钮。
    - Then: 系统将当前列表中的线索或门店潜客数据导出为文件（如Excel）。
  - **查看客户详细档案**
    - Given: 厂端管理人员正在查看线索与门店潜客列表。
    - When: 厂端管理人员点击列表中某个客户的"详情"按钮。
    - Then: 系统弹出一个模态框，展示该客户完整的整合档案信息，包括线索基本信息和在各关联门店的门店潜客详细记录（跟进、试驾、战败、归属变更历史、效果分析摘要）。
  - **查看和配置意向级别设置**
    - Given: 厂端管理人员需要查看或修改门店潜客意向级别的配置。
    - When: 厂端管理人员打开意向级别配置模块，并（如有权限）修改跟进时限或定义描述，然后保存。
    - Then: 系统展示当前意向级别的配置，并（如果修改并保存）更新配置规则，新规则对后续新设置意向级别的门店潜客生效。

### 销售顾问用户旅程 (Sales Consultant User Journey)

**场景1: 记录日常潜客跟进活动**

*   **Given** 销售顾问在其潜客列表中看到一个需要跟进的潜客。
*   **When** 销售顾问点击该潜客操作列中的“跟进”按钮，弹出跟进模态框，填写跟进方式、跟进时间、更新意向级别、设置下次跟进时间（系统提供建议值），并详细描述跟进情况，然后点击“保存”。
*   **Then** 系统的潜客档案中新增一条该潜客的跟进记录；该潜客的最新意向级别和下次跟进时间被更新；系统根据新的意向级别重新计算跟进时限，并将其纳入超时监控；页面显示保存成功的提示。

**场景2: 新增一个通过其他渠道获取的潜客**

*   **Given** 销售顾问通过线下活动等渠道获取了一个新的潜客信息，需要在系统中建立档案。
*   **When** 销售顾问点击页面上方的“新增”按钮，弹出新增潜客模态框；销售顾问在模态框中填写潜客的基本信息（姓名、手机号等）和**必须填写的初始意向级别**；然后点击“保存”。
*   **Then** 系统创建一个新的潜客档案，以手机号为唯一标识；将该潜客关联到当前销售顾问名下；潜客状态被设置为“已认领”（或其他适用状态）；系统记录认领操作和初始意向级别；该潜客被纳入到销售顾问的潜客列表和超时跟进监控中；页面显示新增成功的提示。

**场景3: 查看潜客的详细档案和历史记录**

*   **Given** 销售顾问需要了解某个潜客的完整画像、历史跟进情况及变更记录。
*   **When** 销售顾问在潜客列表的操作列中点击该潜客的“详情”按钮，弹出潜客档案详情模态框。
*   **Then** 模态框分多个 بخش (部分) 展示该潜客的基本信息、意向信息、试驾记录、**所有（包括前任顾问的）跟进记录**和归属/状态变更日志；销售顾问可以浏览这些历史数据；点击“关闭”按钮可退出详情视图。

**场景4: 申请将潜客状态变更为战败**

*   **Given** 销售顾问跟进某个处于“跟进中”状态的潜客后，判断客户已明确无购买意向或流失。
*   **When** 销售顾问在潜客列表的操作列中点击该潜客的“战败申请”按钮，弹出战败申请模态框；销售顾问在模态框中选择战败原因并填写详细说明（必填）；然后点击“提交申请”。
*   **Then** 潜客状态被系统变更为“申请战败中”；该潜客在销售顾问侧被锁定编辑权限；系统保存该战败申请的记录（申请人、时间、原因、备注等）；该申请进入销售经理的待审核列表；页面显示提交申请成功的提示，潜客在列表中状态更新。

**场景6: 销售顾问搜索认领战败状态的潜客（手动激活）**

*   **Given** 销售顾问在新增潜客或通过特定搜索功能找到了一个“已战败”状态的潜客，并希望重新激活跟进。
*   **When** 销售顾问选择认领该战败潜客。
*   **Then** 潜客归属被更新到该销售顾问名下；潜客状态被系统变更为“已认领”；该潜客重新出现在销售顾问的列表中；**销售顾问必须重新设置**该潜客的意向级别（H/A/B/C）；系统保留该潜客的所有跟进历史和原战败申请记录；该潜客被重新纳入跟进监控体系。

### 销售经理用户旅程 (Sales Manager User Journey)

**场景1: 变更门店潜客的所属销售顾问**

*   **Given** 销售经理需要调整门店潜客在顾问间的分配。
*   **When** 销售经理在潜客列表（或特定管理界面）选择一个潜客，点击“变更顾问”按钮，弹出变更顾问模态框；销售经理选择或填写新的销售顾问信息，并填写变更原因；然后点击“保存”。
*   **Then** 系统的潜客档案中更新该潜客的所属顾问信息；系统记录此次归属变更的历史（时间、操作人、原因）；**系统自动通知**新的销售顾问有新的潜客分配；该潜客在列表中所属顾问更新。

**场景2: 审核通过销售顾问提交的战败申请**

*   **Given** 销售经理在待审核列表中看到销售顾问提交的战败申请。
*   **When** 销售经理查看申请详情，包括战败原因、详细说明，并查看该潜客的完整跟进历史；销售经理评估后决定批准，点击“通过”按钮。
*   **Then** 潜客状态被系统变更为“战败”；该潜客被从超时跟进监控中移除；系统记录审核结果（审核人、时间）；**系统通知**原销售顾问申请已获批准；该潜客在列表中状态更新为“已战败”，排序移至末尾。

**场景3: 审核驳回销售顾问提交的战败申请**

*   **Given** 销售经理在待审核列表中看到销售顾问提交的战败申请。
*   **When** 销售经理查看申请详情和跟进历史；销售经理评估后决定驳回，点击“驳回”按钮，并填写驳回意见；然后点击“保存”。
*   **Then** 潜客状态被系统恢复为申请前的状态（如“已认领”）；原销售顾问对该潜客的编辑权限被恢复；系统记录审核结果（审核人、时间、审核意见）；**系统通知**原销售顾问申请被驳回及驳回原因；该潜客在列表中状态恢复正常。

**场景4: 销售经理重新分配战败状态的潜客（手动激活）**

*   **Given** 销售经理希望重新激活一个“已战败”状态的潜客。
*   **When** 销售经理在潜客列表（或特定管理界面）选择该战败潜客，执行“变更顾问”操作，选择新的销售顾问。
*   **Then** 潜客归属被更新到新的销售顾问名下；潜客状态被系统变更为“已认领”；该潜客重新出现在新顾问的列表中；系统保留该潜客的所有跟进历史和原战败申请记录；系统记录该激活操作（操作人、时间）；**系统通知**新的销售顾问有新的潜客分配；新的销售顾问**必须重新设置**该潜客的意向级别（H/A/B/C），将其重新纳入跟进监控体系。


## 2. 业务规则说明

### 1. **数据访问权限规则**
- 厂端管理人员具有全局数据查看权限（线索池、所有门店的门店潜客数据），不受门店限制。
- 区域经理管理一个区域， 一个区域由一家或者多家门店组成。区域经理只能查看所辖区域内的门店潜客数据。
- 所有厂端用户对门店潜客的业务数据通常只有只读权限，不能直接编辑。
- 厂端潜客信息无需脱敏。
- 系统记录所有查看和导出操作的审计日志。

### 2. **数据导出规则**
- 记录导出操作的详细日志（时间、用户、范围、字段）。

### 3. **意向级别与跟进预警规则**
- 各意向级别（H/A/B/C）对应不同的下次跟进时间跨度，具体规则由厂端配置（例如：H级 T+1天，A级 T+2天）。
- 预警/超时监控的判断标准为：销售顾问是否在设定的下次跟进日期（T日）结束前完成潜客跟进工作。 

## 3. 详细功能清单

### 一、厂端-潜客总览主页面功能点

1.  **统计概览数据展示**
    *   **接口维度:** 获取总览统计概览数据 (Get Overview Stats)
    *   **功能描述:** 页面顶部展示关键的线索与门店潜客统计数据。
    *   **语义和规则 (结合文档与新术语):**
        *   数据来源：聚合了**线索池**中的**线索**信息及所有**门店潜客库**中的**门店潜客**信息。
        *   展示内容：原型页面显示总线索数（或总潜在客户数）、H级门店潜客数、本月成交门店潜客数、跨门店客户数。总线索数较上月对比数、H级门店潜客数较上月对比数、本月成交的门店潜客转化率、跨门店客户占比数。
        *   字段计算规则:
               - TODO 开发阶段设计

2.  **列表查询/筛选 (线索与门店潜客)**
    *   **接口维度:** 查询/筛选列表 (Query/Filter List - Leads and Store Prospects)
    *   **功能描述:** 根据用户在筛选区域填写的条件（如客户ID/线索ID、门店、关联门店数、线索注册时间范围等），查询并展示符合条件的线索或列表。
    *   **语义和规则 (结合文档与新术语):**
        *   数据范围：查询范围是厂端用户总览所能访问的线索池中的线索和各门店的门店潜客数据。
        *   筛选条件：支持按客户全局ID、线索ID、门店潜客ID、门店、关联门店数（枚举值：单店、多店）、线索注册时间范围等进行筛选。
        *   列表展示：表格字段包括客户标识（如线索ID或客户全局ID）、姓名、手机号、线索注册区域、关联门店数、潜客状态、潜客建档时间。其中，“潜客状态”的枚举值为：跟进中、已成交、无意向。

3.  **列表视图切换**
    *   **接口维度:** 按类型筛选列表 (Filter List by Type)
    *   **功能描述:** 通过点击"全部潜在客户"、"跨门店客户"、"无意向潜客"、"已成交"按钮，快速切换列表的展示类型。
    *   **语义和规则 (结合文档与新术语):**
        *   "全部潜在客户"：展示所有线索数据
        *   "跨门店客户"：展示关联门店数>1的数据
        *   "无意向潜客"：展示已战败线索数据
        *   "已成交"：展示已成交线索数据

4.  **数据重置筛选条件**
    *   **接口维度:** （无需接口，客户端行为）
    *   **功能描述:** 点击"重置"按钮，清空筛选区域的所有输入和选择。

5.  **数据导出**
    *   **接口维度:** 导出数据 (Export Data - Leads and Store Prospects)
    *   **功能描述:** 将当前查询/筛选条件下的列表数据导出为文件（如Excel）。
    *   **语义和规则 (结合文档与新术语):** 导出的应为客户视图，可能包含线索信息及关键的门店潜客摘要信息。

6.  **列表分页**
    *   **接口维度:** 分页查询列表 (Paginate List Query)
    *   **功能描述:** 控制列表数据的分页显示，包括设置每页显示条数和切换页码。

### 二、厂端- 客户/门店潜客详情模态框功能点

通过点击列表行的"详情"按钮触发，展示的是一个"客户"的完整视图，整合其源头"线索"信息及在各门店的"门店潜客"记录。

1.  **获取客户基本信息 (源于线索)**
    *   **接口维度:** 获取客户基本信息 (Get Customer Basic Info from Lead)
    *   **功能描述:** 在详情模态框的"基本信息"标签页中展示客户的基础档案信息，主要来源于其在"线索池"中的原始"线索"记录。
    *   **语义和规则 (结合文档与新术语):**
        *   展示内容：包括线索ID（或客户全局ID）、姓名、手机号、身份证件类型、证件号码、邮箱、线索注册时间、线索来源渠道、线索当前状态（如未处理、已关联门店等）。
        *   关联门店数量 (Associated Store Count): 标识该线索关联的门店潜客记录数量（数值）。

2.  **获取客户的门店潜客关联记录**
    *   **接口维度:** 获取客户的门店潜客关联记录 (Get Customer's Store Prospect Association Records)
    *   **功能描述:** 在"门店关联"标签页中展示该客户所有关联的"门店潜客"列表及其在各门店的详情。
    *   **语义和规则 (结合文档与新术语):**
        *   展示内容：门店名称、门店潜客ID、线索关联时间、关联原因（通常为销售顾问操作）、当前负责的销售顾问、当前门店潜客的意向级别、最后跟进时间。

3.  **获取门店潜客的跟进记录**
    *   **接口维度:** 获取特定门店潜客的跟进记录 (Get Store Prospect Follow-up Records)
    *   **功能描述:** 在"跟进记录"标签页中展示该客户在各关联门店的"门店潜客"的跟进历史记录 (可能需要先选择一个门店潜客关联记录)。
    *   **语义和规则 (结合文档与新术语):**
        *   展示内容：门店、销售顾问、跟进方式、跟进时间、门店潜客意向级别、跟进情况。

4.  **获取门店潜客的试驾记录**
    *   **接口维度:** 获取特定门店潜客的试驾记录 (Get Store Prospect Test Drive Records)
    *   **功能描述:** 在"试驾记录"标签页中展示该客户在各关联门店的"门店潜客"的试驾记录。
    *   **语义和规则 (结合文档与新术语):**
        *   展示内容：门店、试驾人（通常为客户）、手机号、试驾车型、试驾时间、试驾反馈。

5.  **获取门店潜客的战败记录**
    *   **接口维度:** 获取特定门店潜客的战败申请及审核记录 (Get Store Prospect Failure Records)
    *   **功能描述:** 在"战败记录"标签页中展示该客户关联的"门店潜客"的战败申请和审核历史。
    *   **语义和规则 (结合文档与新术语):**
        *   展示内容：标记时间、门店、标记人、战败原因。

6.  **获取门店潜客的归属变更历史**
    *   **接口维度:** 获取特定门店潜客的归属变更历史 (Get Store Prospect Assignment Change History)
    *   **功能描述:** 在"变更历史"标签页中展示该客户关联的"门店潜客"在其所属门店的归属变更和意向级别变化历史。
    *   **语义和规则 (结合文档与新术语):**
        *   展示内容：变更时间、门店、变更类型（如顾问分配、意向级别）、操作人、变更前值、变更后值。

7.  **获取客户效果分析数据**
    *   **接口维度:** 获取客户效果分析数据 (Get Customer Performance Analysis Data)
    *   **功能描述:** 在"效果分析"标签页中展示针对该客户（综合其所有门店潜客记录）的简化分析结果。
    *   **语义和规则 (结合文档与新术语):**
        *   展示内容：总跟进次数（客户关联的所有门店潜客记录的跟进总数）和最活跃门店（该客户的门店潜客记录中，跟进次数最多的门店）。

8.  **导出客户详情**
    *   **接口维度:** 导出客户详情数据 (Export Customer Detail Data)
    *   **功能描述:** 将当前客户的详细信息（整合线索和所有门店潜客记录）及各标签页内容导出。

### 三、厂端 - 意向级别配置模态框功能点 (针对门店潜客)

1.  **获取门店潜客意向级别配置**
    *   **接口维度:** 获取门店潜客意向级别配置 (Get Store Prospect Intent Level Configuration)
    *   **功能描述:** 打开模态框时，加载当前系统中各门店潜客意向级别（H/A/B/C）的跟进时限和定义描述配置。
    *   **语义和规则 (结合文档与新术语):** 这些配置应用于门店潜客。

2.  **保存门店潜客意向级别配置**
    *   **接口维度:** 保存门店潜客意向级别配置 (Save Store Prospect Intent Level Configuration)
    *   **功能描述:** 点击"保存配置"按钮，提交用户修改的各门店潜客意向级别的跟进时限和定义描述。
    *   **语义和规则 (结合文档与新术语):**
        *   配置项：跟进时限（小时，数值输入）和定义描述（文本输入）。
        *   规则：配置变更无需审批流程和版本管理。
        *   生效范围：新规则只对规则变更后**新设置**意向级别的门店潜客生效，不影响已存在的门店潜客跟进计划。

### 一、店端 - 主页面功能点

主页面主要展示潜客列表，并提供筛选、查询、新增、导出和基本的操作入口。

1.  **潜客列表展示**
    *   **页面元素:** `<table class="table">` 及其内部的 `<thead>` 和 `<tbody>`。
    *   **功能点维度:** `获取潜客列表` (Get Prospect List)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   列表应展示销售顾问名下（或销售经理名下）的潜客信息。
        *   列表字段包括：复选框、序号、来源渠道、潜客分配时间、上次跟进时间、潜客级别（带标签颜色）、潜客状态（带标签颜色）、潜客名称、潜客手机号、State、意向Model、意向Variant、意向Color、所属顾问工号、所属顾问名称、下次跟进时间、操作列。
        *   支持固定列（复选框、序号和操作列）。
        *   超时潜客（根据意向级别和跟进时限判断）在列表中置顶高亮显示，按超时时长倒序排列。
        *   战败和已下单状态的潜客排序在列表最后。
        *   销售顾问仅可查看/编辑自己名下潜客；销售经理可查看/编辑门店所有潜客；厂端管理人员可查看所有潜客数据但不可编辑门店业务数据（页面原型似乎仅展示了顾问/经理视角的数据）。

2.  **潜客列表筛选和查询**
    *   **页面元素:** `<div class="filter-section">` 中的表单元素 (`customerName`, `customerPhone`, `sourceChannel`, `customerLevel`, `customerStatus`) 和“查询”、“重置”按钮。
    *   **功能点维度:** `查询潜客列表` (Query Prospect List)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   提供潜客名称、手机号、来源渠道、潜客级别、潜客状态等筛选条件。
        *   支持根据输入的条件查询匹配的潜客数据。
        *   “重置”按钮应清空所有筛选条件并重新加载列表（或显示所有数据）。

3.  **潜客列表排序**
    *   **页面元素:** 表格头部带有 `sortable` 类的 `<th>` 元素（原型中为“潜客级别”列）。
    *   **功能点维度:** `潜客列表排序` (Sort Prospect List)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   原型中仅明确支持按潜客级别进行升序/降序排序。
        *   根据需求文档，列表默认或可通过排序按意向级别高低（H>A>B>C）排列，战败和已下单排在最后。

4.  **潜客数据导出**
    *   **页面元素:** "导出"按钮 (`<button class="btn btn-outline-secondary me-2" onclick="exportData()">`)。
    *   **功能点维度:** `导出潜客数据` (Export Prospect Data)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   支持将潜客列表数据导出（通常为Excel或CSV格式）。
        *   需求文档中未详细描述导出的具体规则和范围，但根据页面应是导出当前列表中的数据。

5.  **潜客列表日期过滤**
    *   **页面元素:** 日期筛选标签页按钮 (`<button class="tab-button">`).
    *   **功能点维度:** `按日期过滤潜客列表` (Filter Prospect List by Date)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   提供“全部”、“今日”、“逾期”等日期维度进行过滤。
        *   具体过滤规则可能与跟进时间或下次跟进时间相关，需求文档中提到“超时潜客”的预警是基于跟进时限，但此处日期过滤的具体逻辑（例如“今日”是指今日分配的、今日跟进的、还是今日需跟进的）需与业务进一步明确。原型代码中的 `filterByDate` 函数也只是模拟切换视图。

6.  **分页功能**
    *   **页面元素:** `<div class="pagination-section">` 中的元素（每页显示条数选择、总记录数显示、上一页/下一页按钮）。
    *   **功能点维度:** `分页浏览潜客列表` (Paginate Prospect List)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   支持按页码和每页显示条数浏览潜客列表。
        *   显示总记录数。

---

### 二、店端 - 模态框功能点

页面中包含多个模态框，提供更详细的操作和信息查看功能。

1.  **新增潜客模态框 (`#addCustomerModal`)**
    *   **页面元素:** 模态框内部的“潜客查询”区域和“新增潜客表单”区域，以及“取消”、“保存”按钮。
    *   **功能点维度:**
        *   `搜索现有潜客` (Search Existing Prospect)
        *   `创建新潜客档案` (Create New Prospect Profile)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   **潜客查询:** 支持按潜客名称、手机号进行查询，用于检查潜客是否已存在于厂端潜客池中（需求文档中提到销售顾问只能通过新增时搜索认领未认领的潜客）。查询结果可能用于自动填充新增表单。
        *   **新增表单:** 包含必填项（名称、手机号、身份证件类别/号、邮箱、意向级别）和其他信息。
        *   以手机号作为潜客唯一标识，系统不进行跨账号的人员身份识别。
        *   销售顾问认领（新增）潜客时必须设置初始意向级别（H/A/B/C）。
        *   需进行前端表单验证（包括手机号、邮箱格式）。
        *   保存操作应将新潜客信息提交至系统，并将其关联到当前销售顾问名下，状态设为“已认领”（或其他适用状态）。

2.  **潜客跟进模态框 (`#followUpModal`)**
    *   **页面元素:** 模态框内部的“潜客信息”、“潜客意向”、“跟进记录”区域，以及“取消”、“保存”按钮。
    *   **功能点维度:** `记录潜客跟进活动` (Record Prospect Follow-up Activity)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   展示潜客基本信息和当前意向信息。
        *   提供跟进记录表单，包含必填项（跟进方式、时间、意向级别、下次跟进时间、跟进情况）。
        *   支持选择跟进方式（电话、社交软件、到店面谈、上门拜访等）。
        *   跟进时间应可选择具体的日期和时间。
        *   跟进时需填写当前的意向级别。
        *   下次跟进时间应根据填写的意向级别和跟进时间自动计算建议值，但用户可修改（原型JS代码中实现了这个逻辑）。
        *   需详细描述跟进情况。
        *   保存操作应将跟进记录添加到该潜客的档案中，并更新潜客的意向级别和下次跟进时间。
        *   跟进记录需包含：跟进方式、跟进时间、跟进情况、潜客意向级别。
        *   试驾不自动生成跟进记录，需手动添加。
        *   保存操作应触发相应的业务逻辑，如更新跟进状态、重新计算跟进时限等。

3.  **变更顾问模态框 (`#changeConsultantModal`)**
    *   **页面元素:** 模态框内部的当前顾问信息、变更表单（变更顾问工号、变更顾问名字），以及“取消”、“保存”按钮。
    *   **功能点维度:** `变更潜客所属顾问` (Change Prospect Assigned Consultant)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   展示当前潜客的所属顾问信息。
        *   提供输入框和下拉选择框用于选择新的销售顾问（原型中工号和名字联动）。
        *   变更操作需记录归属变更历史（时间、操作人、原因）。
        *   变更顾问时系统自动通知新顾问。
        *   变更无频率和时间限制。
        *   销售经理可逐个变更潜客归属顾问。
        *   保存操作应更新潜客的所属顾问字段。

4.  **战败潜客申请模态框 (`#defeatModal`)**
    *   **页面元素:** 模态框内部展示潜客信息、顾问信息、申请时间，以及战败原因选择框和详细说明文本域，以及“取消”、“提交申请”按钮。
    *   **功能点维度:** `申请潜客战败` (Apply for Prospect Defeat)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   展示要申请战败的潜客和所属顾问信息。
        *   申请需选择战败原因（原型中提供了预设选项）并填写详细说明（必填，500字以内）。
        *   申请提交后，潜客状态变为“申请战败中”，销售顾问无法编辑该潜客。
        *   申请提交后需保存申请记录（申请时间、申请人、战败原因、备注信息）。
        *   战败申请一旦提交不可撤回。
        *   只有“跟进中”状态的潜客才能发起战败申请（原型中通过JS控制了按钮显示）。
        *   销售顾问仅可申请自己名下潜客战败。

5.  **潜客档案详情模态框 (`#customerDetailModal`)**
    *   **页面元素:** 模态框内部包含多个信息分组（潜客信息、潜客意向、试驾记录、跟进记录、变更日志），每个分组内有表格或表单展示数据，以及“关闭”按钮。
    *   **功能点维度:** `查看潜客档案详情` (View Prospect Profile Details)
    *   **需求语义与规则 (结合 `潜客获取和潜客跟进.md`): **
        *   应展示潜客的完整档案信息。
        *   **潜客信息:** 包含潜客来源、状态、名称、手机号、身份证件类别/号、邮箱、地区、地址等。
        *   **潜客意向:** 以表格形式展示意向车型、Variant、Color。
        *   **试驾记录:** 以表格形式展示试驾人、手机号、车型、时间、反馈。
        *   **跟进记录:** 以表格形式展示顾问、跟进方式、时间、意向级别、跟进情况。应包含该潜客在本门店（或跨门店，根据权限）的所有跟进历史。销售顾问可查看前任顾问的完整跟进历史。
        *   **变更日志:** 以表格形式展示潜客归属或状态的变更历史（变更内容、原始信息、变更后信息、操作人、操作时间）。需求文档明确记录潜客归属变更历史。
        *   厂端管理人员可查看全局潜客档案详情，包括跨门店关联记录、全局跟进详情、归属变更历史（厂端视角的详情比店端更全面，原型中展示的似乎是店端顾问/经理视角）。


## 4. 核心对象（只标注了核心字段）
1.  **线索表 (Leads)**
    *   **用途：** 存储客户的原始线索信息。
    *   **字段：**
        *   线索ID / 客户全局ID (Lead ID / Global Customer ID): 唯一标识一个线索/客户。
        *   姓名 (Name): 客户姓名
        *   手机号 (Phone Number): 客户手机号
        *   身份证件类型 (ID Type): 客户身份证件类型。
        *   证件号码 (ID Number): 客户证件号码。
        *   邮箱 (Email): 客户邮箱。
        *   线索注册时间 (Registration Time): 线索创建时间。
        *   线索来源渠道 (Source Channel): 线索的来源（如APP、网站等）。
        *   线索注册区域 (Registration Area): 线索注册时的区域。
        *   当前状态 (Current Status): 线索的当前处理状态（如未处理、已关联门店等）。
        *   关联门店数 (Associated Store Count): 标识该线索关联的门店潜客记录数量（数值）。

2.  **门店潜客表 (StoreProspects)**
    *   **用途：** 存储某个客户在具体某个门店的潜客记录。一个客户可能在多个门店有潜客记录（跨门店客户）。
    *   **字段：**
        *   门店潜客ID (Store Prospect ID): 唯一标识某个客户在某个门店的潜客记录。
        *   客户全局ID (Global Customer ID): 关联到线索表中的客户。
        *   门店ID / 门店名称 (Store ID / Store Name): 关联到具体的门店。
        *   线索关联时间 (Lead Association Time): 该线索被关联到此门店的时间。
        *   关联原因 (Association Reason): 为什么该线索被关联到此门店（如自动分配、客户到店等）。
        *   当前负责销售顾问ID / 姓名 (Current Sales Advisor ID / Name): 负责跟进该潜客的销售顾问。
        *   当前意向级别 (Current Intent Level): 门店潜客当前的意向级别 (H/A/B/C/F等)。
        *   最后跟进时间 (Last Follow-up Time): 最近一次跟进该门店潜客的时间。
        *   最新转化状态 (Latest Conversion Status): 该门店潜客是否已转化（成交/战败）。

3.  **门店潜客跟进记录表 (StoreProspectFollowUpRecords)**
    *   **用途：** 存储销售顾问对某个门店潜客进行的每一次跟进记录。
    *   **字段：**
        *   记录ID (Record ID): 唯一标识一条跟进记录。
        *   门店潜客ID (Store Prospect ID): 关联到具体的门店潜客记录。
        *   门店ID / 门店名称 (Store ID / Store Name): 跟进发生在哪家门店。
        *   销售顾问ID / 姓名 (Sales Advisor ID / Name): 进行跟进的销售顾问。
        *   跟进方式 (Follow-up Method): 跟进的方式（如电话、微信、到店等）。
        *   跟进时间 (Follow-up Time): 跟进发生的时间。
        *   本次跟进后的意向级别 (Intent Level After Follow-up): 跟进后更新的意向级别。
        *   跟进情况 (Follow-up Details): 对本次跟进过程和结果的描述。

