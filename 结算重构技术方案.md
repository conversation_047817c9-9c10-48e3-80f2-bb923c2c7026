# 结算重构技术方案

基于页面目录结构规范和页面重构技术规范，对 `src/views/settlement/` 模块进行重构的完整技术方案。

> **模块归属说明**: 结算管理作为售后服务流程的重要环节，归属于 `afterSales` 模块，将重构到 `src/views/afterSales/settlement/` 目录下。

## 1. 模块归属说明

### 1.1 为什么结算归属于售后模块？

结算管理作为售后服务流程的核心环节，与售后业务紧密相关：

- **业务流程**: 结算是售后服务（维修、保养、质检）完成后的必要步骤
- **数据关联**: 结算单直接关联工单、质检结果、技师工时等售后数据
- **用户角色**: 主要由售后服务顾问、技师等售后人员操作
- **功能定位**: 属于售后服务管理的收尾环节，而非独立的财务管理

因此，将结算模块归属于 `afterSales` 模块更符合业务逻辑和用户使用习惯。

## 2. 现状分析

### 2.1 当前目录结构
```
src/views/settlement/
├── SettlementManagementView.vue        # 主页面文件（634行）
└── components/
    ├── PaymentManagementModal.vue      # 收退款管理对话框
    └── SettlementDetailModal.vue       # 结算详情对话框
```

### 2.2 相关文件分布
```
src/api/modules/settlement.ts           # 结算API模块（已存在）
src/types/module.d.ts                   # 类型定义（分散在通用文件中）
src/mock/data/settlement.ts             # Mock数据（已存在）
src/locales/modules/afterSales/         # 国际化文件（需要扩展afterSales模块）
```

### 2.3 问题识别
- ❌ 页面位置不符合规范：应该在 `afterSales` 模块下
- ❌ 使用 `useI18n()` 而非 `useModuleI18n()`
- ❌ 单文件组件过于庞大（634行），缺乏组件化拆分
- ❌ 类型定义分散在 `module.d.ts` 中
- ❌ 缺乏专门的国际化模块集成
- ❌ Mock数据与API模块集成度不高

## 3. 重构目标结构

### 3.1 目标目录结构
```
src/
├── views/afterSales/settlement/
│   ├── SettlementManagementView.vue    # 主页面（路由页面）
│   └── components/
│       ├── SettlementSearchForm.vue    # 搜索表单组件
│       ├── SettlementTable.vue         # 结算表格组件
│       ├── SettlementDetailDialog.vue  # 结算详情对话框
│       ├── PaymentManagementDialog.vue # 收退款管理对话框
│       ├── SettlementStatsCard.vue     # 统计卡片组件
│       ├── LaborItemsTable.vue         # 工时明细表格组件
│       ├── PartItemsTable.vue          # 零件明细表格组件
│       └── OperationLogTable.vue       # 操作日志表格组件
├── api/modules/afterSales/
│   └── settlement.ts                   # 重构后的API模块
├── types/afterSales/
│   └── settlement.d.ts                 # 统一的类型定义
├── mock/data/afterSales/
│   └── settlement.ts                   # 重构后的Mock数据
└── locales/modules/afterSales/
    ├── zh.json                          # 中文翻译（扩展settlement模块）
    └── en.json                          # 英文翻译（扩展settlement模块）
```

## 4. 重构执行计划

### 4.1 阶段一：基础结构创建

#### 步骤1：创建目录结构
```bash
# 创建新的目录结构
mkdir -p src/views/afterSales/settlement/components
mkdir -p src/api/modules/afterSales
mkdir -p src/types/afterSales
mkdir -p src/mock/data/afterSales
# 注意：afterSales国际化模块已存在，只需扩展
```

#### 步骤2：统一类型定义
将分散的类型定义统一到 `src/types/afterSales/settlement.d.ts`：

```typescript
// 基础类型定义
export type SettlementStatus = 'pre_settlement' | 'pending_settlement' | 'completed' | 'cancelled';
export type PaymentStatus = 'pending' | 'deposit_paid' | 'fully_paid' | 'refunding' | 'refunded';
export type WorkOrderType = 'maintenance' | 'repair' | 'warranty';
export type PaymentMethod = 'cash' | 'pos' | 'wechat' | 'alipay' | 'bank_transfer';

// 结算单列表项接口
export interface SettlementListItem {
  id: string;
  settlementNo: string;
  workOrderNo: string;
  workOrderType: WorkOrderType;
  settlementStatus: SettlementStatus;
  paymentStatus: PaymentStatus;
  customerName: string;
  customerPhone: string;
  vehiclePlate: string;
  vehicleModel: string;
  vehicleConfig: string;
  vehicleColor: string;
  technician: string;
  serviceAdvisor: string;
  totalAmount: number;
  paidAmount: number;
  payableAmount: number;
  createdAt: string;
  inspectionStatus: string;
}

// 搜索参数接口
export interface SettlementSearchParams {
  page: number;
  pageSize: number;
  settlementNo?: string;
  workOrderNo?: string;
  settlementStatus?: SettlementStatus;
  paymentStatus?: PaymentStatus;
  customerName?: string;
  vehiclePlate?: string;
  technician?: string;
  serviceAdvisor?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  amountMin?: number;
  amountMax?: number;
}

// 结算详情接口
export interface SettlementDetail {
  id: string;
  settlementNo: string;
  workOrderNo: string;
  workOrderType: WorkOrderType;
  settlementStatus: SettlementStatus;
  paymentStatus: PaymentStatus;
  createdAt: string;
  serviceAdvisor: string;
  
  // 客户信息
  customerName: string;
  customerPhone: string;
  
  // 车辆信息
  vehiclePlate: string;
  vin: string;
  vehicleModel: string;
  vehicleConfig: string;
  vehicleColor: string;
  vehicleAge: string;
  mileage: string;
  
  // 服务包信息
  servicePackage?: {
    packageCode: string;
    packageName: string;
  };
  
  // 费用明细
  laborItems: SettlementLaborItem[];
  partItems: SettlementPartItem[];
  
  // 费用汇总
  laborTotalAmount: number;
  laborReceivableAmount: number;
  partTotalAmount: number;
  partReceivableAmount: number;
  warrantyAmount: number;
  totalAmount: number;
  packageRightsDeduction: number;
  receivableTotal: number;
  payableAmount: number;
  paidAmount: number;
  discountAmount: number;
  remarks: string;
  
  // 操作日志
  operationLogs: OperationLog[];
}

// 工时明细接口
export interface SettlementLaborItem {
  id: string;
  laborCode: string;
  laborName: string;
  standardHours: number;
  actualHours: number;
  unitPrice: number;
  totalAmount: number;
  receivableAmount: number;
  warrantyAmount: number;
  discountAmount: number;
  remarks: string;
}

// 零件明细接口
export interface SettlementPartItem {
  id: string;
  partCode: string;
  partName: string;
  specification: string;
  unit: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  receivableAmount: number;
  warrantyAmount: number;
  discountAmount: number;
  remarks: string;
}

// 收退款记录接口
export interface PaymentRecord {
  id: string;
  paymentNo: string;
  businessType: '收款' | '退款';
  transactionNo: string;
  paymentMethod: PaymentMethod;
  amount: number;
  paymentType: string;
  paymentTime: string;
  remarks: string;
  createdAt: string;
}

// 收退款表单接口
export interface PaymentForm {
  settlementId: string;
  businessType: '收款' | '退款';
  paymentMethod: PaymentMethod;
  amount: number;
  paymentType: string;
  remarks?: string;
}

// 操作日志接口
export interface OperationLog {
  id: string;
  operationType: string;
  operationTime: string;
  operator: string;
  operationContent: string;
  remarks: string;
}

// 统计信息接口
export interface SettlementStatistics {
  todayTotal: number;
  todayCompleted: number;
  todayAmount: number;
  weekTotal: number;
  weekCompleted: number;
  weekAmount: number;
  monthTotal: number;
  monthCompleted: number;
  monthAmount: number;
  completionRate: number;
  avgSettlementAmount: number;
}

// 分页响应接口
export interface PaginationResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

// 导出参数接口
export interface ExportSettlementParams extends SettlementSearchParams {
  exportType: 'all' | 'selected';
  exportFormat: 'excel' | 'pdf';
  selectedIds?: string[];
}
```

### 4.2 阶段二：API模块重构

#### 步骤3：重构API模块
创建 `src/api/modules/afterSales/settlement.ts`：

```typescript
import request from '@/api';
import type {
  SettlementListItem,
  SettlementSearchParams,
  SettlementDetail,
  PaymentForm,
  PaymentRecord,
  SettlementStatistics,
  PaginationResponse,
  ApiResponse,
  ExportSettlementParams
} from '@/types/afterSales/settlement';
import {
  getMockSettlementList,
  getMockSettlementDetail,
  getMockPaymentRecords,
  processMockPayment,
  getMockSettlementStats,
  exportMockSettlementData
} from '@/mock/data/afterSales/settlement';

// 临时使用Mock数据开关
let USE_MOCK_API_TEMP = true;

// 获取结算单列表
export const getSettlementList = (
  params: SettlementSearchParams
): Promise<PaginationResponse<SettlementListItem>> => {
  if (USE_MOCK_API_TEMP) {
    return getMockSettlementList(params);
  }
  return request.get<any, PaginationResponse<SettlementListItem>>(
    '/after-sales/settlement/list',
    { params }
  );
};

// 获取结算单详情
export const getSettlementDetail = (id: string): Promise<SettlementDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockSettlementDetail(id);
  }
  return request.get<any, SettlementDetail>(`/after-sales/settlement/detail/${id}`);
};

// 推送结算单
export const pushSettlement = (id: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '结算单推送成功'
        });
      }, 1000);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/settlement/push/${id}`);
};

// 处理收退款
export const processPayment = (data: PaymentForm): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return processMockPayment(data);
  }
  return request.post<any, ApiResponse>('/after-sales/settlement/payment', data);
};

// 获取收退款记录
export const getPaymentRecords = (settlementId: string): Promise<PaymentRecord[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockPaymentRecords(settlementId);
  }
  return request.get<any, PaymentRecord[]>(`/after-sales/settlement/payment-records/${settlementId}`);
};

// 获取结算统计
export const getSettlementStatistics = (): Promise<SettlementStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockSettlementStats();
  }
  return request.get<any, SettlementStatistics>('/after-sales/settlement/statistics');
};

// 导出结算数据
export const exportSettlementData = (params: ExportSettlementParams): Promise<Blob> => {
  if (USE_MOCK_API_TEMP) {
    return exportMockSettlementData(params);
  }
  return request.get<any, Blob>(
    '/after-sales/settlement/export',
    {
      params,
      responseType: 'blob'
    }
  );
};
```

### 4.3 阶段三：Mock数据重构

#### 步骤4：重构Mock数据
创建 `src/mock/data/afterSales/settlement.ts`：

```typescript
import type {
  SettlementListItem,
  SettlementSearchParams,
  SettlementDetail,
  PaymentForm,
  PaymentRecord,
  SettlementStatistics,
  PaginationResponse,
  ApiResponse,
  ExportSettlementParams,
  SettlementStatus,
  PaymentStatus,
  WorkOrderType
} from '@/types/afterSales/settlement';

// 动态生成结算Mock数据
function generateMockSettlementData(): SettlementListItem[] {
  const dataCount = Math.floor(Math.random() * 30) + 40; // 40-70条数据
  const mockData: SettlementListItem[] = [];
  
  const statuses: SettlementStatus[] = ['pre_settlement', 'pending_settlement', 'completed', 'cancelled'];
  const paymentStatuses: PaymentStatus[] = ['pending', 'deposit_paid', 'fully_paid', 'refunding', 'refunded'];
  const workOrderTypes: WorkOrderType[] = ['maintenance', 'repair', 'warranty'];
  const vehicleModels = ['Model S', 'Model X', 'Model 3', 'Model Y', 'Cybertruck'];
  const colors = ['珍珠白', '深空灰', '中国红', '冷光银', '深海蓝'];
  const technicians = ['李技师', '王技师', '张技师', '赵技师', '陈技师'];
  const advisors = ['陈经理', '王顾问', '李经理', '赵顾问', '刘经理'];
  const customers = ['张三', '李四', '王五', '赵六', '陈七', '刘八'];
  
  for (let i = 0; i < dataCount; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
    const workOrderType = workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)];
    
    const totalAmount = Math.round((Math.random() * 2000 + 500) * 100) / 100;
    const paidAmount = paymentStatus === 'fully_paid' ? totalAmount : 
                     paymentStatus === 'deposit_paid' ? Math.round(totalAmount * 0.5 * 100) / 100 : 0;
    const payableAmount = totalAmount - paidAmount;
    
    mockData.push({
      id: `settlement-${i + 1}`,
      settlementNo: `ST${String(Date.now() + i).slice(-8)}`,
      workOrderNo: `WO${String(i + 1).padStart(6, '0')}`,
      workOrderType,
      settlementStatus: status,
      paymentStatus,
      customerName: customers[Math.floor(Math.random() * customers.length)],
      customerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      vehiclePlate: `粤B${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfig: Math.random() > 0.5 ? '标准版' : '长续航版',
      vehicleColor: colors[Math.floor(Math.random() * colors.length)],
      technician: technicians[Math.floor(Math.random() * technicians.length)],
      serviceAdvisor: advisors[Math.floor(Math.random() * advisors.length)],
      totalAmount,
      paidAmount,
      payableAmount,
      createdAt: createTime.toISOString(),
      inspectionStatus: Math.random() > 0.2 ? '质检通过' : '质检中'
    });
  }
  
  return mockData;
}

const mockSettlementData = generateMockSettlementData();

export const getMockSettlementList = (
  params: SettlementSearchParams
): Promise<PaginationResponse<SettlementListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockSettlementData];
      
      if (params.settlementNo) {
        filteredData = filteredData.filter(item => 
          item.settlementNo.toLowerCase().includes(params.settlementNo!.toLowerCase())
        );
      }
      
      if (params.workOrderNo) {
        filteredData = filteredData.filter(item => 
          item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
        );
      }
      
      if (params.settlementStatus) {
        filteredData = filteredData.filter(item => item.settlementStatus === params.settlementStatus);
      }
      
      if (params.paymentStatus) {
        filteredData = filteredData.filter(item => item.paymentStatus === params.paymentStatus);
      }
      
      if (params.customerName) {
        filteredData = filteredData.filter(item => 
          item.customerName.includes(params.customerName!)
        );
      }
      
      if (params.vehiclePlate) {
        filteredData = filteredData.filter(item => 
          item.vehiclePlate.includes(params.vehiclePlate!)
        );
      }
      
      if (params.technician) {
        filteredData = filteredData.filter(item => 
          item.technician.includes(params.technician!)
        );
      }
      
      if (params.serviceAdvisor) {
        filteredData = filteredData.filter(item => 
          item.serviceAdvisor.includes(params.serviceAdvisor!)
        );
      }
      
      // 时间范围过滤
      if (params.createTimeStart) {
        filteredData = filteredData.filter(item => 
          item.createdAt >= params.createTimeStart!
        );
      }
      
      if (params.createTimeEnd) {
        filteredData = filteredData.filter(item => 
          item.createdAt <= params.createTimeEnd!
        );
      }
      
      // 金额范围过滤
      if (params.amountMin !== undefined) {
        filteredData = filteredData.filter(item => item.totalAmount >= params.amountMin!);
      }
      
      if (params.amountMax !== undefined) {
        filteredData = filteredData.filter(item => item.totalAmount <= params.amountMax!);
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        data: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 500);
  });
};
```

## 5. 国际化集成

### 5.1 扩展afterSales模块国际化
在 `src/locales/modules/afterSales/zh.json` 中添加settlement模块：

```json
{
  "settlement": {
    "title": "结算管理",
    "subtitle": "结算单管理与收退款处理",
    "settlementNo": "结算单号",
    "workOrderNo": "工单编号",
    "settlementStatus": "结算状态",
    "paymentStatus": "支付状态",
    "customerName": "客户姓名",
    "customerPhone": "客户电话",
    "vehiclePlate": "车牌号",
    "vehicleModel": "车型",
    "technician": "技师",
    "serviceAdvisor": "服务顾问",
    "totalAmount": "结算金额",
    "paidAmount": "已付金额",
    "payableAmount": "应付金额",
    "createdAt": "创建时间",
    "status": {
      "pre_settlement": "预结算",
      "pending_settlement": "待结算",
      "completed": "已完成",
      "cancelled": "已取消"
    },
    "paymentStatus": {
      "pending": "待支付",
      "deposit_paid": "已付定金",
      "fully_paid": "已付全款",
      "refunding": "退款中",
      "refunded": "已退款"
    },
    "actions": {
      "push": "推送结算",
      "payment": "收退款",
      "detail": "详情",
      "export": "导出"
    },
    "messages": {
      "pushSuccess": "结算单推送成功",
      "paymentSuccess": "收退款处理成功",
      "exportSuccess": "数据导出成功"
    }
  }
}
```

### 5.2 更新页面国际化引用
```typescript
// 修改前
const { t: $t } = useI18n();

// 修改后
const { t, tc } = useModuleI18n('afterSales');

// 使用方式
t('settlement.title')        // 结算相关翻译
tc('search')                 // 通用翻译
```

## 6. 路由配置更新

### 6.1 更新路由路径
```typescript
// src/router/index.ts (afterSales模块路由)
{
  path: '/after-sales/settlement',
  name: 'SettlementManagement',
  component: () => import('@/views/afterSales/settlement/SettlementManagementView.vue'),
  meta: {
    title: 'menu.settlementManagement',
    requiresAuth: true,
    icon: 'Money'
  }
}
```

## 7. 重构检查清单

### 7.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/settlement/SettlementManagementView.vue`
- [ ] 组件文件拆分并放置在 `components/` 目录下
- [ ] API模块移动到 `src/api/modules/afterSales/settlement.ts`
- [ ] 类型定义统一到 `src/types/afterSales/settlement.d.ts`
- [ ] Mock数据移动到 `src/mock/data/afterSales/settlement.ts`
- [ ] 国际化文件扩展 `src/locales/modules/afterSales/`

### 7.2 代码质量验证
- [ ] 使用 `useModuleI18n('afterSales')` 替换 `useI18n()`
- [ ] 所有翻译键更新为 `t('settlement.*')`
- [ ] TypeScript类型安全，无编译错误
- [ ] Mock数据支持动态生成和完整的搜索分页功能
- [ ] API模块与Mock数据完全集成

### 7.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 收退款功能正常
- [ ] 详情查看功能正常
- [ ] 国际化切换正常

### 7.4 路由验证
- [ ] 路由路径更新为 `/after-sales/settlement`
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 8. 迁移注意事项

### 8.1 数据兼容性
- 确保新的类型定义与现有数据结构兼容
- Mock数据格式保持与原有API响应一致
- 分页参数和响应格式标准化

### 8.2 功能保持
- 保持所有现有业务功能不变
- 确保结算流程逻辑完整
- 维持用户体验一致性

### 8.3 性能优化
- 合理使用组件懒加载
- 优化大列表渲染性能
- 实现合理的缓存策略

---

**本重构方案遵循DMS前端项目的标准化规范，将结算管理模块正确归属于售后模块，确保代码结构清晰、可维护性强，并与整体项目架构保持一致。**
