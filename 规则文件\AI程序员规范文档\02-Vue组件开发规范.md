# Vue组件开发规范

> **📍 工作流程对应**：本文档主要对应标准工作流程的第5步和第7步
> - **第5步：开发页面组件** - 使用统一模板和规范
> - **第7步：配置路由** - 如需要，在路由文件中添加配置

---

## 🧩 组件开发核心规范

### 1. 标准组件模板

```vue
<template>
  <div class="component-container">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
// 1. 导入Vue相关
import { ref, reactive, computed, watch, onMounted } from 'vue'

// 2. 导入Element Plus组件
import { ElMessage, ElMessageBox } from 'element-plus'

// 3. 导入国际化
import { useModuleI18n } from '@/composables/useModuleI18n'

// 4. 导入API
import { getDataList, saveData } from '@/api/modules/[module]/[feature]'

// 5. 导入类型
import type { DataItem, SearchParams } from '@/types/[module]/[feature]'

// 6. 导入其他工具
import { useRouter } from 'vue-router'

// ===== 组件定义 =====
// Props定义
interface Props {
  modelValue?: string
  disabled?: boolean
  placeholder?: string
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  disabled: false,
  placeholder: ''
})

// Emits定义
interface Emits {
  'update:modelValue': [value: string]
  'change': [value: string]
}
const emit = defineEmits<Emits>()

// 国际化
const { t, tc } = useModuleI18n('模块名')

// 路由
const router = useRouter()

// ===== 响应式数据 =====
const loading = ref(false)
const tableData = ref<DataItem[]>([])
const searchForm = reactive<SearchParams>({
  keyword: '',
  status: '',
  page: 1,
  pageSize: 10
})

// ===== 计算属性 =====
const filteredData = computed(() => {
  return tableData.value.filter(item => 
    item.name.includes(searchForm.keyword)
  )
})

// ===== 监听器 =====
watch(() => props.modelValue, (newVal) => {
  // 处理prop变化
})

// ===== 方法 =====
const handleSearch = async () => {
  loading.value = true
  try {
    const result = await getDataList(searchForm)
    tableData.value = result.list
    ElMessage.success(tc('loadSuccess'))
  } catch (error) {
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

// ===== 生命周期 =====
onMounted(() => {
  handleSearch()
})

// ===== 暴露给父组件的方法 =====
defineExpose({
  refresh: handleSearch,
  resetForm: () => {
    Object.assign(searchForm, { keyword: '', status: '' })
  }
})
</script>

<style scoped lang="scss">
.component-container {
  // 样式定义
}
</style>
```

### 2. 标准页面结构

```vue
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('title') }}</h1>
    
    <!-- 搜索区域 -->
    <el-card class="search-card mb-20">
      <el-form :model="searchForm" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('fields.name')">
              <el-input 
                v-model="searchForm.name"
                :placeholder="t('fields.namePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('fields.status')">
              <el-select 
                v-model="searchForm.status" 
                :placeholder="t('fields.statusPlaceholder')" 
                clearable
              >
                <el-option :label="t('status.active')" value="active" />
                <el-option :label="t('status.inactive')" value="inactive" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="search-buttons">
              <el-button type="primary" @click="handleSearch" :loading="loading">
                {{ tc('search') }}
              </el-button>
              <el-button @click="handleReset">
                {{ tc('reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    
    <!-- 操作按钮区域 -->
    <div class="action-buttons mb-20">
      <el-button type="primary" @click="handleCreate">
        {{ t('actions.create') }}
      </el-button>
      <el-button @click="handleExport" :loading="exportLoading">
        {{ t('actions.export') }}
      </el-button>
    </div>
    
    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" border v-loading="loading">
        <el-table-column :label="t('fields.name')" prop="name" min-width="150" />
        <el-table-column :label="t('fields.status')" prop="status" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ t(`status.${row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('fields.createTime')" prop="createTime" width="180" />
        <el-table-column :label="tc('operations')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleDetail(row)">
              {{ tc('detail') }}
            </el-button>
            <el-button link type="warning" size="small" @click="handleEdit(row)">
              {{ tc('edit') }}
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(row)">
              {{ tc('delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>
```

### 3. Composition API 使用规范

#### 3.1 响应式数据定义
```typescript
// ✅ 正确：明确类型定义
const loading = ref<boolean>(false)
const userInfo = ref<UserInfo | null>(null)
const tableData = ref<DataItem[]>([])

// ✅ 正确：reactive用于对象
const searchForm = reactive<SearchParams>({
  keyword: '',
  status: '',
  page: 1,
  pageSize: 10
})

// ❌ 错误：缺少类型定义
const data = ref([])
const info = reactive({})
```

#### 3.2 计算属性和监听器
```typescript
// ✅ 正确：有意义的计算属性
const filteredList = computed(() => {
  return tableData.value.filter(item => {
    return (!searchForm.keyword || item.name.includes(searchForm.keyword)) &&
           (!searchForm.status || item.status === searchForm.status)
  })
})

// ✅ 正确：监听单个值
watch(() => props.modelValue, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    updateInternalValue(newVal)
  }
})

// ✅ 正确：监听多个值
watch([() => searchForm.page, () => searchForm.pageSize], () => {
  handleSearch()
})
```

### 4. Props和Emits定义

```typescript
// ✅ 正确：完整的Props接口
interface Props {
  // 必需属性
  id: string
  title: string
  
  // 可选属性
  disabled?: boolean
  loading?: boolean
  
  // 带默认值的属性
  size?: 'small' | 'default' | 'large'
  type?: 'primary' | 'success' | 'warning' | 'danger'
  
  // 复杂类型
  data?: DataItem[]
  config?: TableConfig
}

// 定义默认值
const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  loading: false,
  size: 'default',
  type: 'primary',
  data: () => [],
  config: () => ({})
})

// ✅ 正确：类型化的Emits
interface Emits {
  // 基本事件
  'click': [event: MouseEvent]
  'change': [value: string]
  
  // 带数据的事件
  'update:modelValue': [value: string]
  'select': [item: DataItem]
  'delete': [id: string]
  
  // 复杂事件
  'save': [data: FormData, callback: (success: boolean) => void]
  'error': [error: Error]
}

const emit = defineEmits<Emits>()
```

### 5. API调用和状态管理

```typescript
// ✅ 正确：标准API调用模式
const loadData = async () => {
  loading.value = true
  try {
    const result = await getDataList(searchForm)
    tableData.value = result.list
    pagination.total = result.total
  } catch (error) {
    console.error('Load data failed:', error)
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

// ✅ 正确：表单提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    // 提交数据
    submitLoading.value = true
    await saveData(form)
    
    ElMessage.success(tc('saveSuccess'))
    emit('success', form)
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(tc('saveFailed'))
    }
  } finally {
    submitLoading.value = false
  }
}

// ✅ 正确：删除确认
const handleDelete = async (item: DataItem) => {
  try {
    await ElMessageBox.confirm(
      t('deleteConfirmMessage', { name: item.name }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    )
    
    await deleteData(item.id)
    ElMessage.success(tc('deleteSuccess'))
    await loadData() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(tc('deleteFailed'))
    }
  }
}
```

### 6. 表单处理规范

```typescript
// 定义表单引用
const formRef = ref<FormInstance>()

// 定义验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: t('validation.nameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('validation.nameLength'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('validation.emailRequired'), trigger: 'blur' },
    { type: 'email', message: t('validation.emailFormat'), trigger: 'blur' }
  ]
})

// 表单提交处理
const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    // 提交逻辑
    await submitForm()
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
}
```

### 7. 表单模板

```vue
<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-position="top"
    class="form-container"
  >
    <el-form-item :label="t('fields.name')" prop="name">
      <el-input 
        v-model="form.name"
        :placeholder="t('fields.namePlaceholder')"
        clearable
      />
    </el-form-item>
    
    <el-form-item :label="t('fields.email')" prop="email">
      <el-input 
        v-model="form.email"
        :placeholder="t('fields.emailPlaceholder')"
        type="email"
        clearable
      />
    </el-form-item>
    
    <el-form-item>
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="submitLoading"
      >
        {{ tc('save') }}
      </el-button>
      <el-button @click="handleReset">
        {{ tc('reset') }}
      </el-button>
    </el-form-item>
  </el-form>
</template>
```

### 8. 事件处理规范

```typescript
// ✅ 正确：搜索处理
const handleSearch = () => {
  pagination.page = 1 // 重置到第一页
  loadData()
}

// ✅ 正确：重置处理
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    page: 1,
    pageSize: 10
  })
  loadData()
}

// ✅ 正确：分页处理
const handlePageChange = (page: number) => {
  pagination.page = page
  loadData()
}

const handleSizeChange = (size: number) => {
  pagination.page = 1
  pagination.size = size
  loadData()
}

// ✅ 正确：表格行操作
const handleDetail = (row: DataItem) => {
  router.push(`/detail/${row.id}`)
}

const handleEdit = (row: DataItem) => {
  currentEditItem.value = { ...row }
  editDialogVisible.value = true
}
```

### 9. 生命周期使用

```typescript
import { onMounted, onBeforeUnmount, onActivated, onDeactivated } from 'vue'

// 组件挂载后
onMounted(async () => {
  await loadData()
  initializeChart()
})

// 组件卸载前
onBeforeUnmount(() => {
  // 清理定时器
  if (timer.value) {
    clearInterval(timer.value)
  }
  
  // 清理事件监听
  window.removeEventListener('resize', handleResize)
})

// keep-alive激活时
onActivated(() => {
  // 刷新数据
  loadData()
})

// keep-alive失活时
onDeactivated(() => {
  // 暂停定时任务
  if (timer.value) {
    clearInterval(timer.value)
  }
})
```

### 10. 组件暴露和通信

```typescript
// 暴露给父组件的方法和数据
defineExpose({
  // 方法
  refresh: loadData,
  reset: handleReset,
  validate: () => formRef.value?.validate(),
  
  // 数据
  form: readonly(form),
  loading: readonly(loading)
})
```

### 11. 父子组件通信

```vue
<!-- 父组件 -->
<template>
  <ChildComponent
    ref="childRef"
    v-model:value="inputValue"
    :config="tableConfig"
    @save="handleChildSave"
    @error="handleChildError"
  />
</template>

<script setup lang="ts">
const childRef = ref<InstanceType<typeof ChildComponent>>()

// 调用子组件方法
const refreshChild = () => {
  childRef.value?.refresh()
}

// 处理子组件事件
const handleChildSave = (data: FormData) => {
  console.log('Child saved:', data)
}
</script>
```

### 12. 错误处理和边界情况

```typescript
// ✅ 正确：API错误处理
const loadData = async () => {
  try {
    loading.value = true
    const result = await getDataList(searchForm)
    
    // 处理空数据
    if (!result || !result.list) {
      tableData.value = []
      return
    }
    
    tableData.value = result.list
    pagination.total = result.total || 0
  } catch (error) {
    console.error('Load data error:', error)
    tableData.value = []
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

// ✅ 正确：边界条件处理
const handleItemClick = (item: DataItem | null) => {
  if (!item || !item.id) {
    ElMessage.warning(tc('invalidData'))
    return
  }
  
  // 正常处理逻辑
  router.push(`/detail/${item.id}`)
}
```

---

## ✅ Vue组件开发检查清单

### 基础结构
- [ ] 使用`<script setup>`语法
- [ ] 正确导入所需依赖
- [ ] Props和Emits定义完整
- [ ] 响应式数据有明确类型
- [ ] 国际化函数正确导入和使用

### 数据管理
- [ ] API调用有错误处理
- [ ] Loading状态正确管理
- [ ] 表单验证规则完整
- [ ] 事件处理函数命名规范

### 性能和质量
- [ ] 没有内存泄漏风险
- [ ] 合理使用计算属性和监听器
- [ ] 边界条件处理完善
- [ ] 代码符合TypeScript规范 