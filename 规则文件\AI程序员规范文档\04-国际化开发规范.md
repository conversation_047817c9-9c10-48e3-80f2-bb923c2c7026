# 国际化开发规范

> **📍 工作流程对应**：本文档主要对应标准工作流程的第6步
> - **第6步：添加国际化** - 在对应模块下添加翻译

---

## 🌐 国际化核心规范

### 1. 国际化架构概述

#### 1.1 模块化国际化架构
项目采用模块化国际化架构，解决多人协作时的文件冲突问题，提高国际化管理效率。

```
src/locales/
├── modules/                    # 模块化国际化文件
│   ├── common/                 # 通用模块（按钮、操作等）
│   │   ├── zh.json
│   │   └── en.json
│   ├── sales/                  # 销售管理模块
│   │   ├── zh.json
│   │   └── en.json
│   ├── aftersales/             # 售后服务模块
│   │   ├── zh.json
│   │   └── en.json
│   ├── parts/                  # 零件管理模块
│   │   ├── zh.json
│   │   └── en.json
│   └── base/                   # 基础系统模块
│       ├── zh.json
│       └── en.json
├── loader.ts                   # 模块加载器
└── backup/                     # 原文件备份
```

#### 1.2 五个核心模块
- **common**: 通用文本（按钮、操作、状态等）
- **sales**: 销售管理相关文本
- **aftersales**: 售后服务相关文本  
- **parts**: 零件管理相关文本
- **base**: 基础系统功能文本（登录、系统设置等）

### 2. 组件中的国际化使用

#### 2.1 标准使用模式
```vue
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1>{{ t('title') }}</h1>
    
    <!-- 表单字段 -->
    <el-form>
      <el-form-item :label="t('fields.customerName')">
        <el-input 
          v-model="form.customerName"
          :placeholder="t('fields.customerNamePlaceholder')"
        />
      </el-form-item>
      
      <el-form-item :label="t('fields.orderStatus')">
        <el-select v-model="form.status" :placeholder="t('fields.statusPlaceholder')">
          <el-option :label="t('status.pending')" value="pending" />
          <el-option :label="t('status.confirmed')" value="confirmed" />
          <el-option :label="t('status.completed')" value="completed" />
        </el-select>
      </el-form-item>
      
      <!-- 通用按钮使用tc() -->
      <el-form-item>
        <el-button type="primary" @click="handleSave">{{ tc('save') }}</el-button>
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
      </el-form-item>
    </el-form>
    
    <!-- 业务按钮使用t() -->
    <el-button type="primary" @click="handleCreateOrder">
      {{ t('actions.createOrder') }}
    </el-button>
    
    <!-- 表格操作列 -->
    <el-table-column :label="tc('operations')" width="200" fixed="right">
      <template #default="{ row }">
        <el-button link type="primary" size="small" @click="handleDetail(row)">
          {{ tc('detail') }}
        </el-button>
        <el-button link type="warning" size="small" @click="handleEdit(row)">
          {{ tc('edit') }}
        </el-button>
        <el-button link type="danger" size="small" @click="handleDelete(row)">
          {{ tc('delete') }}
        </el-button>
      </template>
    </el-table-column>
  </div>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'

// 根据页面功能选择正确的模块
const { t, tc } = useModuleI18n('sales.orders')
// t() 用于访问当前模块的翻译
// tc() 用于访问通用模块(common)的翻译

// 响应式数据
const form = reactive({
  customerName: '',
  status: ''
})

// 方法中使用国际化
const handleSave = async () => {
  try {
    await saveOrder(form)
    ElMessage.success(tc('saveSuccess'))
  } catch (error) {
    ElMessage.error(tc('saveFailed'))
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('messages.deleteConfirm', { name: row.customerName }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    )
    
    await deleteOrder(row.id)
    ElMessage.success(tc('deleteSuccess'))
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(tc('deleteFailed'))
    }
  }
}
</script>
```

#### 2.2 多模块国际化
```vue
<script setup lang="ts">
import { useMultiModuleI18n } from '@/composables/useModuleI18n'

// 同时使用多个模块的国际化
const { sales, parts, tc } = useMultiModuleI18n(['sales.orders', 'parts.inventory'])

// 使用方式
const orderTitle = sales('title')           // 销售模块：订单管理
const partName = parts('fields.partName')   // 零件模块：零件名称
const saveText = tc('save')                 // 通用模块：保存

// 在模板中使用
const getDisplayText = (type: string) => {
  switch (type) {
    case 'order':
      return sales('orderInfo')
    case 'part':
      return parts('partInfo')
    default:
      return tc('unknown')
  }
}
</script>
```

### 3. 模块选择决策规则

#### 3.1 基础模块判断
| 页面功能关键词 | 使用模块 | 示例模块名 |
|------------|----------|-----------|
| 包含"零件", "库存", "采购", "供应商", "配件" | `parts.*` | `parts.management`, `parts.archives` |
| 包含"销售", "订单", "客户", "车辆", "报价" | `sales.*` | `sales.orders`, `sales.customers` |
| 包含"维修", "售后", "工单", "预约", "保养" | `aftersales.*` | `aftersales.repairs`, `aftersales.warranty` |
| 包含"用户", "角色", "权限", "菜单", "系统" | `base.*` | `base.users`, `base.roles` |
| 包含"财务", "结算", "发票", "付款" | `finance.*` | `finance.settlement`, `finance.invoice` |

#### 3.2 边界情况处理
| 边界情况 | 使用模块 | 处理策略 |
|---------|----------|-----------|
| **跨模块功能页面** | 主要业务模块 | 选择页面核心业务对应的模块 |
| **通用业务组件** | `common` | 当组件在多个模块使用时 |
| **混合功能页面** | 最相关的模块 | 按80%功能确定主模块 |
| **系统级页面** | `base.*` | 404、登录、布局等系统页面 |
| **不确定情况** | `common` | 兜底方案，后续可重构 |

#### 3.3 模块命名规范
```typescript
// ✅ 正确：模块名使用点分隔的层次结构
useModuleI18n('sales.orders')          // 销售模块的订单管理
useModuleI18n('sales.customers')       // 销售模块的客户管理
useModuleI18n('parts.inventory')       // 零件模块的库存管理
useModuleI18n('parts.procurement')     // 零件模块的采购管理
useModuleI18n('aftersales.repairs')    // 售后模块的维修管理
useModuleI18n('base.users')           // 基础模块的用户管理

// ❌ 错误：不规范的模块命名
useModuleI18n('sales_orders')          // 错误：使用下划线
useModuleI18n('SalesOrders')          // 错误：使用大驼峰
useModuleI18n('salesorders')          // 错误：缺少分隔符
```

### 4. t() vs tc() 使用规则

#### 4.1 明确的使用规则
```typescript
const { t, tc } = useModuleI18n('sales.orders')

// ✅ 业务特定内容 → 使用 t()
const pageTitle = t('title')                    // 页面标题
const fieldLabel = t('fields.customerName')    // 业务字段标签
const businessButton = t('actions.createOrder') // 业务特定按钮
const statusText = t('status.pending')         // 业务状态
const messageText = t('messages.orderCreated') // 业务消息

// ✅ 通用操作内容 → 使用 tc()
const saveButton = tc('save')                  // 保存按钮
const cancelButton = tc('cancel')              // 取消按钮
const operationsColumn = tc('operations')      // 操作列
const searchButton = tc('search')              // 搜索按钮
const confirmButton = tc('confirm')            // 确认按钮
const deleteButton = tc('delete')              // 删除按钮
const editButton = tc('edit')                  // 编辑按钮
const detailButton = tc('detail')              // 详情按钮

// ✅ 系统级消息 → 使用 tc()
ElMessage.success(tc('saveSuccess'))           // 保存成功
ElMessage.error(tc('loadFailed'))              // 加载失败
ElMessage.warning(tc('noDataSelected'))       // 未选择数据
```

#### 4.2 边界情况处理
```typescript
// 🤔 模糊情况的处理原则

// 1. 当操作具有业务特性时，使用 t()
const exportOrdersButton = t('actions.exportOrders')  // 导出订单（业务特定）
const importPartsButton = t('actions.importParts')    // 导入零件（业务特定）

// 2. 当操作是通用性质时，使用 tc()
const exportButton = tc('export')                     // 通用导出
const importButton = tc('import')                     // 通用导入

// 3. 表单验证消息
const validationMessage = t('validation.customerNameRequired')  // 业务字段验证
const generalValidation = tc('validation.required')             // 通用验证

// 4. 状态显示
const orderStatus = t(`status.${order.status}`)       // 订单状态（业务）
const enabledStatus = tc('status.enabled')            // 启用状态（通用）
```

### 5. JSON键值结构规范

#### 5.1 强制使用嵌套结构
```json
{
  "orderManagement": {
    "title": "订单管理",
    "fields": {
      "orderNo": "订单编号",
      "orderNoPlaceholder": "请输入订单编号",
      "customerName": "客户姓名",
      "customerNamePlaceholder": "请输入客户姓名",
      "orderAmount": "订单金额",
      "createTime": "创建时间"
    },
    "actions": {
      "createOrder": "创建订单",
      "viewOrder": "查看订单",
      "editOrder": "编辑订单",
      "deleteOrder": "删除订单",
      "exportOrders": "导出订单"
    },
    "status": {
      "pending": "待处理",
      "confirmed": "已确认",
      "completed": "已完成",
      "cancelled": "已取消"
    },
    "messages": {
      "orderCreated": "订单创建成功",
      "orderUpdated": "订单更新成功",
      "orderDeleted": "订单删除成功",
      "deleteConfirm": "确定要删除订单 {name} 吗？",
      "noOrderSelected": "请选择要操作的订单"
    },
    "validation": {
      "customerNameRequired": "客户姓名不能为空",
      "orderAmountRequired": "订单金额不能为空",
      "orderAmountFormat": "请输入有效的金额格式"
    }
  }
}
```

#### 5.2 键名映射规则
| Vue代码中的写法 | 对应JSON路径 | 说明 |
|-------------|------------|------|
| `t('orderManagement.title')` | `sales.orderManagement.title` | 页面主标题固定使用title |
| `t('orderManagement.fields.customerName')` | `sales.orderManagement.fields.customerName` | 字段名放在fields下 |
| `t('orderManagement.fields.customerNamePlaceholder')` | `sales.orderManagement.fields.customerNamePlaceholder` | 占位符添加Placeholder后缀 |
| `t('orderManagement.actions.createOrder')` | `sales.orderManagement.actions.createOrder` | 操作按钮放在actions下 |
| `t('orderManagement.status.pending')` | `sales.orderManagement.status.pending` | 状态值放在status下 |
| `tc('save')` | `common.save` | 通用操作直接使用键名 |

#### 5.3 通用模块(common)结构
```json
{
  "save": "保存",
  "cancel": "取消",
  "confirm": "确认",
  "delete": "删除",
  "edit": "编辑",
  "detail": "详情",
  "create": "创建",
  "update": "更新",
  "search": "搜索",
  "reset": "重置",
  "export": "导出",
  "import": "导入",
  "operations": "操作",
  "loading": "加载中...",
  "noData": "暂无数据",
  "total": "共 {count} 条",
  "selected": "已选择 {count} 项",
  "saveSuccess": "保存成功",
  "saveFailed": "保存失败",
  "deleteSuccess": "删除成功",
  "deleteFailed": "删除失败",
  "loadFailed": "加载数据失败",
  "operationSuccessful": "操作成功",
  "operationFailed": "操作失败",
  "warning": "警告",
  "error": "错误",
  "info": "信息",
  "success": "成功",
  "pleaseSelect": "请选择",
  "pleaseInput": "请输入",
  "required": "此项为必填项",
  "invalidFormat": "格式不正确",
  "yes": "是",
  "no": "否",
  "enabled": "启用",
  "disabled": "禁用",
  "active": "激活",
  "inactive": "未激活"
}
```

### 6. 错误提示与消息国际化

#### 6.1 消息提示国际化
```typescript
// ✅ 正确：ElMessage 国际化
ElMessage.success(tc('saveSuccess'))
ElMessage.error(tc('saveFailed'))
ElMessage.warning(tc('noDataSelected'))
ElMessage.info(tc('operationSuccessful'))

// ✅ 正确：业务特定消息
ElMessage.success(t('messages.orderCreated'))
ElMessage.error(t('messages.orderDeleteFailed'))

// ✅ 正确：ElMessageBox 国际化
ElMessageBox.confirm(
  t('messages.deleteConfirm', { name: item.customerName }),
  tc('warning'),
  {
    confirmButtonText: tc('confirm'),
    cancelButtonText: tc('cancel'),
    type: 'warning'
  }
)

// ✅ 正确：ElNotification 国际化
ElNotification({
  title: tc('success'),
  message: t('messages.orderProcessed'),
  type: 'success'
})
```

#### 6.2 表单验证国际化
```typescript
// 表单验证规则国际化
const rules = reactive({
  customerName: [
    { 
      required: true, 
      message: t('validation.customerNameRequired'), 
      trigger: 'blur' 
    },
    { 
      min: 2, 
      max: 50, 
      message: t('validation.customerNameLength'), 
      trigger: 'blur' 
    }
  ],
  orderAmount: [
    { 
      required: true, 
      message: t('validation.orderAmountRequired'), 
      trigger: 'blur' 
    },
    { 
      pattern: /^\d+(\.\d{1,2})?$/, 
      message: t('validation.orderAmountFormat'), 
      trigger: 'blur' 
    }
  ]
})

// JSON对应结构
{
  "validation": {
    "customerNameRequired": "客户姓名不能为空",
    "customerNameLength": "客户姓名长度应在2-50字符之间",
    "orderAmountRequired": "订单金额不能为空",
    "orderAmountFormat": "请输入有效的金额格式"
  }
}
```

### 7. 动态内容国际化

#### 7.1 带参数的翻译
```typescript
// ✅ 正确：带参数的消息
const deleteMessage = t('messages.deleteConfirm', { 
  name: selectedItem.customerName,
  count: selectedItems.length 
})

// ✅ 正确：动态状态显示
const statusText = t(`status.${item.status}`)

// ✅ 正确：复杂参数的消息
const summaryText = t('messages.operationSummary', {
  successCount: result.successCount,
  failedCount: result.failedCount,
  total: result.total
})

// JSON对应结构
{
  "messages": {
    "deleteConfirm": "确定要删除 {name} 吗？",
    "batchDeleteConfirm": "确定要删除选中的 {count} 项吗？",
    "operationSummary": "操作完成：成功 {successCount} 项，失败 {failedCount} 项，共 {total} 项"
  }
}
```

#### 7.2 条件性翻译
```typescript
// ✅ 正确：根据条件选择翻译
const getActionText = (isEdit: boolean) => {
  return isEdit ? t('actions.updateOrder') : t('actions.createOrder')
}

// ✅ 正确：复杂条件翻译
const getStatusText = (status: string, isVip: boolean) => {
  const baseKey = `status.${status}`
  const suffix = isVip ? 'Vip' : ''
  return t(`${baseKey}${suffix}`)
}

// ✅ 正确：数量相关的翻译
const getCountText = (count: number) => {
  if (count === 0) {
    return tc('noData')
  } else if (count === 1) {
    return t('messages.singleItem')
  } else {
    return t('messages.multipleItems', { count })
  }
}
```

### 8. 日期、数字、货币格式化

#### 8.1 使用JavaScript Intl API
```typescript
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()

// ✅ 正确：日期格式化
const formatDate = (date: string | Date) => {
  return new Intl.DateTimeFormat(locale.value, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(new Date(date))
}

// ✅ 正确：时间格式化
const formatDateTime = (date: string | Date) => {
  return new Intl.DateTimeFormat(locale.value, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(new Date(date))
}

// ✅ 正确：数字格式化
const formatNumber = (number: number) => {
  return new Intl.NumberFormat(locale.value).format(number)
}

// ✅ 正确：货币格式化
const formatCurrency = (amount: number, currency: string = 'CNY') => {
  return new Intl.NumberFormat(locale.value, {
    style: 'currency',
    currency
  }).format(amount)
}
```

#### 8.2 封装格式化工具
```typescript
// src/utils/format.ts
import { useI18n } from 'vue-i18n'

export const useFormatter = () => {
  const { locale } = useI18n()
  
  return {
    date: (date: string | Date) => {
      return new Intl.DateTimeFormat(locale.value, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).format(new Date(date))
    },
    
    currency: (amount: number, currency: string = 'CNY') => {
      return new Intl.NumberFormat(locale.value, {
        style: 'currency',
        currency
      }).format(amount)
    },
    
    number: (num: number) => {
      return new Intl.NumberFormat(locale.value).format(num)
    }
  }
}

// 在组件中使用
const formatter = useFormatter()
const displayAmount = formatter.currency(order.amount)
const displayDate = formatter.date(order.createTime)
```

### 9. 国际化开发检查清单

#### 9.1 开发前检查
- [ ] 确定页面属于哪个业务模块
- [ ] 选择合适的模块名称（使用点分隔）
- [ ] 规划翻译键的嵌套结构
- [ ] 区分业务特定内容和通用内容

#### 9.2 开发中检查
- [ ] 所有用户可见文本都使用国际化函数
- [ ] 正确区分使用 t() 和 tc()
- [ ] 占位符使用 *Placeholder 命名
- [ ] 消息提示使用国际化
- [ ] 表单验证使用国际化
- [ ] 动态内容使用参数化翻译

#### 9.3 开发后检查
- [ ] 没有任何硬编码的中文或英文文本
- [ ] JSON结构规范且层次清晰
- [ ] 翻译键命名符合规范
- [ ] 所有翻译内容准确且完整
- [ ] 格式化函数使用正确

### 10. 常见错误避免

#### 10.1 错误示例
```vue
<!-- ❌ 错误：硬编码文本 -->
<h1>订单管理</h1>
<el-button>保存</el-button>
<el-table-column label="操作" />

<!-- ❌ 错误：混用t和tc -->
<el-button>{{ t('save') }}</el-button>
<h1>{{ tc('orderManagement.title') }}</h1>

<!-- ❌ 错误：缺少占位符国际化 -->
<el-input placeholder="请输入客户姓名" />

<!-- ❌ 错误：消息提示未国际化 -->
<script>
ElMessage.success('保存成功')
ElMessage.error('操作失败')
</script>
```

#### 10.2 正确示例
```vue
<!-- ✅ 正确：完整的国际化 -->
<template>
  <div>
    <h1>{{ t('orderManagement.title') }}</h1>
    <el-button type="primary">{{ tc('save') }}</el-button>
    <el-table-column :label="tc('operations')" />
    <el-input :placeholder="t('orderManagement.fields.customerNamePlaceholder')" />
  </div>
</template>

<script setup>
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales.orders')

const handleSave = () => {
  ElMessage.success(tc('saveSuccess'))
}
</script>
```

---

## 📝 国际化开发检查清单

### 开发阶段检查
- [ ] 正确选择模块：根据页面功能选择合适的i18n模块
- [ ] t() vs tc() 使用正确：业务内容用t()，通用操作用tc()
- [ ] 参数化翻译格式正确：使用对象格式传参
- [ ] 处理空值情况：参数可能为空时添加默认值

### 文件完整性检查
- [ ] 中文翻译文件已添加：src/locales/modules/[module]/zh.json
- [ ] 英文翻译文件已添加：src/locales/modules/[module]/en.json
- [ ] 键值结构一致：中英文文件的键结构完全匹配
- [ ] 无硬编码文本：所有用户可见文本都已国际化

### 功能测试检查
- [ ] 语言切换正常：切换语言后翻译正确显示
- [ ] 缺失键处理：不存在的翻译键显示键名而不报错
- [ ] 动态内容正确：参数化翻译的动态内容正确显示
- [ ] 特殊字符处理：HTML字符、换行符等特殊内容正确处理

记住：国际化是前端开发的基础要求，任何文本都不能跳过国际化处理！ 