# 样式和UI规范

> **📍 工作流程对应**：本文档主要对应标准工作流程的第5步
> - **第5步：开发页面组件** - UI样式和布局规范部分

---

## 🎨 UI设计核心规范

### 1. 页面布局规范

#### 1.1 基础页面结构
```vue
<template>
  <div class="page-container">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <h1 class="page-title">{{ t('title') }}</h1>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="search-card mb-20">
      <el-form :model="searchForm" label-position="top" class="search-form">
        <!-- 搜索表单内容 -->
      </el-form>
    </el-card>
    
    <!-- 操作按钮区域 -->
    <div class="action-buttons mb-20">
      <!-- 操作按钮 -->
    </div>
    
    <!-- 主要内容区域 -->
    <el-card class="content-card">
      <!-- 数据表格或其他内容 -->
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination />
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px); // 减去顶部导航高度
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.4;
}

.mb-20 {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
```

#### 1.2 卡片化布局原则
```scss
// 卡片基础样式
.search-card,
.content-card,
.operation-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e4e7ed;
  
  .el-card__body {
    padding: 20px;
  }
}

// 卡片间距
.search-card {
  margin-bottom: 20px;
}

.operation-card {
  margin-bottom: 20px;
}

.content-card {
  margin-bottom: 20px;
}
```

#### 1.3 栅格系统使用
```vue
<template>
  <!-- 搜索表单栅格布局 -->
  <el-form :model="searchForm" label-position="top">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item :label="t('fields.name')">
          <el-input v-model="searchForm.name" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item :label="t('fields.status')">
          <el-select v-model="searchForm.status">
            <!-- 选项 -->
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item :label="t('fields.dateRange')">
          <el-date-picker v-model="searchForm.dateRange" type="daterange" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label=" " class="search-buttons">
          <el-button type="primary">{{ tc('search') }}</el-button>
          <el-button>{{ tc('reset') }}</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style scoped lang="scss">
.search-form {
  .el-form-item {
    margin-bottom: 16px;
  }
  
  .search-buttons {
    display: flex;
    align-items: flex-end;
    
    .el-form-item__content {
      margin-left: 0 !important;
    }
    
    .el-button {
      margin-left: 10px;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }
}
</style>
```

### 2. 表单设计规范

#### 2.1 标准表单结构
```vue
<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-position="top"
    class="standard-form"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item :label="t('fields.name')" prop="name">
          <el-input 
            v-model="form.name"
            :placeholder="t('fields.namePlaceholder')"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="t('fields.email')" prop="email">
          <el-input 
            v-model="form.email"
            :placeholder="t('fields.emailPlaceholder')"
            type="email"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item :label="t('fields.description')" prop="description">
          <el-input 
            v-model="form.description"
            :placeholder="t('fields.descriptionPlaceholder')"
            type="textarea"
            :rows="4"
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <!-- 表单操作按钮 -->
    <el-form-item class="form-actions">
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="submitLoading"
      >
        {{ tc('save') }}
      </el-button>
      <el-button @click="handleReset">
        {{ tc('reset') }}
      </el-button>
      <el-button @click="handleCancel">
        {{ tc('cancel') }}
      </el-button>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
.standard-form {
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .form-actions {
    margin-top: 30px;
    text-align: center;
    border-top: 1px solid #e4e7ed;
    padding-top: 20px;
    
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
```

#### 2.2 表单项样式规范
```scss
// 表单标签样式
.el-form-item__label {
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  padding-bottom: 8px;
}

// 必填标识样式
.el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

// 输入框样式
.el-input {
  .el-input__inner {
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    
    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }
}

// 选择器样式
.el-select {
  width: 100%;
  
  .el-input__inner {
    cursor: pointer;
  }
}

// 日期选择器样式
.el-date-editor {
  width: 100%;
}
```

### 3. 表格设计规范

#### 3.1 标准表格结构
```vue
<template>
  <el-table 
    :data="tableData" 
    border 
    v-loading="loading"
    class="standard-table"
    empty-text="暂无数据"
  >
    <!-- 选择列（可选） -->
    <el-table-column type="selection" width="50" fixed="left" />
    
    <!-- 序号列（可选） -->
    <el-table-column type="index" label="序号" width="60" />
    
    <!-- 数据列 -->
    <el-table-column 
      :label="t('fields.name')" 
      prop="name" 
      min-width="150"
      show-overflow-tooltip
    />
    
    <el-table-column 
      :label="t('fields.status')" 
      prop="status" 
      width="120"
      align="center"
    >
      <template #default="{ row }">
        <el-tag 
          :type="getStatusType(row.status)"
          size="small"
        >
          {{ t(`status.${row.status}`) }}
        </el-tag>
      </template>
    </el-table-column>
    
    <el-table-column 
      :label="t('fields.createTime')" 
      prop="createTime" 
      width="180"
      align="center"
    >
      <template #default="{ row }">
        {{ formatDateTime(row.createTime) }}
      </template>
    </el-table-column>
    
    <!-- 操作列 -->
    <el-table-column 
      :label="tc('operations')" 
      width="200" 
      fixed="right"
      align="center"
    >
      <template #default="{ row }">
        <el-button 
          link 
          type="primary" 
          size="small" 
          @click="handleDetail(row)"
        >
          {{ tc('detail') }}
        </el-button>
        <el-button 
          link 
          type="warning" 
          size="small" 
          @click="handleEdit(row)"
        >
          {{ tc('edit') }}
        </el-button>
        <el-button 
          link 
          type="danger" 
          size="small" 
          @click="handleDelete(row)"
        >
          {{ tc('delete') }}
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<style scoped lang="scss">
.standard-table {
  // 表格基础样式
  border-radius: 6px;
  overflow: hidden;
  
  // 表头样式
  :deep(.el-table__header-wrapper) {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #303133;
        font-weight: 600;
        border-bottom: 1px solid #e4e7ed;
      }
    }
  }
  
  // 表格行样式
  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #f5f7fa;
      }
    }
    
    td {
      border-bottom: 1px solid #ebeef5;
      padding: 12px 0;
    }
  }
  
  // 操作按钮样式
  .el-button--small.is-link {
    padding: 2px 4px;
    margin: 0 2px;
    
    &:first-child {
      margin-left: 0;
    }
    
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
```

#### 3.2 表格状态样式
```scss
// 状态标签颜色规范
.el-tag {
  &.el-tag--success {
    background-color: #f0f9ff;
    border-color: #b3e5fc;
    color: #0288d1;
  }
  
  &.el-tag--warning {
    background-color: #fffbf0;
    border-color: #ffe58f;
    color: #d46b08;
  }
  
  &.el-tag--danger {
    background-color: #fff2f0;
    border-color: #ffccc7;
    color: #cf1322;
  }
  
  &.el-tag--info {
    background-color: #f6f6f6;
    border-color: #d9d9d9;
    color: #595959;
  }
}

// 状态映射函数示例
const getStatusTagType = (status: string) => {
  const statusMap = {
    'active': 'success',
    'pending': 'warning',
    'inactive': 'info',
    'disabled': 'danger'
  }
  return statusMap[status] || 'info'
}
```

### 4. 按钮设计规范

#### 4.1 按钮类型和使用场景
```vue
<template>
  <div class="button-examples">
    <!-- 主要操作按钮 -->
    <el-button type="primary" size="default">
      {{ tc('save') }}
    </el-button>
    
    <!-- 次要操作按钮 -->
    <el-button type="default" size="default">
      {{ tc('cancel') }}
    </el-button>
    
    <!-- 危险操作按钮 -->
    <el-button type="danger" size="default">
      {{ tc('delete') }}
    </el-button>
    
    <!-- 成功操作按钮 -->
    <el-button type="success" size="default">
      {{ tc('submit') }}
    </el-button>
    
    <!-- 警告操作按钮 -->
    <el-button type="warning" size="default">
      {{ tc('reset') }}
    </el-button>
    
    <!-- 信息操作按钮 -->
    <el-button type="info" size="default">
      {{ tc('info') }}
    </el-button>
    
    <!-- 文本按钮（表格操作） -->
    <el-button link type="primary" size="small">
      {{ tc('edit') }}
    </el-button>
  </div>
</template>

<style scoped lang="scss">
.button-examples {
  .el-button {
    margin: 0 8px 8px 0;
    
    // 按钮最小宽度
    min-width: 80px;
    
    // 按钮圆角
    border-radius: 6px;
    
    // 按钮内边距
    padding: 8px 16px;
    
    // 按钮字体
    font-size: 14px;
    font-weight: 500;
    
    // 按钮高度
    height: 36px;
    
    // 禁用状态
    &.is-disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    // 加载状态
    &.is-loading {
      pointer-events: none;
    }
  }
  
  // 不同尺寸按钮
  .el-button--large {
    height: 40px;
    padding: 10px 20px;
    font-size: 16px;
  }
  
  .el-button--small {
    height: 28px;
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>
```

#### 4.2 按钮组合规范
```vue
<template>
  <!-- 表单按钮组 -->
  <div class="form-button-group">
    <el-button type="primary" @click="handleSave">
      {{ tc('save') }}
    </el-button>
    <el-button @click="handleCancel">
      {{ tc('cancel') }}
    </el-button>
  </div>
  
  <!-- 操作按钮组 -->
  <div class="action-button-group">
    <el-button type="primary" @click="handleCreate">
      {{ t('actions.create') }}
    </el-button>
    <el-button @click="handleExport">
      {{ t('actions.export') }}
    </el-button>
    <el-button type="danger" @click="handleBatchDelete">
      {{ t('actions.batchDelete') }}
    </el-button>
  </div>
  
  <!-- 搜索按钮组 -->
  <div class="search-button-group">
    <el-button type="primary" @click="handleSearch">
      {{ tc('search') }}
    </el-button>
    <el-button @click="handleReset">
      {{ tc('reset') }}
    </el-button>
  </div>
</template>

<style scoped lang="scss">
.form-button-group,
.action-button-group,
.search-button-group {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .el-button {
    margin: 0;
  }
}

.form-button-group {
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.action-button-group {
  justify-content: flex-start;
  margin-bottom: 20px;
}

.search-button-group {
  justify-content: flex-end;
}
</style>
```

### 5. 模态框设计规范

#### 5.1 标准模态框结构
```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="standard-dialog"
  >
    <!-- 对话框内容 -->
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        class="dialog-form"
      >
        <!-- 表单内容 -->
      </el-form>
    </div>
    
    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ tc('cancel') }}
        </el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="submitLoading"
        >
          {{ tc('confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.standard-dialog {
  // 对话框整体样式
  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;
  }
  
  // 对话框头部
  :deep(.el-dialog__header) {
    background-color: #fafafa;
    border-bottom: 1px solid #e4e7ed;
    padding: 16px 20px;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  // 对话框内容
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }
  
  // 对话框底部
  :deep(.el-dialog__footer) {
    background-color: #fafafa;
    border-top: 1px solid #e4e7ed;
    padding: 16px 20px;
  }
}

.dialog-content {
  .dialog-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  .el-button {
    margin: 0;
  }
}
</style>
```

### 6. 分页器设计规范

#### 6.1 标准分页器
```vue
<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :background="true"
      @size-change="handleSizeChange"
      @current-change="handlePageChange"
    />
  </div>
</template>

<style scoped lang="scss">
.pagination-container {
  margin-top: 20px;
  padding: 16px 0;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e4e7ed;
  
  :deep(.el-pagination) {
    // 分页器整体样式
    .el-pagination__total {
      color: #606266;
      font-size: 13px;
    }
    
    .el-pagination__sizes {
      .el-select {
        .el-input__inner {
          height: 28px;
          line-height: 28px;
        }
      }
    }
    
    .el-pager {
      li {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        margin: 0 2px;
        
        &.active {
          background-color: #409eff;
          color: #fff;
        }
        
        &:hover:not(.active) {
          background-color: #f5f7fa;
        }
      }
    }
    
    .btn-prev,
    .btn-next {
      width: 32px;
      height: 32px;
      line-height: 32px;
      border-radius: 4px;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
```

### 7. 响应式设计规范

#### 7.1 断点定义
```scss
// 响应式断点
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}
```

#### 7.2 响应式布局
```vue
<template>
  <div class="responsive-layout">
    <el-row :gutter="20">
      <el-col 
        :xs="24" 
        :sm="12" 
        :md="8" 
        :lg="6" 
        :xl="6"
        v-for="item in cardList" 
        :key="item.id"
      >
        <div class="card-item">
          <!-- 卡片内容 -->
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped lang="scss">
.responsive-layout {
  .card-item {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    // 移动端优化
    @include respond-to(xs) {
      padding: 16px;
      margin-bottom: 16px;
    }
    
    // 平板端优化
    @include respond-to(md) {
      padding: 18px;
    }
    
    // 桌面端优化
    @include respond-to(lg) {
      padding: 20px;
    }
  }
}
</style>
```

### 8. 颜色规范

#### 8.1 主题色彩
```scss
// 主色调
$primary-color: #409eff;
$primary-light: #66b3ff;
$primary-dark: #2e7bd6;

// 辅助色
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 中性色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

// 边框色
$border-light: #ebeef5;
$border-base: #dcdfe6;
$border-dark: #c0c4cc;

// 背景色
$bg-primary: #ffffff;
$bg-light: #f5f7fa;
$bg-dark: #f0f2f5;

// 阴影
$shadow-light: 0 2px 4px rgba(0, 0, 0, 0.04);
$shadow-base: 0 2px 8px rgba(0, 0, 0, 0.06);
$shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.12);
```

#### 8.2 状态颜色
```scss
// 状态色彩映射
.status-active {
  color: $success-color;
  background-color: rgba($success-color, 0.1);
}

.status-pending {
  color: $warning-color;
  background-color: rgba($warning-color, 0.1);
}

.status-inactive {
  color: $info-color;
  background-color: rgba($info-color, 0.1);
}

.status-error {
  color: $danger-color;
  background-color: rgba($danger-color, 0.1);
}
```

### 9. 字体规范

#### 9.1 字体族
```scss
// 字体栈
$font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                     'Helvetica Neue', Arial, sans-serif;
$font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 
                   'Source Code Pro', monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 13px;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;
$font-size-xxxl: 24px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-base: 1.5;
$line-height-lg: 1.6;
$line-height-sm: 1.4;
```

#### 9.2 文字样式类
```scss
// 文字工具类
.text-primary {
  color: $text-primary;
}

.text-regular {
  color: $text-regular;
}

.text-secondary {
  color: $text-secondary;
}

.text-placeholder {
  color: $text-placeholder;
}

// 字体大小类
.text-xs {
  font-size: $font-size-xs;
}

.text-sm {
  font-size: $font-size-sm;
}

.text-base {
  font-size: $font-size-base;
}

.text-lg {
  font-size: $font-size-lg;
}

// 字体粗细类
.font-light {
  font-weight: $font-weight-light;
}

.font-normal {
  font-weight: $font-weight-normal;
}

.font-medium {
  font-weight: $font-weight-medium;
}

.font-semibold {
  font-weight: $font-weight-semibold;
}

.font-bold {
  font-weight: $font-weight-bold;
}
```

### 10. 动画规范

#### 10.1 过渡动画
```scss
// 过渡时间
$transition-fast: 0.15s;
$transition-base: 0.25s;
$transition-slow: 0.35s;

// 缓动函数
$ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
$ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
$ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);

// 通用过渡类
.transition-all {
  transition: all $transition-base $ease-in-out;
}

.transition-colors {
  transition: background-color $transition-base $ease-in-out,
              border-color $transition-base $ease-in-out,
              color $transition-base $ease-in-out;
}

.transition-transform {
  transition: transform $transition-base $ease-in-out;
}
```

#### 10.2 hover效果
```scss
// 按钮hover效果
.el-button {
  transition: all $transition-base $ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-dark;
  }
}

// 卡片hover效果
.card-item {
  transition: all $transition-base $ease-in-out;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-dark;
  }
}

// 表格行hover效果
.el-table__row {
  transition: background-color $transition-fast $ease-in-out;
  
  &:hover {
    background-color: #f5f7fa !important;
  }
}
```

---

## ✅ 样式开发检查清单

### 基础结构
- [ ] 使用标准页面容器结构
- [ ] 卡片化布局应用正确
- [ ] 栅格系统使用合理
- [ ] 间距规范符合设计要求

### 组件样式
- [ ] 表单样式符合规范
- [ ] 表格样式统一规范
- [ ] 按钮使用正确的类型和尺寸
- [ ] 模态框结构和样式标准

### 响应式设计
- [ ] 移动端适配良好
- [ ] 不同屏幕尺寸布局合理
- [ ] 触摸友好的交互设计

### 视觉规范
- [ ] 颜色使用符合主题
- [ ] 字体大小和粗细合适
- [ ] 动画过渡自然流畅
- [ ] 状态反馈清晰明确

通过遵循这些样式和UI规范，AI程序员可以确保生成的界面具有一致的视觉风格和良好的用户体验。 