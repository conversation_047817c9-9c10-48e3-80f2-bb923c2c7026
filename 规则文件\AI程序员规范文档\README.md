# AI程序员规范文档

## 📚 文档内容概览

1. **[01-AI程序员总体规范.md](./01-AI程序员总体规范.md)** ⭐️ **必读基础**
   - 项目概述和技术栈、核心原则、目录结构、基础配置

2. **[03-API接口开发规范.md](./03-API接口开发规范.md)** ⭐️ **重点**
   - API函数开发模式、Mock数据规范、类型定义标准

3. **[02-Vue组件开发规范.md](./02-Vue组件开发规范.md)** ⭐️ **重点**
   - Vue 3 + Composition API规范、组件结构、数据管理

4. **[04-国际化开发规范.md](./04-国际化开发规范.md)** ⭐️ **重点**
   - 模块化国际化架构、t() vs tc() 使用规则

5. **[05-样式和UI规范.md](./05-样式和UI规范.md)**
   - UI设计规范、Element Plus使用、响应式设计

6. **[06-常见问题和解决方案.md](./06-常见问题和解决方案.md)** 🛠️ **速查手册**
   - 常见错误排查、问题解决方案、自检清单

---

## 🚀 快速开始

### 技术栈核心
```
Vue 3 + TypeScript + Composition API + Element Plus + Vite
```

### 模块化国际化
```typescript
// 根据页面功能选择正确模块
const { t, tc } = useModuleI18n('sales.orders')

// 业务特定内容用 t()
t('title')              // 页面标题
t('fields.customerName') // 业务字段

// 通用操作用 tc()
tc('save')              // 保存按钮
tc('operations')        // 操作列
```

### 目录结构规范
```
src/
├── views/           # 路由页面
├── components/      # 非路由组件
├── api/modules/     # API接口（按模块）
├── types/           # TypeScript类型（强制）
├── mock/data/       # Mock数据
├── utils/           # 工具函数（含mock-config.ts）
└── locales/modules/ # 国际化翻译
```

### 标准页面结构
```vue
<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>
    
    <!-- 搜索区域 -->
    <el-card class="search-card mb-20">
      <!-- 搜索表单 -->
    </el-card>
    
    <!-- 操作按钮 -->
    <div class="action-buttons mb-20">
      <!-- 操作按钮 -->
    </div>
    
    <!-- 数据表格 -->
    <el-card class="content-card">
      <!-- 表格和分页 -->
    </el-card>
  </div>
</template>
```

---

## 🔄 标准工作流程

1. **分析需求** → 确定模块和功能类型
2. **创建类型** → 定义TypeScript接口
3. **实现Mock** → 创建测试数据
4. **开发API** → 实现接口调用
5. **开发组件** → 使用标准模板
6. **添加翻译** → 完善国际化
7. **自检验证** → 确保符合规范

---

## 🎯 核心规范速览

### 🚫 绝对禁止
- ❌ 硬编码任何用户可见的文本
- ❌ 使用`any`类型（除request泛型参数）
- ❌ 在API文件同目录定义类型（必须在src/types/）
- ❌ 在组件内创建假数据
- ❌ 跳过TypeScript类型定义

### ✅ 强制要求
- ✅ 所有文本使用国际化（t/tc函数）
- ✅ 明确的TypeScript类型定义
- ✅ 统一的错误处理和Loading状态
- ✅ Mock数据支持
- ✅ 遵循既定的代码结构

---

## 📖 按需查阅

根据当前开发任务，可以直接查阅相关章节：

- **🎯 分析需求** → [01-总体规范 § 工作流程](./01-AI程序员总体规范.md)
- **📝 定义类型** → [01-总体规范 § 类型组织](./01-AI程序员总体规范.md) + [03-API规范](./03-API接口开发规范.md)
- **🔌 开发API** → [03-API接口开发规范](./03-API接口开发规范.md)
- **🧩 开发组件** → [02-Vue组件开发规范](./02-Vue组件开发规范.md)
- **🌐 处理国际化** → [04-国际化开发规范](./04-国际化开发规范.md)
- **🎨 样式设计** → [05-样式和UI规范](./05-样式和UI规范.md)
- **🐛 问题排查** → [06-常见问题解决方案](./06-常见问题和解决方案.md)

---

## 🛟 紧急求助

### 遇到问题时的处理顺序
1. **查看第6个文档** - [常见问题和解决方案.md](./06-常见问题和解决方案.md)
2. **检查控制台错误** - 浏览器F12 Console面板
3. **确认网络请求** - Network面板查看API状态
4. **验证代码规范** - 对照相应规范文档

### 常见错误快速定位
- **Cannot find module** → 检查导入路径和文件扩展名
- **Missing translation** → 检查国际化模块名和键路径
- **Type error** → 检查TypeScript类型定义
- **Network error** → 检查Mock配置或API地址
- **Page blank** → 检查路由配置和组件导入

---

**记住：严格遵循规范比创新更重要，一致性是团队协作的基础！** 🎯 