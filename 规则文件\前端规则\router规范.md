# 路由开发规范

## 1. 文件结构规范

### 1.1 文件命名
- 文件名使用 `模块名.ts` 格式，如：`sales.ts`、`parts.ts`
- 文件存放在 `src/router/modules/` 目录下

### 1.2 导出规范
```typescript
import type { RouteRecordRaw } from 'vue-router'

// 模块路由配置
export const 模块名Routes: RouteRecordRaw[] = [
  // 路由配置
]

export default 模块名Routes
```

## 2. 路由配置规范

### 2.1 基础路由结构
```typescript
{
  path: '/模块名/功能名',           // 完整绝对路径
  name: '模块名-功能名',            // kebab-case命名
  component: () => import('@/views/模块名/功能名/组件名.vue'),
  meta: {
    title: 'menu.功能名',         // 国际化key
    icon: 'IconName',           // Element Plus图标名
    requiresAuth: true          // 是否需要登录
  }
}
```

### 2.2 路径命名规范
- **主功能页面**：`/模块名/功能名`
- **详情页面**：`/模块名/功能名/:id/detail`
- **编辑页面**：`/模块名/功能名/:id/edit`
- **创建页面**：`/模块名/功能名/create`

### 2.3 路由name命名规范
- 使用 `kebab-case` 格式
- 格式：`模块名-功能名-操作`
- 示例：
  ```typescript
  'sales-orders'           // 列表页
  'sales-order-detail'     // 详情页
  'sales-order-edit'       // 编辑页
  'sales-order-create'     // 创建页
  ```

## 3. Meta配置规范

### 3.1 必填字段
```typescript
meta: {
  title: 'menu.功能名',     // 国际化标题key
  requiresAuth: true       // 是否需要登录验证
}
```

### 3.2 可选字段
```typescript
meta: {
  icon: 'IconName',        // 菜单图标（主功能页面必填）
  hideInMenu: true,        // 是否在菜单中隐藏
  permissions: ['权限码'],  // 功能权限数组
  roles: ['角色名'],        // 角色权限数组
  keepAlive: true,         // 是否缓存组件
  breadcrumb: false        // 是否显示面包屑
}
```

### 3.3 菜单显示控制
- **主功能页面**：显示在菜单中，需要 `icon` 字段
- **详情/编辑/创建页面**：设置 `hideInMenu: true`

## 4. 组件导入规范

### 4.1 懒加载导入
```typescript
component: () => import('@/views/模块名/功能名/组件名.vue')
```

### 4.2 路径规范
- 统一使用 `@/views/` 作为视图组件根路径
- 目录结构：`views/模块名/功能名/组件名.vue`
- 组件命名：使用 PascalCase，以 `View.vue` 结尾

## 5. 权限控制规范

### 5.1 基础权限
```typescript
meta: {
  requiresAuth: true  // 所有业务页面必须设置
}
```

### 5.2 功能权限
```typescript
meta: {
  permissions: ['模块名:功能名:操作']  // 如：['sales:orders:view']
}
```

### 5.3 角色权限
```typescript
meta: {
  roles: ['role1', 'role2']  // 限制特定角色访问
}
```

## 6. 国际化规范

### 6.1 title字段
- 统一使用 `menu.` 前缀
- 格式：`menu.功能名`
- 示例：`menu.salesOrders`、`menu.orderApproval`

### 6.2 对应的国际化文件
需要在以下文件中添加对应的翻译：
- `src/locales/modules/menu/zh.json`
- `src/locales/modules/menu/en.json`

## 7. 注意事项

1. **平铺结构**：每个路由都是独立的，不使用嵌套children
2. **绝对路径**：所有path都使用完整的绝对路径
3. **一致性**：同一模块内的路由命名和结构保持一致
4. **权限控制**：根据实际需求添加合适的权限配置
5. **菜单控制**：合理使用 `hideInMenu` 控制菜单显示
6. **国际化**：确保所有title都有对应的国际化配置 