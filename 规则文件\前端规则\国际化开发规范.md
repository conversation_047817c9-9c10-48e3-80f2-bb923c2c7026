# 国际化开发规范 (修正版)

### 1. 核心概念：作用域翻译

我们使用 `useModuleI18n` 钩子来创建一个“有作用域”的翻译函数。

```typescript
// 'parts' 定位到 src/locales/modules/parts/ 目录下的文件
// 'inventoryManagement' 定位到文件内名为 "inventoryManagement" 的JSON对象
const { t, tc } = useModuleI18n('parts.inventoryManagement'); 
```

- **`t()`**: 用于获取当前作用域 (`inventoryManagement`) 内的翻译。
- **`tc()`**: 用于获取全局通用 (`common`) 模块的翻译。

### 2. 开发三步曲

#### 第1步：在组件中设置作用域

```vue
<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n';

// 为当前组件设置翻译作用域
const { t, tc } = useModuleI18n('parts.inventoryManagement'); 
</script>
```

#### 第2步：在JSON文件中定义结构

在 `src/locales/modules/parts/zh.json` 中，必须有一个顶层键与作用域对应。

```json
{
  "inventoryManagement": {  // <== 对应 useModuleI18n 的作用域
    "title": "库存管理",
    "dialog": {
      "adjustTitle": "库存调整",
      "increase": "增加库存",
      "decrease": "减少库存"
    },
    "table": {
      "partCode": "零件编号",
      "partName": "零件名称"
    },
    "validation": {
        "quantityMin": "调整数量必须大于0"
    }
  },
  "purchase": {
      // ... 采购模块的其他翻译
  }
}
```

#### 第3步：在代码中使用相对路径调用 `t()`

所有 `t()` 的调用路径都是**相对于 `inventoryManagement`** 的。

```vue
<template>
  <!-- t('dialog.adjustTitle') 会查找 inventoryManagement.dialog.adjustTitle -->
  <el-dialog :title="t('dialog.adjustTitle')">
  
    <!-- t('table.partName') 会查找 inventoryManagement.table.partName -->
    <span class="info-label">{{ t('table.partName') }}：</span>
    
    <!-- tc('cancel') 会查找 common.cancel -->
    <el-button @click="onClose">{{ tc('cancel') }}</el-button>
    
  </el-dialog>
</template>
```

### 3. JSON键值结构规范

为了保持一致性，请在作用域对象内遵循以下标准嵌套结构：

- **`title`**: 页面或区块的主标题。
- **`fields`**: 表单或详情中的字段标签名。
- **`table`**: 表格的列标题。
- **`actions`**: 页面中的操作按钮文本 (如：库存盘点)。
- **`dialog`**: 对话框(Modal)内的所有文本。
- **`status`**: 业务状态文本。
- **`messages`**: 操作结果的消息提示 (如：`deleteConfirm`)。
- **`validation`**: 表单校验相关的提示文本。 