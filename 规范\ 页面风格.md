
### **`HomeView.vue` 页面开发范本规则 (更新版)**

本规则旨在为前端项目中的所有新页面提供统一的开发指导，确保项目在 UI 风格、交互逻辑、国际化、API 调用和数据管理方面保持高度一致性。所有新页面应严格遵循以下范本规则。

#### **1. 页面整体结构与布局规范**

*   **根容器**:
    *   所有页面内容必须包裹在一个根 `div` 元素中，并应用 `page-container` 类。
    *   **样式**:
        ```scss
        .page-container {
          padding: 20px; // 统一页面内边距，提供视觉舒适区
        }
        ```
*   **页面标题**:
    *   每个页面都应有一个清晰的标题，使用 `h1` 标签，并应用 `page-title` 类。
    *   **国际化**: 标题文本必须使用 `t()` 函数进行国际化。
    *   **样式**:
        ```scss
        .page-title {
          margin-bottom: 20px; // 标题与下方内容之间保持固定间距
        }
        ```
*   **卡片化布局 (`el-card`)**:
    *   页面内容应按逻辑功能划分为不同的 `el-card` 组件，例如“搜索区域”、“操作区域”、“数据展示（表格）区域”。
    *   **样式**: 所有 `el-card` 组件下方应统一有 20px 的外边距。
        ```html
        <el-card class="mb-20 search-card">...</el-card>
        <el-card class="mb-20 operation-card">...</el-card>
        <el-card class="table-card">...</el-card> <!-- 最后一个卡片下方无需 mb-20，由 pagination-container 底部处理 -->
        ```
        ```scss
        .search-card, .operation-card {
          margin-bottom: 20px; // 保持卡片之间垂直间距
        }
        .table-card {
          margin-bottom: 20px; // 表格卡片底部间距，与分页器统一
        }
        ```
*   **栅格系统 (`el-row`, `el-col`)**:
    *   所有内部布局（如表单项、按钮组）必须使用 Element Plus 的 `el-row` 和 `el-col` 栅格系统。
    *   **间距**: `el-row` 必须设置 `gutter="20"`，保持列之间 20px 的水平间距。
    *   **列宽**: 根据内容和设计需求，合理分配 `el-col` 的 `span` 属性。例如，搜索区表单使用 `span="6"` 实现每行四列布局。
        ```html
        <el-row :gutter="20">
          <el-col :span="6">...</el-col>
          <el-col :span="6">...</el-col>
          <el-col :span="6">...</el-col>
          <el-col :span="6">...</el-col>
        </el-row>
        ```
*   **统一间距类**: 优先使用项目预定义的间距类（如 `mb-20` 表示 `margin-bottom: 20px`），或在样式中明确定义。

#### **2. 国际化 (Internationalization) 规范**

*   **强制使用**: 页面中所有用户可见的文本内容（包括但不限于标题、标签、占位符、按钮文本、提示消息、表格列头、动态状态文本、弹窗内容）都必须通过 `useI18n` 提供的 `t()` 函数进行国际化。
    *   **示例**:
        *   标题: `{{ t('sales.vehicleList') }}`
        *   表单标签: `:label="t('sales.vin')"`
        *   占位符: `:placeholder="t('sales.vinPlaceholder')"`
        *   表格列标题: `:label="t('sales.id')"`
        *   按钮文本: `{{ t('common.search') }}`
        *   动态文本: `{{ t(`sales.status.${row.status}`) }}`
        *   消息提示: `ElMessage.success(t('common.operationSuccessful'))`
*   **语言切换**: 如果页面需要语言切换功能，应复用 `HomeView.vue` 中 `el-select` 的实现，包括 `langOptions` 数据结构、`handleLanguageChange` 方法以及将语言保存到 `localStorage` 的逻辑。
    ```html
    <el-select v-model="locale" @change="handleLanguageChange" placeholder="Select Language" class="language-select">
      <el-option v-for="item in langOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    ```

#### **3. 搜索/筛选区域表单规范**

*   **表单布局**:
    *   `el-form` 组件必须设置 `label-position="top"`，确保表单项的标签（title）始终位于输入框的上方。
    *   **示例**: `<el-form :model="searchParams" class="search-form" label-position="top">`
*   **表单项样式**:
    *   `el-form-item` 内部应有统一的间距，防止元素过于紧凑。
    *   **样式**:
        ```scss
        .search-form {
          .el-form-item {
            margin-right: 20px; // 表单项右侧间距
            margin-bottom: 15px; // 表单项底部间距
            &:last-child {
              margin-right: 0; // 最后一个表单项移除右侧间距
            }
          }
        }
        ```
*   **输入组件**: 使用 `el-input` 或 `el-select` 等 Element Plus 组件，并包含 `placeholder` 和 `clearable` 属性，且这些属性的值必须国际化。
*   **操作按钮**:
    *   搜索按钮必须是 `type="primary"` 并带 `Search` 图标。
    *   重置按钮是默认类型。
    *   按钮组统一靠右对齐。
    *   **示例**:
        ```html
        <el-button type="primary" :icon="Search" @click="handleSearch">{{ t('common.search') }}</el-button>
        <el-button @click="resetSearch">{{ t('common.reset') }}</el-button>
        ```
    *   **样式**: 按钮之间有 10px 的左侧间距。
        ```scss
        .buttons-col {
          text-align: right; // 右对齐按钮
          .el-button {
            margin-left: 10px; // 按钮间距
          }
        }
        ```

#### **4. 数据展示（表格）区域规范**

*   **表格组件**: 统一使用 `el-table` 进行数据展示。
*   **加载状态**: 表格必须绑定 `v-loading` 属性，以在数据加载时显示加载动画。
*   **表格列定义**:
    *   `el-table-column` 的 `label` 必须国际化。
    *   `min-width` 应根据内容设置，以保证列宽充足。
*   **操作列固定**:
    *   所有表格的操作列必须固定在右侧，使用 `fixed="right"` 属性，并指定 `width`。
    *   **示例**: `<el-table-column :label="t('common.operations')" width="180" fixed="right">`
*   **操作列按钮样式**:
    *   操作列中的按钮统一使用 `link` 类型。
    *   编辑按钮使用 `type="primary"` 并带 `Edit` 图标。
    *   删除按钮使用 `type="danger"` 并带 `Delete` 图标。
    *   **示例**:
        ```html
        <el-button type="primary" :icon="Edit" link @click="handleEdit(row)">{{ t('common.edit') }}</el-button>
        <el-button type="danger" :icon="Delete" link @click="handleDelete(row)">{{ t('common.delete') }}</el-button>
        ```
*   **长文本处理**:
    *   当表格字段内容可能过长时，必须强制文本不换行，并依赖 `el-table` 自动出现的横向滚动条。
    *   **样式**:
        ```scss
        :deep(.el-table) {
          .el-table__body td,
          .el-table__header th {
            white-space: nowrap; // 禁止文本换行
          }
        }
        ```
*   **分页器**:
    *   分页组件 `el-pagination` 必须包含在 `div.pagination-container` 中。
    *   **对齐**: `pagination-container` 必须设置 `text-align: right;` 使分页器靠右对齐。
    *   **布局**: 必须使用 `layout="total, sizes, prev, pager, next, jumper"`，提供完整的页数显示、每页大小调整、翻页和跳转功能。
    *   **页码大小**: `page-sizes` 属性应包含 `[10, 20, 50, 100]`。
    *   **样式**:
        ```scss
        .pagination-container {
          margin-top: 20px; // 分页器顶部间距
          text-align: right; // 分页器右对齐
        }
        ```

#### **5. 模态框 (Dialog) 规范**

*   **组件**: 统一使用 `el-dialog` 组件。
*   **标题**: `title` 属性必须动态设置并国际化，以反映操作类型（新增/编辑）。
    *   **示例**: `:title="isEdit ? t('sales.editVehicle') : t('sales.addVehicle')"`
*   **宽度**: 对话框宽度应根据内容合理设置，但建议统一常见的宽度，例如 `width="500px"`。
*   **内部表单**:
    *   模态框内的表单 (`el-form`) 也必须设置 `label-position="top"`。
    *   **样式**: 表单项 (`el-form-item`) 之间应有统一的垂直间距。
        ```scss
        .vehicle-dialog {
          .dialog-form-modern {
            .el-form-item {
              margin-bottom: 20px; // 增加表单项垂直间距
            }
          }
        }
        ```
*   **底部按钮**:
    *   对话框底部按钮必须包含在 `span.dialog-footer-buttons` 中。
    *   **对齐**: 按钮组统一靠右对齐 (`justify-content: flex-end;`)。
    *   取消按钮使用默认类型。
    *   确认按钮使用 `type="primary"`。
    *   **样式**: 按钮之间有 10px 的左侧间距，且按钮区域顶部有 20px 内边距。
        ```scss
        .dialog-footer-buttons {
          display: flex;
          justify-content: flex-end;
          padding-top: 20px;
          .el-button {
            margin-left: 10px;
          }
        }
        ```

#### **6. 全局按钮样式规范**

*   **主要操作**: 使用 `type="primary"` (蓝色) 按钮，通常带有相关图标。
    *   示例: 搜索、新增、确认。
*   **次要操作**: 使用默认样式按钮。
    *   示例: 重置、取消。
*   **操作列按钮**: 参考“表格区域”部分，使用 `link` 类型按钮。
*   **统一间距**: 按钮之间保持 `10px` 的水平间距。

#### **7. 数据管理与 API 调用规范**

*   **API 模块化**: 所有后端 API 调用必须通过 `src/api/modules/` 下的对应模块进行封装。
    *   **示例**: `import { addVehicle, deleteVehicle, getVehicleList } from '@/api/modules/sales';`
*   **环境判断与 Mock 机制**:
    *   所有 API 模块内部必须根据环境变量 `import.meta.env.VITE_APP_USE_MOCK_API` 来决定是调用真实 API 还是使用 Mock 数据。
    *   **Mock 数据源**: Mock 数据统一存放在 `src/mock/data/` 目录下，并以清晰的 TypeScript 接口定义数据结构。
    *   **Mock 逻辑**: Mock 数据的增删改查操作应在内存中模拟，并模拟网络延迟 (`setTimeout`)。
    *   **示例 (sales.ts 中的逻辑)**:
        ```typescript
        // ...
        const USE_MOCK_API = import.meta.env.VITE_APP_USE_MOCK_API === 'true';

        export const getVehicleList = (params: VehicleListParams): Promise<PaginationResponse<VehicleListItem>> => {
          if (USE_MOCK_API) {
            return new Promise((resolve) => {
              // ... mock 数据过滤和分页逻辑 ...
            });
          } else {
            return request.get<any, PaginationResponse<VehicleListItem>>('/sales/vehicles', { params });
          }
        };
        // ...
        ```
*   **Axios 统一封装 (`src/api/index.ts`)**:
    *   所有真实 API 请求都必须通过 `src/api/index.ts` 中导出的 `request` 实例进行。
    *   **请求拦截器**: 负责在请求发出前处理，如从 `localStorage` 获取并添加 `Authorization` token。
    *   **响应拦截器**:
        *   **统一错误处理**: 自动根据后端返回的业务状态码（`res.code`）和 HTTP 状态码 (`error.response.status`) 进行判断，并显示国际化的 `ElMessage` 错误提示。
        *   **认证过期处理**: 当遇到认证失败（如 `401` HTTP 状态码或业务错误码 `10001`）时，强制清除 Token 并跳转到 `/login` 页面。
        *   **统一成功返回**: 成功响应统一返回后端 `data` 部分。
*   **错误提示国际化**: 所有通过 API 封装产生的成功/失败消息、警告、错误提示等都必须使用国际化 `i18nGlobal.t()`。

#### **8. 代码组织与技术栈规范**

*   **Vue 3 `<script setup>`**: 所有 Vue 组件都应使用 `<script setup>` 语法糖，简化组件编写。
*   **类型定义**: 重要的数据结构和接口必须在 `src/types/module.d.ts` 或其他合适的类型定义文件中明确定义。
*   **SASS 变量**: 页面中应导入并使用项目统一的 SASS 变量文件 (`@use '@/assets/styles/_variables.scss' as *;`)，以保持颜色、字体等基础样式的一致性。
*   **Element Plus 组件导入**: 所需的 Element Plus 组件应在 `<script setup>` 顶部集中导入。

#### **9. 路由 (Vue Router) 规范**

为了确保路由的统一性和正确性，所有新页面及其路由配置必须遵循以下规则：

*   **路由定义**:
    *   所有页面路由必须在 `src/router/index.ts` 文件中进行集中定义和管理。
    *   **命名规范**: 路由的 `name` 属性应使用清晰、有意义的驼峰命名法。
    *   **路径规范**: `path` 属性应使用小写字母和连字符，反映页面层级结构。
    *   **组件引用**: 路由的 `component` 属性应使用动态导入 (`() => import(...)`)，以实现路由懒加载。
    *   **元信息 (`meta`)**: 如果需要，可以为路由添加元信息，例如 `requiresAuth` (是否需要认证)、`title` (页面标题，用于动态设置或面包屑)。
        *   **示例**:
            ```typescript
            // src/router/index.ts
            import { createRouter, createWebHistory } from 'vue-router'

            const router = createRouter({
              history: createWebHistory(import.meta.env.BASE_URL),
              routes: [
                {
                  path: '/',
                  name: 'home',
                  component: () => import('@/views/HomeView.vue'),
                  meta: { title: 'Home', requiresAuth: true }
                },
                {
                  path: '/sales',
                  name: 'sales',
                  component: () => import('@/views/sales/SalesDashboard.vue'), // 示例
                  meta: { title: 'Sales Dashboard', requiresAuth: true }
                },
                // ... 其他路由
              ]
            })

            export default router
            ```
*   **页面导航**:
    *   **编程式导航**: 在 `<script setup>` 中，使用 `import { useRouter } from 'vue-router'` 获取 `router` 实例进行导航。
        *   **跳转到新页面**: `router.push({ name: 'routeName', params: { ... } })` 或 `router.push('/path/to/page')`。
        *   **替换当前路由**: `router.replace(...)`。
        *   **返回**: `router.back()` 或 `router.go(-1)`。
    *   **声明式导航**: 在 `<template>` 中，使用 `<router-link>` 组件进行导航。
        *   **示例**: `<router-link :to="{ name: 'routeName' }">Go to Page</router-link>` 或 `<router-link to="/path/to/page">Go to Page</router-link>`。
*   **动态路由和参数**:
    *   如果页面需要接收动态参数，路由路径应包含参数占位符（如 `/users/:id`）。
    *   在页面组件中，通过 `import { useRoute } from 'vue-router'` 获取 `route` 实例来访问路由参数 (`route.params`) 和查询参数 (`route.query`)。
*   **路由守卫 (Navigation Guards)**:
    *   如果项目需要进行权限控制或登录状态检查，应在 `src/router/index.ts` 中配置全局前置守卫 (`router.beforeEach`)。
    *   **示例**: 根据路由的 `meta.requiresAuth` 属性判断是否需要登录。
        ```typescript
        // src/router/index.ts
        router.beforeEach((to, from, next) => {
          const isAuthenticated = localStorage.getItem('token'); // 假设通过 Token 判断登录状态

          if (to.meta.requiresAuth && !isAuthenticated) {
            // 如果路由需要认证但用户未登录，则重定向到登录页
            next('/login');
          } else {
            // 否则正常放行
            next();
          }
        });
        ```
*   **常见路由问题与解决方案**:
    *   **症状**: `[Vue Router warn]: No match found for location with path "/home"` (或其他路径)
    *   **根本原因**: 访问的路径没有在路由配置中找到匹配的路由。
    *   **排查步骤与解决方案**:
        1.  **检查 `src/router/index.ts` 路由配置**:
            *   确认目标路径是否已正确定义为一个路由的 `path`。
            *   确认 `name` 属性是否与使用编程式导航时传入的 `name` 一致。
            *   检查是否有拼写错误或大小写不匹配。
        2.  **确保访问的路径与定义的路由匹配**:
            *   例如，如果路由定义是 `/`，则访问 `/home` 会出现此警告，应导航到 `/`。
            *   对于动态路由，确保提供了所有必要的参数。
        3.  **使用正确的路径导航**:
            *   如果路由定义是 `/`，则在代码中应使用 `router.push('/')` 而不是 `router.push('/home')`。
            *   检查 `router-link` 的 `to` 属性是否与路由定义一致。

