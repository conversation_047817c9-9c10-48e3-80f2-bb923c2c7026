我们现在要做库存管理需求的迭代,此刻我们进行需求调整. 你的目标是更新线框图,接口文档,需求文档.
相关文档位于:/Users/<USER>/Desktop/perdua_dms/dms_frontend/docs/parts-management/库存管理
##重要##你必须先阅读文档,再进行后续的改动

这个是实体信息库存表的信息：
CREATE TABLE `replenishment_order_details` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) NOT NULL COMMENT '补货单ID',
  `part_id` bigint(20) NOT NULL COMMENT '零件ID',
  `request_quantity` int(11) NOT NULL COMMENT '申请数量',
  `approved_quantity` int(11) DEFAULT NULL COMMENT '批准数量',
  `received_quantity` int(11) DEFAULT '0' COMMENT '已收数量',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT '小计',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_part_id` (`part_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='补货单明细表';

和实体关系描述，便于你理解后端数据结构：
接下来我们基于线框图进行沟通修改点
A：主页面线框图的调整点：
	1.调整数据列表，‘库位’ 字段改成 ‘占用库存’

B.库存详情弹窗线框图
	1.零件基础信息中 去掉‘品牌’，‘类别’ 两个字段
	2.库存信息信息中 增加'占用库存'字段 去掉 '仓库','库位'字段

C.单个零件库存调整弹窗线框图
	1.零件信息中 去掉'品牌','仓库','库位' 字段. 增加 ‘占用库存’,'损坏库存' 字段
	2.调整原因 去掉 '损坏调整' 类型

总结本次的需求改动点, 形成一份详细的迭代需求文档, 同时制定详细的开发计划.

前端任务中缺少了 国际化文件的调整. 请补充. 另外, 对于前端的开发任务生成一份详细的提示词, 提示词的受众是AI前端开发工程师, 请生成提示词的时候事无巨细的来描述.