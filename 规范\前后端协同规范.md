好的，非常好的思路！在前后端协同开发中，尤其是有 AI 参与时，明确这些点能极大地减少沟通成本和潜在的冲突，确保项目顺利进行。

除了我们之前讨论的 UI 设计、技术架构、技术开发以及 AI 工作流规范，以下是针对 **前后端协同** 过程中需要特别注意和规范的点，这将帮助 AI 在处理与后端交互相关的功能时，更精准地按照设计意图实现。

---

## 前后端协作规范 (供 AI 使用)

这份规范旨在为 AI 在 DMS 前端项目开发中，处理与后端交互相关的任务时提供明确的指导。AI 必须严格遵循以下原则，以确保前后端接口的正确对接、数据的一致性以及高效的协作。

### 1. API 接口契约定义与管理

* **1.1 API 契约作为唯一真理 (Single Source of Truth)**
    * **规范：** 所有 API 接口的请求参数、响应结构、HTTP 状态码以及业务错误码，都必须以**后端提供的 API 文档（如 OpenAPI/Swagger、Postman Collection）**为唯一且权威的契约定义。前端（包括 AI 生成的代码）必须严格遵守这些契约。
    * **最佳实践：**
        * 在项目初期，应由人类开发者将核心 API 契约导入到 `src/types/api.d.ts` 或相关业务模块的 `types` 文件中，并要求 AI 优先引用这些类型。
        * AI 在编写 API 请求和处理响应时，必须基于这些契约类型进行类型声明和数据访问。
    * **约束 AI：**
        * **禁止推测：** 绝不允许 AI 在 API 契约不明确时进行猜测或自行填充参数、字段。
        * **差异即报告：** 如果 AI 发现其对 API 的理解与实际契约存在差异，或者实际返回数据与契约不符，必须立即向人类开发者报告，并暂停相关开发，等待明确指示。
        * **无权修改：** AI 无权修改 API 契约定义。如果发现契约不合理或需要变更，必须通过人类开发者沟通后端。

* **1.2 API 版本管理**
    * **规范：** 如果后端 API 存在版本管理（例如 `/v1/users`, `/v2/users` 或通过请求头 `Accept: application/vnd.dms.v1+json`），前端必须严格遵循当前项目使用的 API 版本。
    * **最佳实践：**
        * API 基础路径应配置在环境变量中（如 `.env` 文件），并在 `src/api/index.ts` 中动态读取。
        * AI 在生成 API 调用时，应自动包含正确的版本前缀或版本请求头。
    * **约束 AI：** 禁止未经指令擅自更改 API 版本或请求路径。

### 2. 统一错误码处理与用户反馈

* **2.1 业务错误码处理**
    * **规范：** 除了标准的 HTTP 状态码（如 401, 403, 404, 500），后端将通过响应体返回特定的**业务错误码**和错误信息。前端必须根据这些业务错误码进行精细化的错误提示和逻辑处理。
    * **最佳实践：**
        * 在 `src/utils/errorCodeMap.ts` 或类似文件中定义一个集中式的错误码映射表，将后端业务错误码映射到国际化的、用户友好的前端提示信息。
        * `src/api/index.ts` 中的响应拦截器应负责解析业务错误码，并通过 `ElMessage.error` 或 `ElNotification` 显示对应的国际化提示。
        * 对于某些特定业务错误（如表单校验失败返回的字段错误），前端可能需要额外处理，例如将错误信息显示在对应的表单字段旁边。
    * **约束 AI：**
        * **禁止显示原始错误信息：** 绝不允许直接将后端返回的原始错误信息（`response.data.message`）展示给用户。必须通过错误码映射转换为国际化提示。
        * **未知错误码提示：** 如果遇到未在映射表中定义的业务错误码，AI 应默认显示一个通用的“操作失败，请重试”提示，并报告该未知错误码，建议添加到映射表。

### 3. 认证与授权流程协同

* **3.1 Token 管理与刷新**
    * **规范：** 前端必须严格遵循后端定义的认证流程（如 OAuth2，JWT）。这包括登录、Token (Access Token, Refresh Token) 的存储、携带、刷新以及过期处理。
    * **最佳实践：**
        * Access Token 统一通过 Axios 请求拦截器添加到 `Authorization` 请求头。
        * Refresh Token 机制应在 `src/api/index.ts` 的响应拦截器中实现，当 Access Token 过期时，自动使用 Refresh Token 请求新的 Access Token 并重试失败的请求。
        * 登录信息（Token, 用户 ID）应持久化存储在 `localStorage` 中，并由 `userStore` 管理。
    * **约束 AI：**
        * **禁止自行管理 Token：** 除了 `src/api/index.ts` 和 `src/stores/modules/user.ts` 之外，任何其他文件都不得直接操作 Token。
        * **遵守刷新机制：** AI 在生成 API 请求时，应假定 Token 刷新机制已由 Axios 拦截器处理，无需在业务代码中重复处理。

* **3.2 权限同步与检查**
    * **规范：** 用户权限数据（角色、权限点列表）必须在用户登录成功或 Token 刷新后，从后端获取并存储在 `userStore` 中。前端的所有权限判断必须以 `userStore` 中存储的权限数据为准。
    * **最佳实践：** 后端应提供一个接口用于获取当前用户的权限列表。在路由守卫和菜单渲染时，确保 `userStore` 中的权限数据是最新的。
    * **约束 AI：** 绝不允许在前端代码中硬编码权限列表。所有权限判断都必须通过 `userStore.checkPermission()` 等方法间接依赖后端提供的权限数据。

### 4. 环境配置管理

* **4.1 动态 API 端点**
    * **规范：** 不同环境（开发、测试、生产）的后端 API 端点必须通过环境变量进行配置，不允许硬编码在任何业务代码中。
    * **最佳实践：**
        * 使用 Vite 的 `.env` 文件（如 `.env.development`, `.env.production`）定义 `VITE_APP_BASE_API` 等环境变量。
        * `src/api/index.ts` 应从 `import.meta.env` 中读取这些变量作为 `axios.create` 的 `baseURL`。
    * **约束 AI：** AI 在生成 API 请求相关的配置时，必须引用 `import.meta.env` 中定义的环境变量，而不是具体的 URL 字符串。

### 5. 数据类型与字段一致性

* **5.1 严格类型匹配**
    * **规范：** 前端 TypeScript 中定义的 API 请求参数类型和响应数据接口 (`interface`) 必须与后端模型的字段名、数据类型（string, number, boolean, array, object）以及非空约束保持严格一致。
    * **最佳实践：**
        * 定期与后端进行 API 文档同步和类型对齐。
        * 对于枚举值，前端应使用 TypeScript `enum` 或字面量联合类型，并与后端约定好的值保持一致。
        * 日期时间字段：前后端应统一日期时间字符串格式（如 ISO 8601），并在前端进行解析和格式化展示。
    * **约束 AI：**
        * **遵循契约，而非推断：** 当后端字段名、类型与前端预期不符时，AI 应优先遵循后端契约，并报告差异，而非擅自修改或“适配”数据。
        * **防止静默错误：** 避免因类型不匹配导致运行时错误或静默的数据转换问题。

---

通过这份 **前后端协作规范**，AI 将在与后端交互时，拥有更明确的指引，从而减少集成风险、提高开发效率，并确保整个系统的稳定性和数据一致性。这能有效避免 AI 在前端层面的“发散”，确保其产出严格符合前后端协同的设计意图。

您觉得这些点是否涵盖了前后端协同的关键方面？我们是否可以进入下一个阶段了？