# 国际化模块化规范

## 概述

本项目采用模块化国际化架构，将国际化文件按业务模块进行组织，解决多人协作时的文件冲突问题。所有新增页面和功能都必须按照此规范进行国际化处理。

## 核心模块划分

项目国际化文件按照以下五个模块进行组织：

### 1. common 模块
- **用途**: 通用文本，跨模块使用的通用内容
- **内容**: 按钮（保存、取消、确认等）、操作（新增、编辑、删除等）、状态（成功、失败、警告等）
- **示例**:
  ```json
  {
    "save": "保存",
    "cancel": "取消",
    "confirm": "确认",
    "delete": "删除",
    "edit": "编辑",
    "add": "新增",
    "success": "操作成功",
    "error": "操作失败"
  }
  ```

### 2. sales 模块
- **用途**: 销售管理相关功能
- **内容**: 车辆管理、订单管理、客户管理、销售报表等
- **示例**:
  ```json
  {
    "vehicleList": "车辆列表",
    "orderManagement": "订单管理",
    "customerInfo": "客户信息",
    "salesReport": "销售报表"
  }
  ```

### 3. aftersales 模块
- **用途**: 售后服务相关功能
- **内容**: 维修管理、配件管理、质量检查、客户服务等
- **示例**:
  ```json
  {
    "repairManagement": "维修管理",
    "serviceRecord": "服务记录",
    "qualityCheck": "质量检查",
    "customerService": "客户服务"
  }
  ```

### 4. parts 模块
- **用途**: 零件管理相关功能
- **内容**: 零件库存、采购管理、供应商管理、零件档案等
- **示例**:
  ```json
  {
    "partInventory": "零件库存",
    "purchaseManagement": "采购管理",
    "supplierManagement": "供应商管理",
    "partArchive": "零件档案"
  }
  ```

### 5. base 模块
- **用途**: 基础系统功能
- **内容**: 登录系统、用户管理、权限管理、系统设置等
- **示例**:
  ```json
  {
    "login": "登录",
    "userManagement": "用户管理",
    "roleManagement": "角色管理",
    "systemSettings": "系统设置"
  }
  ```

## 使用方法

### 1. 单模块使用

```vue
<template>
  <div>
    <h1>{{ t('vehicleList') }}</h1>
    <el-button @click="save">{{ tc('save') }}</el-button>
    <el-button @click="cancel">{{ tc('cancel') }}</el-button>
  </div>
</template>

<script setup>
import { useModuleI18n } from '@/composables/useModuleI18n'

// 使用销售模块的国际化
const { t, tc } = useModuleI18n('sales')
// t() 用于访问当前模块的翻译
// tc() 用于访问通用模块的翻译
</script>
```

### 2. 多模块使用

```vue
<template>
  <div>
    <h1>{{ sales('vehicleList') }}</h1>
    <p>{{ parts('partInventory') }}</p>
    <el-button>{{ tc('save') }}</el-button>
  </div>
</template>

<script setup>
import { useMultiModuleI18n } from '@/composables/useModuleI18n'

// 同时使用多个模块
const { sales, parts, tc } = useMultiModuleI18n(['sales', 'parts'])
</script>
```

### 3. 消息提示国际化

```javascript
import { ElMessage, ElMessageBox } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales')

// 成功消息
ElMessage.success(tc('saveSuccess'))

// 错误消息
ElMessage.error(tc('saveError'))

// 确认对话框
ElMessageBox.confirm(
  tc('confirmDelete'),
  tc('warning'),
  {
    confirmButtonText: tc('confirm'),
    cancelButtonText: tc('cancel'),
    type: 'warning'
  }
)
```

## 开发规范

### 1. 模块选择规范

- **明确页面所属模块**: 新增页面时必须明确属于哪个模块
- **避免跨模块依赖**: 尽量避免一个页面使用多个业务模块的翻译
- **通用内容使用 common**: 所有通用按钮、操作等统一使用 common 模块

### 2. 翻译键命名规范

- **使用 camelCase**: 键名使用驼峰命名法，如 `vehicleList`、`orderDetail`
- **语义化命名**: 键名应具有明确的语义，便于理解
- **避免缩写**: 不使用缩写或数字后缀，如避免 `vehList`、`order1`
- **层级结构**: 对于复杂对象，可使用嵌套结构

### 3. 文件组织规范

```
src/locales/modules/
├── common/
│   ├── zh.json      # 中文翻译
│   └── en.json      # 英文翻译
├── sales/
│   ├── zh.json
│   └── en.json
├── aftersales/
│   ├── zh.json
│   └── en.json
├── parts/
│   ├── zh.json
│   └── en.json
└── base/
    ├── zh.json
    └── en.json
```

### 4. 代码审查要点

- **检查模块归属**: 确认翻译内容归属正确的模块
- **检查键名规范**: 确认键名符合命名规范
- **检查使用方式**: 确认使用了正确的 `useModuleI18n` 方法
- **检查通用内容**: 确认通用内容使用了 `tc()` 方法

## 最佳实践

### 1. 团队协作

- **模块责任制**: 每个开发人员负责自己模块的国际化文件
- **通用内容协商**: 修改 common 模块时需要团队讨论
- **及时同步**: 新增翻译内容及时同步到所有语言版本

### 2. 性能优化

- **按需加载**: 系统自动按需加载相关模块的翻译文件
- **避免重复**: 通用内容统一放在 common 模块，避免重复定义
- **缓存机制**: 利用模块加载器的缓存机制提高性能

### 3. 维护管理

- **定期整理**: 定期清理不再使用的翻译键
- **文档更新**: 及时更新翻译文档和示例
- **版本控制**: 重要修改时备份原始文件

## 迁移指南

### 现有代码迁移

1. **替换导入**: 将 `useI18n` 替换为 `useModuleI18n`
2. **更新翻译键**: 移除模块前缀，直接使用键名
3. **区分模块**: 明确区分当前模块和通用模块的翻译

### 迁移示例

```javascript
// 旧方式 ❌
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const title = t('sales.vehicleList')
const saveBtn = t('common.save')

// 新方式 ✅
import { useModuleI18n } from '@/composables/useModuleI18n'
const { t, tc } = useModuleI18n('sales')
const title = t('vehicleList')
const saveBtn = tc('save')
```

## 常见问题

### Q: 如何判断内容应该放在哪个模块？
A: 根据功能归属判断，如果是跨模块使用的通用内容，放在 common 模块；如果是特定业务功能，放在对应的业务模块。

### Q: 可以在一个组件中使用多个模块吗？
A: 可以，但应尽量避免。如果确实需要，使用 `useMultiModuleI18n` 方法。

### Q: 如何处理动态翻译？
A: 使用模板字符串或参数传递的方式，确保翻译键的动态性。

### Q: 翻译文件冲突如何解决？
A: 模块化设计大大减少了冲突，如果出现冲突，按照模块归属进行分离。

## 工具支持

- **自动检查**: 开发过程中自动检查翻译键的使用规范
- **类型提示**: TypeScript 提供完整的类型提示支持
- **热更新**: 开发环境支持翻译文件的热更新

---

**注意**: 本规范是强制性的，所有新增功能都必须按照此规范进行国际化处理。违反规范的代码将不予合并。 