# 工单管理重构技术方案

本方案旨在根据《页面目录结构规范》和《页面重构技术规范》对 `WorkOrderListView.vue` 进行重构，提升代码的模块化、可维护性和可读性。

## 1. 重构目标

- **遵循目录规范**：将页面和相关文件迁移到 `src/views/afterSales/workOrder/` 目录下。
- **模块化拆分**：将类型定义、API 请求、Mock 数据和子组件进行分离。
- **提升代码质量**：移除内联类型和数据，使用统一的 API 和类型定义。
- **增强可维护性**：通过组件化和模块化，降低单个文件的复杂性。

## 2. 重构分析

`WorkOrderListView.vue` 当前存在以下问题：

- **文件位置不规范**：直接位于 `src/views/`，未按 `afterSales` 模块进行组织。
- **API 模块分散**：使用 `@/api/modules/workOrder` 而非模块化的 `afterSales/workOrder`。
- **类型定义位置不当**：类型定义在 `@/types/workOrder.d.ts` 中，应迁移到 `afterSales` 目录下。
- **组件复杂度高**：单文件 881 行，包含多个弹窗和复杂逻辑，应拆分为子组件。
- **缺少独立Mock数据**：没有专门的Mock数据模块，不便于测试和开发。

## 3. 重构步骤

### 步骤 1：创建目录和文件结构

根据规范，创建以下目录和文件：

```
src/
├── views/afterSales/workOrder/
│   ├── WorkOrderView.vue               # 主页面（原 WorkOrderListView.vue）
│   └── components/
│       ├── WorkOrderSearchForm.vue     # 搜索表单组件
│       ├── WorkOrderTable.vue          # 工单表格组件
│       ├── WorkOrderFormDialog.vue     # 新增/编辑弹窗组件
│       ├── WorkOrderDetailDialog.vue   # 详情查看弹窗组件
│       └── WorkOrderStatusDialog.vue   # 状态变更弹窗组件
├── api/modules/afterSales/
│   └── workOrder.ts                    # 工单 API 模块
├── types/afterSales/
│   └── workOrder.d.ts                  # 工单类型定义
├── mock/data/afterSales/
│   └── workOrder.ts                    # 工单 Mock 数据
└── locales/modules/afterSales/
    ├── zh.json                         # 中文语言包（已存在，需更新）
    └── en.json                         # 英文语言包（已存在，需更新）
```

### 步骤 2：迁移和重构类型定义

将 `src/types/workOrder.d.ts` 迁移到 `src/types/afterSales/workOrder.d.ts`，并进行优化：

```typescript
// src/types/afterSales/workOrder.d.ts

// 工单状态枚举
export type WorkOrderStatus =
  | 'draft'                 // 草稿
  | 'pending_confirmation'  // 待客户确认
  | 'confirmed'            // 已确认
  | 'in_progress'          // 进行中
  | 'completed'            // 已完成
  | 'cancelled'            // 已取消
  | 'rejected';            // 客户拒绝

// 工单优先级枚举
export type WorkOrderPriority = 'normal' | 'urgent'; // 普通 | 紧急

// 客户来源枚举
export type CustomerSource = 'appointment' | 'walk_in'; // 预约客户 | 自然到店客户

// 工单类型枚举
export type WorkOrderType = 'repair' | 'maintenance' | 'claim'; // 维修 | 保养 | 保险

// 付款状态枚举
export type PaymentStatus = 'unpaid' | 'partial' | 'paid'; // 未付款 | 部分付款 | 已付款

// 工单列表项接口
export interface WorkOrderListItem {
  workOrderId: string;
  workOrderNumber: string;
  status: WorkOrderStatus;
  priority: WorkOrderPriority;
  customerSource: CustomerSource;
  workOrderType: WorkOrderType;
  customerName: string;
  customerPhone: string;
  licensePlate: string;
  vehicleModel: string;
  vehicleVin: string;
  serviceAdvisor: string;
  technician: string;
  estimatedAmount: number;
  actualAmount: number;
  paymentStatus: PaymentStatus;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  description: string;
  notes?: string;
}

// 工单详情接口
export interface WorkOrderDetail extends WorkOrderListItem {
  serviceItems: ServiceItem[];
  partItems: PartItem[];
  laborItems: LaborItem[];
  customerSignature?: string;
  technicianNotes?: string;
}

// 服务项目接口
export interface ServiceItem {
  id: string;
  name: string;
  description: string;
  price: number;
  quantity: number;
  total: number;
}

// 配件项目接口
export interface PartItem {
  id: string;
  partNumber: string;
  partName: string;
  brand: string;
  price: number;
  quantity: number;
  total: number;
  supplier?: string;
}

// 工时项目接口
export interface LaborItem {
  id: string;
  operation: string;
  description: string;
  standardHours: number;
  actualHours: number;
  hourlyRate: number;
  total: number;
  technician: string;
}

// 工单搜索参数接口
export interface WorkOrderSearchParams {
  workOrderNumber?: string;
  status?: WorkOrderStatus;
  priority?: WorkOrderPriority;
  customerSource?: CustomerSource;
  workOrderType?: WorkOrderType;
  customerName?: string;
  customerPhone?: string;
  licensePlate?: string;
  serviceAdvisor?: string;
  technician?: string;
  createdAtStart?: string;
  createdAtEnd?: string;
  page?: number;
  pageSize?: number;
}

// 工单分页响应接口
export interface WorkOrderPageResponse {
  list: WorkOrderListItem[];
  total: number;
}

// 工单表单数据接口
export interface WorkOrderFormData extends Omit<WorkOrderListItem, 'workOrderId' | 'workOrderNumber' | 'createdAt' | 'updatedAt'> {
  workOrderId?: string;
  serviceItems?: ServiceItem[];
  partItems?: PartItem[];
  laborItems?: LaborItem[];
}

// 状态变更表单接口
export interface StatusChangeForm {
  workOrderId: string;
  newStatus: WorkOrderStatus;
  reason?: string;
  notes?: string;
}
```

### 步骤 3：重构 Mock 数据

创建 `src/mock/data/afterSales/workOrder.ts`，提供完整的Mock数据支持：

```typescript
// src/mock/data/afterSales/workOrder.ts

import type { 
  WorkOrderSearchParams, 
  WorkOrderPageResponse, 
  WorkOrderListItem,
  WorkOrderDetail,
  WorkOrderFormData,
  WorkOrderStatus,
  WorkOrderPriority,
  CustomerSource,
  WorkOrderType,
  PaymentStatus
} from '@/types/afterSales/workOrder.d.ts';

// 生成动态 Mock 数据
function generateMockWorkOrderData(): WorkOrderListItem[] {
  const data: WorkOrderListItem[] = [];
  const customerNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  const advisors = ['李明', '王强', '张伟', '刘洋'];
  const technicians = ['陈师傅', '刘师傅', '王师傅', '张师傅'];
  const licensePlates = ['京A12345', '沪B67890', '粤C54321', '浙D98765', '苏E11111'];
  const vehicleModels = ['Model Y 2023', 'Model 3 2022', 'Model X 2024', 'Model S 2023'];
  const statuses: WorkOrderStatus[] = ['draft', 'pending_confirmation', 'confirmed', 'in_progress', 'completed', 'cancelled'];
  const priorities: WorkOrderPriority[] = ['normal', 'urgent'];
  const customerSources: CustomerSource[] = ['appointment', 'walk_in'];
  const workOrderTypes: WorkOrderType[] = ['repair', 'maintenance', 'claim'];
  const paymentStatuses: PaymentStatus[] = ['unpaid', 'partial', 'paid'];

  for (let i = 1; i <= 50; i++) {
    const createDate = new Date();
    createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 60));
    const updateDate = new Date(createDate.getTime() + Math.random() * 86400000 * 7);
    
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const completedAt = status === 'completed' ? 
      new Date(updateDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : undefined;

    const estimatedAmount = Math.floor(Math.random() * 5000) + 500;
    const actualAmount = status === 'completed' ? estimatedAmount + Math.floor(Math.random() * 500) - 250 : 0;

    data.push({
      workOrderId: `WO${String(i).padStart(8, '0')}`,
      workOrderNumber: `WO${new Date().getFullYear()}${String(i).padStart(6, '0')}`,
      status,
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      customerSource: customerSources[Math.floor(Math.random() * customerSources.length)],
      workOrderType: workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)],
      customerName: customerNames[Math.floor(Math.random() * customerNames.length)],
      customerPhone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      licensePlate: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleVin: `LFV${Math.random().toString(36).substr(2, 14).toUpperCase()}`,
      serviceAdvisor: advisors[Math.floor(Math.random() * advisors.length)],
      technician: technicians[Math.floor(Math.random() * technicians.length)],
      estimatedAmount,
      actualAmount,
      paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
      createdAt: createDate.toISOString().slice(0, 16).replace('T', ' '),
      updatedAt: updateDate.toISOString().slice(0, 16).replace('T', ' '),
      completedAt,
      description: `${workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)] === 'repair' ? '维修' : '保养'}服务`,
      notes: Math.random() > 0.7 ? '客户要求使用原厂配件' : undefined
    });
  }
  
  return data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
}

const mockWorkOrderData = generateMockWorkOrderData();

export const getWorkOrderList = (params: WorkOrderSearchParams): Promise<WorkOrderPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockWorkOrderData];

      // 应用筛选条件
      if (params.workOrderNumber) {
        filteredData = filteredData.filter(item => 
          item.workOrderNumber.toLowerCase().includes(params.workOrderNumber!.toLowerCase())
        );
      }

      if (params.status) {
        filteredData = filteredData.filter(item => item.status === params.status);
      }

      if (params.priority) {
        filteredData = filteredData.filter(item => item.priority === params.priority);
      }

      if (params.customerSource) {
        filteredData = filteredData.filter(item => item.customerSource === params.customerSource);
      }

      if (params.workOrderType) {
        filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType);
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item => 
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.customerPhone) {
        filteredData = filteredData.filter(item => 
          item.customerPhone.includes(params.customerPhone!)
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item => 
          item.licensePlate.includes(params.licensePlate!)
        );
      }

      if (params.serviceAdvisor) {
        filteredData = filteredData.filter(item => 
          item.serviceAdvisor.includes(params.serviceAdvisor!)
        );
      }

      if (params.technician) {
        filteredData = filteredData.filter(item => 
          item.technician.includes(params.technician!)
        );
      }

      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter(item => {
          const itemDate = item.createdAt.split(' ')[0];
          return itemDate >= params.createdAtStart! && itemDate <= params.createdAtEnd!;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};

export const getWorkOrderDetail = (workOrderId: string): Promise<WorkOrderDetail> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const workOrder = mockWorkOrderData.find(item => item.workOrderId === workOrderId);
      if (workOrder) {
        const detail: WorkOrderDetail = {
          ...workOrder,
          serviceItems: [
            { id: '1', name: '机油更换', description: '全合成机油', price: 300, quantity: 1, total: 300 },
            { id: '2', name: '机滤更换', description: '原厂机滤', price: 80, quantity: 1, total: 80 }
          ],
          partItems: [
            { id: '1', partNumber: 'P001', partName: '机油滤芯', brand: '原厂', price: 80, quantity: 1, total: 80 },
            { id: '2', partNumber: 'P002', partName: '机油', brand: '美孚1号', price: 300, quantity: 1, total: 300 }
          ],
          laborItems: [
            { id: '1', operation: '机油更换', description: '更换机油和机滤', standardHours: 0.5, actualHours: 0.5, hourlyRate: 120, total: 60, technician: workOrder.technician }
          ],
          technicianNotes: '车辆保养正常，建议下次保养时间：6个月后'
        };
        resolve(detail);
      } else {
        reject(new Error('工单不存在'));
      }
    }, 500);
  });
};

export const createWorkOrder = (data: WorkOrderFormData): Promise<{ success: boolean; workOrderId: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = `WO${String(mockWorkOrderData.length + 1).padStart(8, '0')}`;
      const newNumber = `WO${new Date().getFullYear()}${String(mockWorkOrderData.length + 1).padStart(6, '0')}`;
      const now = new Date().toISOString().slice(0, 16).replace('T', ' ');
      
      const newWorkOrder: WorkOrderListItem = {
        ...data,
        workOrderId: newId,
        workOrderNumber: newNumber,
        createdAt: now,
        updatedAt: now
      } as WorkOrderListItem;
      
      mockWorkOrderData.unshift(newWorkOrder);
      
      resolve({
        success: true,
        workOrderId: newId
      });
    }, 800);
  });
};

export const updateWorkOrder = (workOrderId: string, data: Partial<WorkOrderFormData>): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === workOrderId);
      if (index !== -1) {
        mockWorkOrderData[index] = {
          ...mockWorkOrderData[index],
          ...data,
          updatedAt: new Date().toISOString().slice(0, 16).replace('T', ' ')
        };
      }
      
      resolve({ success: true });
    }, 800);
  });
};

export const deleteWorkOrder = (workOrderId: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === workOrderId);
      if (index !== -1) {
        mockWorkOrderData.splice(index, 1);
      }
      
      resolve({ success: true });
    }, 500);
  });
};

export const changeWorkOrderStatus = (workOrderId: string, newStatus: WorkOrderStatus, reason?: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === workOrderId);
      if (index !== -1) {
        mockWorkOrderData[index].status = newStatus;
        mockWorkOrderData[index].updatedAt = new Date().toISOString().slice(0, 16).replace('T', ' ');
        if (newStatus === 'completed') {
          mockWorkOrderData[index].completedAt = new Date().toISOString().slice(0, 16).replace('T', ' ');
        }
      }
      
      resolve({ success: true });
    }, 500);
  });
};
```

### 步骤 4：创建 API 模块

创建 `src/api/modules/afterSales/workOrder.ts`，统一管理 API 请求：

```typescript
// src/api/modules/afterSales/workOrder.ts

import request from '@/api';
import type {
  WorkOrderSearchParams,
  WorkOrderPageResponse,
  WorkOrderDetail,
  WorkOrderFormData,
  WorkOrderStatus
} from '@/types/afterSales/workOrder.d.ts';
import {
  getWorkOrderList as getMockWorkOrderList,
  getWorkOrderDetail as getMockWorkOrderDetail,
  createWorkOrder as createMockWorkOrder,
  updateWorkOrder as updateMockWorkOrder,
  deleteWorkOrder as deleteMockWorkOrder,
  changeWorkOrderStatus as changeMockWorkOrderStatus
} from '@/mock/data/afterSales/workOrder';

let USE_MOCK_API_TEMP = true;

export const getWorkOrderList = (params: WorkOrderSearchParams): Promise<WorkOrderPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkOrderList(params);
  }
  return request.get<any, WorkOrderPageResponse>('/after-sales/work-orders', { params });
};

export const getWorkOrderDetail = (workOrderId: string): Promise<WorkOrderDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkOrderDetail(workOrderId);
  }
  return request.get<any, WorkOrderDetail>(`/after-sales/work-orders/${workOrderId}`);
};

export const createWorkOrder = (data: WorkOrderFormData): Promise<{ success: boolean; workOrderId: string }> => {
  if (USE_MOCK_API_TEMP) {
    return createMockWorkOrder(data);
  }
  return request.post<any, { success: boolean; workOrderId: string }>('/after-sales/work-orders', data);
};

export const updateWorkOrder = (workOrderId: string, data: Partial<WorkOrderFormData>): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return updateMockWorkOrder(workOrderId, data);
  }
  return request.put<any, { success: boolean }>(`/after-sales/work-orders/${workOrderId}`, data);
};

export const deleteWorkOrder = (workOrderId: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return deleteMockWorkOrder(workOrderId);
  }
  return request.delete<any, { success: boolean }>(`/after-sales/work-orders/${workOrderId}`);
};

export const changeWorkOrderStatus = (workOrderId: string, newStatus: WorkOrderStatus, reason?: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return changeMockWorkOrderStatus(workOrderId, newStatus, reason);
  }
  return request.patch<any, { success: boolean }>(`/after-sales/work-orders/${workOrderId}/status`, {
    status: newStatus,
    reason
  });
};
```

### 步骤 5：更新国际化文件

在现有的 `src/locales/modules/afterSales/zh.json` 和 `en.json` 中，将工单相关的翻译组织在 `workOrder` 键下：

```json
// src/locales/modules/afterSales/zh.json (新增部分)
{
  "workOrder": {
    "title": "工单管理",
    "searchForm": {
      "workOrderNumber": "工单号",
      "workOrderNumberPlaceholder": "请输入工单号",
      "status": "工单状态",
      "priority": "优先级",
      "customerSource": "客户来源",
      "workOrderType": "工单类型",
      "customerName": "客户姓名",
      "customerNamePlaceholder": "请输入客户姓名",
      "customerPhone": "客户电话",
      "customerPhonePlaceholder": "请输入客户电话",
      "licensePlate": "车牌号",
      "licensePlatePlaceholder": "请输入车牌号",
      "serviceAdvisor": "服务顾问",
      "serviceAdvisorPlaceholder": "请选择服务顾问",
      "technician": "技师",
      "technicianPlaceholder": "请选择技师",
      "createdAt": "创建时间"
    },
    "status": {
      "draft": "草稿",
      "pending_confirmation": "待客户确认",
      "confirmed": "已确认",
      "in_progress": "进行中",
      "completed": "已完成",
      "cancelled": "已取消",
      "rejected": "客户拒绝"
    },
    "priority": {
      "normal": "普通",
      "urgent": "紧急"
    },
    "customerSource": {
      "appointment": "预约客户",
      "walk_in": "自然到店"
    },
    "workOrderType": {
      "repair": "维修",
      "maintenance": "保养",
      "claim": "保险"
    },
    "paymentStatus": {
      "unpaid": "未付款",
      "partial": "部分付款",
      "paid": "已付款"
    },
    "table": {
      "workOrderNumber": "工单号",
      "status": "状态",
      "priority": "优先级",
      "customerSource": "客户来源",
      "workOrderType": "工单类型",
      "customerName": "客户姓名",
      "customerPhone": "客户电话",
      "licensePlate": "车牌号",
      "vehicleModel": "车型",
      "serviceAdvisor": "服务顾问",
      "technician": "技师",
      "estimatedAmount": "预估金额",
      "actualAmount": "实际金额",
      "paymentStatus": "付款状态",
      "createdAt": "创建时间",
      "completedAt": "完成时间"
    },
    "actions": {
      "create": "新建工单",
      "edit": "编辑",
      "view": "查看详情",
      "delete": "删除",
      "cancel": "取消工单",
      "confirm": "确认工单",
      "complete": "完成工单",
      "print": "打印",
      "export": "导出",
      "addItems": "添加项目",
      "submitApproval": "提交审批"
    },
    "dialog": {
      "createTitle": "新建工单",
      "editTitle": "编辑工单",
      "detailTitle": "工单详情",
      "statusChangeTitle": "状态变更",
      "deleteConfirm": "确定要删除这个工单吗？",
      "cancelConfirm": "确定要取消这个工单吗？",
      "completeConfirm": "确定要完成这个工单吗？",
      "statusChangeReason": "变更原因",
      "statusChangeReasonPlaceholder": "请输入状态变更原因"
    },
    "form": {
      "basicInfo": "基本信息",
      "customerInfo": "客户信息",
      "vehicleInfo": "车辆信息",
      "serviceInfo": "服务信息",
      "serviceItems": "服务项目",
      "partItems": "配件项目",
      "laborItems": "工时项目",
      "notes": "备注",
      "notesPlaceholder": "请输入备注信息"
    },
    "messages": {
      "createSuccess": "工单创建成功",
      "updateSuccess": "工单更新成功",
      "deleteSuccess": "工单删除成功",
      "statusChangeSuccess": "状态变更成功",
      "operationFailed": "操作失败",
      "confirmDelete": "确定要删除这个工单吗？此操作不可撤销。",
      "confirmCancel": "确定要取消这个工单吗？",
      "confirmComplete": "确定要完成这个工单吗？"
    }
  }
}
```

### 步骤 6：创建子组件

#### `WorkOrderSearchForm.vue` - 搜索表单组件
```vue
<!-- src/views/afterSales/workOrder/components/WorkOrderSearchForm.vue -->
<script setup lang="ts">
import { ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElButton, ElRow, ElCol } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderSearchParams } from '@/types/afterSales/workOrder.d.ts';

interface Props {
  searchParams: WorkOrderSearchParams;
  dateRange: [string, string] | null;
  serviceAdvisorOptions: Array<{ label: string; value: string }>;
  technicianOptions: Array<{ label: string; value: string }>;
  statusOptions: Array<{ label: string; value: string }>;
  priorityOptions: Array<{ label: string; value: string }>;
  customerSourceOptions: Array<{ label: string; value: string }>;
  workOrderTypeOptions: Array<{ label: string; value: string }>;
}

interface Emits {
  (e: 'update:searchParams', value: WorkOrderSearchParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.workOrder');

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const updateSearchParams = (field: keyof WorkOrderSearchParams, value: any) => {
  emit('update:searchParams', { ...props.searchParams, [field]: value });
};

const updateDateRange = (value: [string, string] | null) => {
  emit('update:dateRange', value);
};
</script>

<template>
  <el-card class="mb-20 search-card">
    <el-form :model="searchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.workOrderNumber')">
            <el-input
              :model-value="searchParams.workOrderNumber"
              @update:model-value="(val) => updateSearchParams('workOrderNumber', val)"
              :placeholder="t('searchForm.workOrderNumberPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.status')">
            <el-select
              :model-value="searchParams.status"
              @update:model-value="(val) => updateSearchParams('status', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.priority')">
            <el-select
              :model-value="searchParams.priority"
              @update:model-value="(val) => updateSearchParams('priority', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in priorityOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.customerSource')">
            <el-select
              :model-value="searchParams.customerSource"
              @update:model-value="(val) => updateSearchParams('customerSource', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in customerSourceOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.workOrderType')">
            <el-select
              :model-value="searchParams.workOrderType"
              @update:model-value="(val) => updateSearchParams('workOrderType', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in workOrderTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.customerName')">
            <el-input
              :model-value="searchParams.customerName"
              @update:model-value="(val) => updateSearchParams('customerName', val)"
              :placeholder="t('searchForm.customerNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.licensePlate')">
            <el-input
              :model-value="searchParams.licensePlate"
              @update:model-value="(val) => updateSearchParams('licensePlate', val)"
              :placeholder="t('searchForm.licensePlatePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.createdAt')">
            <el-date-picker
              :model-value="dateRange"
              @update:model-value="updateDateRange"
              type="daterange"
              range-separator="-"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" class="buttons-col">
          <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
          <el-button @click="handleReset">{{ tc('reset') }}</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
  .buttons-col {
    text-align: right;
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
```

#### `WorkOrderTable.vue` - 工单表格组件
```vue
<!-- src/views/afterSales/workOrder/components/WorkOrderTable.vue -->
<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElButton, ElPagination, ElTag } from 'element-plus';
import { View, Edit, Delete, Check, Printer, Plus } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderListItem } from '@/types/afterSales/workOrder.d.ts';

interface Props {
  workOrderList: WorkOrderListItem[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

interface Emits {
  (e: 'view-detail', row: WorkOrderListItem): void;
  (e: 'edit', row: WorkOrderListItem): void;
  (e: 'delete', row: WorkOrderListItem): void;
  (e: 'cancel', row: WorkOrderListItem): void;
  (e: 'complete', row: WorkOrderListItem): void;
  (e: 'add-items', row: WorkOrderListItem): void;
  (e: 'submit-approval', row: WorkOrderListItem): void;
  (e: 'print', row: WorkOrderListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.workOrder');

const getStatusType = (status: string) => {
  const statusMap = {
    draft: 'info',
    pending_confirmation: 'warning',
    confirmed: 'primary',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger',
    rejected: 'danger'
  };
  return statusMap[status as keyof typeof statusMap] || 'info';
};

const getPriorityType = (priority: string) => {
  return priority === 'urgent' ? 'danger' : 'primary';
};

const canEdit = (row: WorkOrderListItem) => {
  return ['draft', 'pending_confirmation'].includes(row.status);
};

const canDelete = (row: WorkOrderListItem) => {
  return row.status === 'draft';
};

const canCancel = (row: WorkOrderListItem) => {
  return ['pending_confirmation', 'confirmed'].includes(row.status);
};

const canComplete = (row: WorkOrderListItem) => {
  return row.status === 'in_progress';
};

const canAddItems = (row: WorkOrderListItem) => {
  return ['draft', 'confirmed', 'in_progress'].includes(row.status);
};

const canSubmitApproval = (row: WorkOrderListItem) => {
  return row.status === 'draft';
};

const formatAmount = (amount: number) => {
  return `¥${amount.toFixed(2)}`;
};

const handleViewDetail = (row: WorkOrderListItem) => {
  emit('view-detail', row);
};

const handleEdit = (row: WorkOrderListItem) => {
  emit('edit', row);
};

const handleDelete = (row: WorkOrderListItem) => {
  emit('delete', row);
};

const handleCancel = (row: WorkOrderListItem) => {
  emit('cancel', row);
};

const handleComplete = (row: WorkOrderListItem) => {
  emit('complete', row);
};

const handleAddItems = (row: WorkOrderListItem) => {
  emit('add-items', row);
};

const handleSubmitApproval = (row: WorkOrderListItem) => {
  emit('submit-approval', row);
};

const handlePrint = (row: WorkOrderListItem) => {
  emit('print', row);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (size: number) => {
  emit('page-size-change', size);
};
</script>

<template>
  <el-card class="table-card">
    <el-table
      :data="workOrderList"
      v-loading="loading"
      style="width: 100%"
      border
      :empty-text="tc('noData')"
    >
      <el-table-column type="index" :label="tc('index')" width="60" />
      <el-table-column prop="workOrderNumber" :label="t('table.workOrderNumber')" min-width="140" />
      <el-table-column prop="status" :label="t('table.status')" min-width="120">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ t(`status.${scope.row.status}`) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="priority" :label="t('table.priority')" min-width="100">
        <template #default="scope">
          <el-tag :type="getPriorityType(scope.row.priority)" size="small">
            {{ t(`priority.${scope.row.priority}`) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="customerSource" :label="t('table.customerSource')" min-width="120">
        <template #default="scope">
          {{ t(`customerSource.${scope.row.customerSource}`) }}
        </template>
      </el-table-column>
      <el-table-column prop="workOrderType" :label="t('table.workOrderType')" min-width="100">
        <template #default="scope">
          {{ t(`workOrderType.${scope.row.workOrderType}`) }}
        </template>
      </el-table-column>
      <el-table-column prop="customerName" :label="t('table.customerName')" min-width="100" />
      <el-table-column prop="customerPhone" :label="t('table.customerPhone')" min-width="120" />
      <el-table-column prop="licensePlate" :label="t('table.licensePlate')" min-width="100" />
      <el-table-column prop="vehicleModel" :label="t('table.vehicleModel')" min-width="120" />
      <el-table-column prop="serviceAdvisor" :label="t('table.serviceAdvisor')" min-width="100" />
      <el-table-column prop="technician" :label="t('table.technician')" min-width="100" />
      <el-table-column prop="estimatedAmount" :label="t('table.estimatedAmount')" min-width="120">
        <template #default="scope">
          {{ formatAmount(scope.row.estimatedAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="actualAmount" :label="t('table.actualAmount')" min-width="120">
        <template #default="scope">
          {{ scope.row.actualAmount ? formatAmount(scope.row.actualAmount) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" :label="t('table.createdAt')" min-width="140" />
      <el-table-column prop="completedAt" :label="t('table.completedAt')" min-width="140">
        <template #default="scope">
          {{ scope.row.completedAt || '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="tc('operations')" width="300" fixed="right">
        <template #default="scope">
          <el-button type="primary" :icon="View" link @click="handleViewDetail(scope.row)">
            {{ t('actions.view') }}
          </el-button>

          <el-button
            v-if="canEdit(scope.row)"
            type="primary"
            :icon="Edit"
            link
            @click="handleEdit(scope.row)"
          >
            {{ t('actions.edit') }}
          </el-button>

          <el-button
            v-if="canAddItems(scope.row)"
            type="warning"
            :icon="Plus"
            link
            @click="handleAddItems(scope.row)"
          >
            {{ t('actions.addItems') }}
          </el-button>

          <el-button
            v-if="canSubmitApproval(scope.row)"
            type="success"
            :icon="Check"
            link
            @click="handleSubmitApproval(scope.row)"
          >
            {{ t('actions.submitApproval') }}
          </el-button>

          <el-button
            v-if="canComplete(scope.row)"
            type="success"
            :icon="Check"
            link
            @click="handleComplete(scope.row)"
          >
            {{ t('actions.complete') }}
          </el-button>

          <el-button
            v-if="canCancel(scope.row)"
            type="danger"
            :icon="Delete"
            link
            @click="handleCancel(scope.row)"
          >
            {{ t('actions.cancel') }}
          </el-button>

          <el-button
            v-if="canDelete(scope.row)"
            type="danger"
            :icon="Delete"
            link
            @click="handleDelete(scope.row)"
          >
            {{ t('actions.delete') }}
          </el-button>

          <el-button type="info" :icon="Printer" link @click="handlePrint(scope.row)">
            {{ t('actions.print') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
```

### 步骤 7：重构主页面 `WorkOrderView.vue`

- **移动和重命名**：将 `WorkOrderListView.vue` 移动到 `src/views/afterSales/workOrder/` 并重命名为 `WorkOrderView.vue`。
- **移除内联逻辑**：将搜索表单和表格逻辑移动到子组件。
- **更新数据获取**：使用新的 API 模块获取数据。
- **更新类型引用**：从 `@/types/afterSales/workOrder.d.ts` 导入类型。
- **更新国际化引用**：使用 `useModuleI18n('afterSales.workOrder')`。
- **引入子组件**：在模板中引入并使用子组件。

```vue
<!-- src/views/afterSales/workOrder/WorkOrderView.vue -->
<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getWorkOrderList,
  getWorkOrderDetail,
  createWorkOrder,
  updateWorkOrder,
  deleteWorkOrder,
  changeWorkOrderStatus
} from '@/api/modules/afterSales/workOrder';
import { getServiceAdvisorList, getTechnicianList } from '@/api/modules/masterData';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type {
  WorkOrderListItem,
  WorkOrderSearchParams,
  WorkOrderFormData,
  WorkOrderStatus
} from '@/types/afterSales/workOrder.d.ts';

// 导入子组件
import WorkOrderSearchForm from './components/WorkOrderSearchForm.vue';
import WorkOrderTable from './components/WorkOrderTable.vue';
import WorkOrderFormDialog from './components/WorkOrderFormDialog.vue';
import WorkOrderDetailDialog from './components/WorkOrderDetailDialog.vue';
import WorkOrderStatusDialog from './components/WorkOrderStatusDialog.vue';

const { t, tc } = useModuleI18n('afterSales.workOrder');

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.WORK_ORDER_PRIORITY,
  DICTIONARY_TYPES.WORK_ORDER_STATUS,
  DICTIONARY_TYPES.WORK_ORDER_TYPE,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.BOOLEAN_TYPE
]);

// 搜索相关
const searchParams = reactive<WorkOrderSearchParams>({
  workOrderNumber: '',
  status: undefined,
  priority: undefined,
  customerSource: undefined,
  workOrderType: undefined,
  customerName: '',
  customerPhone: '',
  licensePlate: '',
  serviceAdvisor: '',
  technician: '',
  createdAtStart: '',
  createdAtEnd: '',
  page: 1,
  pageSize: 20,
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.createdAtStart = newVal[0];
    searchParams.createdAtEnd = newVal[1];
  } else {
    searchParams.createdAtStart = '';
    searchParams.createdAtEnd = '';
  }
});

const workOrderList = ref<WorkOrderListItem[]>([]);
const loading = ref(false);
const serviceAdvisorOptions = ref([]);
const technicianOptions = ref([]);

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
});

// 弹窗相关
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const statusDialogVisible = ref(false);
const currentWorkOrder = ref<WorkOrderListItem | null>(null);
const isEdit = ref(false);

// 字典选项
const statusOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.WORK_ORDER_STATUS).map(item => ({ label: item.name, value: item.code }))
);
const priorityOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.WORK_ORDER_PRIORITY).map(item => ({ label: item.name, value: item.code }))
);
const customerSourceOptions = computed(() => [
  { label: t('customerSource.appointment'), value: 'appointment' },
  { label: t('customerSource.walk_in'), value: 'walk_in' }
]);
const workOrderTypeOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.WORK_ORDER_TYPE).map(item => ({ label: item.name, value: item.code }))
);

// 获取工单列表
const fetchWorkOrderList = async () => {
  loading.value = true;
  try {
    const response = await getWorkOrderList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    workOrderList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch work order list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 获取服务顾问列表
const fetchServiceAdvisorList = async () => {
  try {
    const response = await getServiceAdvisorList();
    serviceAdvisorOptions.value = response.map(item => ({ label: item.name, value: item.id }));
  } catch (error) {
    console.error('Failed to fetch service advisor list:', error);
  }
};

// 获取技师列表
const fetchTechnicianList = async () => {
  try {
    const response = await getTechnicianList();
    technicianOptions.value = response.map(item => ({ label: item.name, value: item.id }));
  } catch (error) {
    console.error('Failed to fetch technician list:', error);
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchWorkOrderList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    workOrderNumber: '',
    status: undefined,
    priority: undefined,
    customerSource: undefined,
    workOrderType: undefined,
    customerName: '',
    customerPhone: '',
    licensePlate: '',
    serviceAdvisor: '',
    technician: '',
    createdAtStart: '',
    createdAtEnd: '',
    page: 1,
    pageSize: 20,
  });
  dateRange.value = null;
  fetchWorkOrderList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchWorkOrderList();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchWorkOrderList();
};

// 新建工单
const handleCreate = () => {
  currentWorkOrder.value = null;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 编辑工单
const handleEdit = (row: WorkOrderListItem) => {
  currentWorkOrder.value = row;
  isEdit.value = true;
  formDialogVisible.value = true;
};

// 查看详情
const handleViewDetail = (row: WorkOrderListItem) => {
  currentWorkOrder.value = row;
  detailDialogVisible.value = true;
};

// 删除工单
const handleDelete = async (row: WorkOrderListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmDelete'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await deleteWorkOrder(row.workOrderId);
        ElMessage.success(t('messages.deleteSuccess'));
        fetchWorkOrderList();
      } catch (error) {
        console.error('Failed to delete work order:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 取消工单
const handleCancel = async (row: WorkOrderListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmCancel'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await changeWorkOrderStatus(row.workOrderId, 'cancelled', '用户取消');
        ElMessage.success(t('messages.statusChangeSuccess'));
        fetchWorkOrderList();
      } catch (error) {
        console.error('Failed to cancel work order:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 完成工单
const handleComplete = async (row: WorkOrderListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmComplete'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await changeWorkOrderStatus(row.workOrderId, 'completed', '工单完成');
        ElMessage.success(t('messages.statusChangeSuccess'));
        fetchWorkOrderList();
      } catch (error) {
        console.error('Failed to complete work order:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 添加项目
const handleAddItems = (row: WorkOrderListItem) => {
  // 实现添加项目逻辑
  console.log('Add items for work order:', row.workOrderId);
};

// 提交审批
const handleSubmitApproval = (row: WorkOrderListItem) => {
  // 实现提交审批逻辑
  console.log('Submit approval for work order:', row.workOrderId);
};

// 打印工单
const handlePrint = (row: WorkOrderListItem) => {
  // 实现打印逻辑
  console.log('Print work order:', row.workOrderId);
};

// 导出数据
const handleExport = () => {
  // 实现导出逻辑
  console.log('Export work orders');
};

// 表单提交成功
const handleFormSuccess = () => {
  formDialogVisible.value = false;
  fetchWorkOrderList();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchServiceAdvisorList();
  fetchTechnicianList();
  fetchWorkOrderList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索表单 -->
    <WorkOrderSearchForm
      v-model:search-params="searchParams"
      v-model:date-range="dateRange"
      :service-advisor-options="serviceAdvisorOptions"
      :technician-options="technicianOptions"
      :status-options="statusOptions"
      :priority-options="priorityOptions"
      :customer-source-options="customerSourceOptions"
      :work-order-type-options="workOrderTypeOptions"
      @search="handleSearch"
      @reset="resetSearch"
    />

    <!-- 操作按钮 -->
    <el-card class="mb-20 operation-card">
      <el-button type="primary" :icon="Plus" @click="handleCreate">
        {{ t('actions.create') }}
      </el-button>
      <el-button type="success" :icon="Download" @click="handleExport">
        {{ t('actions.export') }}
      </el-button>
    </el-card>

    <!-- 数据表格 -->
    <WorkOrderTable
      :work-order-list="workOrderList"
      :loading="loading"
      :pagination="pagination"
      @view-detail="handleViewDetail"
      @edit="handleEdit"
      @delete="handleDelete"
      @cancel="handleCancel"
      @complete="handleComplete"
      @add-items="handleAddItems"
      @submit-approval="handleSubmitApproval"
      @print="handlePrint"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 工单表单弹窗 -->
    <WorkOrderFormDialog
      v-model:visible="formDialogVisible"
      :work-order="currentWorkOrder"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 工单详情弹窗 -->
    <WorkOrderDetailDialog
      v-model:visible="detailDialogVisible"
      :work-order="currentWorkOrder"
    />

    <!-- 状态变更弹窗 -->
    <WorkOrderStatusDialog
      v-model:visible="statusDialogVisible"
      :work-order="currentWorkOrder"
      @success="handleFormSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.operation-card {
  margin-bottom: 20px;

  .el-button {
    margin-right: 10px;
  }
}
</style>
```

### 步骤 8：更新路由配置

修改 `src/router/index.ts`，更新工单页面的路由配置：

```typescript
// src/router/index.ts
{
  path: '/after-sales/work-order',
  name: 'WorkOrderManagement',
  component: () => import('@/views/afterSales/workOrder/WorkOrderView.vue'),
  meta: {
    title: 'menu.workOrderManagement',
    requiresAuth: true,
    icon: 'Document'
  }
}
```

## 4. 预期收益

- **结构清晰**：项目结构符合团队规范，新成员更容易上手。
- **职责明确**：每个文件和组件的职责更加单一，便于理解和修改。
- **易于维护**：修改或扩展功能时，只需关注相关模块，降低了代码耦合带来的风险。
- **提升开发效率**：模块化和组件化使得代码复用更加方便。
- **数据动态化**：Mock 数据支持动态生成，便于测试不同场景。
- **类型安全**：完整的 TypeScript 类型定义，提高代码质量和开发体验。

## 5. 重构检查清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/workOrder/WorkOrderView.vue`
- [ ] API模块创建在 `src/api/modules/afterSales/workOrder.ts`
- [ ] Mock数据创建在 `src/mock/data/afterSales/workOrder.ts`
- [ ] 类型定义创建在 `src/types/afterSales/workOrder.d.ts`
- [ ] 子组件创建在 `src/views/afterSales/workOrder/components/`

### 5.2 代码质量验证
- [ ] 移除页面中的内联数据和复杂逻辑
- [ ] 使用统一的API调用替换分散的数据获取逻辑
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持完整的业务流程

### 5.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 工单创建、编辑、删除功能正常工作
- [ ] 状态变更功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

### 5.4 路由验证
- [ ] 路由路径更新为新的模块化路径
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 6. 实施建议

### 6.1 实施顺序
1. **创建基础结构**：先创建目录和类型定义
2. **重构数据层**：创建Mock数据和API模块
3. **拆分组件**：创建子组件并测试
4. **重构主页面**：更新主页面逻辑
5. **更新配置**：修改路由和国际化
6. **测试验证**：全面测试功能正常性

### 6.2 风险控制
- **渐进式重构**：一次只重构一个模块，避免大范围影响
- **保持功能不变**：重构过程中不改变业务逻辑和用户体验
- **及时测试**：每个步骤完成后及时验证功能正常
- **代码备份**：重构前备份原始代码

---

**本技术方案基于页面目录结构规范和页面重构技术规范制定，为工单管理页面的重构提供详细的实施指导。**
