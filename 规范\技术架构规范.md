## 技术架构说明 (供 AI 使用)

这份技术架构说明旨在为 AI 提供关于 DMS 前端项目的技术蓝图。AI 应基于此文档理解项目的技术选型、各模块的职责、数据如何流动以及关键技术的应用方式，确保生成代码符合整体架构设计。

### 1. 整体架构概述

DMS 前端项目将采用基于 **Vue 3** 的现代化前端架构。核心思想是**模块化**、**组件化**、**数据驱动**和**类型安全**。

* **框架：** Vue 3 (Composition API)
* **构建工具：** Vite
* **状态管理：** Pinia
* **路由管理：** Vue Router
* **数据请求：** Axios (封装)
* **UI 组件库：** Element Plus
* **CSS 预处理器：** Sass (SCSS 语法)
* **代码规范：** ESLint + Prettier
* **类型检查：** TypeScript

项目将按照清晰的模块边界进行划分，确保代码的高内聚、低耦合，易于扩展和维护。

### 2. 项目目录结构

以下是 DMS 前端项目推荐的目录结构。AI 在生成新文件或定位现有文件时应严格遵循此结构。

```
dms-frontend/
├── node_modules/           # Node.js 模块依赖
├── public/                 # 静态资源，不会被 Webpack 处理，直接复制到 dist 目录
│   └── index.html          # 应用入口 HTML 文件
├── src/                    # 项目源码目录
│   ├── assets/             # 静态资源，会被 Webpack 处理
│   │   ├── images/         # 图片文件
│   │   ├── fonts/          # 字体文件
│   │   └── styles/         # 全局样式文件
│   │       ├── _variables.scss # Sass 全局变量定义
│   │       ├── _mixins.scss    # Sass 全局混合宏定义
│   │       ├── index.scss      # 全局样式入口，导入其他样式
│   │       └── reset.scss      # CSS Reset 或 normalize 样式
│   ├── api/                # API 请求模块
│   │   ├── index.ts        # Axios 实例的创建、配置、拦截器 (统一处理)
│   │   └── modules/        # 各业务模块的 API 接口定义 （核心业务代码存放点）
│   │       ├── auth.ts     # 认证相关 API (登录、登出、用户信息)
│   │       ├── sales.ts    # 销售模块 API (车辆、订单、合同等)
│   │       ├── customer.ts # 客户模块 API
│   │       └── system.ts   # 系统配置、用户管理等通用 API
│   ├── components/         # 可复用的通用 UI 组件 (不含业务逻辑)
│   │   ├── BaseProTable.vue    # 封装的带搜索和分页的通用表格
│   │   ├── MyButton.vue        # 自定义按钮或其他基础 UI 元素
│   │   ├── DialogForm.vue      # 封装的通用弹窗表单组件
│   │   └── CommonHeader.vue    # 通用头部组件
│   ├── directives/         # 自定义 Vue 指令
│   │   ├── permission/     # 权限指令 v-permission
│   │   │   └── index.ts
│   │   └── index.ts        # 统一注册所有指令
│   ├── layouts/            # 页面布局组件
│   │   └── DefaultLayout.vue   # 默认布局 (侧边栏、顶部导航、内容区)
│   ├── locales/            # 国际化语言包 (模块化组织)
│   │   ├── modules/        # 模块化国际化文件
│   │   │   ├── common/     # 通用模块 (按钮、操作等)
│   │   │   │   ├── zh.json
│   │   │   │   └── en.json
│   │   │   ├── sales/      # 销售管理模块
│   │   │   │   ├── zh.json
│   │   │   │   └── en.json
│   │   │   ├── aftersales/ # 售后服务模块
│   │   │   │   ├── zh.json
│   │   │   │   └── en.json
│   │   │   ├── parts/      # 零件管理模块
│   │   │   │   ├── zh.json
│   │   │   │   └── en.json
│   │   │   └── base/       # 基础系统模块
│   │   │       ├── zh.json
│   │   │       └── en.json
│   │   ├── loader.ts       # 模块加载器
│   │   └── backup/         # 原文件备份
│   ├── plugins/            # 第三方库或工具的初始化和配置
│   │   ├── i18n.ts         # Vue I18n 国际化配置
│   │   └── element-plus.ts # Element Plus 按需引入等配置 (可选，也可直接在 main.ts)
│   ├── router/             # Vue Router 配置
│   │   ├── index.ts        # 路由实例创建、全局导航守卫 (权限守卫)
│   │   └── routes.ts       # 路由定义 (包含 meta 信息)
│   ├── stores/             # Pinia 状态管理模块
│   │   ├── index.ts        # Pinia 实例创建和插件注册 (如持久化)
│   │   └── modules/        # 各业务模块的 Pinia Store （核心业务代码存放点）
│   │       ├── user.ts     # 用户认证、权限、用户信息 (核心)
│   │       ├── sales.ts    # 销售模块相关状态 (车辆列表筛选、订单状态)
│   │       ├── customer.ts # 客户模块相关状态
│   │       └── settings.ts # 全局应用设置 (主题、语言等)
│   ├── types/              # 全局 TypeScript 类型定义
│   │   ├── api.d.ts        # API 响应的通用类型
│   │   ├── module.d.ts     # 业务模块相关的通用接口定义
│   │   └── global.d.ts     # 全局类型声明 (如扩展 Window 对象)
│   ├── utils/              # 通用工具函数
│   │   ├── auth.ts         # 认证相关工具 (如 token 操作)
│   │   ├── datetime.ts     # 日期时间处理工具
│   │   ├── helpers.ts      # 其他通用辅助函数
│   │   └── storage.ts      # 本地存储操作封装
│   ├── views/              # 页面级组件 (对应路由，包含特定业务逻辑) （核心业务代码存放点）
│   │   ├── auth/           # 认证相关页面
│   │   │   └── Login.vue   # 登录页面
│   │   ├── dashboard/      # 仪表盘页面
│   │   │   └── Dashboard.vue
│   │   ├── sales/          # 销售管理模块页面
│   │   │   ├── SalesManagement.vue # 销售模块父级路由组件
│   │   │   ├── VehicleList.vue     # 车辆列表页面
│   │   │   ├── VehicleDetail.vue   # 车辆详情页面
│   │   │   ├── OrderList.vue       # 订单列表页面
│   │   │   └── components/         # 销售模块内部的局部组件
│   │   │       ├── VehicleForm.vue
│   │   │       └── OrderItemCard.vue
│   │   ├── customers/      # 客户管理模块页面
│   │   │   ├── CustomerList.vue
│   │   │   └── CustomerDetail.vue
│   │   ├── system/         # 系统管理模块页面 (用户、角色、权限等)
│   │   │   ├── UserList.vue
│   │   │   └── RoleList.vue
│   │   └── error/          # 错误页面
│   │       ├── 403.vue     # 无权限页面
│   │       └── 404.vue     # 页面不存在
│   ├── App.vue             # 根组件
│   └── main.ts             # 应用入口文件
├── .env.development        # 开发环境环境变量
├── .env.production         # 生产环境环境变量
├── .eslintrc.cjs           # ESLint 配置
├── .prettierrc.cjs         # Prettier 配置
├── tsconfig.json           # TypeScript 配置
├── vite.config.ts          # Vite 配置
├── package.json            # 项目依赖和脚本
└── README.md               # 项目说明文档
```

### 3. 模块划分与职责

项目代码将根据功能领域划分为独立的模块，体现在 `src/views`、`src/stores/modules` 和 `src/api/modules` 目录下。

* **核心模块 (`src/`)**：
    * `main.ts`：应用入口，负责 Vue 应用的初始化、插件注册（Pinia, Vue Router, Element Plus, i18n, 自定义指令）。
    * `App.vue`：根组件，通常只包含 `<router-view>`。
    * `router/`：
        * `index.ts`：Vue Router 实例的创建、全局导航守卫配置（包括权限控制）。
        * `routes.ts`：所有路由的定义，包含 `meta` 字段用于权限、菜单显示和页面标题。
    * `stores/`：
        * `index.ts`：Pinia 实例的创建和插件注册（如 `pinia-plugin-persistedstate`）。
        * `modules/`：**（核心业务代码存放点）**
            * `user.ts`：**用户状态管理核心**。负责用户登录状态、Token、用户信息（包括角色 `roles` 和权限 `permissions`）、登录/登出操作、用户信息获取。**所有权限判断均通过 `userStore` 提供的方法进行**。
            * `[business_module].ts`：**（核心业务代码存放点）** 各业务模块的 Pinia Store，管理该模块特有的共享状态（如列表筛选条件、全局统计数据）。例如 `sales.ts` 管理销售模块的全局状态。
    * `api/`：
        * `index.ts`：**Axios 封装核心**。负责 Axios 实例的创建、统一的请求/响应拦截器配置（包括 Token 添加、全局 Loading、统一错误提示、认证过期处理）。
        * `modules/`：**（核心业务代码存放点）**
            * `[business_module].ts`：定义各业务模块的具体 API 请求函数，使用 TypeScript 明确参数和返回值的类型。例如 `sales.ts` 包含所有销售相关的 API 请求函数（`getVehicleList`, `addOrder` 等）。
    * `layouts/`：页面布局组件，如 `DefaultLayout.vue` 包含 Header、Sidebar、Content。
    * `components/`：可复用的通用 UI 组件，不包含业务逻辑，如 `BaseProTable.vue`、`MyButton.vue`。
    * `directives/`：自定义指令，如 `v-permission`。
    * `locales/`：国际化语言包。
    * `plugins/`：第三方插件配置，如 `i18n.ts`。
    * `types/`：全局 TypeScript 类型定义文件。
    * `utils/`：通用工具函数库（日期格式化、数据验证、常量等）。
    * `assets/`：静态资源（图片、字体、全局样式 `scss` 变量和 mixin）。
    * `views/`：**（核心业务代码存放点）** 页面级组件，通常对应一个路由，集成业务逻辑和通用组件。**此目录下按业务模块（`sales/`、`customers/` 等）组织，每个业务模块下包含其特有的页面组件和局部组件（如 `components/` 目录）**。
        * `sales/` 目录下将包含 `VehicleList.vue` (车辆列表页面), `OrderList.vue` (订单列表页面) 等。
        * `sales/components/` 将包含 `VehicleForm.vue` (车辆表单，可能用于新增/编辑弹窗), `OrderItemCard.vue` 等仅在销售模块内部使用的组件。

### 4. 数据流与交互模式

* **单向数据流（Vue Props & Emits）：**
    * 父组件通过 `props` 向子组件传递数据。
    * 子组件通过 `emit` 向父组件发送事件。
    * **禁止** 子组件直接修改 `props`。
* **状态管理（Pinia）：**
    * 组件通过 `use[StoreName]()` 获取 Store 实例。
    * 组件通过 Store 的 `state` 或 `getters` 读取共享数据。
    * 组件通过 Store 的 `actions` 触发异步操作或复杂的状态修改。
    * **业务数据请求** 统一封装在 Pinia 的 `actions` 或直接在 `api` 模块中，由组件调用。
* **路由导航：**
    * 通过 `router.push()`, `router.replace()`, `router.go()`, `router.back()` 进行页面跳转。
    * **路由参数 (`route.params`, `route.query`)** 用于页面间传递简单数据。
* **用户反馈：**
    * `ElMessage` 用于操作成功/失败/警告的简短提示。
    * `ElNotification` 用于更重要的通知。
    * `ElMessageBox.confirm` 用于二次确认操作。
    * `v-loading` 用于异步操作的加载状态显示。

### 5. 权限管理机制

* **前端权限控制** 完全依赖于 `userStore` 中存储的 `roles` 和 `permissions`。
* **路由权限：** Vue Router 全局前置守卫 (`router.beforeEach`) 负责：
    1.  检查用户登录状态（`userStore.isLoggedIn`）。
    2.  如果未登录，重定向到登录页。
    3.  如果已登录但权限信息未加载，调用 `userStore.fetchUserInfo()` 获取。
    4.  根据路由 `meta` 字段（`roles`, `permission`, `permissions`）判断用户是否有访问权限，无权限则跳转到 `403` 页面。
* **菜单权限：** 侧边栏组件 (`Sidebar.vue`) 根据路由的 `meta` 字段和 `userStore` 的权限判断，动态渲染或隐藏菜单项。
* **功能权限：**
    * 优先使用自定义指令 `v-permission` 来控制按钮、表单字段等元素的可见性。
    * 对于复杂逻辑或动态内容，可以在组件内部使用 `v-if` 或 `v-show` 结合 `userStore.checkPermission()` 或 `userStore.checkRole()` 进行判断。

### 6. 国际化 (i18n) 机制

* **模块化国际化架构：** 项目采用模块化国际化架构，将国际化文件按业务模块进行组织，解决多人协作时的文件冲突问题。
* **五个核心模块：** 所有国际化文件按照以下五个模块进行组织：
    * **common**: 通用文本（按钮、操作、状态等）
    * **sales**: 销售管理相关文本
    * **aftersales**: 售后服务相关文本
    * **parts**: 零件管理相关文本
    * **base**: 基础系统功能文本（登录、系统设置等）
* **组合式API使用：** 统一使用 `useModuleI18n(moduleName)` 获取对应模块的翻译函数，禁止直接使用传统的 `$t()` 方式。
* **目录结构：**
    ```
    src/locales/
    ├── modules/
    │   ├── common/           # 通用模块
    │   │   ├── zh.json
    │   │   └── en.json
    │   ├── sales/            # 销售模块
    │   │   ├── zh.json
    │   │   └── en.json
    │   ├── aftersales/       # 售后服务模块
    │   │   ├── zh.json
    │   │   └── en.json
    │   ├── parts/            # 零件管理模块
    │   │   ├── zh.json
    │   │   └── en.json
    │   └── base/             # 基础系统模块
    │       ├── zh.json
    │       └── en.json
    ├── loader.ts             # 模块加载器
    └── backup/               # 原文件备份
    ```
* **使用方式：**
    * 单模块使用：`const { t, tc } = useModuleI18n('sales')`
    * 多模块使用：`const { sales, parts, tc } = useMultiModuleI18n(['sales', 'parts'])`
    * `t()` 用于访问当前模块翻译，`tc()` 用于访问通用模块翻译
* **Element Plus 组件：** 内置文本通过 Vue I18n 进行集成，统一使用模块化方式管理。
* **格式化处理：** 日期、数字、货币格式化使用 JavaScript `Intl` API 配合当前语言环境。

### 7. 技术规范集成

* **TypeScript：** 所有代码必须使用 TypeScript，强制类型安全。AI 在生成代码时应尽可能使用最明确的类型定义。
* **ESLint & Prettier：** 代码必须符合 `.eslintrc.js` 和 `.prettierrc.js` 中定义的规范。AI 生成代码时应考虑自动格式化和 Lint 规则。
* **Sass (SCSS)：** 样式应使用 SCSS 编写，利用变量、mixin 提高可维护性。样式应尽量使用 `scoped` 避免全局污染。
