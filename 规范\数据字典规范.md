# 数据字典通用迁移规范文档

## 1. 数据字典使用模式分析

### 1.1 核心使用场景
基于代码分析，数据字典在前端主要应用于以下场景：

1. **表格字段展示**：将字典编码转义为可读文本
2. **搜索表单下拉选择**：提供可选项列表
3. **标签样式映射**：根据字典值展示不同颜色标签
4. **表单验证**：确保输入值在字典范围内

### 1.2 标准化API

#### 1.2.1 组合式函数API
```typescript
// 单个字典使用
const { 
  options,           // 字典选项列表
  getNameByCode,     // 根据code获取name
  getCodeByName,     // 根据name获取code
  loading            // 加载状态
} = useDictionary(DICTIONARY_TYPES.ORDER_STATUS);

// 批量字典使用
const { 
  getOptions,        // 获取指定类型的选项
  getNameByCode,     // 根据类型和code获取name
  loading            // 加载状态
} = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.BUYER_TYPE
]);
```

#### 1.2.2 数据格式规范
```typescript
interface DictionaryOption {
  code: string;  // 字典编码（后端存储值）
  name: string;  // 字典名称（前端展示值）
}

// 后端返回格式
interface DictionaryItem {
  code: string;
  name: string;  // 已国际化，前端直接使用
}
```

## 2. 数据字典国际化规则

### 2.1 后端国际化原则
- **前端零配置**：所有字典名称由后端直接返回国际化文本
- **统一编码**：使用标准字典类型编码（如 `DICTIONARY_TYPES.ORDER_STATUS`）
- **动态获取**：通过API实时获取，支持语言切换

### 2.2 字典分类标准
```typescript
// 主数据字典（Master Data）- 业务实体相关
DICTIONARY_CATEGORIES.MASTER_DATA = [
  DICTIONARY_TYPES.VEHICLE_MODEL,     // 车型
  DICTIONARY_TYPES.DEALER,            // 经销商
  DICTIONARY_TYPES.STORE,             // 门店
  DICTIONARY_TYPES.VEHICLE_COLOR,     // 车辆颜色
  DICTIONARY_TYPES.SALES_CONSULTANT   // 销售顾问
];

// 业务状态字典（Business Status）- 状态流转相关
DICTIONARY_CATEGORIES.BUSINESS_STATUS = [
  DICTIONARY_TYPES.ORDER_STATUS,      // 订单状态
  DICTIONARY_TYPES.APPROVAL_STATUS,   // 审批状态
  DICTIONARY_TYPES.PAYMENT_STATUS,    // 支付状态
  DICTIONARY_TYPES.INSURANCE_STATUS   // 保险状态
];
```

### 2.3 国际化处理流程
```
用户切换语言 → 前端重新获取字典 → 后端返回对应语言文本 → 前端直接展示
```

## 3. 前端字段转义规则

### 3.1 转义函数标准化

#### 3.1.1 基础转义函数
```typescript
// 标准转义函数 - 适用于表格字段展示
const formatDictionaryValue = (
  value: string | undefined,
  dictionaryType: DictionaryType,
  getNameByCode: (type: DictionaryType, code: string) => string
): string => {
  if (!value) return '-';
  return getNameByCode(dictionaryType, value) || value;
};

// 带默认值转义函数
const formatDictionaryValueWithDefault = (
  value: string | undefined,
  dictionaryType: DictionaryType,
  getNameByCode: (type: DictionaryType, code: string) => string,
  defaultText: string = '-'
): string => {
  if (!value) return defaultText;
  return getNameByCode(dictionaryType, value) || value;
};
```

#### 3.1.2 标签样式映射函数
```typescript
// 状态标签类型映射
const getStatusTagType = (
  status: string,
  typeMap: Record<string, string>
): string => {
  return typeMap[status] || 'info';
};

// 通用状态映射配置
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.ORDER_STATUS]: {
    'submitted': 'info',
    'confirmed': 'success',
    'pending_delivery': 'warning',
    'completed': 'success',
    'canceled': 'danger'
  },
  [DICTIONARY_TYPES.PAYMENT_STATUS]: {
    'fully_paid': 'success',
    'pending_deposit': 'warning',
    'refund_completed': 'danger'
  }
};
```

### 3.2 表格字段转义规范

#### 3.2.1 标准表格列实现
```vue
<!-- 基础文本展示 -->
<el-table-column :label="t('orders.orderStatus')" prop="orderStatus" min-width="120">
  <template #default="{ row }">
    {{ getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
  </template>
</el-table-column>

<!-- 带标签的展示 -->
<el-table-column :label="t('orders.orderStatus')" prop="orderStatus" min-width="120">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.orderStatus, STATUS_TYPE_MAPS[DICTIONARY_TYPES.ORDER_STATUS])">
      {{ getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
    </el-tag>
  </template>
</el-table-column>
```

#### 3.2.2 搜索表单下拉实现
```vue
<!-- 下拉选择框 -->
<el-form-item :label="t('orders.orderStatus')">
  <el-select
    v-model="searchParams.orderStatus"
    :placeholder="tc('all')"
    clearable
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in getOptions(DICTIONARY_TYPES.ORDER_STATUS)"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>
```

## 4. 迁移通用规范

### 4.1 迁移检查清单

#### 4.1.1 代码迁移检查
- [ ] 替换硬编码字典值为 `DICTIONARY_TYPES` 常量
- [ ] 使用 `useDictionary` 或 `useBatchDictionary` 获取字典数据
- [ ] 统一使用 `getNameByCode` 进行字段转义
- [ ] 统一使用 `getOptions` 获取下拉选项
- [ ] 移除前端字典国际化配置（由后端处理）

#### 4.1.2 样式映射检查
- [ ] 定义标准的状态标签样式映射
- [ ] 统一标签颜色使用规范
- [ ] 确保空值处理一致性（使用'-'或默认值）

### 4.2 通用迁移模板

#### 4.2.1 组件级模板
```typescript
// 1. 引入必要的组合式函数和常量
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 2. 定义样式映射（如需要）
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.ORDER_STATUS]: {
    'submitted': 'info',
    'confirmed': 'success',
    'pending_delivery': 'warning',
    'completed': 'success',
    'canceled': 'danger'
  }
};

// 3. 获取字典数据
const { getOptions, getNameByCode, loading } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.BUYER_TYPE
]);

// 4. 定义格式化函数
const formatOrderStatus = (status: string) => 
  getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;

const getOrderStatusType = (status: string) => 
  STATUS_TYPE_MAPS[DICTIONARY_TYPES.ORDER_STATUS][status] || 'info';
```

#### 4.2.2 表格列模板
```typescript
// 标准表格列定义
const columns = [
  {
    prop: 'orderStatus',
    label: '订单状态',
    formatter: (row: any) => getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus),
    tagType: (row: any) => getStatusTagType(row.orderStatus, STATUS_TYPE_MAPS[DICTIONARY_TYPES.ORDER_STATUS])
  }
];
```

### 4.3 错误处理规范

#### 4.3.1 空值处理
```typescript
// 标准空值处理
const safeGetName = (code: string | null | undefined, type: DictionaryType) => {
  if (!code) return '-';
  return getNameByCode(type, code) || code;
};

// 带默认值处理
const safeGetNameWithDefault = (code: string | null | undefined, type: DictionaryType, defaultText = '-') => {
  if (!code) return defaultText;
  return getNameByCode(type, code) || code;
};
```

#### 4.3.2 错误回退
```typescript
// 错误回退策略
const getDictionaryDisplay = (
  value: string,
  type: DictionaryType,
  getNameByCode: (type: DictionaryType, code: string) => string
): string => {
  try {
    const displayValue = getNameByCode(type, value);
    return displayValue || value; // 回退到原始值
  } catch (error) {
    console.warn(`字典转义失败: ${type} - ${value}`, error);
    return value; // 严重错误时回退到原始值
  }
};
```

## 5. 最佳实践建议

### 5.1 性能优化
- **批量获取**：优先使用 `useBatchDictionary` 减少API调用
- **缓存策略**：字典数据已内置缓存，避免重复请求
- **按需加载**：只在需要字典的组件中引入对应字典类型

### 5.2 代码组织
- **集中管理**：所有字典类型统一在 `DICTIONARY_TYPES` 中定义
- **样式统一**：状态标签样式映射统一配置
- **命名规范**：转义函数命名遵循 `format[Type]Value` 或 `get[Type]Display` 模式

### 5.3 测试建议
- **空值测试**：验证null、undefined、空字符串的处理
- **无效值测试**：验证不存在字典值的处理
- **语言切换测试**：验证国际化切换后的字典展示

## 6. 迁移工具脚本

### 6.1 自动检测脚本
```typescript
// 检测硬编码字典值的正则表达式
const DICTIONARY_REGEX = /['"](submitted|confirmed|pending|completed|canceled)['"]/gi;

// 检测字典类型使用
const TYPE_CHECK_REGEX = /DICTIONARY_TYPES\.\w+/g;

// 检测格式化函数
const FORMATTER_REGEX = /getNameByCode|getOptions|format\w*Value/g;
```

### 6.2 迁移验证工具
```typescript
// 验证迁移完整性
const validateDictionaryMigration = (componentPath: string) => {
  const checks = [
    '是否使用 DICTIONARY_TYPES 常量',
    '是否使用 useDictionary/useBatchDictionary',
    '是否统一使用 getNameByCode 转义',
    '是否移除前端字典国际化'
  ];
  return checks;
};
```

---

**使用说明**：
1. 严格按照此规范进行数据字典迁移
2. 所有字典国际化由后端处理，前端无需额外配置
3. 转义函数统一使用 `getNameByCode` 和 `getOptions`
4. 样式映射统一使用 `STATUS_TYPE_MAPS` 配置对象
5. 空值统一使用 `'-'` 作为默认值