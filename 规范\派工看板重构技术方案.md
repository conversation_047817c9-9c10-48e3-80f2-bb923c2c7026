# 派工看板重构技术方案

本方案旨在根据《页面目录结构规范》和《页面重构技术规范》对 `src/views/workAssignment/WorkAssignmentDashboard.vue` 进行重构，提升代码的模块化、可维护性和可读性。

## 1. 重构目标

- **遵循目录规范**：将页面和相关文件迁移到 `src/views/afterSales/workAssignment/` 目录下。
- **模块化拆分**：将类型定义、API 请求、Mock 数据和子组件进行分离。
- **提升代码质量**：移除内联类型和数据，使用统一的 API 和类型定义。
- **增强可维护性**：通过组件化和模块化，降低单个文件的复杂性。

## 2. 重构分析

`WorkAssignmentDashboard.vue` 当前存在以下问题：

- **文件位置不规范**：位于 `src/views/workAssignment/`，未按 `afterSales` 模块进行组织。
- **API 模块分散**：使用 `@/api/modules/workAssignment` 而非模块化的 `afterSales/workAssignment`。
- **类型定义位置不当**：类型定义在 `@/types/module` 中，应迁移到 `afterSales` 目录下。
- **组件复杂度高**：单文件 1397 行，包含多个图表和复杂逻辑，应拆分为子组件。
- **缺少独立Mock数据**：没有专门的Mock数据模块，不便于测试和开发。
- **图表逻辑复杂**：ECharts 图表初始化和更新逻辑混杂在主组件中。

## 3. 重构步骤

### 步骤 1：创建目录和文件结构

根据规范，创建以下目录和文件：

```
src/
├── views/afterSales/workAssignment/
│   ├── WorkAssignmentDashboardView.vue      # 主页面（原 WorkAssignmentDashboard.vue）
│   └── components/
│       ├── DashboardHeader.vue              # 看板头部统计组件
│       ├── StatusStatisticsCard.vue        # 状态统计卡片组件
│       ├── WorkloadChart.vue                # 工作负荷图表组件
│       ├── StatusDistributionChart.vue     # 状态分布图表组件
│       ├── TechnicianScheduleTable.vue     # 技师排班表格组件
│       └── RealTimeUpdater.vue             # 实时更新组件
├── api/modules/afterSales/
│   └── workAssignmentDashboard.ts           # 派工看板 API 模块
├── types/afterSales/
│   └── workAssignmentDashboard.d.ts         # 派工看板类型定义
├── mock/data/afterSales/
│   └── workAssignmentDashboard.ts           # 派工看板 Mock 数据
└── locales/modules/afterSales/
    ├── zh.json                              # 中文语言包（已存在，需更新）
    └── en.json                              # 英文语言包（已存在，需更新）
```

### 步骤 2：迁移和重构类型定义

将相关类型定义迁移到 `src/types/afterSales/workAssignmentDashboard.d.ts`，并进行优化：

```typescript
// src/types/afterSales/workAssignmentDashboard.d.ts

// 看板统计数据接口
export interface DashboardStatistics {
  totalOrders: number;
  pendingAssignment: number;
  assignedOrders: number;
  inProgressOrders: number;
  completedOrders: number;
  averageAssignmentTime: number; // 平均分配时间（分钟）
  technicianUtilization: number; // 技师利用率（百分比）
  todayCompletedOrders: number;
  todayNewOrders: number;
}

// 技师排班信息接口
export interface TechnicianSchedule {
  technicianInfo: {
    technicianId: string;
    technicianName: string;
    technicianCode: string;
    department: string;
    skillLevel: number;
    specialties: string[];
    avatar?: string;
  };
  workingHours: {
    start: string;
    end: string;
    breakStart?: string;
    breakEnd?: string;
  };
  currentStatus: 'available' | 'busy' | 'break' | 'offline';
  assignedOrders: AssignedOrderInfo[];
  workloadPercentage: number;
  availableCapacity: number; // 可用容量（分钟）
  totalCapacity: number; // 总容量（分钟）
  efficiency: number; // 工作效率（百分比）
}

// 分配的工单信息接口
export interface AssignedOrderInfo {
  workOrderId: string;
  workOrderNo: string;
  customerName: string;
  licensePlate: string;
  vehicleModel: string;
  workOrderType: 'maintenance' | 'repair' | 'inspection' | 'insurance';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimatedDuration: number; // 预计工时（分钟）
  scheduledStartTime: string;
  scheduledEndTime: string;
  actualStartTime?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'delayed';
  progress: number; // 进度百分比
}

// 工作负荷数据接口
export interface WorkloadData {
  technicianId: string;
  technicianName: string;
  currentWorkload: number; // 当前工作负荷（分钟）
  maxCapacity: number; // 最大容量（分钟）
  utilizationRate: number; // 利用率（百分比）
  assignedOrdersCount: number;
  averageOrderDuration: number; // 平均工单时长（分钟）
  efficiency: number; // 效率评分
}

// 状态分布数据接口
export interface StatusDistribution {
  status: string;
  count: number;
  percentage: number;
  color: string;
}

// 实时更新配置接口
export interface RealTimeConfig {
  enabled: boolean;
  interval: number; // 更新间隔（秒）
  lastUpdateTime: string;
  autoRefresh: boolean;
}

// 看板筛选参数接口
export interface DashboardFilters {
  date: string;
  department?: string;
  technicianId?: string;
  workOrderType?: string;
  priority?: string;
}

// 图表配置接口
export interface ChartConfig {
  theme: 'light' | 'dark';
  animation: boolean;
  responsive: boolean;
  height: number;
  width?: number;
}

// 看板设置接口
export interface DashboardSettings {
  layout: 'grid' | 'list';
  chartConfig: ChartConfig;
  realTimeConfig: RealTimeConfig;
  displayOptions: {
    showStatistics: boolean;
    showCharts: boolean;
    showSchedule: boolean;
    showRealTimeUpdates: boolean;
  };
  refreshInterval: number;
}

// 技师绩效数据接口
export interface TechnicianPerformance {
  technicianId: string;
  technicianName: string;
  completedOrdersToday: number;
  completedOrdersWeek: number;
  completedOrdersMonth: number;
  averageCompletionTime: number; // 平均完成时间（分钟）
  qualityScore: number; // 质量评分
  customerSatisfaction: number; // 客户满意度
  onTimeCompletionRate: number; // 按时完成率
}

// 工单流转数据接口
export interface OrderFlowData {
  hour: string;
  newOrders: number;
  assignedOrders: number;
  completedOrders: number;
  cancelledOrders: number;
}

// 部门工作负荷接口
export interface DepartmentWorkload {
  departmentId: string;
  departmentName: string;
  totalTechnicians: number;
  activeTechnicians: number;
  totalOrders: number;
  completedOrders: number;
  averageUtilization: number;
  efficiency: number;
}
```

### 步骤 3：重构 Mock 数据

创建 `src/mock/data/afterSales/workAssignmentDashboard.ts`，提供完整的Mock数据支持：

```typescript
// src/mock/data/afterSales/workAssignmentDashboard.ts

import type { 
  DashboardStatistics,
  TechnicianSchedule,
  AssignedOrderInfo,
  WorkloadData,
  StatusDistribution,
  TechnicianPerformance,
  OrderFlowData,
  DepartmentWorkload,
  DashboardFilters
} from '@/types/afterSales/workAssignmentDashboard.d.ts';

// 生成技师排班Mock数据
function generateMockTechnicianSchedules(): TechnicianSchedule[] {
  const technicianNames = ['陈师傅', '刘师傅', '王师傅', '张师傅', '李师傅', '赵师傅'];
  const departments = ['维修部', '保养部', '检测部'];
  const specialties = [
    ['发动机维修', '变速箱维修'],
    ['电气系统', '空调系统'],
    ['底盘维修', '刹车系统'],
    ['车身维修', '喷漆'],
    ['轮胎更换', '四轮定位'],
    ['电池检测', '充电系统']
  ];
  const statuses = ['available', 'busy', 'break', 'offline'] as const;

  return technicianNames.map((name, index) => {
    const assignedOrdersCount = Math.floor(Math.random() * 4) + 1;
    const assignedOrders: AssignedOrderInfo[] = [];
    
    for (let i = 0; i < assignedOrdersCount; i++) {
      const startTime = new Date();
      startTime.setHours(8 + i * 2, Math.floor(Math.random() * 60));
      const duration = Math.floor(Math.random() * 120) + 60; // 60-180分钟
      const endTime = new Date(startTime.getTime() + duration * 60000);
      
      assignedOrders.push({
        workOrderId: `WO${String(index * 10 + i + 1).padStart(8, '0')}`,
        workOrderNo: `WO${new Date().getFullYear()}${String(index * 10 + i + 1).padStart(6, '0')}`,
        customerName: `客户${index * 10 + i + 1}`,
        licensePlate: `京A${String(index * 10 + i + 1).padStart(5, '0')}`,
        vehicleModel: ['Model Y 2023', 'Model 3 2022', 'Model X 2024'][Math.floor(Math.random() * 3)],
        workOrderType: ['maintenance', 'repair', 'inspection', 'insurance'][Math.floor(Math.random() * 4)] as any,
        priority: ['low', 'normal', 'high', 'urgent'][Math.floor(Math.random() * 4)] as any,
        estimatedDuration: duration,
        scheduledStartTime: startTime.toISOString().slice(0, 16).replace('T', ' '),
        scheduledEndTime: endTime.toISOString().slice(0, 16).replace('T', ' '),
        actualStartTime: Math.random() > 0.5 ? startTime.toISOString().slice(0, 16).replace('T', ' ') : undefined,
        status: ['scheduled', 'in_progress', 'completed', 'delayed'][Math.floor(Math.random() * 4)] as any,
        progress: Math.floor(Math.random() * 100)
      });
    }

    const totalCapacity = 8 * 60; // 8小时工作制
    const workloadMinutes = assignedOrders.reduce((sum, order) => sum + order.estimatedDuration, 0);
    const workloadPercentage = Math.min((workloadMinutes / totalCapacity) * 100, 100);

    return {
      technicianInfo: {
        technicianId: `T${String(index + 1).padStart(3, '0')}`,
        technicianName: name,
        technicianCode: `TECH${String(index + 1).padStart(3, '0')}`,
        department: departments[Math.floor(Math.random() * departments.length)],
        skillLevel: Math.floor(Math.random() * 5) + 1,
        specialties: specialties[index],
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`
      },
      workingHours: {
        start: '08:00',
        end: '18:00',
        breakStart: '12:00',
        breakEnd: '13:00'
      },
      currentStatus: statuses[Math.floor(Math.random() * statuses.length)],
      assignedOrders,
      workloadPercentage,
      availableCapacity: Math.max(totalCapacity - workloadMinutes, 0),
      totalCapacity,
      efficiency: Math.floor(Math.random() * 30) + 70 // 70-100%
    };
  });
}

// 生成看板统计数据
function generateMockDashboardStatistics(): DashboardStatistics {
  const totalOrders = Math.floor(Math.random() * 100) + 50;
  const pendingAssignment = Math.floor(totalOrders * 0.15);
  const assignedOrders = Math.floor(totalOrders * 0.25);
  const inProgressOrders = Math.floor(totalOrders * 0.35);
  const completedOrders = totalOrders - pendingAssignment - assignedOrders - inProgressOrders;

  return {
    totalOrders,
    pendingAssignment,
    assignedOrders,
    inProgressOrders,
    completedOrders,
    averageAssignmentTime: Math.floor(Math.random() * 30) + 15, // 15-45分钟
    technicianUtilization: Math.floor(Math.random() * 40) + 60, // 60-100%
    todayCompletedOrders: Math.floor(Math.random() * 20) + 10,
    todayNewOrders: Math.floor(Math.random() * 25) + 15
  };
}

// 生成工作负荷数据
function generateMockWorkloadData(): WorkloadData[] {
  const technicianNames = ['陈师傅', '刘师傅', '王师傅', '张师傅', '李师傅', '赵师傅'];
  
  return technicianNames.map((name, index) => {
    const maxCapacity = 8 * 60; // 8小时
    const currentWorkload = Math.floor(Math.random() * maxCapacity * 0.8) + maxCapacity * 0.2;
    const utilizationRate = (currentWorkload / maxCapacity) * 100;
    
    return {
      technicianId: `T${String(index + 1).padStart(3, '0')}`,
      technicianName: name,
      currentWorkload,
      maxCapacity,
      utilizationRate,
      assignedOrdersCount: Math.floor(Math.random() * 5) + 1,
      averageOrderDuration: Math.floor(Math.random() * 60) + 90, // 90-150分钟
      efficiency: Math.floor(Math.random() * 30) + 70 // 70-100%
    };
  });
}

// 生成状态分布数据
function generateMockStatusDistribution(): StatusDistribution[] {
  const statuses = [
    { status: 'pending_assign', color: '#F56C6C' },
    { status: 'assigned', color: '#E6A23C' },
    { status: 'in_progress', color: '#409EFF' },
    { status: 'completed', color: '#67C23A' }
  ];

  const total = 100;
  let remaining = total;
  
  return statuses.map((item, index) => {
    const count = index === statuses.length - 1 
      ? remaining 
      : Math.floor(Math.random() * (remaining / (statuses.length - index))) + 1;
    remaining -= count;
    
    return {
      status: item.status,
      count,
      percentage: (count / total) * 100,
      color: item.color
    };
  });
}

const mockTechnicianSchedules = generateMockTechnicianSchedules();
const mockDashboardStatistics = generateMockDashboardStatistics();
const mockWorkloadData = generateMockWorkloadData();
const mockStatusDistribution = generateMockStatusDistribution();

export const getDashboardStatistics = (): Promise<DashboardStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockDashboardStatistics);
    }, 300);
  });
};

export const getTechnicianSchedules = (filters: DashboardFilters): Promise<TechnicianSchedule[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockTechnicianSchedules];
      
      if (filters.department) {
        filteredData = filteredData.filter(item => 
          item.technicianInfo.department === filters.department
        );
      }
      
      if (filters.technicianId) {
        filteredData = filteredData.filter(item => 
          item.technicianInfo.technicianId === filters.technicianId
        );
      }
      
      resolve(filteredData);
    }, 400);
  });
};

export const getWorkloadData = (): Promise<WorkloadData[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockWorkloadData);
    }, 300);
  });
};

export const getStatusDistribution = (): Promise<StatusDistribution[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockStatusDistribution);
    }, 300);
  });
};

export const getTechnicianPerformance = (): Promise<TechnicianPerformance[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const performanceData = mockTechnicianSchedules.map(schedule => ({
        technicianId: schedule.technicianInfo.technicianId,
        technicianName: schedule.technicianInfo.technicianName,
        completedOrdersToday: Math.floor(Math.random() * 8) + 2,
        completedOrdersWeek: Math.floor(Math.random() * 40) + 20,
        completedOrdersMonth: Math.floor(Math.random() * 160) + 80,
        averageCompletionTime: Math.floor(Math.random() * 60) + 90, // 90-150分钟
        qualityScore: Math.floor(Math.random() * 20) + 80, // 80-100分
        customerSatisfaction: Math.floor(Math.random() * 15) + 85, // 85-100%
        onTimeCompletionRate: Math.floor(Math.random() * 20) + 80 // 80-100%
      }));
      
      resolve(performanceData);
    }, 400);
  });
};

export const getOrderFlowData = (): Promise<OrderFlowData[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const flowData: OrderFlowData[] = [];
      
      for (let hour = 8; hour <= 18; hour++) {
        flowData.push({
          hour: `${hour}:00`,
          newOrders: Math.floor(Math.random() * 8) + 2,
          assignedOrders: Math.floor(Math.random() * 6) + 1,
          completedOrders: Math.floor(Math.random() * 5) + 1,
          cancelledOrders: Math.floor(Math.random() * 2)
        });
      }
      
      resolve(flowData);
    }, 300);
  });
};

export const getDepartmentWorkload = (): Promise<DepartmentWorkload[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const departments = ['维修部', '保养部', '检测部'];
      const workloadData = departments.map((dept, index) => ({
        departmentId: `DEPT${String(index + 1).padStart(3, '0')}`,
        departmentName: dept,
        totalTechnicians: Math.floor(Math.random() * 8) + 5,
        activeTechnicians: Math.floor(Math.random() * 6) + 3,
        totalOrders: Math.floor(Math.random() * 30) + 20,
        completedOrders: Math.floor(Math.random() * 20) + 10,
        averageUtilization: Math.floor(Math.random() * 30) + 70, // 70-100%
        efficiency: Math.floor(Math.random() * 25) + 75 // 75-100%
      }));
      
      resolve(workloadData);
    }, 300);
  });
};
```

### 步骤 4：创建 API 模块

创建 `src/api/modules/afterSales/workAssignmentDashboard.ts`，统一管理 API 请求：

```typescript
// src/api/modules/afterSales/workAssignmentDashboard.ts

import request from '@/api';
import type {
  DashboardStatistics,
  TechnicianSchedule,
  WorkloadData,
  StatusDistribution,
  TechnicianPerformance,
  OrderFlowData,
  DepartmentWorkload,
  DashboardFilters
} from '@/types/afterSales/workAssignmentDashboard.d.ts';
import {
  getDashboardStatistics as getMockDashboardStatistics,
  getTechnicianSchedules as getMockTechnicianSchedules,
  getWorkloadData as getMockWorkloadData,
  getStatusDistribution as getMockStatusDistribution,
  getTechnicianPerformance as getMockTechnicianPerformance,
  getOrderFlowData as getMockOrderFlowData,
  getDepartmentWorkload as getMockDepartmentWorkload
} from '@/mock/data/afterSales/workAssignmentDashboard';

let USE_MOCK_API_TEMP = true;

export const getDashboardStatistics = (): Promise<DashboardStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockDashboardStatistics();
  }
  return request.get<any, DashboardStatistics>('/after-sales/work-assignment/dashboard/statistics');
};

export const getTechnicianSchedules = (filters: DashboardFilters): Promise<TechnicianSchedule[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianSchedules(filters);
  }
  return request.get<any, TechnicianSchedule[]>('/after-sales/work-assignment/dashboard/schedules', { params: filters });
};

export const getWorkloadData = (): Promise<WorkloadData[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkloadData();
  }
  return request.get<any, WorkloadData[]>('/after-sales/work-assignment/dashboard/workload');
};

export const getStatusDistribution = (): Promise<StatusDistribution[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockStatusDistribution();
  }
  return request.get<any, StatusDistribution[]>('/after-sales/work-assignment/dashboard/status-distribution');
};

export const getTechnicianPerformance = (): Promise<TechnicianPerformance[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianPerformance();
  }
  return request.get<any, TechnicianPerformance[]>('/after-sales/work-assignment/dashboard/performance');
};

export const getOrderFlowData = (): Promise<OrderFlowData[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockOrderFlowData();
  }
  return request.get<any, OrderFlowData[]>('/after-sales/work-assignment/dashboard/order-flow');
};

export const getDepartmentWorkload = (): Promise<DepartmentWorkload[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockDepartmentWorkload();
  }
  return request.get<any, DepartmentWorkload[]>('/after-sales/work-assignment/dashboard/department-workload');
};
```

### 步骤 5：更新国际化文件

在现有的 `src/locales/modules/afterSales/zh.json` 和 `en.json` 中，将派工看板相关的翻译组织在 `workAssignmentDashboard` 键下：

```json
// src/locales/modules/afterSales/zh.json (新增部分)
{
  "workAssignmentDashboard": {
    "title": "派工看板",
    "statistics": {
      "totalOrders": "总工单数",
      "pendingAssignment": "待分配",
      "assignedOrders": "已分配",
      "inProgressOrders": "进行中",
      "completedOrders": "已完成",
      "averageAssignmentTime": "平均分配时间",
      "technicianUtilization": "技师利用率",
      "todayCompleted": "今日完成",
      "todayNew": "今日新增"
    },
    "charts": {
      "workloadTitle": "技师工作负荷",
      "statusDistributionTitle": "工单状态分布",
      "orderFlowTitle": "工单流转趋势",
      "performanceTitle": "技师绩效",
      "departmentWorkloadTitle": "部门工作负荷"
    },
    "schedule": {
      "title": "技师排班",
      "technicianName": "技师姓名",
      "department": "部门",
      "workingHours": "工作时间",
      "currentStatus": "当前状态",
      "assignedOrders": "分配工单",
      "workloadPercentage": "工作负荷",
      "efficiency": "工作效率",
      "availableCapacity": "可用容量"
    },
    "status": {
      "available": "空闲",
      "busy": "忙碌",
      "break": "休息",
      "offline": "离线",
      "scheduled": "已安排",
      "in_progress": "进行中",
      "completed": "已完成",
      "delayed": "延迟"
    },
    "workOrderType": {
      "maintenance": "保养",
      "repair": "维修",
      "inspection": "检查",
      "insurance": "保险"
    },
    "priority": {
      "low": "低",
      "normal": "普通",
      "high": "高",
      "urgent": "紧急"
    },
    "filters": {
      "date": "日期",
      "department": "部门",
      "technician": "技师",
      "workOrderType": "工单类型",
      "priority": "优先级",
      "all": "全部"
    },
    "realTime": {
      "title": "实时更新",
      "enabled": "启用实时更新",
      "interval": "更新间隔",
      "lastUpdate": "最后更新",
      "autoRefresh": "自动刷新",
      "refreshNow": "立即刷新"
    },
    "settings": {
      "title": "看板设置",
      "layout": "布局",
      "theme": "主题",
      "animation": "动画效果",
      "responsive": "响应式",
      "displayOptions": "显示选项",
      "showStatistics": "显示统计",
      "showCharts": "显示图表",
      "showSchedule": "显示排班",
      "showRealTimeUpdates": "显示实时更新"
    },
    "performance": {
      "completedToday": "今日完成",
      "completedWeek": "本周完成",
      "completedMonth": "本月完成",
      "averageTime": "平均用时",
      "qualityScore": "质量评分",
      "satisfaction": "客户满意度",
      "onTimeRate": "按时完成率"
    },
    "common": {
      "minutes": "分钟",
      "hours": "小时",
      "percentage": "%",
      "orders": "工单",
      "loading": "加载中...",
      "noData": "暂无数据",
      "refresh": "刷新",
      "export": "导出",
      "fullscreen": "全屏",
      "settings": "设置"
    }
  }
}
```

### 步骤 6：创建子组件

#### `DashboardHeader.vue` - 看板头部统计组件
```vue
<!-- src/views/afterSales/workAssignment/components/DashboardHeader.vue -->
<script setup lang="ts">
import { ElRow, ElCol, ElCard, ElStatistic, ElIcon } from 'element-plus';
import { Document, Clock, User, CheckCircle } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { DashboardStatistics } from '@/types/afterSales/workAssignmentDashboard.d.ts';

interface Props {
  statistics: DashboardStatistics;
  loading: boolean;
}

defineProps<Props>();

const { t } = useModuleI18n('afterSales.workAssignmentDashboard');

const statisticsConfig = [
  {
    key: 'totalOrders',
    title: 'statistics.totalOrders',
    icon: Document,
    color: '#409EFF'
  },
  {
    key: 'pendingAssignment',
    title: 'statistics.pendingAssignment',
    icon: Clock,
    color: '#E6A23C'
  },
  {
    key: 'inProgressOrders',
    title: 'statistics.inProgressOrders',
    icon: User,
    color: '#409EFF'
  },
  {
    key: 'completedOrders',
    title: 'statistics.completedOrders',
    icon: CheckCircle,
    color: '#67C23A'
  }
];
</script>

<template>
  <el-row :gutter="20" class="dashboard-header">
    <el-col :span="6" v-for="config in statisticsConfig" :key="config.key">
      <el-card class="statistic-card" :body-style="{ padding: '20px' }">
        <el-statistic
          :title="t(config.title)"
          :value="statistics[config.key as keyof DashboardStatistics]"
          :loading="loading"
        >
          <template #prefix>
            <el-icon :color="config.color" :size="24">
              <component :is="config.icon" />
            </el-icon>
          </template>
        </el-statistic>
      </el-card>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.dashboard-header {
  margin-bottom: 20px;

  .statistic-card {
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
```

#### `WorkloadChart.vue` - 工作负荷图表组件
```vue
<!-- src/views/afterSales/workAssignment/components/WorkloadChart.vue -->
<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import { ElCard, ElSkeleton } from 'element-plus';
import * as echarts from 'echarts';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkloadData } from '@/types/afterSales/workAssignmentDashboard.d.ts';

interface Props {
  data: WorkloadData[];
  loading: boolean;
  height?: number;
}

const props = withDefaults(defineProps<Props>(), {
  height: 400
});

const { t } = useModuleI18n('afterSales.workAssignmentDashboard');

const chartRef = ref<HTMLDivElement>();
let chartInstance: echarts.ECharts | null = null;

const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value);
  updateChart();
};

const updateChart = () => {
  if (!chartInstance || props.loading) return;

  const option = {
    title: {
      text: t('charts.workloadTitle'),
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>
                工作负荷: ${data.value}%<br/>
                工单数量: ${props.data[data.dataIndex]?.assignedOrdersCount || 0}`;
      }
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.technicianName),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '利用率(%)',
      max: 100
    },
    series: [
      {
        name: '工作负荷',
        type: 'bar',
        data: props.data.map(item => ({
          value: item.utilizationRate,
          itemStyle: {
            color: item.utilizationRate > 90 ? '#F56C6C' :
                   item.utilizationRate > 70 ? '#E6A23C' : '#67C23A'
          }
        })),
        barWidth: '60%'
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  };

  chartInstance.setOption(option);
};

watch(() => props.data, () => {
  nextTick(() => {
    updateChart();
  });
}, { deep: true });

watch(() => props.loading, (newVal) => {
  if (!newVal) {
    nextTick(() => {
      updateChart();
    });
  }
});

onMounted(() => {
  initChart();

  // 响应式处理
  window.addEventListener('resize', () => {
    chartInstance?.resize();
  });
});
</script>

<template>
  <el-card class="chart-card">
    <template #header>
      <span>{{ t('charts.workloadTitle') }}</span>
    </template>

    <el-skeleton :loading="loading" animated>
      <template #template>
        <div :style="{ height: `${height}px` }" class="skeleton-chart"></div>
      </template>

      <div
        ref="chartRef"
        :style="{ height: `${height}px` }"
        class="chart-container"
      ></div>
    </el-skeleton>
  </el-card>
</template>

<style scoped lang="scss">
.chart-card {
  margin-bottom: 20px;

  .chart-container {
    width: 100%;
  }

  .skeleton-chart {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
```

### 步骤 7：重构主页面 `WorkAssignmentDashboardView.vue`

- **移动和重命名**：将 `WorkAssignmentDashboard.vue` 移动到 `src/views/afterSales/workAssignment/` 并重命名为 `WorkAssignmentDashboardView.vue`。
- **移除内联逻辑**：将图表和统计逻辑移动到子组件。
- **更新数据获取**：使用新的 API 模块获取数据。
- **更新类型引用**：从 `@/types/afterSales/workAssignmentDashboard.d.ts` 导入类型。
- **更新国际化引用**：使用 `useModuleI18n('afterSales.workAssignmentDashboard')`。
- **引入子组件**：在模板中引入并使用子组件。

```vue
<!-- src/views/afterSales/workAssignment/WorkAssignmentDashboardView.vue -->
<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElRow, ElCol, ElCard, ElButton, ElDatePicker, ElSelect, ElOption } from 'element-plus';
import { Refresh, Setting, FullScreen } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getDashboardStatistics,
  getTechnicianSchedules,
  getWorkloadData,
  getStatusDistribution,
  getTechnicianPerformance,
  getOrderFlowData,
  getDepartmentWorkload
} from '@/api/modules/afterSales/workAssignmentDashboard';
import type {
  DashboardStatistics,
  TechnicianSchedule,
  WorkloadData,
  StatusDistribution,
  DashboardFilters,
  RealTimeConfig
} from '@/types/afterSales/workAssignmentDashboard.d.ts';

// 导入子组件
import DashboardHeader from './components/DashboardHeader.vue';
import StatusStatisticsCard from './components/StatusStatisticsCard.vue';
import WorkloadChart from './components/WorkloadChart.vue';
import StatusDistributionChart from './components/StatusDistributionChart.vue';
import TechnicianScheduleTable from './components/TechnicianScheduleTable.vue';
import RealTimeUpdater from './components/RealTimeUpdater.vue';

const { t, tc } = useModuleI18n('afterSales.workAssignmentDashboard');

// 数据状态
const statistics = ref<DashboardStatistics>({} as DashboardStatistics);
const technicianSchedules = ref<TechnicianSchedule[]>([]);
const workloadData = ref<WorkloadData[]>([]);
const statusDistribution = ref<StatusDistribution[]>([]);

// 加载状态
const loading = reactive({
  statistics: false,
  schedules: false,
  workload: false,
  status: false
});

// 筛选条件
const filters = reactive<DashboardFilters>({
  date: new Date().toISOString().slice(0, 10),
  department: '',
  technicianId: '',
  workOrderType: '',
  priority: ''
});

// 实时更新配置
const realTimeConfig = reactive<RealTimeConfig>({
  enabled: true,
  interval: 30,
  lastUpdateTime: '',
  autoRefresh: true
});

let refreshTimer: NodeJS.Timeout | null = null;

// 获取看板统计数据
const fetchStatistics = async () => {
  loading.statistics = true;
  try {
    statistics.value = await getDashboardStatistics();
  } catch (error) {
    console.error('Failed to fetch dashboard statistics:', error);
  } finally {
    loading.statistics = false;
  }
};

// 获取技师排班数据
const fetchTechnicianSchedules = async () => {
  loading.schedules = true;
  try {
    technicianSchedules.value = await getTechnicianSchedules(filters);
  } catch (error) {
    console.error('Failed to fetch technician schedules:', error);
  } finally {
    loading.schedules = false;
  }
};

// 获取工作负荷数据
const fetchWorkloadData = async () => {
  loading.workload = true;
  try {
    workloadData.value = await getWorkloadData();
  } catch (error) {
    console.error('Failed to fetch workload data:', error);
  } finally {
    loading.workload = false;
  }
};

// 获取状态分布数据
const fetchStatusDistribution = async () => {
  loading.status = true;
  try {
    statusDistribution.value = await getStatusDistribution();
  } catch (error) {
    console.error('Failed to fetch status distribution:', error);
  } finally {
    loading.status = false;
  }
};

// 刷新所有数据
const refreshAllData = async () => {
  await Promise.all([
    fetchStatistics(),
    fetchTechnicianSchedules(),
    fetchWorkloadData(),
    fetchStatusDistribution()
  ]);

  realTimeConfig.lastUpdateTime = new Date().toLocaleTimeString();
};

// 处理筛选变更
const handleFilterChange = () => {
  fetchTechnicianSchedules();
};

// 启动实时更新
const startRealTimeUpdate = () => {
  if (realTimeConfig.enabled && realTimeConfig.autoRefresh) {
    refreshTimer = setInterval(() => {
      refreshAllData();
    }, realTimeConfig.interval * 1000);
  }
};

// 停止实时更新
const stopRealTimeUpdate = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 处理实时更新配置变更
const handleRealTimeConfigChange = (config: RealTimeConfig) => {
  Object.assign(realTimeConfig, config);

  stopRealTimeUpdate();
  if (config.enabled && config.autoRefresh) {
    startRealTimeUpdate();
  }
};

// 组件挂载时获取数据
onMounted(() => {
  refreshAllData();
  startRealTimeUpdate();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopRealTimeUpdate();
});
</script>

<template>
  <div class="dashboard-container">
    <div class="dashboard-header-section">
      <h1 class="page-title">{{ t('title') }}</h1>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" :icon="Refresh" @click="refreshAllData">
          {{ t('common.refresh') }}
        </el-button>
        <el-button :icon="Setting">
          {{ t('common.settings') }}
        </el-button>
        <el-button :icon="FullScreen">
          {{ t('common.fullscreen') }}
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card mb-20">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-date-picker
            v-model="filters.date"
            type="date"
            :placeholder="t('filters.date')"
            value-format="YYYY-MM-DD"
            @change="handleFilterChange"
          />
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="filters.department"
            :placeholder="t('filters.department')"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="维修部" value="维修部" />
            <el-option label="保养部" value="保养部" />
            <el-option label="检测部" value="检测部" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="filters.workOrderType"
            :placeholder="t('filters.workOrderType')"
            clearable
            @change="handleFilterChange"
          >
            <el-option :label="t('workOrderType.maintenance')" value="maintenance" />
            <el-option :label="t('workOrderType.repair')" value="repair" />
            <el-option :label="t('workOrderType.inspection')" value="inspection" />
            <el-option :label="t('workOrderType.insurance')" value="insurance" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="filters.priority"
            :placeholder="t('filters.priority')"
            clearable
            @change="handleFilterChange"
          >
            <el-option :label="t('priority.low')" value="low" />
            <el-option :label="t('priority.normal')" value="normal" />
            <el-option :label="t('priority.high')" value="high" />
            <el-option :label="t('priority.urgent')" value="urgent" />
          </el-select>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计卡片 -->
    <DashboardHeader
      :statistics="statistics"
      :loading="loading.statistics"
    />

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <WorkloadChart
          :data="workloadData"
          :loading="loading.workload"
        />
      </el-col>
      <el-col :span="12">
        <StatusDistributionChart
          :data="statusDistribution"
          :loading="loading.status"
        />
      </el-col>
    </el-row>

    <!-- 技师排班表格 -->
    <TechnicianScheduleTable
      :schedules="technicianSchedules"
      :loading="loading.schedules"
    />

    <!-- 实时更新组件 -->
    <RealTimeUpdater
      :config="realTimeConfig"
      @config-change="handleRealTimeConfigChange"
      @refresh="refreshAllData"
    />
  </div>
</template>

<style scoped lang="scss">
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .page-title {
    margin: 0;
    color: #303133;
  }

  .action-buttons {
    .el-button {
      margin-left: 10px;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
}

.charts-section {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
```

### 步骤 8：更新路由配置

修改 `src/router/index.ts`，更新派工看板页面的路由配置：

```typescript
// src/router/index.ts
{
  path: '/after-sales/work-assignment-dashboard',
  name: 'WorkAssignmentDashboard',
  component: () => import('@/views/afterSales/workAssignment/WorkAssignmentDashboardView.vue'),
  meta: {
    title: 'menu.workAssignmentDashboard',
    requiresAuth: true,
    icon: 'DataBoard'
  }
}
```

## 4. 预期收益

- **结构清晰**：项目结构符合团队规范，新成员更容易上手。
- **职责明确**：每个文件和组件的职责更加单一，便于理解和修改。
- **易于维护**：修改或扩展功能时，只需关注相关模块，降低了代码耦合带来的风险。
- **提升开发效率**：模块化和组件化使得代码复用更加方便。
- **图表性能优化**：独立的图表组件便于性能优化和懒加载。
- **实时更新优化**：独立的实时更新逻辑，便于配置和管理。
- **类型安全**：完整的 TypeScript 类型定义，提高代码质量和开发体验。

## 5. 重构检查清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/workAssignment/WorkAssignmentDashboardView.vue`
- [ ] API模块创建在 `src/api/modules/afterSales/workAssignmentDashboard.ts`
- [ ] Mock数据创建在 `src/mock/data/afterSales/workAssignmentDashboard.ts`
- [ ] 类型定义创建在 `src/types/afterSales/workAssignmentDashboard.d.ts`
- [ ] 子组件创建在 `src/views/afterSales/workAssignment/components/`

### 5.2 代码质量验证
- [ ] 移除页面中的内联数据和复杂逻辑
- [ ] 使用统一的API调用替换分散的数据获取逻辑
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持完整的业务流程
- [ ] ECharts图表组件独立且可复用

### 5.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 筛选功能正常工作
- [ ] 图表渲染正常，响应式布局
- [ ] 实时更新功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

### 5.4 路由验证
- [ ] 路由路径更新为新的模块化路径
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 6. 实施建议

### 6.1 实施顺序
1. **创建基础结构**：先创建目录和类型定义
2. **重构数据层**：创建Mock数据和API模块
3. **拆分图表组件**：创建独立的图表子组件并测试
4. **重构主页面**：更新主页面逻辑
5. **更新配置**：修改路由和国际化
6. **测试验证**：全面测试功能正常性

### 6.2 风险控制
- **渐进式重构**：一次只重构一个模块，避免大范围影响
- **保持功能不变**：重构过程中不改变业务逻辑和用户体验
- **及时测试**：每个步骤完成后及时验证功能正常
- **代码备份**：重构前备份原始代码
- **图表性能监控**：重构后监控图表渲染性能

---

**本技术方案基于页面目录结构规范和页面重构技术规范制定，为派工看板页面的重构提供详细的实施指导。**
