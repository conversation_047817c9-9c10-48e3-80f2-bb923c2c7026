# VehicleQueryView 表格列表样式规范

## 1. 表格容器样式

### 1.1 表格卡片容器
- **组件**: `el-card`
- **类名**: `table-card`
- **下边距**: 20px
- **圆角**: 8px
- **阴影**: `0 2px 12px 0 rgba(0, 0, 0, 0.05)`

### 1.2 表格组件样式
- **组件**: `el-table`
- **宽度**: 100%
- **加载状态**: `v-loading="loading"`
- **文本换行**: 禁止 (`white-space: nowrap`)
- **隔行变色**: `stripe` 属性启用

## 2. 表格列样式规范

### 2.1 序号列样式
- **类型**: `type="index"`
- **宽度**: 60px
- **对齐**: 居中

### 2.2 普通数据列样式
- **最小宽度**: 根据内容设置 (100px-150px)
- **文本溢出**: 显示省略号 (`show-overflow-tooltip`)
- **对齐方式**: 左对齐 (默认)

### 2.3 状态标签列样式
- **最小宽度**: 120px
- **标签组件**: `el-tag`
- **标签类型**: 根据状态动态设置
- **标签颜色**:
  - 成功状态: `type="success"` (#67c23a)
  - 警告状态: `type="warning"` (#e6a23c)
  - 危险状态: `type="danger"` (#f56c6c)
  - 信息状态: `type="info"` (#909399)
  - 主要状态: `type="primary"` (#409eff)

### 2.4 操作列样式
- **宽度**: 100px (固定宽度)
- **固定位置**: `fixed="right"`
- **对齐方式**: 居中

## 3. 表格行样式规范

### 3.1 表格行背景色
- **奇数行背景**: #ffffff (白色)
- **偶数行背景**: #fafafa (浅灰色)
- **悬停行背景**: #f5f7fa (浅蓝灰色)
- **选中行背景**: #ecf5ff (浅蓝色)

### 3.2 表格边框样式
- **表格边框**: #ebeef5 (浅灰色)
- **行分隔线**: #ebeef5 (浅灰色)
- **列分隔线**: #ebeef5 (浅灰色)

### 3.3 表头样式
- **表头背景**: #fafafa (浅灰色)
- **表头文字**: #303133 (深灰色)
- **表头字体粗细**: 500 (medium)
- **表头边框**: #ebeef5 (浅灰色)

## 4. 操作按钮样式规范

### 4.1 图标按钮容器
- **类名**: `operation-buttons`
- **显示方式**: 水平排列
- **按钮间距**: 8px
- **对齐方式**: 居中

### 4.2 图标按钮样式
- **组件**: `el-button`
- **类型**: `link`
- **尺寸**: `size="small"`
- **样式**: 纯图标显示
- **宽度**: 32px
- **高度**: 32px
- **圆角**: 4px
- **背景**: 透明
- **边框**: 无

### 4.3 按钮图标规范
- **查看详情**: `View` 图标
- **编辑**: `Edit` 图标  
- **删除**: `Delete` 图标
- **下载**: `Download` 图标
- **图标大小**: 16px
- **图标颜色**: 继承按钮颜色

### 4.4 按钮悬停效果
- **背景色**: rgba(64, 158, 255, 0.1) (浅蓝色背景)
- **图标颜色**: #409eff (主题蓝色)
- **过渡效果**: `transition: all 0.3s ease`
- **工具提示**: 显示操作名称

### 4.5 按钮颜色规范
- **主要操作**: `type="primary"` (#409eff)
- **成功操作**: `type="success"` (#67c23a)
- **警告操作**: `type="warning"` (#e6a23c)
- **危险操作**: `type="danger"` (#f56c6c)

## 5. 工具提示样式规范

### 5.1 提示容器
- **组件**: `el-tooltip`
- **触发方式**: `trigger="hover"`
- **延迟**: `show-after="500"`
- **位置**: `placement="top"`

### 5.2 提示内容样式
- **背景色**: rgba(0, 0, 0, 0.8)
- **文字颜色**: #ffffff
- **字体大小**: 12px
- **内边距**: 8px 12px
- **圆角**: 4px
- **最大宽度**: 200px

## 6. 空状态样式规范

### 6.1 空状态容器
- **类名**: `empty-state`
- **显示条件**: `!vehicleList.length && !loading`
- **对齐方式**: 居中
- **内边距**: 50px 0

### 6.2 空状态图标
- **组件**: `el-icon`
- **图标**: `el-icon-inbox`
- **大小**: 60px
- **颜色**: #909399
- **下边距**: 20px

### 6.3 空状态文本
- **标题样式**:
  - 字体大小: 16px
  - 颜色: #909399
  - 下边距: 10px
- **描述样式**:
  - 字体大小: 14px
  - 颜色: #c0c4cc

## 7. CSS样式定义

### 7.1 表格基础样式
```scss
.table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  
  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;
    }
    
    // 表头样式
    .el-table__header-wrapper th {
      background-color: #fafafa;
      color: #303133;
      font-weight: 500;
      border-bottom: 1px solid #ebeef5;
    }
    
    // 表格行样式
    .el-table__body tr {
      &:nth-child(even) {
        background-color: #fafafa; // 偶数行背景色
      }
      
      &:nth-child(odd) {
        background-color: #ffffff; // 奇数行背景色
      }
      
      &:hover {
        background-color: #f5f7fa !important; // 悬停行背景色
      }
    }
    
    // 表格边框
    .el-table__border {
      border-color: #ebeef5;
    }
    
    // 单元格边框
    td, th {
      border-bottom: 1px solid #ebeef5;
      border-right: 1px solid #ebeef5;
    }
  }
}
```

### 7.2 操作按钮样式
```scss
.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  
  .icon-button {
    width: 32px;
    height: 32px;
    padding: 0;
    border: none;
    border-radius: 4px;
    background: transparent;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      
      .el-icon {
        color: #409eff;
      }
    }
    
    .el-icon {
      font-size: 16px;
      transition: color 0.3s ease;
    }
  }
  
  .icon-button--primary {
    .el-icon {
      color: #409eff;
    }
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
    }
  }
  
  .icon-button--danger {
    .el-icon {
      color: #f56c6c;
    }
    
    &:hover {
      background-color: rgba(245, 108, 108, 0.1);
    }
  }
}
```

### 7.3 空状态样式
```scss
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  color: #909399;
  
  .el-icon {
    font-size: 60px;
    margin-bottom: 20px;
  }
  
  h3 {
    margin: 0 0 10px 0;
    color: #909399;
    font-size: 16px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: #c0c4cc;
  }
}
```

## 8. 响应式设计

### 8.1 表格自适应
- **最小宽度**: 100%
- **水平滚动**: 内容超出时显示滚动条
- **列宽调整**: 根据内容自动调整

### 8.2 操作按钮适配
- **小屏幕**: 保持图标显示
- **触摸设备**: 增加按钮触摸区域
- **按钮间距**: 在小屏幕上适当增加

## 9. 视觉层次规范

### 9.1 表格层次
- **表头背景**: #fafafa (浅灰色)
- **奇数行背景**: #ffffff (白色)
- **偶数行背景**: #fafafa (浅灰色)
- **表格边框**: #ebeef5 (浅灰色)
- **悬停行背景**: #f5f7fa (浅蓝灰色)

### 9.2 操作按钮层次
- **默认状态**: 透明背景，图标颜色区分
- **悬停状态**: 浅色背景，突出显示
- **激活状态**: 稍深背景，提供反馈

## 10. 特殊处理规范

### 10.1 长文本处理
- **表格单元格**: 使用 `show-overflow-tooltip`
- **工具提示**: 显示完整内容
- **最大宽度**: 根据列内容设置合理宽度

### 10.2 状态标签处理
- **颜色映射**: 根据业务状态映射对应颜色
- **文本内容**: 使用简洁的状态描述
- **视觉一致性**: 保持同类状态颜色统一

### 10.3 操作按钮权限控制
- **显示控制**: 根据权限动态显示按钮
- **禁用状态**: 使用 `disabled` 属性
- **视觉反馈**: 禁用时降低透明度

## 11. 交互优化

### 11.1 按钮交互
- **悬停延迟**: 500ms 后显示工具提示
- **点击反馈**: 轻微的缩放或颜色变化
- **加载状态**: 操作进行时显示加载图标

### 11.2 表格交互
- **行悬停**: 整行背景色变化，优先级高于隔行变色
- **选择状态**: 如有选择功能，突出显示
- **排序指示**: 可排序列显示排序图标

## 12. 隔行变色实现

### 12.1 HTML结构
```html
<el-table :data="tableData" stripe>
  <!-- 表格列定义 -->
</el-table>
```

### 12.2 CSS实现
```scss
:deep(.el-table--striped) {
  .el-table__body tr.el-table__row--striped td {
    background-color: #fafafa;
  }
  
  .el-table__body tr:hover td {
    background-color: #f5f7fa !important;
  }
}
```

### 12.3 颜色对比度
- **奇偶行对比**: #ffffff 与 #fafafa 形成微妙对比
- **悬停突出**: #f5f7fa 提供明显的交互反馈
- **视觉舒适**: 颜色差异适中，不会造成视觉疲劳
