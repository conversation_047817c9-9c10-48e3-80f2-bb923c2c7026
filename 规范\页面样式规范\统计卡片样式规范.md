# TestDriveReport 统计卡片样式规范

## 1. 统计概览区域样式

### 1.1 统计区域容器
- **类名**: `stats-section`
- **下边距**: 20px (`mb-20`)

### 1.2 栅格布局样式
- **行组件**: `el-row`
- **列间距**: 16px (`:gutter="16"`)
- **列组件**: `el-col`
- **列宽度**: `:span="6"` (每行4个统计卡片)

## 2. 统计卡片样式规范

### 2.1 卡片容器样式
- **类名**: `stat-card`
- **背景色**: 白色 (#ffffff)
- **圆角**: 4px (`border-radius: 4px`)
- **内边距**: 20px (`padding: 20px`)
- **阴影**: `0 1px 4px rgba(0,21,41,.08)`
- **左边框**: 4px solid #1890ff (蓝色装饰边框)
- **高度**: 100% (自适应容器高度)

### 2.2 统计标题样式
- **类名**: `stat-title`
- **字体大小**: 14px
- **颜色**: rgba(0,0,0,.45) (浅灰色)
- **下边距**: 8px (`margin-bottom: 8px`)

### 2.3 统计数值样式
- **类名**: `stat-value`
- **字体大小**: 30px
- **字体粗细**: bold
- **颜色**: #333 (深灰色)

### 2.4 长文本数值样式
- **类名**: `stat-value-long`
- **字体大小**: 20px
- **字体粗细**: bold
- **颜色**: #333 (深灰色)
- **文本溢出处理**: 
  - `overflow: hidden`
  - `text-overflow: ellipsis`
  - `white-space: nowrap`

## 3. 布局结构规范

### 3.1 统计卡片排列
- **每行数量**: 4个统计卡片
- **卡片间距**: 16px (通过栅格系统实现)
- **卡片宽度**: 自适应 (1/4容器宽度)

### 3.2 卡片内容结构
```html
<div class="stat-card">
  <div class="stat-title">标题文本</div>
  <div class="stat-value">数值</div>
</div>
```

### 3.3 长文本卡片结构
```html
<div class="stat-card">
  <div class="stat-title">标题文本</div>
  <div class="stat-value-long">长文本内容</div>
</div>
```

## 4. CSS 样式定义

### 4.1 统计区域样式
```scss
.stats-section {
  margin-bottom: 20px;
  
  .stat-card {
    background-color: white;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    border-left: 4px solid #1890ff;
    height: 100%;

    .stat-title {
      font-size: 14px;
      color: rgba(0,0,0,.45);
      margin-bottom: 8px;
    }
    
    .stat-value {
      font-size: 30px;
      font-weight: bold;
      color: #333;
    }
    
    .stat-value-long {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
```

## 5. 视觉设计规范

### 5.1 颜色规范
- **卡片背景**: #ffffff (纯白)
- **装饰边框**: #1890ff (主题蓝色)
- **标题文字**: rgba(0,0,0,.45) (45%透明度黑色)
- **数值文字**: #333333 (深灰色)
- **卡片阴影**: rgba(0,21,41,.08) (8%透明度深蓝)

### 5.2 字体规范
- **标题字体**: 14px, normal
- **普通数值**: 30px, bold
- **长文本数值**: 20px, bold

### 5.3 间距规范
- **卡片内边距**: 20px (四周)
- **标题下边距**: 8px
- **卡片间距**: 16px (通过栅格系统)
- **区域下边距**: 20px

### 5.4 边框规范
- **卡片圆角**: 4px
- **左装饰边框**: 4px solid #1890ff
- **边框位置**: 左侧垂直边框

## 6. 响应式设计

### 6.1 栅格系统
- **总栅格数**: 24
- **每个卡片占用**: 6个栅格 (1/4宽度)
- **列间距**: 16px
- **自适应**: 卡片宽度根据容器自动调整

### 6.2 文本处理
- **短数值**: 正常显示，字体较大 (30px)
- **长文本**: 自动省略，字体适中 (20px)
- **溢出处理**: 使用省略号 (`text-overflow: ellipsis`)

## 7. 阴影和层次

### 7.1 卡片阴影
- **阴影值**: `0 1px 4px rgba(0,21,41,.08)`
- **阴影类型**: 轻微底部阴影
- **视觉效果**: 卡片轻微浮起效果

### 7.2 视觉层次
- **背景层**: 页面背景 (#f0f2f5)
- **卡片层**: 白色卡片 + 轻微阴影
- **装饰层**: 左侧蓝色边框
- **内容层**: 标题 + 数值文本

## 8. 特殊处理规范

### 8.1 长文本卡片
- **适用场景**: 门店名称、车型名称等可能较长的文本
- **字体调整**: 相对较小 (20px vs 30px)
- **溢出处理**: 省略号显示

### 8.2 数值卡片
- **适用场景**: 纯数字统计
- **字体突出**: 较大字体 (30px)
- **视觉重点**: 数值为主要信息