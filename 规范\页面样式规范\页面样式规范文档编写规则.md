# 页面样式规范文档编写规则

## 1. 文档编写原则

### 1.1 核心原则
- **纯样式描述**：只关注UI样式和布局，不涉及业务逻辑
- **精确数值**：使用具体的像素值、百分比等精确数值
- **统一规范**：确保同类型页面样式的一致性
- **可复用性**：规范应能指导其他页面的开发

### 1.2 禁止内容
- ❌ 具体的业务字段信息
- ❌ 国际化支持描述
- ❌ 交互行为细节
- ❌ 性能优化建议
- ❌ 可访问性描述
- ❌ API调用逻辑

### 1.3 必须包含
- ✅ 容器样式规范
- ✅ 布局结构规范
- ✅ 组件样式规范
- ✅ CSS样式定义
- ✅ 颜色和字体规范
- ✅ 间距体系规范
- ✅ 响应式布局规范

## 2. 文档结构规范

### 2.1 标准文档结构
```markdown
# [页面名称] [功能区域]样式规范

## 1. 页面容器样式
## 2. [主要区域]样式规范
## 3. [次要区域]样式规范
## 4. 布局结构规范
## 5. CSS样式定义
## 6. 响应式设计
## 7. 视觉设计规范
## 8. 特殊处理规范（如需要）
```

### 2.2 章节编号规则
- **一级标题**：使用 `# 标题`
- **二级标题**：使用 `## 数字. 标题`
- **三级标题**：使用 `### 数字.数字 标题`
- **四级标题**：使用 `#### 数字.数字.数字 标题`

## 3. 样式描述规范

### 3.1 组件描述格式
```markdown
### 3.1 组件名称
- **组件**: `el-component`
- **类名**: `component-class`
- **属性**: 具体属性值
- **样式**: 具体样式值
```

### 3.2 尺寸规范描述
- **宽度**: 使用px、%、auto等单位
- **高度**: 使用px、vh、auto等单位
- **内边距**: `padding: 20px`
- **外边距**: `margin: 20px`
- **间距**: 具体像素值

### 3.3 颜色规范描述
- **背景色**: 使用十六进制色值 `#ffffff`
- **文字颜色**: 使用十六进制或rgba `#333333`
- **边框颜色**: 使用十六进制色值
- **阴影颜色**: 使用rgba格式

## 4. CSS代码规范

### 4.1 代码块格式
```markdown
### 5.1 样式区域名称
```scss
.class-name {
  property: value;
  
  .nested-class {
    property: value;
  }
}
```

### 4.2 CSS属性顺序
1. **定位属性**: position, top, right, bottom, left, z-index
2. **盒模型**: display, width, height, padding, margin, border
3. **背景**: background, background-color, background-image
4. **字体**: font-size, font-weight, color, line-height
5. **其他**: border-radius, box-shadow, overflow等

### 4.3 命名规范
- **类名**: 使用kebab-case `search-card`
- **变量**: 使用camelCase（如果有）
- **组件**: 使用PascalCase `el-Card`

## 5. 布局描述规范

### 5.1 栅格系统描述
```markdown
### 6.1 栅格系统
- **总栅格数**: 24
- **每个组件占用**: X个栅格
- **列间距**: XXpx
- **自适应**: 描述自适应规则
```

### 5.2 间距体系描述
```markdown
### 6.2 间距体系
- **一级间距**: XXpx (页面级)
- **二级间距**: XXpx (区域级)
- **三级间距**: XXpx (组件级)
- **四级间距**: XXpx (元素级)
```

## 6. 视觉规范描述

### 6.1 颜色规范格式
```markdown
### 8.1 背景色
- **页面背景**: #f5f5f5
- **卡片背景**: #ffffff
- **按钮背景**: Element Plus 主题色

### 8.2 文字颜色
- **标题颜色**: #303133
- **正文颜色**: #606266
- **辅助文字**: #909399
```

### 6.2 字体规范格式
```markdown
### 9.1 字体大小
- **页面标题**: 24px
- **区域标题**: 18px
- **正文**: 14px
- **辅助文字**: 12px

### 9.2 字体粗细
- **标题**: 600 (semi-bold)
- **正文**: 400 (normal)
```

## 7. 文件命名规范

### 7.1 文件命名格式
- **查询功能**: `[页面名称]查询功能页面样式规范.md`
- **统计卡片**: `[页面名称]统计卡片样式规范.md`
- **表格列表**: `[页面名称]表格列表样式规范.md`
- **表单弹窗**: `[页面名称]表单弹窗样式规范.md`

### 7.2 存储位置
- **规范目录**: `规范/`
- **子分类**: 可按功能模块创建子目录

## 8. 特殊情况处理

### 8.1 复杂组件描述
对于复杂组件，应该：
- 分层描述（容器→内容→细节）
- 提供完整的HTML结构示例
- 说明特殊的CSS处理

### 8.2 响应式规范
```markdown
### 6. 响应式设计
- **断点**: 描述不同屏幕尺寸的适配
- **布局变化**: 描述布局在不同尺寸下的变化
- **组件适配**: 描述组件的响应式行为
```

### 8.3 特殊状态描述
```markdown
### 8. 特殊处理规范
- **加载状态**: 描述加载时的样式
- **空数据状态**: 描述无数据时的样式
- **错误状态**: 描述错误时的样式
```

## 9. 质量检查清单

### 9.1 内容检查
- [ ] 是否只包含样式描述
- [ ] 是否使用了精确的数值
- [ ] 是否提供了完整的CSS代码
- [ ] 是否包含了所有必要的样式规范

### 9.2 格式检查
- [ ] 标题层级是否正确
- [ ] 代码块格式是否规范
- [ ] 列表格式是否统一
- [ ] 文档结构是否完整

### 9.3 实用性检查
- [ ] 是否能指导实际开发
- [ ] 是否具有可复用性
- [ ] 是否符合项目整体规范
- [ ] 是否易于理解和执行

## 10. 示例模板

### 10.1 基础模板
```markdown
# [页面名称] [功能区域]样式规范

## 1. 页面容器样式
### 1.1 根容器
- **类名**: `page-container`
- **内边距**: 20px
- **背景色**: #f5f5f5

## 2. [主要区域]样式规范
### 2.1 区域容器
- **组件**: `el-card`
- **类名**: `main-card`
- **样式**: 具体样式描述

## 5. CSS样式定义
### 5.1 主要样式
```scss
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
}
```

## 6. 响应式设计
### 6.1 栅格系统
- **总栅格数**: 24
- **布局规则**: 具体规则描述
```

---

**使用说明**：
1. 严格按照此规范编写样式文档
2. 确保文档只关注样式，不涉及业务逻辑
3. 使用精确的数值和完整的CSS代码
4. 保持文档结构的一致性和可读性