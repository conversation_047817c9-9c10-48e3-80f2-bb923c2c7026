# 页面目录结构规范

本规范定义了 DMS 前端项目中页面目录的组织方式，确保项目结构的一致性和可维护性。

## 1. 基本原则

### 1.1 路由页面与非路由页面区分
- **路由页面**: 需要在菜单中显示或可直接通过 URL 访问的页面，放在功能目录下
- **非路由页面**: 通过弹窗、抽屉或页面跳转访问的页面，统一放在功能目录下的 `components/` 目录中

### 1.2 命名规范
- **路由页面**: 使用 `功能名View.vue` 格式（如 `ProspectsView.vue`）
- **非路由页面**: 使用 `功能操作View.vue` 格式（如 `ProspectDetailView.vue`）
- **组件**: 使用 `功能名Component.vue` 格式（如 `ProspectForm.vue`）

## 2. 目录结构模板

### 2.1 一级模块结构
```
src/views/模块名/
├── 模块名View.vue              # 模块首页（路由页面）
├── 功能A/                      # 功能A目录
└── 功能B/                      # 功能B目录
```

### 2.2 功能模块结构
```
src/views/模块名/功能名/
├── 功能名View.vue              # 功能主页面（路由页面，通常是列表页）
└── components/                 # 非路由页面和组件统一目录
    ├── 功能名DetailView.vue    # 详情页面（非路由）
    ├── 功能名CreateView.vue    # 新建页面（非路由）
    ├── 功能名EditView.vue      # 编辑页面（非路由）
    ├── 功能名Form.vue          # 表单组件
    ├── 功能名Card.vue          # 卡片组件
    └── 功能名Table.vue         # 表格组件
```

## 3. API 接口模块结构

### 3.1 API 模块组织
API 接口按照页面模块结构进行组织，保持一致性：

```
src/api/modules/
├── sales/                      # 销售模块 API
│   ├── prospects.ts            # 潜客管理 API
│   ├── orders.ts               # 订单管理 API
│   └── customers.ts            # 客户管理 API
├── afterSales/                 # 售后模块 API
│   ├── appointments.ts         # 预约管理 API
│   ├── repairs.ts              # 维修管理 API
│   └── warranty.ts             # 保修管理 API
├── parts/                      # 零件模块 API
│   ├── procurement.ts          # 采购管理 API
│   ├── inventory.ts            # 库存管理 API
│   └── suppliers.ts            # 供应商管理 API
└── base/                       # 基础模块 API
    ├── auth.ts                 # 认证相关 API
    ├── dict.ts                 # 字典管理 API
    └── system.ts               # 系统配置 API
```

## 4. Mock 数据结构

### 4.1 Mock 数据组织
Mock 数据按照 API 模块结构进行组织：

```
src/mock/data/
├── sales/                      # 销售模块 Mock 数据
│   ├── prospects.ts            # 潜客管理 Mock 数据
│   ├── orders.ts               # 订单管理 Mock 数据
│   └── customers.ts            # 客户管理 Mock 数据
├── afterSales/                 # 售后模块 Mock 数据
│   ├── appointments.ts         # 预约管理 Mock 数据
│   ├── repairs.ts              # 维修管理 Mock 数据
│   └── warranty.ts             # 保修管理 Mock 数据
├── parts/                      # 零件模块 Mock 数据
│   ├── procurement.ts          # 采购管理 Mock 数据
│   ├── inventory.ts            # 库存管理 Mock 数据
│   └── suppliers.ts            # 供应商管理 Mock 数据
└── base/                       # 基础模块 Mock 数据
    ├── auth.ts                 # 认证相关 Mock 数据
    ├── dict.ts                 # 字典管理 Mock 数据
    └── system.ts               # 系统配置 Mock 数据
```

## 5. 公共组件分层结构

### 5.1 公共组件分层
```
src/components/
├── layout/                     # 布局组件
│   ├── PageLayout.vue          # 页面布局组件
│   ├── PageCard.vue            # 页面卡片组件
│   └── AppHeader.vue           # 应用头部组件
├── common/                     # 通用业务组件
│   ├── DataTable.vue           # 数据表格组件
│   ├── SearchForm.vue          # 搜索表单组件
│   ├── DictSelect.vue          # 字典选择器
│   ├── RegionSelect.vue        # 省市区选择器
│   ├── VehicleSelect.vue       # 车型选择器
│   └── PageTabs.vue            # 页面标签组件
├── form/                       # 表单相关组件
│   ├── FormDialog.vue          # 表单弹窗组件
│   ├── FormDrawer.vue          # 表单抽屉组件
│   └── FormSteps.vue           # 分步表单组件
└── ui/                         # 基础 UI 组件
    ├── IconButton.vue          # 图标按钮
    ├── StatusTag.vue           # 状态标签
    └── EmptyState.vue          # 空状态组件
```

## 6. 路由结构

### 6.1 路由模块组织
路由按照页面模块结构进行组织：

```
src/router/modules/
├── sales.ts                    # 销售模块路由
├── afterSales.ts               # 售后模块路由
├── parts.ts                    # 零件模块路由
└── base.ts                     # 基础模块路由
```

## 7. 国际化文件结构

### 7.1 国际化模块组织
国际化文件按照功能模块进行组织：

```
src/locales/modules/
├── common/                     # 通用模块
│   ├── zh.json                 # 中文
│   └── en.json                 # 英文
├── sales/                      # 销售模块
│   ├── zh.json
│   └── en.json
├── afterSales/                 # 售后模块
│   ├── zh.json
│   └── en.json
├── parts/                      # 零件模块
│   ├── zh.json
│   └── en.json
└── base/                       # 基础模块
    ├── zh.json
    └── en.json
```

## 8. 类型定义结构

### 8.1 类型定义组织
类型定义按照功能模块进行组织：

```
src/types/
├── common/                     # 通用类型定义
│   ├── api.d.ts                # API 通用类型
│   ├── table.d.ts              # 表格相关类型
│   └── form.d.ts               # 表单相关类型
├── sales/                      # 销售模块类型
│   ├── prospects.d.ts          # 潜客管理类型
│   ├── orders.d.ts             # 订单管理类型
│   └── customers.d.ts          # 客户管理类型
├── afterSales/                 # 售后模块类型
│   ├── appointments.d.ts       # 预约管理类型
│   ├── repairs.d.ts            # 维修管理类型
│   └── warranty.d.ts           # 保修管理类型
├── parts/                      # 零件模块类型
│   ├── procurement.d.ts        # 采购管理类型
│   ├── inventory.d.ts          # 库存管理类型
│   └── suppliers.d.ts          # 供应商管理类型
└── base/                       # 基础模块类型
    ├── auth.d.ts               # 认证相关类型
    ├── dict.d.ts               # 字典管理类型
    └── system.d.ts             # 系统配置类型
```

## 9. 实际应用示例

### 9.1 销售模块 - 潜客管理完整结构
```
# 页面结构
src/views/sales/prospects/
├── ProspectsView.vue           # 潜客列表页（路由）
└── components/
    ├── ProspectDetailView.vue  # 潜客详情页（非路由）
    ├── ProspectCreateView.vue  # 新建潜客页（非路由）
    ├── ProspectEditView.vue    # 编辑潜客页（非路由）
    ├── ProspectForm.vue        # 潜客表单组件
    ├── ProspectCard.vue        # 潜客卡片组件
    └── ProspectSearchForm.vue  # 潜客搜索表单组件

# API 接口
src/api/modules/sales/
└── prospects.ts                # 潜客管理 API

# Mock 数据
src/mock/data/sales/
└── prospects.ts                # 潜客管理 Mock 数据

# 路由配置
src/router/modules/
└── sales.ts                    # 销售模块路由（包含潜客管理路由）

# 国际化文件
src/locales/modules/sales/
├── zh.json                     # 中文（包含潜客管理翻译）
└── en.json                     # 英文（包含潜客管理翻译）

# 类型定义
src/types/sales/
└── prospects.d.ts              # 潜客管理类型定义
```

### 9.2 售后模块 - 预约管理完整结构
```
# 页面结构
src/views/afterSales/appointments/
├── AppointmentsView.vue        # 预约列表页（路由）
└── components/
    ├── AppointmentDetailView.vue # 预约详情页（非路由）
    ├── AppointmentCreateView.vue # 新建预约页（非路由）
    ├── AppointmentForm.vue     # 预约表单组件
    ├── TimeSlotPicker.vue      # 时间段选择器组件
    └── ServiceTypeSelector.vue # 服务类型选择器组件

# API 接口
src/api/modules/afterSales/
└── appointments.ts             # 预约管理 API

# Mock 数据
src/mock/data/afterSales/
└── appointments.ts             # 预约管理 Mock 数据

# 路由配置
src/router/modules/
└── afterSales.ts               # 售后模块路由（包含预约管理路由）

# 国际化文件
src/locales/modules/afterSales/
├── zh.json                     # 中文（包含预约管理翻译）
└── en.json                     # 英文（包含预约管理翻译）

# 类型定义
src/types/afterSales/
└── appointments.d.ts           # 预约管理类型定义
```

### 9.3 零件模块 - 采购管理完整结构
```
# 页面结构
src/views/parts/procurement/
├── ProcurementView.vue         # 采购列表页（路由）
└── components/
    ├── ProcurementDetailView.vue # 采购详情页（非路由）
    ├── ProcurementCreateView.vue # 新建采购页（非路由）
    ├── ProcurementEditView.vue   # 编辑采购页（非路由）
    ├── ProcurementForm.vue       # 采购表单组件
    ├── SupplierSelector.vue      # 供应商选择器组件
    └── ProcurementStatusCard.vue # 采购状态卡片组件

# API 接口
src/api/modules/parts/
└── procurement.ts              # 采购管理 API

# Mock 数据
src/mock/data/parts/
└── procurement.ts              # 采购管理 Mock 数据

# 路由配置
src/router/modules/
└── parts.ts                    # 零件模块路由（包含采购管理路由）

# 国际化文件
src/locales/modules/parts/
├── zh.json                     # 中文（包含采购管理翻译）
└── en.json                     # 英文（包含采购管理翻译）

# 类型定义
src/types/parts/
└── procurement.d.ts            # 采购管理类型定义
```

## 10. 页面类型定义

### 10.1 路由页面类型（放在功能目录根部）
- **列表页**: 展示数据列表，支持搜索、筛选、分页（如 `ProspectsView.vue`）
- **仪表盘页**: 展示统计数据和图表（如 `SalesView.vue`）
- **模块首页**: 模块的入口页面，通常包含导航或概览

### 10.2 非路由页面类型（放在 components/ 目录）
- **详情页**: 展示单条记录的详细信息（如 `ProspectDetailView.vue`）
- **新建页**: 创建新记录的表单页面（如 `ProspectCreateView.vue`）
- **编辑页**: 编辑现有记录的表单页面（如 `ProspectEditView.vue`）
- **审批页**: 审批流程相关页面（如 `ProspectApprovalView.vue`）
- **导入页**: 数据导入相关页面（如 `ProspectImportView.vue`）

## 11. 组件分类规范

### 11.1 页面内部组件（components/ 目录）
- **页面组件**: `*View.vue` - 非路由页面
- **表单组件**: `*Form.vue` - 处理数据输入和验证
- **表格组件**: `*Table.vue` - 展示列表数据
- **卡片组件**: `*Card.vue` - 展示单条记录摘要
- **选择器组件**: `*Selector.vue` - 提供选择功能
- **搜索组件**: `*SearchForm.vue` - 提供搜索筛选功能

### 11.2 组件命名示例
```
components/
├── ProspectDetailView.vue     # 潜客详情页面（非路由）
├── ProspectCreateView.vue     # 潜客创建页面（非路由）
├── ProspectForm.vue           # 潜客表单组件
├── ProspectTable.vue          # 潜客表格组件
├── ProspectCard.vue           # 潜客卡片组件
├── CustomerSelector.vue       # 客户选择器组件
└── ProspectSearchForm.vue     # 潜客搜索表单组件
```

## 12. 新增页面检查清单

在创建新页面时，请确认以下事项：

### 12.1 目录结构检查
- [ ] 路由页面直接放在功能目录下
- [ ] 非路由页面和组件统一放在 `components/` 目录下
- [ ] 文件放置在正确的目录层级

### 12.2 命名规范检查
- [ ] 路由页面文件名符合 `*View.vue` 格式
- [ ] 非路由页面文件名符合 `*View.vue` 格式
- [ ] 组件文件名符合功能命名规范
- [ ] 目录名使用小驼峰命名法

### 12.3 功能完整性检查
- [ ] 相关的 API 接口已定义
- [ ] Mock 数据已准备
- [ ] 路由配置已添加（仅路由页面）
- [ ] 类型定义已完善
- [ ] 国际化文本已添加

### 12.4 模块一致性检查
- [ ] API 模块结构与页面结构保持一致
- [ ] Mock 数据结构与 API 模块结构保持一致
- [ ] 类型定义结构与功能模块保持一致
- [ ] 国际化文件按模块正确组织
- [ ] 路由配置按模块正确组织

## 13. 特殊情况处理

### 13.1 共享页面
如果某个页面被多个模块共享，放在 `src/views/shared/` 目录下：
```
src/views/shared/
├── CustomerSelectView.vue      # 客户选择页面（路由）
└── components/
    ├── CustomerDetailView.vue  # 客户详情页面（非路由）
    └── CustomerForm.vue        # 客户表单组件

# 对应的其他结构
src/api/modules/shared/
└── customer.ts                 # 共享客户 API

src/mock/data/shared/
└── customer.ts                 # 共享客户 Mock 数据

src/types/shared/
└── customer.d.ts               # 共享客户类型定义
```

### 13.2 复杂功能模块
对于功能特别复杂的模块，可以在 `components/` 目录下进一步分组：
```
src/views/sales/orders/
├── OrdersView.vue              # 订单列表页（路由）
└── components/
    ├── OrderDetailView.vue     # 订单详情页
    ├── OrderCreateView.vue     # 订单创建页
    ├── OrderEditView.vue       # 订单编辑页
    ├── forms/                  # 表单组件分组
    │   ├── BasicInfoForm.vue
    │   ├── VehicleInfoForm.vue
    │   └── PaymentForm.vue
    ├── cards/                  # 卡片组件分组
    │   ├── OrderSummaryCard.vue
    │   └── PaymentStatusCard.vue
    └── dialogs/                # 弹窗组件分组
        ├── OrderCancelDialog.vue
        └── PaymentDialog.vue
```

### 13.3 多步骤页面
对于包含多个步骤的页面，步骤组件也放在 `components/` 目录下：
```
src/views/sales/orders/
├── OrdersView.vue
└── components/
    ├── OrderCreateView.vue     # 主创建页面
    ├── Step1BasicInfo.vue      # 步骤1组件
    ├── Step2VehicleSelect.vue  # 步骤2组件
    ├── Step3Confirmation.vue   # 步骤3组件
    └── OrderForm.vue           # 订单表单组件
```

## 14. 最佳实践建议

1. **统一管理**: 所有非路由页面和组件统一放在 `components/` 目录下
2. **清晰分类**: 通过文件命名区分页面组件和普通组件
3. **功能内聚**: 相关功能的所有文件放在同一功能目录下
4. **避免深层嵌套**: 在 `components/` 目录下可适当分组，但不超过 2 层
5. **清晰命名**: 文件名能够清楚表达其功能和类型
6. **及时重构**: 当 `components/` 目录文件过多时，考虑按类型分组
7. **模块一致性**: 确保 API、Mock、类型定义、国际化文件结构与页面结构保持一致
8. **类型安全**: 所有 API 接口和数据结构都要有完整的 TypeScript 类型定义

## 15. 目录结构对比

### 15.1 调整前（不推荐）
```
src/views/sales/prospects/
├── ProspectsView.vue
├── detail/
│   └── ProspectDetailView.vue
├── create/
│   └── ProspectCreateView.vue
├── edit/
│   └── ProspectEditView.vue
└── components/
    ├── ProspectForm.vue
    └── ProspectCard.vue
```

### 15.2 调整后（推荐）
```
src/views/sales/prospects/
├── ProspectsView.vue           # 路由页面
└── components/                 # 非路由页面和组件统一目录
    ├── ProspectDetailView.vue  # 非路由页面
    ├── ProspectCreateView.vue  # 非路由页面
    ├── ProspectEditView.vue    # 非路由页面
    ├── ProspectForm.vue        # 组件
    └── ProspectCard.vue        # 组件
```

遵循此规范可以确保项目结构更加简洁统一，便于维护和扩展。通过统一的模块化组织方式，不仅页面结构清晰，API 接口、Mock 数据、类型定义、国际化文件等也都保持一致的组织结构，大大提高了项目的可维护性和开发效率。
