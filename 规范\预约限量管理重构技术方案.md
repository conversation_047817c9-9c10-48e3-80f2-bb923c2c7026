# 预约限量管理页面重构技术方案

本方案旨在根据《页面目录结构规范》和《页面重构技术规范》对 `QuotaManagementView.vue` 进行重构，提升代码的模块化、可维护性和可读性。

## 1. 重构目标

- **遵循目录规范**：将页面和相关文件迁移到 `src/views/afterSales/quota/` 目录下。
- **模块化拆分**：将类型定义、API 请求、Mock 数据和子组件进行分离。
- **提升代码质量**：移除内联类型和数据，使用统一的 API 和类型定义。
- **增强可维护性**：通过组件化和模块化，降低单个文件的复杂性。

## 2. 重构分析

`QuotaManagementView.vue` 当前存在以下问题：

- **文件位置不规范**：直接位于 `src/views/`，未按 `afterSales` 模块进行组织。
- **内联数据耦合**：包含大量内联的 Mock 数据和业务逻辑，应提取到独立的模块。
- **缺少类型定义**：内联了 `QuotaConfig` 和 `TimeSlot` 接口，应提取到独立的 `.d.ts` 文件。
- **API 模块缺失**：直接使用内联数据，未通过统一的 API 模块管理。
- **组件复杂度高**：单文件 790 行，包含多个功能模块，应拆分为子组件。

## 3. 重构步骤

### 步骤 1：创建目录和文件结构

根据规范，创建以下目录和文件：

```
src/
├── views/afterSales/quota/
│   ├── QuotaView.vue                   # 主页面（原 QuotaManagementView.vue）
│   └── components/
│       ├── StoreInfoCard.vue           # 门店信息卡片组件
│       ├── QuotaListTable.vue          # 配额列表表格组件
│       └── QuotaConfigDialog.vue       # 配额配置弹窗组件
├── api/modules/afterSales/
│   └── quota.ts                        # 预约限量 API 模块
├── types/afterSales/
│   └── quota.d.ts                      # 预约限量类型定义
├── mock/data/afterSales/
│   └── quota.ts                        # 预约限量 Mock 数据
└── locales/modules/afterSales/
    ├── zh.json                         # 中文语言包（已存在，需更新）
    └── en.json                         # 英文语言包（已存在，需更新）
```

### 步骤 2：迁移和重构类型定义

创建 `src/types/afterSales/quota.d.ts`，定义限量管理相关的类型：

```typescript
// src/types/afterSales/quota.d.ts

export interface TimeSlot {
  id: number;
  start: string;
  end: string;
  quota: number;
}

export interface QuotaConfig {
  id: number;
  configDate: string;
  timeSlotCount: number;
  totalQuota: number;
  bookedQuantity: number;
  lastUpdateTime: string;
  isExpired: boolean;
}

export interface QuotaSearchParams {
  page?: number;
  pageSize?: number;
  startDate?: string;
  endDate?: string;
  storeId?: string;
}

export interface QuotaPageResponse {
  list: QuotaConfig[];
  total: number;
}

export interface QuotaConfigRequest {
  date: string;
  timeSlots: Omit<TimeSlot, 'id'>[];
}

export interface StoreInfo {
  id: string;
  name: string;
  code: string;
}

export interface QuotaValidationResult {
  isValid: boolean;
  message?: string;
}
```

### 步骤 3：重构 Mock 数据

将 Mock 数据逻辑迁移到 `src/mock/data/afterSales/quota.ts`：

```typescript
// src/mock/data/afterSales/quota.ts

import type { 
  QuotaSearchParams, 
  QuotaPageResponse, 
  QuotaConfig,
  TimeSlot,
  QuotaConfigRequest,
  StoreInfo 
} from '@/types/afterSales/quota.d.ts';

// 门店信息
export const mockStoreInfo: StoreInfo = {
  id: 'store_001',
  name: '北京朝阳店',
  code: 'BJ_CY_001'
};

// 生成动态 Mock 数据
function generateMockQuotaData(): QuotaConfig[] {
  const data: QuotaConfig[] = [];
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = -3; i <= 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    const configDate = date.toISOString().split('T')[0];

    const timeSlotCount = Math.floor(Math.random() * 5) + 2; // 2-6 slots
    let totalQuota = 0;
    
    for (let j = 0; j < timeSlotCount; j++) {
      const quota = Math.floor(Math.random() * 20) + 5; // 5-25 quota
      totalQuota += quota;
    }

    const bookedQuantity = Math.floor(Math.random() * (totalQuota + 1));
    const lastUpdate = new Date(date.getTime() - Math.random() * 86400000)
      .toISOString()
      .slice(0, 16)
      .replace('T', ' ');

    data.push({
      id: i + 4,
      configDate,
      timeSlotCount,
      totalQuota,
      bookedQuantity,
      lastUpdateTime: lastUpdate,
      isExpired: date < today,
    });
  }
  
  return data.sort((a, b) => new Date(a.configDate).getTime() - new Date(b.configDate).getTime());
}

const mockQuotaData = generateMockQuotaData();

// 存储时段配置数据
const existingQuotaData: { [key: string]: TimeSlot[] } = {};

// 初始化时段数据
mockQuotaData.forEach(config => {
  const slots: TimeSlot[] = [];
  for (let i = 0; i < config.timeSlotCount; i++) {
    const startHour = 8 + i;
    const startMin = Math.random() < 0.5 ? 0 : 30;
    const endHour = startHour + 1;
    const endMin = startMin;

    const start = `${String(startHour).padStart(2, '0')}:${String(startMin).padStart(2, '0')}`;
    const end = `${String(endHour).padStart(2, '0')}:${String(endMin).padStart(2, '0')}`;
    const quota = Math.floor(Math.random() * 20) + 5;

    slots.push({ id: i, start, end, quota });
  }
  existingQuotaData[config.configDate] = slots;
});

export const getQuotaList = (params: QuotaSearchParams): Promise<QuotaPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockQuotaData];

      // 日期范围筛选
      if (params.startDate) {
        filteredData = filteredData.filter(item => item.configDate >= params.startDate!);
      }
      if (params.endDate) {
        filteredData = filteredData.filter(item => item.configDate <= params.endDate!);
      }

      // 更新过期状态
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      filteredData = filteredData.map(item => ({
        ...item,
        isExpired: new Date(item.configDate) < today
      }));

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};

export const getQuotaTimeSlots = (date: string): Promise<TimeSlot[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(existingQuotaData[date] || []);
    }, 200);
  });
};

export const saveQuotaConfig = (config: QuotaConfigRequest): Promise<{ success: boolean; message: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 更新存储的时段数据
      existingQuotaData[config.date] = config.timeSlots.map((slot, index) => ({
        ...slot,
        id: index
      }));

      // 更新或添加配额配置
      const existingIndex = mockQuotaData.findIndex(item => item.configDate === config.date);
      const totalQuota = config.timeSlots.reduce((sum, slot) => sum + slot.quota, 0);
      const newConfig: QuotaConfig = {
        id: existingIndex >= 0 ? mockQuotaData[existingIndex].id : Date.now(),
        configDate: config.date,
        timeSlotCount: config.timeSlots.length,
        totalQuota,
        bookedQuantity: existingIndex >= 0 ? mockQuotaData[existingIndex].bookedQuantity : 0,
        lastUpdateTime: new Date().toISOString().slice(0, 16).replace('T', ' '),
        isExpired: false
      };

      if (existingIndex >= 0) {
        mockQuotaData[existingIndex] = newConfig;
      } else {
        mockQuotaData.push(newConfig);
        mockQuotaData.sort((a, b) => new Date(a.configDate).getTime() - new Date(b.configDate).getTime());
      }

      resolve({
        success: true,
        message: '配额配置保存成功'
      });
    }, 500);
  });
};

export const getStoreInfo = (): Promise<StoreInfo> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockStoreInfo);
    }, 100);
  });
};
```

### 步骤 4：创建 API 模块

创建 `src/api/modules/afterSales/quota.ts`，统一管理 API 请求：

```typescript
// src/api/modules/afterSales/quota.ts

import request from '@/api';
import type {
  QuotaSearchParams,
  QuotaPageResponse,
  QuotaConfigRequest,
  TimeSlot,
  StoreInfo
} from '@/types/afterSales/quota.d.ts';
import {
  getQuotaList as getMockQuotaList,
  getQuotaTimeSlots as getMockQuotaTimeSlots,
  saveQuotaConfig as saveMockQuotaConfig,
  getStoreInfo as getMockStoreInfo
} from '@/mock/data/afterSales/quota';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getQuotaList = (params: QuotaSearchParams): Promise<QuotaPageResponse> => {
  if (USE_MOCK_API) {
    return getMockQuotaList(params);
  }
  return request.get<any, QuotaPageResponse>('/after-sales/quota/list', { params });
};

export const getQuotaTimeSlots = (date: string): Promise<TimeSlot[]> => {
  if (USE_MOCK_API) {
    return getMockQuotaTimeSlots(date);
  }
  return request.get<any, TimeSlot[]>(`/after-sales/quota/time-slots/${date}`);
};

export const saveQuotaConfig = (config: QuotaConfigRequest): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return saveMockQuotaConfig(config);
  }
  return request.post<any, { success: boolean; message: string }>('/after-sales/quota/config', config);
};

export const getStoreInfo = (): Promise<StoreInfo> => {
  if (USE_MOCK_API) {
    return getMockStoreInfo();
  }
  return request.get<any, StoreInfo>('/after-sales/quota/store-info');
};
```

### 步骤 5：更新国际化文件

在现有的 `src/locales/modules/afterSales/zh.json` 和 `en.json` 中，将预约限量相关的翻译组织在 `quota` 键下：

```json
// src/locales/modules/afterSales/zh.json (新增部分)
{
  "quota": {
    "pageTitle": "预约限量管理",
    "storeName": "门店名称",
    "storeCode": "门店编码",
    "permissionTip": "您当前只能管理本门店的预约限量配置",
    "configuredListTitle": "已配置限量列表",
    "addNewQuota": "新增预约限量",
    "emptyState": "暂无配置数据，点击上方按钮开始配置",
    "table": {
      "configDate": "配置日期",
      "timeSlotCount": "时段数量",
      "totalQuota": "总限量",
      "bookedQuantity": "已预约数量",
      "lastUpdateTime": "最后更新时间"
    },
    "modal": {
      "title": "新增预约限量",
      "editTitle": "编辑预约限量",
      "selectDate": "选择日期",
      "dateLabel": "配置日期",
      "datePlaceholder": "请选择配置日期",
      "dateTip": "只能配置今天及以后的日期",
      "existingConfig": "该日期已有配置",
      "timeSlotConfiguration": "时段配置",
      "addTimeSlot": "添加时段",
      "noTimeSlots": "暂无时段配置",
      "clickAddPrompt": "点击右上角按钮添加时段",
      "timeSlot": "时段",
      "startTime": "开始时间",
      "endTime": "结束时间",
      "quota": "限量",
      "startTimePlaceholder": "选择开始时间",
      "endTimePlaceholder": "选择结束时间",
      "quotaPlaceholder": "输入限量数量",
      "configDescriptionTitle": "配置说明",
      "configDescription": {
        "item1": "营业时间：08:00-18:00，请在此范围内配置时段",
        "item2": "时段不能重叠，结束时间必须晚于开始时间",
        "item3": "限量数量必须为正整数",
        "item4": "保存后立即生效，客户可在对应时段进行预约"
      }
    },
    "summary": {
      "selectDate": "请先选择配置日期",
      "addTimeSlot": "请添加至少一个时段",
      "timeSlotsUnit": "个时段",
      "totalQuota": "总限量："
    },
    "validation": {
      "dateRequired": "请选择配置日期",
      "atLeastOneTimeSlot": "请至少添加一个时段",
      "timeRequired": "请选择开始时间和结束时间",
      "startBeforeEnd": "开始时间必须早于结束时间",
      "quotaPositive": "限量数量必须大于0"
    },
    "messages": {
      "dateMustBeFutureOrToday": "配置日期不能早于今天",
      "selectDateFirst": "请先选择配置日期",
      "timeSlotExceedsOperatingHours": "时段不能超过营业时间(18:00)",
      "unsavedChangesWarning": "您有未保存的更改，确定要关闭吗？"
    }
  }
}
```

### 步骤 6：创建子组件

#### `StoreInfoCard.vue` - 门店信息卡片组件
```vue
<!-- src/views/afterSales/quota/components/StoreInfoCard.vue -->
<script setup lang="ts">
import { ElCard, ElCol, ElRow, ElIcon } from 'element-plus';
import { InfoFilled } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { StoreInfo } from '@/types/afterSales/quota.d.ts';

interface Props {
  storeInfo: StoreInfo;
}

defineProps<Props>();

const { t } = useModuleI18n('afterSales.quota');
</script>

<template>
  <el-card class="store-info-card">
    <el-row :gutter="20" class="store-info-row">
      <el-col :span="12">
        <span>{{ t('storeName') }}：{{ storeInfo.name }}</span>
      </el-col>
      <el-col :span="12">
        <span>{{ t('storeCode') }}：{{ storeInfo.code }}</span>
      </el-col>
    </el-row>
    <el-row class="permission-tip">
      <el-col>
        <el-icon><InfoFilled /></el-icon>
        <span>{{ t('permissionTip') }}</span>
      </el-col>
    </el-row>
  </el-card>
</template>

<style scoped lang="scss">
.store-info-card {
  .store-info-row {
    font-size: 16px;
    color: #606266;
    margin-bottom: 10px;
    span {
      display: inline-block;
      margin-right: 20px;
    }
  }
  .permission-tip {
    font-size: 14px;
    color: #909399;
    .el-icon {
      vertical-align: middle;
      margin-right: 5px;
    }
  }
}
</style>
```

#### `QuotaListTable.vue` - 配额列表表格组件
```vue
<!-- src/views/afterSales/quota/components/QuotaListTable.vue -->
<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElButton, ElPagination, ElIcon } from 'element-plus';
import { Plus, Edit, Calendar } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { QuotaConfig } from '@/types/afterSales/quota.d.ts';

interface Props {
  quotaList: QuotaConfig[];
  total: number;
  currentPage: number;
  pageSize: number;
  loading: boolean;
}

interface Emits {
  (e: 'add-quota'): void;
  (e: 'edit-quota', configDate: string): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.quota');

const handleAddQuota = () => {
  emit('add-quota');
};

const handleEditQuota = (configDate: string) => {
  emit('edit-quota', configDate);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (size: number) => {
  emit('page-size-change', size);
};
</script>

<template>
  <el-card class="quota-list-card">
    <div class="card-header">
      <h2>{{ t('configuredListTitle') }}</h2>
      <el-button type="primary" :icon="Plus" @click="handleAddQuota">
        {{ t('addNewQuota') }}
      </el-button>
    </div>

    <el-table :data="quotaList" v-loading="loading" style="width: 100%" class="quota-table">
      <el-table-column type="index" :label="tc('index')" width="80" />
      <el-table-column prop="configDate" :label="t('table.configDate')" min-width="150" />
      <el-table-column prop="timeSlotCount" :label="t('table.timeSlotCount')" min-width="120">
        <template #default="{ row }">
          {{ row.timeSlotCount }}{{ t('summary.timeSlotsUnit') }}
        </template>
      </el-table-column>
      <el-table-column prop="totalQuota" :label="t('table.totalQuota')" min-width="100" />
      <el-table-column prop="bookedQuantity" :label="t('table.bookedQuantity')" min-width="120" />
      <el-table-column prop="lastUpdateTime" :label="t('table.lastUpdateTime')" min-width="180" />
      <el-table-column :label="tc('operations')" width="100" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="!row.isExpired"
            type="primary"
            :icon="Edit"
            link
            @click="handleEditQuota(row.configDate)"
          >
            {{ tc('edit') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态 -->
    <div v-if="!loading && quotaList.length === 0" class="empty-state">
      <el-icon class="empty-icon"><Calendar /></el-icon>
      <p>{{ t('emptyState') }}</p>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
        :page-sizes="[10, 20, 50]"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.quota-list-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    h2 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }

  .quota-table {
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 50px 0;
  color: #909399;
  .empty-icon {
    font-size: 60px;
    margin-bottom: 15px;
  }
  p {
    margin: 0;
    font-size: 16px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
```

#### `QuotaConfigDialog.vue` - 配额配置弹窗组件
```vue
<!-- src/views/afterSales/quota/components/QuotaConfigDialog.vue -->
<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  ElDialog, ElCard, ElFormItem, ElDatePicker, ElTag, ElIcon, ElButton,
  ElSelect, ElOption, ElInput, ElRow, ElCol, ElMessage, ElMessageBox
} from 'element-plus';
import { InfoFilled, Plus, Delete, Check, Clock } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { TimeSlot } from '@/types/afterSales/quota.d.ts';

interface Props {
  visible: boolean;
  isEdit: boolean;
  selectedDate: string;
  timeSlots: TimeSlot[];
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'update:selectedDate', value: string): void;
  (e: 'update:timeSlots', value: TimeSlot[]): void;
  (e: 'save', data: { date: string; timeSlots: Omit<TimeSlot, 'id'>[] }): void;
  (e: 'date-change', date: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.quota');

const slotIdCounter = ref(0);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const modalSelectedDate = computed({
  get: () => props.selectedDate,
  set: (value) => emit('update:selectedDate', value)
});

const modalTimeSlots = computed({
  get: () => props.timeSlots,
  set: (value) => emit('update:timeSlots', value)
});

// 检查是否有已有配置
const hasExistingConfig = computed(() => {
  return props.timeSlots.length > 0 && props.isEdit;
});

// 汇总信息
const summaryInfo = computed(() => {
  if (!modalSelectedDate.value) {
    return t('summary.selectDate');
  }
  if (modalTimeSlots.value.length === 0) {
    return t('summary.addTimeSlot');
  }
  const totalQuota = modalTimeSlots.value.reduce((sum, slot) => sum + slot.quota, 0);
  return `${modalSelectedDate.value} · ${modalTimeSlots.value.length}${t('summary.timeSlotsUnit')} · ${t('summary.totalQuota')}${totalQuota}`;
});

// 日期变更处理
const onDateChange = () => {
  if (modalSelectedDate.value) {
    const selectedDate = new Date(modalSelectedDate.value);
    const today = new Date(new Date().setHours(0, 0, 0, 0));
    if (selectedDate < today) {
      ElMessage.warning(t('messages.dateMustBeFutureOrToday'));
      modalSelectedDate.value = today.toISOString().split('T')[0];
      return;
    }
    emit('date-change', modalSelectedDate.value);
  }
};

// 添加时段
const addTimeSlot = () => {
  if (!modalSelectedDate.value) {
    ElMessage.warning(t('messages.selectDateFirst'));
    return;
  }

  const newSlot: TimeSlot = {
    id: slotIdCounter.value++,
    start: '09:00',
    end: '10:00',
    quota: 1,
  };

  modalTimeSlots.value = [...modalTimeSlots.value, newSlot];
};

// 删除时段
const deleteTimeSlot = (slotId: number) => {
  modalTimeSlots.value = modalTimeSlots.value.filter(slot => slot.id !== slotId);
};

// 更新时段
const updateTimeSlot = (slotId: number, field: 'start' | 'end' | 'quota', value: string | number) => {
  const slots = [...modalTimeSlots.value];
  const slotIndex = slots.findIndex(slot => slot.id === slotId);

  if (slotIndex !== -1) {
    if (field === 'quota') {
      slots[slotIndex].quota = Math.max(1, Math.floor(Number(value)));
    } else {
      slots[slotIndex][field] = value as string;
    }

    // 如果修改开始时间，确保结束时间仍然有效
    if (field === 'start') {
      const startTime = slots[slotIndex].start;
      const endTime = slots[slotIndex].end;
      if (compareTimes(startTime, endTime) >= 0) {
        const [startHour, startMin] = startTime.split(':').map(Number);
        const newEndTime = new Date();
        newEndTime.setHours(startHour + 1, startMin, 0);
        slots[slotIndex].end = `${String(newEndTime.getHours()).padStart(2, '0')}:${String(newEndTime.getMinutes()).padStart(2, '0')}`;
      }
    }

    modalTimeSlots.value = slots;
  }
};

// 生成时间选项
const getTimeOptions = (isEndTime: boolean, startTime?: string) => {
  const options = [];
  for (let h = 8; h <= 18; h++) {
    for (const m of [0, 30]) {
      if (h === 18 && m > 0) continue;
      const time = `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}`;
      if (isEndTime && startTime) {
        if (compareTimes(time, startTime) > 0) {
          options.push({ label: time, value: time });
        }
      } else {
        options.push({ label: time, value: time });
      }
    }
  }
  return options;
};

// 比较时间
const compareTimes = (time1: string, time2: string) => {
  const [h1, m1] = time1.split(':').map(Number);
  const [h2, m2] = time2.split(':').map(Number);
  if (h1 !== h2) return h1 - h2;
  return m1 - m2;
};

// 校验配置
const validateConfig = (): boolean => {
  if (!modalSelectedDate.value) {
    ElMessage.error(t('validation.dateRequired'));
    return false;
  }
  if (modalTimeSlots.value.length === 0) {
    ElMessage.error(t('validation.atLeastOneTimeSlot'));
    return false;
  }

  for (const slot of modalTimeSlots.value) {
    if (!slot.start || !slot.end) {
      ElMessage.error(t('validation.timeRequired'));
      return false;
    }
    if (compareTimes(slot.start, slot.end) >= 0) {
      ElMessage.error(t('validation.startBeforeEnd'));
      return false;
    }
    if (slot.quota < 1) {
      ElMessage.error(t('validation.quotaPositive'));
      return false;
    }
  }
  return true;
};

// 保存配置
const saveConfig = () => {
  if (!validateConfig()) {
    return;
  }

  const saveData = {
    date: modalSelectedDate.value,
    timeSlots: modalTimeSlots.value.map(slot => ({
      start: slot.start,
      end: slot.end,
      quota: slot.quota,
    })),
  };

  emit('save', saveData);
};

// 关闭弹窗
const closeDialog = async () => {
  if (modalTimeSlots.value.length > 0) {
    try {
      await ElMessageBox.confirm(
        t('messages.unsavedChangesWarning'),
        tc('warning'), {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning',
      });
      dialogVisible.value = false;
    } catch {
      // 用户取消关闭
    }
  } else {
    dialogVisible.value = false;
  }
};

// 监听时段变化，更新计数器
watch(() => props.timeSlots, (newSlots) => {
  if (newSlots.length > 0) {
    slotIdCounter.value = Math.max(...newSlots.map(s => s.id)) + 1;
  }
}, { immediate: true });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? t('modal.editTitle') : t('modal.title')"
    width="800px"
    class="quota-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <!-- 日期选择区 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('modal.selectDate') }}</span>
        </template>
        <el-form-item :label="t('modal.dateLabel')">
          <el-date-picker
            v-model="modalSelectedDate"
            type="date"
            value-format="YYYY-MM-DD"
            :placeholder="t('modal.datePlaceholder')"
            style="width: 100%;"
            :clearable="false"
            :disabled-date="(date: Date) => date.getTime() < new Date(new Date().setHours(0,0,0,0)).getTime()"
            @change="onDateChange"
          />
        </el-form-item>
        <div v-if="hasExistingConfig" class="existing-config-tip">
          <el-tag type="warning">{{ t('modal.existingConfig') }}</el-tag>
        </div>
        <div class="info-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>{{ t('modal.dateTip') }}</span>
        </div>
      </el-card>

      <!-- 时段配置区 -->
      <el-card class="mb-20">
        <template #header>
          <div class="card-header-flex">
            <span>{{ t('modal.timeSlotConfiguration') }}</span>
            <el-button type="info" :icon="Plus" link @click="addTimeSlot">
              {{ t('modal.addTimeSlot') }}
            </el-button>
          </div>
        </template>

        <div v-if="modalTimeSlots.length === 0" class="empty-state-modal">
          <el-icon class="empty-icon"><Clock /></el-icon>
          <p>{{ t('modal.noTimeSlots') }}</p>
          <p>{{ t('modal.clickAddPrompt') }}</p>
        </div>

        <div class="time-slot-list">
          <el-card v-for="(slot, index) in modalTimeSlots" :key="slot.id" class="time-slot-card mb-10">
            <template #header>
              <div class="card-header-flex">
                <span>{{ t('modal.timeSlot') }} {{ index + 1 }}</span>
                <el-button type="danger" :icon="Delete" link @click="deleteTimeSlot(slot.id)">
                  {{ tc('delete') }}
                </el-button>
              </div>
            </template>
            <el-row :gutter="10" align="middle">
              <el-col :xs="24" :sm="12" :md="8">
                <el-form-item :label="t('modal.startTime')">
                  <el-select
                    v-model="slot.start"
                    :placeholder="t('modal.startTimePlaceholder')"
                    class="time-select"
                    @change="(val: string) => updateTimeSlot(slot.id, 'start', val)"
                  >
                    <el-option
                      v-for="item in getTimeOptions(false)"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <el-form-item :label="t('modal.endTime')">
                  <el-select
                    v-model="slot.end"
                    :placeholder="t('modal.endTimePlaceholder')"
                    class="time-select"
                    @change="(val: string) => updateTimeSlot(slot.id, 'end', val)"
                  >
                    <el-option
                      v-for="item in getTimeOptions(true, slot.start)"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="8">
                <el-form-item :label="t('modal.quota')">
                  <el-input
                    v-model.number="slot.quota"
                    type="number"
                    :min="1"
                    :placeholder="t('modal.quotaPlaceholder')"
                    @change="(val: string | number) => updateTimeSlot(slot.id, 'quota', val)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </el-card>

      <!-- 配置说明区 -->
      <el-card>
        <template #header>
          <span>{{ t('modal.configDescriptionTitle') }}</span>
        </template>
        <ul>
          <li>{{ t('modal.configDescription.item1') }}</li>
          <li>{{ t('modal.configDescription.item2') }}</li>
          <li>{{ t('modal.configDescription.item3') }}</li>
          <li>{{ t('modal.configDescription.item4') }}</li>
        </ul>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer-buttons">
        <span class="summary-info">{{ summaryInfo }}</span>
        <div>
          <el-button @click="closeDialog">{{ tc('cancel') }}</el-button>
          <el-button type="primary" :icon="Check" @click="saveConfig" :loading="loading">
            {{ tc('save') }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.quota-dialog {
  .dialog-content {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .mb-10 {
    margin-bottom: 10px;
  }

  .card-header-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  .existing-config-tip {
    margin-top: 10px;
    text-align: right;
  }

  .info-tip {
    font-size: 13px;
    color: #909399;
    margin-top: 10px;
    .el-icon {
      vertical-align: middle;
      margin-right: 5px;
    }
  }

  .empty-state-modal {
    text-align: center;
    padding: 30px 0;
    color: #909399;
    .empty-icon {
      font-size: 50px;
      margin-bottom: 10px;
    }
    p {
      margin: 5px 0;
      font-size: 14px;
    }
  }

  .time-slot-list {
    .time-slot-card {
      background-color: #F8F8F8;
      border: 1px dashed #DCDFE6;
      .el-form-item {
        margin-bottom: 0;
      }
      .time-select {
        min-width: 120px;
        width: 100%;
      }
    }
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .summary-info {
      font-weight: bold;
      color: #303133;
      font-size: 16px;
    }
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
```

### 步骤 7：重构主页面 `QuotaView.vue`

- **移动和重命名**：将 `QuotaManagementView.vue` 移动到 `src/views/afterSales/quota/` 并重命名为 `QuotaView.vue`。
- **移除内联数据**：将 Mock 数据和业务逻辑移动到 API 模块。
- **更新数据获取**：使用新的 API 模块获取数据。
- **更新类型引用**：从 `@/types/afterSales/quota.d.ts` 导入类型。
- **更新国际化引用**：使用 `useModuleI18n('afterSales.quota')`。
- **引入子组件**：在模板中引入并使用子组件。

```vue
<!-- src/views/afterSales/quota/QuotaView.vue -->
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getQuotaList,
  getQuotaTimeSlots,
  saveQuotaConfig,
  getStoreInfo
} from '@/api/modules/afterSales/quota';
import type {
  QuotaConfig,
  TimeSlot,
  StoreInfo,
  QuotaConfigRequest
} from '@/types/afterSales/quota.d.ts';

// 导入子组件
import StoreInfoCard from './components/StoreInfoCard.vue';
import QuotaListTable from './components/QuotaListTable.vue';
import QuotaConfigDialog from './components/QuotaConfigDialog.vue';

const { t, tc } = useModuleI18n('afterSales.quota');

// 响应式数据
const storeInfo = ref<StoreInfo>({ id: '', name: '', code: '' });
const quotaList = ref<QuotaConfig[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref(false);

// 弹窗相关
const dialogVisible = ref(false);
const isEditModal = ref(false);
const modalSelectedDate = ref('');
const modalTimeSlots = ref<TimeSlot[]>([]);
const dialogLoading = ref(false);

// 获取门店信息
const fetchStoreInfo = async () => {
  try {
    const info = await getStoreInfo();
    storeInfo.value = info;
  } catch (error) {
    console.error('获取门店信息失败:', error);
  }
};

// 获取配额列表
const fetchQuotaList = async () => {
  loading.value = true;
  try {
    const response = await getQuotaList({
      page: currentPage.value,
      pageSize: pageSize.value
    });
    quotaList.value = response.list;
    total.value = response.total;
  } catch (error) {
    console.error('获取配额列表失败:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchQuotaList();
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchQuotaList();
};

// 打开新增配额弹窗
const handleAddQuota = () => {
  isEditModal.value = false;
  dialogVisible.value = true;
  modalSelectedDate.value = new Date().toISOString().split('T')[0];
  modalTimeSlots.value = [];
};

// 编辑配额
const handleEditQuota = async (configDate: string) => {
  isEditModal.value = true;
  dialogVisible.value = true;
  modalSelectedDate.value = configDate;

  try {
    const timeSlots = await getQuotaTimeSlots(configDate);
    modalTimeSlots.value = timeSlots;
  } catch (error) {
    console.error('获取时段配置失败:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 日期变更处理
const handleDateChange = async (date: string) => {
  if (!isEditModal.value) {
    try {
      const timeSlots = await getQuotaTimeSlots(date);
      modalTimeSlots.value = timeSlots;
    } catch (error) {
      modalTimeSlots.value = [];
    }
  }
};

// 保存配额配置
const handleSaveConfig = async (data: QuotaConfigRequest) => {
  dialogLoading.value = true;
  try {
    const result = await saveQuotaConfig(data);
    if (result.success) {
      ElMessage.success(result.message);
      dialogVisible.value = false;
      fetchQuotaList(); // 刷新列表
    } else {
      ElMessage.error(result.message);
    }
  } catch (error) {
    console.error('保存配额配置失败:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    dialogLoading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchStoreInfo();
  fetchQuotaList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('pageTitle') }}</h1>

    <!-- 门店信息展示区 -->
    <StoreInfoCard :store-info="storeInfo" class="mb-20" />

    <!-- 已配置限量列表区 -->
    <QuotaListTable
      :quota-list="quotaList"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      :loading="loading"
      @add-quota="handleAddQuota"
      @edit-quota="handleEditQuota"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 新增/编辑预约限量弹窗 -->
    <QuotaConfigDialog
      v-model:visible="dialogVisible"
      v-model:selected-date="modalSelectedDate"
      v-model:time-slots="modalTimeSlots"
      :is-edit="isEditModal"
      :loading="dialogLoading"
      @save="handleSaveConfig"
      @date-change="handleDateChange"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-title {
  color: #303133;
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
```

### 步骤 8：更新路由配置

修改 `src/router/index.ts` 或相关路由模块，更新预约限量管理页面的路由配置：

```typescript
// src/router/index.ts
{
  path: '/after-sales/quota',
  name: 'QuotaManagement',
  component: () => import('@/views/afterSales/quota/QuotaView.vue'),
  meta: {
    title: 'menu.quotaManagement',
    requiresAuth: true,
    icon: 'Calendar'
  }
}
```

## 4. 预期收益

- **结构清晰**：项目结构符合团队规范，新成员更容易上手。
- **职责明确**：每个文件和组件的职责更加单一，便于理解和修改。
- **易于维护**：修改或扩展功能时，只需关注相关模块，降低了代码耦合带来的风险。
- **提升开发效率**：模块化和组件化使得代码复用更加方便。
- **数据动态化**：Mock 数据支持动态生成，便于测试不同场景。
- **类型安全**：完整的 TypeScript 类型定义，提高代码质量和开发体验。

## 5. 重构检查清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/quota/QuotaView.vue`
- [ ] API模块创建在 `src/api/modules/afterSales/quota.ts`
- [ ] Mock数据创建在 `src/mock/data/afterSales/quota.ts`
- [ ] 类型定义创建在 `src/types/afterSales/quota.d.ts`
- [ ] 子组件创建在 `src/views/afterSales/quota/components/`

### 5.2 代码质量验证
- [ ] 移除页面中的内联数据和类型定义
- [ ] 使用统一的API调用替换内联Mock数据
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持动态生成和CRUD操作

### 5.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 门店信息正确显示
- [ ] 配额列表正常显示和分页
- [ ] 新增配额功能正常工作
- [ ] 编辑配额功能正常工作
- [ ] 时段配置功能正常工作
- [ ] 数据校验功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

### 5.4 路由验证
- [ ] 路由路径更新为新的模块化路径
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 6. 实施建议

### 6.1 实施顺序
1. **创建基础结构**：先创建目录和类型定义
2. **重构数据层**：创建Mock数据和API模块
3. **拆分组件**：创建子组件并测试
4. **重构主页面**：更新主页面逻辑
5. **更新配置**：修改路由和国际化
6. **测试验证**：全面测试功能正常性

### 6.2 风险控制
- **渐进式重构**：一次只重构一个模块，避免大范围影响
- **保持功能不变**：重构过程中不改变业务逻辑和用户体验
- **及时测试**：每个步骤完成后及时验证功能正常
- **代码备份**：重构前备份原始代码

---

**本技术方案基于页面目录结构规范和页面重构技术规范制定，为预约限量管理页面的重构提供详细的实施指导。**
