# 语言切换后端接口对接说明

## 概述

前端语言切换功能需要后端配合，主要体现在：
1. 接收前端传递的语言参数
2. 根据语言返回对应的菜单数据
3. 在其他业务接口中根据语言返回相应内容

## 前端传递的语言信息

### 1. 请求参数
```
GET /api/v1/user/info?lang=zh_CN
POST /api/v1/orders?lang=en_US
```

### 2. 请求头
```
Accept-Language: zh_CN
Accept-Language: en_US
```

## 后端接收语言信息

### Java Spring Boot 示例

```java
@RestController
@RequestMapping("/api/v1")
public class UserController {
    
    @GetMapping("/user/info")
    public Result<UserInfo> getUserInfo(
        @RequestParam(value = "lang", required = false) String lang,
        HttpServletRequest request) {
        
        // 1. 优先从请求参数获取
        if (lang == null || lang.isEmpty()) {
            String acceptLanguage = request.getHeader("Accept-Language");
            if (acceptLanguage != null && !acceptLanguage.isEmpty()) {
                String[] languages = acceptLanguage.split(",");
                if (languages.length > 0) {
                    lang = convertToStandardLanguageCode(languages[0].trim());
                }
            }
        }
        
        // 2. 默认语言
        if (lang == null || lang.isEmpty()) {
            lang = "zh_CN";
        }
        
        // 3. 根据语言获取用户信息
        UserInfo userInfo = userService.getUserInfo(getCurrentUserId(), lang);
        return Result.success(userInfo);
    }
    
    private String convertToStandardLanguageCode(String lang) {
        if (lang.startsWith("zh")) {
            return "zh_CN";
        } else if (lang.startsWith("en")) {
            return "en_US";
        }
        return "zh_CN"; // 默认中文
    }
}
```

## 菜单数据结构

### 请求示例
```http
GET /api/v1/user/info?lang=zh_CN
Authorization: Bearer {token}
Accept-Language: zh_CN
```

### 响应示例（中文）
```json
{
  "code": "200",
  "message": "success",
  "result": {
    "userId": 1,
    "username": "admin",
    "realName": "管理员",
    "roles": ["ADMIN"],
    "permissions": ["*:*:*"],
    "menus": [
      {
        "menuId": 1,
        "menuName": "首页",
        "menuCode": "home",
        "menuPath": "/",
        "menuIcon": "HomeFilled",
        "menuType": "menu",
        "parentId": null,
        "children": []
      },
      {
        "menuId": 2,
        "menuName": "到店登记",
        "menuCode": "checkinList",
        "menuPath": "/checkin",
        "menuIcon": "List",
        "menuType": "menu",
        "parentId": null,
        "children": []
      }
    ]
  }
}
```

### 响应示例（英文）
```json
{
  "code": "200",
  "message": "success",
  "result": {
    "userId": 1,
    "username": "admin",
    "realName": "Administrator",
    "roles": ["ADMIN"],
    "permissions": ["*:*:*"],
    "menus": [
      {
        "menuId": 1,
        "menuName": "Home",
        "menuCode": "home",
        "menuPath": "/",
        "menuIcon": "HomeFilled",
        "menuType": "menu",
        "parentId": null,
        "children": []
      },
      {
        "menuId": 2,
        "menuName": "Check-in List",
        "menuCode": "checkinList",
        "menuPath": "/checkin",
        "menuIcon": "List",
        "menuType": "menu",
        "parentId": null,
        "children": []
      }
    ]
  }
}
```

## 关键字段说明

### 菜单字段
- `menuName`: **重要** - 根据语言返回对应的名称
- `menuCode`: **必须** - 前端用于匹配翻译，应该保持不变
- `menuPath`: 菜单路径，通常不需要国际化
- `menuIcon`: 图标名称，通常不需要国际化
- `menuType`: 菜单类型，通常不需要国际化

### 用户字段
- `realName`: 用户真实姓名，可能需要根据语言调整显示

## 数据库设计建议

### 方案1：多语言字段
```sql
CREATE TABLE sys_menu (
    menu_id BIGINT PRIMARY KEY,
    menu_code VARCHAR(50) NOT NULL,
    menu_name_zh VARCHAR(100),
    menu_name_en VARCHAR(100),
    menu_path VARCHAR(200),
    menu_icon VARCHAR(50),
    menu_type VARCHAR(20),
    parent_id BIGINT,
    created_time DATETIME,
    updated_time DATETIME
);
```

### 方案2：国际化表
```sql
-- 菜单基础表
CREATE TABLE sys_menu (
    menu_id BIGINT PRIMARY KEY,
    menu_code VARCHAR(50) NOT NULL,
    menu_path VARCHAR(200),
    menu_icon VARCHAR(50),
    menu_type VARCHAR(20),
    parent_id BIGINT,
    created_time DATETIME,
    updated_time DATETIME
);

-- 国际化表
CREATE TABLE sys_menu_i18n (
    id BIGINT PRIMARY KEY,
    menu_id BIGINT NOT NULL,
    language VARCHAR(10) NOT NULL,
    menu_name VARCHAR(100) NOT NULL,
    INDEX idx_menu_lang (menu_id, language)
);
```

## Service层实现示例

```java
@Service
public class UserService {
    
    public UserInfo getUserInfo(Long userId, String language) {
        // 获取用户基本信息
        UserInfo userInfo = userMapper.selectById(userId);
        
        // 获取用户菜单（根据语言）
        List<Menu> menus = menuService.getUserMenus(userId, language);
        userInfo.setMenus(menus);
        
        return userInfo;
    }
}

@Service
public class MenuService {
    
    public List<Menu> getUserMenus(Long userId, String language) {
        // 获取用户有权限的菜单
        List<Menu> menus = menuMapper.selectUserMenus(userId);
        
        // 根据语言设置菜单名称
        for (Menu menu : menus) {
            String menuName = getMenuName(menu.getMenuCode(), language);
            menu.setMenuName(menuName);
        }
        
        return menus;
    }
    
    private String getMenuName(String menuCode, String language) {
        // 从国际化配置或数据库获取翻译
        if ("en_US".equals(language)) {
            return menuI18nMapper.getMenuName(menuCode, "en");
        } else {
            return menuI18nMapper.getMenuName(menuCode, "zh");
        }
    }
}
```

## 测试建议

### 1. 接口测试
```bash
# 测试中文
curl -X GET "http://localhost:8080/api/v1/user/info?lang=zh_CN" \
  -H "Authorization: Bearer {token}" \
  -H "Accept-Language: zh_CN"

# 测试英文
curl -X GET "http://localhost:8080/api/v1/user/info?lang=en_US" \
  -H "Authorization: Bearer {token}" \
  -H "Accept-Language: en_US"
```

### 2. 验证要点
- [ ] 接口能正确接收语言参数
- [ ] 菜单名称根据语言正确返回
- [ ] menuCode字段保持一致
- [ ] 默认语言处理正确
- [ ] 语言切换后菜单内容更新

## 常见问题

### Q: 前端为什么同时传递参数和请求头？
A: 为了兼容性，某些代理或网关可能会过滤请求头，而参数更稳定。

### Q: menuCode和menuName的区别？
A: menuCode用于前端匹配翻译，应该保持不变；menuName是显示文本，需要根据语言变化。

### Q: 如果某个菜单没有英文翻译怎么办？
A: 可以返回中文原文，或者返回menuCode，前端会有降级处理。

### Q: 用户信息缓存如何处理？
A: 建议缓存键包含语言信息，如：`user:info:123:zh_CN`

## 注意事项

1. **性能考虑**: 菜单翻译建议缓存，避免每次都查询数据库
2. **数据一致性**: 确保menuCode在不同语言下保持一致
3. **错误处理**: 语言参数异常时应使用默认语言
4. **日志记录**: 记录语言切换相关的操作日志
5. **向后兼容**: 如果没有传递语言参数，应使用默认语言 