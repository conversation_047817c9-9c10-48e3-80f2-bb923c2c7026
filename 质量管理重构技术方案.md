# 质量管理重构技术方案

基于页面目录结构规范和页面重构技术规范，对 `src/views/qualityCheck/` 模块进行重构的完整技术方案。

## 1. 现状分析

### 1.1 当前目录结构
```
src/views/qualityCheck/
├── QualityCheckList.vue                # 主页面文件（759行）
└── components/
    ├── QualityCheckDetailModal.vue     # 详情对话框（859行）
    └── QualityCheckEditModal.vue       # 编辑对话框
```

### 1.2 相关文件分布
```
src/api/modules/qualityCheck.ts         # 质检API模块（已存在）
src/types/module.d.ts                   # 类型定义（分散在通用文件中）
src/mock/data/qualityCheck.ts           # Mock数据（已存在）
src/locales/modules/                    # 国际化文件（需要创建qualityCheck模块）
```

### 1.3 问题识别
- ❌ 页面位置不符合规范：应该在 `afterSales` 模块下
- ❌ 使用 `useI18n()` 而非 `useModuleI18n()`
- ❌ 单文件组件过于庞大（759行），缺乏组件化拆分
- ❌ 类型定义分散在 `module.d.ts` 中
- ❌ 缺乏专门的国际化模块
- ❌ Mock数据与API模块集成度不高

## 2. 重构目标结构

### 2.1 目标目录结构
```
src/
├── views/afterSales/qualityCheck/
│   ├── QualityCheckManagementView.vue  # 主页面（路由页面）
│   └── components/
│       ├── QualityCheckSearchForm.vue  # 搜索表单组件
│       ├── QualityCheckTable.vue       # 质检表格组件
│       ├── QualityCheckDetailDialog.vue # 质检详情对话框
│       ├── QualityCheckEditDialog.vue  # 质检编辑对话框
│       ├── QualityCheckSubmitDialog.vue # 质检提交对话框
│       ├── QualityCheckAuditDialog.vue # 质检审核对话框
│       ├── QualityCheckStatsCard.vue   # 统计卡片组件
│       └── QualityCheckItemForm.vue    # 质检项目表单组件
├── api/modules/afterSales/
│   └── qualityCheck.ts                 # 重构后的API模块
├── types/afterSales/
│   └── qualityCheck.d.ts               # 统一的类型定义
├── mock/data/afterSales/
│   └── qualityCheck.ts                 # 重构后的Mock数据
└── locales/modules/afterSales/
    ├── zh.json                          # 中文翻译（包含qualityCheck）
    └── en.json                          # 英文翻译（包含qualityCheck）
```

## 3. 重构执行计划

### 3.1 阶段一：基础结构创建

#### 步骤1：创建目录结构
```bash
# 创建新的目录结构
mkdir -p src/views/afterSales/qualityCheck/components
mkdir -p src/api/modules/afterSales
mkdir -p src/types/afterSales
mkdir -p src/mock/data/afterSales
```

#### 步骤2：统一类型定义
将分散的类型定义统一到 `src/types/afterSales/qualityCheck.d.ts`：

```typescript
// 基础类型定义
export type QualityCheckStatus = 'pending_check' | 'checking' | 'pending_review' | 'passed' | 'rework';
export type QualityCheckAuditResult = 'passed' | 'rework';
export type QualityCheckItemType = 'BOOLEAN' | 'NUMERIC' | 'TEXT';
export type QualityCheckResult = 'PASS' | 'FAIL';
export type WorkOrderType = 'maintenance' | 'repair' | 'insurance';

// 质检单列表项接口
export interface QualityCheckListItem {
  id: string;
  qualityCheckNo: string;
  status: QualityCheckStatus;
  workOrderNo: string;
  workOrderType: WorkOrderType;
  isClaimRelated: boolean;
  isOutsourceRelated: boolean;
  serviceCustomerName: string;
  serviceCustomerPhone: string;
  plateNumber: string;
  vehicleModel: string;
  vehicleConfig: string;
  vehicleColor: string;
  technicianName: string;
  startTime?: string;
  finishTime?: string;
  estimatedHours?: number;
  actualHours?: number;
  reworkReason?: string;
  reworkRequirement?: string;
  createTime: string;
  updateTime: string;
}

// 搜索参数接口
export interface QualityCheckSearchParams {
  page: number;
  pageSize: number;
  workOrderNo?: string;
  qualityCheckNo?: string;
  status?: QualityCheckStatus[];
  workOrderType?: WorkOrderType;
  technicianName?: string;
  plateNumber?: string;
  serviceCustomerName?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  isClaimRelated?: boolean;
  isOutsourceRelated?: boolean;
}

// 质检项目接口
export interface QualityCheckItem {
  id: string;
  qualityCheckId: string;
  categoryCode: string;
  categoryName: string;
  itemCode: string;
  itemName: string;
  itemType: QualityCheckItemType;
  checkResult?: QualityCheckResult;
  numericValue?: number;
  textValue?: string;
  standardValue?: string;
  unit?: string;
  isRequired: boolean;
  sortOrder: number;
  createTime: string;
  updateTime: string;
}

// 质检详情接口
export interface QualityCheckDetail {
  qualityCheck: QualityCheckOrder;
  checkItems: QualityCheckItem[];
  workOrderInfo: WorkOrderInfo;
  vehicleInfo: VehicleInfo;
  serviceInfo: ServiceInfo;
}

// 质检提交表单接口
export interface QualityCheckSubmitForm {
  qualityCheckId: string;
  actualHours?: number;
  remarks?: string;
  checkItems: QualityCheckItemForm[];
  isDraft?: boolean;
}

// 质检审核表单接口
export interface QualityCheckAuditForm {
  qualityCheckId: string;
  auditResult: QualityCheckAuditResult;
  reworkReason?: string;
  reworkRequirement?: string;
  auditRemark?: string;
}

// 统计信息接口
export interface QualityCheckStatistics {
  todayTotal: number;
  todayPassed: number;
  todayFailed: number;
  weekTotal: number;
  weekPassed: number;
  weekFailed: number;
  passRate: number;
  avgProcessTime: number;
  reworkRate: number;
}
```

### 3.2 阶段二：API模块重构

#### 步骤3：重构API模块
创建 `src/api/modules/afterSales/qualityCheck.ts`：

```typescript
import request from '@/api';
import type {
  QualityCheckListItem,
  QualityCheckSearchParams,
  QualityCheckDetail,
  QualityCheckSubmitForm,
  QualityCheckAuditForm,
  QualityCheckStatistics,
  PaginationResponse,
  ApiResponse
} from '@/types/afterSales/qualityCheck';
import {
  getMockQualityCheckList,
  getMockQualityCheckDetail,
  submitMockQualityCheck,
  auditMockQualityCheck,
  getMockQualityCheckStats,
  exportMockQualityCheckData
} from '@/mock/data/afterSales/qualityCheck';

// 临时使用Mock数据开关
let USE_MOCK_API_TEMP = true;

// 获取质检单列表
export const getQualityCheckList = (
  params: QualityCheckSearchParams
): Promise<PaginationResponse<QualityCheckListItem>> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQualityCheckList(params);
  }
  return request.get<any, PaginationResponse<QualityCheckListItem>>(
    '/after-sales/quality-check/list',
    { params }
  );
};

// 获取质检单详情
export const getQualityCheckDetail = (id: string): Promise<QualityCheckDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQualityCheckDetail(id);
  }
  return request.get<any, QualityCheckDetail>(`/after-sales/quality-check/detail/${id}`);
};

// 提交质检结果
export const submitQualityCheck = (data: QualityCheckSubmitForm): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return submitMockQualityCheck(data);
  }
  return request.post<any, ApiResponse>('/after-sales/quality-check/submit', data);
};

// 审核质检结果
export const auditQualityCheck = (data: QualityCheckAuditForm): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return auditMockQualityCheck(data);
  }
  return request.post<any, ApiResponse>('/after-sales/quality-check/audit', data);
};

// 获取质检统计
export const getQualityCheckStatistics = (): Promise<QualityCheckStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQualityCheckStats();
  }
  return request.get<any, QualityCheckStatistics>('/after-sales/quality-check/statistics');
};

// 导出质检数据
export const exportQualityCheckData = (params: QualityCheckSearchParams): Promise<Blob> => {
  if (USE_MOCK_API_TEMP) {
    return exportMockQualityCheckData(params);
  }
  return request.get<any, Blob>(
    '/after-sales/quality-check/export',
    { 
      params,
      responseType: 'blob'
    }
  );
};
```

### 3.3 阶段三：Mock数据重构

#### 步骤4：重构Mock数据
创建 `src/mock/data/afterSales/qualityCheck.ts`：

```typescript
import type {
  QualityCheckListItem,
  QualityCheckSearchParams,
  QualityCheckDetail,
  QualityCheckSubmitForm,
  QualityCheckAuditForm,
  QualityCheckStatistics,
  PaginationResponse,
  ApiResponse,
  QualityCheckStatus,
  WorkOrderType
} from '@/types/afterSales/qualityCheck';

// 动态生成质检Mock数据
function generateMockQualityCheckData(): QualityCheckListItem[] {
  const dataCount = Math.floor(Math.random() * 20) + 50; // 50-70条数据
  const mockData: QualityCheckListItem[] = [];
  
  const statuses: QualityCheckStatus[] = ['pending_check', 'checking', 'pending_review', 'passed', 'rework'];
  const workOrderTypes: WorkOrderType[] = ['maintenance', 'repair', 'insurance'];
  const vehicleModels = ['Model S', 'Model X', 'Model 3', 'Model Y', 'Cybertruck'];
  const colors = ['白色', '黑色', '红色', '蓝色', '银色'];
  const technicians = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅'];
  const customers = ['张三', '李四', '王五', '赵六', '陈七'];
  
  for (let i = 0; i < dataCount; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const workOrderType = workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)];
    
    mockData.push({
      id: `qc-${i + 1}`,
      qualityCheckNo: `QC${String(Date.now() + i).slice(-8)}`,
      status,
      workOrderNo: `WO${String(i + 1).padStart(6, '0')}`,
      workOrderType,
      isClaimRelated: Math.random() > 0.7,
      isOutsourceRelated: Math.random() > 0.8,
      serviceCustomerName: customers[Math.floor(Math.random() * customers.length)],
      serviceCustomerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      plateNumber: `粤B${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfig: Math.random() > 0.5 ? '长续航' : '标准续航',
      vehicleColor: colors[Math.floor(Math.random() * colors.length)],
      technicianName: technicians[Math.floor(Math.random() * technicians.length)],
      startTime: status !== 'pending_check' ? createTime.toISOString() : undefined,
      finishTime: (status === 'passed' || status === 'rework') ? 
        new Date(createTime.getTime() + Math.random() * 8 * 60 * 60 * 1000).toISOString() : undefined,
      estimatedHours: Math.round((Math.random() * 6 + 1) * 2) / 2,
      actualHours: status !== 'pending_check' ? Math.round((Math.random() * 6 + 1) * 2) / 2 : undefined,
      reworkReason: status === 'rework' ? '质检不合格，需要重新处理' : undefined,
      reworkRequirement: status === 'rework' ? '请检查制动系统相关项目' : undefined,
      createTime: createTime.toISOString(),
      updateTime: createTime.toISOString()
    });
  }
  
  return mockData;
}

const mockQualityCheckData = generateMockQualityCheckData();

export const getMockQualityCheckList = (
  params: QualityCheckSearchParams
): Promise<PaginationResponse<QualityCheckListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockQualityCheckData];
      
      if (params.workOrderNo) {
        filteredData = filteredData.filter(item => 
          item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
        );
      }
      
      if (params.qualityCheckNo) {
        filteredData = filteredData.filter(item => 
          item.qualityCheckNo.toLowerCase().includes(params.qualityCheckNo!.toLowerCase())
        );
      }
      
      if (params.status && params.status.length > 0) {
        filteredData = filteredData.filter(item => 
          params.status!.includes(item.status)
        );
      }
      
      if (params.workOrderType) {
        filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType);
      }
      
      if (params.technicianName) {
        filteredData = filteredData.filter(item => 
          item.technicianName.includes(params.technicianName!)
        );
      }
      
      if (params.plateNumber) {
        filteredData = filteredData.filter(item => 
          item.plateNumber.includes(params.plateNumber!)
        );
      }
      
      if (params.serviceCustomerName) {
        filteredData = filteredData.filter(item => 
          item.serviceCustomerName.includes(params.serviceCustomerName!)
        );
      }
      
      // 时间范围过滤
      if (params.createTimeStart) {
        filteredData = filteredData.filter(item => 
          item.createTime >= params.createTimeStart!
        );
      }
      
      if (params.createTimeEnd) {
        filteredData = filteredData.filter(item => 
          item.createTime <= params.createTimeEnd!
        );
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        data: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 500);
  });
};
```

## 4. 国际化集成

### 4.1 更新国际化文件
在 `src/locales/modules/afterSales/zh.json` 中添加qualityCheck模块：

```json
{
  "qualityCheck": {
    "title": "质量管理",
    "subtitle": "质检单管理与质量控制",
    "qualityCheckNo": "质检单编号",
    "workOrderNo": "工单编号",
    "status": "质检状态",
    "workOrderType": "工单类型",
    "technicianName": "技师",
    "plateNumber": "车牌号",
    "serviceCustomerName": "客户姓名",
    "vehicleModel": "车型",
    "createTime": "创建时间",
    "startTime": "开始时间",
    "finishTime": "完成时间",
    "estimatedHours": "预计工时",
    "actualHours": "实际工时",
    "isClaimRelated": "涉及索赔",
    "isOutsourceRelated": "涉及委外",
    "status": {
      "pending_check": "待质检",
      "checking": "质检中",
      "pending_review": "待审核",
      "passed": "质检通过",
      "rework": "返工"
    },
    "workOrderType": {
      "maintenance": "保养",
      "repair": "维修",
      "insurance": "保险"
    },
    "actions": {
      "submit": "提交质检",
      "audit": "审核",
      "rework": "返工",
      "detail": "详情",
      "edit": "编辑",
      "export": "导出"
    },
    "dialog": {
      "submitTitle": "提交质检结果",
      "auditTitle": "质检审核",
      "detailTitle": "质检详情",
      "editTitle": "编辑质检单"
    },
    "messages": {
      "submitSuccess": "质检结果提交成功",
      "auditSuccess": "质检审核完成",
      "exportSuccess": "数据导出成功"
    }
  }
}
```

### 4.2 更新页面国际化引用
```typescript
// 修改前
const { t } = useI18n();

// 修改后
const { t, tc } = useModuleI18n('afterSales');

// 使用方式
t('qualityCheck.title')        // 质检相关翻译
tc('search')                   // 通用翻译
```

## 5. 路由配置更新

### 5.1 更新路由路径
```typescript
// src/router/modules/afterSales.ts
{
  path: '/after-sales/quality-check',
  name: 'QualityCheckManagement',
  component: () => import('@/views/afterSales/qualityCheck/QualityCheckManagementView.vue'),
  meta: {
    title: 'menu.qualityCheckManagement',
    requiresAuth: true,
    icon: 'DocumentChecked'
  }
}
```

## 6. 重构检查清单

### 6.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/qualityCheck/QualityCheckManagementView.vue`
- [ ] 组件文件拆分并放置在 `components/` 目录下
- [ ] API模块移动到 `src/api/modules/afterSales/qualityCheck.ts`
- [ ] 类型定义统一到 `src/types/afterSales/qualityCheck.d.ts`
- [ ] Mock数据移动到 `src/mock/data/afterSales/qualityCheck.ts`
- [ ] 国际化文件合并到 `src/locales/modules/afterSales/`

### 6.2 代码质量验证
- [ ] 使用 `useModuleI18n('afterSales')` 替换 `useI18n()`
- [ ] 所有翻译键更新为 `t('qualityCheck.*')`
- [ ] TypeScript类型安全，无编译错误
- [ ] Mock数据支持动态生成和完整的搜索分页功能
- [ ] API模块与Mock数据完全集成

### 6.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 质检提交和审核功能正常
- [ ] 详情查看功能正常
- [ ] 国际化切换正常

### 6.4 路由验证
- [ ] 路由路径更新为 `/after-sales/quality-check`
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 7. 迁移注意事项

### 7.1 数据兼容性
- 确保新的类型定义与现有数据结构兼容
- Mock数据格式保持与原有API响应一致
- 分页参数和响应格式标准化

### 7.2 功能保持
- 保持所有现有业务功能不变
- 确保质检流程逻辑完整
- 维持用户体验一致性

### 7.3 性能优化
- 合理使用组件懒加载
- 优化大列表渲染性能
- 实现合理的缓存策略

## 8. 实施时间计划

### 8.1 第一周：基础结构搭建
- **Day 1-2**: 创建目录结构，统一类型定义
- **Day 3-4**: 重构API模块和Mock数据
- **Day 5**: 更新国际化文件和路由配置

### 8.2 第二周：组件拆分重构
- **Day 1-2**: 重构主页面组件，拆分搜索表单和表格组件
- **Day 3-4**: 重构对话框组件（详情、编辑、提交、审核）
- **Day 5**: 功能测试和bug修复

### 8.3 第三周：测试和优化
- **Day 1-2**: 全面功能测试和集成测试
- **Day 3-4**: 性能优化和代码审查
- **Day 5**: 文档更新和部署准备

## 9. 风险评估与应对

### 9.1 技术风险
**风险**: 组件拆分过程中状态管理复杂化
**应对**:
- 使用Props/Emits进行组件通信
- 合理使用provide/inject处理深层传递
- 保持组件职责单一

**风险**: Mock数据与真实API不匹配
**应对**:
- 严格按照现有API响应格式设计Mock数据
- 提供API切换开关，便于测试
- 与后端团队确认接口规范

### 9.2 业务风险
**风险**: 重构过程中影响现有质检流程
**应对**:
- 使用feature分支进行开发
- 保持原有功能完全不变
- 充分的回归测试

**风险**: 用户操作习惯改变
**应对**:
- 保持UI界面和交互逻辑不变
- 确保所有快捷键和操作习惯延续
- 提供平滑的迁移过渡

## 10. 成功标准

### 10.1 技术标准
- ✅ 符合项目目录结构规范
- ✅ 通过所有TypeScript类型检查
- ✅ 通过ESLint代码规范检查
- ✅ 国际化覆盖率100%
- ✅ Mock数据功能完整
- ✅ 组件拆分合理，复用性强

### 10.2 功能标准
- ✅ 所有原有功能正常工作
- ✅ 页面加载性能不降低
- ✅ 搜索和分页响应时间<500ms
- ✅ 支持中英文切换
- ✅ 兼容所有主流浏览器

### 10.3 维护标准
- ✅ 代码结构清晰，易于理解
- ✅ 组件复用性强，职责明确
- ✅ 文档完整，便于后续开发
- ✅ 测试覆盖率>80%

## 11. 后续优化建议

### 11.1 短期优化（1个月内）
- 实现质检流程的可视化展示
- 添加批量质检功能
- 优化质检项目模板管理
- 增加质检提醒和通知功能

### 11.2 中期优化（3个月内）
- 实现智能质检推荐
- 添加质检统计和报表功能
- 支持移动端质检操作
- 集成图片和视频质检证据

### 11.3 长期规划（6个月内）
- 实现AI辅助质检决策
- 添加质检效率分析
- 支持多门店质检协调
- 集成第三方检测设备

## 12. 组件拆分详细设计

### 12.1 主页面组件 (QualityCheckManagementView.vue)
**职责**: 页面布局、状态管理、组件协调
**大小**: 控制在200行以内
**主要功能**:
- 页面整体布局
- 搜索参数管理
- 分页状态管理
- 对话框状态控制

### 12.2 搜索表单组件 (QualityCheckSearchForm.vue)
**职责**: 搜索条件输入和验证
**Props**: searchParams, options
**Emits**: search, reset
**主要功能**:
- 多条件搜索表单
- 表单验证
- 重置功能

### 12.3 质检表格组件 (QualityCheckTable.vue)
**职责**: 数据展示和操作按钮
**Props**: data, loading, pagination
**Emits**: submit, audit, detail, page-change
**主要功能**:
- 质检单列表展示
- 排序和筛选
- 操作按钮

### 12.4 质检详情对话框组件 (QualityCheckDetailDialog.vue)
**职责**: 质检单详细信息展示
**Props**: visible, qualityCheckId
**Emits**: close
**主要功能**:
- 质检单详情展示
- 质检项目展示
- 历史记录

### 12.5 质检编辑对话框组件 (QualityCheckEditDialog.vue)
**职责**: 质检单信息编辑
**Props**: visible, qualityCheck
**Emits**: confirm, cancel
**主要功能**:
- 基本信息编辑
- 质检项目编辑
- 表单验证

### 12.6 质检提交对话框组件 (QualityCheckSubmitDialog.vue)
**职责**: 质检结果提交
**Props**: visible, qualityCheck
**Emits**: confirm, cancel
**主要功能**:
- 质检项目填写
- 结果提交
- 草稿保存

### 12.7 质检审核对话框组件 (QualityCheckAuditDialog.vue)
**职责**: 质检结果审核
**Props**: visible, qualityCheck
**Emits**: confirm, cancel
**主要功能**:
- 审核结果选择
- 返工原因填写
- 审核备注

### 12.8 统计卡片组件 (QualityCheckStatsCard.vue)
**职责**: 质检统计信息展示
**Props**: statistics
**主要功能**:
- 今日统计
- 本周统计
- 通过率展示

### 12.9 质检项目表单组件 (QualityCheckItemForm.vue)
**职责**: 质检项目表单
**Props**: items, readonly
**Emits**: change
**主要功能**:
- 动态表单生成
- 不同类型项目处理
- 验证规则

## 13. 数据流设计

### 13.1 状态管理
```typescript
// 主页面状态
interface QualityCheckState {
  searchParams: QualityCheckSearchParams;
  qualityCheckList: QualityCheckListItem[];
  statistics: QualityCheckStatistics;
  loading: boolean;
  selectedQualityCheck: QualityCheckListItem | null;
}
```

### 13.2 事件流
```
用户操作 → 组件事件 → 主页面处理 → API调用 → 状态更新 → 组件重渲染
```

### 13.3 数据传递
- **父→子**: Props传递数据
- **子→父**: Emits传递事件
- **跨组件**: provide/inject或状态管理

## 14. 性能优化策略

### 14.1 组件优化
- 使用 `v-memo` 优化列表渲染
- 合理使用 `computed` 缓存计算结果
- 组件懒加载减少初始包大小

### 14.2 数据优化
- 虚拟滚动处理大数据量
- 分页加载减少单次数据量
- 缓存常用数据减少请求

### 14.3 交互优化
- 防抖处理搜索输入
- 骨架屏提升加载体验
- 乐观更新提升响应速度

---

**本重构方案基于DMS前端项目的标准化规范制定，确保质量管理模块在保持现有功能完整性的基础上，实现代码结构的现代化和可维护性的显著提升。通过系统性的重构，该模块将更好地融入整体项目架构，为后续功能扩展和维护奠定坚实基础。**
