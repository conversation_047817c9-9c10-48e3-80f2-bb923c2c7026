<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vehicle Model Master Data Management - Perodua DMS System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1890ff;
            --primary-hover: #40a9ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #f5222d;
            --bg-white: #ffffff;
            --bg-light: #fafafa;
            --border-color: #e8e8e8;
            --text-primary: #333333;
            --text-secondary: #666666;
        }

        body {
            font-size: 13px;
            color: var(--text-primary);
            background-color: var(--bg-light);
            margin: 0;
            padding: 0;
            display: flex;
        }

        /* 左侧菜单栏样式 */
        .sidebar {
            width: 180px;
            background-color: #1f2937;
            color: #ffffff;
            min-height: 100vh;
            padding: 0;
            display: flex;
            flex-direction: column;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #374151;
            background-color: #111827;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .sidebar-nav {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: calc(100vh - 80px);
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .sidebar-nav::-webkit-scrollbar {
            display: none;
        }

        .nav-group {
            margin-bottom: 20px;
        }

        .nav-group-title {
            padding: 8px 16px;
            font-size: 10px;
            font-weight: 600;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 6px;
        }

        .nav-item {
            display: block;
            padding: 10px 16px;
            color: #d1d5db;
            text-decoration: none;
            transition: all 0.15s ease;
            border-left: 3px solid transparent;
            font-size: 13px;
        }

        .nav-item:hover {
            background-color: #374151;
            color: #ffffff;
            text-decoration: none;
        }

        .nav-item.active {
            background-color: #1f3a8a;
            color: #ffffff;
            border-left-color: #3b82f6;
            font-weight: 500;
        }

        .nav-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 180px;
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .page-container {
            padding: 20px;
            background-color: var(--bg-light);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .page-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            color: var(--text-primary);
            flex-shrink: 0;
        }

        /* 语言切换器 */
        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            display: flex;
            gap: 4px;
        }

        .language-switcher .btn {
            padding: 4px 12px;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid #d1d5db;
            background-color: #ffffff;
            color: #6b7280;
            transition: all 0.15s ease;
        }

        .language-switcher .btn.active {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .language-switcher .btn:hover:not(.active) {
            background-color: #f9fafb;
            border-color: #9ca3af;
        }

        /* 筛选区域 */
        .filter-section {
            background-color: #ffffff;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            flex-shrink: 0;
        }

        .form-label {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
            font-weight: 500;
        }

        .form-control, .form-select {
            font-size: 13px;
            border-color: var(--border-color);
            border-radius: 4px;
            padding: 6px 12px;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(24, 144, 255, 0.25);
        }

        /* 功能按钮区域 */
        .action-section {
            background-color: var(--bg-white);
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn {
            font-size: 13px;
            padding: 6px 16px;
            border-radius: 4px;
            font-weight: 400;
            transition: all 0.2s ease;
        }

        .btn:active {
            transform: scale(0.98);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--bg-light);
            border-color: var(--text-secondary);
            color: var(--text-secondary);
        }

        /* 同步进度条 */
        .sync-progress {
            display: none;
            width: 200px;
        }

        .sync-progress .progress {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .sync-progress .progress-bar {
            background-color: var(--primary-color);
        }

        /* 表格容器 */
        .table-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
            flex: 1;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            table-layout: fixed;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 0;
            white-space: nowrap;
            min-width: 1200px;
        }

        .table th {
            background-color: var(--bg-light);
            color: var(--text-secondary);
            font-weight: 500;
            border-bottom: 1px solid var(--border-color);
            padding: 12px 8px;
            vertical-align: middle;
            font-size: 13px;
        }

        .table td {
            padding: 12px 8px;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
            font-size: 13px;
        }

        .table tbody tr:hover {
            background-color: #e6f7ff;
        }

        /* 列宽定义 */
        .col-seq { width: 60px; }
        .col-model { width: 100px; }
        .col-variant-name { width: 140px; }
        .col-variant-code { width: 110px; }
        .col-colour-name { width: 140px; }
        .col-colour-code { width: 110px; }
        .col-fmrid { width: 90px; }
        .col-create-time { width: 160px; }
        .col-update-time { width: 160px; }

        /* 模态框样式 */
        .modal-header {
            background-color: var(--bg-light);
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .modal-body {
            padding: 20px;
        }

        /* 状态标签 */
        .tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .tag-success {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .tag-error {
            background-color: #fff2f0;
            color: #f5222d;
            border: 1px solid #ffccc7;
        }

        .tag-warning {
            background-color: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 16px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .sidebar {
                width: 60px;
            }

            .main-content {
                margin-left: 60px;
            }

            .sidebar-header h3 {
                display: none;
            }

            .nav-item span {
                display: none;
            }

            .nav-group-title {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 左侧菜单栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>
                <i class="bi bi-speedometer2 me-2"></i>
                <span data-i18n="system.title">Perodua DMS System</span>
            </h3>
        </div>
        <nav class="sidebar-nav">
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.salesManagement">Sales Management</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-people"></i>
                    <span data-i18n="menu.prospectManagement">Prospect Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-key"></i>
                    <span data-i18n="menu.testDriveManagement">Test Drive Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-clipboard-check"></i>
                    <span data-i18n="menu.orderManagement">Sales Order Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-truck"></i>
                    <span data-i18n="menu.deliveryManagement">Delivery Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-receipt"></i>
                    <span data-i18n="menu.invoiceManagement">Invoice Management</span>
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.vehicleAllocation">Vehicle Allocation & Production</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-car-front"></i>
                    <span data-i18n="menu.vehicleAllocationManagement">Vehicle Allocation Management</span>
                </a>
                <a href="#" class="nav-item active">
                    <i class="bi bi-database"></i>
                    <span data-i18n="menu.vehicleModelMasterData">Vehicle Model Master Data</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-search"></i>
                    <span data-i18n="menu.vehicleInventoryQuery">Vehicle Inventory Query</span>
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.afterSalesManagement">After-sales Management</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-calendar-check"></i>
                    <span data-i18n="menu.appointmentManagement">Appointment Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-clipboard-data"></i>
                    <span data-i18n="menu.workOrderManagement">Work Order Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-tools"></i>
                    <span data-i18n="menu.jobAssignmentManagement">Job Assignment Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-check-circle"></i>
                    <span data-i18n="menu.qualityInspectionManagement">Quality Inspection Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-calculator"></i>
                    <span data-i18n="menu.settlementManagement">Settlement Management</span>
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.partsManagement">Parts Management</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-box"></i>
                    <span data-i18n="menu.inventoryManagement">Inventory Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-box-arrow-in-down"></i>
                    <span data-i18n="menu.inboundManagement">Inbound Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-box-arrow-up"></i>
                    <span data-i18n="menu.outboundManagement">Outbound Management</span>
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title" data-i18n="menu.systemManagement">System Management</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-person-gear"></i>
                    <span data-i18n="menu.userManagement">User Management</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-gear"></i>
                    <span data-i18n="menu.systemSettings">System Settings</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="page-container">
            <!-- 语言切换器 -->
            <div class="language-switcher">
                <button type="button" class="btn active" id="btn-zh" onclick="switchLanguage('zh')">中文</button>
                <button type="button" class="btn" id="btn-en" onclick="switchLanguage('en')">English</button>
            </div>

            <!-- 页面标题 -->
            <h1 class="page-title" data-i18n="page.title">车辆车型主数据管理</h1>

            <!-- 筛选区 -->
            <div class="filter-section">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.model">Model</label>
                        <select class="form-select" id="modelFilter">
                            <option value="" data-i18n="filter.all">全部</option>
                            <option value="AXIA">AXIA</option>
                            <option value="BEZZA">BEZZA</option>
                            <option value="MYVI">MYVI</option>
                            <option value="ALZA">ALZA</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.variantName">Variant Name</label>
                        <select class="form-select" id="variantNameFilter">
                            <option value="" data-i18n="filter.all">全部</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.colourName">Colour Name</label>
                        <select class="form-select" id="colourNameFilter">
                            <option value="" data-i18n="filter.all">全部</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label" data-i18n="filter.fmrid">FMRID</label>
                        <input type="text" class="form-control" id="fmridFilter" placeholder="请输入FMRID编码" data-i18n-placeholder="filter.fmrid.placeholder">
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-primary me-2" id="searchBtn">
                            <i class="fas fa-search me-1"></i>
                            <span data-i18n="button.search">查询</span>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                            <i class="fas fa-undo me-1"></i>
                            <span data-i18n="button.reset">重置</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 功能按钮区 -->
            <div class="action-section">
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary" id="syncDataBtn">
                        <i class="fas fa-sync-alt me-1"></i>
                        <span data-i18n="button.syncData">同步数据</span>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="syncLogBtn">
                        <i class="fas fa-history me-1"></i>
                        <span data-i18n="button.syncLog">同步日志</span>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="exportBtn">
                        <i class="fas fa-download me-1"></i>
                        <span data-i18n="button.export">导出数据</span>
                    </button>
                </div>
                <div class="sync-progress" id="syncProgress">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted mt-1" data-i18n="sync.progress">同步中...</small>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="col-seq" data-i18n="table.seq">序号</th>
                                <th class="col-model" data-i18n="table.model">Model</th>
                                <th class="col-variant-name" data-i18n="table.variantName">Variant Name</th>
                                <th class="col-variant-code" data-i18n="table.variantCode">Variant Code</th>
                                <th class="col-colour-name" data-i18n="table.colourName">Colour Name</th>
                                <th class="col-colour-code" data-i18n="table.colourCode">Colour Code</th>
                                <th class="col-fmrid" data-i18n="table.fmrid">FMRID</th>
                                <th class="col-create-time" data-i18n="table.createTime">创建时间</th>
                                <th class="col-update-time" data-i18n="table.updateTime">更新时间</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <!-- 测试数据 -->
                            <tr>
                                <td>1</td>
                                <td>MYVI</td>
                                <td>1.5L H CVT</td>
                                <td>BD5HZ</td>
                                <td>GRANITE GREY</td>
                                <td>S43(M)</td>
                                <td>C6505</td>
                                <td>2024-01-10 10:30:00</td>
                                <td>2024-01-15 14:30:25</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>MYVI</td>
                                <td>1.5L H CVT</td>
                                <td>BD5HZ</td>
                                <td>ELECTRIC BLUE</td>
                                <td>B77(M)</td>
                                <td>C6506</td>
                                <td>2024-01-10 10:30:00</td>
                                <td>2024-01-15 14:30:25</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>MYVI</td>
                                <td>1.5L H CVT</td>
                                <td>BD5HZ</td>
                                <td>CRANBERRY RED</td>
                                <td>R76(M)</td>
                                <td>C7217</td>
                                <td>2024-01-10 10:30:00</td>
                                <td>2024-01-15 14:30:25</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>MYVI</td>
                                <td>1.5L AV CVT</td>
                                <td>BD5VZ</td>
                                <td>GRANITE GREY</td>
                                <td>S43(M)</td>
                                <td>C6510</td>
                                <td>2024-01-10 10:30:00</td>
                                <td>2024-01-15 14:30:25</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>MYVI</td>
                                <td>1.5L AV CVT</td>
                                <td>BD5VZ</td>
                                <td>ELECTRIC BLUE</td>
                                <td>B77(M)</td>
                                <td>C6511</td>
                                <td>2024-01-10 10:30:00</td>
                                <td>2024-01-15 14:30:25</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>MYVI</td>
                                <td>1.5L AV CVT</td>
                                <td>BD5VZ</td>
                                <td>CRANBERRY RED</td>
                                <td>R76(M)</td>
                                <td>C7219</td>
                                <td>2024-01-10 10:30:00</td>
                                <td>2024-01-15 14:30:25</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 同步日志弹窗 -->
    <div class="modal fade" id="syncLogModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-i18n="modal.syncLog.title">数据同步日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3 mb-3">
                        <div class="col-md-4">
                            <label class="form-label" data-i18n="modal.syncLog.timeRange">同步时间范围</label>
                            <input type="date" class="form-control" id="syncTimeStart">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <input type="date" class="form-control" id="syncTimeEnd">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label" data-i18n="modal.syncLog.status">同步状态</label>
                            <select class="form-select" id="syncStatusFilter">
                                <option value="" data-i18n="filter.all">全部</option>
                                <option value="success" data-i18n="status.success">成功</option>
                                <option value="failed" data-i18n="status.failed">失败</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-12 text-end">
                            <button type="button" class="btn btn-primary me-2" id="syncLogSearchBtn">
                                <i class="fas fa-search me-1"></i>
                                <span data-i18n="button.search">查询</span>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="syncLogResetBtn">
                                <i class="fas fa-undo me-1"></i>
                                <span data-i18n="button.reset">重置</span>
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 180px;" data-i18n="modal.syncLog.syncTime">同步时间</th>
                                    <th style="width: 120px;" data-i18n="modal.syncLog.syncType">同步类型</th>
                                    <th style="width: 100px;" data-i18n="modal.syncLog.syncStatus">同步状态</th>
                                    <th style="width: 120px;" data-i18n="modal.syncLog.successCount">成功记录数</th>
                                    <th style="width: 120px;" data-i18n="modal.syncLog.failedCount">失败记录数</th>
                                    <th style="width: 200px;" data-i18n="modal.syncLog.errorMessage">错误信息</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-15 02:00:00</td>
                                    <td data-i18n="syncType.scheduled">定时同步</td>
                                    <td><span class="tag tag-success" data-i18n="status.success">成功</span></td>
                                    <td class="text-center">156</td>
                                    <td class="text-center">0</td>
                                    <td>-</td>
                                </tr>
                                <tr>
                                    <td>2024-01-14 02:00:00</td>
                                    <td data-i18n="syncType.scheduled">定时同步</td>
                                    <td><span class="tag tag-error" data-i18n="status.failed">失败</span></td>
                                    <td class="text-center">0</td>
                                    <td class="text-center">156</td>
                                    <td>ERP系统连接超时</td>
                                </tr>
                                <tr>
                                    <td>2024-01-13 15:30:00</td>
                                    <td data-i18n="syncType.manual">手动同步</td>
                                    <td><span class="tag tag-success" data-i18n="status.success">成功</span></td>
                                    <td class="text-center">89</td>
                                    <td class="text-center">0</td>
                                    <td>-</td>
                                </tr>
                                <tr>
                                    <td>2024-01-12 02:00:00</td>
                                    <td data-i18n="syncType.scheduled">定时同步</td>
                                    <td><span class="tag tag-success" data-i18n="status.success">成功</span></td>
                                    <td class="text-center">142</td>
                                    <td class="text-center">0</td>
                                    <td>-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 国际化配置
        const i18nData = {
            zh: {
                'system.title': 'Perodua DMS 系统',
                'page.title': '车辆车型主数据管理',
                'menu.salesManagement': '销售管理',
                'menu.prospectManagement': '潜客管理',
                'menu.testDriveManagement': '试驾管理',
                'menu.orderManagement': '销售订单管理',
                'menu.deliveryManagement': '交车管理',
                'menu.invoiceManagement': '发票管理',
                'menu.vehicleAllocation': '车辆配车与生产',
                'menu.vehicleAllocationManagement': '车辆配车管理',
                'menu.vehicleModelMasterData': '车辆车型主数据管理',
                'menu.vehicleInventoryQuery': '车辆库存查询',
                'menu.afterSalesManagement': '售后管理',
                'menu.appointmentManagement': '预约管理',
                'menu.workOrderManagement': '工单管理',
                'menu.jobAssignmentManagement': '派工管理',
                'menu.qualityInspectionManagement': '质检管理',
                'menu.settlementManagement': '结算管理',
                'menu.partsManagement': '零配件管理',
                'menu.inventoryManagement': '库存管理',
                'menu.inboundManagement': '入库管理',
                'menu.outboundManagement': '出库管理',
                'menu.systemManagement': '系统管理',
                'menu.userManagement': '用户管理',
                'menu.systemSettings': '系统设置',
                'button.syncData': '同步数据',
                'button.syncLog': '同步日志',
                'button.export': '导出数据',
                'button.search': '查询',
                'button.reset': '重置',
                'button.viewDetail': '查看详情',
                'filter.all': '全部',
                'filter.model': 'Model',
                'filter.variantName': 'Variant Name',
                'filter.colourName': 'Colour Name',
                'filter.fmrid': 'FMRID',
                'filter.fmrid.placeholder': '请输入FMRID编码',
                'table.seq': '序号',
                'table.model': 'Model',
                'table.variantName': 'Variant Name',
                'table.variantCode': 'Variant Code',
                'table.colourName': 'Colour Name',
                'table.colourCode': 'Colour Code',
                'table.fmrid': 'FMRID',
                'table.createTime': '创建时间',
                'table.updateTime': '更新时间',
                'table.action': '操作',
                'sync.progress': '同步中...',
                'sync.completed': '数据同步完成！',
                'sync.syncing': '同步中...',
                'export.developing': '导出功能开发中...',
                'status.success': '成功',
                'status.failed': '失败',
                'status.partial': '部分成功',
                'syncType.scheduled': '定时同步',
                'syncType.manual': '手动同步',
                'syncType.compensation': '补偿同步',
                'modal.syncLog.title': '数据同步日志',
                'modal.syncLog.timeRange': '同步时间范围',
                'modal.syncLog.status': '同步状态',
                'modal.syncLog.syncTime': '同步时间',
                'modal.syncLog.syncType': '同步类型',
                'modal.syncLog.syncStatus': '同步状态',
                'modal.syncLog.successCount': '成功记录数',
                'modal.syncLog.failedCount': '失败记录数',
                'modal.syncLog.errorMessage': '错误信息'
            },
            en: {
                'system.title': 'Perodua DMS System',
                'page.title': 'Vehicle Model Master Data Management',
                'menu.salesManagement': 'Sales Management',
                'menu.prospectManagement': 'Prospect Management',
                'menu.testDriveManagement': 'Test Drive Management',
                'menu.orderManagement': 'Sales Order Management',
                'menu.deliveryManagement': 'Delivery Management',
                'menu.invoiceManagement': 'Invoice Management',
                'menu.vehicleAllocation': 'Vehicle Allocation & Production',
                'menu.vehicleAllocationManagement': 'Vehicle Allocation Management',
                'menu.vehicleModelMasterData': 'Vehicle Model Master Data',
                'menu.vehicleInventoryQuery': 'Vehicle Inventory Query',
                'menu.afterSalesManagement': 'After-sales Management',
                'menu.appointmentManagement': 'Appointment Management',
                'menu.workOrderManagement': 'Work Order Management',
                'menu.jobAssignmentManagement': 'Job Assignment Management',
                'menu.qualityInspectionManagement': 'Quality Inspection Management',
                'menu.settlementManagement': 'Settlement Management',
                'menu.partsManagement': 'Parts Management',
                'menu.inventoryManagement': 'Inventory Management',
                'menu.inboundManagement': 'Inbound Management',
                'menu.outboundManagement': 'Outbound Management',
                'menu.systemManagement': 'System Management',
                'menu.userManagement': 'User Management',
                'menu.systemSettings': 'System Settings',
                'button.syncData': 'Sync Data',
                'button.syncLog': 'Sync Log',
                'button.export': 'Export Data',
                'button.search': 'Search',
                'button.reset': 'Reset',
                'button.viewDetail': 'View Detail',
                'filter.all': 'All',
                'filter.model': 'Model',
                'filter.variantName': 'Variant Name',
                'filter.colourName': 'Colour Name',
                'filter.fmrid': 'FMRID',
                'filter.fmrid.placeholder': 'Enter FMRID Code',
                'table.seq': 'No.',
                'table.model': 'Model',
                'table.variantName': 'Variant Name',
                'table.variantCode': 'Variant Code',
                'table.colourName': 'Colour Name',
                'table.colourCode': 'Colour Code',
                'table.fmrid': 'FMRID',
                'table.createTime': 'Create Time',
                'table.updateTime': 'Update Time',
                'table.action': 'Action',
                'sync.progress': 'Syncing...',
                'sync.completed': 'Data synchronization completed!',
                'sync.syncing': 'Syncing...',
                'export.developing': 'Export function is under development...',
                'status.success': 'Success',
                'status.failed': 'Failed',
                'syncType.scheduled': 'Scheduled',
                'syncType.manual': 'Manual',
                'syncType.compensation': 'Compensation',
                'modal.syncLog.title': 'Data Sync Log',
                'modal.syncLog.timeRange': 'Sync Time Range',
                'modal.syncLog.status': 'Sync Status',
                'modal.syncLog.syncTime': 'Sync Time',
                'modal.syncLog.syncType': 'Sync Type',
                'modal.syncLog.syncStatus': 'Sync Status',
                'modal.syncLog.successCount': 'Success Count',
                'modal.syncLog.failedCount': 'Failed Count',
                'modal.syncLog.errorMessage': 'Error Message'
            }
        };

        // 当前语言
        let currentLanguage = 'zh';

        // Model-Variant-Colour联动数据
        const vehicleData = {
            'AXIA': {
                variants: {
                    '1000X': ['GRANITE GREY', 'ELECTRIC BLUE', 'CRANBERRY RED'],
                    '1300G': ['GRANITE GREY', 'ELECTRIC BLUE', 'IVORY WHITE']
                }
            },
            'BEZZA': {
                variants: {
                    '1300G': ['GRANITE GREY', 'ELECTRIC BLUE', 'CRANBERRY RED'],
                    '1500H': ['GRANITE GREY', 'IVORY WHITE', 'OCEAN BLUE']
                }
            },
            'MYVI': {
                variants: {
                    '1.5L H CVT': ['GRANITE GREY', 'ELECTRIC BLUE', 'CRANBERRY RED'],
                    '1.5L AV CVT': ['GRANITE GREY', 'ELECTRIC BLUE', 'IVORY WHITE', 'OCEAN BLUE']
                }
            },
            'ALZA': {
                variants: {
                    '1500X': ['GRANITE GREY', 'ELECTRIC BLUE', 'CRANBERRY RED', 'IVORY WHITE'],
                    '1500G': ['GRANITE GREY', 'ELECTRIC BLUE', 'IVORY WHITE']
                }
            }
        };

        // 切换语言
        function switchLanguage(lang) {
            currentLanguage = lang;
            
            // 更新语言按钮状态
            document.querySelectorAll('.language-switcher .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`btn-${lang}`).classList.add('active');
            
            // 更新页面文本
            updatePageText();
            
            // 保存语言偏好
            localStorage.setItem('language', lang);
        }

        // 获取翻译文本
        function t(key) {
            return i18nData[currentLanguage][key] || key;
        }

        // 更新页面文本
        function updatePageText() {
            // 更新所有带有 data-i18n 属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                const translation = t(key);
                element.textContent = translation;
            });
            
            // 更新所有带有 data-i18n-placeholder 属性的元素
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                element.placeholder = t(key);
            });
            
            document.documentElement.lang = currentLanguage === 'zh' ? 'zh-CN' : 'en';
            document.title = t('page.title') + ' - Perodua DMS System';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化语言
            const savedLang = localStorage.getItem('language') || 'zh';
            switchLanguage(savedLang);

            // Model-Variant-Colour三级联动
            document.getElementById('modelFilter').addEventListener('change', function() {
                const variantSelect = document.getElementById('variantNameFilter');
                const colourSelect = document.getElementById('colourNameFilter');
                const selectedModel = this.value;
                const data = i18nData[currentLanguage];
                
                // 清空Variant和Colour选项
                variantSelect.innerHTML = `<option value="">${data['filter.all']}</option>`;
                colourSelect.innerHTML = `<option value="">${data['filter.all']}</option>`;
                
                if (selectedModel && vehicleData[selectedModel]) {
                    const variants = Object.keys(vehicleData[selectedModel].variants);
                    variants.forEach(variant => {
                        const option = document.createElement('option');
                        option.value = variant;
                        option.textContent = variant;
                        variantSelect.appendChild(option);
                    });
                }
            });

            // Variant-Colour二级联动
            document.getElementById('variantNameFilter').addEventListener('change', function() {
                const modelSelect = document.getElementById('modelFilter');
                const colourSelect = document.getElementById('colourNameFilter');
                const selectedModel = modelSelect.value;
                const selectedVariant = this.value;
                const data = i18nData[currentLanguage];
                
                // 清空Colour选项
                colourSelect.innerHTML = `<option value="">${data['filter.all']}</option>`;
                
                if (selectedModel && selectedVariant && vehicleData[selectedModel] && vehicleData[selectedModel].variants[selectedVariant]) {
                    const colours = vehicleData[selectedModel].variants[selectedVariant];
                    colours.forEach(colour => {
                        const option = document.createElement('option');
                        option.value = colour;
                        option.textContent = colour;
                        colourSelect.appendChild(option);
                    });
                }
            });

            // 同步数据按钮
            document.getElementById('syncDataBtn').addEventListener('click', function() {
                const btn = this;
                const progress = document.getElementById('syncProgress');
                const progressBar = progress.querySelector('.progress-bar');
                const data = i18nData[currentLanguage];
                
                btn.disabled = true;
                btn.innerHTML = `<i class="fas fa-spinner fa-spin me-1"></i>${data['sync.syncing']}`;
                progress.style.display = 'block';
                
                let width = 0;
                const interval = setInterval(() => {
                    width += Math.random() * 30;
                    if (width > 100) width = 100;
                    progressBar.style.width = width + '%';
                    
                    if (width >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            btn.disabled = false;
                            btn.innerHTML = `<i class="fas fa-sync-alt me-1"></i>${data['button.syncData']}`;
                            progress.style.display = 'none';
                            progressBar.style.width = '0%';
                            alert(data['sync.completed']);
                        }, 500);
                    }
                }, 200);
            });

            // 同步日志按钮
            document.getElementById('syncLogBtn').addEventListener('click', function() {
                new bootstrap.Modal(document.getElementById('syncLogModal')).show();
            });

            // 导出按钮
            document.getElementById('exportBtn').addEventListener('click', function() {
                const data = i18nData[currentLanguage];
                alert(data['export.developing']);
            });

            // 重置按钮
            document.getElementById('resetBtn').addEventListener('click', function() {
                document.getElementById('modelFilter').value = '';
                document.getElementById('variantNameFilter').innerHTML = '<option value="">全部</option>';
                document.getElementById('colourNameFilter').value = '';
                document.getElementById('fmridFilter').value = '';
                
                // 更新重置后的选项文本
                const data = i18nData[currentLanguage];
                document.getElementById('variantNameFilter').innerHTML = `<option value="">${data['filter.all']}</option>`;
            });

            // 查询按钮
            document.getElementById('searchBtn').addEventListener('click', function() {
                // 查询功能实现
                console.log('执行查询操作');
            });
        });
    </script>
</body>
</html>
