# 车间派工重构技术方案

基于页面目录结构规范和页面重构技术规范，对 `src/views/dispatch/DispatchManagementView.vue` 模块进行重构的完整技术方案。

## 1. 现状分析

### 1.1 当前目录结构
```
src/views/dispatch/
├── DispatchManagementView.vue          # 主页面文件（单文件组件）
└── DispatchManagementView.vue.bak      # 备份文件
```

### 1.2 相关文件分布
```
src/api/modules/workAssignment.ts       # 派工API模块（已存在）
src/types/module.d.ts                   # 类型定义（分散在通用文件中）
src/mock/data/workAssignment.ts         # Mock数据（已存在）
src/locales/modules/                    # 国际化文件（需要创建dispatch模块）
```

### 1.3 问题识别
- ❌ 页面位置不符合规范：应该在 `afterSales` 模块下
- ❌ 使用 `useI18n()` 而非 `useModuleI18n()`
- ❌ 单文件组件过于庞大（860行），缺乏组件化拆分
- ❌ 类型定义分散在 `module.d.ts` 中
- ❌ 缺乏专门的国际化模块
- ❌ Mock数据与API模块集成度不高

## 2. 重构目标结构

### 2.1 目标目录结构
```
src/
├── views/afterSales/dispatch/
│   ├── DispatchManagementView.vue      # 主页面（路由页面）
│   └── components/
│       ├── DispatchSearchForm.vue     # 搜索表单组件
│       ├── DispatchTable.vue          # 派工表格组件
│       ├── AssignmentDialog.vue       # 分配工单对话框
│       ├── ReassignmentDialog.vue     # 重新分配对话框
│       ├── WorkOrderDetailDialog.vue  # 工单详情对话框
│       └── TechnicianStatusCard.vue   # 技师状态卡片组件
├── api/modules/afterSales/
│   └── dispatch.ts                    # 重构后的API模块
├── types/afterSales/
│   └── dispatch.d.ts                  # 统一的类型定义
├── mock/data/afterSales/
│   └── dispatch.ts                    # 重构后的Mock数据
└── locales/modules/afterSales/
    ├── zh.json                         # 中文翻译（包含dispatch）
    └── en.json                         # 英文翻译（包含dispatch）
```

## 3. 重构执行计划

### 3.1 阶段一：基础结构创建

#### 步骤1：创建目录结构
```bash
# 创建新的目录结构
mkdir -p src/views/afterSales/dispatch/components
mkdir -p src/api/modules/afterSales
mkdir -p src/types/afterSales
mkdir -p src/mock/data/afterSales
```

#### 步骤2：统一类型定义
将分散的类型定义统一到 `src/types/afterSales/dispatch.d.ts`：

```typescript
// 基础类型定义
export type WorkOrderPriority = 'urgent' | 'normal';
export type WorkOrderType = 'repair' | 'maintenance' | 'insurance';
export type WorkOrderStatus = 'pendingAssignment' | 'pendingStart' | 'inProgress' | 'paused' | 'completed' | 'cancelled';
export type AssignmentStatus = 'pendingAssignment' | 'assigned';
export type CustomerSource = 'appointment' | 'walkIn';

// 工单列表项接口
export interface DispatchListItem {
  id: number;
  workOrderNo: string;
  priority: WorkOrderPriority;
  workOrderType: WorkOrderType;
  workOrderStatus: WorkOrderStatus;
  creationTime: string;
  customerSource: CustomerSource;
  repairmanName: string;
  repairmanPhone: string;
  licensePlateNumber: string;
  vehicleModel: string;
  configuration: string;
  color: string;
  vehicleAge: number;
  mileage: number;
  serviceAdvisor: string;
  assignmentStatus: AssignmentStatus;
  technician: string;
  estimatedWorkHours: number;
  estimatedStartTime: string;
  estimatedFinishTime: string;
  actualStartTime: string;
  actualFinishTime: string;
  isPaused: boolean;
}

// 搜索参数接口
export interface DispatchSearchParams {
  page: number;
  pageSize: number;
  workOrderNo?: string;
  priority?: WorkOrderPriority;
  workOrderType?: WorkOrderType;
  assignmentStatus?: AssignmentStatus;
  workOrderStatus?: WorkOrderStatus;
  creationTimeStart?: string;
  creationTimeEnd?: string;
  customerSource?: CustomerSource;
  repairmanName?: string;
  licensePlateNumber?: string;
  serviceAdvisor?: string;
  technician?: string;
}

// 技师信息接口
export interface TechnicianInfo {
  id: string;
  name: string;
  department: string;
  status: 'available' | 'busy' | 'offline';
  currentWorkload: number;
  maxWorkload: number;
  skills: string[];
  workingHours: {
    start: string;
    end: string;
  };
}

// 分配表单接口
export interface AssignmentFormData {
  workOrderNo: string;
  technicianId: string;
  estimatedStartTime: string;
  estimatedFinishTime: string;
  notes: string;
}

// 重新分配表单接口
export interface ReassignmentFormData {
  workOrderNo: string;
  originalTechnician: string;
  newTechnicianId: string;
  estimatedStartTime: string;
  estimatedFinishTime: string;
  reason: string;
}
```

### 3.2 阶段二：API模块重构

#### 步骤3：重构API模块
创建 `src/api/modules/afterSales/dispatch.ts`：

```typescript
import request from '@/api';
import type {
  DispatchListItem,
  DispatchSearchParams,
  TechnicianInfo,
  AssignmentFormData,
  ReassignmentFormData,
  PaginationResponse
} from '@/types/afterSales/dispatch';
import {
  getMockDispatchList,
  getMockTechnicianList,
  submitMockAssignment,
  submitMockReassignment
} from '@/mock/data/afterSales/dispatch';
import { USE_MOCK_API } from '@/utils/mock-config';

// 获取派工列表
export const getDispatchList = (
  params: DispatchSearchParams
): Promise<PaginationResponse<DispatchListItem>> => {
  if (USE_MOCK_API) {
    return getMockDispatchList(params);
  }
  return request.get<any, PaginationResponse<DispatchListItem>>(
    '/after-sales/dispatch/list',
    { params }
  );
};

// 获取技师列表
export const getTechnicianList = (): Promise<TechnicianInfo[]> => {
  if (USE_MOCK_API) {
    return getMockTechnicianList();
  }
  return request.get<any, TechnicianInfo[]>('/after-sales/technicians');
};

// 分配工单
export const assignWorkOrder = (data: AssignmentFormData): Promise<any> => {
  if (USE_MOCK_API) {
    return submitMockAssignment(data);
  }
  return request.post('/after-sales/dispatch/assign', data);
};

// 重新分配工单
export const reassignWorkOrder = (data: ReassignmentFormData): Promise<any> => {
  if (USE_MOCK_API) {
    return submitMockReassignment(data);
  }
  return request.post('/after-sales/dispatch/reassign', data);
};
```

### 3.3 阶段三：Mock数据重构

#### 步骤4：重构Mock数据
创建 `src/mock/data/afterSales/dispatch.ts`：

```typescript
import type {
  DispatchListItem,
  DispatchSearchParams,
  TechnicianInfo,
  AssignmentFormData,
  ReassignmentFormData,
  PaginationResponse
} from '@/types/afterSales/dispatch';

// 动态生成派工Mock数据
function generateMockDispatchData(): DispatchListItem[] {
  const dataCount = Math.floor(Math.random() * 10) + 30; // 30-40条数据
  const mockData: DispatchListItem[] = [];
  
  const priorities: WorkOrderPriority[] = ['urgent', 'normal'];
  const types: WorkOrderType[] = ['repair', 'maintenance', 'insurance'];
  const statuses: WorkOrderStatus[] = ['pendingAssignment', 'pendingStart', 'inProgress', 'paused', 'completed'];
  const sources: CustomerSource[] = ['appointment', 'walkIn'];
  const vehicleModels = ['Model Y', 'Model 3', 'Model S', 'Model X'];
  const colors = ['白色', '黑色', '红色', '蓝色', '银色'];
  const technicians = ['技师A-王强', '技师B-李明', '技师C-张伟', '技师D-刘涛'];
  
  for (let i = 0; i < dataCount; i++) {
    const creationTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
    const isAssigned = Math.random() > 0.3;
    
    mockData.push({
      id: i + 1,
      workOrderNo: `WO${String(Date.now() + i).slice(-8)}`,
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      workOrderType: types[Math.floor(Math.random() * types.length)],
      workOrderStatus: statuses[Math.floor(Math.random() * statuses.length)],
      creationTime: creationTime.toISOString().slice(0, 19).replace('T', ' '),
      customerSource: sources[Math.floor(Math.random() * sources.length)],
      repairmanName: `客户${i + 1}`,
      repairmanPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      licensePlateNumber: `粤B${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      configuration: Math.random() > 0.5 ? '长续航' : '标准续航',
      color: colors[Math.floor(Math.random() * colors.length)],
      vehicleAge: Math.floor(Math.random() * 60) + 1,
      mileage: Math.floor(Math.random() * 100000) + 1000,
      serviceAdvisor: `顾问${Math.floor(Math.random() * 5) + 1}`,
      assignmentStatus: isAssigned ? 'assigned' : 'pendingAssignment',
      technician: isAssigned ? technicians[Math.floor(Math.random() * technicians.length)] : '',
      estimatedWorkHours: Math.round((Math.random() * 8 + 0.5) * 2) / 2,
      estimatedStartTime: isAssigned ? new Date(creationTime.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ') : '',
      estimatedFinishTime: isAssigned ? new Date(creationTime.getTime() + Math.random() * 48 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ') : '',
      actualStartTime: '',
      actualFinishTime: '',
      isPaused: false
    });
  }
  
  return mockData;
}

const mockDispatchData = generateMockDispatchData();

export const getMockDispatchList = (
  params: DispatchSearchParams
): Promise<PaginationResponse<DispatchListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockDispatchData];
      
      if (params.workOrderNo) {
        filteredData = filteredData.filter(item => 
          item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
        );
      }
      
      if (params.priority) {
        filteredData = filteredData.filter(item => item.priority === params.priority);
      }
      
      if (params.workOrderType) {
        filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType);
      }
      
      if (params.assignmentStatus) {
        filteredData = filteredData.filter(item => item.assignmentStatus === params.assignmentStatus);
      }
      
      if (params.workOrderStatus) {
        filteredData = filteredData.filter(item => item.workOrderStatus === params.workOrderStatus);
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        data: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 500);
  });
};
```

## 4. 国际化集成

### 4.1 更新国际化文件
在 `src/locales/modules/afterSales/zh.json` 中添加dispatch模块：

```json
{
  "dispatch": {
    "title": "车间派工",
    "workOrderNo": "工单编号",
    "priority": "优先级",
    "workOrderType": "工单类型",
    "workOrderStatus": "工单状态",
    "assignmentStatus": "分配状态",
    "creationTime": "创建时间",
    "customerSource": "客户来源",
    "repairmanName": "送修人",
    "licensePlateNumber": "车牌号",
    "vehicleModel": "车型",
    "serviceAdvisor": "服务顾问",
    "technician": "技师",
    "estimatedWorkHours": "预计工时",
    "priority": {
      "urgent": "紧急",
      "normal": "普通"
    },
    "workOrderType": {
      "repair": "维修",
      "maintenance": "保养",
      "insurance": "保险"
    },
    "workOrderStatus": {
      "pendingAssignment": "待分配",
      "pendingStart": "待开工",
      "inProgress": "进行中",
      "paused": "暂停",
      "completed": "已完成",
      "cancelled": "已取消"
    },
    "assignmentStatus": {
      "pendingAssignment": "待分配",
      "assigned": "已分配"
    },
    "customerSource": {
      "appointment": "预约",
      "walkIn": "到店"
    },
    "actions": {
      "assign": "分配",
      "reassign": "重新分配",
      "detail": "详情",
      "pause": "暂停",
      "resume": "恢复",
      "complete": "完成"
    },
    "dialog": {
      "assignTitle": "分配工单",
      "reassignTitle": "重新分配工单",
      "selectTechnician": "选择技师",
      "estimatedStartTime": "预计开始时间",
      "estimatedFinishTime": "预计完成时间",
      "notes": "备注",
      "reason": "重新分配原因"
    },
    "messages": {
      "assignSuccess": "分配成功",
      "assignFailed": "分配失败",
      "reassignSuccess": "重新分配成功",
      "reassignFailed": "重新分配失败"
    }
  }
}
```

### 4.2 更新页面国际化引用
```typescript
// 修改前
const { t } = useI18n();

// 修改后
const { t, tc } = useModuleI18n('afterSales');

// 使用方式
t('dispatch.title')        // 派工相关翻译
tc('search')              // 通用翻译
```

## 5. 路由配置更新

### 5.1 更新路由路径
```typescript
// src/router/modules/afterSales.ts
{
  path: '/after-sales/dispatch',
  name: 'DispatchManagement',
  component: () => import('@/views/afterSales/dispatch/DispatchManagementView.vue'),
  meta: {
    title: 'menu.dispatchManagement',
    requiresAuth: true,
    icon: 'Operation'
  }
}
```

## 6. 重构检查清单

### 6.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/dispatch/DispatchManagementView.vue`
- [ ] 组件文件拆分并放置在 `components/` 目录下
- [ ] API模块移动到 `src/api/modules/afterSales/dispatch.ts`
- [ ] 类型定义统一到 `src/types/afterSales/dispatch.d.ts`
- [ ] Mock数据移动到 `src/mock/data/afterSales/dispatch.ts`
- [ ] 国际化文件合并到 `src/locales/modules/afterSales/`

### 6.2 代码质量验证
- [ ] 使用 `useModuleI18n('afterSales')` 替换 `useI18n()`
- [ ] 所有翻译键更新为 `t('dispatch.*')`
- [ ] TypeScript类型安全，无编译错误
- [ ] Mock数据支持动态生成和完整的搜索分页功能
- [ ] API模块与Mock数据完全集成

### 6.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 分配和重新分配功能正常
- [ ] 工单详情查看功能正常
- [ ] 国际化切换正常

### 6.4 路由验证
- [ ] 路由路径更新为 `/after-sales/dispatch`
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 7. 迁移注意事项

### 7.1 数据兼容性
- 确保新的类型定义与现有数据结构兼容
- Mock数据格式保持与原有API响应一致
- 分页参数和响应格式标准化

### 7.2 功能保持
- 保持所有现有业务功能不变
- 确保派工流程逻辑完整
- 维持用户体验一致性

### 7.3 性能优化
- 合理使用组件懒加载
- 优化大列表渲染性能
- 实现合理的缓存策略

## 8. 实施时间计划

### 8.1 第一周：基础结构搭建
- **Day 1-2**: 创建目录结构，统一类型定义
- **Day 3-4**: 重构API模块和Mock数据
- **Day 5**: 更新国际化文件和路由配置

### 8.2 第二周：组件拆分重构
- **Day 1-2**: 重构主页面组件，拆分搜索表单和表格组件
- **Day 3-4**: 重构对话框组件（分配、重新分配、详情）
- **Day 5**: 功能测试和bug修复

### 8.3 第三周：测试和优化
- **Day 1-2**: 全面功能测试和集成测试
- **Day 3-4**: 性能优化和代码审查
- **Day 5**: 文档更新和部署准备

## 9. 风险评估与应对

### 9.1 技术风险
**风险**: 组件拆分过程中状态管理复杂化
**应对**:
- 使用Props/Emits进行组件通信
- 合理使用provide/inject处理深层传递
- 保持组件职责单一

**风险**: Mock数据与真实API不匹配
**应对**:
- 严格按照现有API响应格式设计Mock数据
- 提供API切换开关，便于测试
- 与后端团队确认接口规范

### 9.2 业务风险
**风险**: 重构过程中影响现有派工流程
**应对**:
- 使用feature分支进行开发
- 保持原有功能完全不变
- 充分的回归测试

**风险**: 用户操作习惯改变
**应对**:
- 保持UI界面和交互逻辑不变
- 确保所有快捷键和操作习惯延续
- 提供平滑的迁移过渡

## 10. 成功标准

### 10.1 技术标准
- ✅ 符合项目目录结构规范
- ✅ 通过所有TypeScript类型检查
- ✅ 通过ESLint代码规范检查
- ✅ 国际化覆盖率100%
- ✅ Mock数据功能完整
- ✅ 组件拆分合理，复用性强

### 10.2 功能标准
- ✅ 所有原有功能正常工作
- ✅ 页面加载性能不降低
- ✅ 搜索和分页响应时间<500ms
- ✅ 支持中英文切换
- ✅ 兼容所有主流浏览器

### 10.3 维护标准
- ✅ 代码结构清晰，易于理解
- ✅ 组件复用性强，职责明确
- ✅ 文档完整，便于后续开发
- ✅ 测试覆盖率>80%

## 11. 后续优化建议

### 11.1 短期优化（1个月内）
- 实现派工流程的可视化展示
- 添加批量分配功能
- 优化技师工作负载显示
- 增加派工提醒和通知功能

### 11.2 中期优化（3个月内）
- 实现智能派工推荐算法
- 添加派工统计和报表功能
- 支持移动端派工操作
- 集成工时管理系统

### 11.3 长期规划（6个月内）
- 实现AI辅助派工决策
- 添加派工效率分析
- 支持多门店派工协调
- 集成第三方设备管理系统

## 12. 组件拆分详细设计

### 12.1 主页面组件 (DispatchManagementView.vue)
**职责**: 页面布局、状态管理、组件协调
**大小**: 控制在200行以内
**主要功能**:
- 页面整体布局
- 搜索参数管理
- 分页状态管理
- 对话框状态控制

### 12.2 搜索表单组件 (DispatchSearchForm.vue)
**职责**: 搜索条件输入和验证
**Props**: searchParams, options
**Emits**: search, reset
**主要功能**:
- 多条件搜索表单
- 表单验证
- 重置功能

### 12.3 派工表格组件 (DispatchTable.vue)
**职责**: 数据展示和操作按钮
**Props**: data, loading, pagination
**Emits**: assign, reassign, detail, page-change
**主要功能**:
- 工单列表展示
- 排序和筛选
- 操作按钮

### 12.4 分配对话框组件 (AssignmentDialog.vue)
**职责**: 工单分配表单
**Props**: visible, workOrder, technicians
**Emits**: confirm, cancel
**主要功能**:
- 技师选择
- 时间设置
- 表单验证

### 12.5 重新分配对话框组件 (ReassignmentDialog.vue)
**职责**: 工单重新分配表单
**Props**: visible, workOrder, technicians
**Emits**: confirm, cancel
**主要功能**:
- 新技师选择
- 原因填写
- 时间调整

### 12.6 工单详情对话框组件 (WorkOrderDetailDialog.vue)
**职责**: 工单详细信息展示
**Props**: visible, workOrderNo
**Emits**: close
**主要功能**:
- 工单详情展示
- 历史记录
- 相关文档

### 12.7 技师状态卡片组件 (TechnicianStatusCard.vue)
**职责**: 技师工作状态展示
**Props**: technician
**主要功能**:
- 技师基本信息
- 当前工作负载
- 技能标签

---

**本重构方案基于DMS前端项目的标准化规范制定，确保车间派工模块在保持现有功能完整性的基础上，实现代码结构的现代化和可维护性的显著提升。通过系统性的重构，该模块将更好地融入整体项目架构，为后续功能扩展和维护奠定坚实基础。**
