# 门店管理字典系统集成说明

## 更新概述

已成功将门店管理模块集成到项目的统一字典系统中，使用标准的字典组件和API，提升了代码的一致性和可维护性。

## 主要变更

### 1. 字典类型映射

根据 `src/constants/dictionary.ts` 中的定义，门店管理使用以下字典类型：

```typescript
// 门店状态 - 使用通用状态字典
DICTIONARY_TYPES.COMMON_STATUS: '0002'

// 门店属性 - 使用专用门店属性字典  
DICTIONARY_TYPES.STORE_PROPERTIES: '0200'
```

### 2. 组件更新

#### 2.1 搜索表单组件 (`StoreSearchForm.vue`)
- **替换前**：使用 `el-select` + 手动循环字典选项
- **替换后**：使用 `DictionarySelect` 组件

```vue
<!-- 替换前 -->
<el-select v-model="searchParams.storeStatus">
  <el-option
    v-for="option in storeStatusOptions"
    :key="option.code"
    :label="option.name"
    :value="option.code"
  />
</el-select>

<!-- 替换后 -->
<DictionarySelect
  v-model="searchParams.storeStatus"
  :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
  show-all
  :all-label="tc('all')"
  all-value=""
/>
```

#### 2.2 表格组件 (`StoreTable.vue`)
- **替换前**：通过props接收字典选项，手动转义
- **替换后**：使用 `useBatchDictionary` 组合函数

```typescript
// 替换前
const formatStoreStatus = (status: string) => {
  const option = props.storeStatusOptions.find(opt => opt.code === status);
  return option?.name || status;
};

// 替换后
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.COMMON_STATUS,
  DICTIONARY_TYPES.STORE_PROPERTIES
]);

const formatStoreStatus = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.COMMON_STATUS, status) || status;
};
```

#### 2.3 表单弹窗组件 (`StoreFormDialog.vue`)
- **替换前**：使用 `el-checkbox-group` + `el-radio-group` + 手动循环
- **替换后**：使用 `DictionaryCheckbox` 和 `DictionaryRadio` 组件

```vue
<!-- 门店属性 - 替换前 -->
<el-checkbox-group v-model="formData.storeProperties">
  <el-checkbox
    v-for="option in storePropertiesOptions"
    :key="option.code"
    :value="option.code"
  >
    {{ option.name }}
  </el-checkbox>
</el-checkbox-group>

<!-- 门店属性 - 替换后 -->
<DictionaryCheckbox
  v-model="formData.storeProperties"
  :dictionary-type="DICTIONARY_TYPES.STORE_PROPERTIES"
  :disabled="isView"
/>

<!-- 门店状态 - 替换前 -->
<el-radio-group v-model="formData.storeStatus">
  <el-radio
    v-for="option in storeStatusOptions"
    :key="option.code"
    :value="option.code"
  >
    {{ option.name }}
  </el-radio>
</el-radio-group>

<!-- 门店状态 - 替换后 -->
<DictionaryRadio
  v-model="formData.storeStatus"
  :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
  :disabled="isView"
/>
```

#### 2.4 主页面组件 (`StoreView.vue`)
- **移除**：字典相关的props传递
- **简化**：组件间的数据流

```vue
<!-- 替换前 -->
<StoreSearchForm
  v-model="searchParams"
  :store-status-options="storeStatusOptions"
  @search="handleSearch"
  @reset="handleReset"
/>

<!-- 替换后 -->
<StoreSearchForm
  v-model="searchParams"
  @search="handleSearch"
  @reset="handleReset"
/>
```

### 3. API和类型更新

#### 3.1 API模块 (`src/api/modules/base/store.ts`)
- **移除**：`getStoreStatusDictionary` 和 `getStorePropertiesDictionary` 函数
- **添加**：注释说明使用统一字典系统

#### 3.2 类型定义 (`src/types/base/store.d.ts`)
- **移除**：`DictionaryOption` 接口（使用统一的字典类型）

#### 3.3 Mock数据 (`src/mock/data/base/store.ts`)
- **移除**：字典相关的Mock函数和数据
- **添加**：注释说明使用统一字典系统

### 4. 优势和改进

#### 4.1 代码一致性
- ✅ 所有字典数据使用统一的API接口
- ✅ 统一的字典组件使用方式
- ✅ 标准化的错误处理和加载状态

#### 4.2 可维护性提升
- ✅ 减少重复代码，字典逻辑集中管理
- ✅ 自动缓存机制，提升性能
- ✅ 统一的国际化支持

#### 4.3 开发效率
- ✅ 开发者无需关心字典数据的获取和缓存
- ✅ 组件使用更简单，props更少
- ✅ 自动的错误处理和用户提示

### 5. 使用的字典组件

#### 5.1 DictionarySelect
- **用途**：下拉选择框
- **特性**：支持搜索、多选、"全部"选项
- **位置**：搜索表单中的状态选择

#### 5.2 DictionaryRadio  
- **用途**：单选按钮组
- **特性**：支持禁用选项
- **位置**：表单中的状态选择

#### 5.3 DictionaryCheckbox
- **用途**：多选框组
- **特性**：支持禁用选项
- **位置**：表单中的属性选择

### 6. 数据流变化

#### 6.1 替换前的数据流
```
主页面 → API调用获取字典 → 传递给子组件 → 子组件使用
```

#### 6.2 替换后的数据流  
```
各组件 → 直接使用字典组件/组合函数 → 自动获取和缓存字典数据
```

### 7. 注意事项

1. **向后兼容**：保持了所有业务功能不变
2. **UI一致性**：界面显示完全一致
3. **性能优化**：字典数据自动缓存，减少重复请求
4. **错误处理**：统一的错误处理机制
5. **国际化**：自动支持多语言切换

### 8. 验证清单

- [x] 搜索表单的状态下拉框正常工作
- [x] 表格中的状态和属性显示正确
- [x] 新增/编辑表单中的字典选择正常
- [x] 字典数据缓存机制生效
- [x] 错误处理正常
- [x] 国际化切换正常
- [x] 所有组件编译无错误

## 总结

通过集成统一的字典系统，门店管理模块的代码质量和可维护性得到显著提升。所有字典相关的功能现在使用标准化的组件和API，确保了项目的一致性和可扩展性。

---

**更新完成时间**：2025年7月29日  
**更新状态**：✅ 完成  
**测试状态**：⏳ 待测试
