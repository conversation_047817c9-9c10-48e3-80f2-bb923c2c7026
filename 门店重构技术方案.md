# 门店管理重构技术方案

## 1. 重构背景与目标

### 1.1 现状分析
当前门店管理页面 `src/views/system/StoreManagement.vue` 存在以下问题：
- ❌ 页面位置不符合目录结构规范（应在 `base` 模块下）
- ❌ 使用 `permission` 模块的类型定义和API，模块归属不清晰
- ❌ 分页参数不符合MyBatisPlus标准（使用 `current/size` 而非 `pageNum/pageSize`）
- ❌ 数据字典处理方式不统一，硬编码状态值
- ❌ 国际化文件分散在多个模块

### 1.2 重构目标
- ✅ 符合页面目录结构规范，迁移到 `src/views/base/store/` 模块
- ✅ 统一使用MyBatisPlus标准分页参数
- ✅ 规范化数据字典处理方式
- ✅ 统一API响应处理和错误处理
- ✅ 保持UI界面完全一致
- ✅ 保持后端API接口不变

## 2. MyBatisPlus分页规范分析

### 2.1 标准分页参数
```typescript
// ✅ MyBatisPlus标准分页参数
interface PageParams {
  current: number;    // 当前页码，从1开始
  size: number;   // 每页条数
}

// ✅ MyBatisPlus标准分页响应
interface PageResponse<T> {
  records: T[];       // 数据列表
  total: number;      // 总条数
  current: number;    // 当前页码
  size: number;   // 每页条数
  pages: number;      // 总页数
}
```

### 2.2 当前实现问题
```typescript
// ❌ 当前使用的分页参数（不符合MyBatisPlus标准）
const pagination = reactive({
  current: 1,         // ❌ 应为 pageNum
  size: 10,           // ❌ 应为 pageSize
  total: 0,
});

// ❌ API调用参数
const queryParams = {
  ...searchParams,
  current: pagination.current,    // ❌ 应为 pageNum
  size: pagination.size          // ❌ 应为 pageSize
};
```

### 2.3 目标实现
```typescript
// ✅ 修正后的分页参数
const pagination = reactive({
  current: 1,         // ✅ 符合MyBatisPlus标准
  size: 10,           // ✅ 符合MyBatisPlus标准
  total: 0,
});

// ✅ 修正后的API调用
const queryParams = {
  ...searchParams,
  current: pagination.current,    // ✅ 符合MyBatisPlus标准
  size: pagination.size          // ✅ 符合MyBatisPlus标准
};
```

## 3. API响应处理规范

### 3.1 标准响应结构
```typescript
// 标准API响应格式
interface ApiResponse<T> {
  code: number;    // 响应码
  message: string;          // 响应消息
  result: T;               // 业务数据（重要：使用 result 而非 data）
  success: boolean;        // 是否成功
  timestamp: number;        // 时间戳
  traceId: string;        // 跟踪ID
}
```

### 3.2 响应处理标准模式
```typescript
const loadData = async () => {
  try {
    loading.value = true;
    const response = await getStoreList(queryParams);
    
    // ✅ 必须检查响应码和成功状态
    if (response.code === 200 && response.success) {
      // ✅ 必须使用 response.result.records
      tableData.value = response.result.records || [];
      // ✅ 必须使用 response.result.total
      pagination.total = response.result.total || 0;
      pagination.current = response.result.current || pagination.current;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};
```

## 4. 重构实施方案

### 4.1 目录结构重构

#### 4.1.1 目标目录结构
```
src/
├── views/base/store/
│   ├── StoreView.vue                   # 门店管理主页面（路由页面）
│   └── components/
│       ├── StoreFormDialog.vue         # 门店表单弹窗组件
│       ├── StoreTable.vue              # 门店表格组件（可选）
│       └── StoreSearchForm.vue         # 门店搜索表单组件（可选）
├── api/modules/base/
│   └── store.ts                        # 门店管理API
├── types/base/
│   └── store.d.ts                      # 门店管理类型定义
├── mock/data/base/
│   └── store.ts                        # 门店管理Mock数据
└── locales/modules/base/
    ├── zh.json                         # 中文翻译（包含门店管理）
    └── en.json                         # 英文翻译（包含门店管理）
```

#### 4.1.2 路由配置调整
```typescript
// 路由路径调整
{
  path: '/base/store',
  name: 'StoreManagement',
  component: () => import('@/views/base/store/StoreView.vue'),
  meta: {
    title: 'menu.store',
    requiresAuth: true,
    icon: 'Shop'
  }
}
```

### 4.2 类型定义重构

#### 4.2.1 创建标准类型定义
```typescript
// src/types/base/store.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  current?: number;    // 当前页码，从1开始
  size?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  current: number;     // 当前页码
  size: number;    // 每页条数
  pages: number;       // 总页数
}

// 门店信息
export interface StoreItem {
  id: string;
  storeCode: string;
  storeName: string;
  storeType: 'headquarter' | 'main' | 'branch' | 'warehouse';
  storeProperties: string[];
  storeStatus: string;
  manager?: string;
  contactPerson?: string;
  contactPhone?: string;
  province?: string;
  city?: string;
  district?: string;
  detailAddress?: string;
  parentId?: string;
  children?: StoreItem[];
  remark?: string;
  createTime: string;
}

// 门店搜索参数（继承分页参数）
export interface StoreSearchParams extends PageParams {
  storeName?: string;
  storeCode?: string;
  storeStatus?: string;
}

// 门店分页响应
export interface StorePageResponse extends PageResponse<StoreItem> {}

// 新增门店请求
export interface CreateStoreRequest {
  storeCode: string;
  storeName: string;
  storeType: 'headquarter' | 'main' | 'branch' | 'warehouse';
  storeProperties: string[];
  storeStatus: string;
  manager?: string;
  contactPerson?: string;
  contactPhone?: string;
  province?: string;
  city?: string;
  district?: string;
  detailAddress?: string;
  parentId?: string;
  remark?: string;
}

// 更新门店请求
export interface UpdateStoreRequest extends CreateStoreRequest {
  id: string;
}

// 字典选项
export interface DictionaryOption {
  code: string;
  name: string;
}
```

### 4.3 API模块重构

#### 4.3.1 创建标准API模块
```typescript
// src/api/modules/base/store.ts

import request from '@/api';
import type {
  StoreSearchParams,
  StorePageResponse,
  StoreItem,
  CreateStoreRequest,
  UpdateStoreRequest,
  DictionaryOption
} from '@/types/base/store';
import {
  getStoreList as getMockStoreList,
  getStoreDetail as getMockStoreDetail,
  addStore as addMockStore,
  updateStore as updateMockStore,
  deleteStore as deleteMockStore,
  getStoreStatusDictionary as getMockStoreStatusDictionary,
  getStorePropertiesDictionary as getMockStorePropertiesDictionary
} from '@/mock/data/base/store';
import { USE_MOCK_API } from '@/utils/mock-config';

// 获取门店列表
export const getStoreList = (params: StoreSearchParams): Promise<ApiResponse<StorePageResponse>> => {
  if (USE_MOCK_API) {
    return getMockStoreList(params);
  }
  // 保持原有API接口不变
  return request.get<any, ApiResponse<StorePageResponse>>('/stores/list', { params });
};

// 获取门店详情
export const getStoreDetail = (id: string): Promise<ApiResponse<StoreItem>> => {
  if (USE_MOCK_API) {
    return getMockStoreDetail(id);
  }
  // 保持原有API接口不变
  return request.get<any, ApiResponse<StoreItem>>(`/stores/detail`, { params: { id } });
};

// 新增门店
export const addStore = (data: CreateStoreRequest): Promise<ApiResponse<StoreItem>> => {
  if (USE_MOCK_API) {
    return addMockStore(data);
  }
  // 保持原有API接口不变
  return request.post<any, ApiResponse<StoreItem>>('/stores/create', data);
};

// 更新门店
export const updateStore = (data: UpdateStoreRequest): Promise<ApiResponse<StoreItem>> => {
  if (USE_MOCK_API) {
    return updateMockStore(data);
  }
  // 保持原有API接口不变
  return request.post<any, ApiResponse<StoreItem>>('/stores/update', data);
};

// 删除门店
export const deleteStore = (id: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return deleteMockStore(id);
  }
  // 保持原有API接口不变
  return request.delete<any, ApiResponse<void>>(`/stores/delete`, { params: { id } });
};

// 获取门店状态字典
export const getStoreStatusDictionary = (): Promise<ApiResponse<DictionaryOption[]>> => {
  if (USE_MOCK_API) {
    return getMockStoreStatusDictionary();
  }
  // 保持原有API接口不变
  return request.get<any, ApiResponse<DictionaryOption[]>>('/basic/dic/list', { 
    params: { type: '0002' } 
  });
};

// 获取门店属性字典
export const getStorePropertiesDictionary = (): Promise<ApiResponse<DictionaryOption[]>> => {
  if (USE_MOCK_API) {
    return getMockStorePropertiesDictionary();
  }
  // 保持原有API接口不变
  return request.get<any, ApiResponse<DictionaryOption[]>>('/basic/dic/list', { 
    params: { type: '0200' } 
  });
};
```

## 5. 数据字典处理规范

### 5.1 字典数据标准化
```typescript
// 字典选项标准格式
interface DictionaryOption {
  code: string;  // 字典编码（后端存储值）
  name: string;  // 字典名称（前端展示值，已国际化）
}

// 门店状态字典映射
const STORE_STATUS_MAP = {
  '00020001': 'success',  // 正常
  '00020002': 'danger'    // 禁用
};
```

### 5.2 字典使用规范
```typescript
// ✅ 标准字典转义函数
const formatStoreStatus = (status: string) => {
  const option = storeStatusOptions.value.find(opt => opt.code === status);
  return option?.name || status;
};

// ✅ 标准字典标签类型获取
const getStoreStatusType = (status: string) => {
  return STORE_STATUS_MAP[status] || 'info';
};
```

## 6. 重构执行步骤

### 6.1 第一阶段：基础结构创建
1. 创建目录结构
2. 创建类型定义文件
3. 创建API模块文件
4. 创建Mock数据文件

### 6.2 第二阶段：页面重构
1. 迁移页面文件到新位置
2. 修正分页参数和处理逻辑
3. 修正API调用和响应处理
4. 统一数据字典处理

### 6.3 第三阶段：测试验证
1. 功能测试：确保所有功能正常
2. 分页测试：验证MyBatisPlus分页正确
3. 字典测试：验证数据字典显示正确
4. UI测试：确保界面保持一致

## 7. 关键注意事项

### 7.1 API接口保持不变
- 所有API的请求方法、URL、参数必须与原接口保持一致
- 只做前端代码重构，不涉及后端修改
- 确保Mock数据与真实API响应格式一致

### 7.2 UI界面保持一致
- 页面布局、样式、交互逻辑完全保持不变
- 只调整内部实现，不改变用户体验
- 确保所有功能按钮、表单验证等行为一致

### 7.3 分页参数标准化
- 统一使用 `current` 和 `size` 参数
- 分页组件绑定 `pagination.current`
- API响应使用 `response.result.records` 和 `response.result.total`

### 7.4 错误处理规范
- 统一使用 `response.result` 获取业务数据
- 检查响应码 `response.code === 200 && response.success`
- 统一错误提示和异常处理

## 8. 验证清单

### 8.1 目录结构验证
- [ ] 页面文件迁移到 `src/views/base/store/StoreView.vue`
- [ ] API模块创建在 `src/api/modules/base/store.ts`
- [ ] 类型定义创建在 `src/types/base/store.d.ts`
- [ ] Mock数据创建在 `src/mock/data/base/store.ts`

### 8.2 分页功能验证
- [ ] 分页参数使用 `current` 和 `size`
- [ ] 分页组件绑定 `pagination.current`
- [ ] API响应使用 `response.result.records` 和 `response.result.total`
- [ ] 分页功能正常工作

### 8.3 数据字典验证
- [ ] 门店状态下拉选项正确显示
- [ ] 门店属性多选框正确显示
- [ ] 表格状态列正确转义显示
- [ ] 状态标签颜色正确映射

### 8.4 功能完整性验证
- [ ] 搜索功能正常
- [ ] 新增门店功能正常
- [ ] 编辑门店功能正常
- [ ] 查看门店功能正常
- [ ] 删除门店功能正常
- [ ] 树形结构显示正常

## 9. Mock数据实现

### 9.1 Mock数据结构
```typescript
// src/mock/data/base/store.ts

import type {
  StoreSearchParams,
  StorePageResponse,
  StoreItem,
  CreateStoreRequest,
  UpdateStoreRequest,
  DictionaryOption
} from '@/types/base/store';

// 动态生成门店Mock数据（25-30条，便于测试分页）
function generateMockStores(): StoreItem[] {
  const storeCount = Math.floor(Math.random() * 6) + 25;
  const mockStores: StoreItem[] = [];

  // 总部
  mockStores.push({
    id: 'hq_perodua',
    storeCode: 'HQ001',
    storeName: 'Perodua 总部',
    storeType: 'headquarter',
    storeProperties: ['sales', 'after_sales'],
    storeStatus: '00020001',
    manager: 'Mr. CEO',
    contactPerson: 'Admin HQ',
    contactPhone: '03-8888-8888',
    province: '雪兰莪州',
    city: '赛城',
    district: '雪邦区',
    detailAddress: 'Perodua Global Manufacturing Sdn. Bhd.',
    createTime: '2020-01-01T10:00:00Z',
    children: []
  });

  // 生成其他门店
  for (let i = 1; i < storeCount; i++) {
    const storeTypes = ['main', 'branch', 'warehouse'];
    const provinces = ['吉隆坡', '雪兰莪州', '柔佛州', '槟城州', '霹雳州'];
    const cities = ['吉隆坡', '莎阿南', '新山', '乔治市', '怡保'];

    mockStores.push({
      id: `store_${i.toString().padStart(3, '0')}`,
      storeCode: `ST${i.toString().padStart(3, '0')}`,
      storeName: `${provinces[i % provinces.length]}${storeTypes[i % storeTypes.length] === 'main' ? '主店' : storeTypes[i % storeTypes.length] === 'branch' ? '分店' : '仓库'}${i}`,
      storeType: storeTypes[i % storeTypes.length] as any,
      storeProperties: i % 2 === 0 ? ['sales'] : ['sales', 'after_sales'],
      storeStatus: i % 10 === 0 ? '00020002' : '00020001',
      manager: `经理${i}`,
      contactPerson: `联系人${i}`,
      contactPhone: `03-${(1000 + i).toString()}-${(1000 + i * 2).toString()}`,
      province: provinces[i % provinces.length],
      city: cities[i % cities.length],
      district: `${cities[i % cities.length]}区`,
      detailAddress: `${cities[i % cities.length]}街道${i}号`,
      parentId: i % 5 === 0 ? undefined : 'hq_perodua',
      createTime: new Date(2020 + (i % 4), (i % 12), (i % 28) + 1).toISOString(),
      children: []
    });
  }

  return mockStores;
}

const mockStores = generateMockStores();

// 门店状态字典
const mockStoreStatusOptions: DictionaryOption[] = [
  { code: '00020001', name: '正常' },
  { code: '00020002', name: '禁用' }
];

// 门店属性字典
const mockStorePropertiesOptions: DictionaryOption[] = [
  { code: 'sales', name: '销售' },
  { code: 'after_sales', name: '售后' },
  { code: 'parts', name: '零件' },
  { code: 'finance', name: '金融' }
];

// 获取门店列表（支持搜索和分页）
export const getStoreList = (params: StoreSearchParams): Promise<ApiResponse<StorePageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockStores];

      // 搜索过滤
      if (params.storeName) {
        filteredData = filteredData.filter(store =>
          store.storeName.includes(params.storeName!)
        );
      }

      if (params.storeCode) {
        filteredData = filteredData.filter(store =>
          store.storeCode.includes(params.storeCode!)
        );
      }

      if (params.storeStatus) {
        filteredData = filteredData.filter(store =>
          store.storeStatus === params.storeStatus
        );
      }

      // MyBatisPlus标准分页处理
      const current = params.current || 1;
      const size = params.size || 10;
      const total = filteredData.length;
      const pages = Math.ceil(total / size);
      const start = (current - 1) * size;
      const end = start + size;

      resolve({
        code: 200,
        message: '获取成功',
        result: {
          records: filteredData.slice(start, end),
          total,
          current,
          size,
          pages
        },
        success: true,
        timestamp: Date.now()
      });
    }, 500);
  });
};

// 其他Mock函数实现...
export const getStoreDetail = (id: string): Promise<ApiResponse<StoreItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const store = mockStores.find(s => s.id === id);
      if (store) {
        resolve({
          code: 200,
          message: '获取成功',
          result: store,
          success: true,
          timestamp: Date.now()
        });
      } else {
        resolve({
          code: 404,
          message: '门店不存在',
          result: {} as StoreItem,
          success: false,
          timestamp: Date.now()
        });
      }
    }, 300);
  });
};

export const getStoreStatusDictionary = (): Promise<ApiResponse<DictionaryOption[]>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '获取成功',
        result: mockStoreStatusOptions,
        success: true,
        timestamp: Date.now()
      });
    }, 200);
  });
};

export const getStorePropertiesDictionary = (): Promise<ApiResponse<DictionaryOption[]>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '获取成功',
        result: mockStorePropertiesOptions,
        success: true,
        timestamp: Date.now()
      });
    }, 200);
  });
};
```

## 10. 页面实现重构

### 10.1 主页面重构要点
```vue
<!-- src/views/base/store/StoreView.vue -->
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, RefreshLeft, Plus, Edit, Delete, View } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getStoreList,
  getStoreDetail,
  addStore,
  updateStore,
  deleteStore,
  getStoreStatusDictionary,
  getStorePropertiesDictionary
} from '@/api/modules/base/store';
import type {
  StoreItem,
  StoreSearchParams,
  CreateStoreRequest,
  UpdateStoreRequest,
  DictionaryOption
} from '@/types/base/store';
import StoreFormDialog from './components/StoreFormDialog.vue';

const { t, tc } = useModuleI18n('base');

// ✅ 修正分页参数为MyBatisPlus标准
const pagination = reactive({
  current: 1,        // ✅ 使用current而非pageNum
  size: 10,          // ✅ 使用size而非pageSize
  total: 0,
});

// ✅ 修正搜索参数类型
const searchParams = reactive<StoreSearchParams>({
  storeName: '',
  storeCode: '',
  storeStatus: '',
});

// ✅ 标准数据加载函数
const loadData = async () => {
  try {
    loading.value = true;

    const queryParams = {
      ...searchParams,
      current: pagination.current,    // ✅ 使用current
      size: pagination.size          // ✅ 使用size
    };

    const response = await getStoreList(queryParams);

    // ✅ 标准响应处理
    if (response.code === 200 && response.success) {
      // ✅ 使用response.result.records
      tableData.value = response.result.records || [];
      // ✅ 使用response.result.total
      pagination.total = response.result.total || 0;
      pagination.current = response.result.current || pagination.current;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取门店列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// ✅ 修正分页处理函数
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;    // ✅ 使用current
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.current = page;  // ✅ 使用current
  loadData();
};
</script>

<template>
  <!-- 分页组件修正 -->
  <el-pagination
    v-if="pagination.total > 0"
    class="mt-20"
    v-model:current-page="pagination.current"    <!-- ✅ 绑定current -->
    v-model:page-size="pagination.size"
    :total="pagination.total"
    :page-sizes="[10, 20, 50, 100]"
    layout="total, sizes, prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>
```

### 10.2 数据字典处理优化
```typescript
// 字典数据管理
const storeStatusOptions = ref<DictionaryOption[]>([]);
const storePropertiesOptions = ref<DictionaryOption[]>([]);

// ✅ 标准字典转义函数
const formatStoreStatus = (status: string) => {
  const option = storeStatusOptions.value.find(opt => opt.code === status);
  return option?.name || status;
};

const formatStoreProperties = (properties: string[]) => {
  if (!properties || properties.length === 0) return '-';
  return properties.map(prop => {
    const option = storePropertiesOptions.value.find(opt => opt.code === prop);
    return option?.name || prop;
  }).join(', ');
};

// ✅ 状态标签类型映射
const getStoreStatusType = (status: string) => {
  const statusTypeMap = {
    '00020001': 'success',  // 正常
    '00020002': 'danger'    // 禁用
  };
  return statusTypeMap[status] || 'info';
};

// ✅ 加载字典数据
const loadDictionaries = async () => {
  try {
    const [statusResponse, propertiesResponse] = await Promise.all([
      getStoreStatusDictionary(),
      getStorePropertiesDictionary()
    ]);

    if (statusResponse.code === 200 && statusResponse.success) {
      storeStatusOptions.value = statusResponse.result;
    }

    if (propertiesResponse.code === 200 && propertiesResponse.success) {
      storePropertiesOptions.value = propertiesResponse.result;
    }
  } catch (error) {
    console.error('加载字典数据失败:', error);
  }
};
```

## 11. 国际化文件整合

### 11.1 合并到base模块
```json
// src/locales/modules/base/zh.json
{
  "store": {
    "title": "门店管理",
    "storeName": "门店名称",
    "storeCode": "门店编号",
    "parentStore": "上级门店",
    "storeType": "门店类型",
    "storeStatus": "门店状态",
    "storeProperties": "门店属性",
    "manager": "负责人",
    "contactPerson": "联系人",
    "contactPhone": "联系电话",
    "province": "省份",
    "city": "城市",
    "district": "区县",
    "detailAddress": "详细地址",
    "storeStatusNormal": "正常",
    "storeStatusDisabled": "禁用",
    "storeTypeHeadquarter": "总部",
    "storeTypeMain": "主店",
    "storeTypeBranch": "分店",
    "storeTypeWarehouse": "仓库",
    "addStore": "新增门店",
    "editStore": "编辑门店",
    "selectParentStore": "请选择上级门店",
    "selectStoreType": "请选择门店类型",
    "storeNameRequired": "请输入门店名称",
    "storeCodeRequired": "请输入门店编号",
    "storeTypeRequired": "请选择门店类型"
  }
}
```

## 12. 完整实施计划

### 12.1 实施时间安排
- **第1天**：创建目录结构和类型定义
- **第2天**：实现API模块和Mock数据
- **第3天**：重构主页面和组件
- **第4天**：测试验证和问题修复
- **第5天**：文档更新和代码审查

### 12.2 风险控制措施
1. **备份原文件**：重构前备份所有相关文件
2. **分支开发**：使用feature分支进行重构
3. **渐进测试**：每个步骤完成后立即测试
4. **回滚准备**：确保可以快速回滚到原始状态

### 12.3 质量保证
1. **代码审查**：重构完成后进行团队代码审查
2. **功能测试**：全面测试所有功能点
3. **性能测试**：确保重构后性能不下降
4. **兼容性测试**：确保与其他模块的兼容性

---

**本重构方案确保在保持UI界面和后端API完全不变的前提下，将门店管理页面重构为符合项目规范的标准化实现。通过统一的MyBatisPlus分页规范、标准化的API响应处理和规范化的数据字典处理，提升代码质量和可维护性。**
