基于您提供的Service层设计输出格式要求，我将按照Spring Boot + MyBatis Plus + MySQL的技术栈来设计库存管理页面的Service接口。

⏺ Update Todos
  ⎿  ☐ 设计库存概览统计Service接口
     ☐ 设计库存列表查询Service接口
     ☐ 设计库存详情查询Service接口
     ☐ 设计库存调整Service接口
     ☐ 设计批量补货申请Service接口
     ☐ 设计库存趋势查询Service接口

⏺ 库存管理页面Service层接口设计

  1. 库存概览统计Service

  描述：负责库存管理首页统计数据的业务逻辑处理，包括总库存SKU、缺货警报、库存预警和占用价值的实时计算

  名称：IInventoryDashboardService对应接口个数：1个

  接口1定义:
  /**
   * 获取库存概览统计数据
   * 计算门店的库存关键指标，包括总SKU数量、各状态库存统计和占用价值
   */
  @Cacheable(value = "inventory:dashboard", key = "#storeId", unless = "#result == null")
  R<InventoryDashboardVO> getDashboardStatistics(Long storeId);

  入参名称：Long storeId入参描述：门店ID，用于指定查询哪个门店的库存统计数据

  返回参数名称：InventoryDashboardVO返回参数描述：库存概览统计数据视图对象，包含四个核心指标和趋势信息返回参数字段：
  class InventoryDashboardVO {
      /**
       * 总库存SKU数量
       */
      Integer totalSkuCount;

      /**
       * 缺货警报数量(current_stock = 0)
       */
      Integer shortageCount;

      /**
       * 库存预警数量(current_stock <= safety_stock)
       */
      Integer warningCount;

      /**
       * 占用价值总金额(占用库存 * 进货价)
       */
      BigDecimal occupiedValue;

      /**
       * SKU增长率(%)
       */
      BigDecimal skuGrowthRate;

      /**
       * 趋势方向：UP-上升, DOWN-下降, STABLE-稳定
       */
      String trendDirection;
  }

  方法实现类：InventoryDashboardServiceImpl

  数据操作：
  - 查询表: inventory（库存表）、parts（零件主数据表）
  - 操作类型: 纯查询操作
  - 缓存操作: 读取缓存，5分钟过期时间
  - 数据操作详细步骤:
    a. 统计门店总库存SKU数量(COUNT DISTINCT part_id)
    b. 统计缺货警报数量(current_stock = 0)
    c. 统计库存预警数量(current_stock > 0 AND current_stock <= safety_stock)
    d. 计算占用价值总额(SUM(occupied_stock * purchase_price))
    e. 计算SKU增长趋势(对比上月同期数据)

  方法实现逻辑：
  校验入参storeId非空
  构建查询条件 storeId = #{storeId}
  并行执行5个统计查询：
    查询1：统计总SKU数量
    查询2：统计缺货数量
    查询3：统计预警数量
    查询4：计算占用价值
    查询5：计算增长趋势
  IF 任何查询失败 THEN 抛出业务异常
  组装InventoryDashboardVO对象
  设置缓存5分钟过期
  返回统计结果

⏺ Update Todos
  ⎿  ☒ 设计库存概览统计Service接口
     ☐ 设计库存列表查询Service接口
     ☐ 设计库存详情查询Service接口
     ☐ 设计库存调整Service接口
     ☐ 设计批量补货申请Service接口
     ☐ 设计库存趋势查询Service接口

⏺ 2. 库存列表查询Service

  描述：负责库存列表的分页查询和多条件筛选，支持按零件信息和库存状态进行过滤，提供高效的库存数据展示

  名称：IInventoryQueryService对应接口个数：1个

  接口1定义:
  /**
   * 分页查询库存列表
   * 支持按零件类别、编号、名称和库存状态进行筛选
   */
  R<Page<InventoryListVO>> getInventoryList(InventoryQueryRequest request);

  入参名称：InventoryQueryRequest入参描述：库存列表查询请求参数，包含分页信息和筛选条件入参具体参数:
  class InventoryQueryRequest {
      /**
       * 门店ID，必填
       */
      @NotNull(message = "门店ID不能为空")
      Long storeId;

      /**
       * 页码，从1开始
       */
      @Min(value = 1, message = "页码不能小于1")
      Integer page = 1;

      /**
       * 每页条数
       */
      @Min(value = 1, message = "每页条数不能小于1")
      @Max(value = 100, message = "每页条数不能超过100")
      Integer size = 20;

      /**
       * 零件类别筛选
       */
      String category;

      /**
       * 零件编号筛选
       */
      String partCode;

      /**
       * 零件名称筛选
       */
      String partName;

      /**
       * 库存状态筛选：SHORTAGE-缺货, WARNING-预警, NORMAL-正常, OVERSTOCKED-超储
       */
      List<String> stockStatus;
  }

  返回参数名称：Page返回参数描述：分页库存列表数据，包含库存基础信息和分页元数据返回参数字段：
  class InventoryListVO {
      /**
       * 库存记录ID
       */
      Long inventoryId;

      /**
       * 库存状态：SHORTAGE-缺货, WARNING-预警, NORMAL-正常, OVERSTOCKED-超储
       */
      String stockStatus;

      /**
       * 零件编号
       */
      String partCode;

      /**
       * 零件名称
       */
      String partName;

      /**
       * 品牌
       */
      String brand;

      /**
       * 规格型号
       */
      String specification;

      /**
       * 当前库存数量
       */
      Integer currentStock;

      /**
       * 安全库存数量
       */
      Integer safetyStock;

      /**
       * 可用库存数量
       */
      Integer availableStock;

      /**
       * 占用库存数量
       */
      Integer occupiedStock;

      /**
       * 损坏库存数量
       */
      Integer damagedStock;

      /**
       * 库位
       */
      String location;

      /**
       * 仓库名称
       */
      String warehouseName;

      /**
       * 最后盘点时间
       */
      LocalDate lastCheckTime;
  }

  方法实现类：InventoryQueryServiceImpl

  数据操作：
  - 查询表: inventory（库存表）、parts（零件主数据表）、warehouses（仓库表）
  - 操作类型: 纯查询操作
  - 数据操作详细步骤:
    a. 构建动态查询条件(基于传入的筛选参数)
    b. 执行主查询获取库存列表数据(包含LEFT JOIN关联查询)
    c. 在SELECT中计算库存状态(CASE WHEN逻辑)
    d. 按状态优先级排序(缺货>预警>正常>超储)
    e. 应用分页参数进行数据分页
    f. 执行COUNT查询获取总记录数

  方法实现逻辑：
  校验入参request非空且storeId有效
  构建MyBatis Plus的QueryWrapper查询条件：
    添加门店筛选条件 eq("store_id", storeId)
    IF category不为空 THEN 添加 like("p.category", category)
    IF partCode不为空 THEN 添加 like("p.part_code", partCode)
    IF partName不为空 THEN 添加 like("p.part_name", partName)
    IF stockStatus不为空 THEN 添加状态筛选条件
  构建Page对象，设置页码和每页条数
  执行inventoryMapper.selectInventoryListWithParts查询
  遍历查询结果，对每个记录执行以下操作：
    计算库存状态(current_stock与safety_stock比较)
    转换日期格式
    设置库存状态显示文本
  IF 查询异常 THEN 记录日志并返回空结果
  返回分页查询结果

⏺ Update Todos
  ⎿  ☒ 设计库存概览统计Service接口
     ☒ 设计库存列表查询Service接口
     ☐ 设计库存详情查询Service接口
     ☐ 设计库存调整Service接口
     ☐ 设计批量补货申请Service接口
     ☐ 设计库存趋势查询Service接口

⏺ 3. 库存详情查询Service

  描述：负责单个库存项目的详细信息查询，包括零件基础信息、库存详细状态和历史趋势数据的综合展示

  名称：IInventoryDetailService对应接口个数：2个

  接口1定义:
  /**
   * 查询库存详情信息
   * 获取零件基础信息和库存详细数据
   */
  @Cacheable(value = "inventory:detail", key = "#inventoryId", unless = "#result == null")
  R<InventoryDetailVO> getInventoryDetail(Long inventoryId);

  接口2定义:
  /**
   * 查询库存变化趋势数据
   * 获取指定天数内的库存变化趋势图表数据
   */
  @Cacheable(value = "inventory:trend", key = "#inventoryId + '_' + #days", unless = "#result == null")
  R<InventoryTrendVO> getInventoryTrend(Long inventoryId, Integer days);

  入参名称：Long inventoryId, Integer days入参描述：inventoryId为库存记录ID，days为查询趋势的天数(默认30天)

  返回参数名称：InventoryDetailVO返回参数描述：库存详情数据对象，包含零件基础信息和库存详细状态返回参数字段：
  class InventoryDetailVO {
      /**
       * 零件基础信息
       */
      PartInfoVO partInfo;

      /**
       * 库存详细信息
       */
      InventoryInfoVO inventoryInfo;
  }

  class PartInfoVO {
      /**
       * 零件编号
       */
      String partCode;

      /**
       * 零件名称
       */
      String partName;

      /**
       * 品牌
       */
      String brand;

      /**
       * 类别
       */
      String category;

      /**
       * 规格型号
       */
      String specification;

      /**
       * 计量单位
       */
      String unit;

      /**
       * 建议零售价
       */
      BigDecimal retailPrice;

      /**
       * 进货参考价
       */
      BigDecimal purchasePrice;
  }

  class InventoryInfoVO {
      /**
       * 当前库存数量
       */
      Integer currentStock;

      /**
       * 可用库存数量
       */
      Integer availableStock;

      /**
       * 占用库存数量
       */
      Integer occupiedStock;

      /**
       * 损坏库存数量
       */
      Integer damagedStock;

      /**
       * 安全库存数量
       */
      Integer safetyStock;

      /**
       * 最大库存数量
       */
      Integer maximumStock;

      /**
       * 库存状态
       */
      String stockStatus;

      /**
       * 仓库名称
       */
      String warehouseName;

      /**
       * 库位
       */
      String location;

      /**
       * 货架号
       */
      String shelfNumber;

      /**
       * 最后盘点时间
       */
      LocalDateTime lastCheckTime;

      /**
       * 盘点人
       */
      String checkPerson;
  }

  返回参数名称：InventoryTrendVO返回参数描述：库存趋势数据对象，包含时间序列的库存变化数据返回参数字段：
  class InventoryTrendVO {
      /**
       * 安全库存基准线
       */
      Integer safetyStockLine;

      /**
       * 趋势数据点列表
       */
      List<TrendDataPoint> trendData;
  }

  class TrendDataPoint {
      /**
       * 日期
       */
      LocalDate date;

      /**
       * 库存数量
       */
      Integer stock;
  }

  方法实现类：InventoryDetailServiceImpl

  数据操作：
  - 查询表: inventory（库存表）、parts（零件主数据表）、warehouses（仓库表）、inventory_history（库存历史表）
  - 操作类型: 纯查询操作
  - 缓存操作: 详情数据缓存30分钟，趋势数据缓存10分钟
  - 数据操作详细步骤:
    a. 查询库存基础信息(LEFT JOIN parts和warehouses表)
    b. 计算库存状态(基于current_stock和safety_stock比较)
    c. 查询最近盘点人信息(子查询inventory_check_records表)
    d. 查询历史趋势数据(按日期分组聚合)
    e. 组装返回数据结构

  方法实现逻辑：
  // getInventoryDetail方法
  校验入参inventoryId非空
  构建查询条件 eq("i.id", inventoryId)
  执行inventoryMapper.selectDetailWithParts查询：
    LEFT JOIN parts p ON i.part_id = p.id  
    LEFT JOIN warehouses w ON i.warehouse_id = w.id
  IF 查询结果为空 THEN 抛出"库存记录不存在"异常
  计算库存状态：
    IF current_stock = 0 THEN status = "SHORTAGE"
    ELSE IF current_stock <= safety_stock THEN status = "WARNING"  
    ELSE IF current_stock > maximum_stock THEN status = "OVERSTOCKED"
    ELSE status = "NORMAL"
  查询最近盘点人：
    执行子查询获取最近一次盘点记录的操作人
  组装PartInfoVO和InventoryInfoVO对象
  设置缓存30分钟
  返回库存详情结果

  // getInventoryTrend方法  
  校验入参inventoryId非空，days默认30
  查询安全库存基准线
  构建时间范围条件：>= DATE_SUB(NOW(), INTERVAL days DAY)
  执行historyMapper.selectTrendData查询：
    按DATE(create_time)分组
    取每日最后一次记录的库存数量
  IF 当日无历史记录 THEN 补充当前库存数据点
  组装TrendDataPoint列表
  设置缓存10分钟  
  返回趋势数据结果

⏺ Update Todos
  ⎿  ☒ 设计库存概览统计Service接口
     ☒ 设计库存列表查询Service接口
     ☒ 设计库存详情查询Service接口
     ☐ 设计库存调整Service接口
     ☐ 设计批量补货申请Service接口
     ☐ 设计库存趋势查询Service接口

⏺ 4. 库存调整Service

  描述：负责库存数量的手动调整操作，包括增加、减少和设置库存，支持多种调整原因和完整的操作审计

  名称：IInventoryAdjustService对应接口个数：1个

  接口1定义:
  /**
   * 执行库存调整操作
   * 支持增加、减少、设置库存，记录调整原因和操作日志
   */
  @Transactional(rollbackFor = Exception.class)
  @CacheEvict(value = {"inventory:detail", "inventory:dashboard"}, allEntries = true)
  R<InventoryAdjustResultVO> adjustInventory(InventoryAdjustRequest request);

  入参名称：InventoryAdjustRequest入参描述：库存调整请求参数，包含调整类型、数量、原因等完整信息入参具体参数:
  class InventoryAdjustRequest {
      /**
       * 库存记录ID，必填
       */
      @NotNull(message = "库存ID不能为空")
      Long inventoryId;

      /**
       * 调整类型：INCREASE-增加, DECREASE-减少, SET_TO-设置为
       */
      @NotBlank(message = "调整类型不能为空")
      String adjustType;

      /**
       * 调整数量，必须大于0
       */
      @Min(value = 1, message = "调整数量必须大于0")
      Integer quantity;

      /**
       * 调整原因：INVENTORY_CHECK-盘点调整, DAMAGE_ADJUST-损坏调整, SYSTEM_CORRECTION-系统纠错, OTHER-其他
       */
      @NotBlank(message = "调整原因不能为空")
      String reason;

      /**
       * 备注信息
       */
      String remark;

      /**
       * 操作人，必填
       */
      @NotBlank(message = "操作人不能为空")
      String operator;
  }

  返回参数名称：InventoryAdjustResultVO返回参数描述：库存调整结果对象，包含调整前后的库存数量和调整记录ID返回参数字段：
  class InventoryAdjustResultVO {
      /**
       * 调整记录ID
       */
      Long adjustmentId;

      /**
       * 调整前库存数量
       */
      Integer beforeStock;

      /**
       * 调整后库存数量
       */
      Integer afterStock;

      /**
       * 调整数量(带符号)
       */
      Integer adjustQuantity;

      /**
       * 调整后库存状态
       */
      String stockStatus;

      /**
       * 操作时间
       */
      LocalDateTime operateTime;
  }

  方法实现类：InventoryAdjustServiceImpl

  数据操作：
  - 查询表: inventory（库存表）、parts（零件主数据表）
  - 更新表: inventory（库存表）
  - 新增表: inventory_adjustment_logs（库存调整日志表）、inventory_history（库存历史表）
  - 操作类型: 事务性写操作
  - 缓存操作: 清除库存详情和概览统计缓存
  - 数据操作详细步骤:
    a. 开启事务，查询当前库存信息并加行锁
    b. 业务规则校验(调整后库存不能为负数)
    c. 计算调整后的各项库存数量
    d. 更新inventory表的库存字段和状态
    e. 插入调整日志记录到inventory_adjustment_logs表
    f. 插入历史记录到inventory_history表
    g. 提交事务并清除相关缓存

  方法实现逻辑：
  校验入参request完整性和业务规则
  开启事务 @Transactional
  查询当前库存信息(加行锁)：
    SELECT * FROM inventory WHERE id = #{inventoryId} FOR UPDATE
  IF 库存记录不存在 THEN 抛出"库存记录不存在"异常
  计算调整后库存数量：
    IF adjustType = "INCREASE" THEN afterStock = currentStock + quantity
    ELSE IF adjustType = "DECREASE" THEN afterStock = currentStock - quantity  
    ELSE IF adjustType = "SET_TO" THEN afterStock = quantity
  IF afterStock < 0 THEN 抛出"调整后库存不能为负数"异常
  计算调整后库存状态：
    IF afterStock = 0 THEN status = "SHORTAGE"
    ELSE IF afterStock <= safetyStock THEN status = "WARNING"
    ELSE IF afterStock > maximumStock THEN status = "OVERSTOCKED"  
    ELSE status = "NORMAL"
  更新inventory表：
    SET current_stock = afterStock, stock_status = status
    IF reason = "DAMAGE_ADJUST" AND adjustType = "DECREASE" THEN 
      SET damaged_stock = damaged_stock + quantity
    重新计算available_stock = current_stock - occupied_stock - damaged_stock
  调用insertAdjustmentLog方法插入调整日志
  调用insertInventoryHistory方法插入历史记录
  清除缓存 @CacheEvict
  组装InventoryAdjustResultVO返回结果
  TRY块结束
  CATCH Exception e时：
    记录错误日志
    抛出业务异常"库存调整失败"

⏺ Update Todos
  ⎿  ☒ 设计库存概览统计Service接口
     ☒ 设计库存列表查询Service接口
     ☒ 设计库存详情查询Service接口
     ☒ 设计库存调整Service接口
     ☒ 设计库存趋势查询Service接口
     ☐ 设计批量补货申请Service接口

⏺ 5. 批量补货申请Service

  描述：负责批量创建补货申请单的业务逻辑，包括选中库存的补货需求计算、申请单生成和库存占用处理

  名称：IReplenishmentOrderService对应接口个数：1个

  接口1定义:
  /**
   * 批量创建补货申请单
   * 基于选中的库存项目创建补货申请，计算补货数量并更新库存占用状态
   */
  @Transactional(rollbackFor = Exception.class)
  @CacheEvict(value = {"inventory:dashboard", "inventory:detail"}, allEntries = true)
  R<ReplenishmentOrderResultVO> createBatchReplenishmentOrder(BatchReplenishmentRequest request);

  入参名称：BatchReplenishmentRequest入参描述：批量补货申请请求参数，包含门店信息、补货项目列表和申请人信息入参具体参数:
  class BatchReplenishmentRequest {
      /**
       * 门店ID，必填
       */
      @NotNull(message = "门店ID不能为空")
      Long storeId;

      /**
       * 补货项目列表，必填且不能为空
       */
      @NotEmpty(message = "补货项目不能为空")
      @Valid
      List<ReplenishmentItem> inventoryItems;

      /**
       * 申请人，必填
       */
      @NotBlank(message = "申请人不能为空")
      String applicant;

      /**
       * 申请备注
       */
      String remark;
  }

  class ReplenishmentItem {
      /**
       * 库存记录ID
       */
      @NotNull(message = "库存ID不能为空")
      Long inventoryId;

      /**
       * 申请补货数量
       */
      @Min(value = 1, message = "申请数量必须大于0")
      Integer requestQuantity;
  }

  返回参数名称：ReplenishmentOrderResultVO返回参数描述：补货申请单创建结果，包含申请单号和统计信息返回参数字段：
  class ReplenishmentOrderResultVO {
      /**
       * 补货申请单号
       */
      String orderNo;

      /**
       * 申请单ID
       */
      Long orderId;

      /**
       * 申请项目总数
       */
      Integer totalItems;

      /**
       * 申请总数量
       */
      Integer totalQuantity;

      /**
       * 申请总金额
       */
      BigDecimal totalAmount;

      /**
       * 创建时间
       */
      LocalDateTime createTime;

      /**
       * 当前状态
       */
      String status;
  }

  方法实现类：ReplenishmentOrderServiceImpl

  数据操作：
  - 查询表: inventory（库存表）、parts（零件主数据表）
  - 新增表: replenishment_orders（补货单表）、replenishment_order_details（补货单明细表）
  - 更新表: inventory（库存表，更新占用库存）
  - 操作类型: 事务性写操作
  - 缓存操作: 清除库存概览和详情缓存
  - 数据操作详细步骤:
    a. 开启事务，验证库存数据有效性
    b. 生成补货单号(日期+流水号格式)
    c. 插入补货单主表记录
    d. 批量插入补货单明细记录
    e. 计算并更新补货单汇总信息
    f. 更新相关库存的占用状态
    g. 插入操作日志记录

  方法实现逻辑：
  校验入参request完整性和inventoryItems非空
  开启事务 @Transactional
  生成补货单号：
    获取当前日期YYYYMMDD格式
    查询当日最大流水号并+1
    拼接格式：REP + 日期 + 4位流水号
  验证库存数据有效性：
    遍历inventoryItems，对每个item执行以下操作：
      查询库存记录是否存在且属于指定门店
      查询零件状态是否正常(parts.status = 1)
      验证申请数量的合理性
  IF 任何验证失败 THEN 抛出相应业务异常
  插入补货单主表：
    INSERT INTO replenishment_orders
    设置order_no、store_id、applicant等基础信息
    初始状态为DRAFT，金额和数量待计算
  获取补货单ID = LAST_INSERT_ID()
  批量插入补货单明细：
    遍历inventoryItems，对每个item执行以下操作：
      查询零件的purchase_price
      计算小计金额 = requestQuantity * purchase_price
      INSERT INTO replenishment_order_details
  计算补货单汇总信息：
    SUM(request_quantity) AS totalQuantity
    SUM(total_price) AS totalAmount
  更新补货单主表的汇总字段
  更新库存占用状态：
    遍历inventoryItems，对每个item执行以下操作：
      UPDATE inventory SET occupied_stock = occupied_stock + requestQuantity
      重新计算available_stock = current_stock - occupied_stock - damaged_stock
  插入操作日志记录
  清除相关缓存 @CacheEvict
  组装ReplenishmentOrderResultVO返回结果
  TRY块结束
  CATCH Exception e时：
    记录错误日志
    事务自动回滚
    抛出业务异常"批量补货申请创建失败"

⏺ Update Todos
  ⎿  ☒ 设计库存概览统计Service接口
     ☒ 设计库存列表查询Service接口
     ☒ 设计库存详情查询Service接口
     ☒ 设计库存调整Service接口
     ☒ 设计批量补货申请Service接口
     ☒ 设计库存趋势查询Service接口

⏺ ---
  📋 Service层设计总结

  技术特点

  - Spring Boot框架: 使用标准注解和配置
  - MyBatis Plus: 利用QueryWrapper动态构建查询条件
  - MySQL数据库: 完全基于已设计的数据库表结构
  - 缓存策略: 使用Spring Cache注解进行数据缓存
  - 事务管理: 使用@Transactional确保数据一致性

  设计原则

  1. 单一职责: 每个Service专注特定业务领域
  2. 参数校验: 使用JSR-303注解进行入参验证
  3. 异常处理: 统一的业务异常处理机制
  4. 缓存优化: 查询缓存+写操作清缓存策略
  5. 事务控制: 写操作使用事务保证ACID特性
  6. 审计追踪: 关键操作记录完整日志

  核心业务流程

  - 查询流程: 参数校验 → 构建查询条件 → 执行查询 → 数据转换 → 缓存存储
  - 写入流程: 参数校验 → 开启事务 → 业务校验 → 数据操作 → 日志记录 → 清除缓存 → 提交事务

  性能优化策略

  - 分页查询: 大数据量场景使用Page对象
  - 并行统计: 多个统计查询可并行执行
  - 索引利用: 查询条件完全基于数据库索引设计
  - 缓存分层: 不同业务数据使用不同的缓存策略

  这套Service层设计完全符合Spring Boot + MyBatis Plus的最佳实践，为库存管理页面提供了高性能、高可靠性的业务逻辑支撑。