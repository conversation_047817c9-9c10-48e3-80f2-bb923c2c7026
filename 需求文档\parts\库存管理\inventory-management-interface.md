# 库存管理页面接口文档

## 📋 接口定义

### 1. 库存概览统计

**接口地址：** `/api/inventory/dashboard`  
**请求方式：** `POST`  
**接口描述：** 获取库存首页四个核心指标的统计数据

#### 请求参数

```json
{
  "storeId": 1001
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| storeId | integer | 是 | 门店ID |

#### 响应结果

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalSkuCount": 2458,
    "shortageCount": 23,
    "warningCount": 127,
    "occupiedValue": 125000.00,
    "trends": {
      "skuGrowthRate": 5.2,
      "trendDirection": "UP"
    }
  }
}
```

| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalSkuCount | integer | 总库存SKU数量 |
| shortageCount | integer | 缺货警报数量 |
| warningCount | integer | 库存预警数量 |
| occupiedValue | decimal | 占用价值(元) |
| trends.skuGrowthRate | decimal | SKU增长率(%) |
| trends.trendDirection | string | 趋势方向：UP/DOWN/STABLE |

---

### 2. 库存列表查询

**接口地址：** `/api/inventory/list`  
**请求方式：** `POST`  
**接口描述：** 分页查询库存列表，支持多条件筛选

#### 请求参数

```json
{
  "storeId": 1001,
  "page": 1,
  "size": 20,
  "category": "制动系统",
  "partCode": "P10001",
  "partName": "火花塞",
  "stockStatus": ["SHORTAGE", "WARNING"]
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| storeId | integer | 是 | 门店ID |
| page | integer | 否 | 页码(从1开始)，默认1 |
| size | integer | 否 | 每页条数，默认20 |
| category | string | 否 | 零件类别 |
| partCode | string | 否 | 零件编号 |
| partName | string | 否 | 零件名称 |
| stockStatus | array | 否 | 库存状态筛选：SHORTAGE/WARNING/NORMAL/OVERSTOCKED |

#### 响应结果

```json
{
  "code": 200,
  "data": [
    {
      "inventoryId": 1001,
      "stockStatus": "SHORTAGE",
      "partCode": "P10001",
      "partName": "火花塞",
      "currentStock": 0,
      "safetyStock": 5,
      "availableStock": 0,
      "location": "A01-01"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalCount": 1958,
    "totalPages": 98
  }
}
```

---

### 3. 库存详情查询

**接口地址：** `/api/inventory/detail`  
**请求方式：** `POST`  
**接口描述：** 获取零件基础信息、库存详细信息和变化趋势

#### 请求参数

```json
{
  "inventoryId": 1003
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| inventoryId | integer | 是 | 库存记录ID |

#### 响应结果

```json
{
  "code": 200,
  "data": {
    "partInfo": {
      "partCode": "P10003",
      "partName": "刹车片前",
      "brand": "天合",
      "category": "制动系统",
      "specification": "GDB1621",
      "unit": "套",
      "retailPrice": 268.00,
      "purchasePrice": 185.00
    },
    "inventoryInfo": {
      "currentStock": 25,
      "availableStock": 23,
      "occupiedStock": 2,
      "damagedStock": 0,
      "safetyStock": 8,
      "maximumStock": 50,
      "stockStatus": "NORMAL",
      "warehouseName": "主仓库",
      "location": "A01-03",
      "shelfNumber": "A01",
      "lastCheckTime": "2024-01-08T10:30:00",
      "checkPerson": "张库管"
    }
  }
}
```

---

### 4. 库存趋势数据

**接口地址：** `/api/inventory/trend`  
**请求方式：** `POST`  
**接口描述：** 获取指定时间范围内的库存变化趋势数据

#### 请求参数

```json
{
  "inventoryId": 1003,
  "days": 30
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| inventoryId | integer | 是 | 库存记录ID |
| days | integer | 否 | 查询天数，默认30 |

#### 响应结果

```json
{
  "code": 200,
  "data": {
    "safetyStockLine": 8,
    "trendData": [
      {
        "date": "2024-01-15",
        "stock": 25
      },
      {
        "date": "2024-01-14",
        "stock": 22
      }
    ]
  }
}
```

---

### 5. 库存调整

**接口地址：** `/api/inventory/adjust`  
**请求方式：** `POST`  
**接口描述：** 对指定库存进行增减调整

#### 请求参数

```json
{
  "inventoryId": 1003,
  "adjustType": "INCREASE",
  "quantity": 5,
  "reason": "INVENTORY_CHECK",
  "remark": "盘点发现多出5套",
  "operator": "张库管"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| inventoryId | integer | 是 | 库存记录ID |
| adjustType | string | 是 | 调整类型：INCREASE/DECREASE/SET_TO |
| quantity | integer | 是 | 调整数量 |
| reason | string | 是 | 调整原因：INVENTORY_CHECK/DAMAGE_ADJUST/SYSTEM_CORRECTION/OTHER |
| remark | string | 否 | 备注说明 |
| operator | string | 是 | 操作人 |

#### 响应结果

```json
{
  "code": 200,
  "message": "库存调整成功",
  "data": {
    "adjustmentId": 2001,
    "beforeStock": 25,
    "afterStock": 30
  }
}
```

---

### 6. 批量补货申请

**接口地址：** `/api/replenishment/batch-create`  
**请求方式：** `POST`  
**接口描述：** 对选中的库存项目批量创建补货申请

#### 请求参数

```json
{
  "storeId": 1001,
  "inventoryItems": [
    {
      "inventoryId": 1001,
      "requestQuantity": 10
    },
    {
      "inventoryId": 1002,
      "requestQuantity": 15
    }
  ],
  "applicant": "张库管",
  "remark": "月度补货申请"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| storeId | integer | 是 | 门店ID |
| inventoryItems | array | 是 | 补货项目列表 |
| inventoryItems[].inventoryId | integer | 是 | 库存记录ID |
| inventoryItems[].requestQuantity | integer | 是 | 申请数量 |
| applicant | string | 是 | 申请人 |
| remark | string | 否 | 申请备注 |

#### 响应结果

```json
{
  "code": 200,
  "message": "补货申请创建成功",
  "data": {
    "orderNo": "REP202401150001",
    "totalItems": 2
  }
}
```

---

## 🎯 页面交互维度核心功能点

### 1. 📊 数据监控与可视化

**功能描述：** 实时展示库存关键指标，提供业务决策支撑

- **总库存SKU监控：** 显示当前管理的零件种类数量及增长趋势
- **缺货警报：** 实时统计current_stock=0的零件数量，红色高亮显示
- **库存预警：** 统计current_stock≤safety_stock的零件，橙色预警提示
- **占用价值：** 计算occupied_stock的总金额，监控资金占用情况

**交互特点：**
  - 卡片式布局，视觉层次清晰
  - 数值变化趋势箭头指示
  - 点击卡片可快速筛选对应状态的库存

### 2. 🔍 智能搜索与筛选

**功能描述：** 多维度搜索和状态筛选，快速定位目标库存

- **组合搜索：** 支持零件类别、编号、名称的模糊匹配
- **状态筛选：** 按库存状态快速过滤(缺货/预警/正常/超储)
- **重置功能：** 一键清空所有搜索条件

**交互特点：**
  - 实时搜索，输入即过滤
  - 状态筛选带计数显示，如"🔴 缺货(23)"
  - 搜索历史记忆，提升操作效率

### 3. 📋 数据表格与操作

**功能描述：** 核心数据展示和快速操作入口

- **状态可视化：** emoji图标+颜色编码直观显示库存状态
- **关键信息展示：** 零件基础信息+库存数量+库位信息
- **批量操作：** 支持多选进行批量补货申请
- **单项操作：** 每行提供"详情"和"调整"快捷操作

**交互特点：**
  - 固定列+可滚动设计，适配大屏显示
  - 状态列优先展示，便于快速识别
  - 分页加载，支持大数据量展示(共1,958条记录)

### 4. 🔧 功能工具栏

**功能描述：** 核心业务功能的快速入口

- **库存盘点：** 启动盘点流程，更新实际库存
- **批量补货：** 基于选中项目创建补货申请
- **安全库存设置：** 批量维护库存预警阈值

**交互特点：**
  - 工具栏固定位置，操作便捷
  - 与表格数据联动，支持基于选择的批量操作
  - 权限控制，不同角色显示不同功能

### 5. 📱 弹窗交互体验

#### 库存详情弹窗

**功能描述：** 深度展示单个零件的全维度信息

- **零件档案：** 完整的基础信息展示(编号/名称/品牌/价格)
- **库存详情：** 多维度库存数据(当前/可用/占用/损坏/安全/最大)
- **趋势分析：** 30天库存变化图表，辅助库存管理决策

**交互特点：**
  - 模态弹窗设计，专注信息浏览
  - 信息分区布局，层次清晰
  - 趋势图可视化，直观展示变化规律

#### 库存调整弹窗

**功能描述：** 精准的库存调整操作界面

- **调整类型：** 增加/减少/设置为三种调整模式
- **数量输入：** 实时计算调整后库存，防止误操作
- **原因分类：** 标准化调整原因(盘点/损坏/纠错/其他)
- **操作确认：** 警告提示+二次确认，保证操作安全

**交互特点：**
  - 表单式交互，步骤引导清晰
  - 实时计算反馈，所见即所得
  - 风险提示机制，降低误操作风险

### 6. ⚡ 实时响应与反馈

**功能描述：** 系统实时性和用户反馈机制

- **状态实时计算：** 基于业务规则自动计算库存状态
- **操作即时反馈：** 调整操作后立即更新列表数据
- **分页导航：** 支持大数据量的高效浏览

**交互特点：**
  - 无刷新更新，提升操作流畅度
  - Loading状态提示，操作状态可感知
  - 错误处理机制，异常情况友好提示

---

## 🔧 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 📝 库存状态枚举

| 状态值 | 中文名称 | 说明 |
|--------|----------|------|
| SHORTAGE | 缺货 | 当前库存为0 |
| WARNING | 预警 | 当前库存≤安全库存 |
| NORMAL | 正常 | 库存处于安全范围内 |
| OVERSTOCKED | 超储 | 库存超过最大库存阈值 |

## 🔄 调整类型枚举

| 类型值 | 中文名称 | 说明 |
|--------|----------|------|
| INCREASE | 增加库存 | 在当前库存基础上增加指定数量 |
| DECREASE | 减少库存 | 在当前库存基础上减少指定数量 |
| SET_TO | 设置为 | 直接设置库存为指定数量 |

## 📋 调整原因枚举

| 原因值 | 中文名称 | 说明 |
|--------|----------|------|
| INVENTORY_CHECK | 盘点调整 | 定期或不定期盘点发现的差异 |
| DAMAGE_ADJUST | 损坏调整 | 零件损坏导致的库存调整 |
| SYSTEM_CORRECTION | 系统纠错 | 系统数据错误的修正 |
| OTHER | 其他 | 其他原因导致的调整 |

