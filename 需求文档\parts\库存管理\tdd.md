⏺ 您对TDD的理解是完全正确的！基于我的搜索结果，TDD的核心确实是：

  1. 先写测试用例（描述期望的行为）
  2. 运行测试（此时应该失败，因为还没有实现）
  3. 编写最少的代码让测试通过
  4. 重构代码（保持测试通过）
  5. 重复这个循环

  而且您提到的"测试用例是否能完全执行通过作为接口完成标准"正是TDD的核心理念。

  现在我为库存管理Service接口设计完整的TDD测试用例：

  库存管理Service接口TDD测试用例设计

  1. 库存概览统计Service测试用例

  @SpringBootTest
  @Transactional
  @Rollback
  class InventoryDashboardServiceTest {

      @Autowired
      private IInventoryDashboardService dashboardService;

      @MockBean
      private InventoryMapper inventoryMapper;

      @MockBean
      private PartsMapper partsMapper;

      @Test
      @DisplayName("正常场景：获取库存概览统计数据")
      void testGetDashboardStatistics_Success() {
          // Given - 准备测试数据
          Long storeId = 1L;
          when(inventoryMapper.countTotalSku(storeId)).thenReturn(2458);
          when(inventoryMapper.countByStockStatus(storeId, "SHORTAGE")).thenReturn(23);
          when(inventoryMapper.countByStockStatus(storeId, "WARNING")).thenReturn(127);
          when(inventoryMapper.calculateOccupiedValue(storeId)).thenReturn(new BigDecimal("125000.00"));
          when(inventoryMapper.calculateSkuGrowthRate(storeId, 30)).thenReturn(new BigDecimal("5.2"));

          // When - 执行测试方法
          R<InventoryDashboardVO> result = dashboardService.getDashboardStatistics(storeId);

          // Then - 验证结果
          assertThat(result).isNotNull();
          assertThat(result.getCode()).isEqualTo(200);
          assertThat(result.getData().getTotalSkuCount()).isEqualTo(2458);
          assertThat(result.getData().getShortageCount()).isEqualTo(23);
          assertThat(result.getData().getWarningCount()).isEqualTo(127);
          assertThat(result.getData().getOccupiedValue()).isEqualByComparingTo("125000.00");
          assertThat(result.getData().getSkuGrowthRate()).isEqualByComparingTo("5.2");
          assertThat(result.getData().getTrendDirection()).isEqualTo("UP");
      }

      @Test
      @DisplayName("边界条件：门店ID为null")
      void testGetDashboardStatistics_NullStoreId() {
          // Given
          Long storeId = null;

          // When & Then
          assertThatThrownBy(() -> dashboardService.getDashboardStatistics(storeId))
              .isInstanceOf(IllegalArgumentException.class)
              .hasMessage("门店ID不能为空");
      }

      @Test
      @DisplayName("边界条件：门店不存在")
      void testGetDashboardStatistics_StoreNotFound() {
          // Given
          Long storeId = 99999L;
          when(inventoryMapper.countTotalSku(storeId)).thenReturn(0);
          when(inventoryMapper.countByStockStatus(storeId, "SHORTAGE")).thenReturn(0);
          when(inventoryMapper.countByStockStatus(storeId, "WARNING")).thenReturn(0);
          when(inventoryMapper.calculateOccupiedValue(storeId)).thenReturn(BigDecimal.ZERO);
          when(inventoryMapper.calculateSkuGrowthRate(storeId, 30)).thenReturn(BigDecimal.ZERO);

          // When
          R<InventoryDashboardVO> result = dashboardService.getDashboardStatistics(storeId);

          // Then
          assertThat(result.getData().getTotalSkuCount()).isEqualTo(0);
          assertThat(result.getData().getShortageCount()).isEqualTo(0);
          assertThat(result.getData().getOccupiedValue()).isEqualByComparingTo("0");
      }

      @Test
      @DisplayName("异常场景：数据库查询异常")
      void testGetDashboardStatistics_DatabaseException() {
          // Given
          Long storeId = 1L;
          when(inventoryMapper.countTotalSku(storeId))
              .thenThrow(new DataAccessException("数据库连接失败") {});

          // When & Then
          assertThatThrownBy(() -> dashboardService.getDashboardStatistics(storeId))
              .isInstanceOf(BusinessException.class)
              .hasMessage("获取库存统计数据失败");
      }

      @Test
      @DisplayName("性能测试：统计查询响应时间小于500ms")
      void testGetDashboardStatistics_Performance() {
          // Given
          Long storeId = 1L;
          mockSuccessfulStatistics(storeId);

          // When & Then
          assertTimeout(Duration.ofMillis(500), () -> {
              dashboardService.getDashboardStatistics(storeId);
          });
      }

      @Test
      @DisplayName("缓存测试：第二次调用使用缓存")
      void testGetDashboardStatistics_Cache() {
          // Given
          Long storeId = 1L;
          mockSuccessfulStatistics(storeId);

          // When - 第一次调用
          dashboardService.getDashboardStatistics(storeId);
          // 第二次调用  
          dashboardService.getDashboardStatistics(storeId);

          // Then - 验证mapper只被调用一次（使用了缓存）
          verify(inventoryMapper, times(1)).countTotalSku(storeId);
      }
  }

  2. 库存列表查询Service测试用例

  @SpringBootTest
  @Transactional
  @Rollback
  class InventoryQueryServiceTest {

      @Autowired
      private IInventoryQueryService queryService;

      @MockBean
      private InventoryMapper inventoryMapper;

      @Test
      @DisplayName("正常场景：分页查询库存列表")
      void testGetInventoryList_Success() {
          // Given
          InventoryQueryRequest request = InventoryQueryRequest.builder()
              .storeId(1L)
              .page(1)
              .size(20)
              .category("制动系统")
              .partCode("P10001")
              .partName("刹车片")
              .stockStatus(Arrays.asList("SHORTAGE", "WARNING"))
              .build();

          List<InventoryListVO> inventoryList = createMockInventoryList();
          Page<InventoryListVO> mockPage = new Page<>(1, 20);
          mockPage.setRecords(inventoryList);
          mockPage.setTotal(1958L);

          when(inventoryMapper.selectInventoryListWithParts(any(), any()))
              .thenReturn(mockPage);

          // When
          R<Page<InventoryListVO>> result = queryService.getInventoryList(request);

          // Then
          assertThat(result).isNotNull();
          assertThat(result.getCode()).isEqualTo(200);
          assertThat(result.getData().getRecords()).hasSize(3);
          assertThat(result.getData().getTotal()).isEqualTo(1958L);
          assertThat(result.getData().getCurrent()).isEqualTo(1);
          assertThat(result.getData().getSize()).isEqualTo(20);
      }

      @Test
      @DisplayName("边界条件：查询条件为空")
      void testGetInventoryList_EmptyConditions() {
          // Given
          InventoryQueryRequest request = InventoryQueryRequest.builder()
              .storeId(1L)
              .page(1)
              .size(20)
              .build();

          when(inventoryMapper.selectInventoryListWithParts(any(), any()))
              .thenReturn(new Page<>(1, 20));

          // When
          R<Page<InventoryListVO>> result = queryService.getInventoryList(request);

          // Then
          assertThat(result.getCode()).isEqualTo(200);
          verify(inventoryMapper).selectInventoryListWithParts(any(), argThat(wrapper ->
              wrapper.getSqlSegment().contains("store_id = 1")
          ));
      }

      @Test
      @DisplayName("参数校验：页码小于1")
      void testGetInventoryList_InvalidPage() {
          // Given
          InventoryQueryRequest request = InventoryQueryRequest.builder()
              .storeId(1L)
              .page(0)
              .size(20)
              .build();

          // When & Then
          assertThatThrownBy(() -> queryService.getInventoryList(request))
              .isInstanceOf(ValidationException.class)
              .hasMessageContaining("页码不能小于1");
      }

      @Test
      @DisplayName("参数校验：每页条数超过限制")
      void testGetInventoryList_ExceedSizeLimit() {
          // Given
          InventoryQueryRequest request = InventoryQueryRequest.builder()
              .storeId(1L)
              .page(1)
              .size(200)
              .build();

          // When & Then
          assertThatThrownBy(() -> queryService.getInventoryList(request))
              .isInstanceOf(ValidationException.class)
              .hasMessageContaining("每页条数不能超过100");
      }

      @Test
      @DisplayName("业务规则：库存状态计算正确性")
      void testGetInventoryList_StockStatusCalculation() {
          // Given
          InventoryQueryRequest request = createValidRequest();
          List<InventoryListVO> mockData = Arrays.asList(
              createInventoryVO(0, 5),     // 缺货
              createInventoryVO(3, 10),    // 预警
              createInventoryVO(25, 8),    // 正常
              createInventoryVO(85, 15)    // 超储(假设最大库存为50)
          );

          Page<InventoryListVO> mockPage = new Page<>();
          mockPage.setRecords(mockData);
          when(inventoryMapper.selectInventoryListWithParts(any(), any()))
              .thenReturn(mockPage);

          // When
          R<Page<InventoryListVO>> result = queryService.getInventoryList(request);

          // Then
          List<InventoryListVO> records = result.getData().getRecords();
          assertThat(records.get(0).getStockStatus()).isEqualTo("SHORTAGE");
          assertThat(records.get(1).getStockStatus()).isEqualTo("WARNING");
          assertThat(records.get(2).getStockStatus()).isEqualTo("NORMAL");
          assertThat(records.get(3).getStockStatus()).isEqualTo("OVERSTOCKED");
      }

      private List<InventoryListVO> createMockInventoryList() {
          return Arrays.asList(
              InventoryListVO.builder()
                  .inventoryId(1L)
                  .stockStatus("SHORTAGE")
                  .partCode("P10001")
                  .partName("火花塞")
                  .currentStock(0)
                  .safetyStock(5)
                  .build(),
              InventoryListVO.builder()
                  .inventoryId(2L)
                  .stockStatus("WARNING")
                  .partCode("P10002")
                  .partName("机油滤清器")
                  .currentStock(3)
                  .safetyStock(10)
                  .build(),
              InventoryListVO.builder()
                  .inventoryId(3L)
                  .stockStatus("NORMAL")
                  .partCode("P10003")
                  .partName("刹车片前")
                  .currentStock(25)
                  .safetyStock(8)
                  .build()
          );
      }
  }

  3. 库存调整Service测试用例

  @SpringBootTest
  @Transactional
  @Rollback
  class InventoryAdjustServiceTest {

      @Autowired
      private IInventoryAdjustService adjustService;

      @MockBean
      private InventoryMapper inventoryMapper;

      @MockBean
      private InventoryAdjustmentLogMapper logMapper;

      @MockBean
      private InventoryHistoryMapper historyMapper;

      @Test
      @DisplayName("正常场景：增加库存调整")
      void testAdjustInventory_IncreaseSuccess() {
          // Given
          InventoryAdjustRequest request = InventoryAdjustRequest.builder()
              .inventoryId(1L)
              .adjustType("INCREASE")
              .quantity(5)
              .reason("INVENTORY_CHECK")
              .remark("盘点发现多出5套")
              .operator("张库管")
              .build();

          Inventory mockInventory = createMockInventory(25, 23, 2, 0, 8, 50);
          when(inventoryMapper.selectByIdForUpdate(1L)).thenReturn(mockInventory);
          when(inventoryMapper.updateById(any())).thenReturn(1);
          when(logMapper.insert(any())).thenReturn(1);
          when(historyMapper.insert(any())).thenReturn(1);

          // When
          R<InventoryAdjustResultVO> result = adjustService.adjustInventory(request);

          // Then
          assertThat(result.getCode()).isEqualTo(200);
          assertThat(result.getData().getBeforeStock()).isEqualTo(25);
          assertThat(result.getData().getAfterStock()).isEqualTo(30);
          assertThat(result.getData().getAdjustQuantity()).isEqualTo(5);
          assertThat(result.getData().getStockStatus()).isEqualTo("NORMAL");

          // 验证数据库操作
          verify(inventoryMapper).selectByIdForUpdate(1L);
          verify(inventoryMapper).updateById(argThat(inventory ->
              inventory.getCurrentStock().equals(30)
          ));
          verify(logMapper).insert(any());
          verify(historyMapper).insert(any());
      }

      @Test
      @DisplayName("正常场景：减少库存调整")
      void testAdjustInventory_DecreaseSuccess() {
          // Given
          InventoryAdjustRequest request = InventoryAdjustRequest.builder()
              .inventoryId(1L)
              .adjustType("DECREASE")
              .quantity(3)
              .reason("DAMAGE_ADJUST")
              .remark("发现损坏3套")
              .operator("李仓管")
              .build();

          Inventory mockInventory = createMockInventory(25, 23, 2, 0, 8, 50);
          when(inventoryMapper.selectByIdForUpdate(1L)).thenReturn(mockInventory);
          when(inventoryMapper.updateById(any())).thenReturn(1);
          when(logMapper.insert(any())).thenReturn(1);
          when(historyMapper.insert(any())).thenReturn(1);

          // When
          R<InventoryAdjustResultVO> result = adjustService.adjustInventory(request);

          // Then
          assertThat(result.getData().getAfterStock()).isEqualTo(22);
          verify(inventoryMapper).updateById(argThat(inventory ->
              inventory.getCurrentStock().equals(22) &&
              inventory.getDamagedStock().equals(3) // 损坏调整应该增加损坏库存
          ));
      }

      @Test
      @DisplayName("正常场景：设置库存调整")
      void testAdjustInventory_SetToSuccess() {
          // Given
          InventoryAdjustRequest request = InventoryAdjustRequest.builder()
              .inventoryId(1L)
              .adjustType("SET_TO")
              .quantity(30)
              .reason("SYSTEM_CORRECTION")
              .remark("系统纠错")
              .operator("管理员")
              .build();

          Inventory mockInventory = createMockInventory(25, 23, 2, 0, 8, 50);
          when(inventoryMapper.selectByIdForUpdate(1L)).thenReturn(mockInventory);
          when(inventoryMapper.updateById(any())).thenReturn(1);
          when(logMapper.insert(any())).thenReturn(1);
          when(historyMapper.insert(any())).thenReturn(1);

          // When
          R<InventoryAdjustResultVO> result = adjustService.adjustInventory(request);

          // Then
          assertThat(result.getData().getAfterStock()).isEqualTo(30);
          assertThat(result.getData().getAdjustQuantity()).isEqualTo(5); // 30-25
      }

      @Test
      @DisplayName("业务规则：调整后库存不能为负数")
      void testAdjustInventory_NegativeStockNotAllowed() {
          // Given
          InventoryAdjustRequest request = InventoryAdjustRequest.builder()
              .inventoryId(1L)
              .adjustType("DECREASE")
              .quantity(30)
              .reason("DAMAGE_ADJUST")
              .operator("张库管")
              .build();

          Inventory mockInventory = createMockInventory(25, 23, 2, 0, 8, 50);
          when(inventoryMapper.selectByIdForUpdate(1L)).thenReturn(mockInventory);

          // When & Then
          assertThatThrownBy(() -> adjustService.adjustInventory(request))
              .isInstanceOf(BusinessException.class)
              .hasMessage("调整后库存不能为负数");

          // 验证事务回滚，没有更新数据库
          verify(inventoryMapper, never()).updateById(any());
          verify(logMapper, never()).insert(any());
      }

      @Test
      @DisplayName("业务规则：库存状态计算正确")
      void testAdjustInventory_StockStatusCalculation() {
          // Given - 测试调整到缺货状态
          InventoryAdjustRequest request = InventoryAdjustRequest.builder()
              .inventoryId(1L)
              .adjustType("SET_TO")
              .quantity(0)
              .reason("INVENTORY_CHECK")
              .operator("张库管")
              .build();

          Inventory mockInventory = createMockInventory(25, 23, 2, 0, 8, 50);
          when(inventoryMapper.selectByIdForUpdate(1L)).thenReturn(mockInventory);
          when(inventoryMapper.updateById(any())).thenReturn(1);
          when(logMapper.insert(any())).thenReturn(1);
          when(historyMapper.insert(any())).thenReturn(1);

          // When
          R<InventoryAdjustResultVO> result = adjustService.adjustInventory(request);

          // Then
          assertThat(result.getData().getStockStatus()).isEqualTo("SHORTAGE");
          verify(inventoryMapper).updateById(argThat(inventory ->
              "SHORTAGE".equals(inventory.getStockStatus())
          ));
      }

      @Test
      @DisplayName("参数校验：必填字段校验")
      void testAdjustInventory_RequiredFieldValidation() {
          // Given - 缺少必填字段
          InventoryAdjustRequest request = InventoryAdjustRequest.builder()
              .inventoryId(null) // 缺少库存ID
              .adjustType("INCREASE")
              .quantity(5)
              .build();

          // When & Then
          assertThatThrownBy(() -> adjustService.adjustInventory(request))
              .isInstanceOf(ValidationException.class)
              .hasMessageContaining("库存ID不能为空");
      }

      @Test
      @DisplayName("异常场景：库存记录不存在")
      void testAdjustInventory_InventoryNotFound() {
          // Given
          InventoryAdjustRequest request = createValidAdjustRequest();
          when(inventoryMapper.selectByIdForUpdate(1L)).thenReturn(null);

          // When & Then
          assertThatThrownBy(() -> adjustService.adjustInventory(request))
              .isInstanceOf(BusinessException.class)
              .hasMessage("库存记录不存在");
      }

      @Test
      @DisplayName("事务测试：异常时自动回滚")
      void testAdjustInventory_TransactionRollback() {
          // Given
          InventoryAdjustRequest request = createValidAdjustRequest();
          Inventory mockInventory = createMockInventory(25, 23, 2, 0, 8, 50);

          when(inventoryMapper.selectByIdForUpdate(1L)).thenReturn(mockInventory);
          when(inventoryMapper.updateById(any())).thenReturn(1);
          when(logMapper.insert(any())).thenThrow(new DataAccessException("插入日志失败") {});

          // When & Then
          assertThatThrownBy(() -> adjustService.adjustInventory(request))
              .isInstanceOf(BusinessException.class);

          // 验证事务回滚后库存数据未被修改
          // (在真实测试中需要检查数据库状态)
      }

      @Test
      @DisplayName("并发测试：多线程调整同一库存")
      void testAdjustInventory_ConcurrentAccess() throws InterruptedException {
          // Given
          Inventory mockInventory = createMockInventory(100, 98, 2, 0, 10, 200);
          when(inventoryMapper.selectByIdForUpdate(1L)).thenReturn(mockInventory);
          when(inventoryMapper.updateById(any())).thenReturn(1);
          when(logMapper.insert(any())).thenReturn(1);
          when(historyMapper.insert(any())).thenReturn(1);

          CountDownLatch latch = new CountDownLatch(10);
          ExecutorService executor = Executors.newFixedThreadPool(10);

          List<Future<R<InventoryAdjustResultVO>>> futures = new ArrayList<>();

          // When - 10个线程同时调整库存
          for (int i = 0; i < 10; i++) {
              Future<R<InventoryAdjustResultVO>> future = executor.submit(() -> {
                  InventoryAdjustRequest request = InventoryAdjustRequest.builder()
                      .inventoryId(1L)
                      .adjustType("DECREASE")
                      .quantity(1)
                      .reason("INVENTORY_CHECK")
                      .operator("测试员" + Thread.currentThread().getId())
                      .build();

                  latch.countDown();
                  try {
                      latch.await();
                  } catch (InterruptedException e) {
                      Thread.currentThread().interrupt();
                  }

                  return adjustService.adjustInventory(request);
              });
              futures.add(future);
          }

          // Then - 所有调整都应该成功，且数据一致
          for (Future<R<InventoryAdjustResultVO>> future : futures) {
              R<InventoryAdjustResultVO> result = future.get();
              assertThat(result.getCode()).isEqualTo(200);
          }

          executor.shutdown();
      }

      private Inventory createMockInventory(int current, int available, int occupied, 
                                          int damaged, int safety, int maximum) {
          Inventory inventory = new Inventory();
          inventory.setId(1L);
          inventory.setCurrentStock(current);
          inventory.setAvailableStock(available);
          inventory.setOccupiedStock(occupied);
          inventory.setDamagedStock(damaged);
          inventory.setSafetyStock(safety);
          inventory.setMaximumStock(maximum);
          return inventory;
      }
  }

  4. 批量补货申请Service测试用例

  @SpringBootTest
  @Transactional
  @Rollback
  class ReplenishmentOrderServiceTest {

      @Autowired
      private IReplenishmentOrderService replenishmentService;

      @MockBean
      private InventoryMapper inventoryMapper;

      @MockBean
      private PartsMapper partsMapper;

      @MockBean
      private ReplenishmentOrderMapper orderMapper;

      @MockBean
      private ReplenishmentOrderDetailsMapper detailsMapper;

      @Test
      @DisplayName("正常场景：批量创建补货申请成功")
      void testCreateBatchReplenishmentOrder_Success() {
          // Given
          BatchReplenishmentRequest request = BatchReplenishmentRequest.builder()
              .storeId(1L)
              .applicant("张经理")
              .remark("紧急补货")
              .inventoryItems(Arrays.asList(
                  ReplenishmentItem.builder().inventoryId(1L).requestQuantity(10).build(),
                  ReplenishmentItem.builder().inventoryId(2L).requestQuantity(5).build(),
                  ReplenishmentItem.builder().inventoryId(3L).requestQuantity(8).build()
              ))
              .build();

          // Mock数据
          mockInventoryAndPartsData();
          when(orderMapper.selectMaxOrderNoToday()).thenReturn("REP202501230003");
          when(orderMapper.insert(any())).thenReturn(1);
          when(detailsMapper.insert(any())).thenReturn(1);
          when(inventoryMapper.updateById(any())).thenReturn(1);

          // When
          R<ReplenishmentOrderResultVO> result = replenishmentService.createBatchReplenishmentOrder(request);

          // Then
          assertThat(result.getCode()).isEqualTo(200);
          assertThat(result.getData().getOrderNo()).startsWith("REP20250123");
          assertThat(result.getData().getTotalItems()).isEqualTo(3);
          assertThat(result.getData().getTotalQuantity()).isEqualTo(23); // 10+5+8
          assertThat(result.getData().getStatus()).isEqualTo("DRAFT");

          // 验证数据库操作
          verify(orderMapper).insert(any(ReplenishmentOrder.class));
          verify(detailsMapper, times(3)).insert(any(ReplenishmentOrderDetails.class));
          verify(inventoryMapper, times(3)).updateById(any(Inventory.class));
      }

      @Test
      @DisplayName("业务规则：补货单号生成正确")
      void testCreateBatchReplenishmentOrder_OrderNoGeneration() {
          // Given
          BatchReplenishmentRequest request = createValidBatchRequest();

          // Mock今日最大单号为REP202501230005
          when(orderMapper.selectMaxOrderNoToday()).thenReturn("REP202501230005");
          mockOtherDependencies();

          // When
          R<ReplenishmentOrderResultVO> result = replenishmentService.createBatchReplenishmentOrder(request);

          // Then - 新单号应该是REP202501230006
          assertThat(result.getData().getOrderNo()).isEqualTo("REP202501230006");

          verify(orderMapper).insert(argThat(order ->
              "REP202501230006".equals(order.getOrderNo())
          ));
      }

      @Test
      @DisplayName("业务规则：库存占用更新正确")
      void testCreateBatchReplenishmentOrder_InventoryOccupationUpdate() {
          // Given
          BatchReplenishmentRequest request = BatchReplenishmentRequest.builder()
              .storeId(1L)
              .applicant("张经理")
              .inventoryItems(Arrays.asList(
                  ReplenishmentItem.builder().inventoryId(1L).requestQuantity(5).build()
              ))
              .build();

          Inventory mockInventory = createMockInventory(1L, 25, 20, 3, 2, 8, 50);
          when(inventoryMapper.selectById(1L)).thenReturn(mockInventory);
          mockOtherDependencies();

          // When
          replenishmentService.createBatchReplenishmentOrder(request);

          // Then - 验证库存占用被更新
          verify(inventoryMapper).updateById(argThat(inventory ->
              inventory.getOccupiedStock().equals(8) && // 原3 + 申请5 = 8
              inventory.getAvailableStock().equals(15)   // 25 - 8 - 2 = 15
          ));
      }

      @Test
      @DisplayName("参数校验：申请项目不能为空")
      void testCreateBatchReplenishmentOrder_EmptyItems() {
          // Given
          BatchReplenishmentRequest request = BatchReplenishmentRequest.builder()
              .storeId(1L)
              .applicant("张经理")
              .inventoryItems(Collections.emptyList())
              .build();

          // When & Then
          assertThatThrownBy(() -> replenishmentService.createBatchReplenishmentOrder(request))
              .isInstanceOf(ValidationException.class)
              .hasMessageContaining("补货项目不能为空");
      }

      @Test
      @DisplayName("参数校验：申请数量必须大于0")
      void testCreateBatchReplenishmentOrder_InvalidQuantity() {
          // Given
          BatchReplenishmentRequest request = BatchReplenishmentRequest.builder()
              .storeId(1L)
              .applicant("张经理")
              .inventoryItems(Arrays.asList(
                  ReplenishmentItem.builder().inventoryId(1L).requestQuantity(0).build()
              ))
              .build();

          // When & Then
          assertThatThrownBy(() -> replenishmentService.createBatchReplenishmentOrder(request))
              .isInstanceOf(ValidationException.class)
              .hasMessageContaining("申请数量必须大于0");
      }

      @Test
      @DisplayName("业务校验：库存记录必须存在")
      void testCreateBatchReplenishmentOrder_InventoryNotFound() {
          // Given
          BatchReplenishmentRequest request = createValidBatchRequest();
          when(inventoryMapper.selectById(1L)).thenReturn(null); // 库存不存在

          // When & Then
          assertThatThrownBy(() -> replenishmentService.createBatchReplenishmentOrder(request))
              .isInstanceOf(BusinessException.class)
              .hasMessage("库存记录不存在：ID=1");
      }

      @Test
      @DisplayName("业务校验：零件状态必须正常")
      void testCreateBatchReplenishmentOrder_PartsInactive() {
          // Given
          BatchReplenishmentRequest request = createValidBatchRequest();

          Inventory mockInventory = createMockInventory(1L, 25, 20, 3, 2, 8, 50);
          Parts mockParts = createMockParts(1L, "P10001", "火花塞", 0); // status = 0 停用

          when(inventoryMapper.selectById(1L)).thenReturn(mockInventory);
          when(partsMapper.selectById(1L)).thenReturn(mockParts);

          // When & Then
          assertThatThrownBy(() -> replenishmentService.createBatchReplenishmentOrder(request))
              .isInstanceOf(BusinessException.class)
              .hasMessage("零件已停用，不能申请补货：P10001");
      }

      @Test
      @DisplayName("业务校验：申请数量合理性检查")
      void testCreateBatchReplenishmentOrder_UnreasonableQuantity() {
          // Given - 申请数量超过最大库存的2倍
          BatchReplenishmentRequest request = BatchReplenishmentRequest.builder()
              .storeId(1L)
              .applicant("张经理")
              .inventoryItems(Arrays.asList(
                  ReplenishmentItem.builder().inventoryId(1L).requestQuantity(200).build() // 超大数量
              ))
              .build();

          Inventory mockInventory = createMockInventory(1L, 5, 3, 2, 0, 10, 50); // 最大库存50
          Parts mockParts = createMockParts(1L, "P10001", "火花塞", 1);

          when(inventoryMapper.selectById(1L)).thenReturn(mockInventory);
          when(partsMapper.selectById(1L)).thenReturn(mockParts);

          // When & Then
          assertThatThrownBy(() -> replenishmentService.createBatchReplenishmentOrder(request))
              .isInstanceOf(BusinessException.class)
              .hasMessage("申请数量过大，建议不超过最大库存的2倍");
      }

      @Test
      @DisplayName("金额计算：补货总金额计算正确")
      void testCreateBatchReplenishmentOrder_TotalAmountCalculation() {
          // Given
          BatchReplenishmentRequest request = BatchReplenishmentRequest.builder()
              .storeId(1L)
              .applicant("张经理")
              .inventoryItems(Arrays.asList(
                  ReplenishmentItem.builder().inventoryId(1L).requestQuantity(10).build(), // 10 * 100 = 1000
                  ReplenishmentItem.builder().inventoryId(2L).requestQuantity(5).build()   // 5 * 200 = 1000
              ))
              .build();

          // Mock数据 - 不同价格的零件
          Inventory inventory1 = createMockInventory(1L, 25, 20, 3, 2, 8, 50);
          Inventory inventory2 = createMockInventory(2L, 15, 12, 2, 1, 5, 30);
          Parts parts1 = createMockPartsWithPrice(1L, "P10001", "火花塞", 1, new BigDecimal("100.00"));
          Parts parts2 = createMockPartsWithPrice(2L, "P10002", "滤清器", 1, new BigDecimal("200.00"));

          when(inventoryMapper.selectById(1L)).thenReturn(inventory1);
          when(inventoryMapper.selectById(2L)).thenReturn(inventory2);
          when(partsMapper.selectById(1L)).thenReturn(parts1);
          when(partsMapper.selectById(2L)).thenReturn(parts2);
          mockOtherDependencies();

          // When
          R<ReplenishmentOrderResultVO> result = replenishmentService.createBatchReplenishmentOrder(request);

          // Then
          assertThat(result.getData().getTotalAmount()).isEqualByComparingTo("2000.00");

          verify(orderMapper).insert(argThat(order ->
              order.getTotalAmount().compareTo(new BigDecimal("2000.00")) == 0
          ));
      }

      @Test
      @DisplayName("事务测试：部分失败时整体回滚")
      void testCreateBatchReplenishmentOrder_TransactionRollback() {
          // Given
          BatchReplenishmentRequest request = createValidBatchRequest();
          mockInventoryAndPartsData();

          // Mock主表插入成功，但明细插入失败
          when(orderMapper.insert(any())).thenReturn(1);
          when(detailsMapper.insert(any()))
              .thenReturn(1)  // 第一条成功
              .thenThrow(new DataAccessException("插入明细失败") {}); // 第二条失败

          // When & Then
          assertThatThrownBy(() -> replenishmentService.createBatchReplenishmentOrder(request))
              .isInstanceOf(BusinessException.class);

          // 验证事务回滚，库存占用未被更新
          verify(inventoryMapper, never()).updateById(any());
      }

      @Test
      @DisplayName("性能测试：大批量申请响应时间")
      void testCreateBatchReplenishmentOrder_Performance() {
          // Given - 创建100个补货项目
          List<ReplenishmentItem> items = IntStream.range(1, 101)
              .mapToObj(i -> ReplenishmentItem.builder()
                  .inventoryId((long) i)
                  .requestQuantity(1)
                  .build())
              .collect(Collectors.toList());

          BatchReplenishmentRequest request = BatchReplenishmentRequest.builder()
              .storeId(1L)
              .applicant("压力测试")
              .inventoryItems(items)
              .build();

          // Mock大量数据
          mockLargeDataSet(100);

          // When & Then - 应该在3秒内完成
          assertTimeout(Duration.ofSeconds(3), () -> {
              replenishmentService.createBatchReplenishmentOrder(request);
          });
      }

      private void mockInventoryAndPartsData() {
          // Mock库存数据
          when(inventoryMapper.selectById(1L))
              .thenReturn(createMockInventory(1L, 25, 20, 3, 2, 8, 50));
          when(inventoryMapper.selectById(2L))
              .thenReturn(createMockInventory(2L, 15, 12, 2, 1, 5, 30));
          when(inventoryMapper.selectById(3L))
              .thenReturn(createMockInventory(3L, 30, 28, 1, 1, 10, 60));

          // Mock零件数据
          when(partsMapper.selectById(1L))
              .thenReturn(createMockPartsWithPrice(1L, "P10001", "火花塞", 1, new BigDecimal("50.00")));
          when(partsMapper.selectById(2L))
              .thenReturn(createMockPartsWithPrice(2L, "P10002", "滤清器", 1, new BigDecimal("80.00")));
          when(partsMapper.selectById(3L))
              .thenReturn(createMockPartsWithPrice(3L, "P10003", "刹车片", 1, new BigDecimal("120.00")));
      }
  }

  📊 TDD测试策略总结

  测试分层策略

  1. 单元测试：测试单个方法的逻辑正确性
  2. 集成测试：测试Service与数据库交互
  3. 契约测试：验证Service接口契约
  4. 端到端测试：完整业务流程测试

  测试覆盖维度

  1. 正常场景：业务流程正确执行
  2. 边界条件：临界值和极端情况
  3. 异常处理：错误情况的处理
  4. 业务规则：复杂业务逻辑验证
  5. 并发安全：多线程访问安全性
  6. 性能指标：响应时间和吞吐量

  TDD开发流程

  1. 编写失败测试 → 2. 实现最小代码 → 3. 重构优化 → 4. 重复循环

  测试质量标准

  - 代码覆盖率：行覆盖率 ≥ 80%，分支覆盖率 ≥ 70%
  - 测试速度：单元测试 < 100ms，集成测试 < 1s
  - 测试稳定性：连续运行100次成功率 ≥ 99%
  - 测试独立性：每个测试用例独立运行

  这套测试用例确保了接口功能的完整性、业务逻辑的正确性和系统的稳定性，完全符合TDD的开发理念。